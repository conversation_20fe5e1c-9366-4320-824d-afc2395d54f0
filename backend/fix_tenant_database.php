<?php

// Complete tenant database fix script
require_once 'vendor/autoload.php';

$app = require_once 'bootstrap/app.php';
$kernel = $app->make('Illuminate\Contracts\Console\Kernel');
$kernel->bootstrap();

use App\Models\Tenant;
use Illuminate\Support\Facades\Artisan;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

echo "=== Fixing Tenant Database for Login ===\n\n";

try {
    // Find the tenant
    $tenant = Tenant::find('demo-restaurant');
    
    if (!$tenant) {
        echo "❌ Tenant 'demo-restaurant' not found!\n";
        echo "Creating tenant first...\n";
        
        $tenant = Tenant::create([
            'id' => 'demo-restaurant',
            'name' => 'Demo Restaurant',
            'email' => '<EMAIL>',
            'subscription_status' => 'active'
        ]);
        
        $tenant->domains()->create(['domain' => 'demo-restaurant.localhost']);
        echo "✓ Tenant created\n";
    } else {
        echo "✓ Tenant found: {$tenant->name}\n";
    }

    // Switch to tenant context and fix database
    $tenant->run(function () {
        echo "\n🔧 Fixing tenant database tables...\n";
        
        // 1. Create sessions table
        if (!Schema::hasTable('sessions')) {
            echo "Creating sessions table...\n";
            DB::statement("
                CREATE TABLE `sessions` (
                    `id` varchar(255) NOT NULL,
                    `user_id` bigint unsigned DEFAULT NULL,
                    `ip_address` varchar(45) DEFAULT NULL,
                    `user_agent` text,
                    `payload` longtext NOT NULL,
                    `last_activity` int NOT NULL,
                    PRIMARY KEY (`id`),
                    KEY `sessions_user_id_index` (`user_id`),
                    KEY `sessions_last_activity_index` (`last_activity`)
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
            ");
            echo "✓ Sessions table created\n";
        } else {
            echo "✓ Sessions table already exists\n";
        }

        // 2. Create cache table
        if (!Schema::hasTable('cache')) {
            echo "Creating cache table...\n";
            DB::statement("
                CREATE TABLE `cache` (
                    `key` varchar(255) NOT NULL,
                    `value` mediumtext NOT NULL,
                    `expiration` int NOT NULL,
                    PRIMARY KEY (`key`)
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
            ");
            echo "✓ Cache table created\n";
        } else {
            echo "✓ Cache table already exists\n";
        }

        // 3. Create cache_locks table
        if (!Schema::hasTable('cache_locks')) {
            echo "Creating cache_locks table...\n";
            DB::statement("
                CREATE TABLE `cache_locks` (
                    `key` varchar(255) NOT NULL,
                    `owner` varchar(255) NOT NULL,
                    `expiration` int NOT NULL,
                    PRIMARY KEY (`key`)
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
            ");
            echo "✓ Cache locks table created\n";
        } else {
            echo "✓ Cache locks table already exists\n";
        }

        // 4. Create jobs table
        if (!Schema::hasTable('jobs')) {
            echo "Creating jobs table...\n";
            DB::statement("
                CREATE TABLE `jobs` (
                    `id` bigint unsigned NOT NULL AUTO_INCREMENT,
                    `queue` varchar(255) NOT NULL,
                    `payload` longtext NOT NULL,
                    `attempts` tinyint unsigned NOT NULL,
                    `reserved_at` int unsigned DEFAULT NULL,
                    `available_at` int unsigned NOT NULL,
                    `created_at` int unsigned NOT NULL,
                    PRIMARY KEY (`id`),
                    KEY `jobs_queue_index` (`queue`)
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
            ");
            echo "✓ Jobs table created\n";
        } else {
            echo "✓ Jobs table already exists\n";
        }

        // 5. Create failed_jobs table
        if (!Schema::hasTable('failed_jobs')) {
            echo "Creating failed_jobs table...\n";
            DB::statement("
                CREATE TABLE `failed_jobs` (
                    `id` bigint unsigned NOT NULL AUTO_INCREMENT,
                    `uuid` varchar(255) NOT NULL,
                    `connection` text NOT NULL,
                    `queue` text NOT NULL,
                    `payload` longtext NOT NULL,
                    `exception` longtext NOT NULL,
                    `failed_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
                    PRIMARY KEY (`id`),
                    UNIQUE KEY `failed_jobs_uuid_unique` (`uuid`)
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
            ");
            echo "✓ Failed jobs table created\n";
        } else {
            echo "✓ Failed jobs table already exists\n";
        }

        // 6. Create migrations table
        if (!Schema::hasTable('migrations')) {
            echo "Creating migrations table...\n";
            DB::statement("
                CREATE TABLE `migrations` (
                    `id` int unsigned NOT NULL AUTO_INCREMENT,
                    `migration` varchar(255) NOT NULL,
                    `batch` int NOT NULL,
                    PRIMARY KEY (`id`)
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
            ");
            echo "✓ Migrations table created\n";
        } else {
            echo "✓ Migrations table already exists\n";
        }

        // 7. Create personal_access_tokens table (for API)
        if (!Schema::hasTable('personal_access_tokens')) {
            echo "Creating personal_access_tokens table...\n";
            DB::statement("
                CREATE TABLE `personal_access_tokens` (
                    `id` bigint unsigned NOT NULL AUTO_INCREMENT,
                    `tokenable_type` varchar(255) NOT NULL,
                    `tokenable_id` bigint unsigned NOT NULL,
                    `name` varchar(255) NOT NULL,
                    `token` varchar(64) NOT NULL,
                    `abilities` text,
                    `last_used_at` timestamp NULL DEFAULT NULL,
                    `expires_at` timestamp NULL DEFAULT NULL,
                    `created_at` timestamp NULL DEFAULT NULL,
                    `updated_at` timestamp NULL DEFAULT NULL,
                    PRIMARY KEY (`id`),
                    UNIQUE KEY `personal_access_tokens_token_unique` (`token`),
                    KEY `personal_access_tokens_tokenable_type_tokenable_id_index` (`tokenable_type`,`tokenable_id`)
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
            ");
            echo "✓ Personal access tokens table created\n";
        } else {
            echo "✓ Personal access tokens table already exists\n";
        }

        // 8. Create restaurants table
        if (!Schema::hasTable('restaurants')) {
            echo "Creating restaurants table...\n";
            DB::statement("
                CREATE TABLE `restaurants` (
                    `id` bigint unsigned NOT NULL AUTO_INCREMENT,
                    `name` varchar(255) NOT NULL,
                    `slug` varchar(255) NOT NULL,
                    `description` text,
                    `phone` varchar(255),
                    `email` varchar(255),
                    `address` text,
                    `city` varchar(255),
                    `state` varchar(255),
                    `postal_code` varchar(255),
                    `country` varchar(255),
                    `timezone` varchar(255) DEFAULT 'UTC',
                    `currency` varchar(3) DEFAULT 'USD',
                    `is_active` tinyint(1) NOT NULL DEFAULT '1',
                    `created_at` timestamp NULL DEFAULT NULL,
                    `updated_at` timestamp NULL DEFAULT NULL,
                    PRIMARY KEY (`id`),
                    UNIQUE KEY `restaurants_slug_unique` (`slug`)
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
            ");
            
            // Insert demo restaurant data
            DB::table('restaurants')->insert([
                'name' => 'Demo Restaurant',
                'slug' => 'demo-restaurant',
                'description' => 'A wonderful demo restaurant for testing',
                'phone' => '******-0123',
                'email' => '<EMAIL>',
                'address' => '123 Main Street',
                'city' => 'New York',
                'state' => 'NY',
                'postal_code' => '10001',
                'country' => 'USA',
                'timezone' => 'America/New_York',
                'currency' => 'USD',
                'is_active' => true,
                'created_at' => now(),
                'updated_at' => now(),
            ]);
            
            echo "✓ Restaurants table created with demo data\n";
        } else {
            echo "✓ Restaurants table already exists\n";
        }

        // Verify all tables
        echo "\n📋 Verifying tenant database tables:\n";
        $requiredTables = [
            'sessions', 'cache', 'cache_locks', 'jobs', 'failed_jobs', 
            'migrations', 'personal_access_tokens', 'restaurants'
        ];
        
        foreach ($requiredTables as $table) {
            if (Schema::hasTable($table)) {
                echo "  ✓ {$table}\n";
            } else {
                echo "  ❌ {$table} (missing)\n";
            }
        }
    });

    echo "\n🎉 Tenant database fix completed!\n";
    echo "\n📋 Test Information:\n";
    echo "  Tenant URL: http://demo-restaurant.localhost:8000/login\n";
    echo "  Test Credentials:\n";
    echo "    Manager: <EMAIL> / Manager@2024\n";
    echo "    Waiter: <EMAIL> / Waiter@2024\n";
    echo "    Kitchen: <EMAIL> / Kitchen@2024\n";
    echo "    Delivery: <EMAIL> / Delivery@2024\n";
    echo "\n✅ You should now be able to login without database errors!\n";

} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
    echo "File: " . $e->getFile() . ":" . $e->getLine() . "\n";
}
