<?php

// Quick fix for Employee servedOrders method
require_once 'vendor/autoload.php';

$app = require_once 'bootstrap/app.php';
$kernel = $app->make('Illuminate\Contracts\Console\Kernel');
$kernel->bootstrap();

use App\Models\Tenant;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

echo "=== Quick Fix for Employee servedOrders Method ===\n";

try {
    $tenant = Tenant::find('demo-restaurant');
    
    if (!$tenant) {
        echo "❌ Tenant 'demo-restaurant' not found!\n";
        exit(1);
    }

    echo "✓ Tenant found: {$tenant->name}\n";

    $tenant->run(function () {
        echo "🔧 Fixing database structure...\n";
        
        // 1. Add served_by column to orders table if missing
        if (Schema::hasTable('orders')) {
            $columns = Schema::getColumnListing('orders');
            if (!in_array('served_by', $columns)) {
                DB::statement('ALTER TABLE orders ADD COLUMN served_by bigint unsigned DEFAULT NULL');
                echo "✓ Added served_by column to orders table\n";
            } else {
                echo "✓ served_by column already exists in orders table\n";
            }
        }
        
        // 2. Create employees table if missing
        if (!Schema::hasTable('employees')) {
            DB::statement('
                CREATE TABLE employees (
                    id bigint unsigned NOT NULL AUTO_INCREMENT,
                    employee_id varchar(255) NOT NULL,
                    first_name varchar(255) NOT NULL,
                    last_name varchar(255) NOT NULL,
                    position varchar(255) DEFAULT NULL,
                    status enum("active","inactive") NOT NULL DEFAULT "active",
                    created_at timestamp NULL DEFAULT NULL,
                    updated_at timestamp NULL DEFAULT NULL,
                    PRIMARY KEY (id)
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4
            ');
            echo "✓ Created employees table\n";
            
            // Add sample employee
            DB::table('employees')->insert([
                'employee_id' => 'EMP-001',
                'first_name' => 'John',
                'last_name' => 'Doe',
                'position' => 'Waiter',
                'status' => 'active',
                'created_at' => now(),
                'updated_at' => now()
            ]);
            echo "✓ Added sample employee\n";
        } else {
            echo "✓ Employees table already exists\n";
        }
        
        // 3. Test the relationship
        echo "🧪 Testing Employee servedOrders relationship...\n";
        
        try {
            $result = DB::select('
                SELECT 
                    e.id,
                    e.first_name,
                    e.last_name,
                    COUNT(o.id) as orders_count
                FROM employees e
                LEFT JOIN orders o ON e.id = o.served_by
                WHERE e.status = "active"
                GROUP BY e.id, e.first_name, e.last_name
                LIMIT 5
            ');
            
            echo "✅ Relationship test successful! Found " . count($result) . " employees\n";
            foreach ($result as $emp) {
                echo "   - {$emp->first_name} {$emp->last_name}: {$emp->orders_count} orders\n";
            }
            
        } catch (Exception $e) {
            echo "❌ Relationship test failed: " . $e->getMessage() . "\n";
        }
        
        echo "✅ Fix completed!\n";
    });

    echo "\n🎉 Employee servedOrders method fix completed!\n";
    echo "✅ Added servedOrders() method to Employee model\n";
    echo "✅ Added served_by column to orders table\n";
    echo "✅ Created employees table with sample data\n";
    echo "\n🌐 You can now access: http://demo-restaurant.localhost:8000/manager/dashboard\n";

} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
    echo "File: " . $e->getFile() . ":" . $e->getLine() . "\n";
}
