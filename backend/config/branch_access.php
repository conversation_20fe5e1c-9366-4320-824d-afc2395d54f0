<?php

return [
    /*
    |--------------------------------------------------------------------------
    | Branch Access Cache Configuration
    |--------------------------------------------------------------------------
    |
    | This configuration controls how branch access data is cached across
    | different cache drivers. The system automatically detects the cache
    | driver and applies appropriate caching strategies.
    |
    */

    'cache' => [
        /*
        |--------------------------------------------------------------------------
        | Cache TTL (Time To Live)
        |--------------------------------------------------------------------------
        |
        | How long to cache branch access data in seconds.
        | Default: 300 seconds (5 minutes)
        |
        */
        'ttl' => env('BRANCH_ACCESS_CACHE_TTL', 300),

        /*
        |--------------------------------------------------------------------------
        | Cache Key Prefix
        |--------------------------------------------------------------------------
        |
        | Prefix for all branch access cache keys to avoid conflicts.
        |
        */
        'key_prefix' => env('BRANCH_ACCESS_CACHE_PREFIX', 'user_branches'),

        /*
        |--------------------------------------------------------------------------
        | Cache Tags
        |--------------------------------------------------------------------------
        |
        | Tags used for cache invalidation when using Redis/Memcached.
        | Only used when the cache driver supports tagging.
        |
        */
        'tags' => [
            'user_branches',
            'tenant_data',
        ],

        /*
        |--------------------------------------------------------------------------
        | Supported Tagging Drivers
        |--------------------------------------------------------------------------
        |
        | Cache drivers that support tagging for more efficient cache management.
        |
        */
        'tagging_drivers' => [
            'redis',
            'memcached',
            'dynamodb',
        ],

        /*
        |--------------------------------------------------------------------------
        | Cache Store
        |--------------------------------------------------------------------------
        |
        | Specific cache store to use for branch access data.
        | If null, uses the default cache store.
        |
        */
        'store' => env('BRANCH_ACCESS_CACHE_STORE', null),

        /*
        |--------------------------------------------------------------------------
        | Fallback Behavior
        |--------------------------------------------------------------------------
        |
        | What to do when caching fails:
        | - 'execute': Execute the callback directly (recommended)
        | - 'throw': Throw the exception
        | - 'return_empty': Return empty collection
        |
        */
        'fallback_on_error' => env('BRANCH_ACCESS_CACHE_FALLBACK', 'execute'),
    ],

    /*
    |--------------------------------------------------------------------------
    | Branch Access Rules
    |--------------------------------------------------------------------------
    |
    | Configuration for branch access logic and permissions.
    |
    */
    'access_rules' => [
        /*
        |--------------------------------------------------------------------------
        | Admin Roles
        |--------------------------------------------------------------------------
        |
        | Roles that have access to all branches regardless of assignments.
        |
        */
        'admin_roles' => [
            'super-admin',
            'admin',
        ],

        /*
        |--------------------------------------------------------------------------
        | Manager Roles
        |--------------------------------------------------------------------------
        |
        | Roles that can access all branches or their managed branches.
        |
        */
        'manager_roles' => [
            'manager',
            'restaurant-manager',
        ],

        /*
        |--------------------------------------------------------------------------
        | Staff Roles
        |--------------------------------------------------------------------------
        |
        | Roles that access branches based on their employee assignments.
        |
        */
        'staff_roles' => [
            'waiter',
            'kitchen',
            'cashier',
            'delivery',
        ],

        /*
        |--------------------------------------------------------------------------
        | Fallback Access
        |--------------------------------------------------------------------------
        |
        | Whether to grant access to all branches when no specific assignment
        | is found. Useful for smaller restaurants with flexible staff.
        |
        */
        'allow_fallback_access' => env('BRANCH_ACCESS_ALLOW_FALLBACK', true),

        /*
        |--------------------------------------------------------------------------
        | Require Employee Record
        |--------------------------------------------------------------------------
        |
        | Whether users must have an employee record to access branches.
        | If false, users with appropriate roles can access branches without
        | an employee record.
        |
        */
        'require_employee_record' => env('BRANCH_ACCESS_REQUIRE_EMPLOYEE', false),
    ],

    /*
    |--------------------------------------------------------------------------
    | Logging Configuration
    |--------------------------------------------------------------------------
    |
    | Configuration for logging branch access events and errors.
    |
    */
    'logging' => [
        /*
        |--------------------------------------------------------------------------
        | Enable Logging
        |--------------------------------------------------------------------------
        |
        | Whether to log branch access events and cache operations.
        |
        */
        'enabled' => env('BRANCH_ACCESS_LOGGING', true),

        /*
        |--------------------------------------------------------------------------
        | Log Level
        |--------------------------------------------------------------------------
        |
        | Minimum log level for branch access events.
        | Options: emergency, alert, critical, error, warning, notice, info, debug
        |
        */
        'level' => env('BRANCH_ACCESS_LOG_LEVEL', 'warning'),

        /*
        |--------------------------------------------------------------------------
        | Log Channel
        |--------------------------------------------------------------------------
        |
        | Specific log channel for branch access events.
        | If null, uses the default log channel.
        |
        */
        'channel' => env('BRANCH_ACCESS_LOG_CHANNEL', null),
    ],

    /*
    |--------------------------------------------------------------------------
    | Performance Configuration
    |--------------------------------------------------------------------------
    |
    | Settings to optimize performance of branch access operations.
    |
    */
    'performance' => [
        /*
        |--------------------------------------------------------------------------
        | Eager Load Relations
        |--------------------------------------------------------------------------
        |
        | Relations to eager load when fetching branch data to reduce queries.
        |
        */
        'eager_load' => [
            'floors',
            'manager',
        ],

        /*
        |--------------------------------------------------------------------------
        | Query Optimization
        |--------------------------------------------------------------------------
        |
        | Whether to use optimized queries for branch access checks.
        |
        */
        'optimize_queries' => env('BRANCH_ACCESS_OPTIMIZE_QUERIES', true),

        /*
        |--------------------------------------------------------------------------
        | Batch Operations
        |--------------------------------------------------------------------------
        |
        | Whether to use batch operations for multiple branch access checks.
        |
        */
        'use_batch_operations' => env('BRANCH_ACCESS_BATCH_OPERATIONS', true),
    ],
];
