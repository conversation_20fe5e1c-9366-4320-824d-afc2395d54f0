-- Create tenant domain directly in database
-- Run this SQL script to fix the tenant domain issue

-- First, check if tenant exists
SELECT * FROM tenants WHERE id = 'demo-restaurant';

-- If tenant doesn't exist, create it
INSERT IGNORE INTO tenants (id, name, email, phone, address, city, state, country, postal_code, timezone, currency, language, subscription_status, trial_ends_at, created_at, updated_at)
VALUES (
    'demo-restaurant',
    'Demo Restaurant',
    '<EMAIL>',
    '******-0123',
    '123 Main Street',
    'New York',
    'NY',
    'USA',
    '10001',
    'America/New_York',
    'USD',
    'en',
    'active',
    DATE_ADD(NOW(), INTERVAL 30 DAY),
    NOW(),
    NOW()
);

-- Create the domain
INSERT IGNORE INTO domains (domain, tenant_id, created_at, updated_at)
VALUES (
    'demo-restaurant.localhost',
    'demo-restaurant',
    NOW(),
    NOW()
);

-- Verify the setup
SELECT 
    t.id as tenant_id,
    t.name as tenant_name,
    d.domain as domain_name
FROM tenants t
LEFT JOIN domains d ON t.id = d.tenant_id
WHERE t.id = 'demo-restaurant';

-- Show all tenants and domains
SELECT 
    t.id as tenant_id,
    t.name as tenant_name,
    t.subscription_status,
    d.domain as domain_name
FROM tenants t
LEFT JOIN domains d ON t.id = d.tenant_id
ORDER BY t.created_at DESC;
