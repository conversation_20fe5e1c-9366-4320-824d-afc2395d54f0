<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Log;
use App\Models\Tenant\User;
use Spatie\Permission\Models\Role;
use Spatie\Permission\Models\Permission;

class ManagerSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        Log::info('ManagerSeeder: Starting execution');

        // Get manager credentials from config (set by TenantController)
        $managerEmail = config('tenant.manager_email', '<EMAIL>');
        $managerPassword = config('tenant.manager_password', 'Restaurant@2024');
        $tenantName = config('tenant.tenant_name', 'Restaurant');

        Log::info('ManagerSeeder: Retrieved config', [
            'manager_email' => $managerEmail,
            'tenant_name' => $tenantName,
            'has_password' => !empty($managerPassword),
            'tenant_id' => tenant() ? tenant('id') : 'NO_TENANT',
            'database_connection' => config('database.default'),
        ]);

        // Create permissions if they don't exist
        $permissions = [
            'view dashboard',
            'manage orders',
            'manage menu',
            'manage staff',
            'manage branches',
            'manage inventory',
            'manage reports',
            'manage settings',
            'manage customers',
            'manage tables',
            'manage floors',
            'manage categories',
            'manage menu items',
            'manage employees',
            'manage departments',
            'manage shifts',
            'manage vendors',
            'manage expenses',
            'manage purchase orders',
            'view analytics',
            'manage pos',
            'manage kitchen',
            'manage delivery',
            'manage loyalty',
            'manage pages',
        ];

        Log::info('ManagerSeeder: Creating permissions', ['count' => count($permissions)]);

        foreach ($permissions as $permission) {
            Permission::firstOrCreate(['name' => $permission]);
        }

        Log::info('ManagerSeeder: Permissions created successfully');

        // Create manager role if it doesn't exist
        $managerRole = Role::firstOrCreate(['name' => 'manager']);

        Log::info('ManagerSeeder: Manager role created', ['role_id' => $managerRole->id]);

        // Assign all permissions to manager role
        $managerRole->syncPermissions($permissions);

        Log::info('ManagerSeeder: Permissions synced to manager role');

        // Create manager user
        $manager = User::firstOrCreate(
            ['email' => $managerEmail],
            [
                'name' => $tenantName . ' Manager',
                'email' => $managerEmail,
                'password' => Hash::make($managerPassword),
                'email_verified_at' => now(),
            ]
        );

        // Assign manager role to user
        $manager->assignRole('manager');

        $this->command->info("Manager account created: {$managerEmail}");
    }
}
