<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\SubscriptionPlan;

class BDTSubscriptionPlanSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Clear existing plans (delete instead of truncate to handle foreign keys)
        SubscriptionPlan::query()->delete();

        $plans = [
            [
                'name' => 'Basic Plan',
                'slug' => 'basic',
                'description' => 'Perfect for small restaurants just getting started with essential features',
                'price' => 1000.00,
                'currency' => 'BDT',
                'billing_cycle' => 'monthly',
                'trial_days' => 14,
                'features' => [
                    'Up to 20 menu items (lifetime limit)',
                    '500 orders per month',
                    'Single branch only',
                    'Basic POS features',
                    'Order management',
                    'Customer database',
                    'Basic reporting',
                    'Email support',
                ],
                'max_orders_per_month' => 500,
                'max_menu_items' => 20,
                'max_tables' => 10,
                'max_staff' => 5,
                'max_pages' => 3,
                'max_branches' => 1,
                'has_delivery' => false,
                'has_home_delivery' => false,
                'has_email_marketing' => false,
                'has_loyalty_program' => false,
                'has_analytics' => false,
                'has_advanced_reporting' => false,
                'has_multi_location' => false,
                'has_api_access' => false,
                'is_active' => true,
                'sort_order' => 1,
            ],
            [
                'name' => 'Basic Plus Plan',
                'slug' => 'basic-plus',
                'description' => 'Enhanced features for growing restaurants with delivery and marketing tools',
                'price' => 2000.00,
                'currency' => 'BDT',
                'billing_cycle' => 'monthly',
                'trial_days' => 14,
                'features' => [
                    'Up to 50 menu items (lifetime limit)',
                    '1,000 orders per month',
                    'Home delivery feature enabled',
                    'Up to 5 custom pages',
                    'Email marketing tools',
                    'Customer loyalty program',
                    'Advanced order tracking',
                    'SMS notifications',
                    'Priority email support',
                ],
                'max_orders_per_month' => 1000,
                'max_menu_items' => 50,
                'max_tables' => 20,
                'max_staff' => 10,
                'max_pages' => 5,
                'max_branches' => 1,
                'has_delivery' => true,
                'has_home_delivery' => true,
                'has_email_marketing' => true,
                'has_loyalty_program' => true,
                'has_analytics' => false,
                'has_advanced_reporting' => false,
                'has_multi_location' => false,
                'has_api_access' => false,
                'is_active' => true,
                'sort_order' => 2,
            ],
            [
                'name' => 'Professional Plan',
                'slug' => 'professional',
                'description' => 'Complete solution for established restaurants with multiple branches and advanced features',
                'price' => 5000.00,
                'currency' => 'BDT',
                'billing_cycle' => 'monthly',
                'trial_days' => 14,
                'features' => [
                    'Up to 200 menu items (lifetime limit)',
                    '5,000 orders per month',
                    'Up to 3 branches',
                    'Advanced analytics and reporting',
                    'Up to 20 custom pages',
                    'Loyalty program features',
                    'Multi-location management',
                    'Staff performance tracking',
                    'Inventory management',
                    'Financial reporting',
                    'API access',
                    '24/7 phone support',
                ],
                'max_orders_per_month' => 5000,
                'max_menu_items' => 200,
                'max_tables' => 100,
                'max_staff' => 50,
                'max_pages' => 20,
                'max_branches' => 3,
                'has_delivery' => true,
                'has_home_delivery' => true,
                'has_email_marketing' => true,
                'has_loyalty_program' => true,
                'has_analytics' => true,
                'has_advanced_reporting' => true,
                'has_multi_location' => true,
                'has_api_access' => true,
                'is_active' => true,
                'sort_order' => 3,
            ],
        ];

        foreach ($plans as $plan) {
            SubscriptionPlan::create($plan);
        }

        $this->command->info('BDT Subscription plans created successfully!');
    }
}
