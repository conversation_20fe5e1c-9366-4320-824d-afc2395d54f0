<?php

namespace Database\Seeders;

use App\Models\Tenant\InventoryItem;
use App\Models\Tenant\InventoryCategory;
use App\Models\Tenant\Vendor;
use App\Models\Tenant\Restaurant;
use Illuminate\Database\Seeder;

class InventoryItemSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $restaurant = Restaurant::first();
        $categories = InventoryCategory::all();
        $vendors = Vendor::all();

        foreach ($categories as $category) {
            $this->createInventoryItems($restaurant->id, $category, $vendors);
        }
    }

    private function createInventoryItems($restaurantId, $category, $vendors)
    {
        $items = [];

        switch ($category->name) {
            case 'Proteins':
                $items = [
                    [
                        'name' => 'Chicken Breast',
                        'description' => 'Fresh boneless chicken breast',
                        'unit_of_measurement' => 'lb',
                        'unit_cost' => 4.99,
                        'current_stock' => 25.0,
                        'minimum_stock' => 10.0,
                        'reorder_point' => 15.0,
                        'reorder_quantity' => 50.0,
                        'is_perishable' => true,
                        'track_expiry' => true,
                        'vendor_type' => 'supplier',
                    ],
                    [
                        'name' => 'Ground Beef',
                        'description' => '80/20 ground beef',
                        'unit_of_measurement' => 'lb',
                        'unit_cost' => 5.99,
                        'current_stock' => 20.0,
                        'minimum_stock' => 8.0,
                        'reorder_point' => 12.0,
                        'reorder_quantity' => 40.0,
                        'is_perishable' => true,
                        'track_expiry' => true,
                        'vendor_type' => 'supplier',
                    ],
                    [
                        'name' => 'Atlantic Salmon',
                        'description' => 'Fresh Atlantic salmon fillet',
                        'unit_of_measurement' => 'lb',
                        'unit_cost' => 12.99,
                        'current_stock' => 15.0,
                        'minimum_stock' => 5.0,
                        'reorder_point' => 8.0,
                        'reorder_quantity' => 25.0,
                        'is_perishable' => true,
                        'track_expiry' => true,
                        'vendor_type' => 'supplier',
                    ],
                ];
                break;

            case 'Vegetables':
                $items = [
                    [
                        'name' => 'Roma Tomatoes',
                        'description' => 'Fresh Roma tomatoes',
                        'unit_of_measurement' => 'lb',
                        'unit_cost' => 2.49,
                        'current_stock' => 30.0,
                        'minimum_stock' => 10.0,
                        'reorder_point' => 15.0,
                        'reorder_quantity' => 50.0,
                        'is_perishable' => true,
                        'track_expiry' => true,
                        'vendor_type' => 'supplier',
                    ],
                    [
                        'name' => 'Yellow Onions',
                        'description' => 'Fresh yellow onions',
                        'unit_of_measurement' => 'lb',
                        'unit_cost' => 1.99,
                        'current_stock' => 40.0,
                        'minimum_stock' => 15.0,
                        'reorder_point' => 20.0,
                        'reorder_quantity' => 60.0,
                        'is_perishable' => true,
                        'track_expiry' => false,
                        'vendor_type' => 'supplier',
                    ],
                    [
                        'name' => 'Romaine Lettuce',
                        'description' => 'Fresh romaine lettuce heads',
                        'unit_of_measurement' => 'piece',
                        'unit_cost' => 1.49,
                        'current_stock' => 24.0,
                        'minimum_stock' => 8.0,
                        'reorder_point' => 12.0,
                        'reorder_quantity' => 36.0,
                        'is_perishable' => true,
                        'track_expiry' => true,
                        'vendor_type' => 'supplier',
                    ],
                ];
                break;

            case 'Dairy Products':
                $items = [
                    [
                        'name' => 'Whole Milk',
                        'description' => 'Fresh whole milk',
                        'unit_of_measurement' => 'gal',
                        'unit_cost' => 3.99,
                        'current_stock' => 12.0,
                        'minimum_stock' => 4.0,
                        'reorder_point' => 6.0,
                        'reorder_quantity' => 20.0,
                        'is_perishable' => true,
                        'track_expiry' => true,
                        'vendor_type' => 'supplier',
                    ],
                    [
                        'name' => 'Mozzarella Cheese',
                        'description' => 'Fresh mozzarella cheese',
                        'unit_of_measurement' => 'lb',
                        'unit_cost' => 6.99,
                        'current_stock' => 8.0,
                        'minimum_stock' => 3.0,
                        'reorder_point' => 5.0,
                        'reorder_quantity' => 15.0,
                        'is_perishable' => true,
                        'track_expiry' => true,
                        'vendor_type' => 'supplier',
                    ],
                ];
                break;

            case 'Grains & Cereals':
                $items = [
                    [
                        'name' => 'All-Purpose Flour',
                        'description' => 'Unbleached all-purpose flour',
                        'unit_of_measurement' => 'lb',
                        'unit_cost' => 2.99,
                        'current_stock' => 50.0,
                        'minimum_stock' => 20.0,
                        'reorder_point' => 30.0,
                        'reorder_quantity' => 100.0,
                        'is_perishable' => false,
                        'track_expiry' => false,
                        'vendor_type' => 'supplier',
                    ],
                    [
                        'name' => 'Jasmine Rice',
                        'description' => 'Premium jasmine rice',
                        'unit_of_measurement' => 'lb',
                        'unit_cost' => 1.99,
                        'current_stock' => 75.0,
                        'minimum_stock' => 25.0,
                        'reorder_point' => 40.0,
                        'reorder_quantity' => 150.0,
                        'is_perishable' => false,
                        'track_expiry' => false,
                        'vendor_type' => 'supplier',
                    ],
                ];
                break;

            case 'Spices & Seasonings':
                $items = [
                    [
                        'name' => 'Black Pepper',
                        'description' => 'Ground black pepper',
                        'unit_of_measurement' => 'oz',
                        'unit_cost' => 4.99,
                        'current_stock' => 16.0,
                        'minimum_stock' => 4.0,
                        'reorder_point' => 8.0,
                        'reorder_quantity' => 24.0,
                        'is_perishable' => false,
                        'track_expiry' => false,
                        'vendor_type' => 'supplier',
                    ],
                    [
                        'name' => 'Sea Salt',
                        'description' => 'Fine sea salt',
                        'unit_of_measurement' => 'lb',
                        'unit_cost' => 3.49,
                        'current_stock' => 10.0,
                        'minimum_stock' => 3.0,
                        'reorder_point' => 5.0,
                        'reorder_quantity' => 15.0,
                        'is_perishable' => false,
                        'track_expiry' => false,
                        'vendor_type' => 'supplier',
                    ],
                ];
                break;

            case 'Beverages':
                $items = [
                    [
                        'name' => 'Coca-Cola',
                        'description' => 'Coca-Cola soft drink',
                        'unit_of_measurement' => 'case',
                        'unit_cost' => 24.99,
                        'current_stock' => 8.0,
                        'minimum_stock' => 3.0,
                        'reorder_point' => 5.0,
                        'reorder_quantity' => 15.0,
                        'is_perishable' => false,
                        'track_expiry' => true,
                        'vendor_type' => 'supplier',
                    ],
                ];
                break;

            case 'Cleaning Supplies':
                $items = [
                    [
                        'name' => 'All-Purpose Cleaner',
                        'description' => 'Commercial all-purpose cleaner',
                        'unit_of_measurement' => 'bottle',
                        'unit_cost' => 8.99,
                        'current_stock' => 12.0,
                        'minimum_stock' => 4.0,
                        'reorder_point' => 6.0,
                        'reorder_quantity' => 20.0,
                        'is_perishable' => false,
                        'track_expiry' => false,
                        'vendor_type' => 'service_provider',
                    ],
                ];
                break;

            case 'Disposables':
                $items = [
                    [
                        'name' => 'Paper Napkins',
                        'description' => 'White paper napkins',
                        'unit_of_measurement' => 'case',
                        'unit_cost' => 19.99,
                        'current_stock' => 5.0,
                        'minimum_stock' => 2.0,
                        'reorder_point' => 3.0,
                        'reorder_quantity' => 10.0,
                        'is_perishable' => false,
                        'track_expiry' => false,
                        'vendor_type' => 'supplier',
                    ],
                ];
                break;
        }

        foreach ($items as $itemData) {
            // Find appropriate vendor
            $vendor = $vendors->where('vendor_type', $itemData['vendor_type'])->first();

            InventoryItem::create([
                'restaurant_id' => $restaurantId,
                'inventory_category_id' => $category->id,
                'vendor_id' => $vendor?->id,
                'name' => $itemData['name'],
                'description' => $itemData['description'],
                'unit_of_measurement' => $itemData['unit_of_measurement'],
                'unit_cost' => $itemData['unit_cost'],
                'current_stock' => $itemData['current_stock'],
                'minimum_stock' => $itemData['minimum_stock'],
                'reorder_point' => $itemData['reorder_point'],
                'reorder_quantity' => $itemData['reorder_quantity'],
                'shelf_life_days' => $category->shelf_life_days,
                'storage_requirements' => $category->storage_requirements,
                'is_perishable' => $itemData['is_perishable'],
                'track_expiry' => $itemData['track_expiry'],
                'track_batches' => $itemData['is_perishable'],
                'is_active' => true,
                'last_restocked_at' => now()->subDays(rand(1, 30)),
                'last_counted_at' => now()->subDays(rand(1, 7)),
            ]);
        }
    }
}
