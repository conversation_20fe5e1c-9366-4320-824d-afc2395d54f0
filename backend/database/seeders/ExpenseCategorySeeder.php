<?php

namespace Database\Seeders;

use App\Models\Tenant\ExpenseCategory;
use App\Models\Tenant\Restaurant;
use Illuminate\Database\Seeder;

class ExpenseCategorySeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $restaurant = Restaurant::first();

        $categories = [
            [
                'name' => 'Food & Ingredients',
                'description' => 'All food purchases and ingredients',
                'color_code' => '#FF6B6B',
                'icon' => 'utensils',
                'budget_limit' => 5000.00,
                'sort_order' => 1,
            ],
            [
                'name' => 'Beverages',
                'description' => 'Alcoholic and non-alcoholic beverages',
                'color_code' => '#4ECDC4',
                'icon' => 'wine-glass',
                'budget_limit' => 2000.00,
                'sort_order' => 2,
            ],
            [
                'name' => 'Kitchen Equipment',
                'description' => 'Kitchen appliances and equipment',
                'color_code' => '#45B7D1',
                'icon' => 'blender',
                'budget_limit' => 1500.00,
                'sort_order' => 3,
            ],
            [
                'name' => 'Utilities',
                'description' => 'Electricity, gas, water, internet',
                'color_code' => '#F7DC6F',
                'icon' => 'bolt',
                'budget_limit' => 1200.00,
                'sort_order' => 4,
            ],
            [
                'name' => 'Rent & Lease',
                'description' => 'Property rent and equipment leases',
                'color_code' => '#BB8FCE',
                'icon' => 'home',
                'budget_limit' => 8000.00,
                'sort_order' => 5,
            ],
            [
                'name' => 'Marketing & Advertising',
                'description' => 'Promotional activities and advertising',
                'color_code' => '#82E0AA',
                'icon' => 'bullhorn',
                'budget_limit' => 800.00,
                'sort_order' => 6,
            ],
            [
                'name' => 'Cleaning Supplies',
                'description' => 'Cleaning products and sanitation',
                'color_code' => '#F8C471',
                'icon' => 'spray-can',
                'budget_limit' => 400.00,
                'sort_order' => 7,
            ],
            [
                'name' => 'Office Supplies',
                'description' => 'Administrative and office materials',
                'color_code' => '#85C1E9',
                'icon' => 'paperclip',
                'budget_limit' => 300.00,
                'sort_order' => 8,
            ],
            [
                'name' => 'Insurance',
                'description' => 'Business insurance premiums',
                'color_code' => '#F1948A',
                'icon' => 'shield-alt',
                'budget_limit' => 600.00,
                'sort_order' => 9,
            ],
            [
                'name' => 'Professional Services',
                'description' => 'Legal, accounting, consulting services',
                'color_code' => '#D7BDE2',
                'icon' => 'briefcase',
                'budget_limit' => 1000.00,
                'sort_order' => 10,
            ],
            [
                'name' => 'Maintenance & Repairs',
                'description' => 'Equipment maintenance and facility repairs',
                'color_code' => '#A9DFBF',
                'icon' => 'tools',
                'budget_limit' => 800.00,
                'sort_order' => 11,
            ],
            [
                'name' => 'Transportation',
                'description' => 'Delivery vehicles and transportation costs',
                'color_code' => '#F9E79F',
                'icon' => 'truck',
                'budget_limit' => 500.00,
                'sort_order' => 12,
            ],
        ];

        foreach ($categories as $categoryData) {
            ExpenseCategory::create([
                'restaurant_id' => $restaurant->id,
                'name' => $categoryData['name'],
                'description' => $categoryData['description'],
                'color_code' => $categoryData['color_code'],
                'icon' => $categoryData['icon'],
                'budget_limit' => $categoryData['budget_limit'],
                'sort_order' => $categoryData['sort_order'],
                'is_active' => true,
            ]);
        }
    }
}
