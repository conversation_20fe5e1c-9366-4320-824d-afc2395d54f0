<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\Tenant\Floor;
use App\Models\Tenant\Branch;
use App\Models\Tenant\Table;

class FloorSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Get all branches
        $branches = Branch::all();

        foreach ($branches as $branch) {
            // Create floors for each branch
            $floors = [
                [
                    'name' => 'Ground Floor',
                    'description' => 'Main dining area on the ground floor',
                    'sort_order' => 1,
                ],
                [
                    'name' => 'First Floor',
                    'description' => 'Upper dining area with city view',
                    'sort_order' => 2,
                ],
                [
                    'name' => 'Rooftop',
                    'description' => 'Outdoor dining area on the rooftop',
                    'sort_order' => 3,
                ],
            ];

            foreach ($floors as $index => $floorData) {
                $floor = Floor::create([
                    'name' => $floorData['name'],
                    'description' => $floorData['description'],
                    'branch_id' => $branch->id,
                    'sort_order' => $floorData['sort_order'],
                    'is_active' => true,
                ]);

                // Create tables for each floor
                $this->createTablesForFloor($floor, $index);
            }
        }
    }

    /**
     * Create tables for a specific floor.
     */
    private function createTablesForFloor(Floor $floor, int $floorIndex): void
    {
        $tableConfigs = [
            // Ground Floor - More tables
            0 => [
                ['name' => 'Table 1', 'capacity' => 2, 'location' => 'Window side'],
                ['name' => 'Table 2', 'capacity' => 4, 'location' => 'Center'],
                ['name' => 'Table 3', 'capacity' => 6, 'location' => 'Corner'],
                ['name' => 'Table 4', 'capacity' => 2, 'location' => 'Window side'],
                ['name' => 'Table 5', 'capacity' => 4, 'location' => 'Center'],
                ['name' => 'Table 6', 'capacity' => 8, 'location' => 'Private area'],
            ],
            // First Floor - Medium tables
            1 => [
                ['name' => 'Table 7', 'capacity' => 4, 'location' => 'City view'],
                ['name' => 'Table 8', 'capacity' => 6, 'location' => 'Center'],
                ['name' => 'Table 9', 'capacity' => 2, 'location' => 'Quiet corner'],
                ['name' => 'Table 10', 'capacity' => 4, 'location' => 'City view'],
            ],
            // Rooftop - Fewer tables
            2 => [
                ['name' => 'Table 11', 'capacity' => 4, 'location' => 'Outdoor'],
                ['name' => 'Table 12', 'capacity' => 6, 'location' => 'Outdoor'],
                ['name' => 'Table 13', 'capacity' => 2, 'location' => 'Outdoor'],
            ],
        ];

        $tables = $tableConfigs[$floorIndex] ?? [];

        foreach ($tables as $index => $tableData) {
            Table::create([
                'name' => $tableData['name'],
                'number' => $tableData['name'], // Use name as number for simplicity
                'capacity' => $tableData['capacity'],
                'location' => $tableData['location'],
                'branch_id' => $floor->branch_id,
                'floor_id' => $floor->id,
                'is_active' => true,
                'is_available' => true,
                'sort_order' => $index + 1,
            ]);
        }
    }
}
