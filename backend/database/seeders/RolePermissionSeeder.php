<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use <PERSON>tie\Permission\Models\Role;
use <PERSON><PERSON>\Permission\Models\Permission;

class RolePermissionSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Reset cached roles and permissions
        app()[\Spatie\Permission\PermissionRegistrar::class]->forgetCachedPermissions();

        // Create permissions
        $permissions = [
            // Admin permissions
            'manage_tenants',
            'manage_subscriptions',
            'manage_payments',
            'view_admin_dashboard',
            'manage_system_settings',

            // Restaurant Manager permissions
            'manage_restaurant',
            'manage_menu',
            'manage_categories',
            'manage_subcategories',
            'manage_staff',
            'manage_branches',
            'manage_employees',
            'manage_employee_shifts',
            'view_reports',
            'manage_orders',
            'manage_customers',
            'manage_tables',
            'manage_reservations',
            'view_analytics',
            'manage_inventory',
            'manage_expenses',
            'manage_delivery_zones',
            'manage_promotions',

            // Waiter permissions
            'take_orders',
            'manage_tables',
            'view_menu',
            'update_order_status',
            'view_customer_info',
            'process_payments',
            'manage_reservations',

            // Kitchen permissions
            'view_kitchen_orders',
            'update_preparation_status',
            'mark_items_ready',
            'view_kitchen_display',
            'manage_inventory_usage',

            // Delivery permissions
            'view_delivery_orders',
            'accept_deliveries',
            'update_delivery_status',
            'update_location',
            'view_delivery_history',
            'view_earnings',

            // Review permissions
            'view_reviews',
            'create_reviews',
            'edit_reviews',
            'delete_reviews',
            'moderate_reviews',
        ];

        foreach ($permissions as $permission) {
            Permission::firstOrCreate(['name' => $permission]);
        }

        // Create roles and assign permissions

        // Admin role (has all permissions)
        $adminRole = Role::firstOrCreate(['name' => 'admin']);
        $adminRole->syncPermissions($permissions); // Give admin all permissions

        // Restaurant Manager role
        $managerRole = Role::firstOrCreate(['name' => 'restaurant_manager']);
        $managerRole->syncPermissions([
            'manage_restaurant',
            'manage_menu',
            'manage_categories',
            'manage_subcategories',
            'manage_staff',
            'manage_branches',
            'manage_employees',
            'manage_employee_shifts',
            'view_reports',
            'manage_orders',
            'manage_customers',
            'manage_tables',
            'manage_reservations',
            'view_analytics',
            'manage_inventory',
            'manage_expenses',
            'manage_delivery_zones',
            'manage_promotions',
            'take_orders',
            'view_menu',
            'update_order_status',
            'view_customer_info',
            'process_payments',
            'view_kitchen_orders',
            'update_preparation_status',
            'mark_items_ready',
            'view_delivery_orders',
            'view_reviews',
            'moderate_reviews',
        ]);

        // Waiter role
        $waiterRole = Role::firstOrCreate(['name' => 'waiter']);
        $waiterRole->syncPermissions([
            'take_orders',
            'manage_tables',
            'view_menu',
            'update_order_status',
            'view_customer_info',
            'process_payments',
            'manage_reservations',
            'view_reviews',
            'create_reviews',
        ]);

        // Kitchen role
        $kitchenRole = Role::firstOrCreate(['name' => 'kitchen']);
        $kitchenRole->syncPermissions([
            'view_kitchen_orders',
            'update_preparation_status',
            'mark_items_ready',
            'view_kitchen_display',
            'manage_inventory_usage',
            'view_menu',
        ]);

        // Delivery role
        $deliveryRole = Role::firstOrCreate(['name' => 'delivery']);
        $deliveryRole->syncPermissions([
            'view_delivery_orders',
            'accept_deliveries',
            'update_delivery_status',
            'update_location',
            'view_delivery_history',
            'view_earnings',
        ]);

        $this->command->info('Roles and permissions created successfully!');
    }
}
