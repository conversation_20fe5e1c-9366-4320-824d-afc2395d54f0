<?php

namespace Database\Seeders;

use App\Models\Tenant\Table;
use App\Models\Tenant\Restaurant;
use Illuminate\Database\Seeder;

class TableSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $restaurant = Restaurant::first();

        $tables = [
            // Indoor Tables
            ['name' => 'Table 1', 'capacity' => 2, 'location' => 'Indoor', 'section' => 'Window'],
            ['name' => 'Table 2', 'capacity' => 2, 'location' => 'Indoor', 'section' => 'Window'],
            ['name' => 'Table 3', 'capacity' => 4, 'location' => 'Indoor', 'section' => 'Main'],
            ['name' => 'Table 4', 'capacity' => 4, 'location' => 'Indoor', 'section' => 'Main'],
            ['name' => 'Table 5', 'capacity' => 4, 'location' => 'Indoor', 'section' => 'Main'],
            ['name' => 'Table 6', 'capacity' => 6, 'location' => 'Indoor', 'section' => 'Main'],
            ['name' => 'Table 7', 'capacity' => 6, 'location' => 'Indoor', 'section' => 'Main'],
            ['name' => 'Table 8', 'capacity' => 8, 'location' => 'Indoor', 'section' => 'Private'],
            
            // Bar Tables
            ['name' => 'Bar 1', 'capacity' => 2, 'location' => 'Bar', 'section' => 'Bar'],
            ['name' => 'Bar 2', 'capacity' => 2, 'location' => 'Bar', 'section' => 'Bar'],
            ['name' => 'Bar 3', 'capacity' => 3, 'location' => 'Bar', 'section' => 'Bar'],
            ['name' => 'Bar 4', 'capacity' => 3, 'location' => 'Bar', 'section' => 'Bar'],
            
            // Outdoor Tables (Patio)
            ['name' => 'Patio 1', 'capacity' => 4, 'location' => 'Outdoor', 'section' => 'Patio'],
            ['name' => 'Patio 2', 'capacity' => 4, 'location' => 'Outdoor', 'section' => 'Patio'],
            ['name' => 'Patio 3', 'capacity' => 6, 'location' => 'Outdoor', 'section' => 'Patio'],
            ['name' => 'Patio 4', 'capacity' => 6, 'location' => 'Outdoor', 'section' => 'Patio'],
            
            // Private Dining
            ['name' => 'Private Room A', 'capacity' => 12, 'location' => 'Private', 'section' => 'Private Dining'],
            ['name' => 'Private Room B', 'capacity' => 8, 'location' => 'Private', 'section' => 'Private Dining'],
        ];

        foreach ($tables as $index => $tableData) {
            Table::create([
                'restaurant_id' => $restaurant->id,
                'name' => $tableData['name'],
                'capacity' => $tableData['capacity'],
                'location' => $tableData['location'],
                'section' => $tableData['section'],
                'sort_order' => $index + 1,
                'is_active' => true,
                'status' => 'available',
            ]);
        }
    }
}
