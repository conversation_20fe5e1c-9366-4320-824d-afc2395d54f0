<?php

namespace Database\Seeders\Tenant;

use Illuminate\Database\Seeder;
use Spatie\Permission\Models\Role;
use Spatie\Permission\Models\Permission;

class RolesAndPermissionsSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Reset cached roles and permissions
        app()[\Spatie\Permission\PermissionRegistrar::class]->forgetCachedPermissions();

        // Create permissions
        $permissions = [
            // Branch Management
            'view branches',
            'create branches',
            'edit branches',
            'delete branches',
            'manage branch staff',
            
            // Menu Management
            'view menu',
            'create menu items',
            'edit menu items',
            'delete menu items',
            'manage categories',
            
            // Order Management
            'view orders',
            'create orders',
            'edit orders',
            'cancel orders',
            'process payments',
            
            // Staff Management
            'view staff',
            'create staff',
            'edit staff',
            'delete staff',
            'manage schedules',
            'view payroll',
            
            // Customer Management
            'view customers',
            'create customers',
            'edit customers',
            'delete customers',
            
            // Inventory Management
            'view inventory',
            'manage inventory',
            'create purchase orders',
            'approve purchase orders',
            
            // Reports
            'view reports',
            'export reports',
            'view analytics',
            
            // Settings
            'manage settings',
            'manage restaurant profile',
            'manage integrations',
        ];

        foreach ($permissions as $permission) {
            Permission::create(['name' => $permission]);
        }

        // Create roles and assign permissions
        
        // Manager Role - Full access
        $managerRole = Role::create(['name' => 'Manager']);
        $managerRole->givePermissionTo(Permission::all());

        // Waiter Role - Limited access
        $waiterRole = Role::create(['name' => 'Waiter']);
        $waiterRole->givePermissionTo([
            'view menu',
            'view orders',
            'create orders',
            'edit orders',
            'view customers',
            'create customers',
            'edit customers',
        ]);

        // Chef/Kitchen Role - Kitchen focused
        $chefRole = Role::create(['name' => 'Chef']);
        $chefRole->givePermissionTo([
            'view menu',
            'view orders',
            'edit orders',
            'view inventory',
        ]);

        // Kitchen Role (alias for Chef)
        $kitchenRole = Role::create(['name' => 'Kitchen']);
        $kitchenRole->givePermissionTo([
            'view menu',
            'view orders',
            'edit orders',
            'view inventory',
        ]);

        // Cashier Role - Payment focused
        $cashierRole = Role::create(['name' => 'Cashier']);
        $cashierRole->givePermissionTo([
            'view menu',
            'view orders',
            'create orders',
            'edit orders',
            'process payments',
            'view customers',
            'create customers',
        ]);

        // Delivery Role - Delivery focused
        $deliveryRole = Role::create(['name' => 'Delivery']);
        $deliveryRole->givePermissionTo([
            'view orders',
            'edit orders',
            'view customers',
        ]);

        // Admin Role - Super admin access
        $adminRole = Role::create(['name' => 'Admin']);
        $adminRole->givePermissionTo(Permission::all());

        $this->command->info('Roles and permissions created successfully!');
    }
}
