<?php

namespace Database\Seeders;

use App\Models\Tenant;
use App\Models\User;
use App\Models\SubscriptionPlan;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Artisan;
use Illuminate\Support\Facades\DB;

class TenantSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Get the first user to be the owner
        $user = User::first();

        if (!$user) {
            $this->command->error('No users found. Please run UserSeeder first.');
            return;
        }

        // Get a subscription plan
        $plan = SubscriptionPlan::where('name', 'Basic')->first();

        // Check if tenant already exists
        $existingTenant = Tenant::find('demo-restaurant');
        $tenantExists = $existingTenant !== null;

        // Handle existing tenant database before creating tenant record
        if (!$tenantExists) {
            $databaseName = 'tenant_demo-restaurant';
            $databaseExists = $this->checkDatabaseExists($databaseName);

            if ($databaseExists) {
                $this->command->warn("Tenant database {$databaseName} already exists. Dropping it...");
                try {
                    DB::statement("DROP DATABASE IF EXISTS `{$databaseName}`");
                    $this->command->info("Dropped existing database: {$databaseName}");
                } catch (\Exception $e) {
                    $this->command->error("Failed to drop database: " . $e->getMessage());
                }
            }
        }

        // Create demo restaurant tenant
        $tenant = Tenant::firstOrCreate(
            ['id' => 'demo-restaurant'],
            [
                'name' => 'Demo Restaurant',
                'email' => '<EMAIL>',
                'phone' => '******-0123',
                'address' => '123 Main Street',
                'city' => 'New York',
                'state' => 'NY',
                'country' => 'USA',
                'postal_code' => '10001',
                'timezone' => 'America/New_York',
                'currency' => 'USD',
                'language' => 'en',
                'subscription_plan_id' => $plan?->id,
                'subscription_status' => 'active',
                'trial_ends_at' => now()->addDays(30),
            ]
        );

        // Create domains for the tenant
        $tenant->domains()->firstOrCreate([
            'domain' => 'demo-restaurant.localhost',
        ]);

        $this->command->info("Tenant: {$tenant->name} with domain: demo-restaurant.localhost");

        // Handle tenant database setup
        if (!$tenantExists) {
            // Tenant was just created, set up database
            $this->setupTenantDatabase($tenant);
        } else {
            $this->command->info("Tenant already exists, skipping database setup.");
        }
    }

    /**
     * Set up tenant database with proper error handling
     */
    private function setupTenantDatabase($tenant)
    {
        $this->command->info("Setting up tenant database...");

        try {
            // Try to create and migrate the tenant database
            Artisan::call('tenants:migrate', ['--tenants' => [$tenant->id]]);

            // Seed tenant database
            $this->command->info("Seeding tenant database...");
            Artisan::call('tenants:seed', ['--tenants' => [$tenant->id]]);

            $this->command->info("Tenant setup completed successfully!");

        } catch (\Stancl\Tenancy\Exceptions\TenantDatabaseAlreadyExistsException $e) {
            // Database exists but tenant record was recreated (e.g., after migrate:fresh)
            $this->command->warn("Tenant database already exists. Attempting to use existing database...");

            try {
                // Check if we can connect to the existing database
                $databaseName = 'tenant_' . $tenant->id;
                $this->command->info("Checking existing database: {$databaseName}");

                // Try to run migrations on existing database
                Artisan::call('tenants:migrate', ['--tenants' => [$tenant->id], '--force' => true]);
                $this->command->info("Successfully connected to existing tenant database!");

            } catch (\Exception $e2) {
                // If we can't use the existing database, try to drop and recreate it
                $this->command->warn("Cannot use existing database. Attempting to recreate...");

                try {
                    $databaseName = 'tenant_' . $tenant->id;
                    DB::statement("DROP DATABASE IF EXISTS `{$databaseName}`");
                    $this->command->info("Dropped existing database: {$databaseName}");

                    // Now try to create fresh
                    Artisan::call('tenants:migrate', ['--tenants' => [$tenant->id]]);
                    Artisan::call('tenants:seed', ['--tenants' => [$tenant->id]]);

                    $this->command->info("Tenant database recreated successfully!");

                } catch (\Exception $e3) {
                    $this->command->error("Failed to set up tenant database: " . $e3->getMessage());
                    $this->command->warn("You may need to manually drop the database: tenant_{$tenant->id}");
                }
            }

        } catch (\Exception $e) {
            $this->command->error("Tenant database setup failed: " . $e->getMessage());
        }
    }

    /**
     * Check if a database exists
     */
    private function checkDatabaseExists($databaseName): bool
    {
        try {
            $databases = DB::select('SHOW DATABASES');
            foreach ($databases as $database) {
                if ($database->Database === $databaseName) {
                    return true;
                }
            }
            return false;
        } catch (\Exception $e) {
            return false;
        }
    }
}
