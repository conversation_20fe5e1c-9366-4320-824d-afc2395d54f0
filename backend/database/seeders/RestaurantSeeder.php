<?php

namespace Database\Seeders;

use App\Models\Tenant\Restaurant;
use Illuminate\Database\Seeder;

class RestaurantSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        Restaurant::create([
            'name' => 'Demo Restaurant',
            'slug' => 'demo-restaurant',
            'description' => 'A wonderful place to dine with family and friends. We serve fresh, delicious food in a warm and welcoming atmosphere.',
            'email' => '<EMAIL>',
            'phone' => '******-0123',
            'address' => '123 Main Street',
            'city' => 'New York',
            'state' => 'NY',
            'country' => 'USA',
            'postal_code' => '10001',
            'currency' => 'USD',
            'timezone' => 'America/New_York',
            'language' => 'en',
            'tax_rate' => 8.25,
            'service_charge' => 10.00,
            'delivery_charge' => 5.00,
            'minimum_order_amount' => 15.00,
            'opening_time' => '09:00',
            'closing_time' => '22:00',
            'is_active' => true,
        ]);
    }
}
