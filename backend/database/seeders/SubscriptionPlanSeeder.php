<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\SubscriptionPlan;

class SubscriptionPlanSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $plans = [
            [
                'name' => 'Starter',
                'slug' => 'starter',
                'description' => 'Perfect for small restaurants just getting started',
                'price' => 0,
                'billing_cycle' => 'monthly',
                'trial_days' => 14,
                'features' => [
                    'Up to 50 menu items',
                    'Up to 10 tables',
                    'Up to 5 staff members',
                    'Basic reporting',
                    'Email support',
                ],
                'max_orders_per_month' => 500,
                'max_menu_items' => 50,
                'max_tables' => 10,
                'max_staff' => 5,
                'has_delivery' => false,
                'has_analytics' => false,
                'has_multi_location' => false,
                'has_api_access' => false,
                'is_active' => true,
                'sort_order' => 1,
            ],
            [
                'name' => 'Professional',
                'slug' => 'professional',
                'description' => 'Great for growing restaurants with more features',
                'price' => 29.99,
                'billing_cycle' => 'monthly',
                'trial_days' => 14,
                'features' => [
                    'Up to 200 menu items',
                    'Up to 25 tables',
                    'Up to 15 staff members',
                    'Advanced reporting',
                    'Delivery management',
                    'Priority support',
                ],
                'max_orders_per_month' => 2000,
                'max_menu_items' => 200,
                'max_tables' => 25,
                'max_staff' => 15,
                'has_delivery' => true,
                'has_analytics' => true,
                'has_multi_location' => false,
                'has_api_access' => false,
                'is_active' => true,
                'sort_order' => 2,
            ],
            [
                'name' => 'Enterprise',
                'slug' => 'enterprise',
                'description' => 'For large restaurants and chains with unlimited features',
                'price' => 99.99,
                'billing_cycle' => 'monthly',
                'trial_days' => 14,
                'features' => [
                    'Unlimited menu items',
                    'Unlimited tables',
                    'Unlimited staff members',
                    'Advanced analytics',
                    'Multi-location support',
                    'API access',
                    'Custom integrations',
                    '24/7 phone support',
                ],
                'max_orders_per_month' => null,
                'max_menu_items' => null,
                'max_tables' => null,
                'max_staff' => null,
                'has_delivery' => true,
                'has_analytics' => true,
                'has_multi_location' => true,
                'has_api_access' => true,
                'is_active' => true,
                'sort_order' => 3,
            ],
        ];

        foreach ($plans as $plan) {
            SubscriptionPlan::firstOrCreate(
                ['slug' => $plan['slug']],
                $plan
            );
        }
    }
}
