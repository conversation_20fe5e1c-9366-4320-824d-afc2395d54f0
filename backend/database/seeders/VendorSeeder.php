<?php

namespace Database\Seeders;

use App\Models\Tenant\Vendor;
use App\Models\Tenant\Restaurant;
use Illuminate\Database\Seeder;

class VendorSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $restaurant = Restaurant::first();

        $vendors = [
            [
                'name' => 'Fresh Foods Wholesale',
                'company_name' => 'Fresh Foods Wholesale Inc.',
                'vendor_type' => 'supplier',
                'contact_person' => '<PERSON>',
                'email' => '<EMAIL>',
                'phone' => '******-0101',
                'mobile' => '******-0102',
                'website' => 'https://freshfoods.com',
                'address' => '456 Wholesale Ave',
                'city' => 'New York',
                'state' => 'NY',
                'country' => 'USA',
                'postal_code' => '10002',
                'payment_terms' => 'Net 30',
                'credit_limit' => 10000.00,
                'discount_percentage' => 5.0,
                'rating' => 4.5,
            ],
            [
                'name' => 'Premium Meats Co.',
                'company_name' => 'Premium Meats Company LLC',
                'vendor_type' => 'supplier',
                'contact_person' => '<PERSON> Johnson',
                'email' => '<EMAIL>',
                'phone' => '******-0201',
                'mobile' => '******-0202',
                'address' => '789 Meat Market St',
                'city' => 'Brooklyn',
                'state' => 'NY',
                'country' => 'USA',
                'postal_code' => '11201',
                'payment_terms' => 'Net 15',
                'credit_limit' => 8000.00,
                'discount_percentage' => 3.0,
                'rating' => 4.8,
            ],
            [
                'name' => 'Ocean Fresh Seafood',
                'company_name' => 'Ocean Fresh Seafood Ltd.',
                'vendor_type' => 'supplier',
                'contact_person' => 'Mike Rodriguez',
                'email' => '<EMAIL>',
                'phone' => '******-0301',
                'address' => '321 Harbor Blvd',
                'city' => 'Queens',
                'state' => 'NY',
                'country' => 'USA',
                'postal_code' => '11101',
                'payment_terms' => 'Net 7',
                'credit_limit' => 6000.00,
                'discount_percentage' => 2.0,
                'rating' => 4.3,
            ],
            [
                'name' => 'Beverage Distributors Inc.',
                'company_name' => 'Beverage Distributors Inc.',
                'vendor_type' => 'supplier',
                'contact_person' => 'Lisa Chen',
                'email' => '<EMAIL>',
                'phone' => '******-0401',
                'website' => 'https://beveragedist.com',
                'address' => '654 Distribution Way',
                'city' => 'Bronx',
                'state' => 'NY',
                'country' => 'USA',
                'postal_code' => '10451',
                'payment_terms' => 'Net 30',
                'credit_limit' => 5000.00,
                'discount_percentage' => 4.0,
                'rating' => 4.2,
            ],
            [
                'name' => 'Kitchen Equipment Pro',
                'company_name' => 'Kitchen Equipment Pro LLC',
                'vendor_type' => 'supplier',
                'contact_person' => 'David Wilson',
                'email' => '<EMAIL>',
                'phone' => '******-0501',
                'website' => 'https://kitchenpro.com',
                'address' => '987 Equipment Rd',
                'city' => 'Manhattan',
                'state' => 'NY',
                'country' => 'USA',
                'postal_code' => '10003',
                'payment_terms' => 'Net 45',
                'credit_limit' => 15000.00,
                'discount_percentage' => 8.0,
                'rating' => 4.6,
            ],
            [
                'name' => 'CleanPro Services',
                'company_name' => 'CleanPro Commercial Services',
                'vendor_type' => 'service_provider',
                'contact_person' => 'Maria Garcia',
                'email' => '<EMAIL>',
                'phone' => '******-0601',
                'address' => '147 Service Lane',
                'city' => 'Staten Island',
                'state' => 'NY',
                'country' => 'USA',
                'postal_code' => '10301',
                'payment_terms' => 'Net 15',
                'credit_limit' => 2000.00,
                'rating' => 4.4,
            ],
            [
                'name' => 'Metro Utilities',
                'company_name' => 'Metropolitan Utilities Corporation',
                'vendor_type' => 'utility',
                'contact_person' => 'Robert Taylor',
                'email' => '<EMAIL>',
                'phone' => '******-0701',
                'website' => 'https://metroutilities.com',
                'address' => '555 Utility Plaza',
                'city' => 'New York',
                'state' => 'NY',
                'country' => 'USA',
                'postal_code' => '10004',
                'payment_terms' => 'Net 30',
                'rating' => 4.0,
            ],
            [
                'name' => 'TechFix Solutions',
                'company_name' => 'TechFix Solutions Inc.',
                'vendor_type' => 'service_provider',
                'contact_person' => 'Kevin Lee',
                'email' => '<EMAIL>',
                'phone' => '******-0801',
                'mobile' => '******-0802',
                'website' => 'https://techfix.com',
                'address' => '258 Tech Street',
                'city' => 'New York',
                'state' => 'NY',
                'country' => 'USA',
                'postal_code' => '10005',
                'payment_terms' => 'Net 15',
                'credit_limit' => 3000.00,
                'rating' => 4.7,
            ],
        ];

        foreach ($vendors as $vendorData) {
            Vendor::create([
                'restaurant_id' => $restaurant->id,
                'name' => $vendorData['name'],
                'company_name' => $vendorData['company_name'],
                'vendor_type' => $vendorData['vendor_type'],
                'contact_person' => $vendorData['contact_person'],
                'email' => $vendorData['email'],
                'phone' => $vendorData['phone'],
                'mobile' => $vendorData['mobile'] ?? null,
                'website' => $vendorData['website'] ?? null,
                'address' => $vendorData['address'],
                'city' => $vendorData['city'],
                'state' => $vendorData['state'],
                'country' => $vendorData['country'],
                'postal_code' => $vendorData['postal_code'],
                'payment_terms' => $vendorData['payment_terms'],
                'credit_limit' => $vendorData['credit_limit'] ?? null,
                'discount_percentage' => $vendorData['discount_percentage'] ?? 0,
                'rating' => $vendorData['rating'] ?? null,
                'is_active' => true,
            ]);
        }
    }
}
