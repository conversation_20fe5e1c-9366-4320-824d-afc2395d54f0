<?php

namespace Database\Seeders;

use App\Models\User;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Hash;

class UserSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Create Super Admin
        $admin = User::firstOrCreate(
            ['email' => '<EMAIL>'],
            [
                'name' => 'Super Admin',
                'password' => Hash::make('Restaurant@2024'),
                'email_verified_at' => now(),
                'role' => 'admin',
                'preferred_language' => 'en',
                'theme_preference' => 'light',
                'is_active' => true,
            ]
        );
        if (!$admin->hasRole('admin')) {
            $admin->assignRole('admin');
        }

        // Create Restaurant Manager
        $manager = User::firstOrCreate(
            ['email' => '<EMAIL>'],
            [
                'name' => 'Restaurant Manager',
                'password' => Hash::make('Manager@2024'),
                'email_verified_at' => now(),
                'role' => 'restaurant_manager',
                'preferred_language' => 'en',
                'theme_preference' => 'light',
                'is_active' => true,
            ]
        );
        if (!$manager->hasRole('restaurant_manager')) {
            $manager->assignRole('restaurant_manager');
        }

        // Create Head Waiter
        $headWaiter = User::firstOrCreate(
            ['email' => '<EMAIL>'],
            [
                'name' => 'John Smith',
                'password' => Hash::make('Waiter@2024'),
                'email_verified_at' => now(),
                'role' => 'waiter',
                'preferred_language' => 'en',
                'theme_preference' => 'light',
                'is_active' => true,
            ]
        );
        if (!$headWaiter->hasRole('waiter')) {
            $headWaiter->assignRole('waiter');
        }

        // Create Kitchen Staff
        $kitchenStaff = User::firstOrCreate(
            ['email' => '<EMAIL>'],
            [
                'name' => 'Chef Maria',
                'password' => Hash::make('Kitchen@2024'),
                'email_verified_at' => now(),
                'role' => 'kitchen',
                'preferred_language' => 'en',
                'theme_preference' => 'light',
                'is_active' => true,
            ]
        );
        if (!$kitchenStaff->hasRole('kitchen')) {
            $kitchenStaff->assignRole('kitchen');
        }

        // Create Delivery Driver
        $deliveryDriver = User::firstOrCreate(
            ['email' => '<EMAIL>'],
            [
                'name' => 'Ahmed Rahman',
                'password' => Hash::make('Delivery@2024'),
                'email_verified_at' => now(),
                'role' => 'delivery',
                'preferred_language' => 'bn',
                'theme_preference' => 'light',
                'is_active' => true,
            ]
        );
        if (!$deliveryDriver->hasRole('delivery')) {
            $deliveryDriver->assignRole('delivery');
        }

        // Create additional waiters
        for ($i = 1; $i <= 3; $i++) {
            $waiter = User::firstOrCreate(
                ['email' => "waiter{$i}@restaurant.com"],
                [
                    'name' => "Waiter {$i}",
                    'password' => Hash::make('password'),
                    'email_verified_at' => now(),
                    'role' => 'waiter',
                    'preferred_language' => 'en',
                    'theme_preference' => 'light',
                    'is_active' => true,
                ]
            );
            if (!$waiter->hasRole('waiter')) {
                $waiter->assignRole('waiter');
            }
        }

        // Create additional kitchen staff
        for ($i = 1; $i <= 2; $i++) {
            $kitchen = User::firstOrCreate(
                ['email' => "kitchen{$i}@restaurant.com"],
                [
                    'name' => "Kitchen Staff {$i}",
                    'password' => Hash::make('password'),
                    'email_verified_at' => now(),
                    'role' => 'kitchen',
                    'preferred_language' => 'en',
                    'theme_preference' => 'light',
                    'is_active' => true,
                ]
            );
            if (!$kitchen->hasRole('kitchen')) {
                $kitchen->assignRole('kitchen');
            }
        }

        // Create additional delivery drivers
        for ($i = 1; $i <= 2; $i++) {
            $delivery = User::firstOrCreate(
                ['email' => "driver{$i}@restaurant.com"],
                [
                    'name' => "Driver {$i}",
                    'password' => Hash::make('password'),
                    'email_verified_at' => now(),
                    'role' => 'delivery',
                    'preferred_language' => 'bn',
                    'theme_preference' => 'light',
                    'is_active' => true,
                ]
            );
            if (!$delivery->hasRole('delivery')) {
                $delivery->assignRole('delivery');
            }
        }

        $this->command->info('Demo users created successfully!');
        $this->command->info('Login credentials:');
        $this->command->info('Admin: <EMAIL> / Restaurant@2024');
        $this->command->info('Manager: <EMAIL> / Manager@2024');
        $this->command->info('Waiter: <EMAIL> / Waiter@2024');
        $this->command->info('Kitchen: <EMAIL> / Kitchen@2024');
        $this->command->info('Delivery: <EMAIL> / Delivery@2024');
    }
}
