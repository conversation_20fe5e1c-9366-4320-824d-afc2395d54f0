<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\Tenant;
use App\Models\SubscriptionPlan;
use App\Models\TenantSubscription;
use App\Models\SubscriptionUsage;
use Stancl\Tenancy\Facades\Tenancy;

class DemoTenantSubscriptionSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Find the demo tenant
        $tenant = Tenant::where('id', 'demo-restaurant')->first();

        if (!$tenant) {
            $this->command->error('Demo tenant not found!');
            return;
        }

        // Find the Basic Plus plan
        $basicPlusPlan = SubscriptionPlan::where('slug', 'basic-plus')->first();

        if (!$basicPlusPlan) {
            $this->command->error('Basic Plus plan not found!');
            return;
        }

        // Update tenant subscription plan reference
        $tenant->update([
            'subscription_plan_id' => $basicPlusPlan->id,
            'subscription_status' => 'active',
            'trial_ends_at' => now()->addDays(14),
            'subscription_ends_at' => now()->addMonth(),
        ]);

        // Create tenant subscription record
        $subscription = TenantSubscription::updateOrCreate(
            ['tenant_id' => $tenant->id],
            [
                'subscription_plan_id' => $basicPlusPlan->id,
                'status' => 'active',
                'trial_ends_at' => now()->addDays(14),
                'current_period_start' => now()->startOfMonth(),
                'current_period_end' => now()->endOfMonth(),
                'amount' => $basicPlusPlan->price,
                'currency' => $basicPlusPlan->currency,
                'billing_cycle' => $basicPlusPlan->billing_cycle,
                'metadata' => [
                    'assigned_at' => now()->toISOString(),
                    'assigned_by' => 'system',
                    'reason' => 'demo_setup',
                ],
            ]
        );

        // Initialize tenant context to get current counts from tenant database
        Tenancy::initialize($tenant);

        // Get current counts from tenant database
        $menuItemsCount = \App\Models\Tenant\MenuItem::count();
        $pagesCount = \App\Models\Tenant\Page::count();
        $branchesCount = \App\Models\Tenant\Branch::count();
        $staffCount = \App\Models\User::whereHas('roles', function($q) {
            $q->whereIn('name', ['waiter', 'chef', 'manager']);
        })->count();
        $ordersThisMonth = \App\Models\Tenant\Order::whereMonth('created_at', now()->month)
                                                  ->whereYear('created_at', now()->year)
                                                  ->count();

        // End tenant context to work with central database
        Tenancy::end();

        // Create current period usage record in central database
        SubscriptionUsage::updateOrCreate(
            [
                'tenant_id' => $tenant->id,
                'period_start' => now()->startOfMonth()->toDateString(),
                'period_end' => now()->endOfMonth()->toDateString(),
            ],
            [
                'subscription_plan_id' => $basicPlusPlan->id,
                'menu_items_used' => $ordersThisMonth,
                'orders_used' => $ordersThisMonth,
                'pages_used' => 0, // Reset monthly
                'branches_used' => $branchesCount,
                'staff_used' => $staffCount,
                'total_menu_items' => $menuItemsCount,
                'total_pages' => $pagesCount,
                'total_branches' => $branchesCount,
                'revenue_generated' => 15000.00, // Demo revenue
                'customers_served' => 45, // Demo customers
            ]
        );

        $this->command->info("Demo tenant '{$tenant->id}' assigned to Basic Plus plan successfully!");
        $this->command->info("Current usage: {$menuItemsCount} menu items, {$pagesCount} pages, {$branchesCount} branches, {$staffCount} staff");
    }
}
