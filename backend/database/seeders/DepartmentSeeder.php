<?php

namespace Database\Seeders;

use App\Models\Tenant\Department;
use App\Models\Tenant\Restaurant;
use Illuminate\Database\Seeder;

class DepartmentSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $restaurant = Restaurant::first();

        $departments = [
            [
                'name' => 'Kitchen',
                'description' => 'Food preparation and cooking staff',
                'color_code' => '#FF6B6B',
                'icon' => 'utensils',
                'budget_limit' => 15000.00,
                'max_employees' => 8,
                'sort_order' => 1,
            ],
            [
                'name' => 'Front of House',
                'description' => 'Servers, hosts, and customer service staff',
                'color_code' => '#4ECDC4',
                'icon' => 'users',
                'budget_limit' => 12000.00,
                'max_employees' => 12,
                'sort_order' => 2,
            ],
            [
                'name' => 'Management',
                'description' => 'Restaurant managers and supervisors',
                'color_code' => '#45B7D1',
                'icon' => 'user-tie',
                'budget_limit' => 8000.00,
                'max_employees' => 3,
                'sort_order' => 3,
            ],
            [
                'name' => 'Bar',
                'description' => 'Bartenders and bar staff',
                'color_code' => '#F7DC6F',
                'icon' => 'wine-glass',
                'budget_limit' => 6000.00,
                'max_employees' => 4,
                'sort_order' => 4,
            ],
            [
                'name' => 'Cleaning',
                'description' => 'Cleaning and maintenance staff',
                'color_code' => '#BB8FCE',
                'icon' => 'broom',
                'budget_limit' => 4000.00,
                'max_employees' => 3,
                'sort_order' => 5,
            ],
            [
                'name' => 'Delivery',
                'description' => 'Delivery drivers and logistics',
                'color_code' => '#82E0AA',
                'icon' => 'truck',
                'budget_limit' => 5000.00,
                'max_employees' => 5,
                'sort_order' => 6,
            ],
        ];

        foreach ($departments as $departmentData) {
            Department::create([
                'restaurant_id' => $restaurant->id,
                'name' => $departmentData['name'],
                'description' => $departmentData['description'],
                'color_code' => $departmentData['color_code'],
                'icon' => $departmentData['icon'],
                'budget_limit' => $departmentData['budget_limit'],
                'max_employees' => $departmentData['max_employees'],
                'sort_order' => $departmentData['sort_order'],
                'is_active' => true,
            ]);
        }
    }
}
