<?php

namespace Database\Seeders;

use App\Models\Tenant\MenuItem;
use App\Models\Tenant\Category;
use App\Models\Tenant\Restaurant;
use App\Models\Tenant\Branch;
use Illuminate\Database\Seeder;

class MenuItemSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $restaurant = Restaurant::first();
        $categories = Category::all();

        foreach ($categories as $category) {
            $this->createMenuItems($restaurant->id, $category->id, $category->name);
        }

        // Associate all menu items with all branches
        $this->associateMenuItemsWithBranches();
    }

    private function createMenuItems($restaurantId, $categoryId, $categoryName)
    {
        $items = [];

        switch ($categoryName) {
            case 'Appetizers':
                $items = [
                    [
                        'name' => 'Caesar Salad',
                        'description' => 'Fresh romaine lettuce with caesar dressing, parmesan cheese, and croutons',
                        'price' => 12.99,
                        'preparation_time' => 10,
                        'calories' => 350,
                        'is_vegetarian' => true,
                    ],
                    [
                        'name' => 'Buffalo Chicken Wings',
                        'description' => 'Spicy buffalo wings served with blue cheese dip and celery sticks',
                        'price' => 14.99,
                        'preparation_time' => 15,
                        'calories' => 480,
                        'is_spicy' => true,
                    ],
                    [
                        'name' => 'Mozzarella Sticks',
                        'description' => 'Crispy breaded mozzarella sticks with marinara sauce',
                        'price' => 9.99,
                        'preparation_time' => 12,
                        'calories' => 320,
                        'is_vegetarian' => true,
                    ],
                    [
                        'name' => 'Calamari Rings',
                        'description' => 'Golden fried squid rings with spicy aioli sauce',
                        'price' => 13.99,
                        'preparation_time' => 15,
                        'calories' => 380,
                    ],
                ];
                break;

            case 'Main Courses':
                $items = [
                    [
                        'name' => 'Grilled Atlantic Salmon',
                        'description' => 'Fresh Atlantic salmon grilled to perfection with lemon butter sauce and seasonal vegetables',
                        'price' => 24.99,
                        'preparation_time' => 20,
                        'calories' => 520,
                        'is_gluten_free' => true,
                    ],
                    [
                        'name' => 'Prime Ribeye Steak',
                        'description' => 'Premium 12oz ribeye steak cooked to your preference with garlic mashed potatoes',
                        'price' => 32.99,
                        'preparation_time' => 25,
                        'calories' => 680,
                        'is_gluten_free' => true,
                    ],
                    [
                        'name' => 'Chicken Parmesan',
                        'description' => 'Breaded chicken breast topped with marinara sauce and melted mozzarella, served with pasta',
                        'price' => 19.99,
                        'preparation_time' => 22,
                        'calories' => 590,
                    ],
                    [
                        'name' => 'Vegetarian Pasta Primavera',
                        'description' => 'Fresh pasta with seasonal vegetables in a light cream sauce',
                        'price' => 16.99,
                        'preparation_time' => 18,
                        'calories' => 450,
                        'is_vegetarian' => true,
                    ],
                    [
                        'name' => 'BBQ Pork Ribs',
                        'description' => 'Slow-cooked pork ribs with our signature BBQ sauce and coleslaw',
                        'price' => 26.99,
                        'preparation_time' => 30,
                        'calories' => 720,
                    ],
                ];
                break;

            case 'Desserts':
                $items = [
                    [
                        'name' => 'Chocolate Lava Cake',
                        'description' => 'Warm chocolate cake with molten center, served with vanilla ice cream',
                        'price' => 8.99,
                        'preparation_time' => 15,
                        'calories' => 420,
                        'is_vegetarian' => true,
                    ],
                    [
                        'name' => 'New York Cheesecake',
                        'description' => 'Classic New York style cheesecake with berry compote',
                        'price' => 7.99,
                        'preparation_time' => 5,
                        'calories' => 380,
                        'is_vegetarian' => true,
                    ],
                    [
                        'name' => 'Tiramisu',
                        'description' => 'Traditional Italian dessert with coffee-soaked ladyfingers and mascarpone',
                        'price' => 8.49,
                        'preparation_time' => 5,
                        'calories' => 350,
                        'is_vegetarian' => true,
                    ],
                ];
                break;

            case 'Beverages':
                $items = [
                    [
                        'name' => 'Freshly Brewed Coffee',
                        'description' => 'Premium coffee beans freshly brewed to perfection',
                        'price' => 3.99,
                        'preparation_time' => 3,
                        'calories' => 5,
                        'is_vegan' => true,
                    ],
                    [
                        'name' => 'Fresh Orange Juice',
                        'description' => 'Freshly squeezed orange juice, no added sugar',
                        'price' => 4.99,
                        'preparation_time' => 2,
                        'calories' => 110,
                        'is_vegan' => true,
                    ],
                    [
                        'name' => 'Craft Beer Selection',
                        'description' => 'Local craft beer on tap - ask your server for today\'s selection',
                        'price' => 6.99,
                        'preparation_time' => 1,
                        'calories' => 150,
                    ],
                    [
                        'name' => 'House Wine',
                        'description' => 'Red or white wine by the glass',
                        'price' => 8.99,
                        'preparation_time' => 1,
                        'calories' => 120,
                    ],
                ];
                break;

            case 'Salads':
                $items = [
                    [
                        'name' => 'Greek Salad',
                        'description' => 'Mixed greens with feta cheese, olives, tomatoes, and Greek dressing',
                        'price' => 11.99,
                        'preparation_time' => 8,
                        'calories' => 280,
                        'is_vegetarian' => true,
                        'is_gluten_free' => true,
                    ],
                    [
                        'name' => 'Grilled Chicken Salad',
                        'description' => 'Mixed greens with grilled chicken, avocado, and balsamic vinaigrette',
                        'price' => 14.99,
                        'preparation_time' => 12,
                        'calories' => 380,
                        'is_gluten_free' => true,
                    ],
                ];
                break;

            case 'Soups':
                $items = [
                    [
                        'name' => 'Tomato Basil Soup',
                        'description' => 'Creamy tomato soup with fresh basil and a touch of cream',
                        'price' => 6.99,
                        'preparation_time' => 5,
                        'calories' => 180,
                        'is_vegetarian' => true,
                    ],
                    [
                        'name' => 'Chicken Noodle Soup',
                        'description' => 'Classic chicken soup with vegetables and egg noodles',
                        'price' => 7.99,
                        'preparation_time' => 5,
                        'calories' => 220,
                    ],
                ];
                break;
        }

        foreach ($items as $index => $item) {
            MenuItem::create([
                'restaurant_id' => $restaurantId,
                'category_id' => $categoryId,
                'name' => $item['name'],
                'slug' => \Str::slug($item['name']),
                'description' => $item['description'],
                'price' => $item['price'],
                'preparation_time' => $item['preparation_time'],
                'calories' => $item['calories'] ?? null,
                'is_featured' => $index === 0, // Make first item in each category featured
                'is_available' => true,
                'is_vegetarian' => $item['is_vegetarian'] ?? false,
                'is_vegan' => $item['is_vegan'] ?? false,
                'is_gluten_free' => $item['is_gluten_free'] ?? false,
                'is_spicy' => $item['is_spicy'] ?? false,
                'sort_order' => $index + 1,
            ]);
        }
    }

    /**
     * Associate all menu items with all branches
     */
    private function associateMenuItemsWithBranches(): void
    {
        $menuItems = MenuItem::all();
        $branches = Branch::all();

        foreach ($menuItems as $menuItem) {
            foreach ($branches as $branch) {
                $menuItem->branches()->attach($branch->id, [
                    'is_available' => true,
                    'branch_specific_price' => null,
                ]);
            }
        }
    }
}
