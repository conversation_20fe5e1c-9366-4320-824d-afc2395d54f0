<?php

namespace Database\Seeders;

use App\Models\Tenant\InventoryCategory;
use App\Models\Tenant\Restaurant;
use Illuminate\Database\Seeder;

class InventoryCategorySeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $restaurant = Restaurant::first();

        $categories = [
            [
                'name' => 'Proteins',
                'description' => 'Meat, poultry, seafood, and protein sources',
                'color_code' => '#FF6B6B',
                'icon' => 'drumstick-bite',
                'storage_requirements' => 'refrigerated',
                'shelf_life_days' => 7,
                'sort_order' => 1,
            ],
            [
                'name' => 'Vegetables',
                'description' => 'Fresh vegetables and produce',
                'color_code' => '#4ECDC4',
                'icon' => 'carrot',
                'storage_requirements' => 'refrigerated',
                'shelf_life_days' => 5,
                'sort_order' => 2,
            ],
            [
                'name' => 'Fruits',
                'description' => 'Fresh fruits and berries',
                'color_code' => '#45B7D1',
                'icon' => 'apple-alt',
                'storage_requirements' => 'refrigerated',
                'shelf_life_days' => 7,
                'sort_order' => 3,
            ],
            [
                'name' => 'Dairy Products',
                'description' => 'Milk, cheese, butter, and dairy items',
                'color_code' => '#F7DC6F',
                'icon' => 'cheese',
                'storage_requirements' => 'refrigerated',
                'shelf_life_days' => 14,
                'sort_order' => 4,
            ],
            [
                'name' => 'Grains & Cereals',
                'description' => 'Rice, pasta, flour, and grain products',
                'color_code' => '#BB8FCE',
                'icon' => 'seedling',
                'storage_requirements' => 'dry',
                'shelf_life_days' => 365,
                'sort_order' => 5,
            ],
            [
                'name' => 'Spices & Seasonings',
                'description' => 'Herbs, spices, and seasoning blends',
                'color_code' => '#82E0AA',
                'icon' => 'pepper-hot',
                'storage_requirements' => 'dry',
                'shelf_life_days' => 730,
                'sort_order' => 6,
            ],
            [
                'name' => 'Beverages',
                'description' => 'Soft drinks, juices, and beverage ingredients',
                'color_code' => '#F8C471',
                'icon' => 'wine-bottle',
                'storage_requirements' => 'ambient',
                'shelf_life_days' => 180,
                'sort_order' => 7,
            ],
            [
                'name' => 'Frozen Foods',
                'description' => 'Frozen vegetables, meats, and prepared foods',
                'color_code' => '#85C1E9',
                'icon' => 'snowflake',
                'storage_requirements' => 'frozen',
                'shelf_life_days' => 90,
                'sort_order' => 8,
            ],
            [
                'name' => 'Condiments & Sauces',
                'description' => 'Sauces, dressings, and condiments',
                'color_code' => '#F1948A',
                'icon' => 'bottle-droplet',
                'storage_requirements' => 'ambient',
                'shelf_life_days' => 365,
                'sort_order' => 9,
            ],
            [
                'name' => 'Baking Supplies',
                'description' => 'Flour, sugar, baking powder, and baking ingredients',
                'color_code' => '#D7BDE2',
                'icon' => 'birthday-cake',
                'storage_requirements' => 'dry',
                'shelf_life_days' => 365,
                'sort_order' => 10,
            ],
            [
                'name' => 'Cleaning Supplies',
                'description' => 'Cleaning products and sanitation supplies',
                'color_code' => '#A9DFBF',
                'icon' => 'spray-can',
                'storage_requirements' => 'dry',
                'shelf_life_days' => 730,
                'sort_order' => 11,
            ],
            [
                'name' => 'Disposables',
                'description' => 'Paper products, containers, and disposable items',
                'color_code' => '#F9E79F',
                'icon' => 'box',
                'storage_requirements' => 'dry',
                'shelf_life_days' => 1095,
                'sort_order' => 12,
            ],
        ];

        foreach ($categories as $categoryData) {
            InventoryCategory::create([
                'restaurant_id' => $restaurant->id,
                'name' => $categoryData['name'],
                'description' => $categoryData['description'],
                'color_code' => $categoryData['color_code'],
                'icon' => $categoryData['icon'],
                'storage_requirements' => $categoryData['storage_requirements'],
                'shelf_life_days' => $categoryData['shelf_life_days'],
                'sort_order' => $categoryData['sort_order'],
                'is_active' => true,
            ]);
        }
    }
}
