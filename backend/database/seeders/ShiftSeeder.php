<?php

namespace Database\Seeders;

use App\Models\Tenant\Shift;
use App\Models\Tenant\Employee;
use App\Models\Tenant\Restaurant;
use Illuminate\Database\Seeder;
use Carbon\Carbon;

class ShiftSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $restaurant = Restaurant::first();
        $employees = Employee::all();

        // Create shifts for the next 7 days
        for ($i = 0; $i < 7; $i++) {
            $date = now()->addDays($i);
            $this->createDailyShifts($restaurant->id, $employees, $date);
        }
    }

    private function createDailyShifts($restaurantId, $employees, Carbon $date)
    {
        // Skip Sundays (restaurant closed)
        if ($date->isSunday()) {
            return;
        }

        foreach ($employees as $employee) {
            $this->createEmployeeShift($restaurantId, $employee, $date);
        }
    }

    private function createEmployeeShift($restaurantId, $employee, Carbon $date)
    {
        $shifts = $this->getShiftsByPosition($employee->position);

        if (empty($shifts)) {
            return;
        }

        $shift = $shifts[array_rand($shifts)];

        $startDateTime = $date->copy()->setTimeFromTimeString($shift['start']);
        $endDateTime = $date->copy()->setTimeFromTimeString($shift['end']);

        // Handle overnight shifts
        if ($shift['end'] < $shift['start']) {
            $endDateTime->addDay();
        }

        // Determine status based on date
        $status = 'scheduled';
        if ($date->isPast()) {
            $status = 'completed';
        } elseif ($date->isToday()) {
            $status = 'confirmed';
        }

        Shift::create([
            'restaurant_id' => $restaurantId,
            'employee_id' => $employee->id,
            'department_id' => $employee->department_id,
            'shift_date' => $date,
            'start_time' => $startDateTime,
            'end_time' => $endDateTime,
            'break_duration' => 30,
            'position' => $employee->position,
            'status' => $status,
            'created_by' => 1,
        ]);
    }

    private function getShiftsByPosition($position)
    {
        $shifts = [
            'Head Chef' => [
                ['start' => '10:00', 'end' => '22:00'],
            ],
            'Sous Chef' => [
                ['start' => '11:00', 'end' => '21:00'],
            ],
            'Line Cook' => [
                ['start' => '11:00', 'end' => '19:00'],
                ['start' => '15:00', 'end' => '23:00'],
            ],
            'Prep Cook' => [
                ['start' => '08:00', 'end' => '16:00'],
            ],
            'Floor Manager' => [
                ['start' => '11:00', 'end' => '21:00'],
            ],
            'Server' => [
                ['start' => '11:00', 'end' => '17:00'],
                ['start' => '16:00', 'end' => '22:00'],
            ],
            'Host' => [
                ['start' => '11:00', 'end' => '22:00'],
            ],
            'General Manager' => [
                ['start' => '09:00', 'end' => '18:00'],
            ],
            'Assistant Manager' => [
                ['start' => '10:00', 'end' => '18:00'],
                ['start' => '14:00', 'end' => '22:00'],
            ],
            'Head Bartender' => [
                ['start' => '17:00', 'end' => '01:00'],
            ],
            'Bartender' => [
                ['start' => '18:00', 'end' => '00:00'],
            ],
            'Cleaning Supervisor' => [
                ['start' => '23:00', 'end' => '03:00'],
            ],
            'Delivery Driver' => [
                ['start' => '11:00', 'end' => '15:00'],
                ['start' => '17:00', 'end' => '21:00'],
            ],
        ];

        return $shifts[$position] ?? [];
    }
}
