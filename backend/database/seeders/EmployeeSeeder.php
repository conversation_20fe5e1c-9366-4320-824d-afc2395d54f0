<?php

namespace Database\Seeders;

use App\Models\Tenant\Employee;
use App\Models\Tenant\Department;
use App\Models\Tenant\Restaurant;
use Illuminate\Database\Seeder;

class EmployeeSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $restaurant = Restaurant::first();
        $departments = Department::all();

        foreach ($departments as $department) {
            $this->createEmployees($restaurant->id, $department);
        }
    }

    private function createEmployees($restaurantId, $department)
    {
        $employees = [];

        switch ($department->name) {
            case 'Kitchen':
                $employees = [
                    [
                        'first_name' => '<PERSON>',
                        'last_name' => '<PERSON>',
                        'email' => '<EMAIL>',
                        'phone' => '******-1001',
                        'position' => 'Head Chef',
                        'employment_type' => 'full_time',
                        'salary' => 65000,
                        'hourly_rate' => null,
                        'hire_date' => now()->subYears(3),
                    ],
                    [
                        'first_name' => '<PERSON>',
                        'last_name' => 'Kim',
                        'email' => '<EMAIL>',
                        'phone' => '******-1002',
                        'position' => 'Sous Chef',
                        'employment_type' => 'full_time',
                        'salary' => 45000,
                        'hourly_rate' => null,
                        'hire_date' => now()->subYears(2),
                    ],
                    [
                        'first_name' => 'James',
                        'last_name' => 'Wilson',
                        'email' => '<EMAIL>',
                        'phone' => '******-1003',
                        'position' => 'Line Cook',
                        'employment_type' => 'full_time',
                        'salary' => null,
                        'hourly_rate' => 18.50,
                        'hire_date' => now()->subYear(),
                    ],
                    [
                        'first_name' => 'Maria',
                        'last_name' => 'Gonzalez',
                        'email' => '<EMAIL>',
                        'phone' => '******-1004',
                        'position' => 'Prep Cook',
                        'employment_type' => 'part_time',
                        'salary' => null,
                        'hourly_rate' => 16.00,
                        'hire_date' => now()->subMonths(8),
                    ],
                ];
                break;

            case 'Front of House':
                $employees = [
                    [
                        'first_name' => 'Emily',
                        'last_name' => 'Johnson',
                        'email' => '<EMAIL>',
                        'phone' => '******-2001',
                        'position' => 'Floor Manager',
                        'employment_type' => 'full_time',
                        'salary' => 42000,
                        'hourly_rate' => null,
                        'hire_date' => now()->subYears(2),
                    ],
                    [
                        'first_name' => 'Michael',
                        'last_name' => 'Brown',
                        'email' => '<EMAIL>',
                        'phone' => '******-2002',
                        'position' => 'Server',
                        'employment_type' => 'full_time',
                        'salary' => null,
                        'hourly_rate' => 15.00,
                        'hire_date' => now()->subYear(),
                    ],
                    [
                        'first_name' => 'Jessica',
                        'last_name' => 'Davis',
                        'email' => '<EMAIL>',
                        'phone' => '******-2003',
                        'position' => 'Server',
                        'employment_type' => 'part_time',
                        'salary' => null,
                        'hourly_rate' => 15.00,
                        'hire_date' => now()->subMonths(6),
                    ],
                    [
                        'first_name' => 'David',
                        'last_name' => 'Lee',
                        'email' => '<EMAIL>',
                        'phone' => '******-2004',
                        'position' => 'Host',
                        'employment_type' => 'part_time',
                        'salary' => null,
                        'hourly_rate' => 14.00,
                        'hire_date' => now()->subMonths(4),
                    ],
                ];
                break;

            case 'Management':
                $employees = [
                    [
                        'first_name' => 'Robert',
                        'last_name' => 'Anderson',
                        'email' => '<EMAIL>',
                        'phone' => '******-3001',
                        'position' => 'General Manager',
                        'employment_type' => 'full_time',
                        'salary' => 75000,
                        'hourly_rate' => null,
                        'hire_date' => now()->subYears(4),
                    ],
                    [
                        'first_name' => 'Lisa',
                        'last_name' => 'Thompson',
                        'email' => '<EMAIL>',
                        'phone' => '******-3002',
                        'position' => 'Assistant Manager',
                        'employment_type' => 'full_time',
                        'salary' => 48000,
                        'hourly_rate' => null,
                        'hire_date' => now()->subYears(2),
                    ],
                ];
                break;

            case 'Bar':
                $employees = [
                    [
                        'first_name' => 'Alex',
                        'last_name' => 'Martinez',
                        'email' => '<EMAIL>',
                        'phone' => '******-4001',
                        'position' => 'Head Bartender',
                        'employment_type' => 'full_time',
                        'salary' => null,
                        'hourly_rate' => 20.00,
                        'hire_date' => now()->subYears(2),
                    ],
                    [
                        'first_name' => 'Rachel',
                        'last_name' => 'White',
                        'email' => '<EMAIL>',
                        'phone' => '******-4002',
                        'position' => 'Bartender',
                        'employment_type' => 'part_time',
                        'salary' => null,
                        'hourly_rate' => 17.00,
                        'hire_date' => now()->subYear(),
                    ],
                ];
                break;

            case 'Cleaning':
                $employees = [
                    [
                        'first_name' => 'Carlos',
                        'last_name' => 'Ramirez',
                        'email' => '<EMAIL>',
                        'phone' => '******-5001',
                        'position' => 'Cleaning Supervisor',
                        'employment_type' => 'full_time',
                        'salary' => null,
                        'hourly_rate' => 16.00,
                        'hire_date' => now()->subYears(3),
                    ],
                ];
                break;

            case 'Delivery':
                $employees = [
                    [
                        'first_name' => 'Kevin',
                        'last_name' => 'Taylor',
                        'email' => '<EMAIL>',
                        'phone' => '******-6001',
                        'position' => 'Delivery Driver',
                        'employment_type' => 'part_time',
                        'salary' => null,
                        'hourly_rate' => 15.00,
                        'hire_date' => now()->subMonths(8),
                    ],
                    [
                        'first_name' => 'Amanda',
                        'last_name' => 'Clark',
                        'email' => '<EMAIL>',
                        'phone' => '******-6002',
                        'position' => 'Delivery Driver',
                        'employment_type' => 'part_time',
                        'salary' => null,
                        'hourly_rate' => 15.00,
                        'hire_date' => now()->subMonths(5),
                    ],
                ];
                break;
        }

        foreach ($employees as $employeeData) {
            Employee::create([
                'restaurant_id' => $restaurantId,
                'department_id' => $department->id,
                'first_name' => $employeeData['first_name'],
                'last_name' => $employeeData['last_name'],
                'email' => $employeeData['email'],
                'phone' => $employeeData['phone'],
                'position' => $employeeData['position'],
                'employment_type' => $employeeData['employment_type'],
                'salary' => $employeeData['salary'],
                'hourly_rate' => $employeeData['hourly_rate'],
                'hire_date' => $employeeData['hire_date'],
                'status' => 'active',
                'date_of_birth' => now()->subYears(rand(22, 55)),
                'gender' => ['male', 'female'][rand(0, 1)],
                'address' => fake()->streetAddress(),
                'city' => 'New York',
                'state' => 'NY',
                'country' => 'USA',
                'postal_code' => fake()->postcode(),
                'emergency_contact_name' => fake()->name(),
                'emergency_contact_phone' => fake()->phoneNumber(),
                'emergency_contact_relationship' => ['spouse', 'parent', 'sibling', 'friend'][rand(0, 3)],
            ]);
        }
    }
}
