<?php

namespace Database\Seeders;

use App\Models\User;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Hash;
use Spatie\Permission\Models\Role;
use Spatie\Permission\Models\Permission;

class CentralSystemAdminSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Ensure we're working with the central database
        config(['database.default' => config('tenancy.database.central_connection', 'mysql')]);

        // Reset cached roles and permissions
        app()[\Spatie\Permission\PermissionRegistrar::class]->forgetCachedPermissions();

        // Create central system permissions
        $centralPermissions = [
            // System Administration
            'manage_system',
            'manage_tenants',
            'manage_subscriptions',
            'manage_payments',
            'view_admin_dashboard',
            'manage_system_settings',
            'manage_subscription_plans',

            // CMS Permissions
            'manage_dynamic_pages',
            'create_dynamic_pages',
            'edit_dynamic_pages',
            'delete_dynamic_pages',
            'publish_dynamic_pages',

            'manage_blogs',
            'create_blogs',
            'edit_blogs',
            'delete_blogs',
            'publish_blogs',

            'manage_media',
            'upload_media',
            'delete_media',

            // User Management
            'manage_users',
            'create_users',
            'edit_users',
            'delete_users',
            'assign_roles',

            // System Monitoring
            'view_system_logs',
            'view_system_analytics',
            'manage_system_backups',
        ];

        foreach ($centralPermissions as $permission) {
            Permission::firstOrCreate(['name' => $permission]);
        }

        // Create system superadmin role
        $superAdminRole = Role::firstOrCreate(['name' => 'system_superadmin']);
        $superAdminRole->syncPermissions($centralPermissions);

        // Create system admin role (limited permissions)
        $systemAdminRole = Role::firstOrCreate(['name' => 'system_admin']);
        $systemAdminRole->syncPermissions([
            'view_admin_dashboard',
            'manage_tenants',
            'manage_subscriptions',
            'manage_payments',
            'manage_dynamic_pages',
            'create_dynamic_pages',
            'edit_dynamic_pages',
            'publish_dynamic_pages',
            'manage_blogs',
            'create_blogs',
            'edit_blogs',
            'publish_blogs',
            'manage_media',
            'upload_media',
        ]);

        // Create content manager role (CMS only)
        $contentManagerRole = Role::firstOrCreate(['name' => 'content_manager']);
        $contentManagerRole->syncPermissions([
            'manage_dynamic_pages',
            'create_dynamic_pages',
            'edit_dynamic_pages',
            'publish_dynamic_pages',
            'manage_blogs',
            'create_blogs',
            'edit_blogs',
            'publish_blogs',
            'manage_media',
            'upload_media',
        ]);

        // Create the central system superadmin user
        $superAdmin = User::firstOrCreate(
            ['email' => '<EMAIL>'],
            [
                'name' => 'System Super Administrator',
                'password' => Hash::make('Restaurant@2024'),
                'email_verified_at' => now(),
                'preferred_language' => 'en',
                'theme_preference' => 'light',
                'is_active' => true,
                'is_system_admin' => true, // Flag to identify system admins
            ]
        );

        // Assign superadmin role
        if (!$superAdmin->hasRole('system_superadmin')) {
            $superAdmin->assignRole('system_superadmin');
        }

        // Create a secondary system admin
        $systemAdmin = User::firstOrCreate(
            ['email' => '<EMAIL>'],
            [
                'name' => 'System Administrator',
                'password' => Hash::make('Restaurant@2024'),
                'email_verified_at' => now(),
                'preferred_language' => 'en',
                'theme_preference' => 'light',
                'is_active' => true,
                'is_system_admin' => true,
            ]
        );

        if (!$systemAdmin->hasRole('system_admin')) {
            $systemAdmin->assignRole('system_admin');
        }

        // Create a content manager
        $contentManager = User::firstOrCreate(
            ['email' => '<EMAIL>'],
            [
                'name' => 'Content Manager',
                'password' => Hash::make('Restaurant@2024'),
                'email_verified_at' => now(),
                'preferred_language' => 'en',
                'theme_preference' => 'light',
                'is_active' => true,
                'is_system_admin' => false,
            ]
        );

        if (!$contentManager->hasRole('content_manager')) {
            $contentManager->assignRole('content_manager');
        }

        $this->command->info('Central system administrators created successfully!');
        $this->command->info('');
        $this->command->info('=== CENTRAL SYSTEM LOGIN CREDENTIALS ===');
        $this->command->info('Super Admin: <EMAIL> / Restaurant@2024');
        $this->command->info('System Admin: <EMAIL> / Restaurant@2024');
        $this->command->info('Content Manager: <EMAIL> / Restaurant@2024');
        $this->command->info('');
        $this->command->info('Access URL: http://localhost:8000/login');
        $this->command->info('Admin Dashboard: http://localhost:8000/admin/dashboard');
    }
}
