<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\User;
use App\Models\Role;
use App\Models\Tenant;
use Illuminate\Support\Facades\Hash;

class TenantManagerSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $this->command->info('🚀 Setting up Tenant Manager User...');

        // Create or find the restaurant_manager role
        $managerRole = Role::firstOrCreate(
            ['name' => 'restaurant_manager'],
            [
                'guard_name' => 'web',
                'created_at' => now(),
                'updated_at' => now(),
            ]
        );

        $this->command->info("✅ Role '{$managerRole->name}' ensured in central database");

        // Create additional roles if they don't exist
        $roles = [
            'admin' => 'System Administrator',
            'waiter' => 'Restaurant Waiter',
            'chef' => 'Kitchen Chef',
            'kitchen' => 'Kitchen Staff',
            'delivery' => 'Delivery Rider',
        ];

        foreach ($roles as $roleName => $description) {
            Role::firstOrCreate(
                ['name' => $roleName],
                [
                    'guard_name' => 'web',
                    'created_at' => now(),
                    'updated_at' => now(),
                ]
            );
        }

        $this->command->info('✅ All roles ensured in central database');

        // Find the demo tenant
        $tenant = Tenant::where('id', 'demo-restaurant')->first();

        if (!$tenant) {
            $this->command->error('❌ Demo tenant not found!');
            return;
        }

        $this->command->info("✅ Found tenant: {$tenant->id}");

        // Create or update the manager user
        $user = User::updateOrCreate(
            ['email' => '<EMAIL>'],
            [
                'name' => 'Restaurant Manager',
                'password' => Hash::make('password123'),
                'email_verified_at' => now(),
                'is_active' => true,
                'created_at' => now(),
                'updated_at' => now(),
            ]
        );

        $this->command->info("✅ User: {$user->name} ({$user->email}) - ID: {$user->id}");

        // Assign the restaurant_manager role
        if (!$user->hasRole('restaurant_manager')) {
            $user->assignRole('restaurant_manager');
            $this->command->info('✅ Assigned restaurant_manager role');
        } else {
            $this->command->info('ℹ️  User already has restaurant_manager role');
        }

        // Verify role assignment
        $userRoles = $user->roles->pluck('name')->toArray();
        $this->command->info('✅ User roles: ' . implode(', ', $userRoles));

        // Test role checking methods
        $this->command->info("\n=== 🔍 Role Checking Verification ===");
        $this->command->info("hasRole('restaurant_manager'): " . ($user->hasRole('restaurant_manager') ? '✅ true' : '❌ false'));
        $this->command->info("isRestaurantManager(): " . ($user->isRestaurantManager() ? '✅ true' : '❌ false'));
        $this->command->info("hasAnyRole(['restaurant_manager', 'admin']): " . ($user->hasAnyRole(['restaurant_manager', 'admin']) ? '✅ true' : '❌ false'));

        // Check permissions if any exist
        $permissions = $user->getAllPermissions();
        $this->command->info("User permissions: " . ($permissions->count() > 0 ? $permissions->pluck('name')->join(', ') : 'None'));

        $this->command->info("\n🎉 Tenant Manager setup completed successfully!");
        $this->command->info("\n📋 Login Credentials:");
        $this->command->info("   Email: <EMAIL>");
        $this->command->info("   Password: password123");
        $this->command->info("   URL: http://demo-restaurant.localhost:8000/login");
        $this->command->info("\n🔗 Test URLs:");
        $this->command->info("   Dashboard: http://demo-restaurant.localhost:8000/dashboard");
        $this->command->info("   Subscription: http://demo-restaurant.localhost:8000/subscription");
    }
}
