<?php

namespace Database\Seeders;

use App\Models\Tenant\Category;
use App\Models\Tenant\Restaurant;
use Illuminate\Database\Seeder;

class MenuCategorySeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $restaurant = Restaurant::first();

        $categories = [
            [
                'name' => 'Appetizers',
                'slug' => 'appetizers',
                'description' => 'Start your meal with our delicious appetizers',
                'sort_order' => 1,
                'color_code' => '#FF6B6B',
                'icon' => 'utensils',
            ],
            [
                'name' => 'Main Courses',
                'slug' => 'main-courses',
                'description' => 'Our signature main dishes',
                'sort_order' => 2,
                'color_code' => '#4ECDC4',
                'icon' => 'drumstick-bite',
            ],
            [
                'name' => 'Desserts',
                'slug' => 'desserts',
                'description' => 'Sweet endings to your perfect meal',
                'sort_order' => 3,
                'color_code' => '#45B7D1',
                'icon' => 'ice-cream',
            ],
            [
                'name' => 'Beverages',
                'slug' => 'beverages',
                'description' => 'Refreshing drinks and beverages',
                'sort_order' => 4,
                'color_code' => '#F7DC6F',
                'icon' => 'coffee',
            ],
            [
                'name' => 'Salads',
                'slug' => 'salads',
                'description' => 'Fresh and healthy salad options',
                'sort_order' => 5,
                'color_code' => '#82E0AA',
                'icon' => 'leaf',
            ],
            [
                'name' => 'Soups',
                'slug' => 'soups',
                'description' => 'Warm and comforting soups',
                'sort_order' => 6,
                'color_code' => '#F1948A',
                'icon' => 'bowl-hot',
            ],
        ];

        foreach ($categories as $categoryData) {
            Category::create([
                'restaurant_id' => $restaurant->id,
                'name' => $categoryData['name'],
                'slug' => $categoryData['slug'],
                'description' => $categoryData['description'],
                'sort_order' => $categoryData['sort_order'],
                'color_code' => $categoryData['color_code'],
                'icon' => $categoryData['icon'],
                'is_active' => true,
            ]);
        }
    }
}
