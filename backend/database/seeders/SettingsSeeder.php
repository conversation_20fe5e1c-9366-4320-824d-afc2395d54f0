<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\Tenant\Setting;
use App\Models\Tenant\Page;

class SettingsSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Default General Settings
        Setting::set('site_name', 'Demo Restaurant', 'string', 'general');
        Setting::set('site_description', 'Delicious food, great service, memorable experiences.', 'string', 'general');

        // Default Contact Settings
        Setting::set('phone', '+****************', 'string', 'contact');
        Setting::set('email', '<EMAIL>', 'string', 'contact');
        Setting::set('address', '123 Main Street', 'string', 'contact');
        Setting::set('city', 'New York', 'string', 'contact');
        Setting::set('state', 'NY', 'string', 'contact');
        Setting::set('postal_code', '10001', 'string', 'contact');
        Setting::set('country', 'United States', 'string', 'contact');

        // Default Appearance Settings
        Setting::set('primary_color', '#FF6B35', 'string', 'appearance');
        Setting::set('secondary_color', '#2D3748', 'string', 'appearance');
        Setting::set('theme_mode', 'light', 'string', 'appearance');

        // Default Social Media Settings
        Setting::set('facebook_url', '', 'string', 'social');
        Setting::set('instagram_url', '', 'string', 'social');
        Setting::set('twitter_url', '', 'string', 'social');
        Setting::set('youtube_url', '', 'string', 'social');
        Setting::set('linkedin_url', '', 'string', 'social');

        // Create default pages
        $this->createDefaultPages();
    }

    /**
     * Create default pages
     */
    private function createDefaultPages(): void
    {
        $pages = [
            [
                'title' => 'About Us',
                'slug' => 'about-us',
                'content' => '<h1>About Our Restaurant</h1><p>Welcome to Demo Restaurant, where culinary excellence meets exceptional service. Our passion for food drives us to create memorable dining experiences for every guest.</p><h2>Our Story</h2><p>Founded with a vision to bring authentic flavors and innovative cuisine to our community, we have been serving delicious meals made from the finest ingredients.</p><h2>Our Mission</h2><p>To provide exceptional dining experiences through quality food, outstanding service, and a welcoming atmosphere that brings people together.</p>',
                'excerpt' => 'Learn about our restaurant\'s story, mission, and commitment to culinary excellence.',
                'meta_title' => 'About Us - Demo Restaurant',
                'meta_description' => 'Discover the story behind Demo Restaurant, our mission, and our commitment to providing exceptional dining experiences.',
                'is_published' => true,
                'show_in_menu' => true,
                'sort_order' => 1,
            ],
            [
                'title' => 'Contact Us',
                'slug' => 'contact',
                'content' => '<h1>Get in Touch</h1><p>We\'d love to hear from you! Whether you have questions about our menu, want to make a reservation, or need information about catering services, we\'re here to help.</p><h2>Contact Information</h2><p><strong>Phone:</strong> +****************<br><strong>Email:</strong> <EMAIL><br><strong>Address:</strong> 123 Main Street, New York, NY 10001</p><h2>Hours of Operation</h2><p><strong>Monday - Thursday:</strong> 11:00 AM - 10:00 PM<br><strong>Friday - Saturday:</strong> 11:00 AM - 11:00 PM<br><strong>Sunday:</strong> 12:00 PM - 9:00 PM</p>',
                'excerpt' => 'Find our contact information, location, and hours of operation.',
                'meta_title' => 'Contact Us - Demo Restaurant',
                'meta_description' => 'Contact Demo Restaurant for reservations, inquiries, or catering services. Find our location and hours of operation.',
                'is_published' => true,
                'show_in_menu' => true,
                'sort_order' => 2,
            ],
            [
                'title' => 'Privacy Policy',
                'slug' => 'privacy-policy',
                'content' => '<h1>Privacy Policy</h1><p>This Privacy Policy describes how Demo Restaurant collects, uses, and protects your personal information when you visit our website or dine at our restaurant.</p><h2>Information We Collect</h2><p>We may collect personal information such as your name, email address, phone number, and dining preferences when you make reservations or sign up for our newsletter.</p><h2>How We Use Your Information</h2><p>We use your information to process reservations, improve our services, and communicate with you about special offers and events.</p><h2>Data Protection</h2><p>We implement appropriate security measures to protect your personal information against unauthorized access, alteration, disclosure, or destruction.</p>',
                'excerpt' => 'Our privacy policy explains how we collect, use, and protect your personal information.',
                'meta_title' => 'Privacy Policy - Demo Restaurant',
                'meta_description' => 'Read our privacy policy to understand how Demo Restaurant collects, uses, and protects your personal information.',
                'is_published' => true,
                'show_in_menu' => false,
                'sort_order' => 3,
            ],
            [
                'title' => 'Terms & Conditions',
                'slug' => 'terms-conditions',
                'content' => '<h1>Terms & Conditions</h1><p>By using our website and services, you agree to comply with and be bound by the following terms and conditions.</p><h2>Reservations</h2><p>Reservations are subject to availability and may be cancelled or modified by the restaurant if necessary. We recommend making reservations in advance, especially during peak hours.</p><h2>Cancellation Policy</h2><p>Please provide at least 2 hours notice for reservation cancellations. Late cancellations or no-shows may be subject to a cancellation fee.</p><h2>Payment</h2><p>We accept cash and major credit cards. Gratuity is not included in menu prices and is at your discretion.</p>',
                'excerpt' => 'Terms and conditions for using our services and dining at our restaurant.',
                'meta_title' => 'Terms & Conditions - Demo Restaurant',
                'meta_description' => 'Read our terms and conditions for reservations, payments, and dining policies at Demo Restaurant.',
                'is_published' => true,
                'show_in_menu' => false,
                'sort_order' => 4,
            ],
        ];

        foreach ($pages as $pageData) {
            Page::create($pageData);
        }
    }
}
