<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('tenants', function (Blueprint $table) {
            $table->string('setup_status')->default('pending')->after('subscription_ends_at');
            $table->timestamp('setup_started_at')->nullable()->after('setup_status');
            $table->timestamp('setup_completed_at')->nullable()->after('setup_started_at');
            $table->timestamp('setup_failed_at')->nullable()->after('setup_completed_at');
            $table->text('setup_error')->nullable()->after('setup_failed_at');
            $table->integer('setup_duration_minutes')->nullable()->after('setup_error');
            $table->timestamp('last_activity_at')->nullable()->after('setup_duration_minutes');
            $table->boolean('is_active')->default(false)->after('last_activity_at');
            
            // Add index for setup status queries
            $table->index('setup_status');
            $table->index('is_active');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('tenants', function (Blueprint $table) {
            $table->dropIndex(['setup_status']);
            $table->dropIndex(['is_active']);
            
            $table->dropColumn([
                'setup_status',
                'setup_started_at',
                'setup_completed_at',
                'setup_failed_at',
                'setup_error',
                'setup_duration_minutes',
                'last_activity_at',
                'is_active',
            ]);
        });
    }
};
