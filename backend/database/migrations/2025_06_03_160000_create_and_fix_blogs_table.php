<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Create blogs table if it doesn't exist
        if (!Schema::hasTable('blogs')) {
            Schema::create('blogs', function (Blueprint $table) {
                $table->id();
                $table->json('title'); // Multi-language title
                $table->string('slug')->unique();
                $table->longText('content');
                $table->text('excerpt')->nullable();
                $table->foreignId('featured_image_id')->nullable()->constrained('media')->onDelete('set null');
                $table->text('meta_description')->nullable();
                $table->enum('status', ['draft', 'published'])->default('draft');
                $table->timestamp('published_at')->nullable();
                $table->foreignId('author_id')->nullable()->constrained('users')->onDelete('set null');
                $table->json('tags')->nullable(); // JSON array of tags
                $table->json('categories')->nullable(); // JSON array of categories
                $table->integer('sort_order')->default(0);
                $table->foreignId('created_by')->nullable()->constrained('users')->onDelete('set null');
                $table->foreignId('updated_by')->nullable()->constrained('users')->onDelete('set null');
                $table->timestamps();

                // Indexes for better performance
                $table->index(['status', 'published_at']);
                $table->index(['author_id', 'status']);
                $table->index('sort_order');
            });
        } else {
            // Table exists, update structure to match our requirements
            Schema::table('blogs', function (Blueprint $table) {
                // Fix status column - change from int to enum
                if (Schema::hasColumn('blogs', 'status')) {
                    // First, update existing data to use string values
                    DB::statement("UPDATE blogs SET status = CASE 
                        WHEN status = 1 THEN 'published' 
                        WHEN status = 0 THEN 'draft' 
                        ELSE 'draft' 
                    END");
                    
                    // Drop and recreate as enum
                    $table->dropColumn('status');
                }
            });

            // Add the enum status column
            Schema::table('blogs', function (Blueprint $table) {
                $table->enum('status', ['draft', 'published'])->default('draft');
            });

            // Add missing columns if they don't exist
            Schema::table('blogs', function (Blueprint $table) {
                if (!Schema::hasColumn('blogs', 'content')) {
                    $table->longText('content');
                }

                if (!Schema::hasColumn('blogs', 'excerpt')) {
                    $table->text('excerpt')->nullable();
                }

                if (!Schema::hasColumn('blogs', 'featured_image_id')) {
                    $table->foreignId('featured_image_id')->nullable()->constrained('media')->onDelete('set null');
                }

                if (!Schema::hasColumn('blogs', 'meta_description')) {
                    $table->text('meta_description')->nullable();
                }

                if (!Schema::hasColumn('blogs', 'published_at')) {
                    $table->timestamp('published_at')->nullable();
                }

                if (!Schema::hasColumn('blogs', 'author_id')) {
                    $table->foreignId('author_id')->nullable()->constrained('users')->onDelete('set null');
                }

                if (!Schema::hasColumn('blogs', 'tags')) {
                    $table->json('tags')->nullable();
                }

                if (!Schema::hasColumn('blogs', 'categories')) {
                    $table->json('categories')->nullable();
                }

                if (!Schema::hasColumn('blogs', 'sort_order')) {
                    $table->integer('sort_order')->default(0);
                }

                if (!Schema::hasColumn('blogs', 'created_by')) {
                    $table->foreignId('created_by')->nullable()->constrained('users')->onDelete('set null');
                }

                if (!Schema::hasColumn('blogs', 'updated_by')) {
                    $table->foreignId('updated_by')->nullable()->constrained('users')->onDelete('set null');
                }
            });

            // Convert title column to JSON if it exists as varchar
            Schema::table('blogs', function (Blueprint $table) {
                if (Schema::hasColumn('blogs', 'title')) {
                    // First, backup existing titles and convert to JSON format
                    $blogs = DB::table('blogs')->get();
                    foreach ($blogs as $blog) {
                        if (!is_null($blog->title) && !json_decode($blog->title)) {
                            // Convert existing string title to JSON format
                            $titleJson = json_encode([
                                'en' => $blog->title,
                                'bn' => $blog->title
                            ]);
                            DB::table('blogs')->where('id', $blog->id)->update(['title' => $titleJson]);
                        }
                    }

                    // Change column type to JSON
                    $table->json('title')->change();
                }
            });

            // Add indexes for better performance
            Schema::table('blogs', function (Blueprint $table) {
                try {
                    $table->index(['status', 'published_at']);
                } catch (Exception $e) {
                    // Index might already exist, ignore
                }

                try {
                    $table->index(['author_id', 'status']);
                } catch (Exception $e) {
                    // Index might already exist, ignore
                }

                try {
                    $table->index('sort_order');
                } catch (Exception $e) {
                    // Index might already exist, ignore
                }
            });

            // Remove soft deletes column if it exists (since we removed SoftDeletes from model)
            Schema::table('blogs', function (Blueprint $table) {
                if (Schema::hasColumn('blogs', 'deleted_at')) {
                    $table->dropColumn('deleted_at');
                }
            });

            // Clean up old columns that don't match our new structure
            Schema::table('blogs', function (Blueprint $table) {
                // Remove old columns that don't match our new structure
                $columnsToRemove = ['category_id', 'short_description', 'description', 'banner', 'meta_title', 'meta_img', 'meta_keywords'];
                
                foreach ($columnsToRemove as $column) {
                    if (Schema::hasColumn('blogs', $column)) {
                        $table->dropColumn($column);
                    }
                }
            });
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('blogs');
    }
};
