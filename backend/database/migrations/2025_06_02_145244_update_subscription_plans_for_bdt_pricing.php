<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('subscription_plans', function (Blueprint $table) {
            // Add new fields for enhanced subscription management
            if (!Schema::hasColumn('subscription_plans', 'max_pages')) {
                $table->integer('max_pages')->nullable()->after('max_staff');
            }

            if (!Schema::hasColumn('subscription_plans', 'max_branches')) {
                $table->integer('max_branches')->nullable()->after('max_pages');
            }

            if (!Schema::hasColumn('subscription_plans', 'currency')) {
                $table->string('currency', 3)->default('BDT')->after('price');
            }

            if (!Schema::hasColumn('subscription_plans', 'has_home_delivery')) {
                $table->boolean('has_home_delivery')->default(false)->after('has_delivery');
            }

            if (!Schema::hasColumn('subscription_plans', 'has_email_marketing')) {
                $table->boolean('has_email_marketing')->default(false)->after('has_home_delivery');
            }

            if (!Schema::hasColumn('subscription_plans', 'has_loyalty_program')) {
                $table->boolean('has_loyalty_program')->default(false)->after('has_email_marketing');
            }

            if (!Schema::hasColumn('subscription_plans', 'has_advanced_reporting')) {
                $table->boolean('has_advanced_reporting')->default(false)->after('has_loyalty_program');
            }
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('subscription_plans', function (Blueprint $table) {
            $table->dropColumn([
                'max_pages',
                'max_branches',
                'currency',
                'has_home_delivery',
                'has_email_marketing',
                'has_loyalty_program',
                'has_advanced_reporting'
            ]);
        });
    }
};
