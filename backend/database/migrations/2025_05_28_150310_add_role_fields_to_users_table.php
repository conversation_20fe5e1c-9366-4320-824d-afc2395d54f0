<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('users', function (Blueprint $table) {
            $table->string('role')->nullable()->after('email');
            $table->string('preferred_language', 10)->default('en')->after('role');
            $table->string('theme_preference', 20)->default('light')->after('preferred_language');
            $table->boolean('is_active')->default(true)->after('theme_preference');
            $table->timestamp('last_login_at')->nullable()->after('is_active');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('users', function (Blueprint $table) {
            $table->dropColumn([
                'role',
                'preferred_language',
                'theme_preference',
                'is_active',
                'last_login_at'
            ]);
        });
    }
};
