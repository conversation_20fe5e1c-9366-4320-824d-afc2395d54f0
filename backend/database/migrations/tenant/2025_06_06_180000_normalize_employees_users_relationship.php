<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use App\Models\Tenant\Employee;
use App\Models\Tenant\User;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Step 1: Migrate existing employee data to users table
        $this->migrateEmployeesToUsers();
        
        // Step 2: Remove duplicate columns from employees table
        Schema::table('employees', function (Blueprint $table) {
            // Drop columns that should only exist in users table
            $columnsToRemove = [
                'email', 'password', 'username', 'name', 'phone', 'role',
                'address', 'emergency_contact_name', 'emergency_contact_phone'
            ];
            
            foreach ($columnsToRemove as $column) {
                if (Schema::hasColumn('employees', $column)) {
                    // Drop unique constraints first
                    try {
                        if ($column === 'email') {
                            $table->dropUnique(['email']);
                        }
                        if ($column === 'username') {
                            $table->dropUnique(['username']);
                        }
                    } catch (Exception $e) {
                        // Continue if constraint doesn't exist
                    }
                    
                    $table->dropColumn($column);
                }
            }
        });
        
        // Step 3: Ensure user_id foreign key exists and is properly constrained
        Schema::table('employees', function (Blueprint $table) {
            if (!Schema::hasColumn('employees', 'user_id')) {
                $table->foreignId('user_id')->nullable()->constrained('users')->onDelete('cascade')->after('id');
            } else {
                // Make sure it's properly constrained
                try {
                    $table->foreign('user_id')->references('id')->on('users')->onDelete('cascade');
                } catch (Exception $e) {
                    // Constraint might already exist
                }
            }
        });
        
        // Step 4: Update existing employees to link to their user records
        $this->linkEmployeesToUsers();
    }

    /**
     * Migrate existing employee authentication data to users table
     */
    private function migrateEmployeesToUsers(): void
    {
        $employees = Employee::all();
        
        foreach ($employees as $employee) {
            // Check if user already exists with this email
            $existingUser = User::where('email', $employee->email)->first();
            
            if (!$existingUser && $employee->email) {
                // Create user record from employee data
                $userData = [
                    'name' => $employee->name ?: trim($employee->first_name . ' ' . $employee->last_name),
                    'email' => $employee->email,
                    'password' => $employee->password ?: \Hash::make('password'), // Default password if none
                    'phone' => $employee->phone,
                    'role' => $employee->role ?: 'waiter',
                    'department' => $employee->department?->name,
                    'position' => $employee->position,
                    'hire_date' => $employee->hire_date,
                    'salary' => $employee->salary,
                    'hourly_rate' => $employee->hourly_rate,
                    'address' => $employee->address,
                    'emergency_contact_name' => $employee->emergency_contact_name,
                    'emergency_contact_phone' => $employee->emergency_contact_phone,
                    'notes' => $employee->notes,
                    'is_active' => $employee->is_active ?? true,
                    'email_verified_at' => now(),
                ];
                
                try {
                    $user = User::create($userData);
                    
                    // Update employee to reference the user
                    $employee->update(['user_id' => $user->id]);
                    
                } catch (Exception $e) {
                    \Log::error("Failed to migrate employee {$employee->id} to user: " . $e->getMessage());
                }
            }
        }
    }
    
    /**
     * Link existing employees to their user records
     */
    private function linkEmployeesToUsers(): void
    {
        $employees = Employee::whereNull('user_id')->get();
        
        foreach ($employees as $employee) {
            // Try to find matching user by email or name
            $user = User::where('email', $employee->email)->first();
            
            if (!$user && $employee->name) {
                $user = User::where('name', $employee->name)->first();
            }
            
            if ($user) {
                $employee->update(['user_id' => $user->id]);
            }
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Add back the columns to employees table
        Schema::table('employees', function (Blueprint $table) {
            $table->string('email')->nullable()->after('last_name');
            $table->string('password')->nullable()->after('email');
            $table->string('username')->nullable()->after('password');
            $table->string('name')->nullable()->after('username');
            $table->string('phone')->nullable()->after('name');
            $table->enum('role', ['waiter', 'chef', 'rider', 'manager'])->default('waiter')->after('position');
            $table->text('address')->nullable()->after('phone');
            $table->string('emergency_contact_name')->nullable()->after('address');
            $table->string('emergency_contact_phone')->nullable()->after('emergency_contact_name');
        });
        
        // Migrate data back from users to employees
        $employees = Employee::with('user')->get();
        
        foreach ($employees as $employee) {
            if ($employee->user) {
                $employee->update([
                    'email' => $employee->user->email,
                    'password' => $employee->user->password,
                    'name' => $employee->user->name,
                    'phone' => $employee->user->phone,
                    'role' => $employee->user->role,
                    'address' => $employee->user->address,
                    'emergency_contact_name' => $employee->user->emergency_contact_name,
                    'emergency_contact_phone' => $employee->user->emergency_contact_phone,
                ]);
            }
        }
    }
};
