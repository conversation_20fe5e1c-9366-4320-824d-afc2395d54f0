<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\Hash;
use App\Models\Tenant\User;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Create sample users for POS system if they don't exist
        $sampleUsers = [
            [
                'name' => '<PERSON> Johnson',
                'email' => '<EMAIL>',
                'password' => Hash::make('password'),
                'role' => 'waiter',
                'department' => 'Service',
                'position' => 'Senior Waiter',
                'is_active' => true,
                'email_verified_at' => now(),
            ],
            [
                'name' => '<PERSON>',
                'email' => '<EMAIL>',
                'password' => Hash::make('password'),
                'role' => 'waiter',
                'department' => 'Service',
                'position' => 'Waiter',
                'is_active' => true,
                'email_verified_at' => now(),
            ],
            [
                'name' => '<PERSON>',
                'email' => '<EMAIL>',
                'password' => Hash::make('password'),
                'role' => 'manager',
                'department' => 'Management',
                'position' => 'Floor Manager',
                'is_active' => true,
                'email_verified_at' => now(),
            ],
            [
                'name' => 'David Miller',
                'email' => '<EMAIL>',
                'password' => Hash::make('password'),
                'role' => 'chef',
                'department' => 'Kitchen',
                'position' => 'Head Chef',
                'is_active' => true,
                'email_verified_at' => now(),
            ],
            [
                'name' => 'Emma Brown',
                'email' => '<EMAIL>',
                'password' => Hash::make('password'),
                'role' => 'waiter',
                'department' => 'Service',
                'position' => 'Waiter',
                'is_active' => true,
                'email_verified_at' => now(),
            ],
        ];

        foreach ($sampleUsers as $userData) {
            // Check if user already exists by email
            $existingUser = User::where('email', $userData['email'])->first();

            if (!$existingUser) {
                try {
                    User::create($userData);
                } catch (\Exception $e) {
                    // Log error but continue with other users
                    \Log::error('Failed to create sample user: ' . $e->getMessage());
                }
            }
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Remove sample users
        $emails = [
            '<EMAIL>',
            '<EMAIL>',
            '<EMAIL>',
            '<EMAIL>',
            '<EMAIL>',
        ];

        User::whereIn('email', $emails)->delete();
    }
};
