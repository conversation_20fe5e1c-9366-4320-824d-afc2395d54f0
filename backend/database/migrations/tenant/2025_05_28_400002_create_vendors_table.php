<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('vendors', function (Blueprint $table) {
            $table->id();
            $table->foreignId('restaurant_id')->constrained()->onDelete('cascade');
            $table->string('name');
            $table->string('company_name')->nullable();
            $table->enum('vendor_type', ['supplier', 'service_provider', 'contractor', 'utility', 'other'])->default('supplier');
            $table->string('contact_person')->nullable();
            $table->string('email')->nullable();
            $table->string('phone')->nullable();
            $table->string('mobile')->nullable();
            $table->string('website')->nullable();
            $table->string('tax_id')->nullable();
            $table->string('registration_number')->nullable();
            
            // Address information
            $table->text('address')->nullable();
            $table->string('city')->nullable();
            $table->string('state')->nullable();
            $table->string('country')->nullable();
            $table->string('postal_code')->nullable();
            
            // Business terms
            $table->string('payment_terms')->nullable(); // e.g., "Net 30", "COD"
            $table->decimal('credit_limit', 12, 2)->nullable();
            $table->decimal('discount_percentage', 5, 2)->default(0);
            
            $table->boolean('is_active')->default(true);
            $table->text('notes')->nullable();
            
            // Performance metrics
            $table->decimal('rating', 3, 2)->default(5.00);
            $table->integer('total_orders')->default(0);
            $table->decimal('total_amount_spent', 12, 2)->default(0);
            $table->date('last_order_date')->nullable();
            
            $table->timestamps();

            $table->index(['restaurant_id', 'vendor_type']);
            $table->index(['restaurant_id', 'is_active']);
            $table->index('name');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('vendors');
    }
};
