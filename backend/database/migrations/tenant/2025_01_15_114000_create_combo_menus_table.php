<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('combo_menus', function (Blueprint $table) {
            $table->id();
            $table->string('name');
            $table->string('name_bn')->nullable();
            $table->text('description')->nullable();
            $table->text('description_bn')->nullable();
            $table->decimal('price', 10, 2); // Combo price (usually discounted)
            $table->boolean('is_available')->default(true);
            $table->integer('sort_order')->default(0);
            $table->string('image_url')->nullable();
            $table->json('customization_rules')->nullable(); // JSON rules for combo customization
            $table->timestamps();

            $table->index(['is_available', 'sort_order']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('combo_menus');
    }
};
