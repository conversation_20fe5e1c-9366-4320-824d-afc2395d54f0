<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('employees', function (Blueprint $table) {
            // Check if username column exists and make it nullable with default
            if (Schema::hasColumn('employees', 'username')) {
                // First, update any existing records that have null username
                DB::statement("UPDATE employees SET username = CONCAT('emp_', id) WHERE username IS NULL OR username = ''");
                
                // Make sure the column allows for auto-generation
                $table->string('username')->nullable()->change();
            } else {
                // If username column doesn't exist, add it as nullable
                $table->string('username')->nullable()->unique()->after('password');
            }
        });
        
        // Update any existing employees that don't have usernames
        $employees = \App\Models\Tenant\Employee::whereNull('username')
            ->orWhere('username', '')
            ->get();
            
        foreach ($employees as $employee) {
            $baseUsername = strtolower(str_replace(' ', '.', $employee->first_name . '.' . $employee->last_name));
            $username = $baseUsername;
            $counter = 1;
            
            // Ensure username is unique
            while (\App\Models\Tenant\Employee::where('username', $username)->where('id', '!=', $employee->id)->exists()) {
                $username = $baseUsername . $counter;
                $counter++;
            }
            
            $employee->update(['username' => $username]);
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('employees', function (Blueprint $table) {
            // Don't remove username column as it might be needed
            // Just make it nullable if it wasn't before
            if (Schema::hasColumn('employees', 'username')) {
                $table->string('username')->nullable()->change();
            }
        });
    }
};
