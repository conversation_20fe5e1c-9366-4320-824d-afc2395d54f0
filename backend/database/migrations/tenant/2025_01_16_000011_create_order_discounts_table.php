<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('order_discounts', function (Blueprint $table) {
            $table->id();
            $table->foreignId('order_id')->constrained()->onDelete('cascade');
            // $table->foreignId('order_item_id')->nullable()->constrained()->onDelete('cascade');
            $table->string('discount_type'); // 'percentage', 'fixed_amount', 'buy_one_get_one', etc.
            $table->string('discount_name'); // Human readable name
            $table->decimal('discount_value', 8, 2); // Percentage or fixed amount
            $table->decimal('discount_amount', 10, 2); // Calculated discount amount
            $table->enum('applied_to', ['order', 'item'])->default('order');
            $table->string('coupon_code')->nullable();
            // $table->foreignId('applied_by')->nullable()->constrained('users')->onDelete('set null');
            $table->text('reason')->nullable();
            // $table->json('conditions')->nullable(); // Store discount conditions
            $table->timestamps();

            // Indexes
            $table->index(['order_id', 'applied_to']);
            // $table->index(['order_item_id']);
            $table->index(['discount_type']);
            $table->index(['coupon_code']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('order_discounts');
    }
};
