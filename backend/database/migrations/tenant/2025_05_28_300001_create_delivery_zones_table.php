<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('delivery_zones', function (Blueprint $table) {
            $table->id();
            $table->foreignId('restaurant_id')->constrained()->onDelete('cascade');
            $table->string('name');
            $table->text('description')->nullable();
            $table->json('polygon_coordinates'); // Array of lat/lng points defining the zone
            $table->decimal('delivery_fee', 8, 2)->default(0);
            $table->decimal('minimum_order_amount', 8, 2)->default(0);
            $table->integer('estimated_delivery_time')->default(30); // in minutes
            $table->boolean('is_active')->default(true);
            $table->integer('priority_order')->default(0);
            $table->string('color_code', 7)->nullable(); // Hex color for map visualization
            $table->timestamps();

            $table->index(['restaurant_id', 'is_active']);
            $table->index('priority_order');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('delivery_zones');
    }
};
