<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('loyalty_settings', function (Blueprint $table) {
            $table->id();
            $table->boolean('enabled')->default(false);
            $table->decimal('points_per_dollar', 8, 2)->default(1.00); // Points earned per dollar spent
            $table->integer('points_for_dollar_discount')->default(100); // Points required for $1 discount
            $table->decimal('max_discount_percentage', 5, 2)->default(20.00); // Maximum discount percentage per order
            $table->decimal('minimum_order_amount', 8, 2)->default(0.00); // Minimum order amount to earn points
            $table->integer('points_expiry_days')->nullable(); // Points expiry in days (null = never expire)
            $table->boolean('birthday_bonus_enabled')->default(false);
            $table->integer('birthday_bonus_points')->default(0);
            $table->boolean('referral_program_enabled')->default(false);
            $table->integer('referral_bonus_points')->default(0);
            $table->json('tier_settings')->nullable(); // JSON for tier configurations
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('loyalty_settings');
    }
};
