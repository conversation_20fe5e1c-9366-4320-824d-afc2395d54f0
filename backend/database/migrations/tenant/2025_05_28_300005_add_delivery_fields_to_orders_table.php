<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('orders', function (Blueprint $table) {
            // Only add fields that don't already exist in orders table
            // Note: delivery_person_id, delivery_latitude, delivery_longitude, delivery_address already exist

            // Additional delivery zone and route tracking
            $table->foreignId('delivery_zone_id')->nullable()->constrained()->onDelete('set null');
            // $table->foreignId('delivery_route_id')->nullable()->constrained()->onDelete('set null');

            // Enhanced delivery tracking timestamps
            $table->timestamp('assigned_to_delivery_at')->nullable();
            $table->timestamp('picked_up_at')->nullable();
            $table->timestamp('out_for_delivery_at')->nullable();
            $table->timestamp('delivery_attempted_at')->nullable();
            // $table->text('delivery_notes')->nullable();

            // Delivery rating and feedback
            // $table->decimal('delivery_rating', 3, 2)->nullable();
            // $table->text('delivery_feedback')->nullable();

            // Add indexes for new fields only
            $table->index(['delivery_zone_id', 'status']);
            // $table->index(['delivery_route_id']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('orders', function (Blueprint $table) {
            // Only drop the fields that this migration actually added
            $table->dropForeign(['delivery_zone_id']);
            $table->dropForeign(['delivery_route_id']);

            $table->dropColumn([
                'delivery_zone_id',
                'delivery_route_id',
                'assigned_to_delivery_at',
                'picked_up_at',
                'out_for_delivery_at',
                'delivery_attempted_at',
                'delivery_notes',
                'delivery_rating',
                'delivery_feedback',
            ]);
        });
    }
};
