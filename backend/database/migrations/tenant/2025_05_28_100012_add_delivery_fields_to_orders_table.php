<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('orders', function (Blueprint $table) {
            // Check if columns don't already exist before adding them
            if (!Schema::hasColumn('orders', 'delivery_rider_id')) {
                $table->foreignId('delivery_rider_id')->nullable()->constrained('riders')->onDelete('set null');
            }
            
            if (!Schema::hasColumn('orders', 'pickup_time')) {
                $table->timestamp('pickup_time')->nullable();
            }
            
            if (!Schema::hasColumn('orders', 'delivery_time')) {
                $table->timestamp('delivery_time')->nullable();
            }
            
            if (!Schema::hasColumn('orders', 'delivery_status')) {
                $table->enum('delivery_status', ['pending', 'assigned', 'picked_up', 'en_route', 'delivered', 'failed'])->nullable();
            }
            
            if (!Schema::hasColumn('orders', 'delivery_notes')) {
                $table->text('delivery_notes')->nullable();
            }
            
            if (!Schema::hasColumn('orders', 'delivery_fee')) {
                $table->decimal('delivery_fee', 8, 2)->default(0);
            }
            
            if (!Schema::hasColumn('orders', 'rider_commission')) {
                $table->decimal('rider_commission', 8, 2)->default(0);
            }
            
            if (!Schema::hasColumn('orders', 'estimated_delivery_time')) {
                $table->integer('estimated_delivery_time')->nullable(); // minutes
            }
            
            if (!Schema::hasColumn('orders', 'actual_delivery_time')) {
                $table->integer('actual_delivery_time')->nullable(); // minutes
            }
            
            if (!Schema::hasColumn('orders', 'delivery_rating')) {
                $table->integer('delivery_rating')->nullable(); // 1-5 stars
            }
            
            if (!Schema::hasColumn('orders', 'delivery_feedback')) {
                $table->text('delivery_feedback')->nullable();
            }
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('orders', function (Blueprint $table) {
            $table->dropForeign(['delivery_rider_id']);
            $table->dropColumn([
                'delivery_rider_id',
                'pickup_time',
                'delivery_time',
                'delivery_status',
                'delivery_notes',
                'delivery_fee',
                'rider_commission',
                'estimated_delivery_time',
                'actual_delivery_time',
                'delivery_rating',
                'delivery_feedback'
            ]);
        });
    }
};
