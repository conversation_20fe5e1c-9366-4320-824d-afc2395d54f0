<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('inventory_items', function (Blueprint $table) {
            $table->id();
            $table->foreignId('restaurant_id')->constrained()->onDelete('cascade');
            $table->foreignId('inventory_category_id')->constrained()->onDelete('restrict');
            $table->foreignId('vendor_id')->nullable()->constrained()->onDelete('set null');
            
            $table->string('name');
            $table->text('description')->nullable();
            $table->string('sku')->unique();
            $table->string('barcode')->nullable();
            
            // Measurement and pricing
            $table->string('unit_of_measurement'); // kg, lbs, pieces, liters, etc.
            $table->decimal('unit_cost', 10, 4); // Cost per unit
            $table->decimal('selling_price', 10, 4)->nullable(); // Selling price per unit
            
            // Stock levels
            $table->decimal('current_stock', 10, 2)->default(0);
            $table->decimal('minimum_stock', 10, 2)->default(0);
            $table->decimal('maximum_stock', 10, 2)->nullable();
            $table->decimal('reorder_point', 10, 2)->default(0);
            $table->decimal('reorder_quantity', 10, 2)->default(0);
            
            // Product characteristics
            $table->integer('shelf_life_days')->nullable();
            $table->enum('storage_requirements', ['ambient', 'refrigerated', 'frozen', 'dry', 'controlled'])->nullable();
            $table->boolean('is_perishable')->default(false);
            $table->boolean('is_active')->default(true);
            
            // Tracking options
            $table->boolean('track_expiry')->default(false);
            $table->boolean('track_batches')->default(false);
            
            // Timestamps
            $table->timestamp('last_restocked_at')->nullable();
            $table->timestamp('last_counted_at')->nullable();
            
            $table->text('notes')->nullable();
            $table->timestamps();

            $table->index(['restaurant_id', 'inventory_category_id']);
            $table->index(['restaurant_id', 'vendor_id']);
            $table->index(['restaurant_id', 'is_active']);
            $table->index('sku');
            $table->index('barcode');
            $table->index(['current_stock', 'minimum_stock']); // For low stock queries
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('inventory_items');
    }
};
