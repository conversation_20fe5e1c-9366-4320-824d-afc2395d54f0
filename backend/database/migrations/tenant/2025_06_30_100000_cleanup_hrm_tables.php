<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Clean up any existing HRM tables that might have been partially created
        DB::statement('SET FOREIGN_KEY_CHECKS=0');

        $tables = [
            'loan_payments',
            'payroll_records',
            'employee_loans',
            'attendance_records',
            'leave_requests',
            'employee_leave_entitlements',
            'leave_types'
        ];

        foreach ($tables as $table) {
            DB::statement("DROP TABLE IF EXISTS {$table}");
        }

        // Also clean up any migration records for these tables
        DB::table('migrations')->where('migration', 'like', '2025_06_30_1000%')->delete();

        DB::statement('SET FOREIGN_KEY_CHECKS=1');
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Nothing to do here
    }
};
