<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('loan_payments', function (Blueprint $table) {
            $table->id();
            $table->foreignId('employee_loan_id')->constrained()->onDelete('cascade');
            $table->unsignedBigInteger('payroll_record_id')->nullable(); // Will add foreign key later
            $table->decimal('payment_amount', 10, 2);
            $table->decimal('principal_amount', 10, 2); // Principal portion
            $table->decimal('interest_amount', 10, 2)->default(0); // Interest portion
            $table->date('payment_date');
            $table->date('due_date'); // Original due date for this payment
            $table->enum('payment_method', ['salary_deduction', 'cash', 'bank_transfer', 'other'])->default('salary_deduction');
            $table->enum('status', ['scheduled', 'paid', 'overdue', 'partial'])->default('scheduled');
            $table->text('notes')->nullable();
            $table->boolean('is_early_payment')->default(false);
            $table->boolean('is_late_payment')->default(false);
            $table->integer('days_late')->default(0);
            $table->decimal('late_fee', 8, 2)->default(0);
            $table->string('reference_number')->nullable(); // Transaction reference
            $table->timestamps();

            $table->index(['employee_loan_id', 'payment_date']);
            $table->index(['payment_date', 'status']);
            $table->index(['due_date', 'status']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('loan_payments');
    }
};
