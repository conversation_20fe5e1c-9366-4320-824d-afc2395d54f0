<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('delivery_orders', function (Blueprint $table) {
            $table->id();
            $table->foreignId('restaurant_id')->constrained()->onDelete('cascade');
            $table->foreignId('order_id')->constrained()->onDelete('cascade');
            $table->foreignId('delivery_driver_id')->nullable()->constrained()->onDelete('set null');
            $table->foreignId('delivery_zone_id')->nullable()->constrained()->onDelete('set null');
            
            // Delivery details
            $table->text('pickup_address'); // Restaurant address
            $table->text('delivery_address');
            $table->decimal('pickup_latitude', 10, 8)->nullable();
            $table->decimal('pickup_longitude', 11, 8)->nullable();
            $table->decimal('delivery_latitude', 10, 8)->nullable();
            $table->decimal('delivery_longitude', 11, 8)->nullable();
            
            // Timing
            $table->timestamp('assigned_at')->nullable();
            $table->timestamp('picked_up_at')->nullable();
            $table->timestamp('delivered_at')->nullable();
            $table->integer('estimated_delivery_time')->nullable(); // minutes
            $table->integer('actual_delivery_time')->nullable(); // minutes
            
            // Status tracking
            $table->enum('status', [
                'pending', 'assigned', 'picked_up', 'on_the_way', 
                'delivered', 'failed', 'cancelled', 'returned'
            ])->default('pending');
            
            // Distance and fees
            $table->decimal('distance_km', 8, 2)->nullable();
            $table->decimal('delivery_fee', 8, 2);
            $table->decimal('driver_commission', 8, 2)->nullable();
            
            // Customer interaction
            $table->text('delivery_instructions')->nullable();
            $table->text('delivery_notes')->nullable(); // Driver notes
            $table->string('delivery_photo')->nullable(); // Proof of delivery
            $table->integer('customer_rating')->nullable(); // 1-5 stars for driver
            $table->text('customer_feedback')->nullable();
            
            // Failed delivery tracking
            $table->text('failure_reason')->nullable();
            $table->integer('delivery_attempts')->default(0);
            $table->timestamp('next_attempt_at')->nullable();
            
            $table->timestamps();

            $table->index(['restaurant_id', 'status']);
            $table->index(['delivery_driver_id', 'status']);
            $table->index(['order_id']);
            $table->unique(['order_id']); // One delivery record per order
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('delivery_orders');
    }
};
