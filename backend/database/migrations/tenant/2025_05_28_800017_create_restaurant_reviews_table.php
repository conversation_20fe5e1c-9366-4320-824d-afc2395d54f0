<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('restaurant_reviews', function (Blueprint $table) {
            $table->id();
            $table->foreignId('restaurant_id')->constrained()->onDelete('cascade');
            $table->foreignId('customer_id')->nullable()->constrained()->onDelete('set null');
            $table->foreignId('order_id')->nullable()->constrained()->onDelete('set null');
            $table->foreignId('reservation_id')->nullable()->constrained('table_reservations')->onDelete('set null');
            
            // Review details
            $table->integer('overall_rating'); // 1-5 stars
            $table->string('title')->nullable();
            $table->text('review')->nullable();
            $table->json('images')->nullable(); // Array of review images
            
            // Detailed ratings
            $table->integer('food_rating')->nullable(); // 1-5
            $table->integer('service_rating')->nullable(); // 1-5
            $table->integer('ambiance_rating')->nullable(); // 1-5
            $table->integer('value_rating')->nullable(); // 1-5
            $table->integer('cleanliness_rating')->nullable(); // 1-5
            
            // Experience details
            $table->enum('visit_type', ['dine_in', 'takeaway', 'delivery'])->nullable();
            $table->date('visit_date')->nullable();
            $table->enum('party_size', ['1', '2', '3-4', '5-6', '7+'])->nullable();
            $table->enum('occasion', [
                'casual', 'business', 'date', 'family', 'celebration', 'other'
            ])->nullable();
            
            // Review metadata
            $table->string('reviewer_name')->nullable(); // For guest reviews
            $table->boolean('is_verified_visit')->default(false);
            $table->boolean('is_anonymous')->default(false);
            $table->enum('status', ['pending', 'approved', 'rejected', 'hidden'])->default('pending');
            
            // Moderation
            $table->foreignId('moderated_by')->nullable()->constrained('employees')->onDelete('set null');
            $table->timestamp('moderated_at')->nullable();
            $table->text('moderation_notes')->nullable();
            
            // Engagement
            $table->integer('helpful_count')->default(0);
            $table->integer('not_helpful_count')->default(0);
            $table->boolean('featured')->default(false);
            
            // Response from restaurant
            $table->text('restaurant_response')->nullable();
            $table->timestamp('responded_at')->nullable();
            $table->foreignId('responded_by')->nullable()->constrained('employees')->onDelete('set null');
            
            // Source tracking
            $table->string('source')->nullable(); // google, yelp, internal, etc.
            $table->string('external_id')->nullable(); // ID from external platform
            
            $table->timestamps();

            $table->index(['restaurant_id', 'status']);
            $table->index(['restaurant_id', 'customer_id']);
            $table->index(['restaurant_id', 'overall_rating']);
            $table->index(['restaurant_id', 'visit_date']);
            $table->index(['restaurant_id', 'featured']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('restaurant_reviews');
    }
};
