<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     * 
     * For now, we'll just add a backup column and keep the original category_id
     * This allows us to test the many-to-many relationship while maintaining backward compatibility
     */
    public function up(): void
    {
        Schema::table('menu_items', function (Blueprint $table) {
            // Add a backup column for the old category_id
            $table->unsignedBigInteger('category_id_backup')->nullable()->after('category_id');
        });
        
        // Copy existing category_id data to backup column
        DB::statement('UPDATE menu_items SET category_id_backup = category_id WHERE category_id IS NOT NULL');
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('menu_items', function (Blueprint $table) {
            $table->dropColumn('category_id_backup');
        });
    }
};
