<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Tables that need restaurant_id renamed to branch_id
        $tablesToUpdate = [
            'inventory_items',
            'inventory_categories',
            'vendors',
            'purchase_orders',
            'purchase_order_items',
            'stock_movements',
            'stock_batches',
            'expenses',
            'expense_categories',
            'waste_records',
        ];

        foreach ($tablesToUpdate as $table) {
            if (Schema::hasTable($table)) {
                // Check if restaurant_id column exists and branch_id doesn't
                if (Schema::hasColumn($table, 'restaurant_id') && !Schema::hasColumn($table, 'branch_id')) {
                    Schema::table($table, function (Blueprint $table) {
                        // Add branch_id column
                        $table->foreignId('branch_id')->nullable()->after('id')->constrained('branches')->onDelete('cascade');
                    });

                    // Copy data from restaurant_id to branch_id (assuming first branch for existing data)
                    $firstBranchId = DB::table('branches')->value('id');
                    if ($firstBranchId) {
                        DB::table($table)->update(['branch_id' => $firstBranchId]);
                    }

                    Schema::table($table, function (Blueprint $table) {
                        // Make branch_id not nullable
                        $table->foreignId('branch_id')->nullable(false)->change();
                        
                        // Drop restaurant_id column
                        $table->dropForeign(['restaurant_id']);
                        $table->dropColumn('restaurant_id');
                        
                        // Add indexes
                        $table->index(['branch_id']);
                    });
                }
            }
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        $tablesToRevert = [
            'inventory_items',
            'inventory_categories',
            'vendors',
            'purchase_orders',
            'purchase_order_items',
            'stock_movements',
            'stock_batches',
            'expenses',
            'expense_categories',
            'waste_records',
        ];

        foreach ($tablesToRevert as $table) {
            if (Schema::hasTable($table) && Schema::hasColumn($table, 'branch_id')) {
                Schema::table($table, function (Blueprint $table) {
                    // Add restaurant_id column back
                    $table->unsignedBigInteger('restaurant_id')->nullable()->after('id');
                });

                // Copy data back (this is a simplified revert)
                DB::table($table)->update(['restaurant_id' => 1]);

                Schema::table($table, function (Blueprint $table) {
                    // Make restaurant_id not nullable and drop branch_id
                    $table->unsignedBigInteger('restaurant_id')->nullable(false)->change();
                    $table->dropForeign(['branch_id']);
                    $table->dropColumn('branch_id');
                });
            }
        }
    }
};
