<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('tables', function (Blueprint $table) {
            // Check if columns don't exist before adding them
            if (!Schema::hasColumn('tables', 'restaurant_id')) {
                $table->unsignedBigInteger('restaurant_id')->nullable()->after('id');
            }
            
            if (!Schema::hasColumn('tables', 'qr_code')) {
                $table->string('qr_code')->nullable()->unique()->after('name');
            }
            
            if (!Schema::hasColumn('tables', 'min_capacity')) {
                $table->integer('min_capacity')->default(1)->after('capacity');
            }
            
            if (!Schema::hasColumn('tables', 'shape')) {
                $table->enum('shape', ['rectangle', 'circle', 'square'])->default('rectangle')->after('location');
            }
            
            if (!Schema::hasColumn('tables', 'description')) {
                $table->text('description')->nullable()->after('shape');
            }
            
            if (!Schema::hasColumn('tables', 'status')) {
                $table->enum('status', ['available', 'occupied', 'reserved', 'maintenance', 'cleaning'])->default('available')->after('description');
            }
            
            // Position and layout fields
            if (!Schema::hasColumn('tables', 'position_x')) {
                $table->decimal('position_x', 8, 2)->nullable()->after('status');
            }
            
            if (!Schema::hasColumn('tables', 'position_y')) {
                $table->decimal('position_y', 8, 2)->nullable()->after('position_x');
            }
            
            if (!Schema::hasColumn('tables', 'width')) {
                $table->decimal('width', 8, 2)->default(100)->after('position_y');
            }
            
            if (!Schema::hasColumn('tables', 'height')) {
                $table->decimal('height', 8, 2)->default(100)->after('width');
            }
            
            if (!Schema::hasColumn('tables', 'rotation')) {
                $table->integer('rotation')->default(0)->after('height');
            }
            
            // Feature flags
            if (!Schema::hasColumn('tables', 'has_power_outlet')) {
                $table->boolean('has_power_outlet')->default(false)->after('rotation');
            }
            
            if (!Schema::hasColumn('tables', 'has_window_view')) {
                $table->boolean('has_window_view')->default(false)->after('has_power_outlet');
            }
            
            if (!Schema::hasColumn('tables', 'is_wheelchair_accessible')) {
                $table->boolean('is_wheelchair_accessible')->default(true)->after('has_window_view');
            }
            
            if (!Schema::hasColumn('tables', 'is_high_top')) {
                $table->boolean('is_high_top')->default(false)->after('is_wheelchair_accessible');
            }
            
            if (!Schema::hasColumn('tables', 'is_booth')) {
                $table->boolean('is_booth')->default(false)->after('is_high_top');
            }
            
            if (!Schema::hasColumn('tables', 'is_outdoor')) {
                $table->boolean('is_outdoor')->default(false)->after('is_booth');
            }
            
            // Remove the old is_available column if it exists and add it back in the right place
            if (Schema::hasColumn('tables', 'is_available')) {
                $table->dropColumn('is_available');
            }
            
            // Rename number to name if number exists and name doesn't
            if (Schema::hasColumn('tables', 'number') && !Schema::hasColumn('tables', 'name')) {
                $table->renameColumn('number', 'name');
            } elseif (Schema::hasColumn('tables', 'number') && Schema::hasColumn('tables', 'name')) {
                // If both exist, drop number
                $table->dropColumn('number');
            }
        });
        
        // Add foreign key constraint if restaurant_id was added
        if (Schema::hasColumn('tables', 'restaurant_id')) {
            Schema::table('tables', function (Blueprint $table) {
                // Only add foreign key if restaurants table exists
                if (Schema::hasTable('restaurants')) {
                    $table->foreign('restaurant_id')->references('id')->on('restaurants')->onDelete('cascade');
                }
            });
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('tables', function (Blueprint $table) {
            // Drop foreign key first
            if (Schema::hasColumn('tables', 'restaurant_id')) {
                $table->dropForeign(['restaurant_id']);
            }
            
            $table->dropColumn([
                'restaurant_id', 'qr_code', 'min_capacity', 'shape', 'description', 'status',
                'position_x', 'position_y', 'width', 'height', 'rotation',
                'has_power_outlet', 'has_window_view', 'is_wheelchair_accessible',
                'is_high_top', 'is_booth', 'is_outdoor'
            ]);
            
            // Add back the old columns
            $table->boolean('is_available')->default(true);
            $table->string('number')->unique();
        });
    }
};
