<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('menu_item_combos', function (Blueprint $table) {
            $table->id();
            $table->foreignId('parent_item_id')->constrained('menu_items')->onDelete('cascade');
            $table->foreignId('menu_item_id')->constrained('menu_items')->onDelete('cascade');
            $table->enum('component_type', ['main', 'side', 'drink', 'dessert']);
            $table->boolean('is_required')->default(false);
            $table->integer('sort_order')->default(0);
            $table->timestamps();

            // Indexes
            $table->index(['parent_item_id', 'sort_order']);
            $table->index(['parent_item_id', 'component_type']);
            $table->unique(['parent_item_id', 'menu_item_id'], 'unique_combo_item');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('menu_item_combos');
    }
};
