<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('menu_items', function (Blueprint $table) {
            // Add media_id foreign key - check if media table exists first
            if (Schema::hasTable('media')) {
                $table->foreignId('media_id')->nullable()->after('category_id')->constrained('media')->onDelete('set null');
            } else {
                $table->unsignedBigInteger('media_id')->nullable()->after('category_id');
            }

            // Keep the old image column for backward compatibility during migration
            // We'll remove it later after data migration
            $table->string('image_backup')->nullable()->after('media_id');
        });
        
        // Copy existing image data to backup column
        DB::statement('UPDATE menu_items SET image_backup = image WHERE image IS NOT NULL');
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('menu_items', function (Blueprint $table) {
            $table->dropForeign(['media_id']);
            $table->dropColumn(['media_id', 'image_backup']);
        });
    }
};
