<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('menu_item_addons', function (Blueprint $table) {
            $table->id();
            $table->string('name'); // e.g., "Extra Cheese", "Sausage", "Bacon"
            $table->string('name_bn')->nullable(); // Bengali translation
            $table->decimal('price', 10, 2); // Additional price for this addon
            $table->string('category')->default('general'); // e.g., "Toppings", "Sides", "Sauces"
            $table->string('category_bn')->nullable(); // Bengali category translation
            $table->integer('max_quantity')->default(1); // Maximum quantity allowed per order
            $table->boolean('is_available')->default(true);
            $table->integer('sort_order')->default(0);
            $table->text('description')->nullable();
            $table->text('description_bn')->nullable();
            $table->timestamps();

            $table->index(['category', 'is_available']);
            $table->index(['is_available', 'sort_order']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('menu_item_addons');
    }
};
