<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('orders', function (Blueprint $table) {
            // Add order_closed_at column as nullable timestamp
            // $table->timestamp('order_closed_at')->nullable();

            // Add index for better query performance
            // $table->index(['order_closed_at']);
            $table->index(['branch_id', 'order_closed_at']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('orders', function (Blueprint $table) {
            // Drop indexes first
            $table->dropIndex(['order_closed_at']);
            $table->dropIndex(['branch_id', 'order_closed_at']);
            
            // Drop the column
            $table->dropColumn('order_closed_at');
        });
    }
};
