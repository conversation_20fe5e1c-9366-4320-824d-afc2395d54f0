<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('orders', function (Blueprint $table) {
            // Add POS-specific columns that are missing
            if (!Schema::hasColumn('orders', 'branch_id')) {
                $table->foreignId('branch_id')->nullable()->constrained()->onDelete('set null')->after('id');
            }
            
            if (!Schema::hasColumn('orders', 'order_source')) {
                $table->enum('order_source', ['online', 'pos', 'phone', 'walk_in'])->default('online')->after('order_type');
            }
            
            if (!Schema::hasColumn('orders', 'kitchen_status')) {
                $table->enum('kitchen_status', ['pending', 'preparing', 'ready', 'served', 'cancelled'])->default('pending')->after('status');
            }
            
            if (!Schema::hasColumn('orders', 'pos_session_id')) {
                $table->string('pos_session_id')->nullable()->after('order_number');
            }
            
            if (!Schema::hasColumn('orders', 'guest_count')) {
                $table->integer('guest_count')->default(1)->after('pos_session_id');
            }
            
            if (!Schema::hasColumn('orders', 'pos_notes')) {
                $table->text('pos_notes')->nullable()->after('special_instructions');
            }
            
            if (!Schema::hasColumn('orders', 'tax_rate')) {
                $table->decimal('tax_rate', 5, 4)->default(0)->after('total_amount');
            }
            
            if (!Schema::hasColumn('orders', 'service_charge_rate')) {
                $table->decimal('service_charge_rate', 5, 4)->default(0)->after('tax_rate');
            }
            
            if (!Schema::hasColumn('orders', 'opened_by')) {
                $table->foreignId('opened_by')->nullable()->constrained('users')->onDelete('set null')->after('service_charge_rate');
            }
            
            if (!Schema::hasColumn('orders', 'closed_by')) {
                $table->foreignId('closed_by')->nullable()->constrained('users')->onDelete('set null')->after('opened_by');
            }
            
            if (!Schema::hasColumn('orders', 'order_opened_at')) {
                $table->timestamp('order_opened_at')->nullable()->after('closed_by');
            }
            
            if (!Schema::hasColumn('orders', 'order_closed_at')) {
                $table->timestamp('order_closed_at')->nullable()->after('order_opened_at');
            }
            
            if (!Schema::hasColumn('orders', 'kitchen_notified_at')) {
                $table->timestamp('kitchen_notified_at')->nullable()->after('order_closed_at');
            }
            
            if (!Schema::hasColumn('orders', 'estimated_prep_time')) {
                $table->integer('estimated_prep_time')->nullable()->after('kitchen_notified_at');
            }
            
            if (!Schema::hasColumn('orders', 'kitchen_instructions')) {
                $table->json('kitchen_instructions')->nullable()->after('estimated_prep_time');
            }
            
            if (!Schema::hasColumn('orders', 'applied_taxes')) {
                $table->json('applied_taxes')->nullable()->after('kitchen_instructions');
            }
            
            if (!Schema::hasColumn('orders', 'is_split_bill')) {
                $table->boolean('is_split_bill')->default(false)->after('applied_taxes');
            }
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('orders', function (Blueprint $table) {
            $columns = [
                'branch_id',
                'order_source',
                'kitchen_status',
                'pos_session_id',
                'guest_count',
                'pos_notes',
                'tax_rate',
                'service_charge_rate',
                'opened_by',
                'closed_by',
                'order_opened_at',
                'order_closed_at',
                'kitchen_notified_at',
                'estimated_prep_time',
                'kitchen_instructions',
                'applied_taxes',
                'is_split_bill'
            ];
            
            foreach ($columns as $column) {
                if (Schema::hasColumn('orders', $column)) {
                    $table->dropColumn($column);
                }
            }
        });
    }
};
