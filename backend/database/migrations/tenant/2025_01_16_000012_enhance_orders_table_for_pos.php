<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('orders', function (Blueprint $table) {
            // Add branch_id if not exists
            if (!Schema::hasColumn('orders', 'branch_id')) {
                $table->foreignId('branch_id')->nullable()->after('restaurant_id')->constrained()->onDelete('cascade');
            }
            
            // POS specific fields
            $table->string('pos_session_id')->nullable()->after('order_number');
            $table->enum('order_source', ['pos', 'online', 'phone', 'walk_in', 'qr_code'])->default('pos')->after('order_type');
            $table->decimal('tax_rate', 5, 4)->default(0)->after('tax_amount'); // Store tax rate used
            // $table->decimal('service_charge_rate', 5, 4)->default(0)->after('service_fee'); // Store service charge rate
            $table->json('applied_taxes')->nullable()->after('tax_rate'); // Multiple tax types
            // $table->boolean('is_split_bill')->default(false)->after('payment_method');
            // $table->integer('guest_count')->nullable()->after('table_id');
            $table->text('pos_notes')->nullable()->after('special_instructions');
            // $table->timestamp('order_opened_at')->nullable()->after('created_at');
            $table->timestamp('order_closed_at')->nullable()->after('pos_notes');
            // $table->foreignId('opened_by')->nullable()->after('assigned_to')->constrained('users')->onDelete('set null');
            // $table->foreignId('closed_by')->nullable()->after('opened_by')->constrained('users')->onDelete('set null');
            
            // Kitchen workflow fields
            // $table->timestamp('kitchen_notified_at')->nullable()->after('preparing_at');
            // $table->integer('estimated_prep_time')->nullable()->after('estimated_preparation_time'); // in minutes
            $table->json('kitchen_instructions')->nullable()->after('pos_notes');
            
            // Enhanced status tracking
            $table->enum('kitchen_status', [
                'pending', 
                'received', 
                'preparing', 
                'ready', 
                'served'
            ])->default('pending')->after('status');
            
            // Add indexes for POS operations
            // $table->index(['branch_id', 'status']);
            $table->index(['table_id', 'status']);
            $table->index(['order_source', 'created_at']);
            $table->index(['kitchen_status', 'created_at']);
            $table->index(['pos_session_id']);
            // $table->index(['', 'order_opened_at']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('orders', function (Blueprint $table) {
            // Remove indexes first
            $table->dropIndex(['branch_id', 'status']);
            $table->dropIndex(['table_id', 'status']);
            $table->dropIndex(['order_source', 'created_at']);
            $table->dropIndex(['kitchen_status', 'created_at']);
            $table->dropIndex(['pos_session_id']);
            $table->dropIndex(['opened_by', 'order_opened_at']);
            
            // Remove columns
            $table->dropForeign(['branch_id']);
            $table->dropForeign(['opened_by']);
            $table->dropForeign(['closed_by']);
            
            $table->dropColumn([
                'branch_id',
                'pos_session_id',
                'order_source',
                'tax_rate',
                'service_charge_rate',
                'applied_taxes',
                'is_split_bill',
                'guest_count',
                'pos_notes',
                'order_opened_at',
                'order_closed_at',
                'opened_by',
                'closed_by',
                'kitchen_notified_at',
                'estimated_prep_time',
                'kitchen_instructions',
                'kitchen_status'
            ]);
        });
    }
};
