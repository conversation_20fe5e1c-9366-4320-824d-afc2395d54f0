<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('loyalty_transactions', function (Blueprint $table) {
            $table->id();
            $table->foreignId('loyalty_account_id')->constrained()->onDelete('cascade');
            $table->foreignId('order_id')->nullable()->constrained()->onDelete('set null');
            $table->enum('transaction_type', ['earned', 'redeemed', 'refunded', 'manual_adjustment', 'birthday_bonus', 'referral_bonus', 'expired']);
            $table->integer('points'); // Positive for earned/refunded, negative for redeemed
            $table->decimal('order_amount', 12, 2)->nullable(); // Order amount for earned points
            $table->string('description');
            $table->unsignedBigInteger('processed_by')->nullable(); // User ID who processed (for manual adjustments)
            $table->text('notes')->nullable();
            $table->json('metadata')->nullable(); // Additional data (e.g., referral details)
            $table->timestamp('expires_at')->nullable(); // For point expiry tracking
            $table->timestamps();

            // Indexes for performance
            $table->index(['loyalty_account_id', 'transaction_type']);
            $table->index(['order_id']);
            $table->index(['transaction_type', 'created_at']);
            $table->index(['expires_at']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('loyalty_transactions');
    }
};
