<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('employee_loans', function (Blueprint $table) {
            $table->id();
            $table->foreignId('employee_id')->constrained()->onDelete('cascade');
            $table->string('loan_number')->unique(); // Auto-generated loan reference
            $table->decimal('principal_amount', 12, 2); // Original loan amount
            $table->decimal('interest_rate', 5, 2)->default(0); // Annual interest rate %
            $table->integer('term_months'); // Loan term in months
            $table->decimal('monthly_installment', 10, 2); // Fixed monthly payment
            $table->decimal('total_amount', 12, 2); // Principal + Interest
            $table->decimal('paid_amount', 12, 2)->default(0); // Amount paid so far
            $table->decimal('outstanding_balance', 12, 2); // Remaining balance
            $table->date('start_date'); // When loan starts
            $table->date('end_date'); // Expected completion date
            $table->date('next_payment_date')->nullable(); // Next installment due
            $table->enum('status', ['active', 'completed', 'defaulted', 'cancelled'])->default('active');
            $table->text('purpose')->nullable(); // Reason for loan
            $table->text('terms_conditions')->nullable();
            $table->text('notes')->nullable();
            
            // Approval workflow
            $table->foreignId('approved_by')->nullable()->constrained('employees')->onDelete('set null');
            $table->timestamp('approved_at')->nullable();
            $table->text('approval_notes')->nullable();
            
            // Guarantor information (optional)
            $table->string('guarantor_name')->nullable();
            $table->string('guarantor_phone')->nullable();
            $table->text('guarantor_address')->nullable();
            
            $table->timestamps();

            $table->index(['employee_id', 'status']);
            $table->index(['status', 'next_payment_date']);
            $table->index('loan_number');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('employee_loans');
    }
};
