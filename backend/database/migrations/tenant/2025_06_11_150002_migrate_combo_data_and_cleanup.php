<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Migrate existing combo data if combo_menus table exists
        if (Schema::hasTable('combo_menus')) {
            $combos = DB::table('combo_menus')->get();
            
            foreach ($combos as $combo) {
                // Create a new menu item for this combo
                $menuItemId = DB::table('menu_items')->insertGetId([
                    'name' => $combo->name,
                    'description' => $combo->description,
                    'price' => $combo->price,
                    'is_available' => $combo->is_available,
                    'is_combo' => true,
                    'sort_order' => $combo->sort_order,
                    'media_id' => $combo->media_id ?? null,
                    'created_at' => $combo->created_at,
                    'updated_at' => $combo->updated_at,
                ]);

                // Migrate combo components if they exist
                if (Schema::hasTable('combo_components')) {
                    $components = DB::table('combo_components')
                        ->where('combo_menu_id', $combo->id)
                        ->get();
                    
                    foreach ($components as $component) {
                        DB::table('menu_item_combos')->insert([
                            'parent_item_id' => $menuItemId,
                            'menu_item_id' => $component->menu_item_id,
                            'component_type' => $component->component_type,
                            'is_required' => $component->is_required ?? false,
                            'sort_order' => $component->sort_order ?? 0,
                            'created_at' => $component->created_at,
                            'updated_at' => $component->updated_at,
                        ]);
                    }
                }
            }
        }

        // Drop old tables
        Schema::dropIfExists('combo_components');
        Schema::dropIfExists('combo_menu_media');
        Schema::dropIfExists('combo_menus');
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Recreate combo_menus table
        Schema::create('combo_menus', function (Blueprint $table) {
            $table->id();
            $table->string('name');
            $table->text('description')->nullable();
            $table->decimal('price', 10, 2);
            $table->boolean('is_available')->default(true);
            $table->integer('sort_order')->default(0);
            $table->string('image_url')->nullable();
            $table->foreignId('media_id')->nullable()->constrained()->onDelete('set null');
            $table->json('customization_rules')->nullable();
            $table->timestamps();

            $table->index(['is_available', 'sort_order']);
        });

        // Recreate combo_components table
        Schema::create('combo_components', function (Blueprint $table) {
            $table->id();
            $table->foreignId('combo_menu_id')->constrained()->onDelete('cascade');
            $table->foreignId('menu_item_id')->constrained()->onDelete('cascade');
            $table->enum('component_type', ['main', 'side', 'drink', 'dessert']);
            $table->boolean('is_required')->default(false);
            $table->integer('max_selections')->default(1);
            $table->integer('sort_order')->default(0);
            $table->timestamps();

            $table->index(['combo_menu_id', 'component_type']);
        });

        // Recreate combo_menu_media table
        Schema::create('combo_menu_media', function (Blueprint $table) {
            $table->id();
            $table->foreignId('combo_menu_id')->constrained()->onDelete('cascade');
            $table->foreignId('media_id')->constrained()->onDelete('cascade');
            $table->integer('sort_order')->default(0);
            $table->boolean('is_primary')->default(false);
            $table->timestamps();

            $table->index(['combo_menu_id', 'sort_order']);
            $table->index(['combo_menu_id', 'is_primary']);
            $table->unique(['combo_menu_id', 'media_id']);
        });

        // Migrate data back (simplified - would need more complex logic for full restoration)
        $comboItems = DB::table('menu_items')->where('is_combo', true)->get();
        
        foreach ($comboItems as $item) {
            $comboId = DB::table('combo_menus')->insertGetId([
                'name' => $item->name,
                'description' => $item->description,
                'price' => $item->price,
                'is_available' => $item->is_available,
                'sort_order' => $item->sort_order,
                'media_id' => $item->media_id,
                'created_at' => $item->created_at,
                'updated_at' => $item->updated_at,
            ]);

            $components = DB::table('menu_item_combos')
                ->where('parent_item_id', $item->id)
                ->get();
            
            foreach ($components as $component) {
                DB::table('combo_components')->insert([
                    'combo_menu_id' => $comboId,
                    'menu_item_id' => $component->menu_item_id,
                    'component_type' => $component->component_type,
                    'is_required' => $component->is_required,
                    'sort_order' => $component->sort_order,
                    'created_at' => $component->created_at,
                    'updated_at' => $component->updated_at,
                ]);
            }
        }

        // Remove combo items from menu_items
        DB::table('menu_items')->where('is_combo', true)->delete();
    }
};
