<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('waste_records', function (Blueprint $table) {
            $table->id();
            $table->foreignId('restaurant_id')->constrained()->onDelete('cascade');
            $table->foreignId('inventory_item_id')->constrained()->onDelete('cascade');
            
            $table->string('batch_number')->nullable();
            $table->decimal('quantity', 10, 2);
            $table->decimal('unit_cost', 10, 4);
            $table->decimal('total_cost', 12, 2);
            
            $table->enum('reason', [
                'expired', 'damaged', 'spoiled', 'contaminated', 'overproduction',
                'preparation_error', 'customer_return', 'quality_control', 'theft', 'other'
            ]);
            
            $table->date('waste_date');
            $table->unsignedBigInteger('recorded_by')->nullable(); // References central users table
            $table->text('notes')->nullable();
            $table->timestamps();

            $table->index(['restaurant_id', 'inventory_item_id']);
            $table->index(['restaurant_id', 'reason']);
            $table->index(['restaurant_id', 'waste_date']);
            $table->index('batch_number');
            $table->index('waste_date');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('waste_records');
    }
};
