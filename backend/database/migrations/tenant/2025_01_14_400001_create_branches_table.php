<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('branches', function (Blueprint $table) {
            $table->id();
            $table->string('name');
            $table->string('slug')->unique();
            $table->text('description')->nullable();
            $table->string('address');
            $table->string('city');
            $table->string('state')->nullable();
            $table->string('postal_code')->nullable();
            $table->string('country')->default('Bangladesh');
            $table->string('phone')->nullable();
            $table->string('email')->nullable();
            // manager_id foreign key will be added later after employees table is created
            $table->decimal('latitude', 10, 8)->nullable();
            $table->decimal('longitude', 11, 8)->nullable();
            $table->time('opening_time')->default('09:00');
            $table->time('closing_time')->default('22:00');
            $table->boolean('is_active')->default(true);
            $table->boolean('is_main_branch')->default(false);
            $table->integer('sort_order')->default(0);
            $table->json('operating_days')->nullable(); // ['monday', 'tuesday', etc.]
            $table->json('contact_info')->nullable(); // Additional contact details
            $table->timestamps();

            $table->index(['is_active', 'sort_order']);
            $table->index('is_main_branch');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('branches');
    }
};
