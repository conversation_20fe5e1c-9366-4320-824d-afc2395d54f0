<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Add branch_id to orders table if it exists
        if (Schema::hasTable('orders')) {
            Schema::table('orders', function (Blueprint $table) {
                if (!Schema::hasColumn('orders', 'branch_id')) {
                    $table->foreignId('branch_id')->nullable()->after('id')->constrained('branches')->onDelete('cascade');
                    $table->index(['branch_id', 'status']);
                    $table->index(['branch_id', 'created_at']);
                }
            });
        }

        // Add branch_id to inventory table if it exists
        if (Schema::hasTable('inventory')) {
            Schema::table('inventory', function (Blueprint $table) {
                if (!Schema::hasColumn('inventory', 'branch_id')) {
                    $table->foreignId('branch_id')->nullable()->after('id')->constrained('branches')->onDelete('cascade');
                    $table->index(['branch_id', 'item_name']);
                    $table->index(['branch_id', 'category_id']);
                }
            });
        }

        // Add branch_id to purchase_orders table if it exists
        if (Schema::hasTable('purchase_orders')) {
            Schema::table('purchase_orders', function (Blueprint $table) {
                if (!Schema::hasColumn('purchase_orders', 'branch_id')) {
                    $table->foreignId('branch_id')->nullable()->after('id')->constrained('branches')->onDelete('cascade');
                    $table->index(['branch_id', 'status']);
                    $table->index(['branch_id', 'created_at']);
                }
            });
        }

        // Add branch_id to tables table if it exists
        if (Schema::hasTable('tables')) {
            Schema::table('tables', function (Blueprint $table) {
                if (!Schema::hasColumn('tables', 'branch_id')) {
                    $table->foreignId('branch_id')->nullable()->after('id')->constrained('branches')->onDelete('cascade');
                    $table->index(['branch_id', 'is_active']);
                    $table->index(['branch_id', 'is_available']);
                }
            });
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Remove branch_id from orders table
        if (Schema::hasTable('orders') && Schema::hasColumn('orders', 'branch_id')) {
            Schema::table('orders', function (Blueprint $table) {
                $table->dropForeign(['branch_id']);
                $table->dropColumn('branch_id');
            });
        }

        // Remove branch_id from inventory table
        if (Schema::hasTable('inventory') && Schema::hasColumn('inventory', 'branch_id')) {
            Schema::table('inventory', function (Blueprint $table) {
                $table->dropForeign(['branch_id']);
                $table->dropColumn('branch_id');
            });
        }

        // Remove branch_id from purchase_orders table
        if (Schema::hasTable('purchase_orders') && Schema::hasColumn('purchase_orders', 'branch_id')) {
            Schema::table('purchase_orders', function (Blueprint $table) {
                $table->dropForeign(['branch_id']);
                $table->dropColumn('branch_id');
            });
        }

        // Remove branch_id from tables table
        if (Schema::hasTable('tables') && Schema::hasColumn('tables', 'branch_id')) {
            Schema::table('tables', function (Blueprint $table) {
                $table->dropForeign(['branch_id']);
                $table->dropColumn('branch_id');
            });
        }
    }
};
