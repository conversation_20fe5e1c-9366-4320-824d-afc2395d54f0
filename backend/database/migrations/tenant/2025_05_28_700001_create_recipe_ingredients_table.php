<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('recipe_ingredients', function (Blueprint $table) {
            $table->id();
            $table->foreignId('menu_item_id')->constrained()->onDelete('cascade');
            $table->foreignId('inventory_item_id')->constrained()->onDelete('cascade');
            $table->decimal('quantity_required', 10, 4); // Quantity needed per menu item
            $table->string('unit'); // Unit of measurement for this ingredient
            $table->decimal('cost_per_unit', 10, 4)->nullable(); // Cost per unit at time of recipe creation
            $table->timestamps();

            $table->unique(['menu_item_id', 'inventory_item_id']);
            $table->index(['menu_item_id']);
            $table->index(['inventory_item_id']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('recipe_ingredients');
    }
};
