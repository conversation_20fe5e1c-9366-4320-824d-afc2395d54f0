<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('waitlists', function (Blueprint $table) {
            $table->id();
            $table->foreignId('restaurant_id')->constrained()->onDelete('cascade');
            $table->foreignId('customer_id')->nullable()->constrained()->onDelete('set null');
            $table->string('waitlist_number')->unique();
            $table->string('customer_name');
            $table->string('customer_phone');
            $table->integer('party_size');
            $table->string('preferred_table_type')->nullable(); // indoor, outdoor, booth, etc.
            $table->integer('estimated_wait_time')->nullable(); // in minutes
            $table->enum('status', [
                'waiting', 'notified', 'seated', 'left', 'no_show'
            ])->default('waiting');
            $table->text('special_requests')->nullable();
            $table->text('notes')->nullable();
            $table->timestamp('joined_at');
            $table->timestamp('notified_at')->nullable();
            $table->timestamp('seated_at')->nullable();
            $table->timestamp('left_at')->nullable();
            $table->unsignedBigInteger('created_by')->nullable(); // References central users table
            $table->timestamps();

            $table->index(['restaurant_id', 'status']);
            $table->index(['restaurant_id', 'joined_at']);
            $table->index('party_size');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('waitlists');
    }
};
