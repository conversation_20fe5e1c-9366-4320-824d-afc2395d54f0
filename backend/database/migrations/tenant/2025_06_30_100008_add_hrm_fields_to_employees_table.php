<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('employees', function (Blueprint $table) {
            // Payroll-related fields
            if (!Schema::hasColumn('employees', 'pay_frequency')) {
                $table->enum('pay_frequency', ['weekly', 'bi_weekly', 'monthly'])->default('monthly')->after('hourly_rate');
            }
            
            if (!Schema::hasColumn('employees', 'overtime_rate_multiplier')) {
                $table->decimal('overtime_rate_multiplier', 4, 2)->default(1.5)->after('pay_frequency');
            }
            
            if (!Schema::hasColumn('employees', 'tax_id_number')) {
                $table->string('tax_id_number')->nullable()->after('tax_id');
            }
            
            // Leave-related fields
            if (!Schema::hasColumn('employees', 'annual_leave_days')) {
                $table->integer('annual_leave_days')->default(21)->after('overtime_rate_multiplier');
            }
            
            if (!Schema::hasColumn('employees', 'sick_leave_days')) {
                $table->integer('sick_leave_days')->default(10)->after('annual_leave_days');
            }
            
            // Attendance-related fields
            if (!Schema::hasColumn('employees', 'default_shift_start')) {
                $table->time('default_shift_start')->nullable()->after('sick_leave_days');
            }
            
            if (!Schema::hasColumn('employees', 'default_shift_end')) {
                $table->time('default_shift_end')->nullable()->after('default_shift_start');
            }
            
            if (!Schema::hasColumn('employees', 'work_schedule')) {
                $table->json('work_schedule')->nullable()->after('default_shift_end'); // Days of week they work
            }
            
            // Manager/supervisor relationship
            if (!Schema::hasColumn('employees', 'supervisor_id')) {
                $table->foreignId('supervisor_id')->nullable()->constrained('employees')->onDelete('set null')->after('primary_branch_id');
            }
            
            // HR-specific fields
            if (!Schema::hasColumn('employees', 'probation_end_date')) {
                $table->date('probation_end_date')->nullable()->after('hire_date');
            }
            
            if (!Schema::hasColumn('employees', 'contract_type')) {
                $table->enum('contract_type', ['permanent', 'temporary', 'contract', 'internship'])->default('permanent')->after('employment_type');
            }
            
            if (!Schema::hasColumn('employees', 'notice_period_days')) {
                $table->integer('notice_period_days')->default(30)->after('contract_type');
            }
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('employees', function (Blueprint $table) {
            $columns = [
                'pay_frequency', 'overtime_rate_multiplier', 'tax_id_number',
                'annual_leave_days', 'sick_leave_days', 'default_shift_start',
                'default_shift_end', 'work_schedule', 'supervisor_id',
                'probation_end_date', 'contract_type', 'notice_period_days'
            ];
            
            foreach ($columns as $column) {
                if (Schema::hasColumn('employees', $column)) {
                    if ($column === 'supervisor_id') {
                        $table->dropForeign(['supervisor_id']);
                    }
                    $table->dropColumn($column);
                }
            }
        });
    }
};
