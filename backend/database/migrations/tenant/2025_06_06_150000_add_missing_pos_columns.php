<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('orders', function (Blueprint $table) {
            $existingColumns = Schema::getColumnListing('orders');
            
            // Add branch_id - essential for POS queries
            if (!in_array('branch_id', $existingColumns)) {
                $table->foreignId('branch_id')->nullable()->constrained('branches')->onDelete('set null')->after('id');
            }
            
            // Add order_source - needed to filter POS orders
            if (!in_array('order_source', $existingColumns)) {
                $table->enum('order_source', ['online', 'pos', 'phone', 'walk_in'])->default('online')->after('order_type');
            }
            
            // Add order_closed_at - needed for POS queries
            if (!in_array('order_closed_at', $existingColumns)) {
                $table->timestamp('order_closed_at')->nullable()->after('updated_at');
            }
            
            // Add order_number - useful for order identification
            if (!in_array('order_number', $existingColumns)) {
                $table->string('order_number')->unique()->nullable()->after('id');
            }
            
            // Add kitchen_status - needed for kitchen workflow
            if (!in_array('kitchen_status', $existingColumns)) {
                $table->enum('kitchen_status', ['pending', 'preparing', 'ready', 'served', 'cancelled'])->default('pending')->after('status');
            }
            
            // Add total_amount - might be referenced in some places
            if (!in_array('total_amount', $existingColumns)) {
                $table->decimal('total_amount', 10, 2)->default(0)->after('grand_total');
            }
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('orders', function (Blueprint $table) {
            $columns = [
                'branch_id', 'order_source', 'order_closed_at', 
                'order_number', 'kitchen_status', 'total_amount'
            ];
            
            foreach ($columns as $column) {
                if (Schema::hasColumn('orders', $column)) {
                    $table->dropColumn($column);
                }
            }
        });
    }
};
