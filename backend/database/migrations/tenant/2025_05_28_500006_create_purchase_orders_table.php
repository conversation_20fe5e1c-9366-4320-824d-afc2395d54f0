<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('purchase_orders', function (Blueprint $table) {
            $table->id();
            $table->foreignId('restaurant_id')->constrained()->onDelete('cascade');
            $table->foreignId('vendor_id')->constrained()->onDelete('restrict');
            $table->unsignedBigInteger('created_by')->nullable(); // References central users table
            $table->unsignedBigInteger('approved_by')->nullable(); // References central users table
            
            $table->string('po_number')->unique();
            $table->enum('status', [
                'draft', 'pending', 'approved', 'sent', 'partially_received', 
                'received', 'completed', 'cancelled'
            ])->default('draft');
            
            $table->date('order_date');
            $table->date('expected_delivery_date')->nullable();
            $table->date('actual_delivery_date')->nullable();
            
            // Financial details
            $table->decimal('subtotal', 12, 2)->default(0);
            $table->decimal('tax_amount', 12, 2)->default(0);
            $table->decimal('shipping_cost', 12, 2)->default(0);
            $table->decimal('discount_amount', 12, 2)->default(0);
            $table->decimal('total_amount', 12, 2)->default(0);
            
            $table->string('payment_terms')->nullable();
            $table->text('delivery_address')->nullable();
            $table->text('notes')->nullable(); // Notes for vendor
            $table->text('internal_notes')->nullable(); // Internal notes
            
            $table->timestamps();

            $table->index(['restaurant_id', 'vendor_id']);
            $table->index(['restaurant_id', 'status']);
            $table->index('po_number');
            $table->index(['order_date', 'status']);
            $table->index('expected_delivery_date');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('purchase_orders');
    }
};
