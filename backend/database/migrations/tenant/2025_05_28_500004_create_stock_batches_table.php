<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('stock_batches', function (Blueprint $table) {
            $table->id();
            $table->foreignId('restaurant_id')->constrained()->onDelete('cascade');
            $table->foreignId('inventory_item_id')->constrained()->onDelete('cascade');
            
            $table->string('batch_number');
            $table->decimal('quantity', 10, 2); // Current quantity in batch
            $table->decimal('original_quantity', 10, 2); // Original quantity when received
            $table->decimal('unit_cost', 10, 4);
            
            $table->date('expiry_date')->nullable();
            $table->date('received_date');
            $table->string('supplier_batch_number')->nullable(); // Vendor's batch number
            
            $table->text('notes')->nullable();
            $table->timestamps();

            $table->index(['restaurant_id', 'inventory_item_id']);
            $table->index(['restaurant_id', 'expiry_date']);
            $table->index('batch_number');
            $table->index(['expiry_date', 'quantity']); // For expiry queries
            $table->index('received_date');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('stock_batches');
    }
};
