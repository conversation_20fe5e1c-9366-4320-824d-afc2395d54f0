<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('tables', function (Blueprint $table) {
            // Add table_number column if it doesn't exist
            if (!Schema::hasColumn('tables', 'table_number')) {
                $table->string('table_number')->nullable()->after('name');
            }
            
            // Add branch_id column if it doesn't exist
            if (!Schema::hasColumn('tables', 'branch_id')) {
                $table->foreignId('branch_id')->nullable()->after('restaurant_id')->constrained()->onDelete('cascade');
                $table->index('branch_id');
            }
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('tables', function (Blueprint $table) {
            if (Schema::hasColumn('tables', 'table_number')) {
                $table->dropColumn('table_number');
            }
            
            if (Schema::hasColumn('tables', 'branch_id')) {
                $table->dropForeign(['branch_id']);
                $table->dropColumn('branch_id');
            }
        });
    }
};
