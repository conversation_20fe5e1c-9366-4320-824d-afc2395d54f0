<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('tables', function (Blueprint $table) {
            // Add waiter assignment field
            if (!Schema::hasColumn('tables', 'assigned_waiter_id')) {
                $table->foreignId('assigned_waiter_id')->nullable()->after('status')->constrained('users')->onDelete('set null');
                $table->index('assigned_waiter_id');
            }
            
            // Add current party size field
            if (!Schema::hasColumn('tables', 'current_party_size')) {
                $table->integer('current_party_size')->nullable()->after('assigned_waiter_id');
            }
            
            // Add occupied timestamp
            if (!Schema::hasColumn('tables', 'occupied_at')) {
                $table->timestamp('occupied_at')->nullable()->after('current_party_size');
            }
            
            // Add table type field if it doesn't exist
            if (!Schema::hasColumn('tables', 'table_type')) {
                $table->enum('table_type', ['regular', 'vip', 'outdoor', 'private', 'bar'])->default('regular')->after('occupied_at');
            }
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('tables', function (Blueprint $table) {
            // Drop foreign key first
            if (Schema::hasColumn('tables', 'assigned_waiter_id')) {
                $table->dropForeign(['assigned_waiter_id']);
                $table->dropColumn('assigned_waiter_id');
            }
            
            // Drop other columns
            $columnsToRemove = ['current_party_size', 'occupied_at', 'table_type'];
            foreach ($columnsToRemove as $column) {
                if (Schema::hasColumn('tables', $column)) {
                    $table->dropColumn($column);
                }
            }
        });
    }
};
