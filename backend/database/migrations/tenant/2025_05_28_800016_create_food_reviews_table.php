<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('food_reviews', function (Blueprint $table) {
            $table->id();
            $table->foreignId('branch_id')->constrained()->onDelete('cascade');
            $table->foreignId('menu_item_id')->constrained()->onDelete('cascade');
            $table->foreignId('customer_id')->nullable()->constrained()->onDelete('set null');
            $table->foreignId('order_id')->nullable()->constrained()->onDelete('set null');

            // Review details
            $table->integer('rating'); // 1-5 stars
            $table->string('title')->nullable();
            $table->text('review')->nullable();
            $table->json('images')->nullable(); // Array of review images

            // Detailed ratings
            $table->integer('taste_rating')->nullable(); // 1-5
            $table->integer('presentation_rating')->nullable(); // 1-5
            $table->integer('value_rating')->nullable(); // 1-5
            $table->integer('portion_rating')->nullable(); // 1-5

            // Review metadata
            $table->string('reviewer_name')->nullable(); // For guest reviews
            $table->boolean('is_verified_purchase')->default(false);
            $table->boolean('is_anonymous')->default(false);
            $table->enum('status', ['pending', 'approved', 'rejected', 'hidden'])->default('pending');

            // Moderation
            $table->foreignId('moderated_by')->nullable()->constrained('employees')->onDelete('set null');
            $table->timestamp('moderated_at')->nullable();
            $table->text('moderation_notes')->nullable();

            // Engagement
            $table->integer('helpful_count')->default(0);
            $table->integer('not_helpful_count')->default(0);
            $table->boolean('featured')->default(false);

            // Response from restaurant
            $table->text('restaurant_response')->nullable();
            $table->timestamp('responded_at')->nullable();
            $table->foreignId('responded_by')->nullable()->constrained('employees')->onDelete('set null');

            $table->timestamps();

            $table->index(['branch_id', 'menu_item_id', 'status']);
            $table->index(['branch_id', 'customer_id']);
            $table->index(['branch_id', 'rating']);
            $table->index(['menu_item_id', 'status', 'rating']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('food_reviews');
    }
};
