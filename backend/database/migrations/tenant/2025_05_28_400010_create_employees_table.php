<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('employees', function (Blueprint $table) {
            $table->id();
            $table->foreignId('department_id')->nullable()->constrained()->onDelete('set null');
            $table->foreignId('primary_branch_id')->nullable()->constrained('branches')->onDelete('set null');
            $table->integer('experience_years')->default(0);
            $table->unsignedBigInteger('user_id')->nullable(); // References central users table

            $table->string('employee_id')->unique();

            // Personal Information
            $table->string('first_name');
            $table->string('last_name');
            $table->string('email')->unique();
            $table->string('password'); // For authentication
            $table->string('username')->unique();
            $table->string('phone')->nullable();
            $table->string('mobile')->nullable();
            $table->date('date_of_birth')->nullable();
            $table->enum('gender', ['male', 'female', 'other', 'prefer_not_to_say'])->nullable();

            // Address Information
            $table->text('address')->nullable();
            $table->string('emergency_contact')->nullable();
            $table->string('nid')->nullable(); // National ID
            $table->enum('blood_group', ['A+', 'A-', 'B+', 'B-', 'AB+', 'AB-', 'O+', 'O-'])->nullable();
            $table->string('city')->nullable();
            $table->string('state')->nullable();
            $table->string('country')->nullable();
            $table->string('postal_code')->nullable();

            // Emergency Contact
            $table->string('emergency_contact_name')->nullable();
            $table->string('emergency_contact_phone')->nullable();
            $table->string('emergency_contact_relationship')->nullable();

            // Employment Information
            $table->string('position');
            $table->enum('employment_type', ['full_time', 'part_time', 'contract', 'temporary', 'intern'])->default('full_time');
            $table->date('hire_date');
            $table->date('termination_date')->nullable();

            // Compensation
            $table->decimal('salary', 12, 2)->nullable(); // Annual salary
            $table->decimal('hourly_rate', 8, 2)->nullable(); // Hourly rate

            // Status
            $table->enum('status', ['active', 'inactive', 'terminated', 'on_leave'])->default('active');
            $table->boolean('is_active')->default(true);
            $table->string('name')->nullable(); // Combined name field for compatibility
            $table->enum('role', ['waiter', 'chef', 'rider', 'manager'])->default('waiter');
            $table->enum('employment_status', ['active', 'inactive', 'terminated', 'resigned'])->default('active');
            $table->text('notes')->nullable();

            // Additional fields
            $table->json('skills')->nullable(); // Array of skills
            $table->json('certifications')->nullable(); // Array of certifications

            // Sensitive Information (encrypted)
            $table->string('tax_id')->nullable();
            $table->string('bank_account_number')->nullable();
            $table->string('bank_routing_number')->nullable();
            $table->string('bank_name')->nullable();
            
            $table->timestamps();

            // Indexes
            $table->index(['primary_branch_id', 'status']);
            $table->index(['employment_status']);
            $table->index(['department_id', 'status']);
            $table->index('employee_id');
            $table->index(['first_name', 'last_name']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('employees');
    }
};
