<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('orders', function (Blueprint $table) {
            $existingColumns = Schema::getColumnListing('orders');
            
            // Add grand_total if it doesn't exist
            if (!in_array('grand_total', $existingColumns)) {
                $table->decimal('grand_total', 10, 2)->default(0)->after('subtotal');
            }
            
            // Add loyalty_points if it doesn't exist
            if (!in_array('loyalty_points', $existingColumns)) {
                $table->integer('loyalty_points')->default(0)->after('grand_total');
            }
            
            // Add discount if it doesn't exist
            if (!in_array('discount', $existingColumns)) {
                $table->decimal('discount', 10, 2)->default(0)->after('loyalty_points');
            }
            
            // Add points_applied if it doesn't exist
            if (!in_array('points_applied', $existingColumns)) {
                $table->integer('points_applied')->default(0)->after('discount');
            }
            
            // Add waiter_id if it doesn't exist
            if (!in_array('waiter_id', $existingColumns)) {
                $table->foreignId('waiter_id')->nullable()->constrained('users')->onDelete('set null')->after('customer_id');
            }
            
            // Add chef_id if it doesn't exist
            if (!in_array('chef_id', $existingColumns)) {
                $table->foreignId('chef_id')->nullable()->constrained('users')->onDelete('set null')->after('waiter_id');
            }
            
            // Add delivery_location if it doesn't exist
            if (!in_array('delivery_location', $existingColumns)) {
                $table->text('delivery_location')->nullable()->after('delivery_address');
            }
            
            // Add notes if it doesn't exist (rename special_instructions if it exists)
            if (!in_array('notes', $existingColumns)) {
                if (in_array('special_instructions', $existingColumns)) {
                    $table->renameColumn('special_instructions', 'notes');
                } else {
                    $table->text('notes')->nullable()->after('chef_id');
                }
            }
            
            // Ensure phone column exists
            if (!in_array('phone', $existingColumns)) {
                if (in_array('customer_phone', $existingColumns)) {
                    $table->renameColumn('customer_phone', 'phone');
                } else {
                    $table->string('phone')->nullable()->after('delivery_location');
                }
            }
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('orders', function (Blueprint $table) {
            $columns = [
                'grand_total', 'loyalty_points', 'discount', 'points_applied',
                'waiter_id', 'chef_id', 'delivery_location'
            ];
            
            foreach ($columns as $column) {
                if (Schema::hasColumn('orders', $column)) {
                    $table->dropColumn($column);
                }
            }
        });
    }
};
