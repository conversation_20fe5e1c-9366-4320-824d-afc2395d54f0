<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('expenses', function (Blueprint $table) {
            $table->id();
            $table->foreignId('restaurant_id')->constrained()->onDelete('cascade');
            $table->foreignId('expense_category_id')->constrained()->onDelete('restrict');
            $table->foreignId('vendor_id')->nullable()->constrained()->onDelete('set null');
            $table->unsignedBigInteger('created_by')->nullable(); // References central users table
            $table->unsignedBigInteger('approved_by')->nullable(); // References central users table
            
            $table->string('expense_number')->unique();
            $table->string('title');
            $table->text('description')->nullable();
            
            // Amount details
            $table->decimal('amount', 12, 2);
            $table->decimal('tax_amount', 12, 2)->default(0);
            $table->decimal('total_amount', 12, 2);
            
            // Dates
            $table->date('expense_date');
            $table->date('due_date')->nullable();
            $table->timestamp('paid_at')->nullable();
            
            // Payment information
            $table->enum('payment_method', ['cash', 'check', 'bank_transfer', 'credit_card', 'debit_card', 'online', 'other'])->nullable();
            $table->string('payment_reference')->nullable();
            
            // Status and workflow
            $table->enum('status', ['pending', 'approved', 'rejected', 'paid', 'cancelled'])->default('pending');
            $table->enum('priority', ['low', 'medium', 'high', 'urgent'])->default('medium');
            
            // Recurring expenses
            $table->boolean('is_recurring')->default(false);
            $table->enum('recurring_frequency', ['daily', 'weekly', 'monthly', 'quarterly', 'yearly'])->nullable();
            $table->date('recurring_until')->nullable();
            
            // Document references
            $table->string('invoice_number')->nullable();
            $table->string('receipt_number')->nullable();
            
            $table->text('notes')->nullable();
            $table->json('tags')->nullable();
            
            $table->timestamps();

            $table->index(['restaurant_id', 'status']);
            $table->index(['restaurant_id', 'expense_category_id']);
            $table->index(['restaurant_id', 'vendor_id']);
            $table->index(['expense_date', 'status']);
            $table->index(['due_date', 'status']);
            $table->index('expense_number');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('expenses');
    }
};
