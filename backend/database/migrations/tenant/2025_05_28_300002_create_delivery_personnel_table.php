<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('delivery_personnel', function (Blueprint $table) {
            $table->id();
            $table->foreignId('restaurant_id')->constrained()->onDelete('cascade');
            $table->unsignedBigInteger('user_id')->nullable(); // References central users table
            $table->string('employee_id')->unique();
            $table->string('first_name');
            $table->string('last_name');
            $table->string('phone');
            $table->string('email')->nullable();
            $table->enum('vehicle_type', ['bicycle', 'motorcycle', 'car', 'van'])->default('motorcycle');
            $table->string('vehicle_number')->nullable();
            $table->string('license_number')->nullable();

            // Location tracking
            $table->decimal('current_latitude', 10, 8)->nullable();
            $table->decimal('current_longitude', 11, 8)->nullable();
            $table->timestamp('last_location_update')->nullable();

            // Status and availability
            $table->enum('status', ['available', 'busy', 'on_delivery', 'offline'])->default('available');
            $table->boolean('is_active')->default(true);

            // Compensation
            $table->decimal('hourly_rate', 8, 2)->nullable();
            $table->decimal('commission_rate', 5, 2)->default(0); // Percentage

            // Performance settings
            $table->integer('max_concurrent_orders')->default(3);
            $table->decimal('delivery_radius', 8, 2)->default(10); // in kilometers

            // Performance metrics
            $table->decimal('rating', 3, 2)->default(5.00);
            $table->integer('total_deliveries')->default(0);
            $table->integer('on_time_deliveries')->default(0);

            // Shift information
            $table->time('shift_start_time')->nullable();
            $table->time('shift_end_time')->nullable();

            // Emergency contact
            $table->string('emergency_contact_name')->nullable();
            $table->string('emergency_contact_phone')->nullable();

            $table->timestamps();

            $table->index(['restaurant_id', 'status']);
            $table->index(['restaurant_id', 'is_active']);
            $table->index(['current_latitude', 'current_longitude']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('delivery_personnel');
    }
};
