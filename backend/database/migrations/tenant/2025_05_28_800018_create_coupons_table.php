<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('coupons', function (Blueprint $table) {
            $table->id();
            $table->foreignId('restaurant_id')->constrained()->onDelete('cascade');

            // Coupon identification
            $table->string('code')->unique();
            $table->string('name');
            $table->text('description')->nullable();

            // Discount details
            $table->enum('type', ['percentage', 'fixed_amount', 'free_delivery', 'buy_x_get_y']);
            $table->decimal('value', 8, 2); // Percentage or fixed amount
            $table->decimal('maximum_discount', 8, 2)->nullable(); // Max discount for percentage coupons

            // Usage restrictions
            $table->decimal('minimum_order_amount', 8, 2)->default(0);
            $table->decimal('maximum_order_amount', 8, 2)->nullable();
            $table->integer('usage_limit')->nullable(); // Total usage limit
            $table->integer('usage_limit_per_customer')->nullable();
            $table->integer('used_count')->default(0);

            // Validity period
            $table->datetime('valid_from');
            $table->datetime('valid_until');

            // Applicable conditions
            $table->json('applicable_order_types')->nullable(); // dine_in, takeaway, delivery
            $table->json('applicable_days')->nullable(); // [1,2,3,4,5,6,7] for Mon-Sun
            $table->time('applicable_from')->nullable(); // Time of day
            $table->time('applicable_until')->nullable();

            // Target audience
            $table->enum('customer_eligibility', ['all', 'new_customers', 'existing_customers', 'specific_customers']);
            $table->json('eligible_customer_ids')->nullable(); // For specific customers
            $table->json('eligible_customer_tiers')->nullable(); // bronze, silver, gold, platinum

            // Product restrictions
            $table->json('applicable_food_ids')->nullable(); // Specific foods
            $table->json('applicable_category_ids')->nullable(); // Specific categories
            $table->json('excluded_food_ids')->nullable(); // Excluded foods
            $table->json('excluded_category_ids')->nullable(); // Excluded categories

            // Buy X Get Y specific fields
            $table->integer('buy_quantity')->nullable();
            $table->integer('get_quantity')->nullable();
            $table->foreignId('buy_menu_item_id')->nullable()->constrained('menu_items')->onDelete('set null');
            $table->foreignId('get_menu_item_id')->nullable()->constrained('menu_items')->onDelete('set null');

            // Status and settings
            $table->boolean('is_active')->default(true);
            $table->boolean('is_stackable')->default(false); // Can be combined with other coupons
            $table->boolean('auto_apply')->default(false); // Automatically apply if conditions met

            // Analytics
            $table->decimal('total_discount_given', 10, 2)->default(0);
            $table->decimal('total_revenue_generated', 10, 2)->default(0);

            $table->timestamps();

            $table->index(['restaurant_id', 'is_active']);
            $table->index(['restaurant_id', 'valid_from', 'valid_until']);
            $table->index(['code']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('coupons');
    }
};
