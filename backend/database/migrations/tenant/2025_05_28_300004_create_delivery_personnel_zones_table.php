<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('delivery_personnel_zones', function (Blueprint $table) {
            $table->id();
            $table->foreignId('delivery_personnel_id')->constrained('delivery_personnel')->onDelete('cascade');
            $table->foreignId('delivery_zone_id')->constrained()->onDelete('cascade');
            $table->timestamps();

            $table->unique(['delivery_personnel_id', 'delivery_zone_id'], 'personnel_zone_unique');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('delivery_personnel_zones');
    }
};
