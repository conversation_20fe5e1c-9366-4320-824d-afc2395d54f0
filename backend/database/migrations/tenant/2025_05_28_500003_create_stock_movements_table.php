<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('stock_movements', function (Blueprint $table) {
            $table->id();
            $table->foreignId('restaurant_id')->constrained()->onDelete('cascade');
            $table->foreignId('inventory_item_id')->constrained()->onDelete('cascade');
            
            $table->enum('movement_type', ['in', 'out', 'adjustment', 'transfer', 'waste']);
            $table->decimal('quantity', 10, 2);
            $table->decimal('unit_cost', 10, 4);
            $table->decimal('total_cost', 12, 2);
            
            $table->enum('reason', [
                'purchase', 'usage', 'sale', 'waste', 'expired', 'damaged', 'theft',
                'adjustment', 'transfer_in', 'transfer_out', 'return', 'sample', 'promotion'
            ]);
            
            // Reference to related record (polymorphic)
            $table->string('reference_type')->nullable();
            $table->unsignedBigInteger('reference_id')->nullable();
            
            $table->string('batch_number')->nullable();
            $table->unsignedBigInteger('performed_by')->nullable(); // References central users table
            $table->text('notes')->nullable();
            $table->timestamp('movement_date');
            $table->timestamps();

            $table->index(['restaurant_id', 'inventory_item_id']);
            $table->index(['restaurant_id', 'movement_type']);
            $table->index(['restaurant_id', 'reason']);
            $table->index(['reference_type', 'reference_id']);
            $table->index('movement_date');
            $table->index('batch_number');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('stock_movements');
    }
};
