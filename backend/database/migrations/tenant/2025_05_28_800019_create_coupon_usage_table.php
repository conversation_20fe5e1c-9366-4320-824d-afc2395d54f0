<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('coupon_usage', function (Blueprint $table) {
            $table->id();
            $table->foreignId('restaurant_id')->constrained()->onDelete('cascade');
            $table->foreignId('coupon_id')->constrained()->onDelete('cascade');
            $table->foreignId('order_id')->constrained()->onDelete('cascade');
            $table->foreignId('customer_id')->nullable()->constrained()->onDelete('set null');
            
            // Usage details
            $table->decimal('discount_amount', 8, 2);
            $table->decimal('order_amount_before_discount', 10, 2);
            $table->decimal('order_amount_after_discount', 10, 2);
            
            $table->timestamps();

            $table->index(['coupon_id', 'customer_id']);
            $table->index(['restaurant_id', 'created_at']);
            $table->unique(['order_id']); // One coupon per order
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('coupon_usage');
    }
};
