<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('employee_shifts', function (Blueprint $table) {
            $table->id();
            $table->foreignId('branch_id')->constrained()->onDelete('cascade');
            $table->foreignId('employee_id')->constrained()->onDelete('cascade');
            $table->date('shift_date');
            $table->time('start_time');
            $table->time('end_time');
            $table->enum('shift_type', ['morning', 'afternoon', 'evening', 'night', 'full_day', 'custom'])->default('custom');
            $table->enum('status', ['scheduled', 'confirmed', 'started', 'completed', 'cancelled', 'no_show'])->default('scheduled');
            $table->time('actual_start_time')->nullable();
            $table->time('actual_end_time')->nullable();
            $table->text('notes')->nullable();
            $table->decimal('break_duration', 4, 2)->default(0); // Hours
            $table->decimal('overtime_hours', 4, 2)->default(0);
            $table->boolean('is_holiday')->default(false);
            $table->decimal('hourly_rate', 8, 2)->nullable();
            $table->foreignId('created_by')->nullable()->constrained('employees')->onDelete('set null');
            $table->timestamps();

            $table->unique(['employee_id', 'shift_date', 'start_time']);
            $table->index(['branch_id', 'shift_date']);
            $table->index(['employee_id', 'shift_date']);
            $table->index(['shift_date', 'status']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('employee_shifts');
    }
};
