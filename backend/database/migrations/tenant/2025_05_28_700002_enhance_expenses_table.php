<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Create expense_categories table if it doesn't exist
        if (!Schema::hasTable('expense_categories')) {
            Schema::create('expense_categories', function (Blueprint $table) {
                $table->id();
                $table->foreignId('branch_id')->constrained('branches')->onDelete('cascade');
                $table->string('name');
                $table->string('slug')->unique();
                $table->text('description')->nullable();
                $table->string('color_code')->default('#6B7280');
                $table->string('icon')->nullable();
                $table->boolean('is_active')->default(true);
                $table->integer('sort_order')->default(0);
                $table->timestamps();
                
                $table->index(['branch_id', 'is_active']);
                $table->index('slug');
            });
        }

        // Enhance expenses table
        if (!Schema::hasTable('expenses')) {
            Schema::create('expenses', function (Blueprint $table) {
                $table->id();
                $table->foreignId('branch_id')->constrained('branches')->onDelete('cascade');
                $table->foreignId('expense_category_id')->nullable()->constrained('expense_categories')->onDelete('set null');
                $table->foreignId('supplier_id')->nullable()->constrained('vendors')->onDelete('set null');
                $table->foreignId('created_by')->constrained('users')->onDelete('cascade');
                $table->foreignId('approved_by')->nullable()->constrained('users')->onDelete('set null');
                $table->string('expense_number')->unique();
                $table->string('title');
                $table->text('description')->nullable();
                $table->decimal('amount', 12, 2);
                $table->decimal('tax_amount', 12, 2)->default(0);
                $table->decimal('total_amount', 12, 2);
                $table->date('expense_date');
                $table->date('due_date')->nullable();
                $table->timestamp('paid_at')->nullable();
                $table->string('payment_method')->nullable();
                $table->string('payment_reference')->nullable();
                $table->enum('status', ['pending', 'approved', 'rejected', 'paid'])->default('pending');
                $table->enum('expense_type', [
                    'food_beverage', 'equipment', 'utilities', 'marketing', 
                    'staff', 'rent', 'maintenance', 'other'
                ])->default('other');
                $table->enum('priority', ['low', 'medium', 'high', 'urgent'])->default('medium');
                $table->string('receipt_image')->nullable();
                $table->string('invoice_number')->nullable();
                $table->text('notes')->nullable();
                $table->json('tags')->nullable();
                $table->boolean('is_recurring')->default(false);
                $table->enum('recurring_frequency', ['weekly', 'monthly', 'quarterly', 'yearly'])->nullable();
                $table->date('recurring_until')->nullable();
                $table->timestamps();
                
                $table->index(['branch_id', 'status']);
                $table->index(['branch_id', 'expense_date']);
                $table->index(['expense_type', 'status']);
                $table->index(['supplier_id', 'status']);
                $table->index('expense_number');
            });
        } else {
            // Add missing columns to existing expenses table
            Schema::table('expenses', function (Blueprint $table) {
                if (!Schema::hasColumn('expenses', 'expense_type')) {
                    $table->enum('expense_type', [
                        'food_beverage', 'equipment', 'utilities', 'marketing', 
                        'staff', 'rent', 'maintenance', 'other'
                    ])->default('other')->after('status');
                }
                
                if (!Schema::hasColumn('expenses', 'supplier_id')) {
                    $table->foreignId('supplier_id')->nullable()->after('expense_category_id')->constrained('vendors')->onDelete('set null');
                }
                
                if (!Schema::hasColumn('expenses', 'receipt_image')) {
                    $table->string('receipt_image')->nullable()->after('priority');
                }
                
                if (!Schema::hasColumn('expenses', 'approved_by')) {
                    $table->foreignId('approved_by')->nullable()->after('created_by')->constrained('users')->onDelete('set null');
                }
            });
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('expenses');
        Schema::dropIfExists('expense_categories');
    }
};
