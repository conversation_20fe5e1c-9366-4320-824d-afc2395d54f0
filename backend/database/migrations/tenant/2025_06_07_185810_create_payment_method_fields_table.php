<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('payment_method_fields', function (Blueprint $table) {
            $table->id();
            $table->foreignId('payment_method_id')->constrained()->onDelete('cascade');
            $table->string('field_name');
            $table->enum('field_type', ['text', 'number', 'select', 'textarea', 'checkbox', 'email', 'tel', 'date']);
            $table->string('field_label');
            $table->boolean('is_required')->default(false);
            $table->text('default_value')->nullable();
            $table->json('validation_rules')->nullable();
            $table->json('select_options')->nullable(); // For select field type
            $table->integer('display_order')->default(0);
            $table->string('placeholder')->nullable();
            $table->text('help_text')->nullable();
            $table->timestamps();

            $table->index(['payment_method_id', 'display_order']);
            $table->unique(['payment_method_id', 'field_name']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('payment_method_fields');
    }
};
