<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('combo_components', function (Blueprint $table) {
            $table->id();
            $table->foreignId('combo_menu_id')->constrained()->onDelete('cascade');
            $table->enum('component_type', ['main', 'side', 'drink', 'dessert']); // Type of component
            $table->foreignId('menu_item_id')->constrained()->onDelete('cascade');
            $table->boolean('is_required')->default(false); // Whether this component is required
            $table->integer('max_selections')->default(1); // Maximum selections allowed for this component type
            $table->integer('sort_order')->default(0);
            $table->timestamps();

            $table->index(['combo_menu_id', 'component_type']);
            $table->index(['combo_menu_id', 'sort_order']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('combo_components');
    }
};
