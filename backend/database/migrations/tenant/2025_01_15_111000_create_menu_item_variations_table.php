<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('menu_item_variations', function (Blueprint $table) {
            $table->id();
            $table->foreignId('menu_item_id')->constrained()->onDelete('cascade');
            $table->string('name'); // e.g., "6 inch", "12 inch", "Single Patty"
            $table->string('name_bn')->nullable(); // Bengali translation
            $table->decimal('price_modifier', 10, 2)->default(0); // Can be positive, negative, or zero
            $table->boolean('is_available')->default(true);
            $table->integer('sort_order')->default(0);
            $table->text('description')->nullable();
            $table->text('description_bn')->nullable();
            $table->timestamps();

            $table->index(['menu_item_id', 'is_available']);
            $table->index(['menu_item_id', 'sort_order']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('menu_item_variations');
    }
};
