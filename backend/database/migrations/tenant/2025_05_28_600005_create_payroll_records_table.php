<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('payroll_records', function (Blueprint $table) {
            $table->id();
            $table->foreignId('restaurant_id')->constrained()->onDelete('cascade');
            $table->foreignId('employee_id')->constrained()->onDelete('cascade');
            
            $table->date('pay_period_start');
            $table->date('pay_period_end');
            
            // Hours
            $table->decimal('regular_hours', 8, 2)->default(0);
            $table->decimal('overtime_hours', 8, 2)->default(0);
            
            // Pay amounts
            $table->decimal('regular_pay', 10, 2)->default(0);
            $table->decimal('overtime_pay', 10, 2)->default(0);
            $table->decimal('gross_pay', 10, 2)->default(0);
            
            // Deductions
            $table->decimal('tax_deductions', 10, 2)->default(0);
            $table->decimal('other_deductions', 10, 2)->default(0);
            $table->decimal('net_pay', 10, 2)->default(0);
            
            $table->enum('status', ['draft', 'processed', 'paid', 'cancelled'])->default('draft');
            
            $table->unsignedBigInteger('processed_by')->nullable(); // References central users table
            $table->timestamp('processed_at')->nullable();
            $table->text('notes')->nullable();
            
            $table->timestamps();

            $table->index(['restaurant_id', 'employee_id']);
            $table->index(['pay_period_start', 'pay_period_end']);
            $table->index(['status', 'processed_at']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('payroll_records');
    }
};
