<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('rider_order_history', function (Blueprint $table) {
            $table->id();
            $table->foreignId('rider_id')->constrained()->onDelete('cascade');
            $table->foreignId('order_id')->constrained()->onDelete('cascade');
            $table->enum('action', ['assigned', 'accepted', 'picked_up', 'en_route', 'delivered', 'cancelled', 'failed']);
            $table->timestamp('action_time');
            $table->decimal('latitude', 10, 8)->nullable();
            $table->decimal('longitude', 11, 8)->nullable();
            $table->text('notes')->nullable();
            $table->json('metadata')->nullable(); // Additional data like distance, weather, etc.
            $table->timestamps();
            
            // Indexes
            $table->index(['rider_id', 'action_time']);
            $table->index(['order_id', 'action']);
            $table->index(['action', 'action_time']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('rider_order_history');
    }
};
