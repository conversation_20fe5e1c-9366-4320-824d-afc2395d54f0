<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('employees', function (Blueprint $table) {
            // Check if columns don't exist before adding them
            if (!Schema::hasColumn('employees', 'role')) {
                $table->enum('role', ['waiter', 'chef', 'rider', 'manager'])->default('waiter')->after('position');
            }

            if (!Schema::hasColumn('employees', 'email')) {
                $table->string('email')->unique()->after('last_name');
            }

            if (!Schema::hasColumn('employees', 'password')) {
                $table->string('password')->after('email');
            }

            if (!Schema::hasColumn('employees', 'first_name')) {
                $table->string('first_name')->after('id');
            }

            if (!Schema::hasColumn('employees', 'last_name')) {
                $table->string('last_name')->after('first_name');
            }

            // Remove restaurant_id if it exists (tenant-agnostic design)
            if (Schema::hasColumn('employees', 'restaurant_id')) {
                $table->dropForeign(['restaurant_id']);
                $table->dropColumn('restaurant_id');
            }

            // Add indexes for new columns
            $table->index('role');
            $table->index(['role', 'is_active']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('employees', function (Blueprint $table) {
            // Remove the columns we added
            if (Schema::hasColumn('employees', 'role')) {
                $table->dropIndex(['role']);
                $table->dropIndex(['role', 'is_active']);
                $table->dropColumn('role');
            }

            // Note: We don't remove email, password, first_name, last_name
            // as they might be needed by other parts of the system
        });
    }
};
