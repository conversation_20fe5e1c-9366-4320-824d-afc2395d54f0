<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('riders', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('user_id')->nullable(); // References central users table
            $table->string('employee_id')->unique();
            $table->string('name');
            $table->string('phone')->unique();
            $table->string('email')->nullable();
            $table->enum('vehicle_type', ['bike', 'car', 'scooter', 'bicycle'])->default('bike');
            $table->string('license_number')->nullable();
            $table->enum('status', ['active', 'inactive', 'suspended'])->default('active');
            $table->date('hire_date');
            $table->text('address')->nullable();
            $table->string('profile_photo')->nullable();
            
            // Delivery status
            $table->enum('delivery_status', ['available', 'busy', 'offline', 'on_break'])->default('offline');
            $table->integer('current_delivery_count')->default(0);
            $table->integer('max_concurrent_deliveries')->default(3);
            
            // Location tracking
            $table->decimal('current_latitude', 10, 8)->nullable();
            $table->decimal('current_longitude', 11, 8)->nullable();
            $table->timestamp('last_location_update')->nullable();
            
            // Performance metrics
            $table->integer('total_deliveries')->default(0);
            $table->decimal('average_rating', 3, 2)->default(5.0);
            $table->integer('rating_count')->default(0);
            $table->decimal('average_delivery_time', 8, 2)->default(0); // minutes
            $table->integer('completed_orders_today')->default(0);
            $table->decimal('total_earnings', 10, 2)->default(0);
            $table->decimal('commission_rate', 5, 2)->default(10.0); // percentage
            
            // Working hours
            $table->time('shift_start_time')->nullable();
            $table->time('shift_end_time')->nullable();
            $table->json('working_days')->nullable(); // Array of days [1,2,3,4,5,6,7]
            
            // Emergency contact
            $table->string('emergency_contact_name')->nullable();
            $table->string('emergency_contact_phone')->nullable();
            
            // Additional info
            $table->text('notes')->nullable();
            $table->boolean('is_verified')->default(false);
            $table->timestamp('last_active_at')->nullable();
            
            $table->timestamps();
            
            // Indexes
            $table->index(['status', 'delivery_status']);
            $table->index(['phone']);
            $table->index(['delivery_status', 'current_delivery_count']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('riders');
    }
};
