<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('loyalty_accounts', function (Blueprint $table) {
            $table->id();
            $table->string('phone_number')->unique(); // Primary identifier
            $table->string('customer_name');
            $table->text('address')->nullable();
            $table->string('email')->nullable();
            $table->date('date_of_birth')->nullable();
            $table->integer('total_points')->default(0);
            $table->integer('lifetime_points_earned')->default(0); // Total points ever earned
            $table->integer('lifetime_points_redeemed')->default(0); // Total points ever redeemed
            $table->enum('tier', ['regular', 'bronze', 'silver', 'gold', 'platinum'])->default('regular');
            $table->decimal('total_spent', 12, 2)->default(0.00);
            $table->integer('total_orders')->default(0);
            $table->timestamp('last_transaction_at')->nullable();
            $table->timestamp('last_order_at')->nullable();
            $table->boolean('is_active')->default(true);
            $table->text('notes')->nullable();
            $table->timestamps();

            // Indexes for performance
            $table->index(['phone_number']);
            $table->index(['tier', 'is_active']);
            $table->index(['total_points']);
            $table->index(['last_transaction_at']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('loyalty_accounts');
    }
};
