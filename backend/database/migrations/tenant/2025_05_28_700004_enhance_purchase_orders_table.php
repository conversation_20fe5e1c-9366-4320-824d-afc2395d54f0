<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Enhance purchase_orders table
        if (Schema::hasTable('purchase_orders')) {
            Schema::table('purchase_orders', function (Blueprint $table) {
                // Add missing columns for enhanced functionality
                if (!Schema::hasColumn('purchase_orders', 'delivery_tracking_number')) {
                    $table->string('delivery_tracking_number')->nullable()->after('actual_delivery_date');
                }
                
                if (!Schema::hasColumn('purchase_orders', 'quality_inspection_notes')) {
                    $table->text('quality_inspection_notes')->nullable()->after('delivery_tracking_number');
                }
                
                if (!Schema::hasColumn('purchase_orders', 'quality_rating')) {
                    $table->integer('quality_rating')->nullable()->after('quality_inspection_notes'); // 1-5 stars
                }
                
                if (!Schema::hasColumn('purchase_orders', 'delivery_rating')) {
                    $table->integer('delivery_rating')->nullable()->after('quality_rating'); // 1-5 stars
                }
                
                if (!Schema::hasColumn('purchase_orders', 'is_template')) {
                    $table->boolean('is_template')->default(false)->after('delivery_rating');
                }
                
                if (!Schema::hasColumn('purchase_orders', 'template_name')) {
                    $table->string('template_name')->nullable()->after('is_template');
                }
                
                if (!Schema::hasColumn('purchase_orders', 'recurring_frequency')) {
                    $table->enum('recurring_frequency', ['weekly', 'monthly', 'quarterly'])->nullable()->after('template_name');
                }
                
                if (!Schema::hasColumn('purchase_orders', 'next_order_date')) {
                    $table->date('next_order_date')->nullable()->after('recurring_frequency');
                }
                
                // Update status enum to include new statuses
                if (Schema::hasColumn('purchase_orders', 'status')) {
                    $table->dropColumn('status');
                }
                $table->enum('status', [
                    'draft', 'sent', 'confirmed', 'delivered', 'invoiced', 'paid', 'cancelled'
                ])->default('draft')->after('vendor_id');
            });
        }

        // Create purchase order templates table
        if (!Schema::hasTable('purchase_order_templates')) {
            Schema::create('purchase_order_templates', function (Blueprint $table) {
                $table->id();
                $table->foreignId('branch_id')->constrained('branches')->onDelete('cascade');
                $table->foreignId('vendor_id')->constrained('vendors')->onDelete('cascade');
                // $table->foreignId('created_by')->constrained('users')->onDelete('cascade');
                $table->string('name');
                $table->text('description')->nullable();
                $table->enum('frequency', ['weekly', 'monthly', 'quarterly']);
                $table->json('items'); // Template items with quantities
                $table->decimal('estimated_total', 12, 2)->nullable();
                $table->boolean('is_active')->default(true);
                $table->date('last_used_date')->nullable();
                $table->integer('usage_count')->default(0);
                $table->timestamps();
                
                $table->index(['branch_id', 'is_active']);
                $table->index(['vendor_id', 'frequency']);
            });
        }

        // Create purchase order delivery tracking table
        if (!Schema::hasTable('purchase_order_deliveries')) {
            Schema::create('purchase_order_deliveries', function (Blueprint $table) {
                $table->id();
                $table->foreignId('purchase_order_id')->constrained('purchase_orders')->onDelete('cascade');
                $table->string('tracking_number')->nullable();
                $table->enum('delivery_status', [
                    'pending', 'shipped', 'in_transit', 'out_for_delivery', 'delivered', 'failed'
                ])->default('pending');
                $table->timestamp('shipped_at')->nullable();
                $table->timestamp('delivered_at')->nullable();
                $table->text('delivery_notes')->nullable();
                $table->string('received_by')->nullable();
                $table->json('delivery_photos')->nullable();
                $table->timestamps();
                
                $table->index(['purchase_order_id', 'delivery_status'], 'po_delivery_status_idx');
                $table->index('tracking_number', 'po_tracking_idx');
            });
        }

       
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('purchase_order_deliveries');
        Schema::dropIfExists('purchase_order_templates');
        
        if (Schema::hasTable('purchase_orders')) {
            Schema::table('purchase_orders', function (Blueprint $table) {
                $columns = [
                    'delivery_tracking_number', 'quality_inspection_notes', 'quality_rating',
                    'delivery_rating', 'is_template', 'template_name', 'recurring_frequency',
                    'next_order_date'
                ];
                
                foreach ($columns as $column) {
                    if (Schema::hasColumn('purchase_orders', $column)) {
                        $table->dropColumn($column);
                    }
                }
            });
        }
    }
};
