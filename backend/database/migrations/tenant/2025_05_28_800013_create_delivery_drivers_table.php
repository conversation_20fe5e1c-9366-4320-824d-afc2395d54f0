<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('delivery_drivers', function (Blueprint $table) {
            $table->id();
            $table->foreignId('restaurant_id')->constrained()->onDelete('cascade');
            $table->foreignId('employee_id')->constrained()->onDelete('cascade');
            
            // Driver details
            $table->string('license_number');
            $table->date('license_expiry');
            $table->string('vehicle_type'); // bike, scooter, car, etc.
            $table->string('vehicle_model')->nullable();
            $table->string('vehicle_plate_number');
            $table->string('insurance_number')->nullable();
            $table->date('insurance_expiry')->nullable();
            
            // Current status
            $table->enum('status', ['offline', 'available', 'busy', 'on_break'])->default('offline');
            $table->decimal('current_latitude', 10, 8)->nullable();
            $table->decimal('current_longitude', 11, 8)->nullable();
            $table->timestamp('last_location_update')->nullable();
            
            // Performance metrics
            $table->integer('total_deliveries')->default(0);
            $table->decimal('average_rating', 3, 2)->default(0);
            $table->integer('rating_count')->default(0);
            $table->decimal('average_delivery_time', 8, 2)->default(0); // minutes
            $table->integer('completed_orders_today')->default(0);
            
            // Availability
            $table->boolean('is_active')->default(true);
            $table->json('working_zones')->nullable(); // Array of delivery zone IDs
            
            $table->timestamps();

            $table->index(['restaurant_id', 'status']);
            $table->index(['restaurant_id', 'is_active']);
            $table->unique(['restaurant_id', 'employee_id']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('delivery_drivers');
    }
};
