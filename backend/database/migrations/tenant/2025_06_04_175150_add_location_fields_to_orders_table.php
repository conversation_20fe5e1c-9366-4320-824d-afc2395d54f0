<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('orders', function (Blueprint $table) {
            // Only add columns that don't exist
            if (!Schema::hasColumn('orders', 'delivery_formatted_address')) {
                $table->text('delivery_formatted_address')->nullable()->after('delivery_longitude');
            }
            if (!Schema::hasColumn('orders', 'location_enabled')) {
                $table->boolean('location_enabled')->default(false)->after('delivery_formatted_address');
            }
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('orders', function (Blueprint $table) {
            // Only drop columns that exist and were added by this migration
            if (Schema::hasColumn('orders', 'delivery_formatted_address')) {
                $table->dropColumn('delivery_formatted_address');
            }
            if (Schema::hasColumn('orders', 'location_enabled')) {
                $table->dropColumn('location_enabled');
            }
        });
    }
};
