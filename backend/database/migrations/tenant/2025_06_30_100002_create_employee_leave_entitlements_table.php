<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('employee_leave_entitlements', function (Blueprint $table) {
            $table->id();
            $table->foreignId('employee_id')->constrained()->onDelete('cascade');
            $table->foreignId('leave_type_id')->constrained()->onDelete('cascade');
            $table->year('year'); // Leave year
            $table->integer('entitled_days'); // Total days entitled for this year
            $table->integer('used_days')->default(0); // Days already used
            $table->integer('pending_days')->default(0); // Days in pending requests
            $table->integer('carried_forward_days')->default(0); // Days carried from previous year
            $table->date('expires_at')->nullable(); // When carried forward days expire
            $table->timestamps();

            $table->unique(['employee_id', 'leave_type_id', 'year'], 'emp_leave_entitlement_unique');
            $table->index(['employee_id', 'year']);
            $table->index(['leave_type_id', 'year']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('employee_leave_entitlements');
    }
};
