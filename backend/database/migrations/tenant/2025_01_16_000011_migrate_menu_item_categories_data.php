<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Migrate existing category_id data to the pivot table
        DB::statement('
            INSERT INTO menu_item_categories (menu_item_id, category_id, created_at, updated_at)
            SELECT id, category_id, created_at, updated_at
            FROM menu_items 
            WHERE category_id IS NOT NULL
        ');
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Clear the pivot table
        DB::table('menu_item_categories')->truncate();
    }
};
