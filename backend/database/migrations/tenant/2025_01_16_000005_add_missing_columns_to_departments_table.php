<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('departments', function (Blueprint $table) {
            if (!Schema::hasColumn('departments', 'color_code')) {
                $table->string('color_code', 7)->nullable()->after('description');
            }
            if (!Schema::hasColumn('departments', 'icon')) {
                $table->string('icon')->nullable()->after('color_code');
            }
            if (!Schema::hasColumn('departments', 'restaurant_id')) {
                $table->unsignedBigInteger('restaurant_id')->nullable()->after('id');
            }
            if (!Schema::hasColumn('departments', 'budget_limit')) {
                $table->decimal('budget_limit', 10, 2)->nullable()->after('icon');
            }
            if (!Schema::hasColumn('departments', 'max_employees')) {
                $table->integer('max_employees')->nullable()->after('budget_limit');
            }
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('departments', function (Blueprint $table) {
            $table->dropColumn(['color_code', 'icon', 'restaurant_id', 'budget_limit', 'max_employees']);
        });
    }
};
