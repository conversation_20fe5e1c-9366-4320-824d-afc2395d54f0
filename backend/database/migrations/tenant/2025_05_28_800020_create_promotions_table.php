<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('promotions', function (Blueprint $table) {
            $table->id();
            $table->foreignId('restaurant_id')->constrained()->onDelete('cascade');
            
            // Promotion details
            $table->string('name');
            $table->text('description')->nullable();
            $table->string('banner_image')->nullable();
            $table->string('thumbnail_image')->nullable();
            
            // Promotion type and rules
            $table->enum('type', [
                'happy_hour', 'daily_special', 'combo_deal', 'loyalty_reward',
                'seasonal_offer', 'flash_sale', 'group_discount', 'birthday_special'
            ]);
            
            // Discount configuration
            $table->enum('discount_type', ['percentage', 'fixed_amount', 'buy_x_get_y', 'free_item']);
            $table->decimal('discount_value', 8, 2)->nullable();
            $table->decimal('maximum_discount', 8, 2)->nullable();
            
            // Validity period
            $table->datetime('starts_at');
            $table->datetime('ends_at');
            
            // Timing restrictions
            $table->json('applicable_days')->nullable(); // [1,2,3,4,5,6,7] for Mon-Sun
            $table->time('applicable_from')->nullable();
            $table->time('applicable_until')->nullable();
            
            // Target products
            $table->json('applicable_food_ids')->nullable();
            $table->json('applicable_category_ids')->nullable();
            $table->json('combo_items')->nullable(); // For combo deals
            
            // Conditions
            $table->decimal('minimum_order_amount', 8, 2)->default(0);
            $table->integer('minimum_items')->default(1);
            $table->integer('maximum_uses')->nullable();
            $table->integer('maximum_uses_per_customer')->nullable();
            $table->integer('current_uses')->default(0);
            
            // Target audience
            $table->enum('target_audience', ['all', 'new_customers', 'loyal_customers', 'birthday_customers']);
            $table->json('eligible_customer_tiers')->nullable();
            
            // Display settings
            $table->boolean('is_featured')->default(false);
            $table->boolean('show_on_homepage')->default(false);
            $table->boolean('show_in_menu')->default(true);
            $table->integer('sort_order')->default(0);
            
            // Status
            $table->boolean('is_active')->default(true);
            $table->boolean('auto_activate')->default(false);
            $table->boolean('auto_deactivate')->default(true);
            
            // Analytics
            $table->integer('view_count')->default(0);
            $table->integer('click_count')->default(0);
            $table->integer('conversion_count')->default(0);
            $table->decimal('total_discount_given', 10, 2)->default(0);
            $table->decimal('total_revenue_generated', 10, 2)->default(0);
            
            $table->timestamps();

            $table->index(['restaurant_id', 'is_active']);
            $table->index(['restaurant_id', 'type']);
            $table->index(['restaurant_id', 'starts_at', 'ends_at']);
            $table->index(['restaurant_id', 'is_featured']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('promotions');
    }
};
