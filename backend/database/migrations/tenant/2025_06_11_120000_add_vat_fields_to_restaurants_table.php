<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('restaurants', function (Blueprint $table) {
            // VAT settings
            $table->boolean('vat_enabled')->default(false)->after('tax_rate');
            $table->decimal('vat_rate', 5, 2)->default(0)->after('vat_enabled');
            $table->string('vat_number')->nullable()->after('vat_rate');
            
            // Tax settings enhancement
            $table->boolean('tax_enabled')->default(false)->after('vat_number');
            $table->string('tax_name')->default('Tax')->after('tax_enabled');
            
            // Service charge enhancement
            $table->boolean('service_charge_enabled')->default(false)->after('service_charge');
            $table->string('service_charge_name')->default('Service Charge')->after('service_charge_enabled');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('restaurants', function (Blueprint $table) {
            $table->dropColumn([
                'vat_enabled',
                'vat_rate', 
                'vat_number',
                'tax_enabled',
                'tax_name',
                'service_charge_enabled',
                'service_charge_name'
            ]);
        });
    }
};
