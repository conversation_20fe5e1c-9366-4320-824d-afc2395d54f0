<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('order_payments', function (Blueprint $table) {
            $table->id();
            $table->foreignId('order_id')->constrained()->onDelete('cascade');
            $table->foreignId('branch_id')->constrained()->onDelete('cascade');
            $table->enum('payment_method', [
                'cash', 
                'card', 
                'digital_wallet', 
                'bank_transfer',
                'split_payment'
            ])->default('cash');
            $table->decimal('amount', 10, 2);
            $table->enum('status', [
                'pending', 
                'processing',
                'completed', 
                'failed', 
                'cancelled',
                'refunded',
                'partially_refunded'
            ])->default('pending');
            $table->string('transaction_reference')->nullable();
            $table->string('gateway_transaction_id')->nullable();
            $table->string('payment_gateway')->nullable(); // stripe, paypal, etc.
            $table->json('payment_details')->nullable(); // Store gateway-specific data
            $table->json('card_details')->nullable(); // Last 4 digits, type, etc.
            $table->decimal('refunded_amount', 10, 2)->default(0);
            $table->timestamp('processed_at')->nullable();
            $table->timestamp('refunded_at')->nullable();
            // $table->foreignId('processed_by')->nullable()->constrained('users')->onDelete('set null');
            // $table->foreignId('refunded_by')->nullable()->constrained('users')->onDelete('set null');
            $table->text('notes')->nullable();
            $table->text('failure_reason')->nullable();
            $table->timestamps();

            // Indexes for performance
            $table->index(['order_id', 'status']);
            $table->index(['branch_id', 'payment_method']);
            $table->index(['payment_method', 'status']);
            $table->index(['processed_at', 'status']);
            $table->index('gateway_transaction_id');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('order_payments');
    }
};
