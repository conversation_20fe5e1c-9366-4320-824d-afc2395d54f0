<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('leave_types', function (Blueprint $table) {
            $table->id();
            $table->string('name'); // Annual, Sick, Personal, Maternity, etc.
            $table->text('description')->nullable();
            $table->integer('default_days_per_year')->default(0); // Default entitlement
            $table->boolean('is_paid')->default(true);
            $table->boolean('requires_approval')->default(true);
            $table->boolean('requires_medical_certificate')->default(false);
            $table->integer('max_consecutive_days')->nullable(); // Max days in a row
            $table->integer('min_notice_days')->default(0); // Minimum notice required
            $table->integer('max_carry_forward_days')->default(0); // Days that can be carried to next year
            $table->boolean('is_active')->default(true);
            $table->string('color_code', 7)->default('#3B82F6'); // For calendar display
            $table->integer('sort_order')->default(0);
            $table->timestamps();

            $table->index(['is_active', 'sort_order']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('leave_types');
    }
};
