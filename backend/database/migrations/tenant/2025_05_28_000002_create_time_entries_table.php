<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('time_entries', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('user_id')->nullable(); // References central users table
            $table->foreignId('shift_id')->nullable()->constrained()->onDelete('set null');
            $table->timestamp('clock_in_time');
            $table->timestamp('clock_out_time')->nullable();
            $table->date('date');
            $table->decimal('total_hours', 5, 2)->nullable();
            $table->integer('break_duration')->default(0); // in minutes
            $table->text('clock_in_notes')->nullable();
            $table->text('clock_out_notes')->nullable();
            $table->string('clock_in_location')->nullable();
            $table->string('clock_out_location')->nullable();
            $table->text('admin_notes')->nullable();
            $table->boolean('is_late')->default(false);
            $table->boolean('is_early_departure')->default(false);
            $table->boolean('is_manual_entry')->default(false);
            $table->unsignedBigInteger('created_by')->nullable(); // References central users table
            $table->timestamps();

            $table->index(['user_id', 'date']);
            $table->index(['date']);
            $table->index(['clock_in_time']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('time_entries');
    }
};
