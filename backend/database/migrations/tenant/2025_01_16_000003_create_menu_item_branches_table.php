<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('menu_item_branches', function (Blueprint $table) {
            $table->id();
            $table->foreignId('menu_item_id')->constrained('menu_items')->onDelete('cascade');
            $table->foreignId('branch_id')->constrained('branches')->onDelete('cascade');
            $table->boolean('is_available')->default(true);
            $table->decimal('branch_specific_price', 10, 2)->nullable(); // Optional branch-specific pricing
            $table->timestamps();

            // Ensure unique combination of menu_item_id and branch_id
            $table->unique(['menu_item_id', 'branch_id']);
            
            // Indexes for performance
            $table->index(['menu_item_id', 'is_available']);
            $table->index(['branch_id', 'is_available']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('menu_item_branches');
    }
};
