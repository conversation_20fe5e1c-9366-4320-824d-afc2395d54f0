<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('payroll_records', function (Blueprint $table) {
            $table->id();
            $table->foreignId('employee_id')->constrained()->onDelete('cascade');
            $table->string('payroll_number')->unique(); // Auto-generated payroll reference
            $table->date('pay_period_start');
            $table->date('pay_period_end');
            $table->date('pay_date');
            $table->enum('pay_frequency', ['weekly', 'bi_weekly', 'monthly'])->default('monthly');
            
            // Basic salary information
            $table->decimal('base_salary', 10, 2)->default(0); // Monthly salary for salaried employees
            $table->decimal('hourly_rate', 8, 2)->default(0); // Hourly rate for hourly employees
            $table->decimal('regular_hours', 6, 2)->default(0); // Regular hours worked
            $table->decimal('overtime_hours', 6, 2)->default(0); // Overtime hours
            $table->decimal('overtime_rate_multiplier', 4, 2)->default(1.5); // 1.5x for overtime
            
            // Earnings breakdown
            $table->decimal('regular_pay', 10, 2)->default(0);
            $table->decimal('overtime_pay', 10, 2)->default(0);
            $table->decimal('bonus', 10, 2)->default(0);
            $table->decimal('commission', 10, 2)->default(0);
            $table->decimal('allowances', 10, 2)->default(0); // Transport, meal, etc.
            $table->decimal('gross_pay', 10, 2)->default(0); // Total before deductions
            
            // Deductions
            $table->decimal('tax_deduction', 10, 2)->default(0);
            $table->decimal('insurance_deduction', 10, 2)->default(0);
            $table->decimal('loan_deductions', 10, 2)->default(0);
            $table->decimal('advance_deductions', 10, 2)->default(0);
            $table->decimal('other_deductions', 10, 2)->default(0);
            $table->decimal('total_deductions', 10, 2)->default(0);
            
            // Final amounts
            $table->decimal('net_pay', 10, 2)->default(0); // Amount to be paid
            $table->enum('status', ['draft', 'approved', 'paid', 'cancelled'])->default('draft');
            $table->text('notes')->nullable();
            $table->json('earnings_breakdown')->nullable(); // Detailed breakdown
            $table->json('deductions_breakdown')->nullable(); // Detailed breakdown
            
            // Approval workflow
            $table->foreignId('approved_by')->nullable()->constrained('employees')->onDelete('set null');
            $table->timestamp('approved_at')->nullable();
            $table->foreignId('paid_by')->nullable()->constrained('employees')->onDelete('set null');
            $table->timestamp('paid_at')->nullable();
            
            $table->timestamps();

            $table->index(['employee_id', 'pay_period_start', 'pay_period_end'], 'payroll_emp_period_idx');
            $table->index(['pay_date', 'status']);
            $table->index(['status', 'created_at']);
            $table->index('payroll_number');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('payroll_records');
    }
};
