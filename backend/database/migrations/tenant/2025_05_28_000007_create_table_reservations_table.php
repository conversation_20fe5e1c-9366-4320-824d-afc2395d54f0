<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('table_reservations', function (Blueprint $table) {
            $table->id();
            $table->foreignId('table_id')->constrained()->onDelete('cascade');
            $table->foreignId('customer_id')->nullable()->constrained()->onDelete('set null');
            $table->string('guest_name')->nullable();
            $table->string('guest_phone')->nullable();
            $table->string('guest_email')->nullable();
            $table->date('reservation_date');
            $table->time('reservation_time');
            $table->integer('party_size');
            $table->integer('duration_minutes')->default(120); // 2 hours default
            $table->text('special_requests')->nullable();
            $table->string('occasion')->nullable();
            $table->enum('status', ['confirmed', 'seated', 'completed', 'cancelled', 'no_show'])->default('confirmed');
            $table->string('confirmation_code')->unique();
            $table->text('cancellation_reason')->nullable();
            $table->timestamp('cancelled_at')->nullable();
            $table->unsignedBigInteger('cancelled_by')->nullable(); // References central users table
            $table->timestamp('seated_at')->nullable();
            $table->timestamp('completed_at')->nullable();
            $table->text('notes')->nullable();
            $table->timestamps();

            $table->index(['table_id', 'reservation_date']);
            $table->index(['customer_id', 'reservation_date']);
            $table->index(['reservation_date', 'status']);
            $table->index(['confirmation_code']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('table_reservations');
    }
};
