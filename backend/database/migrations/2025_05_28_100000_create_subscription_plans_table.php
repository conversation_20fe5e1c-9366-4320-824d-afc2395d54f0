<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('subscription_plans', function (Blueprint $table) {
            $table->id();
            $table->string('name');
            $table->string('slug')->unique();
            $table->text('description')->nullable();
            $table->decimal('price', 10, 2)->default(0);
            $table->enum('billing_cycle', ['monthly', 'yearly'])->default('monthly');
            $table->integer('trial_days')->default(14);
            $table->json('features')->nullable();
            
            // Limits
            $table->integer('max_orders_per_month')->nullable();
            $table->integer('max_menu_items')->nullable();
            $table->integer('max_tables')->nullable();
            $table->integer('max_staff')->nullable();
            
            // Features
            $table->boolean('has_delivery')->default(false);
            $table->boolean('has_analytics')->default(false);
            $table->boolean('has_multi_location')->default(false);
            $table->boolean('has_api_access')->default(false);
            
            $table->boolean('is_active')->default(true);
            $table->integer('sort_order')->default(0);
            
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('subscription_plans');
    }
};
