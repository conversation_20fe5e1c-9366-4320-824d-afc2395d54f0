<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // First, let's fix the status column by converting int values to enum strings
        DB::statement("UPDATE blogs SET status = CASE 
            WHEN status = 1 THEN 'published' 
            WHEN status = 0 THEN 'draft' 
            ELSE 'draft' 
        END");

        // Drop and recreate the status column as enum
        Schema::table('blogs', function (Blueprint $table) {
            $table->dropColumn('status');
        });

        Schema::table('blogs', function (Blueprint $table) {
            $table->enum('status', ['draft', 'published'])->default('draft');
        });

        // Add missing columns that our Blog model expects
        Schema::table('blogs', function (Blueprint $table) {
            // Add content column (map from description)
            if (!Schema::hasColumn('blogs', 'content')) {
                $table->longText('content')->nullable();
                
                // Copy data from description to content
                DB::statement("UPDATE blogs SET content = description WHERE description IS NOT NULL");
            }
            
            // Add excerpt column (map from short_description)
            if (!Schema::hasColumn('blogs', 'excerpt')) {
                $table->text('excerpt')->nullable();
                
                // Copy data from short_description to excerpt
                DB::statement("UPDATE blogs SET excerpt = short_description WHERE short_description IS NOT NULL");
            }
            
            // Add featured_image_id column (map from banner)
            if (!Schema::hasColumn('blogs', 'featured_image_id')) {
                $table->foreignId('featured_image_id')->nullable()->constrained('media')->onDelete('set null');
                
                // Copy data from banner to featured_image_id
                DB::statement("UPDATE blogs SET featured_image_id = banner WHERE banner IS NOT NULL");
            }
            
            // Add published_at column
            if (!Schema::hasColumn('blogs', 'published_at')) {
                $table->timestamp('published_at')->nullable();
                
                // Set published_at for published blogs
                DB::statement("UPDATE blogs SET published_at = created_at WHERE status = 'published'");
            }
            
            // Add author_id column
            if (!Schema::hasColumn('blogs', 'author_id')) {
                $table->foreignId('author_id')->nullable()->constrained('users')->onDelete('set null');
            }
            
            // Add tags column (JSON)
            if (!Schema::hasColumn('blogs', 'tags')) {
                $table->json('tags')->nullable();
            }
            
            // Add categories column (JSON)
            if (!Schema::hasColumn('blogs', 'categories')) {
                $table->json('categories')->nullable();
            }
            
            // Add sort_order column
            if (!Schema::hasColumn('blogs', 'sort_order')) {
                $table->integer('sort_order')->default(0);
            }
            
            // Add created_by column
            if (!Schema::hasColumn('blogs', 'created_by')) {
                $table->foreignId('created_by')->nullable()->constrained('users')->onDelete('set null');
            }
            
            // Add updated_by column
            if (!Schema::hasColumn('blogs', 'updated_by')) {
                $table->foreignId('updated_by')->nullable()->constrained('users')->onDelete('set null');
            }
        });

        // Convert title column to JSON format
        Schema::table('blogs', function (Blueprint $table) {
            // Get all existing blogs and convert titles to JSON
            $blogs = DB::table('blogs')->get();
            foreach ($blogs as $blog) {
                if (!is_null($blog->title)) {
                    // Check if it's already JSON
                    $decoded = json_decode($blog->title, true);
                    if (json_last_error() !== JSON_ERROR_NONE) {
                        // Convert string title to JSON format
                        $titleJson = json_encode([
                            'en' => $blog->title,
                            'bn' => $blog->title
                        ]);
                        DB::table('blogs')->where('id', $blog->id)->update(['title' => $titleJson]);
                    }
                }
            }
        });

        // Remove old columns that we don't need anymore
        Schema::table('blogs', function (Blueprint $table) {
            $columnsToRemove = ['category_id', 'short_description', 'description', 'banner', 'meta_title', 'meta_img', 'meta_keywords'];
            
            foreach ($columnsToRemove as $column) {
                if (Schema::hasColumn('blogs', $column)) {
                    $table->dropColumn($column);
                }
            }
        });

        // Remove soft deletes column since we removed SoftDeletes from the model
        Schema::table('blogs', function (Blueprint $table) {
            if (Schema::hasColumn('blogs', 'deleted_at')) {
                $table->dropColumn('deleted_at');
            }
        });

        // Indexes were already created in the previous migration
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // This is a destructive migration, so we won't implement a full rollback
        // Instead, we'll just recreate the basic structure
        Schema::table('blogs', function (Blueprint $table) {
            $table->dropColumn([
                'content', 'excerpt', 'featured_image_id', 'published_at', 
                'author_id', 'tags', 'categories', 'sort_order', 
                'created_by', 'updated_by'
            ]);
            
            $table->integer('status')->default(0);
        });
    }
};
