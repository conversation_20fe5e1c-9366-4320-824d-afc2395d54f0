<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('users', function (Blueprint $table) {
            $table->string('phone')->nullable()->after('email');
            $table->string('department')->nullable()->after('last_login_at');
            $table->string('position')->nullable()->after('department');
            $table->date('hire_date')->nullable()->after('position');
            $table->decimal('salary', 10, 2)->nullable()->after('hire_date');
            $table->decimal('hourly_rate', 8, 2)->nullable()->after('salary');
            $table->text('address')->nullable()->after('hourly_rate');
            $table->string('emergency_contact_name')->nullable()->after('address');
            $table->string('emergency_contact_phone')->nullable()->after('emergency_contact_name');
            $table->text('notes')->nullable()->after('emergency_contact_phone');
            $table->boolean('is_on_shift')->default(false)->after('notes');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('users', function (Blueprint $table) {
            $table->dropColumn([
                'phone',
                'department',
                'position',
                'hire_date',
                'salary',
                'hourly_rate',
                'address',
                'emergency_contact_name',
                'emergency_contact_phone',
                'notes',
                'is_on_shift',
            ]);
        });
    }
};
