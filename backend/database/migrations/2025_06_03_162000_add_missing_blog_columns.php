<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('blogs', function (Blueprint $table) {
            // Add content column if it doesn't exist
            if (!Schema::hasColumn('blogs', 'content')) {
                $table->longText('content')->nullable();
            }
            
            // Add excerpt column if it doesn't exist
            if (!Schema::hasColumn('blogs', 'excerpt')) {
                $table->text('excerpt')->nullable();
            }
            
            // Add featured_image_id column if it doesn't exist
            if (!Schema::hasColumn('blogs', 'featured_image_id')) {
                $table->foreignId('featured_image_id')->nullable()->constrained('media')->onDelete('set null');
            }
            
            // Add published_at column if it doesn't exist
            if (!Schema::hasColumn('blogs', 'published_at')) {
                $table->timestamp('published_at')->nullable();
            }
            
            // Add author_id column if it doesn't exist
            if (!Schema::hasColumn('blogs', 'author_id')) {
                $table->foreignId('author_id')->nullable()->constrained('users')->onDelete('set null');
            }
            
            // Add tags column if it doesn't exist
            if (!Schema::hasColumn('blogs', 'tags')) {
                $table->json('tags')->nullable();
            }
            
            // Add categories column if it doesn't exist
            if (!Schema::hasColumn('blogs', 'categories')) {
                $table->json('categories')->nullable();
            }
            
            // Add sort_order column if it doesn't exist
            if (!Schema::hasColumn('blogs', 'sort_order')) {
                $table->integer('sort_order')->default(0);
            }
            
            // Add created_by column if it doesn't exist
            if (!Schema::hasColumn('blogs', 'created_by')) {
                $table->foreignId('created_by')->nullable()->constrained('users')->onDelete('set null');
            }
            
            // Add updated_by column if it doesn't exist
            if (!Schema::hasColumn('blogs', 'updated_by')) {
                $table->foreignId('updated_by')->nullable()->constrained('users')->onDelete('set null');
            }
        });

        // Copy data from old columns to new ones (only if they exist)
        if (Schema::hasColumn('blogs', 'description')) {
            DB::statement("UPDATE blogs SET content = description WHERE description IS NOT NULL AND content IS NULL");
        }

        if (Schema::hasColumn('blogs', 'short_description')) {
            DB::statement("UPDATE blogs SET excerpt = short_description WHERE short_description IS NOT NULL AND excerpt IS NULL");
        }

        if (Schema::hasColumn('blogs', 'banner')) {
            DB::statement("UPDATE blogs SET featured_image_id = banner WHERE banner IS NOT NULL AND featured_image_id IS NULL");
        }

        // Set published_at for published blogs (assuming status 1 = published)
        DB::statement("UPDATE blogs SET published_at = created_at WHERE status = 1 AND published_at IS NULL");
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('blogs', function (Blueprint $table) {
            $table->dropColumn([
                'content', 'excerpt', 'featured_image_id', 'published_at', 
                'author_id', 'tags', 'categories', 'sort_order', 
                'created_by', 'updated_by'
            ]);
        });
    }
};
