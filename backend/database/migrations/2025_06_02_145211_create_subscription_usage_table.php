<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('subscription_usage', function (Blueprint $table) {
            $table->id();
            $table->string('tenant_id');
            $table->unsignedBigInteger('subscription_plan_id');
            $table->date('period_start');
            $table->date('period_end');

            // Usage metrics
            $table->integer('menu_items_used')->default(0);
            $table->integer('orders_used')->default(0);
            $table->integer('pages_used')->default(0);
            $table->integer('branches_used')->default(0);
            $table->integer('staff_used')->default(0);

            // Lifetime limits (for items that don't reset monthly)
            $table->integer('total_menu_items')->default(0);
            $table->integer('total_pages')->default(0);
            $table->integer('total_branches')->default(0);

            // Additional metrics
            $table->decimal('revenue_generated', 10, 2)->default(0);
            $table->integer('customers_served')->default(0);

            $table->timestamps();

            $table->foreign('tenant_id')->references('id')->on('tenants')->onDelete('cascade');
            $table->foreign('subscription_plan_id')->references('id')->on('subscription_plans')->onDelete('cascade');

            $table->unique(['tenant_id', 'period_start', 'period_end']);
            $table->index(['tenant_id', 'period_start']);
            $table->index(['subscription_plan_id']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('subscription_usage');
    }
};
