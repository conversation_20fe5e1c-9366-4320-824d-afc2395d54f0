<?php

// Test if the route fixes are working
require_once 'vendor/autoload.php';

$app = require_once 'bootstrap/app.php';
$kernel = $app->make('Illuminate\Contracts\Console\Kernel');
$kernel->bootstrap();

use App\Models\Tenant;
use Illuminate\Support\Facades\Route;

echo "=== Testing Route Fixes ===\n\n";

try {
    $tenant = Tenant::find('demo-restaurant');
    
    if (!$tenant) {
        echo "❌ Tenant 'demo-restaurant' not found!\n";
        exit(1);
    }

    echo "✓ Tenant found: {$tenant->name}\n";

    $tenant->run(function () {
        echo "\n🔧 Testing route availability...\n";
        
        // Test routes that should exist
        $routesToTest = [
            'manager.dashboard',
            'manager.analytics', 
            'menu-items.index',
            'orders.index',
            'tables.index',
            'customers.index',
            'staff.index',
            'departments.index',
            'inventory.index',
            'reports.index',
            'settings.index',
            'waiter.dashboard',
            'waiter.tables',
            'waiter.orders',
            'waiter.menu',
            'waiter.customers',
            'waiter.reservations'
        ];
        
        $existingRoutes = [];
        $missingRoutes = [];
        
        foreach ($routesToTest as $routeName) {
            try {
                $url = route($routeName);
                $existingRoutes[] = $routeName;
                echo "✓ {$routeName}: {$url}\n";
            } catch (Exception $e) {
                $missingRoutes[] = $routeName;
                echo "❌ {$routeName}: Missing\n";
            }
        }
        
        echo "\n📊 Summary:\n";
        echo "✅ Existing routes: " . count($existingRoutes) . "\n";
        echo "❌ Missing routes: " . count($missingRoutes) . "\n";
        
        if (!empty($missingRoutes)) {
            echo "\n🔧 Missing routes that need to be added:\n";
            foreach ($missingRoutes as $route) {
                echo "   - {$route}\n";
            }
        }
        
        // Test specific problematic routes
        echo "\n🧪 Testing specific problematic routes:\n";
        
        try {
            $menuUrl = route('menu-items.index');
            echo "✅ menu-items.index works: {$menuUrl}\n";
        } catch (Exception $e) {
            echo "❌ menu-items.index failed: " . $e->getMessage() . "\n";
        }
        
        try {
            $waiterMenuUrl = route('waiter.menu');
            echo "✅ waiter.menu works: {$waiterMenuUrl}\n";
        } catch (Exception $e) {
            echo "❌ waiter.menu failed: " . $e->getMessage() . "\n";
        }
    });

    echo "\n🎉 Route testing completed!\n";
    echo "\n📋 Next Steps:\n";
    echo "1. Clear route cache: php artisan route:clear\n";
    echo "2. Rebuild frontend assets: npm run build\n";
    echo "3. Test the manager dashboard: http://demo-restaurant.localhost:8000/manager/dashboard\n";
    echo "\n✅ The Ziggy route errors should be resolved!\n";

} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
    echo "File: " . $e->getFile() . ":" . $e->getLine() . "\n";
}
