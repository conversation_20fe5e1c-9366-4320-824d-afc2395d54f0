<?php

/**
 * Quick test script to verify tenant authentication fix
 * Run this from the backend directory: php test-auth-fix.php
 */

require_once 'vendor/autoload.php';

$app = require_once 'bootstrap/app.php';

echo "🔧 Testing Tenant Authentication Fix\n";
echo "=====================================\n\n";

// Test 1: Check if middleware is properly configured
echo "1. Checking middleware configuration...\n";
$middlewareAliases = config('app.middleware_aliases', []);
if (isset($middlewareAliases['tenant.verified'])) {
    echo "   ✅ tenant.verified middleware is registered\n";
} else {
    echo "   ❌ tenant.verified middleware is NOT registered\n";
}

// Test 2: Check if TenantEnsureEmailIsVerified class exists
echo "\n2. Checking custom middleware class...\n";
if (class_exists('App\Http\Middleware\TenantEnsureEmailIsVerified')) {
    echo "   ✅ TenantEnsureEmailIsVerified middleware class exists\n";
} else {
    echo "   ❌ TenantEnsureEmailIsVerified middleware class NOT found\n";
}

// Test 3: Check if Tenant User model implements MustVerifyEmail
echo "\n3. Checking Tenant User model...\n";
if (class_exists('App\Models\Tenant\User')) {
    $reflection = new ReflectionClass('App\Models\Tenant\User');
    $interfaces = $reflection->getInterfaceNames();
    
    if (in_array('Illuminate\Contracts\Auth\MustVerifyEmail', $interfaces)) {
        echo "   ✅ Tenant User implements MustVerifyEmail\n";
    } else {
        echo "   ❌ Tenant User does NOT implement MustVerifyEmail\n";
    }
    
    if ($reflection->hasMethod('getDashboardRoute')) {
        echo "   ✅ Tenant User has getDashboardRoute method\n";
    } else {
        echo "   ❌ Tenant User missing getDashboardRoute method\n";
    }
} else {
    echo "   ❌ Tenant User model NOT found\n";
}

// Test 4: Check auth configuration
echo "\n4. Checking auth configuration...\n";
$guards = config('auth.guards', []);
if (isset($guards['tenant'])) {
    echo "   ✅ Tenant guard is configured\n";
    echo "   - Driver: " . $guards['tenant']['driver'] . "\n";
    echo "   - Provider: " . $guards['tenant']['provider'] . "\n";
} else {
    echo "   ❌ Tenant guard is NOT configured\n";
}

$providers = config('auth.providers', []);
if (isset($providers['tenant_users'])) {
    echo "   ✅ Tenant users provider is configured\n";
    echo "   - Model: " . $providers['tenant_users']['model'] . "\n";
} else {
    echo "   ❌ Tenant users provider is NOT configured\n";
}

// Test 5: Check if RedirectBasedOnRole is removed from global middleware
echo "\n5. Checking global middleware...\n";
echo "   ℹ️  RedirectBasedOnRole should NOT be in global web middleware\n";
echo "   ℹ️  Check bootstrap/app.php manually\n";

echo "\n🎯 Test Summary:\n";
echo "================\n";
echo "If all tests pass, the authentication fix should work.\n";
echo "Next steps:\n";
echo "1. Clear caches: php artisan config:clear && php artisan route:clear\n";
echo "2. Test login at: http://your-tenant.localhost/login\n";
echo "3. Check debug routes:\n";
echo "   - http://your-tenant.localhost/debug-auth\n";
echo "   - http://your-tenant.localhost/test-dashboard\n";
echo "   - http://your-tenant.localhost/simple-auth-test\n\n";
