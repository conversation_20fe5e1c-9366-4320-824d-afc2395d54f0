<?php

// Fix tenant database schema mismatch
require_once 'vendor/autoload.php';

$app = require_once 'bootstrap/app.php';
$kernel = $app->make('Illuminate\Contracts\Console\Kernel');
$kernel->bootstrap();

use App\Models\Tenant;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

echo "=== Fixing Tenant Database Schema Mismatch ===\n\n";

try {
    // Find the tenant
    $tenant = Tenant::find('demo-restaurant');
    
    if (!$tenant) {
        echo "❌ Tenant 'demo-restaurant' not found!\n";
        exit(1);
    }

    echo "✓ Tenant found: {$tenant->name}\n";

    // Switch to tenant context and fix schemas
    $tenant->run(function () {
        echo "\n🔧 Analyzing and fixing table schemas...\n";

        // 1. Check and fix menu_items table schema
        echo "\n1. Fixing menu_items table schema...\n";
        
        if (Schema::hasTable('menu_items')) {
            echo "   Checking existing menu_items columns...\n";
            
            // Get current columns
            $columns = Schema::getColumnListing('menu_items');
            echo "   Current columns: " . implode(', ', $columns) . "\n";
            
            // Add missing columns
            $missingColumns = [
                'is_active' => "ALTER TABLE `menu_items` ADD COLUMN `is_active` tinyint(1) NOT NULL DEFAULT '1'",
                'is_available' => "ALTER TABLE `menu_items` ADD COLUMN `is_available` tinyint(1) NOT NULL DEFAULT '1'",
                'is_featured' => "ALTER TABLE `menu_items` ADD COLUMN `is_featured` tinyint(1) NOT NULL DEFAULT '0'",
                'sort_order' => "ALTER TABLE `menu_items` ADD COLUMN `sort_order` int NOT NULL DEFAULT '0'",
                'short_description' => "ALTER TABLE `menu_items` ADD COLUMN `short_description` varchar(500) DEFAULT NULL",
                'cost_price' => "ALTER TABLE `menu_items` ADD COLUMN `cost_price` decimal(10,2) DEFAULT NULL",
                'image' => "ALTER TABLE `menu_items` ADD COLUMN `image` varchar(255) DEFAULT NULL",
                'ingredients' => "ALTER TABLE `menu_items` ADD COLUMN `ingredients` text",
                'preparation_time' => "ALTER TABLE `menu_items` ADD COLUMN `preparation_time` int DEFAULT NULL",
                'is_vegetarian' => "ALTER TABLE `menu_items` ADD COLUMN `is_vegetarian` tinyint(1) NOT NULL DEFAULT '0'",
                'is_vegan' => "ALTER TABLE `menu_items` ADD COLUMN `is_vegan` tinyint(1) NOT NULL DEFAULT '0'",
                'is_gluten_free' => "ALTER TABLE `menu_items` ADD COLUMN `is_gluten_free` tinyint(1) NOT NULL DEFAULT '0'"
            ];
            
            foreach ($missingColumns as $column => $sql) {
                if (!in_array($column, $columns)) {
                    try {
                        DB::statement($sql);
                        echo "   ✓ Added column: {$column}\n";
                    } catch (Exception $e) {
                        echo "   ⚠ Failed to add {$column}: " . $e->getMessage() . "\n";
                    }
                } else {
                    echo "   ✓ Column exists: {$column}\n";
                }
            }
            
            // Add indexes for performance
            try {
                DB::statement("ALTER TABLE `menu_items` ADD INDEX `menu_items_is_active_index` (`is_active`)");
                echo "   ✓ Added is_active index\n";
            } catch (Exception $e) {
                // Index might already exist
            }
            
            try {
                DB::statement("ALTER TABLE `menu_items` ADD INDEX `menu_items_is_available_index` (`is_available`)");
                echo "   ✓ Added is_available index\n";
            } catch (Exception $e) {
                // Index might already exist
            }
            
        } else {
            echo "   Creating menu_items table with complete schema...\n";
            DB::statement("
                CREATE TABLE `menu_items` (
                    `id` bigint unsigned NOT NULL AUTO_INCREMENT,
                    `name` varchar(255) NOT NULL,
                    `slug` varchar(255) NOT NULL,
                    `description` text,
                    `short_description` varchar(500) DEFAULT NULL,
                    `price` decimal(10,2) NOT NULL,
                    `cost_price` decimal(10,2) DEFAULT NULL,
                    `category_id` bigint unsigned DEFAULT NULL,
                    `image` varchar(255) DEFAULT NULL,
                    `ingredients` text,
                    `preparation_time` int DEFAULT NULL,
                    `is_vegetarian` tinyint(1) NOT NULL DEFAULT '0',
                    `is_vegan` tinyint(1) NOT NULL DEFAULT '0',
                    `is_gluten_free` tinyint(1) NOT NULL DEFAULT '0',
                    `is_active` tinyint(1) NOT NULL DEFAULT '1',
                    `is_available` tinyint(1) NOT NULL DEFAULT '1',
                    `is_featured` tinyint(1) NOT NULL DEFAULT '0',
                    `sort_order` int NOT NULL DEFAULT '0',
                    `created_at` timestamp NULL DEFAULT NULL,
                    `updated_at` timestamp NULL DEFAULT NULL,
                    PRIMARY KEY (`id`),
                    UNIQUE KEY `menu_items_slug_unique` (`slug`),
                    KEY `menu_items_category_id_index` (`category_id`),
                    KEY `menu_items_is_active_index` (`is_active`),
                    KEY `menu_items_is_available_index` (`is_available`),
                    KEY `menu_items_is_featured_index` (`is_featured`)
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
            ");
            echo "   ✓ Menu items table created with complete schema\n";
        }

        // 2. Ensure food table has consistent schema
        echo "\n2. Ensuring food table schema consistency...\n";
        
        if (!Schema::hasTable('food')) {
            echo "   Creating food table...\n";
            DB::statement("
                CREATE TABLE `food` (
                    `id` bigint unsigned NOT NULL AUTO_INCREMENT,
                    `name` varchar(255) NOT NULL,
                    `slug` varchar(255) NOT NULL,
                    `description` text,
                    `price` decimal(10,2) NOT NULL,
                    `category_id` bigint unsigned DEFAULT NULL,
                    `image` varchar(255) DEFAULT NULL,
                    `is_active` tinyint(1) NOT NULL DEFAULT '1',
                    `is_available` tinyint(1) NOT NULL DEFAULT '1',
                    `is_featured` tinyint(1) NOT NULL DEFAULT '0',
                    `sort_order` int NOT NULL DEFAULT '0',
                    `created_at` timestamp NULL DEFAULT NULL,
                    `updated_at` timestamp NULL DEFAULT NULL,
                    PRIMARY KEY (`id`),
                    UNIQUE KEY `food_slug_unique` (`slug`),
                    KEY `food_category_id_index` (`category_id`),
                    KEY `food_is_active_index` (`is_active`),
                    KEY `food_is_available_index` (`is_available`)
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
            ");
            echo "   ✓ Food table created\n";
        } else {
            echo "   ✓ Food table already exists\n";
        }

        // 3. Create other essential tables
        echo "\n3. Creating other essential restaurant tables...\n";

        // Food categories
        if (!Schema::hasTable('food_categories')) {
            DB::statement("
                CREATE TABLE `food_categories` (
                    `id` bigint unsigned NOT NULL AUTO_INCREMENT,
                    `name` varchar(255) NOT NULL,
                    `slug` varchar(255) NOT NULL,
                    `description` text,
                    `image` varchar(255) DEFAULT NULL,
                    `sort_order` int NOT NULL DEFAULT '0',
                    `is_active` tinyint(1) NOT NULL DEFAULT '1',
                    `parent_id` bigint unsigned DEFAULT NULL,
                    `created_at` timestamp NULL DEFAULT NULL,
                    `updated_at` timestamp NULL DEFAULT NULL,
                    PRIMARY KEY (`id`),
                    UNIQUE KEY `food_categories_slug_unique` (`slug`),
                    KEY `food_categories_is_active_index` (`is_active`)
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
            ");
            echo "   ✓ Food categories table created\n";
        }

        // Customers
        if (!Schema::hasTable('customers')) {
            DB::statement("
                CREATE TABLE `customers` (
                    `id` bigint unsigned NOT NULL AUTO_INCREMENT,
                    `name` varchar(255) NOT NULL,
                    `email` varchar(255) DEFAULT NULL,
                    `phone` varchar(255) DEFAULT NULL,
                    `address` text,
                    `is_active` tinyint(1) NOT NULL DEFAULT '1',
                    `created_at` timestamp NULL DEFAULT NULL,
                    `updated_at` timestamp NULL DEFAULT NULL,
                    PRIMARY KEY (`id`),
                    KEY `customers_email_index` (`email`),
                    KEY `customers_phone_index` (`phone`)
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
            ");
            echo "   ✓ Customers table created\n";
        }

        // Orders
        if (!Schema::hasTable('orders')) {
            DB::statement("
                CREATE TABLE `orders` (
                    `id` bigint unsigned NOT NULL AUTO_INCREMENT,
                    `order_number` varchar(255) NOT NULL,
                    `customer_id` bigint unsigned DEFAULT NULL,
                    `status` enum('pending','confirmed','preparing','ready','completed','cancelled') NOT NULL DEFAULT 'pending',
                    `type` enum('dine_in','takeaway','delivery') NOT NULL DEFAULT 'dine_in',
                    `total_amount` decimal(10,2) NOT NULL DEFAULT '0.00',
                    `created_at` timestamp NULL DEFAULT NULL,
                    `updated_at` timestamp NULL DEFAULT NULL,
                    PRIMARY KEY (`id`),
                    UNIQUE KEY `orders_order_number_unique` (`order_number`),
                    KEY `orders_status_index` (`status`)
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
            ");
            echo "   ✓ Orders table created\n";
        }

        // Order items
        if (!Schema::hasTable('order_items')) {
            DB::statement("
                CREATE TABLE `order_items` (
                    `id` bigint unsigned NOT NULL AUTO_INCREMENT,
                    `order_id` bigint unsigned NOT NULL,
                    `menu_item_id` bigint unsigned DEFAULT NULL,
                    `food_id` bigint unsigned DEFAULT NULL,
                    `name` varchar(255) NOT NULL,
                    `price` decimal(10,2) NOT NULL,
                    `quantity` int NOT NULL DEFAULT '1',
                    `total` decimal(10,2) NOT NULL,
                    `created_at` timestamp NULL DEFAULT NULL,
                    `updated_at` timestamp NULL DEFAULT NULL,
                    PRIMARY KEY (`id`),
                    KEY `order_items_order_id_index` (`order_id`)
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
            ");
            echo "   ✓ Order items table created\n";
        }

        // Tables
        if (!Schema::hasTable('tables')) {
            DB::statement("
                CREATE TABLE `tables` (
                    `id` bigint unsigned NOT NULL AUTO_INCREMENT,
                    `number` varchar(255) NOT NULL,
                    `name` varchar(255) DEFAULT NULL,
                    `capacity` int NOT NULL DEFAULT '4',
                    `status` enum('available','occupied','reserved') NOT NULL DEFAULT 'available',
                    `is_active` tinyint(1) NOT NULL DEFAULT '1',
                    `created_at` timestamp NULL DEFAULT NULL,
                    `updated_at` timestamp NULL DEFAULT NULL,
                    PRIMARY KEY (`id`),
                    UNIQUE KEY `tables_number_unique` (`number`)
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
            ");
            echo "   ✓ Tables table created\n";
        }

        // 4. Insert demo data with correct schema
        echo "\n4. Inserting demo data...\n";

        // Demo categories
        if (DB::table('food_categories')->count() == 0) {
            DB::table('food_categories')->insert([
                ['name' => 'Appetizers', 'slug' => 'appetizers', 'description' => 'Start your meal right', 'sort_order' => 1, 'is_active' => 1, 'created_at' => now(), 'updated_at' => now()],
                ['name' => 'Main Courses', 'slug' => 'main-courses', 'description' => 'Hearty main dishes', 'sort_order' => 2, 'is_active' => 1, 'created_at' => now(), 'updated_at' => now()],
                ['name' => 'Desserts', 'slug' => 'desserts', 'description' => 'Sweet endings', 'sort_order' => 3, 'is_active' => 1, 'created_at' => now(), 'updated_at' => now()],
                ['name' => 'Beverages', 'slug' => 'beverages', 'description' => 'Refreshing drinks', 'sort_order' => 4, 'is_active' => 1, 'created_at' => now(), 'updated_at' => now()],
            ]);
            echo "   ✓ Demo categories inserted\n";
        }

        // Demo food items
        if (DB::table('food')->count() == 0) {
            DB::table('food')->insert([
                ['name' => 'Caesar Salad', 'slug' => 'caesar-salad', 'description' => 'Fresh romaine with caesar dressing', 'price' => 12.99, 'category_id' => 1, 'is_active' => 1, 'is_available' => 1, 'sort_order' => 1, 'created_at' => now(), 'updated_at' => now()],
                ['name' => 'Grilled Chicken', 'slug' => 'grilled-chicken', 'description' => 'Tender grilled chicken breast', 'price' => 18.99, 'category_id' => 2, 'is_active' => 1, 'is_available' => 1, 'sort_order' => 1, 'created_at' => now(), 'updated_at' => now()],
                ['name' => 'Chocolate Cake', 'slug' => 'chocolate-cake', 'description' => 'Rich chocolate cake', 'price' => 8.99, 'category_id' => 3, 'is_active' => 1, 'is_available' => 1, 'sort_order' => 1, 'created_at' => now(), 'updated_at' => now()],
                ['name' => 'Fresh Juice', 'slug' => 'fresh-juice', 'description' => 'Freshly squeezed juice', 'price' => 4.99, 'category_id' => 4, 'is_active' => 1, 'is_available' => 1, 'sort_order' => 1, 'created_at' => now(), 'updated_at' => now()],
            ]);
            echo "   ✓ Demo food items inserted\n";
        }

        // Sync menu_items with food data (with correct columns)
        if (DB::table('menu_items')->count() == 0) {
            $foodItems = DB::table('food')->get();
            foreach ($foodItems as $food) {
                DB::table('menu_items')->insert([
                    'name' => $food->name,
                    'slug' => $food->slug . '-menu',
                    'description' => $food->description,
                    'price' => $food->price,
                    'category_id' => $food->category_id,
                    'is_active' => $food->is_active,
                    'is_available' => $food->is_available,
                    'is_featured' => 0,
                    'sort_order' => $food->sort_order,
                    'is_vegetarian' => 0,
                    'is_vegan' => 0,
                    'is_gluten_free' => 0,
                    'created_at' => $food->created_at,
                    'updated_at' => $food->updated_at,
                ]);
            }
            echo "   ✓ Menu items synced with food data\n";
        }

        // Demo tables
        if (DB::table('tables')->count() == 0) {
            for ($i = 1; $i <= 8; $i++) {
                DB::table('tables')->insert([
                    'number' => "T{$i}",
                    'name' => "Table {$i}",
                    'capacity' => rand(2, 6),
                    'status' => 'available',
                    'is_active' => 1,
                    'created_at' => now(),
                    'updated_at' => now(),
                ]);
            }
            echo "   ✓ Demo tables inserted\n";
        }

        // 5. Final verification
        echo "\n5. Final schema verification...\n";
        
        $tables = ['food_categories', 'food', 'menu_items', 'customers', 'orders', 'order_items', 'tables'];
        
        foreach ($tables as $table) {
            if (Schema::hasTable($table)) {
                $count = DB::table($table)->count();
                $columns = Schema::getColumnListing($table);
                echo "   ✓ {$table}: {$count} records, " . count($columns) . " columns\n";
                
                // Check for essential columns
                if ($table === 'food' || $table === 'menu_items') {
                    $essentialCols = ['is_active', 'is_available'];
                    foreach ($essentialCols as $col) {
                        if (in_array($col, $columns)) {
                            echo "     ✓ Has {$col} column\n";
                        } else {
                            echo "     ❌ Missing {$col} column\n";
                        }
                    }
                }
            } else {
                echo "   ❌ {$table}: Table missing\n";
            }
        }
    });

    echo "\n🎉 Schema fix completed successfully!\n";
    echo "\n📋 Summary:\n";
    echo "✅ Menu items table schema fixed (added missing columns)\n";
    echo "✅ Food table schema verified\n";
    echo "✅ All restaurant tables created with consistent schemas\n";
    echo "✅ Demo data inserted successfully\n";
    echo "✅ Indexes added for performance\n";
    echo "\n🧪 Verification commands:\n";
    echo "php artisan tinker --execute=\"\$tenant = App\\Models\\Tenant::find('demo-restaurant'); \$tenant->run(function() { echo 'Food count: ' . DB::table('food')->where('is_active', 1)->count(); });\"\n";
    echo "\n🌐 Test URLs:\n";
    echo "http://demo-restaurant.localhost:8000/dashboard\n";
    echo "\n✅ The schema mismatch error should be completely resolved!\n";

} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
    echo "File: " . $e->getFile() . ":" . $e->getLine() . "\n";
}
