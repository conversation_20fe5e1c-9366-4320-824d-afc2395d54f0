<?php

// Fix missing tenant database tables
require_once 'vendor/autoload.php';

$app = require_once 'bootstrap/app.php';
$kernel = $app->make('Illuminate\Contracts\Console\Kernel');
$kernel->bootstrap();

use App\Models\Tenant;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

echo "=== Fixing Missing Tenant Database Tables ===\n\n";

try {
    // Find the tenant
    $tenant = Tenant::find('demo-restaurant');
    
    if (!$tenant) {
        echo "❌ Tenant 'demo-restaurant' not found!\n";
        exit(1);
    }

    echo "✓ Tenant found: {$tenant->name}\n";

    // Switch to tenant context and create missing tables
    $tenant->run(function () {
        echo "\n🔧 Creating missing restaurant tables...\n";

        // 1. Create food_categories table (for menu organization)
        if (!Schema::hasTable('food_categories')) {
            echo "Creating food_categories table...\n";
            DB::statement("
                CREATE TABLE `food_categories` (
                    `id` bigint unsigned NOT NULL AUTO_INCREMENT,
                    `name` varchar(255) NOT NULL,
                    `slug` varchar(255) NOT NULL,
                    `description` text,
                    `image` varchar(255) DEFAULT NULL,
                    `sort_order` int NOT NULL DEFAULT '0',
                    `is_active` tinyint(1) NOT NULL DEFAULT '1',
                    `parent_id` bigint unsigned DEFAULT NULL,
                    `created_at` timestamp NULL DEFAULT NULL,
                    `updated_at` timestamp NULL DEFAULT NULL,
                    PRIMARY KEY (`id`),
                    UNIQUE KEY `food_categories_slug_unique` (`slug`),
                    KEY `food_categories_parent_id_index` (`parent_id`),
                    KEY `food_categories_is_active_index` (`is_active`)
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
            ");
            echo "✓ Food categories table created\n";
        } else {
            echo "✓ Food categories table already exists\n";
        }

        // 2. Create menu_items table (main food/menu table)
        if (!Schema::hasTable('menu_items')) {
            echo "Creating menu_items table...\n";
            DB::statement("
                CREATE TABLE `menu_items` (
                    `id` bigint unsigned NOT NULL AUTO_INCREMENT,
                    `name` varchar(255) NOT NULL,
                    `slug` varchar(255) NOT NULL,
                    `description` text,
                    `short_description` varchar(500) DEFAULT NULL,
                    `price` decimal(10,2) NOT NULL,
                    `cost_price` decimal(10,2) DEFAULT NULL,
                    `category_id` bigint unsigned DEFAULT NULL,
                    `image` varchar(255) DEFAULT NULL,
                    `images` json DEFAULT NULL,
                    `ingredients` text,
                    `allergens` varchar(255) DEFAULT NULL,
                    `nutritional_info` json DEFAULT NULL,
                    `preparation_time` int DEFAULT NULL,
                    `calories` int DEFAULT NULL,
                    `is_vegetarian` tinyint(1) NOT NULL DEFAULT '0',
                    `is_vegan` tinyint(1) NOT NULL DEFAULT '0',
                    `is_gluten_free` tinyint(1) NOT NULL DEFAULT '0',
                    `is_spicy` tinyint(1) NOT NULL DEFAULT '0',
                    `spice_level` enum('mild','medium','hot','very_hot') DEFAULT NULL,
                    `is_active` tinyint(1) NOT NULL DEFAULT '1',
                    `is_available` tinyint(1) NOT NULL DEFAULT '1',
                    `is_featured` tinyint(1) NOT NULL DEFAULT '0',
                    `sort_order` int NOT NULL DEFAULT '0',
                    `tags` json DEFAULT NULL,
                    `created_at` timestamp NULL DEFAULT NULL,
                    `updated_at` timestamp NULL DEFAULT NULL,
                    PRIMARY KEY (`id`),
                    UNIQUE KEY `menu_items_slug_unique` (`slug`),
                    KEY `menu_items_category_id_index` (`category_id`),
                    KEY `menu_items_is_active_index` (`is_active`),
                    KEY `menu_items_is_available_index` (`is_available`),
                    KEY `menu_items_is_featured_index` (`is_featured`)
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
            ");
            echo "✓ Menu items table created\n";
        } else {
            echo "✓ Menu items table already exists\n";
        }

        // 3. Create food table (alias/view for menu_items for compatibility)
        if (!Schema::hasTable('food')) {
            echo "Creating food table (alias for menu_items)...\n";
            DB::statement("
                CREATE TABLE `food` (
                    `id` bigint unsigned NOT NULL AUTO_INCREMENT,
                    `name` varchar(255) NOT NULL,
                    `slug` varchar(255) NOT NULL,
                    `description` text,
                    `price` decimal(10,2) NOT NULL,
                    `category_id` bigint unsigned DEFAULT NULL,
                    `image` varchar(255) DEFAULT NULL,
                    `is_active` tinyint(1) NOT NULL DEFAULT '1',
                    `is_available` tinyint(1) NOT NULL DEFAULT '1',
                    `is_featured` tinyint(1) NOT NULL DEFAULT '0',
                    `created_at` timestamp NULL DEFAULT NULL,
                    `updated_at` timestamp NULL DEFAULT NULL,
                    PRIMARY KEY (`id`),
                    UNIQUE KEY `food_slug_unique` (`slug`),
                    KEY `food_category_id_index` (`category_id`),
                    KEY `food_is_active_index` (`is_active`),
                    KEY `food_is_available_index` (`is_available`)
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
            ");
            echo "✓ Food table created\n";
        } else {
            echo "✓ Food table already exists\n";
        }

        // 4. Create customers table
        if (!Schema::hasTable('customers')) {
            echo "Creating customers table...\n";
            DB::statement("
                CREATE TABLE `customers` (
                    `id` bigint unsigned NOT NULL AUTO_INCREMENT,
                    `name` varchar(255) NOT NULL,
                    `email` varchar(255) DEFAULT NULL,
                    `phone` varchar(255) DEFAULT NULL,
                    `address` text,
                    `city` varchar(255) DEFAULT NULL,
                    `postal_code` varchar(255) DEFAULT NULL,
                    `date_of_birth` date DEFAULT NULL,
                    `loyalty_points` int NOT NULL DEFAULT '0',
                    `total_orders` int NOT NULL DEFAULT '0',
                    `total_spent` decimal(10,2) NOT NULL DEFAULT '0.00',
                    `is_active` tinyint(1) NOT NULL DEFAULT '1',
                    `notes` text,
                    `created_at` timestamp NULL DEFAULT NULL,
                    `updated_at` timestamp NULL DEFAULT NULL,
                    PRIMARY KEY (`id`),
                    KEY `customers_email_index` (`email`),
                    KEY `customers_phone_index` (`phone`),
                    KEY `customers_is_active_index` (`is_active`)
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
            ");
            echo "✓ Customers table created\n";
        } else {
            echo "✓ Customers table already exists\n";
        }

        // 5. Create orders table
        if (!Schema::hasTable('orders')) {
            echo "Creating orders table...\n";
            DB::statement("
                CREATE TABLE `orders` (
                    `id` bigint unsigned NOT NULL AUTO_INCREMENT,
                    `order_number` varchar(255) NOT NULL,
                    `customer_id` bigint unsigned DEFAULT NULL,
                    `customer_name` varchar(255) DEFAULT NULL,
                    `customer_phone` varchar(255) DEFAULT NULL,
                    `customer_email` varchar(255) DEFAULT NULL,
                    `type` enum('dine_in','takeaway','delivery') NOT NULL DEFAULT 'dine_in',
                    `status` enum('pending','confirmed','preparing','ready','completed','cancelled') NOT NULL DEFAULT 'pending',
                    `table_number` varchar(255) DEFAULT NULL,
                    `subtotal` decimal(10,2) NOT NULL DEFAULT '0.00',
                    `tax_amount` decimal(10,2) NOT NULL DEFAULT '0.00',
                    `discount_amount` decimal(10,2) NOT NULL DEFAULT '0.00',
                    `delivery_fee` decimal(10,2) NOT NULL DEFAULT '0.00',
                    `total_amount` decimal(10,2) NOT NULL DEFAULT '0.00',
                    `payment_status` enum('pending','paid','failed','refunded') NOT NULL DEFAULT 'pending',
                    `payment_method` varchar(255) DEFAULT NULL,
                    `notes` text,
                    `delivery_address` text,
                    `estimated_delivery_time` timestamp NULL DEFAULT NULL,
                    `completed_at` timestamp NULL DEFAULT NULL,
                    `created_at` timestamp NULL DEFAULT NULL,
                    `updated_at` timestamp NULL DEFAULT NULL,
                    PRIMARY KEY (`id`),
                    UNIQUE KEY `orders_order_number_unique` (`order_number`),
                    KEY `orders_customer_id_index` (`customer_id`),
                    KEY `orders_status_index` (`status`),
                    KEY `orders_type_index` (`type`)
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
            ");
            echo "✓ Orders table created\n";
        } else {
            echo "✓ Orders table already exists\n";
        }

        // 6. Create order_items table
        if (!Schema::hasTable('order_items')) {
            echo "Creating order_items table...\n";
            DB::statement("
                CREATE TABLE `order_items` (
                    `id` bigint unsigned NOT NULL AUTO_INCREMENT,
                    `order_id` bigint unsigned NOT NULL,
                    `menu_item_id` bigint unsigned DEFAULT NULL,
                    `food_id` bigint unsigned DEFAULT NULL,
                    `name` varchar(255) NOT NULL,
                    `price` decimal(10,2) NOT NULL,
                    `quantity` int NOT NULL DEFAULT '1',
                    `total` decimal(10,2) NOT NULL,
                    `notes` text,
                    `status` enum('pending','preparing','ready','served') NOT NULL DEFAULT 'pending',
                    `created_at` timestamp NULL DEFAULT NULL,
                    `updated_at` timestamp NULL DEFAULT NULL,
                    PRIMARY KEY (`id`),
                    KEY `order_items_order_id_index` (`order_id`),
                    KEY `order_items_menu_item_id_index` (`menu_item_id`),
                    KEY `order_items_food_id_index` (`food_id`)
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
            ");
            echo "✓ Order items table created\n";
        } else {
            echo "✓ Order items table already exists\n";
        }

        // 7. Create tables table
        if (!Schema::hasTable('tables')) {
            echo "Creating tables table...\n";
            DB::statement("
                CREATE TABLE `tables` (
                    `id` bigint unsigned NOT NULL AUTO_INCREMENT,
                    `number` varchar(255) NOT NULL,
                    `name` varchar(255) DEFAULT NULL,
                    `capacity` int NOT NULL DEFAULT '4',
                    `status` enum('available','occupied','reserved','maintenance') NOT NULL DEFAULT 'available',
                    `location` varchar(255) DEFAULT NULL,
                    `qr_code` varchar(255) DEFAULT NULL,
                    `is_active` tinyint(1) NOT NULL DEFAULT '1',
                    `created_at` timestamp NULL DEFAULT NULL,
                    `updated_at` timestamp NULL DEFAULT NULL,
                    PRIMARY KEY (`id`),
                    UNIQUE KEY `tables_number_unique` (`number`),
                    KEY `tables_status_index` (`status`),
                    KEY `tables_is_active_index` (`is_active`)
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
            ");
            echo "✓ Tables table created\n";
        } else {
            echo "✓ Tables table already exists\n";
        }

        // Insert demo data
        echo "\n🌱 Inserting demo data...\n";

        // Demo food categories
        if (DB::table('food_categories')->count() == 0) {
            DB::table('food_categories')->insert([
                ['name' => 'Appetizers', 'slug' => 'appetizers', 'description' => 'Start your meal with our delicious appetizers', 'sort_order' => 1, 'is_active' => 1, 'created_at' => now(), 'updated_at' => now()],
                ['name' => 'Main Courses', 'slug' => 'main-courses', 'description' => 'Hearty main dishes to satisfy your hunger', 'sort_order' => 2, 'is_active' => 1, 'created_at' => now(), 'updated_at' => now()],
                ['name' => 'Desserts', 'slug' => 'desserts', 'description' => 'Sweet treats to end your meal', 'sort_order' => 3, 'is_active' => 1, 'created_at' => now(), 'updated_at' => now()],
                ['name' => 'Beverages', 'slug' => 'beverages', 'description' => 'Refreshing drinks and beverages', 'sort_order' => 4, 'is_active' => 1, 'created_at' => now(), 'updated_at' => now()],
            ]);
            echo "✓ Demo categories inserted\n";
        }

        // Demo food items
        if (DB::table('food')->count() == 0) {
            DB::table('food')->insert([
                ['name' => 'Caesar Salad', 'slug' => 'caesar-salad', 'description' => 'Fresh romaine lettuce with caesar dressing', 'price' => 12.99, 'category_id' => 1, 'is_active' => 1, 'is_available' => 1, 'created_at' => now(), 'updated_at' => now()],
                ['name' => 'Grilled Chicken', 'slug' => 'grilled-chicken', 'description' => 'Tender grilled chicken breast with herbs', 'price' => 18.99, 'category_id' => 2, 'is_active' => 1, 'is_available' => 1, 'created_at' => now(), 'updated_at' => now()],
                ['name' => 'Chocolate Cake', 'slug' => 'chocolate-cake', 'description' => 'Rich chocolate cake with cream frosting', 'price' => 8.99, 'category_id' => 3, 'is_active' => 1, 'is_available' => 1, 'created_at' => now(), 'updated_at' => now()],
                ['name' => 'Fresh Orange Juice', 'slug' => 'fresh-orange-juice', 'description' => 'Freshly squeezed orange juice', 'price' => 4.99, 'category_id' => 4, 'is_active' => 1, 'is_available' => 1, 'created_at' => now(), 'updated_at' => now()],
            ]);
            echo "✓ Demo food items inserted\n";
        }

        // Copy food data to menu_items for consistency
        if (DB::table('menu_items')->count() == 0) {
            $foodItems = DB::table('food')->get();
            foreach ($foodItems as $food) {
                DB::table('menu_items')->insert([
                    'name' => $food->name,
                    'slug' => $food->slug,
                    'description' => $food->description,
                    'price' => $food->price,
                    'category_id' => $food->category_id,
                    'is_active' => $food->is_active,
                    'is_available' => $food->is_available,
                    'created_at' => $food->created_at,
                    'updated_at' => $food->updated_at,
                ]);
            }
            echo "✓ Menu items synced with food data\n";
        }

        // Demo tables
        if (DB::table('tables')->count() == 0) {
            for ($i = 1; $i <= 10; $i++) {
                DB::table('tables')->insert([
                    'number' => "T{$i}",
                    'name' => "Table {$i}",
                    'capacity' => rand(2, 8),
                    'status' => 'available',
                    'is_active' => 1,
                    'created_at' => now(),
                    'updated_at' => now(),
                ]);
            }
            echo "✓ Demo tables inserted\n";
        }

        // Verify all tables
        echo "\n📋 Verifying tenant database tables:\n";
        $requiredTables = [
            'sessions', 'cache', 'restaurants', 'food_categories', 
            'menu_items', 'food', 'customers', 'orders', 'order_items', 'tables'
        ];
        
        foreach ($requiredTables as $table) {
            if (Schema::hasTable($table)) {
                $count = DB::table($table)->count();
                echo "  ✓ {$table} ({$count} records)\n";
            } else {
                echo "  ❌ {$table} (missing)\n";
            }
        }
    });

    echo "\n🎉 Tenant database setup completed!\n";
    echo "\n📋 Summary:\n";
    echo "✅ Food categories table created\n";
    echo "✅ Menu items table created\n";
    echo "✅ Food table created (for compatibility)\n";
    echo "✅ Customers table created\n";
    echo "✅ Orders and order items tables created\n";
    echo "✅ Tables table created\n";
    echo "✅ Demo data inserted\n";
    echo "\n🧪 Test the fix:\n";
    echo "1. Visit: http://demo-restaurant.localhost:8000/dashboard\n";
    echo "2. Should load without 'food table doesn't exist' error\n";
    echo "3. Menu functionality should work properly\n";
    echo "\n✅ The missing table error should be resolved!\n";

} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
    echo "File: " . $e->getFile() . ":" . $e->getLine() . "\n";
}
