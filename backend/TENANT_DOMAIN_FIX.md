# 🔧 Tenant Domain Fix Guide

## Issue: `TenantCouldNotBeIdentifiedOnDomainException`

**Error**: Tenant could not be identified on domain `demo-restaurant.localhost`

**Cause**: The demo tenant or its domain is not properly created in the database.

## 🚀 Quick Fix Solutions

### Solution 1: Laravel Artisan (Recommended)

```bash
php artisan tinker
```

Then paste this code:
```php
$tenant = App\Models\Tenant::updateOrCreate(
    ['id' => 'demo-restaurant'],
    [
        'name' => 'Demo Restaurant',
        'email' => '<EMAIL>',
        'subscription_status' => 'active'
    ]
);

$tenant->domains()->updateOrCreate(['domain' => 'demo-restaurant.localhost']);

echo "✅ Tenant domain fixed!";
```

### Solution 2: Run Fix Script

```bash
php quick_fix_tenant.php
```

**Note**: Update database credentials in the script if needed.

### Solution 3: Manual Database Insert

Run this SQL in your database:

```sql
-- Create tenant
INSERT IGNORE INTO tenants (id, name, email, subscription_status, created_at, updated_at)
VALUES ('demo-restaurant', 'Demo Restaurant', '<EMAIL>', 'active', NOW(), NOW());

-- Create domain
INSERT IGNORE INTO domains (domain, tenant_id, created_at, updated_at)
VALUES ('demo-restaurant.localhost', 'demo-restaurant', NOW(), NOW());
```

### Solution 4: Re-run Manual Seeding

```bash
php manual_seed.php
```

This will recreate all users, roles, and the demo tenant.

## 🔍 Verification

After applying any fix, verify the setup:

```bash
php artisan tinker --execute="
echo 'Tenant: ' . App\Models\Tenant::find('demo-restaurant')?->name ?? 'NOT FOUND';
echo PHP_EOL;
echo 'Domain: ' . App\Models\Tenant::find('demo-restaurant')?->domains?->first()?->domain ?? 'NOT FOUND';
"
```

Expected output:
```
Tenant: Demo Restaurant
Domain: demo-restaurant.localhost
```

## 🌐 Access URLs

After fixing:

- **Central Admin**: http://localhost:8000/login
- **Restaurant**: http://demo-restaurant.localhost:8000/login

## 🔐 Login Credentials

| Role | Email | Password |
|------|-------|----------|
| **Super Admin** | <EMAIL> | Restaurant@2024 |
| **Restaurant Manager** | <EMAIL> | Manager@2024 |
| **Waiter** | <EMAIL> | Waiter@2024 |
| **Kitchen Staff** | <EMAIL> | Kitchen@2024 |
| **Delivery Driver** | <EMAIL> | Delivery@2024 |

## 🐛 Troubleshooting

### Issue: Database Connection Error
**Solution**: Update database credentials in `.env` file

### Issue: Still Getting 404
**Solution**: 
1. Clear Laravel caches: `php artisan optimize:clear`
2. Restart the server: `php artisan serve`
3. Check `/etc/hosts` file for domain mapping

### Issue: Tenant Database Not Created
**Solution**: Tenant databases are created automatically on first access. This is normal behavior.

### Issue: Permission Denied
**Solution**: Make sure the user has the correct role assigned:
```bash
php artisan tinker --execute="
\$user = App\Models\User::where('email', '<EMAIL>')->first();
if (\$user && !\$user->hasRole('restaurant_manager')) {
    \$user->assignRole('restaurant_manager');
    echo 'Role assigned!';
}
"
```

## 📋 Complete Reset (If All Else Fails)

If you need to completely reset the system:

```bash
# 1. Reset database
php artisan migrate:fresh

# 2. Recreate everything
php manual_seed.php

# 3. Verify setup
php verify_setup.php

# 4. Start server
php artisan serve
```

## ✅ Success Indicators

You'll know it's working when:

1. ✅ No `TenantCouldNotBeIdentifiedOnDomainException` error
2. ✅ `http://demo-restaurant.localhost:8000` loads the login page
3. ✅ Login redirects to the correct role-based dashboard
4. ✅ No 404 errors on dashboard pages

## 🎯 Expected Behavior

**Correct Flow:**
1. Visit `http://demo-restaurant.localhost:8000`
2. See restaurant login page (not 404)
3. Login with restaurant staff credentials
4. Redirect to role-specific dashboard
5. Dashboard loads without errors

**If you see the login page, the tenant domain is working correctly!**

---

**🎉 Once fixed, your multi-tenant restaurant system will be fully operational!**
