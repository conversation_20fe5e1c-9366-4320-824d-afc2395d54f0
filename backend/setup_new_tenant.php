<?php

// Standardized new tenant setup script
require_once 'vendor/autoload.php';

$app = require_once 'bootstrap/app.php';
$kernel = $app->make('Illuminate\Contracts\Console\Kernel');
$kernel->bootstrap();

use App\Models\Tenant;
use App\Models\SubscriptionPlan;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

// Get tenant ID from command line or use default
$tenantId = $argv[1] ?? 'demo-restaurant';
$tenantName = $argv[2] ?? 'Demo Restaurant';

echo "=== Setting Up New Tenant: {$tenantName} ===\n\n";

try {
    // 1. Create tenant record
    echo "1. Creating tenant record...\n";
    
    $plan = SubscriptionPlan::first();
    
    $tenant = Tenant::updateOrCreate(
        ['id' => $tenantId],
        [
            'name' => $tenantName,
            'email' => strtolower(str_replace(' ', '', $tenantName)) . '@restaurant.com',
            'subscription_plan_id' => $plan?->id,
            'subscription_status' => 'active',
        ]
    );
    
    echo "   ✓ Tenant: {$tenant->name}\n";

    // 2. Create domain
    echo "2. Creating domain...\n";
    
    $domain = $tenant->domains()->updateOrCreate([
        'domain' => $tenantId . '.localhost'
    ]);
    
    echo "   ✓ Domain: {$domain->domain}\n";

    // 3. Setup tenant database with standardized schema
    echo "3. Setting up tenant database...\n";
    
    $tenant->run(function () use ($tenantName) {
        // Essential Laravel tables
        $laravelTables = [
            'sessions' => "
                CREATE TABLE IF NOT EXISTS `sessions` (
                    `id` varchar(255) NOT NULL,
                    `user_id` bigint unsigned DEFAULT NULL,
                    `ip_address` varchar(45) DEFAULT NULL,
                    `user_agent` text,
                    `payload` longtext NOT NULL,
                    `last_activity` int NOT NULL,
                    PRIMARY KEY (`id`),
                    KEY `sessions_user_id_index` (`user_id`),
                    KEY `sessions_last_activity_index` (`last_activity`)
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4
            ",
            'cache' => "
                CREATE TABLE IF NOT EXISTS `cache` (
                    `key` varchar(255) NOT NULL,
                    `value` mediumtext NOT NULL,
                    `expiration` int NOT NULL,
                    PRIMARY KEY (`key`)
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4
            ",
            'migrations' => "
                CREATE TABLE IF NOT EXISTS `migrations` (
                    `id` int unsigned NOT NULL AUTO_INCREMENT,
                    `migration` varchar(255) NOT NULL,
                    `batch` int NOT NULL,
                    PRIMARY KEY (`id`)
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4
            "
        ];

        // Restaurant-specific tables with standardized schema
        $restaurantTables = [
            'restaurants' => "
                CREATE TABLE IF NOT EXISTS `restaurants` (
                    `id` bigint unsigned NOT NULL AUTO_INCREMENT,
                    `name` varchar(255) NOT NULL,
                    `slug` varchar(255) NOT NULL,
                    `description` text,
                    `phone` varchar(255),
                    `email` varchar(255),
                    `address` text,
                    `is_active` tinyint(1) NOT NULL DEFAULT '1',
                    `created_at` timestamp NULL DEFAULT NULL,
                    `updated_at` timestamp NULL DEFAULT NULL,
                    PRIMARY KEY (`id`),
                    UNIQUE KEY `restaurants_slug_unique` (`slug`)
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4
            ",
            'food_categories' => "
                CREATE TABLE IF NOT EXISTS `food_categories` (
                    `id` bigint unsigned NOT NULL AUTO_INCREMENT,
                    `name` varchar(255) NOT NULL,
                    `slug` varchar(255) NOT NULL,
                    `description` text,
                    `sort_order` int NOT NULL DEFAULT '0',
                    `is_active` tinyint(1) NOT NULL DEFAULT '1',
                    `created_at` timestamp NULL DEFAULT NULL,
                    `updated_at` timestamp NULL DEFAULT NULL,
                    PRIMARY KEY (`id`),
                    UNIQUE KEY `food_categories_slug_unique` (`slug`)
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4
            ",
            'food' => "
                CREATE TABLE IF NOT EXISTS `food` (
                    `id` bigint unsigned NOT NULL AUTO_INCREMENT,
                    `name` varchar(255) NOT NULL,
                    `slug` varchar(255) NOT NULL,
                    `description` text,
                    `price` decimal(10,2) NOT NULL,
                    `category_id` bigint unsigned DEFAULT NULL,
                    `image` varchar(255) DEFAULT NULL,
                    `is_active` tinyint(1) NOT NULL DEFAULT '1',
                    `is_available` tinyint(1) NOT NULL DEFAULT '1',
                    `is_featured` tinyint(1) NOT NULL DEFAULT '0',
                    `sort_order` int NOT NULL DEFAULT '0',
                    `created_at` timestamp NULL DEFAULT NULL,
                    `updated_at` timestamp NULL DEFAULT NULL,
                    PRIMARY KEY (`id`),
                    UNIQUE KEY `food_slug_unique` (`slug`),
                    KEY `food_is_active_index` (`is_active`),
                    KEY `food_is_available_index` (`is_available`)
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4
            ",
            'menu_items' => "
                CREATE TABLE IF NOT EXISTS `menu_items` (
                    `id` bigint unsigned NOT NULL AUTO_INCREMENT,
                    `name` varchar(255) NOT NULL,
                    `slug` varchar(255) NOT NULL,
                    `description` text,
                    `price` decimal(10,2) NOT NULL,
                    `category_id` bigint unsigned DEFAULT NULL,
                    `image` varchar(255) DEFAULT NULL,
                    `is_active` tinyint(1) NOT NULL DEFAULT '1',
                    `is_available` tinyint(1) NOT NULL DEFAULT '1',
                    `is_featured` tinyint(1) NOT NULL DEFAULT '0',
                    `sort_order` int NOT NULL DEFAULT '0',
                    `is_vegetarian` tinyint(1) NOT NULL DEFAULT '0',
                    `is_vegan` tinyint(1) NOT NULL DEFAULT '0',
                    `is_gluten_free` tinyint(1) NOT NULL DEFAULT '0',
                    `created_at` timestamp NULL DEFAULT NULL,
                    `updated_at` timestamp NULL DEFAULT NULL,
                    PRIMARY KEY (`id`),
                    UNIQUE KEY `menu_items_slug_unique` (`slug`),
                    KEY `menu_items_is_active_index` (`is_active`),
                    KEY `menu_items_is_available_index` (`is_available`)
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4
            ",
            'customers' => "
                CREATE TABLE IF NOT EXISTS `customers` (
                    `id` bigint unsigned NOT NULL AUTO_INCREMENT,
                    `name` varchar(255) NOT NULL,
                    `email` varchar(255) DEFAULT NULL,
                    `phone` varchar(255) DEFAULT NULL,
                    `address` text,
                    `is_active` tinyint(1) NOT NULL DEFAULT '1',
                    `created_at` timestamp NULL DEFAULT NULL,
                    `updated_at` timestamp NULL DEFAULT NULL,
                    PRIMARY KEY (`id`)
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4
            ",
            'orders' => "
                CREATE TABLE IF NOT EXISTS `orders` (
                    `id` bigint unsigned NOT NULL AUTO_INCREMENT,
                    `order_number` varchar(255) NOT NULL,
                    `customer_id` bigint unsigned DEFAULT NULL,
                    `status` enum('pending','confirmed','preparing','ready','completed','cancelled') NOT NULL DEFAULT 'pending',
                    `total_amount` decimal(10,2) NOT NULL DEFAULT '0.00',
                    `created_at` timestamp NULL DEFAULT NULL,
                    `updated_at` timestamp NULL DEFAULT NULL,
                    PRIMARY KEY (`id`),
                    UNIQUE KEY `orders_order_number_unique` (`order_number`)
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4
            ",
            'tables' => "
                CREATE TABLE IF NOT EXISTS `tables` (
                    `id` bigint unsigned NOT NULL AUTO_INCREMENT,
                    `number` varchar(255) NOT NULL,
                    `capacity` int NOT NULL DEFAULT '4',
                    `status` enum('available','occupied','reserved') NOT NULL DEFAULT 'available',
                    `is_active` tinyint(1) NOT NULL DEFAULT '1',
                    `created_at` timestamp NULL DEFAULT NULL,
                    `updated_at` timestamp NULL DEFAULT NULL,
                    PRIMARY KEY (`id`),
                    UNIQUE KEY `tables_number_unique` (`number`)
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4
            "
        ];

        // Create all tables
        $allTables = array_merge($laravelTables, $restaurantTables);
        
        foreach ($allTables as $tableName => $sql) {
            try {
                DB::statement($sql);
                echo "   ✓ Created table: {$tableName}\n";
            } catch (Exception $e) {
                echo "   ⚠ Table {$tableName}: " . $e->getMessage() . "\n";
            }
        }

        // Insert restaurant record
        DB::table('restaurants')->updateOrInsert(
            ['slug' => Str::slug($tenantName)],
            [
                'name' => $tenantName,
                'slug' => Str::slug($tenantName),
                'description' => "Welcome to {$tenantName}",
                'is_active' => 1,
                'created_at' => now(),
                'updated_at' => now(),
            ]
        );

        echo "   ✓ Restaurant record created\n";
    });

    echo "\n🎉 Tenant setup completed!\n";
    echo "\n📋 Tenant Information:\n";
    echo "   ID: {$tenant->id}\n";
    echo "   Name: {$tenant->name}\n";
    echo "   Domain: {$domain->domain}\n";
    echo "   Database: tenant_{$tenant->id}\n";
    echo "\n🌐 Access URL:\n";
    echo "   http://{$domain->domain}:8000\n";
    echo "\n✅ Ready for use!\n";

} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
    echo "File: " . $e->getFile() . ":" . $e->getLine() . "\n";
}

// Usage examples:
// php setup_new_tenant.php demo-restaurant "Demo Restaurant"
// php setup_new_tenant.php pizza-palace "Pizza Palace"
// php setup_new_tenant.php burger-joint "Burger Joint"
