<?php

// Test tenant migration script
require_once 'vendor/autoload.php';

$app = require_once 'bootstrap/app.php';
$kernel = $app->make('Illuminate\Contracts\Console\Kernel');
$kernel->bootstrap();

use App\Models\Tenant;
use Illuminate\Support\Facades\Artisan;
use Illuminate\Support\Facades\DB;

echo "=== Testing Tenant Migration ===\n\n";

try {
    // Check if tenant exists
    $tenant = Tenant::find('demo-restaurant');
    if (!$tenant) {
        echo "Creating demo tenant...\n";
        $tenant = Tenant::create([
            'id' => 'demo-restaurant',
            'name' => 'Demo Restaurant',
            'email' => '<EMAIL>',
            'subscription_status' => 'active'
        ]);
        
        // Create domain
        $tenant->domains()->create([
            'domain' => 'demo-restaurant.localhost'
        ]);
        echo "✓ Tenant created\n";
    } else {
        echo "✓ Tenant already exists: {$tenant->name}\n";
    }

    // Check if tenant database exists
    $databaseName = 'tenant_demo-restaurant';
    $databases = DB::select('SHOW DATABASES');
    $dbExists = false;
    foreach ($databases as $db) {
        if ($db->Database === $databaseName) {
            $dbExists = true;
            break;
        }
    }

    if ($dbExists) {
        echo "✓ Tenant database exists: {$databaseName}\n";
    } else {
        echo "⚠ Tenant database does not exist, will be created during migration\n";
    }

    // Try to run tenant migrations
    echo "\nRunning tenant migrations...\n";
    try {
        Artisan::call('tenants:migrate', ['--tenants' => ['demo-restaurant']]);
        echo "✓ Tenant migrations completed successfully!\n";
        
        // Check some tenant tables
        $tenant->run(function () {
            $tables = ['restaurants', 'menu_items', 'orders', 'customers'];
            foreach ($tables as $table) {
                if (Schema::hasTable($table)) {
                    echo "   ✓ Table '{$table}' exists\n";
                } else {
                    echo "   ⚠ Table '{$table}' missing\n";
                }
            }
        });
        
    } catch (Exception $e) {
        echo "✗ Tenant migration failed: " . $e->getMessage() . "\n";
        echo "Error details: " . $e->getFile() . ":" . $e->getLine() . "\n";
    }

    echo "\n=== Test Completed ===\n";

} catch (Exception $e) {
    echo "Error: " . $e->getMessage() . "\n";
    echo "File: " . $e->getFile() . ":" . $e->getLine() . "\n";
}
