# 🔧 Tenant Sessions Table Fix

## Issue: `Table 'tenant_demo-restaurant.sessions' doesn't exist`

**Error**: The tenant database exists but doesn't have the required Laravel tables (sessions, cache, jobs).

**Cause**: Tenant migrations haven't been run yet, so the tenant database is empty.

## 🚀 Quick Fix Solutions

### Solution 1: Run Tenant Migrations (Recommended)

```bash
php artisan tenants:migrate --tenants=demo-restaurant
```

If that fails, try:
```bash
php artisan tenants:migrate --tenants=demo-restaurant --force
```

### Solution 2: Laravel Artisan Manual Migration

```bash
php artisan tinker
```

Then paste:
```php
$tenant = App\Models\Tenant::find('demo-restaurant');
$tenant->run(function () {
    Artisan::call('migrate', [
        '--path' => 'database/migrations/tenant',
        '--force' => true
    ]);
    echo "Tenant migrations completed!";
});
```

### Solution 3: Run Fix Script

```bash
php fix_tenant_migrations.php
```

### Solution 4: Quick Sessions Table Fix

```bash
php fix_sessions_table.php
```

### Solution 5: Manual SQL Fix

Connect to your MySQL and run:

```sql
-- Switch to tenant database
USE `tenant_demo-restaurant`;

-- Create sessions table
CREATE TABLE `sessions` (
    `id` varchar(255) NOT NULL,
    `user_id` bigint unsigned DEFAULT NULL,
    `ip_address` varchar(45) DEFAULT NULL,
    `user_agent` text,
    `payload` longtext NOT NULL,
    `last_activity` int NOT NULL,
    PRIMARY KEY (`id`),
    KEY `sessions_user_id_index` (`user_id`),
    KEY `sessions_last_activity_index` (`last_activity`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- Create cache table
CREATE TABLE `cache` (
    `key` varchar(255) NOT NULL,
    `value` mediumtext NOT NULL,
    `expiration` int NOT NULL,
    PRIMARY KEY (`key`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- Create cache_locks table
CREATE TABLE `cache_locks` (
    `key` varchar(255) NOT NULL,
    `owner` varchar(255) NOT NULL,
    `expiration` int NOT NULL,
    PRIMARY KEY (`key`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- Create jobs table
CREATE TABLE `jobs` (
    `id` bigint unsigned NOT NULL AUTO_INCREMENT,
    `queue` varchar(255) NOT NULL,
    `payload` longtext NOT NULL,
    `attempts` tinyint unsigned NOT NULL,
    `reserved_at` int unsigned DEFAULT NULL,
    `available_at` int unsigned NOT NULL,
    `created_at` int unsigned NOT NULL,
    PRIMARY KEY (`id`),
    KEY `jobs_queue_index` (`queue`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
```

## 🔍 Verification

Check if the fix worked:

```bash
php artisan tinker --execute="
\$tenant = App\Models\Tenant::find('demo-restaurant');
\$tenant->run(function () {
    echo 'Sessions table exists: ' . (Schema::hasTable('sessions') ? 'YES' : 'NO') . PHP_EOL;
    echo 'Cache table exists: ' . (Schema::hasTable('cache') ? 'YES' : 'NO') . PHP_EOL;
    echo 'Jobs table exists: ' . (Schema::hasTable('jobs') ? 'YES' : 'NO') . PHP_EOL;
});
"
```

Expected output:
```
Sessions table exists: YES
Cache table exists: YES
Jobs table exists: YES
```

## 🌐 Test Access

After fixing, test the tenant access:

```bash
curl -I http://demo-restaurant.localhost:8000
```

Should return `200 OK` instead of an error.

## 🔐 Login Test

Try logging in:

1. **URL**: http://demo-restaurant.localhost:8000/login
2. **Credentials**: <EMAIL> / Manager@2024
3. **Expected**: Redirect to `/manager/dashboard`

## 🐛 Alternative: Use Central Sessions

If tenant sessions keep causing issues, you can configure Laravel to use central sessions for all tenants by updating `config/tenancy.php`:

```php
'bootstrappers' => [
    Stancl\Tenancy\Bootstrappers\DatabaseTenancyBootstrapper::class,
    // Comment out CacheTenancyBootstrapper to use central cache/sessions
    // Stancl\Tenancy\Bootstrappers\CacheTenancyBootstrapper::class,
    Stancl\Tenancy\Bootstrappers\FilesystemTenancyBootstrapper::class,
    Stancl\Tenancy\Bootstrappers\QueueTenancyBootstrapper::class,
],
```

Then restart the server:
```bash
php artisan serve
```

## 📋 Complete Reset (If Needed)

If all else fails, reset everything:

```bash
# 1. Drop tenant database
mysql -u root -p -e "DROP DATABASE IF EXISTS \`tenant_demo-restaurant\`;"

# 2. Reset central database
php artisan migrate:fresh

# 3. Recreate everything
php manual_seed.php

# 4. Run tenant migrations
php artisan tenants:migrate --tenants=demo-restaurant

# 5. Start server
php artisan serve
```

## ✅ Success Indicators

You'll know it's working when:

1. ✅ No "sessions table doesn't exist" error
2. ✅ `http://demo-restaurant.localhost:8000` loads login page
3. ✅ Login works and redirects to dashboard
4. ✅ Dashboard loads without database errors

## 🎯 Root Cause

This issue occurs because:
1. ✅ Tenant record exists in central database
2. ✅ Tenant domain is configured correctly  
3. ❌ Tenant database exists but is empty (no migrations run)

The fix is to run the tenant migrations to create all required tables.

---

**🎉 Once the tenant migrations are complete, your restaurant system will work perfectly!**
