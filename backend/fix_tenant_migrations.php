<?php

// Fix tenant migrations script
require_once 'vendor/autoload.php';

$app = require_once 'bootstrap/app.php';
$kernel = $app->make('Illuminate\Contracts\Console\Kernel');
$kernel->bootstrap();

use App\Models\Tenant;
use Illuminate\Support\Facades\Artisan;
use Illuminate\Support\Facades\DB;

echo "=== Fixing Tenant Migrations ===\n\n";

try {
    // Check if tenant exists
    $tenant = Tenant::find('demo-restaurant');
    
    if (!$tenant) {
        echo "❌ Demo tenant not found. Creating it first...\n";
        
        $tenant = Tenant::create([
            'id' => 'demo-restaurant',
            'name' => 'Demo Restaurant',
            'email' => '<EMAIL>',
            'phone' => '******-0123',
            'address' => '123 Main Street',
            'city' => 'New York',
            'state' => 'NY',
            'country' => 'USA',
            'postal_code' => '10001',
            'timezone' => 'America/New_York',
            'currency' => 'USD',
            'language' => 'en',
            'subscription_status' => 'active',
            'trial_ends_at' => now()->addDays(30),
        ]);
        
        // Create domain
        $tenant->domains()->create([
            'domain' => 'demo-restaurant.localhost'
        ]);
        
        echo "✓ Demo tenant created\n";
    } else {
        echo "✓ Demo tenant exists: {$tenant->name}\n";
    }

    // Check if tenant database exists
    $databaseName = 'tenant_demo-restaurant';
    $databases = DB::select('SHOW DATABASES');
    $dbExists = false;
    foreach ($databases as $db) {
        if ($db->Database === $databaseName) {
            $dbExists = true;
            break;
        }
    }

    if (!$dbExists) {
        echo "Creating tenant database: {$databaseName}\n";
        DB::statement("CREATE DATABASE `{$databaseName}`");
        echo "✓ Tenant database created\n";
    } else {
        echo "✓ Tenant database exists: {$databaseName}\n";
    }

    // Run tenant migrations
    echo "\nRunning tenant migrations...\n";
    try {
        // Use the tenant context to run migrations
        $tenant->run(function () {
            // Run the migrations
            Artisan::call('migrate', [
                '--path' => 'database/migrations/tenant',
                '--force' => true
            ]);
            
            echo "✓ Tenant migrations completed\n";
            
            // Check if sessions table exists now
            if (Schema::hasTable('sessions')) {
                echo "✓ Sessions table created\n";
            } else {
                echo "⚠ Sessions table still missing\n";
            }
            
            // List some key tables
            $tables = ['sessions', 'restaurants', 'menu_items', 'orders', 'customers'];
            echo "\nTenant database tables:\n";
            foreach ($tables as $table) {
                if (Schema::hasTable($table)) {
                    echo "  ✓ {$table}\n";
                } else {
                    echo "  ✗ {$table} (missing)\n";
                }
            }
        });
        
    } catch (Exception $e) {
        echo "❌ Tenant migration failed: " . $e->getMessage() . "\n";
        
        // Try alternative approach - run migrations directly
        echo "\nTrying alternative migration approach...\n";
        
        try {
            Artisan::call('tenants:migrate', [
                '--tenants' => ['demo-restaurant'],
                '--force' => true
            ]);
            echo "✓ Alternative migration successful\n";
        } catch (Exception $e2) {
            echo "❌ Alternative migration also failed: " . $e2->getMessage() . "\n";
            
            // Manual session table creation
            echo "\nCreating sessions table manually...\n";
            $tenant->run(function () {
                DB::statement("
                    CREATE TABLE IF NOT EXISTS `sessions` (
                        `id` varchar(255) NOT NULL,
                        `user_id` bigint unsigned DEFAULT NULL,
                        `ip_address` varchar(45) DEFAULT NULL,
                        `user_agent` text,
                        `payload` longtext NOT NULL,
                        `last_activity` int NOT NULL,
                        PRIMARY KEY (`id`),
                        KEY `sessions_user_id_index` (`user_id`),
                        KEY `sessions_last_activity_index` (`last_activity`)
                    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
                ");
                echo "✓ Sessions table created manually\n";
            });
        }
    }

    echo "\n=== Fix Completed ===\n";
    echo "You can now access: http://demo-restaurant.localhost:8000\n";

} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
    echo "File: " . $e->getFile() . ":" . $e->getLine() . "\n";
}
