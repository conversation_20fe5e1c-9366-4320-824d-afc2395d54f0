<?php

// Complete tenant fix script
echo "=== Complete Tenant Migration Fix ===\n";

// Database configuration
$host = '127.0.0.1';
$centralDb = 'restaurant_management';
$tenantDb = 'tenant_demo-restaurant';
$username = 'root';
$password = '';

try {
    // Connect to central database first
    $centralPdo = new PDO("mysql:host=$host;dbname=$centralDb", $username, $password);
    $centralPdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    echo "✓ Connected to central database\n";

    // Check if tenant exists in central database
    $stmt = $centralPdo->prepare("SELECT * FROM tenants WHERE id = ?");
    $stmt->execute(['demo-restaurant']);
    $tenant = $stmt->fetch();

    if (!$tenant) {
        echo "Creating demo tenant in central database...\n";
        
        $stmt = $centralPdo->prepare("
            INSERT INTO tenants (id, name, email, subscription_status, created_at, updated_at)
            VALUES (?, ?, ?, ?, ?, ?)
        ");
        
        $stmt->execute([
            'demo-restaurant',
            'Demo Restaurant',
            '<EMAIL>',
            'active',
            date('Y-m-d H:i:s'),
            date('Y-m-d H:i:s')
        ]);
        
        echo "✓ Tenant created in central database\n";
    } else {
        echo "✓ Tenant exists in central database\n";
    }

    // Check if domain exists
    $stmt = $centralPdo->prepare("SELECT * FROM domains WHERE domain = ?");
    $stmt->execute(['demo-restaurant.localhost']);
    $domain = $stmt->fetch();

    if (!$domain) {
        echo "Creating domain in central database...\n";
        
        $stmt = $centralPdo->prepare("
            INSERT INTO domains (domain, tenant_id, created_at, updated_at)
            VALUES (?, ?, ?, ?)
        ");
        
        $stmt->execute([
            'demo-restaurant.localhost',
            'demo-restaurant',
            date('Y-m-d H:i:s'),
            date('Y-m-d H:i:s')
        ]);
        
        echo "✓ Domain created in central database\n";
    } else {
        echo "✓ Domain exists in central database\n";
    }

    // Create tenant database if it doesn't exist
    $stmt = $centralPdo->query("SHOW DATABASES LIKE '$tenantDb'");
    if ($stmt->rowCount() == 0) {
        echo "Creating tenant database: $tenantDb\n";
        $centralPdo->exec("CREATE DATABASE `$tenantDb`");
        echo "✓ Tenant database created\n";
    } else {
        echo "✓ Tenant database exists\n";
    }

    // Connect to tenant database
    $tenantPdo = new PDO("mysql:host=$host;dbname=$tenantDb", $username, $password);
    $tenantPdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    echo "✓ Connected to tenant database\n";

    // Create essential Laravel tables first
    echo "\nCreating essential Laravel tables...\n";

    // Sessions table
    $tenantPdo->exec("
        CREATE TABLE IF NOT EXISTS `sessions` (
            `id` varchar(255) NOT NULL,
            `user_id` bigint unsigned DEFAULT NULL,
            `ip_address` varchar(45) DEFAULT NULL,
            `user_agent` text,
            `payload` longtext NOT NULL,
            `last_activity` int NOT NULL,
            PRIMARY KEY (`id`),
            KEY `sessions_user_id_index` (`user_id`),
            KEY `sessions_last_activity_index` (`last_activity`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    ");
    echo "✓ Sessions table created\n";

    // Cache table
    $tenantPdo->exec("
        CREATE TABLE IF NOT EXISTS `cache` (
            `key` varchar(255) NOT NULL,
            `value` mediumtext NOT NULL,
            `expiration` int NOT NULL,
            PRIMARY KEY (`key`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    ");
    echo "✓ Cache table created\n";

    // Cache locks table
    $tenantPdo->exec("
        CREATE TABLE IF NOT EXISTS `cache_locks` (
            `key` varchar(255) NOT NULL,
            `owner` varchar(255) NOT NULL,
            `expiration` int NOT NULL,
            PRIMARY KEY (`key`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    ");
    echo "✓ Cache locks table created\n";

    // Jobs table
    $tenantPdo->exec("
        CREATE TABLE IF NOT EXISTS `jobs` (
            `id` bigint unsigned NOT NULL AUTO_INCREMENT,
            `queue` varchar(255) NOT NULL,
            `payload` longtext NOT NULL,
            `attempts` tinyint unsigned NOT NULL,
            `reserved_at` int unsigned DEFAULT NULL,
            `available_at` int unsigned NOT NULL,
            `created_at` int unsigned NOT NULL,
            PRIMARY KEY (`id`),
            KEY `jobs_queue_index` (`queue`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    ");
    echo "✓ Jobs table created\n";

    // Failed jobs table
    $tenantPdo->exec("
        CREATE TABLE IF NOT EXISTS `failed_jobs` (
            `id` bigint unsigned NOT NULL AUTO_INCREMENT,
            `uuid` varchar(255) NOT NULL,
            `connection` text NOT NULL,
            `queue` text NOT NULL,
            `payload` longtext NOT NULL,
            `exception` longtext NOT NULL,
            `failed_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
            PRIMARY KEY (`id`),
            UNIQUE KEY `failed_jobs_uuid_unique` (`uuid`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    ");
    echo "✓ Failed jobs table created\n";

    // Migrations table
    $tenantPdo->exec("
        CREATE TABLE IF NOT EXISTS `migrations` (
            `id` int unsigned NOT NULL AUTO_INCREMENT,
            `migration` varchar(255) NOT NULL,
            `batch` int NOT NULL,
            PRIMARY KEY (`id`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    ");
    echo "✓ Migrations table created\n";

    // Create a basic restaurants table
    $tenantPdo->exec("
        CREATE TABLE IF NOT EXISTS `restaurants` (
            `id` bigint unsigned NOT NULL AUTO_INCREMENT,
            `name` varchar(255) NOT NULL,
            `slug` varchar(255) NOT NULL,
            `description` text,
            `phone` varchar(255),
            `email` varchar(255),
            `address` text,
            `city` varchar(255),
            `state` varchar(255),
            `postal_code` varchar(255),
            `country` varchar(255),
            `timezone` varchar(255) DEFAULT 'UTC',
            `currency` varchar(3) DEFAULT 'USD',
            `is_active` tinyint(1) NOT NULL DEFAULT '1',
            `created_at` timestamp NULL DEFAULT NULL,
            `updated_at` timestamp NULL DEFAULT NULL,
            PRIMARY KEY (`id`),
            UNIQUE KEY `restaurants_slug_unique` (`slug`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    ");
    echo "✓ Restaurants table created\n";

    // Insert demo restaurant data
    $stmt = $tenantPdo->prepare("SELECT COUNT(*) FROM restaurants");
    $stmt->execute();
    $count = $stmt->fetchColumn();

    if ($count == 0) {
        echo "Inserting demo restaurant data...\n";
        $stmt = $tenantPdo->prepare("
            INSERT INTO restaurants (name, slug, description, phone, email, address, city, state, postal_code, country, timezone, currency, created_at, updated_at)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        ");
        
        $stmt->execute([
            'Demo Restaurant',
            'demo-restaurant',
            'A wonderful demo restaurant for testing',
            '******-0123',
            '<EMAIL>',
            '123 Main Street',
            'New York',
            'NY',
            '10001',
            'USA',
            'America/New_York',
            'USD',
            date('Y-m-d H:i:s'),
            date('Y-m-d H:i:s')
        ]);
        
        echo "✓ Demo restaurant data inserted\n";
    } else {
        echo "✓ Restaurant data already exists\n";
    }

    echo "\n=== Tenant Setup Complete ===\n";
    echo "✅ Central database: Tenant and domain configured\n";
    echo "✅ Tenant database: Essential tables created\n";
    echo "✅ Demo data: Restaurant record inserted\n";
    echo "\nYou can now access: http://demo-restaurant.localhost:8000\n";

} catch (PDOException $e) {
    echo "❌ Database error: " . $e->getMessage() . "\n";
}
