<?php

// Verification script for multi-tenant setup
require_once 'vendor/autoload.php';

$app = require_once 'bootstrap/app.php';
$kernel = $app->make('Illuminate\Contracts\Console\Kernel');
$kernel->bootstrap();

use App\Models\User;
use App\Models\Tenant;
use App\Models\SubscriptionPlan;
use Spatie\Permission\Models\Role;
use Spatie\Permission\Models\Permission;
use Illuminate\Support\Facades\Schema;

echo "=== Multi-Tenant Setup Verification ===\n\n";

$errors = [];
$warnings = [];

try {
    // 1. Check Database Connection
    echo "1. Database Connection...\n";
    try {
        DB::connection()->getPdo();
        echo "   ✓ Connected to database successfully\n";
    } catch (Exception $e) {
        $errors[] = "Database connection failed: " . $e->getMessage();
        echo "   ✗ Database connection failed\n";
    }

    // 2. Check Central Tables
    echo "\n2. Central Database Tables...\n";
    $centralTables = [
        'users' => 'User management',
        'tenants' => 'Tenant management', 
        'domains' => 'Domain routing',
        'subscription_plans' => 'Billing plans',
        'roles' => 'User roles',
        'permissions' => 'User permissions',
        'teams' => 'Jetstream teams',
        'personal_access_tokens' => 'API tokens'
    ];

    foreach ($centralTables as $table => $description) {
        if (Schema::hasTable($table)) {
            $count = DB::table($table)->count();
            echo "   ✓ {$table} ({$count} records) - {$description}\n";
        } else {
            $errors[] = "Missing table: {$table}";
            echo "   ✗ Missing table: {$table}\n";
        }
    }

    // 3. Check Tenant Migrations
    echo "\n3. Tenant Migrations...\n";
    $tenantMigrations = glob('database/migrations/tenant/*.php');
    echo "   ✓ Found " . count($tenantMigrations) . " tenant migration files\n";
    
    if (count($tenantMigrations) < 50) {
        $warnings[] = "Expected ~52 tenant migrations, found " . count($tenantMigrations);
    }

    // 4. Check Roles and Permissions
    echo "\n4. Roles and Permissions...\n";
    $expectedRoles = ['admin', 'restaurant_manager', 'waiter', 'kitchen', 'delivery'];
    foreach ($expectedRoles as $roleName) {
        $role = Role::where('name', $roleName)->first();
        if ($role) {
            $permCount = $role->permissions()->count();
            echo "   ✓ Role '{$roleName}' exists with {$permCount} permissions\n";
        } else {
            $errors[] = "Missing role: {$roleName}";
            echo "   ✗ Missing role: {$roleName}\n";
        }
    }

    $totalPermissions = Permission::count();
    echo "   ✓ Total permissions: {$totalPermissions}\n";

    // 5. Check Users
    echo "\n5. Demo Users...\n";
    $expectedUsers = [
        '<EMAIL>' => 'admin',
        '<EMAIL>' => 'restaurant_manager',
        '<EMAIL>' => 'waiter',
        '<EMAIL>' => 'kitchen',
        '<EMAIL>' => 'delivery'
    ];

    foreach ($expectedUsers as $email => $expectedRole) {
        $user = User::where('email', $email)->first();
        if ($user) {
            $hasRole = $user->hasRole($expectedRole);
            $dashboardRoute = $user->getDashboardRoute();
            if ($hasRole) {
                echo "   ✓ {$email} ({$expectedRole}) → {$dashboardRoute}\n";
            } else {
                $errors[] = "User {$email} missing role {$expectedRole}";
                echo "   ✗ {$email} missing role {$expectedRole}\n";
            }
        } else {
            $errors[] = "Missing user: {$email}";
            echo "   ✗ Missing user: {$email}\n";
        }
    }

    // 6. Check Tenants
    echo "\n6. Demo Tenant...\n";
    $tenant = Tenant::find('demo-restaurant');
    if ($tenant) {
        echo "   ✓ Tenant: {$tenant->name}\n";
        
        $domain = $tenant->domains()->first();
        if ($domain) {
            echo "   ✓ Domain: {$domain->domain}\n";
        } else {
            $errors[] = "Demo tenant missing domain";
            echo "   ✗ Demo tenant missing domain\n";
        }

        if ($tenant->subscription_plan_id) {
            $plan = $tenant->subscriptionPlan;
            echo "   ✓ Subscription: {$plan->name}\n";
        } else {
            $warnings[] = "Demo tenant has no subscription plan";
            echo "   ⚠ Demo tenant has no subscription plan\n";
        }
    } else {
        $errors[] = "Demo tenant not found";
        echo "   ✗ Demo tenant not found\n";
    }

    // 7. Check Vue.js Dashboard Pages
    echo "\n7. Dashboard Pages...\n";
    $dashboardPages = [
        'resources/js/Pages/Admin/Dashboard.vue' => 'Super Admin Dashboard',
        'resources/js/Pages/Manager/Dashboard.vue' => 'Restaurant Manager Dashboard',
        'resources/js/Pages/Waiter/Dashboard.vue' => 'Waiter Dashboard',
        'resources/js/Pages/Kitchen/Dashboard.vue' => 'Kitchen Dashboard',
        'resources/js/Pages/Delivery/Dashboard.vue' => 'Delivery Dashboard'
    ];

    foreach ($dashboardPages as $file => $description) {
        if (file_exists($file)) {
            echo "   ✓ {$description}\n";
        } else {
            $errors[] = "Missing dashboard page: {$file}";
            echo "   ✗ Missing: {$description}\n";
        }
    }

    // 8. Check Language Files
    echo "\n8. Language Support...\n";
    $langFiles = [
        'lang/en/dashboard.json' => 'English translations',
        'lang/bn/dashboard.json' => 'Bengali translations'
    ];

    foreach ($langFiles as $file => $description) {
        if (file_exists($file)) {
            echo "   ✓ {$description}\n";
        } else {
            $warnings[] = "Missing language file: {$file}";
            echo "   ⚠ Missing: {$description}\n";
        }
    }

    // Summary
    echo "\n" . str_repeat("=", 50) . "\n";
    echo "VERIFICATION SUMMARY\n";
    echo str_repeat("=", 50) . "\n";

    if (empty($errors)) {
        echo "✅ SETUP SUCCESSFUL - No critical errors found!\n";
    } else {
        echo "❌ SETUP ISSUES - " . count($errors) . " critical error(s) found:\n";
        foreach ($errors as $error) {
            echo "   • {$error}\n";
        }
    }

    if (!empty($warnings)) {
        echo "\n⚠️  WARNINGS - " . count($warnings) . " warning(s):\n";
        foreach ($warnings as $warning) {
            echo "   • {$warning}\n";
        }
    }

    echo "\n" . str_repeat("=", 50) . "\n";
    echo "NEXT STEPS:\n";
    echo "1. Start server: php artisan serve\n";
    echo "2. Visit: http://localhost:8000 (Super Admin)\n";
    echo "3. Visit: http://demo-restaurant.localhost:8000 (Restaurant)\n";
    echo "4. Login with provided credentials\n";
    echo str_repeat("=", 50) . "\n";

} catch (Exception $e) {
    echo "Verification failed: " . $e->getMessage() . "\n";
    echo "File: " . $e->getFile() . ":" . $e->getLine() . "\n";
}
