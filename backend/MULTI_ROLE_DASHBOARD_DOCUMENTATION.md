# Multi-Role Dashboard System Documentation

## Overview

This document describes the comprehensive multi-role dashboard system implemented for the restaurant management application. The system provides role-based access control, personalized dashboards, and real-time data updates for different user types.

## System Architecture

### 1. Role-Based Access Control (RBAC)

The system uses **Spatie Permission** package to implement a robust role and permission system:

#### Roles:
- **Admin**: System-wide management and oversight
- **Restaurant Manager**: Complete restaurant operations management
- **Waiter**: Customer service and order management
- **Kitchen**: Food preparation and kitchen operations
- **Delivery**: Delivery management and tracking

#### Permissions:
Each role has specific permissions that control access to features and data.

### 2. Dashboard Controllers

#### Admin Dashboard (`AdminDashboardController`)
- **Route**: `/admin/dashboard`
- **Features**:
  - Tenant management overview
  - System-wide analytics
  - Payment and subscription monitoring
  - System health metrics

#### Manager Dashboard (`ManagerDashboardController`)
- **Route**: `/manager/dashboard`
- **Features**:
  - Revenue analytics and KPIs
  - Staff performance monitoring
  - Customer insights
  - Menu performance analysis
  - Operational metrics

#### Waiter Dashboard (`WaiterDashboardController`)
- **Route**: `/waiter/dashboard`
- **Features**:
  - Table management
  - Order taking and tracking
  - Customer information access
  - Performance metrics
  - Task management

#### Kitchen Dashboard (`KitchenDashboardController`)
- **Route**: `/kitchen/dashboard`
- **Features**:
  - Real-time preparation queue
  - Order item status management
  - Preparation time analytics
  - Inventory alerts

#### Delivery Dashboard (`DeliveryDashboardController`)
- **Route**: `/delivery/dashboard`
- **Features**:
  - Delivery order management
  - Location tracking
  - Earnings tracking
  - Performance metrics

## Authentication & Authorization

### Middleware

1. **RoleMiddleware**: Protects routes based on user roles
2. **RedirectBasedOnRole**: Automatically redirects users to appropriate dashboards
3. **SetLocale**: Handles multi-language support

### Route Protection

```php
// Example route protection
Route::middleware(['role:admin'])->group(function () {
    Route::get('/admin/dashboard', [AdminDashboardController::class, 'index']);
});
```

## Multi-Language Support

### Supported Languages
- **English (en)**: Default language
- **Bengali (bn)**: Secondary language

### Language Switching
- **API Endpoint**: `POST /language/switch`
- **User Preference**: Stored in user profile
- **Session Persistence**: Maintains language across requests

### Translation Files
- `resources/lang/en.json`: English translations
- `resources/lang/bn.json`: Bengali translations

## Real-Time Features

### Dashboard API (`DashboardApiController`)

#### Endpoints:
- `GET /api/dashboard/data`: Role-specific dashboard data
- `GET /api/dashboard/orders/updates`: Real-time order updates
- `GET /api/notifications`: User notifications
- `POST /api/notifications/{id}/read`: Mark notification as read

### Notification System (`NotificationService`)

#### Features:
- Role-based notifications
- Real-time alerts
- Email and push notifications
- Notification preferences

## User Management

### User Model Enhancements

```php
// Role checking methods
$user->isAdmin()
$user->isRestaurantManager()
$user->isWaiter()
$user->isKitchen()
$user->isDelivery()

// Dashboard route resolution
$user->getDashboardRoute()
```

### User Preferences
- Language preference
- Theme preference (light/dark/auto)
- Notification settings
- Timezone settings

## Settings Management

### Settings Controller (`SettingsController`)

#### Features:
- Profile management
- Password updates
- Preference configuration
- Notification settings
- Security settings
- Two-factor authentication

#### Routes:
- `GET /settings`: Settings page
- `POST /settings/profile`: Update profile
- `POST /settings/password`: Change password
- `POST /settings/preferences`: Update preferences

## Payment Gateway Integration

### Fixed Payment Routes
- `GET /return`: Payment gateway return handler
- `POST /webhook`: Payment webhook handler
- `GET /payment/success`: Success page
- `GET /payment/cancel`: Cancel page
- `GET /payment/fail`: Failure page

### Payment Controller (`PaymentController`)
- Handles payment gateway responses
- Updates payment status
- Manages payment redirections

## Database Schema

### User Table Additions
```sql
ALTER TABLE users ADD COLUMN role VARCHAR(50);
ALTER TABLE users ADD COLUMN preferred_language VARCHAR(10) DEFAULT 'en';
ALTER TABLE users ADD COLUMN theme_preference VARCHAR(20) DEFAULT 'light';
ALTER TABLE users ADD COLUMN is_active BOOLEAN DEFAULT TRUE;
ALTER TABLE users ADD COLUMN last_login_at TIMESTAMP NULL;
```

### Spatie Permission Tables
- `roles`: User roles
- `permissions`: System permissions
- `role_has_permissions`: Role-permission relationships
- `model_has_roles`: User-role assignments

## API Documentation

### Dashboard Data API

#### Admin Dashboard Data
```json
{
  "role": "admin",
  "metrics": {
    "total_tenants": 150,
    "active_tenants": 142,
    "total_revenue": 50000,
    "pending_payments": 5
  },
  "recent_activities": [...],
  "system_health": {...}
}
```

#### Manager Dashboard Data
```json
{
  "role": "restaurant_manager",
  "metrics": {
    "total_revenue": 15000,
    "total_orders": 250,
    "active_orders": 12,
    "table_occupancy": 75
  },
  "recent_orders": [...],
  "top_selling_items": [...],
  "alerts": {...}
}
```

## Security Features

### Access Control
- Role-based route protection
- Permission-based feature access
- Secure API endpoints
- CSRF protection

### Data Protection
- User data encryption
- Secure password hashing
- Two-factor authentication support
- Activity logging

## Testing

### Test Coverage
- Role-based access tests
- Dashboard functionality tests
- API endpoint tests
- Language switching tests
- Payment gateway tests

### Running Tests
```bash
php artisan test --filter MultiRoleDashboardTest
```

## Deployment Considerations

### Environment Setup
1. Configure database connections
2. Set up queue workers for notifications
3. Configure broadcasting for real-time updates
4. Set up file storage for uploads

### Performance Optimization
- Database indexing for role queries
- Caching for dashboard data
- Queue processing for notifications
- CDN for static assets

## Demo Users

### Login Credentials
- **Admin**: <EMAIL> / Restaurant@2024
- **Manager**: <EMAIL> / Manager@2024
- **Waiter**: <EMAIL> / Waiter@2024
- **Kitchen**: <EMAIL> / Kitchen@2024
- **Delivery**: <EMAIL> / Delivery@2024

## Future Enhancements

### Planned Features
1. Mobile app integration
2. Advanced analytics dashboard
3. Real-time chat system
4. Advanced reporting tools
5. Integration with external services

### Scalability Considerations
- Microservices architecture
- Database sharding
- Load balancing
- Caching strategies

## Support & Maintenance

### Monitoring
- Application performance monitoring
- Error tracking and logging
- User activity analytics
- System health checks

### Updates
- Regular security updates
- Feature enhancements
- Bug fixes
- Performance improvements

---

This multi-role dashboard system provides a comprehensive foundation for restaurant management with proper role separation, security, and scalability considerations.
