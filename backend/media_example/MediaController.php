<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\Media;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;
use Intervention\Image\ImageManager;
use Intervention\Image\Drivers\Gd\Driver;

class MediaController extends Controller
{
    /**
     * Display a listing of the user's media.
     */
    public function index(Request $request)
    {
        // The BelongsToTenant trait will automatically filter by tenant_id
        $query = Media::where('user_id', Auth::id());

        // Apply search filter if provided
        if ($request->has('search') && !empty($request->search)) {
            $search = $request->search;
            $query->where('name', 'like', "%{$search}%");
        }

        // Apply file type filter if provided
        if ($request->has('type') && !empty($request->type)) {
            $query->where('file_type', 'like', $request->type . '%');
        }

        // Pagination
        $perPage = $request->input('per_page', 20);
        $media = $query->latest()->paginate($perPage);

        // Transform each media item to ensure URLs are properly generated
        $items = $media->items();

        // Log the first item for debugging
        if (count($items) > 0) {
            Log::debug('First media item:', [
                'id' => $items[0]->id,
                'name' => $items[0]->name,
                'thumbnail_path' => $items[0]->thumbnail_path,
                'thumbnail_url' => $items[0]->thumbnail_url,
                'file_path' => $items[0]->file_path,
                'url' => $items[0]->url
            ]);
        }

        return response()->json([
            'data' => $items,
            'meta' => [
                'pagination' => [
                    'total' => $media->total(),
                    'per_page' => $media->perPage(),
                    'current_page' => $media->currentPage(),
                    'last_page' => $media->lastPage(),
                    'from' => $media->firstItem(),
                    'to' => $media->lastItem()
                ]
            ]
        ]);
    }

    /**
     * Store a newly created media in storage.
     */
    public function store(Request $request)
    {
        // Check if user is authenticated
        if (!Auth::check()) {
            return response()->json(['message' => 'Unauthenticated'], 401);
        }

        $userId = Auth::id();
        if (!$userId) {
            return response()->json(['message' => 'User ID not found'], 401);
        }

        $request->validate([
            'file' => 'nullable|file|mimes:jpeg,png,jpg,gif|max:10240', // 10MB max
            'files.*' => 'nullable|file|mimes:jpeg,png,jpg,gif|max:10240', // 10MB max
            'name' => 'nullable|string|max:255'
        ]);

        try {
            $uploadedMedia = [];
            $files = [];

            // Handle single file upload
            if ($request->hasFile('file')) {
                $files[] = $request->file('file');
            }

            // Handle multiple files upload
            if ($request->hasFile('files')) {
                $files = array_merge($files, $request->file('files'));
            }

            if (empty($files)) {
                return response()->json(['message' => 'No files uploaded'], 400);
            }

            // Create image manager instance with desired driver
            $manager = new ImageManager(new Driver());

            // Process each file
            foreach ($files as $file) {
                $fileName = $file->hashName(); // Generate a unique, random name
                $fileType = $file->getMimeType();
                $fileSize = $file->getSize();
                $name = pathinfo($file->getClientOriginalName(), PATHINFO_FILENAME);

                // Create directories if they don't exist
                $originalPath = 'media/original/' . date('Y/m');
                $webpPath = 'media/webp/' . date('Y/m');
                $thumbnailPath = 'media/thumbnails/' . date('Y/m');

                // Store original file
                $originalFilePath = $file->storeAs($originalPath, $fileName, 'public');

                // Process image to WebP format
                $image = $manager->read($file);
                $webpFileName = pathinfo($fileName, PATHINFO_FILENAME) . '.webp';

                // Save WebP version
                $webpFilePath = $webpPath . '/' . $webpFileName;
                Storage::disk('public')->put(
                    $webpFilePath,
                    $image->toWebp(80)->toString()
                );

                // Create thumbnail
                $thumbnail = $manager->read($file)->cover(300, 300);
                $thumbnailFilePath = $thumbnailPath . '/' . $webpFileName;
                Storage::disk('public')->put(
                    $thumbnailFilePath,
                    $thumbnail->toWebp(60)->toString()
                );

                // Save media record to database
                $media = Media::create([
                    'tenant_id' => Auth::user()->tenant_id,
                    'user_id' => $userId,
                    'name' => $name,
                    'file_path' => $webpFilePath, // Use WebP as primary
                    'file_type' => $fileType,
                    'file_size' => $fileSize,
                    'thumbnail_path' => $thumbnailFilePath,
                    'metadata' => [
                        'original_path' => $originalFilePath,
                        'original_name' => $file->getClientOriginalName(),
                        'dimensions' => [
                            'width' => $image->width(),
                            'height' => $image->height()
                        ]
                    ]
                ]);

                $uploadedMedia[] = $media;
            }

            return response()->json($uploadedMedia, 201);
        } catch (\Exception $e) {
            return response()->json([
                'message' => 'Failed to upload media',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Display the specified media.
     */
    public function show($id)
    {
        $media = Media::findOrFail($id);

        // Check if user owns this media
        if ($media->user_id !== Auth::id()) {
            return response()->json(['message' => 'Unauthorized'], 403);
        }

        return response()->json($media);
    }

    /**
     * Remove the specified media from storage.
     */
    public function destroy($id)
    {
        $media = Media::findOrFail($id);

        // Check if user owns this media
        if ($media->user_id !== Auth::id()) {
            return response()->json(['message' => 'Unauthorized'], 403);
        }

        // Delete files from storage
        if (isset($media->metadata['original_path'])) {
            Storage::disk('public')->delete($media->metadata['original_path']);
        }
        Storage::disk('public')->delete($media->file_path);
        Storage::disk('public')->delete($media->thumbnail_path);

        // Delete database record
        $media->delete();

        return response()->json(['message' => 'Media deleted successfully']);
    }
}
