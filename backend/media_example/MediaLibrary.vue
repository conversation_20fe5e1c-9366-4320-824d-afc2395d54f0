<template>
  <div class="media-library">
    <div class="media-header">
      <div class="media-search">
        <input
          type="text"
          placeholder="Search media..."
          v-model="search"
          @input="searchMedia"
          class="search-input"
        >
      </div>
      <button @click="openUploadModal" class="btn btn-primary btn-sm">
        <i class="fas fa-upload"></i> Upload
      </button>
    </div>
    <div class="media-container">
      <div v-if="loading" class="media-loading">
        <i class="fas fa-spinner fa-spin"></i>
        <span>Loading media...</span>
      </div>
      <div v-else-if="items && items.length === 0" class="media-empty">
        <p>No media found</p>
        <button @click="openUploadModal" class="btn btn-primary btn-sm">
          <i class="fas fa-upload"></i> Upload
        </button>
      </div>
      <div v-else class="media-grid">
        <div
          v-for="item in items"
          :key="item.id"
          class="media-item"
          @click="selectMedia(item)"
          :class="{ 'selected': selectedItem && selectedItem.id === item.id }"
        >
          <img :src="item.thumbnail_url" :alt="item.name">
          <div class="media-item-info">
            <span class="media-item-name">{{ truncateName(item.name, 15) }}</span>
          </div>
        </div>
      </div>
    </div>
    <div class="media-pagination" v-if="pagination && pagination.last_page > 1">
      <button
        class="pagination-btn"
        :disabled="pagination.current_page === 1"
        @click="changePage(pagination.current_page - 1)"
      >
        <i class="fas fa-chevron-left"></i>
      </button>
      <span class="pagination-info">{{ pagination.current_page }} / {{ pagination.last_page }}</span>
      <button
        class="pagination-btn"
        :disabled="pagination.current_page === pagination.last_page"
        @click="changePage(pagination.current_page + 1)"
      >
        <i class="fas fa-chevron-right"></i>
      </button>
    </div>

    <!-- Media Upload Modal -->
    <div v-if="showUploadModal" class="modal-overlay">
      <div class="modal-container">
        <div class="modal-header">
          <h3>Upload Media1</h3>
          <button @click="showUploadModal = false" class="close-btn">
            <i class="fas fa-times"></i>
          </button>
        </div>
        <div class="modal-body">
          <div
            class="upload-area"
            :class="{ 'dragging': isDragging }"
            @dragover.prevent="isDragging = true"
            @dragleave.prevent="isDragging = false"
            @drop.prevent="handleFileDrop"
          >
            <div v-if="uploadFiles.length === 0">
              <i class="fas fa-cloud-upload-alt"></i>
              <p>Drag and drop images here, or click to select</p>
              <input
                type="file"
                accept="image/*"
                @change="handleFileSelect"
                id="media-file-upload"
                class="file-input"
                multiple
              >
              <label for="media-file-upload" class="btn btn-outline-primary">Select Files</label>
            </div>
            <div v-else class="files-preview">
              <div v-for="(file, index) in uploadFiles" :key="index" class="file-preview-item">
                <img :src="file.preview" alt="Preview" class="preview-image">
                <div class="file-info">
                  <p>{{ truncateName(file.name, 20) }}</p>
                  <p>{{ formatFileSize(file.size) }}</p>
                </div>
                <button @click="removeFile(index)" class="btn btn-sm btn-outline-danger remove-btn">
                  <i class="fas fa-times"></i>
                </button>
              </div>
              <div class="add-more-files">
                <input
                  type="file"
                  accept="image/*"
                  @change="handleFileSelect"
                  id="media-file-upload-more"
                  class="file-input"
                  multiple
                >
                <label for="media-file-upload-more" class="btn btn-sm btn-outline-primary">
                  <i class="fas fa-plus"></i> Add More
                </label>
              </div>
            </div>
          </div>
        </div>
        <div class="modal-footer">
          <button @click="showUploadModal = false" class="btn btn-secondary">Cancel</button>
          <button
            @click="uploadMedia"
            class="btn btn-primary"
            :disabled="uploadFiles.length === 0 || uploading"
          >
            <i v-if="uploading" class="fas fa-spinner fa-spin"></i>
            {{ uploading ? 'Uploading...' : 'Upload' }}
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { mapActions } from 'vuex';

export default {
  name: 'MediaLibrary',
  props: {
    onSelect: {
      type: Function,
      default: () => {}
    }
  },
  data() {
    return {
      search: '',
      items: [], // Initialize as empty array to prevent undefined errors
      loading: true, // Start with loading state
      searchTimeout: null,
      selectedItem: null,
      pagination: {
        current_page: 1,
        last_page: 1,
        per_page: 12
      },

      // Upload modal
      showUploadModal: false,
      uploadFiles: [],
      uploading: false,
      isDragging: false
    };
  },
  methods: {
    ...mapActions({
      fetchMedia: 'media/fetchMedia',
      uploadMediaFile: 'media/uploadMedia'
    }),

    async loadMedia() {
      this.loading = true;
      try {
        console.log('Fetching media with params:', {
          search: this.search,
          page: this.pagination.current_page,
          per_page: this.pagination.per_page
        });

        const response = await this.fetchMedia({
          search: this.search,
          page: this.pagination.current_page,
          per_page: this.pagination.per_page
        });

        console.log('Media API response:', response);

        // The response should be the data array directly from the store
        if (Array.isArray(response)) {
          this.items = response;
        } else {
          console.error('Expected array but got:', typeof response);
          this.items = [];
        }

        console.log('Items after processing:', this.items);

        // Check if items have the expected properties
        if (this.items.length > 0) {
          console.log('First item properties:', {
            id: this.items[0].id,
            name: this.items[0].name,
            thumbnail_url: this.items[0].thumbnail_url,
            url: this.items[0].url
          });

          // Check if thumbnail_url is valid
          if (!this.items[0].thumbnail_url) {
            console.error('Missing thumbnail_url in item:', this.items[0]);
          }
        }

        this.loading = false;
      } catch (error) {
        console.error('Failed to load media:', error);
        // Ensure items is always a valid array even on error
        this.items = [];
        this.loading = false;

        // Handle authentication errors
        if (error.response && error.response.status === 401) {
          alert('Your session has expired. Please log in again.');
          // Redirect to login page or trigger auth refresh
        }
      }
    },

    changePage(page) {
      if (page < 1 || page > this.pagination.last_page) return;
      this.pagination.current_page = page;
      this.loadMedia();
    },

    searchMedia() {
      // Debounce search
      if (this.searchTimeout) {
        clearTimeout(this.searchTimeout);
      }

      this.searchTimeout = setTimeout(() => {
        this.loadMedia();
      }, 300);
    },

    selectMedia(item) {
      this.selectedItem = item;
      this.onSelect(item);
    },

    truncateName(text, length) {
      if (!text) return '';
      if (text.length <= length) return text;

      const extension = text.lastIndexOf('.') > -1 ? text.substring(text.lastIndexOf('.')) : '';
      const name = text.substring(0, text.lastIndexOf('.') > -1 ? text.lastIndexOf('.') : text.length);

      if (name.length <= length - 3) return text;
      return name.substring(0, length - 3) + '...' + extension;
    },

    formatFileSize(bytes) {
      if (!bytes) return '0 B';
      const k = 1024;
      const sizes = ['B', 'KB', 'MB', 'GB'];
      const i = Math.floor(Math.log(bytes) / Math.log(k));
      return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    },

    openUploadModal() {
      this.showUploadModal = true;
      this.uploadFiles = [];
      this.isDragging = false;
    },

    handleFileSelect(event) {
      const files = event.target.files;
      if (files && files.length > 0) {
        this.addFilesToUpload(files);
      }
    },

    handleFileDrop(event) {
      this.isDragging = false;
      const files = event.dataTransfer.files;
      if (files && files.length > 0) {
        this.addFilesToUpload(files);
      }
    },

    addFilesToUpload(files) {
      for (let i = 0; i < files.length; i++) {
        const file = files[i];
        if (file && file.type.startsWith('image/')) {
          // Check if file is already in the list
          const isDuplicate = this.uploadFiles.some(f =>
            f.name === file.name && f.size === file.size && f.lastModified === file.lastModified
          );

          if (!isDuplicate) {
            this.uploadFiles.push({
              file: file,
              name: file.name,
              size: file.size,
              preview: URL.createObjectURL(file)
            });
          }
        }
      }
    },

    removeFile(index) {
      if (this.uploadFiles[index] && this.uploadFiles[index].preview) {
        URL.revokeObjectURL(this.uploadFiles[index].preview);
      }
      this.uploadFiles.splice(index, 1);
    },

    async uploadMedia() {
      if (this.uploadFiles.length === 0) return;

      this.uploading = true;

      try {
        const formData = new FormData();

        // Append all files to the form data
        this.uploadFiles.forEach((fileObj, index) => {
          formData.append(`files[]`, fileObj.file);
        });

        await this.uploadMediaFile(formData);

        // Refresh media library
        await this.loadMedia();

        // Clean up and close modal
        this.uploadFiles.forEach(fileObj => {
          if (fileObj.preview) {
            URL.revokeObjectURL(fileObj.preview);
          }
        });

        this.showUploadModal = false;
        this.uploadFiles = [];

      } catch (error) {
        console.error('Failed to upload media:', error);

        let errorMessage = 'Failed to upload media. Please try again.';

        if (error.response) {
          if (error.response.status === 401) {
            errorMessage = 'Your session has expired. Please log in again.';
          } else if (error.response.data && error.response.data.message) {
            errorMessage = error.response.data.message;
          }
        }

        alert(errorMessage);
      } finally {
        this.uploading = false;
      }
    }
  },
  mounted() {
    this.loadMedia();
  },
  beforeUnmount() {
    // Clean up all preview URLs
    this.uploadFiles.forEach(fileObj => {
      if (fileObj.preview) {
        URL.revokeObjectURL(fileObj.preview);
      }
    });
  }
};
</script>

<style scoped>
.media-library {
  width: 100%;
}

.media-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.media-search {
  flex: 1;
  margin-right: 10px;
}

.search-input {
  width: 100%;
  padding: 8px 12px;
  border: 1px solid #e5e7eb;
  border-radius: 4px;
  font-size: 14px;
}

.media-container {
  min-height: 150px;
  border: 1px solid #e5e7eb;
  border-radius: 4px;
  background-color: white;
  margin-bottom: 12px;
  padding: 8px;
}

.media-loading, .media-empty {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 24px;
  color: #666;
  text-align: center;
}

.media-loading i, .media-empty i {
  font-size: 24px;
  margin-bottom: 10px;
}

.media-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(100px, 1fr));
  gap: 10px;
  padding: 10px;
}

.media-item {
  position: relative;
  cursor: pointer;
  transition: all 0.2s;
  border: 2px solid #e5e7eb;
  border-radius: 4px;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.media-item img {
  width: 100%;
  height: 80px;
  object-fit: cover;
  display: block;
}

.media-item-info {
  padding: 5px;
  font-size: 12px;
  background-color: #f9fafb;
  border-top: 1px solid #e5e7eb;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.media-item:hover {
  transform: translateY(-2px);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  border-color: #d1d5db;
}

.media-item.selected {
  border-color: #4f46e5;
  box-shadow: 0 0 0 2px rgba(79, 70, 229, 0.3);
}

.media-pagination {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 10px;
  margin-top: 10px;
  padding: 5px 0;
}

.pagination-btn {
  width: 30px;
  height: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 1px solid #e5e7eb;
  border-radius: 4px;
  background: white;
  cursor: pointer;
}

.pagination-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.pagination-info {
  font-size: 14px;
  color: #6b7280;
}

/* Modal styles */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.modal-container {
  background: white;
  border-radius: 8px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  width: 400px;
  max-width: 90%;
  overflow: hidden;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px;
  border-bottom: 1px solid #e5e7eb;
}

.modal-header h3 {
  margin: 0;
  font-size: 18px;
}

.close-btn {
  background: none;
  border: none;
  font-size: 16px;
  cursor: pointer;
}

.modal-body {
  padding: 16px;
}

.upload-area {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  border: 2px dashed #e5e7eb;
  border-radius: 8px;
  padding: 24px;
  transition: border-color 0.2s;
}

.upload-area.dragging {
  border-color: #4f46e5;
}

.upload-area i {
  font-size: 48px;
  margin-bottom: 12px;
  color: #e5e7eb;
}

.upload-area p {
  color: #666;
  margin-bottom: 16px;
}

.file-input {
  display: none;
}

.files-preview {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
  gap: 15px;
  width: 100%;
  margin-top: 10px;
}

.file-preview-item {
  position: relative;
  border: 1px solid #e5e7eb;
  border-radius: 4px;
  overflow: hidden;
  padding: 5px;
}

.preview-image {
  width: 100%;
  height: 120px;
  object-fit: cover;
  border-radius: 4px;
  margin-bottom: 8px;
}

.file-info {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.file-info p {
  margin: 0;
  font-size: 12px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.remove-btn {
  position: absolute;
  top: 5px;
  right: 5px;
  width: 24px;
  height: 24px;
  padding: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: rgba(255, 255, 255, 0.8);
  border-radius: 50%;
}

.add-more-files {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
  min-height: 120px;
  border: 2px dashed #e5e7eb;
  border-radius: 4px;
}

.modal-footer {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
  padding: 16px;
  border-top: 1px solid #e5e7eb;
}

.mt-3 {
  margin-top: 16px;
}
</style>
