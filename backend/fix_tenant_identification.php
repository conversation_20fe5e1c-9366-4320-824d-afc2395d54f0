<?php

// Fix tenant identification issue
require_once 'vendor/autoload.php';

$app = require_once 'bootstrap/app.php';
$kernel = $app->make('Illuminate\Contracts\Console\Kernel');
$kernel->bootstrap();

use App\Models\Tenant;
use App\Models\SubscriptionPlan;
use Illuminate\Support\Facades\DB;

echo "=== Fixing Tenant Domain Identification ===\n\n";

try {
    // Step 1: Check current tenant and domain status
    echo "1. Checking current tenant status...\n";
    
    $tenant = Tenant::find('demo-restaurant');
    if ($tenant) {
        echo "   ✓ Tenant exists: {$tenant->name}\n";
        
        $domains = $tenant->domains;
        echo "   Domains found: " . $domains->count() . "\n";
        foreach ($domains as $domain) {
            echo "     - {$domain->domain}\n";
        }
    } else {
        echo "   ❌ Tenant 'demo-restaurant' not found!\n";
    }

    // Step 2: Check domain table directly
    echo "\n2. Checking domains table directly...\n";
    $domainRecords = DB::table('domains')->where('domain', 'demo-restaurant.localhost')->get();
    
    if ($domainRecords->count() > 0) {
        foreach ($domainRecords as $domain) {
            echo "   ✓ Domain record found: {$domain->domain} -> tenant: {$domain->tenant_id}\n";
        }
    } else {
        echo "   ❌ No domain record found for 'demo-restaurant.localhost'\n";
    }

    // Step 3: Fix tenant record if missing
    if (!$tenant) {
        echo "\n3. Creating missing tenant record...\n";
        
        $plan = SubscriptionPlan::first();
        
        $tenant = Tenant::create([
            'id' => 'demo-restaurant',
            'name' => 'Demo Restaurant',
            'email' => '<EMAIL>',
            'phone' => '******-0123',
            'address' => '123 Main Street',
            'city' => 'New York',
            'state' => 'NY',
            'country' => 'USA',
            'postal_code' => '10001',
            'timezone' => 'America/New_York',
            'currency' => 'USD',
            'language' => 'en',
            'subscription_plan_id' => $plan?->id,
            'subscription_status' => 'active',
            'trial_ends_at' => now()->addDays(30),
        ]);
        
        echo "   ✓ Tenant created: {$tenant->name}\n";
    }

    // Step 4: Fix domain record
    echo "\n4. Ensuring domain record exists...\n";
    
    // Delete any existing domain records for this domain (cleanup)
    DB::table('domains')->where('domain', 'demo-restaurant.localhost')->delete();
    
    // Create fresh domain record
    $domain = $tenant->domains()->create([
        'domain' => 'demo-restaurant.localhost'
    ]);
    
    echo "   ✓ Domain record created: {$domain->domain}\n";

    // Step 5: Verify the fix
    echo "\n5. Verifying tenant identification...\n";
    
    // Test domain lookup
    $foundDomain = DB::table('domains')
        ->where('domain', 'demo-restaurant.localhost')
        ->first();
    
    if ($foundDomain) {
        echo "   ✓ Domain lookup successful: {$foundDomain->domain} -> {$foundDomain->tenant_id}\n";
        
        // Test tenant lookup via domain
        $foundTenant = Tenant::find($foundDomain->tenant_id);
        if ($foundTenant) {
            echo "   ✓ Tenant lookup successful: {$foundTenant->name}\n";
        } else {
            echo "   ❌ Tenant lookup failed for ID: {$foundDomain->tenant_id}\n";
        }
    } else {
        echo "   ❌ Domain lookup failed\n";
    }

    // Step 6: Test tenancy identification (simulate what middleware does)
    echo "\n6. Testing tenancy identification logic...\n";
    
    try {
        // This simulates what the tenancy middleware does
        $domain = \Stancl\Tenancy\Database\Models\Domain::where('domain', 'demo-restaurant.localhost')->first();
        
        if ($domain && $domain->tenant) {
            echo "   ✓ Tenancy identification working: {$domain->tenant->name}\n";
        } else {
            echo "   ❌ Tenancy identification failed\n";
        }
    } catch (Exception $e) {
        echo "   ❌ Tenancy test error: " . $e->getMessage() . "\n";
    }

    // Step 7: Check tenancy configuration
    echo "\n7. Checking tenancy configuration...\n";
    
    $centralDomains = config('tenancy.central_domains', []);
    echo "   Central domains: " . implode(', ', $centralDomains) . "\n";
    
    if (in_array('localhost', $centralDomains)) {
        echo "   ✓ localhost is configured as central domain\n";
    } else {
        echo "   ⚠ localhost not in central domains\n";
    }

    // Step 8: Final verification
    echo "\n8. Final verification...\n";
    
    $finalTenant = Tenant::find('demo-restaurant');
    $finalDomains = $finalTenant->domains;
    
    echo "   Tenant: {$finalTenant->name} (ID: {$finalTenant->id})\n";
    echo "   Status: {$finalTenant->subscription_status}\n";
    echo "   Domains:\n";
    foreach ($finalDomains as $d) {
        echo "     - {$d->domain} (ID: {$d->id})\n";
    }

    echo "\n=== Fix Completed Successfully! ===\n";
    echo "\n📋 Test Information:\n";
    echo "  ✅ Tenant: demo-restaurant\n";
    echo "  ✅ Domain: demo-restaurant.localhost\n";
    echo "  ✅ Database: tenant_demo-restaurant\n";
    echo "\n🌐 Test URLs:\n";
    echo "  Central: http://localhost:8000/login\n";
    echo "  Tenant: http://demo-restaurant.localhost:8000/login\n";
    echo "  Dashboard: http://demo-restaurant.localhost:8000/dashboard\n";
    echo "\n🔐 Test Credentials:\n";
    echo "  Manager: <EMAIL> / Manager@2024\n";
    echo "  Waiter: <EMAIL> / Waiter@2024\n";
    echo "  Kitchen: <EMAIL> / Kitchen@2024\n";
    echo "  Delivery: <EMAIL> / Delivery@2024\n";
    echo "\n✅ You should now be able to access the dashboard without tenant identification errors!\n";

} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
    echo "File: " . $e->getFile() . ":" . $e->getLine() . "\n";
    
    // Additional debugging
    echo "\n🔍 Debug Information:\n";
    echo "Database connection: " . config('database.default') . "\n";
    echo "Tenant model: " . config('tenancy.tenant_model') . "\n";
    echo "Domain model: " . config('tenancy.domain_model') . "\n";
}
