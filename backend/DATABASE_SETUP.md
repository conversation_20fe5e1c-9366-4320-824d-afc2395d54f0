# 🍽️ Restaurant Management System - Database Setup Guide

This guide will help you set up the complete database structure for the Restaurant Management System with all modules and sample data.

## 📋 Prerequisites

- PHP 8.1 or higher
- Composer installed
- MySQL 8.0 or higher (or MariaDB 10.3+)
- Laravel 10.x
- Node.js and NPM (for frontend assets)

## 🚀 Quick Setup

### Option 1: Automated Setup (Recommended)

**For Linux/Mac:**
```bash
chmod +x setup-database.sh
./setup-database.sh
```

**For Windows:**
```cmd
setup-database.bat
```

### Option 2: Manual Setup

1. **Configure Environment**
   ```bash
   cp .env.example .env
   ```
   Update your `.env` file with database credentials:
   ```env
   DB_CONNECTION=mysql
   DB_HOST=127.0.0.1
   DB_PORT=3306
   DB_DATABASE=restaurant_management
   DB_USERNAME=your_username
   DB_PASSWORD=your_password
   ```

2. **Install Dependencies**
   ```bash
   composer install
   npm install
   ```

3. **Generate Application Key**
   ```bash
   php artisan key:generate
   ```

4. **Run Migrations and Seeders**
   ```bash
   # Clear cache
   php artisan config:clear
   php artisan cache:clear
   
   # Setup main database
   php artisan migrate:fresh --seed
   
   # Setup tenant databases
   php artisan tenants:migrate
   php artisan tenants:seed
   
   # Create storage links
   php artisan storage:link
   ```

## 🔐 Default Login Credentials

### Super Admin
- **Email:** `<EMAIL>`
- **Password:** `Restaurant@2024`
- **Access:** Full system administration

### Restaurant Manager
- **Email:** `<EMAIL>`
- **Password:** `Manager@2024`
- **Access:** Restaurant management features

### Test User
- **Email:** `<EMAIL>`
- **Password:** `Test@2024`
- **Access:** Basic user features

## 🏢 Demo Tenant

A demo restaurant tenant is automatically created:
- **Domain:** `demo-restaurant.localhost`
- **Name:** Demo Restaurant
- **Status:** Active

### Local Development Setup

Add this line to your hosts file:
```
127.0.0.1 demo-restaurant.localhost
```

**Hosts file locations:**
- **Windows:** `C:\Windows\System32\drivers\etc\hosts`
- **Mac/Linux:** `/etc/hosts`

## 📊 Database Structure

### Main Database Tables
- `users` - System users
- `teams` - User teams (Jetstream)
- `tenants` - Restaurant tenants
- `domains` - Tenant domains
- `subscription_plans` - Subscription plans

### Tenant Database Tables

#### 🍽️ Restaurant Management
- `restaurants` - Restaurant information
- `tables` - Restaurant tables
- `categories` - Menu categories
- `menu_items` - Menu items
- `recipe_ingredients` - Menu-inventory integration

#### 💰 Financial Management
- `expense_categories` - Expense categories
- `expenses` - Expense records
- `vendors` - Vendor information
- `budgets` - Budget management

#### 📦 Inventory Management
- `inventory_categories` - Inventory categories
- `inventory_items` - Inventory items
- `stock_movements` - Stock movement history
- `stock_batches` - Batch tracking
- `waste_records` - Waste tracking
- `purchase_orders` - Purchase orders
- `purchase_order_items` - PO line items

#### 👥 Staff Management
- `departments` - Staff departments
- `employees` - Employee records
- `shifts` - Shift scheduling
- `time_entries` - Time tracking
- `payroll_records` - Payroll data

## 🌱 Sample Data Included

### Menu Items
- **Appetizers:** Caesar Salad, Buffalo Wings, Mozzarella Sticks, Calamari
- **Main Courses:** Grilled Salmon, Ribeye Steak, Chicken Parmesan, Pasta Primavera
- **Desserts:** Chocolate Lava Cake, Cheesecake, Tiramisu
- **Beverages:** Coffee, Fresh Juices, Beer, Wine
- **Salads & Soups:** Greek Salad, Chicken Salad, Tomato Basil Soup

### Inventory Items
- **Proteins:** Chicken, Beef, Salmon
- **Vegetables:** Tomatoes, Onions, Lettuce
- **Dairy:** Milk, Cheese
- **Pantry:** Flour, Rice, Spices
- **Supplies:** Cleaning products, Disposables

### Staff Structure
- **Kitchen:** Head Chef, Sous Chef, Line Cooks, Prep Cook
- **Front of House:** Floor Manager, Servers, Host
- **Management:** General Manager, Assistant Manager
- **Bar:** Head Bartender, Bartender
- **Support:** Cleaning Supervisor, Delivery Drivers

### Financial Categories
- Food & Ingredients, Beverages, Equipment
- Utilities, Rent, Marketing
- Cleaning Supplies, Office Supplies
- Insurance, Professional Services

## 🔧 Troubleshooting

### Common Issues

1. **Migration Errors**
   ```bash
   php artisan migrate:rollback
   php artisan migrate:fresh --seed
   ```

2. **Tenant Database Issues**
   ```bash
   php artisan tenants:migrate:fresh --seed
   ```

3. **Permission Errors**
   ```bash
   sudo chown -R www-data:www-data storage bootstrap/cache
   sudo chmod -R 775 storage bootstrap/cache
   ```

4. **Cache Issues**
   ```bash
   php artisan config:clear
   php artisan cache:clear
   php artisan route:clear
   php artisan view:clear
   ```

### Database Reset

To completely reset the database:
```bash
php artisan migrate:fresh --seed
php artisan tenants:migrate:fresh --seed
```

## 🚀 Starting the Application

1. **Start the development server:**
   ```bash
   php artisan serve
   ```

2. **Access the application:**
   - Main App: http://localhost:8000
   - Demo Restaurant: http://demo-restaurant.localhost:8000

3. **Compile frontend assets:**
   ```bash
   npm run dev
   # or for production
   npm run build
   ```

## 📚 Next Steps

1. **Customize the demo data** to match your restaurant
2. **Configure email settings** for notifications
3. **Set up file storage** (local/S3) for media uploads
4. **Configure payment gateways** for subscription billing
5. **Set up backup procedures** for data protection

## 🆘 Support

If you encounter any issues during setup:

1. Check the Laravel logs: `storage/logs/laravel.log`
2. Verify database connection settings
3. Ensure all PHP extensions are installed
4. Check file permissions on storage directories

## 🔒 Security Notes

- **Change default passwords** before production use
- **Configure proper database permissions**
- **Set up SSL certificates** for production
- **Enable Laravel's security features** (CSRF, etc.)
- **Regular database backups** are recommended

---

**🎉 Congratulations!** Your Restaurant Management System database is now set up with comprehensive sample data. You can start exploring all the features including menu management, inventory tracking, staff scheduling, and financial reporting.
