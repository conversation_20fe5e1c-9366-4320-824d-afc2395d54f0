<?php

namespace Tests\Feature;

use Tests\TestCase;
use App\Models\User;
use Spatie\Permission\Models\Role;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;

class MultiRoleDashboardTest extends TestCase
{
    use RefreshDatabase, WithFaker;

    protected function setUp(): void
    {
        parent::setUp();
        
        // Create roles and permissions
        $this->artisan('db:seed', ['--class' => 'RolePermissionSeeder']);
    }

    /** @test */
    public function admin_can_access_admin_dashboard()
    {
        $admin = User::factory()->create();
        $admin->assignRole('admin');

        $response = $this->actingAs($admin)
            ->get('/admin/dashboard');

        $response->assertStatus(200);
    }

    /** @test */
    public function restaurant_manager_can_access_manager_dashboard()
    {
        $manager = User::factory()->create();
        $manager->assignRole('restaurant_manager');

        $response = $this->actingAs($manager)
            ->get('/manager/dashboard');

        $response->assertStatus(200);
    }

    /** @test */
    public function waiter_can_access_waiter_dashboard()
    {
        $waiter = User::factory()->create();
        $waiter->assignRole('waiter');

        $response = $this->actingAs($waiter)
            ->get('/waiter/dashboard');

        $response->assertStatus(200);
    }

    /** @test */
    public function kitchen_staff_can_access_kitchen_dashboard()
    {
        $kitchen = User::factory()->create();
        $kitchen->assignRole('kitchen');

        $response = $this->actingAs($kitchen)
            ->get('/kitchen/dashboard');

        $response->assertStatus(200);
    }

    /** @test */
    public function delivery_driver_can_access_delivery_dashboard()
    {
        $delivery = User::factory()->create();
        $delivery->assignRole('delivery');

        $response = $this->actingAs($delivery)
            ->get('/delivery/dashboard');

        $response->assertStatus(200);
    }

    /** @test */
    public function user_cannot_access_unauthorized_dashboard()
    {
        $waiter = User::factory()->create();
        $waiter->assignRole('waiter');

        // Waiter trying to access admin dashboard
        $response = $this->actingAs($waiter)
            ->get('/admin/dashboard');

        $response->assertStatus(302); // Redirected
    }

    /** @test */
    public function user_gets_redirected_to_appropriate_dashboard()
    {
        $manager = User::factory()->create();
        $manager->assignRole('restaurant_manager');

        // Manager accessing generic dashboard should be redirected
        $response = $this->actingAs($manager)
            ->get('/dashboard');

        $response->assertRedirect('/manager/dashboard');
    }

    /** @test */
    public function api_dashboard_data_returns_role_specific_data()
    {
        $admin = User::factory()->create();
        $admin->assignRole('admin');

        $response = $this->actingAs($admin)
            ->getJson('/api/dashboard/data');

        $response->assertStatus(200)
            ->assertJsonStructure([
                'role',
                'metrics',
                'recent_activities',
                'system_health'
            ])
            ->assertJson(['role' => 'admin']);
    }

    /** @test */
    public function language_switching_works()
    {
        $user = User::factory()->create();

        $response = $this->actingAs($user)
            ->postJson('/language/switch', ['locale' => 'bn']);

        $response->assertStatus(200)
            ->assertJson(['success' => true, 'locale' => 'bn']);

        $this->assertEquals('bn', $user->fresh()->preferred_language);
    }

    /** @test */
    public function notification_system_works()
    {
        $user = User::factory()->create();

        $response = $this->actingAs($user)
            ->getJson('/api/notifications');

        $response->assertStatus(200)
            ->assertJsonStructure([
                'notifications',
                'unread_count'
            ]);
    }

    /** @test */
    public function settings_can_be_updated()
    {
        $user = User::factory()->create();

        $response = $this->actingAs($user)
            ->postJson('/settings/preferences', [
                'preferred_language' => 'bn',
                'theme_preference' => 'dark',
                'timezone' => 'Asia/Dhaka'
            ]);

        $response->assertStatus(200)
            ->assertJson(['success' => true]);

        $user->refresh();
        $this->assertEquals('bn', $user->preferred_language);
        $this->assertEquals('dark', $user->theme_preference);
    }

    /** @test */
    public function payment_return_routes_work()
    {
        $response = $this->get('/return?transaction_id=test123&status=success');
        $response->assertStatus(302); // Redirects to success page

        $response = $this->get('/payment/success');
        $response->assertStatus(200);

        $response = $this->get('/payment/cancel');
        $response->assertStatus(200);

        $response = $this->get('/payment/fail');
        $response->assertStatus(200);
    }

    /** @test */
    public function role_middleware_protects_routes()
    {
        $waiter = User::factory()->create();
        $waiter->assignRole('waiter');

        // Waiter trying to access kitchen routes
        $response = $this->actingAs($waiter)
            ->get('/kitchen/dashboard');

        $response->assertStatus(302); // Redirected due to insufficient permissions
    }

    /** @test */
    public function user_role_helper_methods_work()
    {
        $admin = User::factory()->create();
        $admin->assignRole('admin');

        $this->assertTrue($admin->isAdmin());
        $this->assertFalse($admin->isWaiter());

        $waiter = User::factory()->create();
        $waiter->assignRole('waiter');

        $this->assertTrue($waiter->isWaiter());
        $this->assertFalse($waiter->isAdmin());
    }

    /** @test */
    public function dashboard_route_resolution_works()
    {
        $admin = User::factory()->create();
        $admin->assignRole('admin');

        $this->assertStringContains('/admin/dashboard', $admin->getDashboardRoute());

        $waiter = User::factory()->create();
        $waiter->assignRole('waiter');

        $this->assertStringContains('/waiter/dashboard', $waiter->getDashboardRoute());
    }

    /** @test */
    public function real_time_order_updates_work()
    {
        $kitchen = User::factory()->create();
        $kitchen->assignRole('kitchen');

        $response = $this->actingAs($kitchen)
            ->getJson('/api/dashboard/orders/updates');

        $response->assertStatus(200)
            ->assertJsonStructure([
                'orders',
                'timestamp'
            ]);
    }

    /** @test */
    public function translation_api_works()
    {
        $response = $this->getJson('/language/translations?locale=en');

        $response->assertStatus(200)
            ->assertJsonStructure([
                'locale',
                'translations'
            ]);
    }

    /** @test */
    public function current_language_api_works()
    {
        $response = $this->getJson('/language/current');

        $response->assertStatus(200)
            ->assertJsonStructure([
                'current_locale',
                'available_locales',
                'user_preference'
            ]);
    }
}
