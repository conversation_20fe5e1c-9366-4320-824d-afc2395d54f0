<?php

// Simple test script to verify the multi-tenant setup
require_once 'vendor/autoload.php';

$app = require_once 'bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

echo "=== Multi-Tenant Restaurant Management System Test ===\n\n";

try {
    // Test database connection
    echo "1. Testing database connection...\n";
    DB::connection()->getPdo();
    echo "   ✓ Database connected successfully!\n\n";

    // Check if central tables exist
    echo "2. Checking central database tables...\n";
    $centralTables = ['users', 'tenants', 'domains', 'subscription_plans', 'roles', 'permissions'];
    foreach ($centralTables as $table) {
        if (Schema::hasTable($table)) {
            echo "   ✓ Table '{$table}' exists\n";
        } else {
            echo "   ✗ Table '{$table}' missing\n";
        }
    }
    echo "\n";

    // Check tenant migrations directory
    echo "3. Checking tenant migrations...\n";
    $tenantMigrations = glob('database/migrations/tenant/*.php');
    echo "   ✓ Found " . count($tenantMigrations) . " tenant migrations\n\n";

    // Test tenant creation
    echo "4. Testing tenant setup...\n";
    $tenant = \App\Models\Tenant::firstOrCreate(
        ['id' => 'test-restaurant'],
        [
            'name' => 'Test Restaurant',
            'email' => '<EMAIL>',
            'subscription_status' => 'active'
        ]
    );
    echo "   ✓ Tenant created/found: {$tenant->name}\n";

    // Create domain
    $domain = $tenant->domains()->firstOrCreate([
        'domain' => 'test-restaurant.localhost'
    ]);
    echo "   ✓ Domain created: {$domain->domain}\n\n";

    echo "=== Setup Test Completed Successfully! ===\n";

} catch (Exception $e) {
    echo "Error: " . $e->getMessage() . "\n";
    echo "File: " . $e->getFile() . ":" . $e->getLine() . "\n";
}
