<?php

require_once 'vendor/autoload.php';

$app = require_once 'bootstrap/app.php';
$app->make(Illuminate\Contracts\Console\Kernel::class)->bootstrap();

// Initialize tenant
$tenant = \App\Models\Tenant::where('id', 'demo-restaurant')->first();
if ($tenant) {
    tenancy()->initialize($tenant);
    
    echo "Connected to tenant: " . $tenant->id . "\n";
    
    // Check if tables exist
    $tables = [
        'menu_items',
        'categories', 
        'subcategories',
        'menu_item_media',
        'media'
    ];
    
    foreach ($tables as $table) {
        $exists = \Illuminate\Support\Facades\Schema::hasTable($table);
        echo "Table '$table': " . ($exists ? "EXISTS" : "MISSING") . "\n";
        
        if ($exists && $table === 'menu_items') {
            $columns = \Illuminate\Support\Facades\Schema::getColumnListing($table);
            echo "  Columns: " . implode(', ', $columns) . "\n";
        }
    }
    
    // Check if subcategory_id column exists in menu_items
    if (\Illuminate\Support\Facades\Schema::hasTable('menu_items')) {
        $hasSubcategoryColumn = \Illuminate\Support\Facades\Schema::hasColumn('menu_items', 'subcategory_id');
        echo "menu_items.subcategory_id column: " . ($hasSubcategoryColumn ? "EXISTS" : "MISSING") . "\n";
    }
    
} else {
    echo "Tenant 'demo-restaurant' not found\n";
}
