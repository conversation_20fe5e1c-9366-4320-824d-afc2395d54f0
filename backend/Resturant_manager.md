THis is a multi tenant subdomain based restaurant management system. The system is built on Laravel 12 and uses the following packages:

1. stancl/tenancy - For multi-tenancy
2. spatie/laravel-permission - For role-based access control
3. laravel/jetstream - For authentication and user management
4. inertiajs/inertia-laravel - For frontend routing and rendering
5. vuejs/vue - For frontend development

The system has the following features:

1. Super admin can manage tenants, subscriptions, and payments
2. Restaurant manager can manage restaurant settings, menu, staff, and reports
3. Wait<PERSON> can manage orders, tables, and customers
4. Kitchen staff can manage order queue, recipes, and inventory
5. Delivery driver can manage delivery assignments, routes, and tracking

The system has the following database structure:

1. Central database - For system users, tenants, subscriptions, and payments
2. Tenant database - For restaurant settings, menu, staff, orders, customers, inventory, and reports

Translation is done using JSON files. The system supports English and Bengali languages.
located on resources/lang/en.json and resources/lang/bn.json

current task
http://demo-restaurant.localhost:8000/pages
here manager can add pages thats work fine
#todo
but edit page is return 404
complete the translation on 
http://demo-restaurant.localhost:8000/pages
