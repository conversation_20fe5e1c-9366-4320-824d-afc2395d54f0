<?php

namespace App\Traits;

use App\Models\Tenant\Branch;
use App\Models\Tenant\Employee;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Session;
use Illuminate\Support\Facades\Log;

trait HasBranchAccess
{
    /**
     * Get all branches accessible to the current user
     */
    public function getAccessibleBranches(): Collection
    {
        // Use the current model instance if available, otherwise get from auth
        $user = $this instanceof \Illuminate\Foundation\Auth\User ? $this : $this->getAuthUser();

        if (!$user) {
            return new Collection();
        }

        // Cache key for user's accessible branches
        $cacheKey = $this->getBranchCacheKey($user->id);

        return $this->rememberWithTags($cacheKey, 300, function () use ($user) { // Cache for 5 minutes
            // First check if any branches exist at all
            $allBranches = Branch::active()->ordered()->get();
            if ($allBranches->isEmpty()) {
                // No branches exist, return empty collection
                return new Collection();
            }

            // Super Admin or Admin can access all branches
            if ($this->userHasRole($user, ['super-admin', 'admin'])) {
                return $allBranches;
            }

            // Get user's employee record in current tenant
            $employee = Employee::where('user_id', $user->id)->first();

            if (!$employee) {
                // If no employee record, check if user has any POS-related roles
                if ($this->userHasRole($user, ['manager', 'restaurant-manager', 'waiter', 'kitchen', 'cashier'])) {
                    // Users with POS roles can access all branches if no specific employee record
                    // This provides flexibility for development and smaller restaurants
                    return $allBranches;
                }

                // For users without POS roles and no employee record, return empty collection
                return new Collection();
            }

            $accessibleBranches = new Collection();

            // If employee has a primary branch, add it
            if ($employee->primary_branch_id) {
                $primaryBranch = Branch::active()->find($employee->primary_branch_id);
                if ($primaryBranch) {
                    $accessibleBranches->push($primaryBranch);
                }
            }

            // If employee is a manager of any branch, add those branches
            $managedBranches = Branch::active()
                ->where('manager_id', $employee->id)
                ->get();
            
            $accessibleBranches = $accessibleBranches->merge($managedBranches);

            // If user has manager role, they can access all branches
            if ($this->userHasRole($user, 'manager')) {
                return $allBranches;
            }

            // If user has waiter or kitchen role, they can access their assigned branches
            if ($this->userHasRole($user, ['waiter', 'kitchen', 'cashier'])) {
                // If no specific branches found, allow access to all branches
                // This provides flexibility for smaller restaurants
                if ($accessibleBranches->isEmpty()) {
                    return $allBranches;
                }
            }

            // Remove duplicates and return ordered collection
            return $accessibleBranches->unique('id')->sortBy('sort_order')->values();
        });
    }

    /**
     * Get the current selected branch ID for the user
     */
    public function getCurrentBranchId(): ?int
    {
        // First check session
        $sessionBranchId = Session::get('selected_branch_id');
        
        if ($sessionBranchId) {
            // Verify the branch is accessible to the user
            $accessibleBranches = $this->getAccessibleBranches();
            if ($accessibleBranches->contains('id', $sessionBranchId)) {
                return $sessionBranchId;
            }
        }

        // If no valid session branch, get default branch
        return $this->getDefaultBranchId();
    }

    /**
     * Get the default branch ID for the user
     */
    public function getDefaultBranchId(): ?int
    {
        // Use the current model instance if available, otherwise get from auth
        $user = $this instanceof \Illuminate\Foundation\Auth\User ? $this : $this->getAuthUser();

        if (!$user) {
            return null;
        }

        $accessibleBranches = $this->getAccessibleBranches();
        
        if ($accessibleBranches->isEmpty()) {
            return null;
        }

        // Get user's employee record
        $employee = Employee::where('user_id', $user->id)->first();
        
        // Priority 1: Employee's primary branch
        if ($employee && $employee->primary_branch_id) {
            $primaryBranch = $accessibleBranches->firstWhere('id', $employee->primary_branch_id);
            if ($primaryBranch) {
                return $primaryBranch->id;
            }
        }

        // Priority 2: Branch where employee is manager
        if ($employee) {
            $managedBranch = $accessibleBranches->firstWhere('manager_id', $employee->id);
            if ($managedBranch) {
                return $managedBranch->id;
            }
        }

        // Priority 3: Main branch
        $mainBranch = $accessibleBranches->firstWhere('is_main_branch', true);
        if ($mainBranch) {
            return $mainBranch->id;
        }

        // Priority 4: First available branch
        return $accessibleBranches->first()->id;
    }

    /**
     * Set the current branch for the user session
     */
    public function setCurrentBranch(int $branchId): bool
    {
        $accessibleBranches = $this->getAccessibleBranches();
        
        if (!$accessibleBranches->contains('id', $branchId)) {
            return false;
        }

        Session::put('selected_branch_id', $branchId);
        return true;
    }

    /**
     * Get the current branch model
     */
    public function getCurrentBranch(): ?Branch
    {
        $branchId = $this->getCurrentBranchId();
        
        if (!$branchId) {
            return null;
        }

        return Branch::find($branchId);
    }

    /**
     * Check if user has access to a specific branch
     */
    public function hasAccessToBranch(int $branchId): bool
    {
        return $this->getAccessibleBranches()->contains('id', $branchId);
    }

    /**
     * Clear the branch access cache for the current user
     */
    public function clearBranchCache(): void
    {
        // Use the current model instance if available, otherwise get from auth
        $user = $this instanceof \Illuminate\Foundation\Auth\User ? $this : $this->getAuthUser();

        if ($user) {
            $this->forgetBranchCache($user->id);
        }
    }

    /**
     * Clear branch cache for specific user
     */
    public function clearBranchCacheForUser(int $userId): void
    {
        $this->forgetBranchCache($userId);
    }

    /**
     * Clear all branch caches for current tenant
     */
    public function clearAllBranchCaches(): void
    {
        try {
            if ($this->cacheSupportsTagging()) {
                // Use tags for Redis/Memcached
                $tenantId = tenant() ? tenant('id') : 'central';
                Cache::tags(["tenant_{$tenantId}", 'user_branches'])->flush();
            } else {
                // For database cache, we can't easily clear all user caches
                // This would require storing a list of all user cache keys
                // For now, we'll just log that a full cache clear was requested
                Log::info('Branch cache clear requested but cache driver does not support tagging', [
                    'tenant_id' => tenant() ? tenant('id') : null,
                    'cache_driver' => Cache::getDefaultDriver()
                ]);
            }
        } catch (\Exception $e) {
            Log::warning('Failed to clear branch caches', [
                'error' => $e->getMessage(),
                'tenant_id' => tenant() ? tenant('id') : null
            ]);
        }
    }

    /**
     * Forget cache for specific user with error handling
     */
    protected function forgetBranchCache(int $userId): void
    {
        $cacheKey = $this->getBranchCacheKey($userId);
        $success = $this->forgetWithTags($cacheKey);

        if (!$success) {
            Log::warning('Failed to clear user branch cache', [
                'user_id' => $userId,
                'cache_key' => $cacheKey,
                'tenant_id' => tenant() ? tenant('id') : null
            ]);
        }
    }

    /**
     * Generate cache key for user branch access
     */
    protected function getBranchCacheKey(int $userId): string
    {
        $tenantId = tenant() ? tenant('id') : 'central';
        return "user_branches_{$userId}_{$tenantId}";
    }

    /**
     * Check if cache driver supports tagging
     */
    protected function cacheSupportsTagging(): bool
    {
        $driver = Cache::getDefaultDriver();
        return in_array($driver, ['redis', 'memcached', 'dynamodb']);
    }

    /**
     * Get cache store instance with fallback for non-tagging drivers
     */
    protected function getCacheStore()
    {
        return Cache::store();
    }

    /**
     * Cache remember with tagging support for Redis/Memcached
     */
    protected function rememberWithTags(string $key, int $ttl, \Closure $callback)
    {
        try {
            if ($this->cacheSupportsTagging()) {
                // Use tags for Redis/Memcached
                $tenantId = tenant() ? tenant('id') : 'central';
                $tags = ["tenant_{$tenantId}", 'user_branches'];

                return Cache::tags($tags)->remember($key, $ttl, $callback);
            } else {
                // Fallback to regular cache for database/file drivers
                return Cache::remember($key, $ttl, $callback);
            }
        } catch (\Exception $e) {
            // If caching fails, log the error and execute callback directly
            Log::warning('Cache operation failed, executing callback directly', [
                'key' => $key,
                'error' => $e->getMessage(),
                'cache_driver' => Cache::getDefaultDriver(),
                'tenant_id' => tenant() ? tenant('id') : null
            ]);

            return $callback();
        }
    }

    /**
     * Enhanced cache forget with error handling
     */
    protected function forgetWithTags(string $key): bool
    {
        try {
            if ($this->cacheSupportsTagging()) {
                $tenantId = tenant() ? tenant('id') : 'central';
                $tags = ["tenant_{$tenantId}", 'user_branches'];

                return Cache::tags($tags)->forget($key);
            } else {
                return Cache::forget($key);
            }
        } catch (\Exception $e) {
            Log::warning('Failed to forget cache key', [
                'key' => $key,
                'error' => $e->getMessage(),
                'cache_driver' => Cache::getDefaultDriver(),
                'tenant_id' => tenant() ? tenant('id') : null
            ]);

            return false;
        }
    }

    /**
     * Get branch selection data for frontend
     */
    public function getBranchSelectionData(): array
    {
        $accessibleBranches = $this->getAccessibleBranches();
        $currentBranchId = $this->getCurrentBranchId();
        $currentBranch = $this->getCurrentBranch();

        return [
            'accessible_branches' => $accessibleBranches,
            'current_branch_id' => $currentBranchId,
            'current_branch' => $currentBranch,
            'has_multiple_branches' => $accessibleBranches->count() > 1,
            'can_switch_branches' => $accessibleBranches->count() > 1,
        ];
    }

    /**
     * Get the authenticated user from the appropriate guard
     */
    protected function getAuthUser()
    {
        // In tenant context, use tenant guard
        if (tenant()) {
            return auth('tenant')->user();
        }

        // In central context, use default guard
        return auth()->user();
    }

    /**
     * Check if user has specific role(s) - compatible with both central and tenant users
     */
    protected function userHasRole($user, $roles): bool
    {
        if (!$user) {
            return false;
        }

        // Normalize roles to array
        $roles = is_array($roles) ? $roles : [$roles];

        // If user has hasRole method (Spatie permissions), use it
        if (method_exists($user, 'hasRole')) {
            return $user->hasRole($roles);
        }

        // If user has hasAnyRole method, use it
        if (method_exists($user, 'hasAnyRole')) {
            return $user->hasAnyRole($roles);
        }

        // Fallback to simple role property check
        if (property_exists($user, 'role') || isset($user->role)) {
            return in_array($user->role, $roles);
        }

        return false;
    }
}
