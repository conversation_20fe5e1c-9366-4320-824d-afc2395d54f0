<?php

namespace App\Traits;

use App\Models\Tenant\Media;
use Illuminate\Database\Eloquent\Relations\MorphMany;
use Illuminate\Http\UploadedFile;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;
use Intervention\Image\Facades\Image;

trait HasMediaLibrary
{
    /**
     * Get all media for this model.
     */
    public function media(): MorphMany
    {
        return $this->morphMany(Media::class, 'model')->orderBy('order_column');
    }

    /**
     * Get media in a specific collection.
     */
    public function getMedia(string $collectionName = ''): \Illuminate\Database\Eloquent\Collection
    {
        return $this->media()
            ->when($collectionName, function ($query) use ($collectionName) {
                return $query->where('collection_name', $collectionName);
            })
            ->get();
    }

    /**
     * Get the first media item in a collection.
     */
    public function getFirstMedia(string $collectionName = ''): ?Media
    {
        return $this->getMedia($collectionName)->first();
    }

    /**
     * Get the first media URL in a collection.
     */
    public function getFirstMediaUrl(string $collectionName = '', string $conversion = ''): string
    {
        $media = $this->getFirstMedia($collectionName);
        
        if (!$media) {
            return '';
        }

        if ($conversion && $media->getConversionUrl($conversion)) {
            return $media->getConversionUrl($conversion);
        }

        return $media->url;
    }

    /**
     * Add media to this model.
     */
    public function addMedia(UploadedFile $file, string $collectionName = '', array $customProperties = []): Media
    {
        $tenantId = tenant('id');
        $fileName = $this->generateFileName($file, $tenantId);
        $disk = 'public';
        
        // Store the file
        $path = $file->storeAs("tenant_{$tenantId}/media", $fileName, $disk);
        
        // Get image dimensions if it's an image
        $width = null;
        $height = null;
        if (str_starts_with($file->getMimeType(), 'image/')) {
            try {
                $image = Image::make($file);
                $width = $image->width();
                $height = $image->height();
            } catch (\Exception $e) {
                // Ignore if we can't get dimensions
            }
        }

        // Create media record
        $media = $this->media()->create([
            'name' => pathinfo($file->getClientOriginalName(), PATHINFO_FILENAME),
            'file_name' => $path,
            'mime_type' => $file->getMimeType(),
            'disk' => $disk,
            'collection_name' => $collectionName,
            'size' => $file->getSize(),
            'manipulations' => [],
            'custom_properties' => $customProperties,
            'generated_conversions' => [],
            'responsive_images' => [],
            'width' => $width,
            'height' => $height,
            'uploaded_by' => auth()->id(),
        ]);

        // Generate conversions for images
        if ($media->isImage()) {
            $this->generateConversions($media);
        }

        return $media;
    }

    /**
     * Add media from existing media ID.
     */
    public function addMediaFromLibrary(int $mediaId, string $collectionName = ''): ?Media
    {
        $existingMedia = Media::find($mediaId);

        if (!$existingMedia) {
            return null;
        }

        // Check if this model has a many-to-many relationship with media (like MenuItem)
        if (method_exists($this, 'mediaItems')) {
            // Use the many-to-many relationship via pivot table
            $this->mediaItems()->attach($mediaId, [
                'sort_order' => $this->mediaItems()->count(),
                'is_primary' => $this->mediaItems()->count() === 0, // First image is primary
            ]);
            return $existingMedia;
        }

        // For polymorphic relationships, check if this media is already associated with this model
        $existingAssociation = $this->media()
            ->where('file_name', $existingMedia->file_name)
            ->where('collection_name', $collectionName)
            ->first();

        if ($existingAssociation) {
            // Media is already associated with this model, return existing association
            return $existingAssociation;
        }

        // Check if the existing media is unattached (from media library)
        if (is_null($existingMedia->model_type) && is_null($existingMedia->model_id)) {
            // Update the existing media record to associate it with this model
            $existingMedia->update([
                'model_type' => get_class($this),
                'model_id' => $this->id,
                'collection_name' => $collectionName,
            ]);
            return $existingMedia;
        }

        // If media is already attached to another model, create a copy
        return $this->media()->create([
            'name' => $existingMedia->name,
            'file_name' => $existingMedia->file_name,
            'mime_type' => $existingMedia->mime_type,
            'disk' => $existingMedia->disk,
            'collection_name' => $collectionName,
            'size' => $existingMedia->size,
            'manipulations' => $existingMedia->manipulations,
            'custom_properties' => $existingMedia->custom_properties,
            'generated_conversions' => $existingMedia->generated_conversions,
            'responsive_images' => $existingMedia->responsive_images,
            'alt_text' => $existingMedia->alt_text,
            'caption' => $existingMedia->caption,
            'tags' => $existingMedia->tags,
            'width' => $existingMedia->width,
            'height' => $existingMedia->height,
            'folder' => $existingMedia->folder,
            'uploaded_by' => $existingMedia->uploaded_by,
        ]);
    }

    /**
     * Clear media collection.
     */
    public function clearMediaCollection(string $collectionName = ''): void
    {
        $this->getMedia($collectionName)->each(function (Media $media) {
            $media->delete();
        });
    }

    /**
     * Generate a unique file name.
     */
    protected function generateFileName(UploadedFile $file, string $tenantId): string
    {
        $extension = $file->getClientOriginalExtension();
        $name = Str::slug(pathinfo($file->getClientOriginalName(), PATHINFO_FILENAME));
        $timestamp = now()->format('Y-m-d-H-i-s');
        $random = Str::random(8);
        
        return "{$name}-{$timestamp}-{$random}.{$extension}";
    }

    /**
     * Generate image conversions.
     */
    protected function generateConversions(Media $media): void
    {
        if (!$media->isImage()) {
            return;
        }

        $conversions = [
            'thumbnail' => ['width' => 150, 'height' => 150],
            'medium' => ['width' => 500, 'height' => 500],
            'large' => ['width' => 1200, 'height' => 1200],
        ];

        $generatedConversions = [];
        $originalPath = Storage::disk($media->disk)->path($media->file_name);

        foreach ($conversions as $name => $dimensions) {
            try {
                $image = Image::make($originalPath);
                
                // Resize while maintaining aspect ratio
                $image->resize($dimensions['width'], $dimensions['height'], function ($constraint) {
                    $constraint->aspectRatio();
                    $constraint->upsize();
                });

                // Generate conversion path
                $conversionPath = str_replace(
                    $media->name . '.' . $media->extension,
                    $name . '/' . $media->name . '.' . $media->extension,
                    $media->file_name
                );

                // Save conversion
                $fullConversionPath = Storage::disk($media->disk)->path($conversionPath);
                $conversionDir = dirname($fullConversionPath);
                
                if (!is_dir($conversionDir)) {
                    mkdir($conversionDir, 0755, true);
                }

                $image->save($fullConversionPath, 85); // 85% quality
                $generatedConversions[$name] = true;

            } catch (\Exception $e) {
                // Log error but continue
                \Log::error("Failed to generate {$name} conversion for media {$media->id}: " . $e->getMessage());
            }
        }

        $media->update(['generated_conversions' => $generatedConversions]);
    }

    /**
     * Check if model has media in collection.
     */
    public function hasMedia(string $collectionName = ''): bool
    {
        return $this->getMedia($collectionName)->isNotEmpty();
    }

    /**
     * Get media count in collection.
     */
    public function getMediaCount(string $collectionName = ''): int
    {
        return $this->getMedia($collectionName)->count();
    }
}
