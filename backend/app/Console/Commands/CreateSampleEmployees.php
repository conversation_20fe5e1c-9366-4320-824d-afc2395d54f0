<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\Tenant\Employee;
use Spatie\Permission\Models\Role;

class CreateSampleEmployees extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'tenant:create-sample-employees';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Create sample employees with roles for testing';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('Creating sample employees...');

        // Get or create a restaurant
        $restaurant = \App\Models\Tenant\Restaurant::first();
        if (!$restaurant) {
            $restaurant = \App\Models\Tenant\Restaurant::create([
                'name' => 'Demo Restaurant',
                'slug' => 'demo-restaurant',
                'description' => 'A demo restaurant for testing',
                'address' => '123 Main St',
                'city' => 'Demo City',
                'state' => 'Demo State',
                'country' => 'Demo Country',
                'phone' => '+1234567890',
                'email' => '<EMAIL>',
                'is_active' => true,
            ]);
        }

        // Get or create departments
        $managementDept = \App\Models\Tenant\Department::firstOrCreate(
            ['name' => 'Management'],
            [
                'description' => 'Management Department',
                'is_active' => true,
                'sort_order' => 1,
            ]
        );

        $kitchenDept = \App\Models\Tenant\Department::firstOrCreate(
            ['name' => 'Kitchen'],
            [
                'description' => 'Kitchen Department',
                'is_active' => true,
                'sort_order' => 2,
            ]
        );

        $serviceDept = \App\Models\Tenant\Department::firstOrCreate(
            ['name' => 'Service'],
            [
                'description' => 'Service Department',
                'is_active' => true,
                'sort_order' => 3,
            ]
        );

        // Sample employee data
        $employees = [
            [
                'name' => 'John Manager',
                'first_name' => 'John',
                'last_name' => 'Manager',
                'username' => 'john.manager',
                'email' => '<EMAIL>',
                'phone' => '+1234567890',
                'position' => 'Restaurant Manager',
                'employee_type' => 'manager',
                'employment_type' => 'full_time',
                'salary' => 50000,
                'hire_date' => now()->subMonths(12),
                'status' => 'active',
                'is_active' => true,
                'department_id' => $managementDept->id,
                'role' => 'Manager'
            ],
            [
                'name' => 'Sarah Smith',
                'first_name' => 'Sarah',
                'last_name' => 'Smith',
                'username' => 'sarah.smith',
                'email' => '<EMAIL>',
                'phone' => '+1234567891',
                'position' => 'Assistant Manager',
                'employee_type' => 'manager',
                'employment_type' => 'full_time',
                'salary' => 40000,
                'hire_date' => now()->subMonths(8),
                'status' => 'active',
                'is_active' => true,
                'department_id' => $managementDept->id,
                'role' => 'Manager'
            ],
            [
                'name' => 'Mike Johnson',
                'first_name' => 'Mike',
                'last_name' => 'Johnson',
                'username' => 'mike.johnson',
                'email' => '<EMAIL>',
                'phone' => '+1234567892',
                'position' => 'Head Waiter',
                'employee_type' => 'waiter',
                'employment_type' => 'part_time',
                'hourly_rate' => 15.50,
                'hire_date' => now()->subMonths(6),
                'status' => 'active',
                'is_active' => true,
                'department_id' => $serviceDept->id,
                'role' => 'Waiter'
            ],
            [
                'name' => 'Lisa Chen',
                'first_name' => 'Lisa',
                'last_name' => 'Chen',
                'username' => 'lisa.chen',
                'email' => '<EMAIL>',
                'phone' => '+1234567893',
                'position' => 'Head Chef',
                'employee_type' => 'chef',
                'employment_type' => 'full_time',
                'salary' => 45000,
                'hire_date' => now()->subMonths(18),
                'status' => 'active',
                'is_active' => true,
                'department_id' => $kitchenDept->id,
                'role' => 'Chef'
            ]
        ];

        foreach ($employees as $employeeData) {
            $role = $employeeData['role'];
            unset($employeeData['role']);

            // Add restaurant_id
            $employeeData['restaurant_id'] = $restaurant->id;

            // Check if employee already exists
            $existingEmployee = Employee::where('username', $employeeData['username'])->first();
            if ($existingEmployee) {
                // Just assign role if employee exists
                if (!$existingEmployee->hasRole($role)) {
                    $existingEmployee->assignRole($role);
                    $this->info("Assigned role {$role} to existing employee: {$existingEmployee->name}");
                } else {
                    $this->info("Employee {$existingEmployee->name} already has role: {$role}");
                }
                continue;
            }

            // Create employee
            $employee = Employee::create($employeeData);

            // Assign role
            $employee->assignRole($role);

            $this->info("Created employee: {$employee->name} with role: {$role}");
        }

        $this->info('Sample employees created successfully!');
    }
}
