<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\Tenant\MenuItem;
use App\Models\Tenant\Category;
use Illuminate\Support\Facades\Schema;

class TestMenuItemCategories extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'test:menu-item-categories';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Test the many-to-many relationship between menu items and categories';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('Testing Menu Item Categories Many-to-Many Relationship...');

        // Check if tables exist
        if (!Schema::hasTable('menu_items')) {
            $this->error('menu_items table does not exist');
            return 1;
        }

        if (!Schema::hasTable('categories')) {
            $this->error('categories table does not exist');
            return 1;
        }

        if (!Schema::hasTable('menu_item_categories')) {
            $this->error('menu_item_categories table does not exist');
            return 1;
        }

        $this->info('✓ All required tables exist');

        // Test creating categories
        $this->info('Creating test categories...');
        $category1 = Category::firstOrCreate(['name' => 'Test Appetizers', 'slug' => 'test-appetizers']);
        $category2 = Category::firstOrCreate(['name' => 'Test Main Course', 'slug' => 'test-main-course']);
        $category3 = Category::firstOrCreate(['name' => 'Test Beverages', 'slug' => 'test-beverages']);

        $this->info("✓ Created categories: {$category1->name}, {$category2->name}, {$category3->name}");

        // Test creating a menu item
        $this->info('Creating test menu item...');
        $menuItem = MenuItem::firstOrCreate([
            'name' => 'Test Multi-Category Item',
            'slug' => 'test-multi-category-item',
            'price' => 15.99,
            'is_active' => true,
            'is_available' => true,
        ]);

        $this->info("✓ Created menu item: {$menuItem->name}");

        // Test attaching multiple categories
        $this->info('Attaching multiple categories to menu item...');
        $menuItem->categories()->sync([$category1->id, $category2->id, $category3->id]);

        $this->info('✓ Attached categories to menu item');

        // Test retrieving categories for menu item
        $this->info('Testing category retrieval...');
        $attachedCategories = $menuItem->categories()->get();
        $this->info("✓ Menu item has {$attachedCategories->count()} categories:");
        foreach ($attachedCategories as $category) {
            $this->info("  - {$category->name}");
        }

        // Test retrieving menu items for category
        $this->info('Testing menu item retrieval from category...');
        $menuItemsInCategory = $category1->menuItems()->get();
        $this->info("✓ Category '{$category1->name}' has {$menuItemsInCategory->count()} menu items:");
        foreach ($menuItemsInCategory as $item) {
            $this->info("  - {$item->name}");
        }

        // Test removing a category
        $this->info('Testing category removal...');
        $menuItem->categories()->detach($category3->id);
        $remainingCategories = $menuItem->categories()->get();
        $this->info("✓ After removal, menu item has {$remainingCategories->count()} categories:");
        foreach ($remainingCategories as $category) {
            $this->info("  - {$category->name}");
        }

        $this->info('✅ All tests passed! Many-to-many relationship is working correctly.');
        return 0;
    }
}
