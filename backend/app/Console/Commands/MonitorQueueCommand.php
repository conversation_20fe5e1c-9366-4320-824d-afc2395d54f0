<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;

class MonitorQueueCommand extends Command
{
    /**
     * The name and signature of the console command.
     */
    protected $signature = 'queue:monitor {--queue=tenant-creation}';

    /**
     * The console command description.
     */
    protected $description = 'Monitor queue jobs status';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $queue = $this->option('queue');
        
        $this->info("Monitoring queue: {$queue}");
        $this->line('');

        // Check pending jobs
        $pendingJobs = DB::table('jobs')
            ->where('queue', $queue)
            ->orderBy('created_at', 'desc')
            ->get();

        $this->info("Pending Jobs ({$pendingJobs->count()}):");
        if ($pendingJobs->count() > 0) {
            foreach ($pendingJobs as $job) {
                $payload = json_decode($job->payload, true);
                $jobClass = $payload['displayName'] ?? 'Unknown';
                $createdAt = date('Y-m-d H:i:s', $job->created_at);
                $availableAt = date('Y-m-d H:i:s', $job->available_at);
                
                $this->line("- {$jobClass} (ID: {$job->id})");
                $this->line("  Created: {$createdAt}");
                $this->line("  Available: {$availableAt}");
                $this->line("  Attempts: {$job->attempts}");
                $this->line('');
            }
        } else {
            $this->line("No pending jobs in queue: {$queue}");
        }

        // Check failed jobs
        $failedJobs = DB::table('failed_jobs')
            ->where('queue', $queue)
            ->orderBy('failed_at', 'desc')
            ->limit(10)
            ->get();

        $this->line('');
        $this->error("Failed Jobs ({$failedJobs->count()}):");
        if ($failedJobs->count() > 0) {
            foreach ($failedJobs as $job) {
                $payload = json_decode($job->payload, true);
                $jobClass = $payload['displayName'] ?? 'Unknown';
                
                $this->line("- {$jobClass} (UUID: {$job->uuid})");
                $this->line("  Failed: {$job->failed_at}");
                $this->line("  Exception: " . substr($job->exception, 0, 100) . '...');
                $this->line('');
            }
        } else {
            $this->line("No failed jobs in queue: {$queue}");
        }

        return 0;
    }
}
