<?php

namespace App\Console\Commands;

use App\Models\Tenant;
use App\Models\SubscriptionPlan;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Artisan;

class SetupDemoTenant extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'tenant:setup-demo {--force : Force setup even if tenant exists}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Setup demo restaurant tenant with migrations and basic data';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('🏗️  Setting up demo restaurant tenant...');

        try {
            // Check if tenant exists
            $tenant = Tenant::find('demo-restaurant');
            
            if ($tenant && !$this->option('force')) {
                $this->warn('Demo tenant already exists. Use --force to recreate.');
                return 1;
            }

            // Create or update tenant
            $this->info('📝 Creating tenant record...');
            $plan = SubscriptionPlan::first();
            
            $tenant = Tenant::updateOrCreate(
                ['id' => 'demo-restaurant'],
                [
                    'name' => 'Demo Restaurant',
                    'email' => '<EMAIL>',
                    'phone' => '******-0123',
                    'address' => '123 Main Street',
                    'city' => 'New York',
                    'state' => 'NY',
                    'country' => 'USA',
                    'postal_code' => '10001',
                    'timezone' => 'America/New_York',
                    'currency' => 'USD',
                    'language' => 'en',
                    'subscription_plan_id' => $plan?->id,
                    'subscription_status' => 'active',
                    'trial_ends_at' => now()->addDays(30),
                ]
            );

            $this->info("✅ Tenant created: {$tenant->name}");

            // Create domain
            $this->info('🌐 Creating domain...');
            $domain = $tenant->domains()->updateOrCreate([
                'domain' => 'demo-restaurant.localhost'
            ]);

            $this->info("✅ Domain created: {$domain->domain}");

            // Run tenant migrations
            $this->info('🔄 Running tenant migrations...');
            
            $tenant->run(function () {
                $this->info('  📦 Migrating tenant database...');
                
                Artisan::call('migrate', [
                    '--path' => 'database/migrations/tenant',
                    '--force' => true
                ]);
                
                $this->info('  ✅ Tenant migrations completed');
                
                // Check if essential tables exist
                $tables = ['sessions', 'cache', 'restaurants'];
                foreach ($tables as $table) {
                    if (\Schema::hasTable($table)) {
                        $this->info("  ✅ Table '{$table}' exists");
                    } else {
                        $this->warn("  ⚠️  Table '{$table}' missing");
                    }
                }
            });

            // Seed tenant data (optional)
            if ($this->confirm('Would you like to seed demo data for the restaurant?', true)) {
                $this->info('🌱 Seeding tenant data...');
                
                $tenant->run(function () {
                    // Create demo restaurant record
                    \DB::table('restaurants')->updateOrInsert(
                        ['slug' => 'demo-restaurant'],
                        [
                            'name' => 'Demo Restaurant',
                            'slug' => 'demo-restaurant',
                            'description' => 'A wonderful demo restaurant for testing the multi-tenant system',
                            'phone' => '******-0123',
                            'email' => '<EMAIL>',
                            'address' => '123 Main Street',
                            'city' => 'New York',
                            'state' => 'NY',
                            'postal_code' => '10001',
                            'country' => 'USA',
                            'timezone' => 'America/New_York',
                            'currency' => 'USD',
                            'is_active' => true,
                            'created_at' => now(),
                            'updated_at' => now(),
                        ]
                    );
                    
                    $this->info('  ✅ Demo restaurant data seeded');
                });
            }

            $this->info('');
            $this->info('🎉 Demo tenant setup completed successfully!');
            $this->info('');
            $this->info('📋 Access Information:');
            $this->info("   Tenant ID: {$tenant->id}");
            $this->info("   Tenant Name: {$tenant->name}");
            $this->info("   Domain: {$domain->domain}");
            $this->info("   Database: tenant_{$tenant->id}");
            $this->info('');
            $this->info('🌐 Access URLs:');
            $this->info('   Restaurant: http://demo-restaurant.localhost:8000');
            $this->info('   Login: http://demo-restaurant.localhost:8000/login');
            $this->info('');
            $this->info('🔐 Test Credentials:');
            $this->info('   Manager: <EMAIL> / Manager@2024');
            $this->info('   Waiter: <EMAIL> / Waiter@2024');
            $this->info('   Kitchen: <EMAIL> / Kitchen@2024');
            $this->info('   Delivery: <EMAIL> / Delivery@2024');

            return 0;

        } catch (\Exception $e) {
            $this->error('❌ Error setting up demo tenant: ' . $e->getMessage());
            $this->error('File: ' . $e->getFile() . ':' . $e->getLine());
            return 1;
        }
    }
}
