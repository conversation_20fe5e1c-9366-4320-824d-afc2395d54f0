<?php

namespace App\Console\Commands;

use App\Models\User;
use App\Models\Tenant\Branch;
use App\Models\Tenant\Employee;
use App\Models\Tenant\Floor;
use App\Models\Tenant\Table;
use Illuminate\Console\Command;
use Spatie\Permission\Models\Role;

class SetupPOSAccess extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'pos:setup-access {--user-id= : User ID to setup access for} {--tenant= : Tenant ID}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Setup POS access for a user by creating necessary branches, employee records, and roles';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $userId = $this->option('user-id');
        $tenantId = $this->option('tenant');

        if (!$userId) {
            $userId = $this->ask('Enter User ID');
        }

        if (!$tenantId) {
            $tenantId = $this->ask('Enter Tenant ID (subdomain)');
        }

        // Switch to tenant context
        $tenant = \App\Models\Tenant::find($tenantId);
        if (!$tenant) {
            $this->error("Tenant '{$tenantId}' not found!");
            return 1;
        }

        tenancy()->initialize($tenant);

        // Get user
        $user = User::find($userId);
        if (!$user) {
            $this->error("User with ID '{$userId}' not found!");
            return 1;
        }

        $this->info("Setting up POS access for user: {$user->name} ({$user->email})");
        $this->info("Tenant: {$tenant->name} ({$tenant->id})");

        // Create roles if they don't exist
        $this->createRoles();

        // Assign manager role to user
        if (!$user->hasRole('manager')) {
            $user->assignRole('manager');
            $this->info("✅ Assigned 'manager' role to user");
        } else {
            $this->info("ℹ️  User already has 'manager' role");
        }

        // Create branch if none exist
        $this->createBranches();

        // Create employee record
        $this->createEmployeeRecord($user);

        // Create floors and tables
        $this->createFloorsAndTables();

        $this->info("🎉 POS access setup completed!");
        $this->info("User can now access POS system at: https://{$tenant->id}.yourdomain.com/pos");

        return 0;
    }

    private function createRoles()
    {
        $roles = ['manager', 'waiter', 'kitchen', 'cashier'];

        foreach ($roles as $roleName) {
            if (!Role::where('name', $roleName)->exists()) {
                Role::create(['name' => $roleName]);
                $this->info("✅ Created role: {$roleName}");
            }
        }
    }

    private function createBranches()
    {
        if (Branch::count() === 0) {
            $branch = Branch::create([
                'name' => 'Main Branch',
                'address' => '123 Main Street',
                'phone' => '+1234567890',
                'email' => '<EMAIL>',
                'is_main_branch' => true,
                'is_active' => true,
                'sort_order' => 1,
                'operating_hours' => [
                    'monday' => ['open' => '09:00', 'close' => '22:00'],
                    'tuesday' => ['open' => '09:00', 'close' => '22:00'],
                    'wednesday' => ['open' => '09:00', 'close' => '22:00'],
                    'thursday' => ['open' => '09:00', 'close' => '22:00'],
                    'friday' => ['open' => '09:00', 'close' => '23:00'],
                    'saturday' => ['open' => '09:00', 'close' => '23:00'],
                    'sunday' => ['open' => '10:00', 'close' => '21:00'],
                ],
            ]);

            $this->info("✅ Created main branch: {$branch->name}");
        } else {
            $this->info("ℹ️  Branches already exist");
        }
    }

    private function createEmployeeRecord(User $user)
    {
        $employee = Employee::where('user_id', $user->id)->first();

        if (!$employee) {
            $mainBranch = Branch::where('is_main_branch', true)->first() ?? Branch::first();

            $employee = Employee::create([
                'user_id' => $user->id,
                'employee_id' => 'EMP' . str_pad($user->id, 4, '0', STR_PAD_LEFT),
                'first_name' => explode(' ', $user->name)[0] ?? $user->name,
                'last_name' => explode(' ', $user->name, 2)[1] ?? '',
                'email' => $user->email,
                'phone' => '+1234567890',
                'position' => 'Manager',
                'department' => 'Management',
                'hire_date' => now(),
                'primary_branch_id' => $mainBranch?->id,
                'salary' => 50000,
                'is_active' => true,
            ]);

            // Set as manager of the main branch
            if ($mainBranch && !$mainBranch->manager_id) {
                $mainBranch->update(['manager_id' => $employee->id]);
                $this->info("✅ Set user as manager of main branch");
            }

            $this->info("✅ Created employee record for user");
        } else {
            $this->info("ℹ️  Employee record already exists");
        }
    }

    private function createFloorsAndTables()
    {
        $branch = Branch::first();
        if (!$branch) {
            return;
        }

        if (Floor::count() === 0) {
            $floor = Floor::create([
                'branch_id' => $branch->id,
                'name' => 'Ground Floor',
                'description' => 'Main dining area',
                'sort_order' => 1,
                'is_active' => true,
            ]);

            $this->info("✅ Created floor: {$floor->name}");

            // Create some tables
            for ($i = 1; $i <= 10; $i++) {
                Table::create([
                    'floor_id' => $floor->id,
                    'name' => "Table {$i}",
                    'number' => $i,
                    'capacity' => rand(2, 8),
                    'status' => 'available',
                    'position_x' => rand(10, 90),
                    'position_y' => rand(10, 90),
                    'is_active' => true,
                ]);
            }

            $this->info("✅ Created 10 tables");
        } else {
            $this->info("ℹ️  Floors and tables already exist");
        }
    }
}
