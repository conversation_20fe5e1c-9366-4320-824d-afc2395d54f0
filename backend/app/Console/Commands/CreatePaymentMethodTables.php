<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\DB;
use App\Models\Tenant\PaymentMethod;

class CreatePaymentMethodTables extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'payment-methods:create-tables {tenant?}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Create payment method tables for tenant';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $tenantId = $this->argument('tenant') ?? 'khanas';

        $this->info("Creating payment method tables for tenant: {$tenantId}");

        try {
            // Set tenant context
            $tenant = \App\Models\Tenant::find($tenantId);
            if (!$tenant) {
                $this->error("Tenant {$tenantId} not found");
                return 1;
            }

            tenancy()->initialize($tenant);

            // Create payment_methods table
            if (!Schema::hasTable('payment_methods')) {
                Schema::create('payment_methods', function (Blueprint $table) {
                    $table->id();
                    $table->string('name')->unique();
                    $table->enum('type', ['cash', 'card', 'bank_transfer', 'digital_wallet', 'other'])->default('other');
                    $table->text('instructions')->nullable();
                    $table->boolean('is_active')->default(true);
                    $table->integer('display_order')->default(0);
                    $table->string('icon')->nullable();
                    $table->string('image')->nullable();
                    $table->timestamps();
                    $table->softDeletes();

                    $table->index(['is_active', 'display_order']);
                });
                $this->info('✓ Created payment_methods table');
            } else {
                $this->info('✓ payment_methods table already exists');
            }

            // Create payment_method_fields table
            if (!Schema::hasTable('payment_method_fields')) {
                Schema::create('payment_method_fields', function (Blueprint $table) {
                    $table->id();
                    $table->foreignId('payment_method_id')->constrained()->onDelete('cascade');
                    $table->string('field_name');
                    $table->enum('field_type', ['text', 'number', 'select', 'textarea', 'checkbox', 'email', 'tel', 'date']);
                    $table->string('field_label');
                    $table->boolean('is_required')->default(false);
                    $table->text('default_value')->nullable();
                    $table->json('validation_rules')->nullable();
                    $table->json('select_options')->nullable();
                    $table->integer('display_order')->default(0);
                    $table->string('placeholder')->nullable();
                    $table->text('help_text')->nullable();
                    $table->timestamps();

                    $table->index(['payment_method_id', 'display_order']);
                    $table->unique(['payment_method_id', 'field_name']);
                });
                $this->info('✓ Created payment_method_fields table');
            } else {
                $this->info('✓ payment_method_fields table already exists');
            }

            // Update orders table
            if (Schema::hasTable('orders')) {
                if (!Schema::hasColumn('orders', 'payment_method_id')) {
                    Schema::table('orders', function (Blueprint $table) {
                        $table->foreignId('payment_method_id')->nullable()->constrained()->onDelete('set null');
                        $table->index(['payment_method_id']);
                    });
                    $this->info('✓ Added payment_method_id to orders table');
                } else {
                    $this->info('✓ orders.payment_method_id already exists');
                }

                if (!Schema::hasColumn('orders', 'payment_details')) {
                    Schema::table('orders', function (Blueprint $table) {
                        $table->json('payment_details')->nullable();
                    });
                    $this->info('✓ Added payment_details to orders table');
                } else {
                    $this->info('✓ orders.payment_details already exists');
                }
            } else {
                $this->error('orders table does not exist');
                return 1;
            }

            // Create default payment methods
            $this->info('Creating default payment methods...');
            PaymentMethod::createDefaults();
            $this->info('✓ Default payment methods created');

            // Test the system
            $this->info('Testing payment methods system...');
            $paymentMethods = PaymentMethod::with('fields')->get();
            $this->info("✓ Found {$paymentMethods->count()} payment methods");

            foreach ($paymentMethods as $method) {
                $this->info("  - {$method->name} ({$method->type}) - {$method->fields->count()} fields");
            }

            $this->info('Payment method tables created successfully!');
            return 0;

        } catch (\Exception $e) {
            $this->error('Error: ' . $e->getMessage());
            $this->error('Trace: ' . $e->getTraceAsString());
            return 1;
        }
    }
}
