<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\App;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Session;
use Symfony\Component\HttpFoundation\Response;

class SetLocale
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        // Get locale from various sources in order of priority
        $locale = $this->getLocale($request);

        // Set the application locale
        App::setLocale($locale);

        // Store in session for persistence
        Session::put('locale', $locale);

        return $next($request);
    }

    /**
     * Get the locale from various sources
     */
    private function getLocale(Request $request): string
    {
        // 1. Check if locale is being changed via request
        if ($request->has('locale') && $this->isValidLocale($request->get('locale'))) {
            return $request->get('locale');
        }

        // 2. Check authenticated user's preference
        if (Auth::check() && Auth::user()->preferred_language) {
            $userLocale = Auth::user()->preferred_language;
            if ($this->isValidLocale($userLocale)) {
                return $userLocale;
            }
        }

        // 3. Check session
        if (Session::has('locale') && $this->isValidLocale(Session::get('locale'))) {
            return Session::get('locale');
        }

        // 4. Check browser preference
        $browserLocale = $request->getPreferredLanguage($this->getSupportedLocales());
        if ($browserLocale && $this->isValidLocale($browserLocale)) {
            return $browserLocale;
        }

        // 5. Fall back to default
        return config('app.locale', 'en');
    }

    /**
     * Check if the given locale is valid/supported
     */
    private function isValidLocale(string $locale): bool
    {
        return in_array($locale, $this->getSupportedLocales());
    }

    /**
     * Get list of supported locales
     */
    private function getSupportedLocales(): array
    {
        return ['en', 'bn']; // English and Bengali
    }
}
