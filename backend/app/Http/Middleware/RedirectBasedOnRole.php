<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;

class RedirectBasedOnRole
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        // Determine which guard to use based on context
        $guard = tenant() ? 'tenant' : 'web';

        if (auth($guard)->check()) {
            $user = auth($guard)->user();

            // If user is accessing generic dashboard, redirect to role-specific dashboard
            if ($request->routeIs('dashboard')) {
                $dashboardRoute = $user->getDashboardRoute();

                // Get current URL to compare
                $currentUrl = $request->url();

                // Only redirect if it's different from current route and not already the target
                if ($dashboardRoute !== $currentUrl && $dashboardRoute !== route('dashboard')) {
                    return redirect($dashboardRoute);
                }
            }
        }

        return $next($request);
    }
}
