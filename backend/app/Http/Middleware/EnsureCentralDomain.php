<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;

class EnsureCentralDomain
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        $currentDomain = $request->getHost();
        $centralDomains = config('tenancy.central_domains', ['localhost', '127.0.0.1']);

        // If not a central domain, return 404
        if (!in_array($currentDomain, $centralDomains)) {
            dd("ok0s");
            abort(404);
        }

        return $next($request);
    }
}
