<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Symfony\Component\HttpFoundation\Response;

class EnsureTenantContext
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        // Check if we're in a tenant context
        if (!tenant()) {
            Log::error('Request made outside tenant context', [
                'url' => $request->fullUrl(),
                'ip' => $request->ip(),
                'user_agent' => $request->userAgent(),
            ]);

            // Redirect to main domain or show error
            if ($request->expectsJson()) {
                return response()->json([
                    'error' => 'Tenant context required',
                    'message' => 'This request must be made from a valid tenant domain.'
                ], 400);
            }

            // For web requests, redirect to main domain
            return redirect()->to(config('app.url') . '/admin');
        }

        // Log tenant context for debugging
        Log::debug('Request in tenant context', [
            'tenant_id' => tenant('id'),
            'tenant_name' => tenant('name'),
            'url' => $request->fullUrl(),
        ]);

        return $next($request);
    }
}
