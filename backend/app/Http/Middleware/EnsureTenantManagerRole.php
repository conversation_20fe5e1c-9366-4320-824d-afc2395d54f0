<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;
use App\Services\TenantRoleService;
use Illuminate\Support\Facades\Auth;

class EnsureTenantManagerRole
{
    protected $tenantRoleService;

    public function __construct(TenantRoleService $tenantRoleService)
    {
        $this->tenantRoleService = $tenantRoleService;
    }

    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        // Only process if user is authenticated and we're in a tenant context
        if (Auth::check() && tenant()) {
            $user = Auth::user();
            $tenant = tenant();

            // Check if this user should have manager access to this tenant
            // For now, we'll assume any authenticated user in a tenant context should have manager role
            // In a production system, you'd check if the user is the tenant owner
            if (!$user->hasRole('restaurant_manager') && !$user->hasRole('admin')) {
                // Auto-assign restaurant_manager role for tenant access
                $this->tenantRoleService->assignTenantOwnerRole($user, $tenant);
            }
        }

        return $next($request);
    }
}
