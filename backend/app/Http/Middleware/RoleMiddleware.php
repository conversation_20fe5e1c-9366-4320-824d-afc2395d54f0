<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;

class RoleMiddleware
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next, ...$roles): Response
    {
        // Determine which guard to use based on context
        $guard = tenant() ? 'tenant' : 'web';

        if (!auth($guard)->check()) {
            return redirect()->route('login');
        }

        $user = auth($guard)->user();

        // Check if user has any of the required roles
        foreach ($roles as $role) {
            if ($this->userHasRole($user, $role)) {
                return $next($request);
            }
        }

        // If user doesn't have required role, redirect to appropriate dashboard
        return redirect($user->getDashboardRoute())
            ->with('error', 'You do not have permission to access this area.');
    }

    /**
     * Check if user has specific role - compatible with both central and tenant users
     */
    protected function userHasRole($user, $role): bool
    {
        if (!$user) {
            return false;
        }

        // If user has hasRole method (Spatie permissions), use it
        if (method_exists($user, 'hasRole')) {
            return $user->hasRole($role);
        }

        // If user has hasAnyRole method, use it
        if (method_exists($user, 'hasAnyRole')) {
            return $user->hasAnyRole($role);
        }

        // Fallback to simple role property check
        if (property_exists($user, 'role') || isset($user->role)) {
            return $user->role === $role;
        }

        return false;
    }
}
