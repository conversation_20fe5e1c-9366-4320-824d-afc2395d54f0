<?php

namespace App\Http\Middleware;

use App\Services\BranchSelectionService;
use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Symfony\Component\HttpFoundation\Response;

class EnsureBranchAccess
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        // Skip for non-authenticated users (let auth middleware handle it)
        if (!auth()->check()) {
            return $next($request);
        }

        // Check branch access
        $accessCheck = BranchSelectionService::ensureBranchAccess();

        // Log branch access attempts for debugging
        Log::info('Branch access check', [
            'user_id' => auth()->id(),
            'tenant_id' => tenant() ? tenant('id') : null,
            'route' => $request->route()->getName(),
            'has_access' => $accessCheck['has_access'],
            'message' => $accessCheck['message'] ?? null,
            'redirect_route' => $accessCheck['redirect_route'] ?? null,
        ]);

        if (!$accessCheck['has_access']) {
            // For API requests, return JSON error
            if ($request->expectsJson()) {
                return response()->json([
                    'error' => $accessCheck['message'],
                    'redirect' => route($accessCheck['redirect_route']),
                    'debug_info' => [
                        'user_id' => auth()->id(),
                        'tenant_id' => tenant() ? tenant('id') : null,
                        'route' => $request->route()->getName(),
                    ]
                ], 403);
            }

            // For web requests, redirect with error message
            return redirect()->route($accessCheck['redirect_route'])
                ->with('error', $accessCheck['message'])
                ->with('debug_info', [
                    'user_id' => auth()->id(),
                    'tenant_id' => tenant() ? tenant('id') : null,
                    'route' => $request->route()->getName(),
                ]);
        }

        return $next($request);
    }
}
