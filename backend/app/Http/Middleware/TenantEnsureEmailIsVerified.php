<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Contracts\Auth\MustVerifyEmail;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Redirect;
use Illuminate\Support\Facades\URL;
use Symfony\Component\HttpFoundation\Response;

class TenantEnsureEmailIsVerified
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next, string $redirectToRoute = null): Response
    {
        // Get the authenticated user from the tenant guard
        $user = auth('tenant')->user();

        if (!$user) {
            Log::debug('TenantEnsureEmailIsVerified: No authenticated user found');
            return redirect()->route('login');
        }

        // Check if the user implements MustVerifyEmail
        if (!($user instanceof MustVerifyEmail)) {
            Log::debug('TenantEnsureEmailIsVerified: User does not implement MustVerifyEmail, allowing access');
            return $next($request);
        }

        // Check if email is verified
        if (!$user->hasVerifiedEmail()) {
            Log::info('TenantEnsureEmailIsVerified: User email not verified', [
                'user_id' => $user->id,
                'email' => $user->email,
                'tenant_id' => tenant() ? tenant('id') : null,
            ]);

            // For tenant users, we'll auto-verify them for now
            // In production, you might want to implement proper email verification
            if (tenant()) {
                Log::info('Auto-verifying tenant user email', [
                    'user_id' => $user->id,
                    'email' => $user->email,
                    'tenant_id' => tenant('id'),
                ]);
                
                $user->markEmailAsVerified();
                return $next($request);
            }

            // If not in tenant context, redirect to verification page
            return $request->expectsJson()
                ? abort(403, 'Your email address is not verified.')
                : Redirect::guest(URL::route($redirectToRoute ?: 'verification.notice'));
        }

        Log::debug('TenantEnsureEmailIsVerified: Email verified, allowing access');
        return $next($request);
    }
}
