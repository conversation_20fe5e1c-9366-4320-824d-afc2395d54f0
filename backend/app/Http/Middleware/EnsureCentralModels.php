<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;

class EnsureCentralModels
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        // Ensure User, Role, and Permission models always use central database
        // This is a safety net in case the CentralConnection trait doesn't work
        
        // Force User model to use central connection
        if (method_exists(\App\Models\User::class, 'setConnection')) {
            \App\Models\User::setConnection(config('tenancy.database.central_connection', 'mysql'));
        }
        
        // Force Role model to use central connection
        if (class_exists(\App\Models\Role::class) && method_exists(\App\Models\Role::class, 'setConnection')) {
            \App\Models\Role::setConnection(config('tenancy.database.central_connection', 'mysql'));
        }
        
        // Force Permission model to use central connection
        if (class_exists(\App\Models\Permission::class) && method_exists(\App\Models\Permission::class, 'setConnection')) {
            \App\Models\Permission::setConnection(config('tenancy.database.central_connection', 'mysql'));
        }

        return $next($request);
    }
}
