<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;

class SetTenantLocale
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        // Get locale from session, fallback to default
        $locale = session('locale', config('app.locale', 'en'));
        
        // Ensure the locale is supported
        if (!in_array($locale, ['en', 'bn'])) {
            $locale = 'en';
        }
        
        // Set the application locale
        app()->setLocale($locale);
        
        return $next($request);
    }
}
