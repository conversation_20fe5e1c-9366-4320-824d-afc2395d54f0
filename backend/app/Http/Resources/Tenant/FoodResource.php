<?php

namespace App\Http\Resources\Tenant;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class FoodResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'name' => $this->name,
            'slug' => $this->slug,
            'description' => $this->description,
            'ingredients' => $this->ingredients,
            
            // Images
            'images' => $this->images,
            'primary_image' => $this->primary_image,
            
            // Category
            'food_category_id' => $this->food_category_id,
            'category' => $this->whenLoaded('category', function () {
                return [
                    'id' => $this->category->id,
                    'name' => $this->category->name,
                    'slug' => $this->category->slug,
                    'description' => $this->category->description,
                ];
            }),
            
            // Pricing
            'base_price' => $this->base_price,
            'sale_price' => $this->sale_price,
            'current_price' => $this->current_price,
            'cost_price' => $this->cost_price,
            'discount_percentage' => $this->discount_percentage,
            
            // Nutritional information
            'calories' => $this->calories,
            'protein' => $this->protein,
            'carbs' => $this->carbs,
            'fat' => $this->fat,
            'fiber' => $this->fiber,
            'sugar' => $this->sugar,
            'sodium' => $this->sodium,
            
            // Dietary flags
            'is_vegetarian' => $this->is_vegetarian,
            'is_vegan' => $this->is_vegan,
            'is_gluten_free' => $this->is_gluten_free,
            'is_dairy_free' => $this->is_dairy_free,
            'is_nut_free' => $this->is_nut_free,
            'is_spicy' => $this->is_spicy,
            'is_halal' => $this->is_halal,
            'is_kosher' => $this->is_kosher,
            'dietary_flags' => $this->dietary_flags,
            
            // Kitchen information
            'preparation_time' => $this->preparation_time,
            'cooking_time' => $this->cooking_time,
            'spice_level' => $this->spice_level,
            'kitchen_notes' => $this->kitchen_notes,
            
            // Display settings
            'sort_order' => $this->sort_order,
            'is_featured' => $this->is_featured,
            'is_popular' => $this->is_popular,
            'is_new' => $this->is_new,
            'is_available' => $this->is_available,
            'is_active' => $this->is_active,
            
            // Availability
            'available_days' => $this->available_days,
            'available_from' => $this->available_from,
            'available_until' => $this->available_until,
            'is_available_now' => $this->isAvailableNow(),
            
            // Stock management
            'track_quantity' => $this->track_quantity,
            'quantity_available' => $this->quantity_available,
            'minimum_quantity' => $this->minimum_quantity,
            
            // SEO
            'meta_title' => $this->meta_title,
            'meta_description' => $this->meta_description,
            'meta_keywords' => $this->meta_keywords,
            
            // Statistics
            'view_count' => $this->view_count,
            'order_count' => $this->order_count,
            'average_rating' => $this->average_rating,
            'review_count' => $this->review_count,
            
            // Variants
            'variants' => $this->whenLoaded('variants', function () {
                return $this->variants->map(function ($variant) {
                    return [
                        'id' => $variant->id,
                        'name' => $variant->name,
                        'type' => $variant->type,
                        'description' => $variant->description,
                        'price_adjustment' => $variant->price_adjustment,
                        'price_type' => $variant->price_type,
                        'final_price' => $variant->final_price,
                        'is_available' => $variant->is_available,
                        'is_default' => $variant->is_default,
                        'track_quantity' => $variant->track_quantity,
                        'quantity_available' => $variant->quantity_available,
                        'sort_order' => $variant->sort_order,
                        'image' => $variant->image,
                    ];
                });
            }),
            
            // Addons
            'addons' => $this->whenLoaded('addons', function () {
                return $this->addons->map(function ($addon) {
                    return [
                        'id' => $addon->id,
                        'name' => $addon->name,
                        'description' => $addon->description,
                        'price' => $addon->price,
                        'category' => $addon->category,
                        'is_available' => $addon->is_available,
                        'sort_order' => $addon->sort_order,
                        'image' => $addon->image,
                        'pivot' => [
                            'is_required' => $addon->pivot->is_required,
                            'max_quantity' => $addon->pivot->max_quantity,
                            'price_override' => $addon->pivot->price_override,
                        ],
                    ];
                });
            }),
            
            // Reviews (limited)
            'reviews' => $this->whenLoaded('reviews', function () {
                return $this->reviews->map(function ($review) {
                    return [
                        'id' => $review->id,
                        'rating' => $review->rating,
                        'title' => $review->title,
                        'review' => $review->review,
                        'reviewer_display_name' => $review->reviewer_display_name,
                        'is_verified_purchase' => $review->is_verified_purchase,
                        'created_at' => $review->created_at->toISOString(),
                    ];
                });
            }),
            
            // Timestamps
            'created_at' => $this->created_at->toISOString(),
            'updated_at' => $this->updated_at->toISOString(),
        ];
    }
}
