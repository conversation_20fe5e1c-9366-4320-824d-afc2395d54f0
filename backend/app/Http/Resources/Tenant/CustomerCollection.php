<?php

namespace App\Http\Resources\Tenant;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\ResourceCollection;

class CustomerCollection extends ResourceCollection
{
    /**
     * Transform the resource collection into an array.
     */
    public function toArray(Request $request): array
    {
        return [
            'data' => $this->collection,
            'meta' => [
                'total' => $this->total(),
                'count' => $this->count(),
                'per_page' => $this->perPage(),
                'current_page' => $this->currentPage(),
                'total_pages' => $this->lastPage(),
                'has_more_pages' => $this->hasMorePages(),
            ],
            'links' => [
                'first' => $this->url(1),
                'last' => $this->url($this->lastPage()),
                'prev' => $this->previousPageUrl(),
                'next' => $this->nextPageUrl(),
            ],
            'summary' => $this->when($request->get('include_summary'), function () {
                return $this->getCustomerSummary();
            }),
        ];
    }

    /**
     * Get customer summary statistics
     */
    protected function getCustomerSummary(): array
    {
        $customers = $this->collection;

        return [
            'total_customers' => $customers->count(),
            'total_spent' => $customers->sum('total_spent'),
            'average_spent_per_customer' => $customers->avg('total_spent'),
            'total_orders' => $customers->sum('total_orders'),
            'average_orders_per_customer' => $customers->avg('total_orders'),
            'tier_breakdown' => $customers->groupBy('customer_tier')->map->count(),
            'status_breakdown' => $customers->groupBy('status')->map->count(),
            'vip_customers' => $customers->where('is_vip', true)->count(),
            'recently_active' => $customers->filter(function ($customer) {
                return $customer->isRecentlyActive();
            })->count(),
        ];
    }
}
