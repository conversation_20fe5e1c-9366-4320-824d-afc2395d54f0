<?php

namespace App\Http\Resources\Tenant;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\ResourceCollection;

class FoodCollection extends ResourceCollection
{
    /**
     * Transform the resource collection into an array.
     */
    public function toArray(Request $request): array
    {
        return [
            'data' => $this->collection,
            'meta' => [
                'total' => $this->total(),
                'count' => $this->count(),
                'per_page' => $this->perPage(),
                'current_page' => $this->currentPage(),
                'total_pages' => $this->lastPage(),
                'has_more_pages' => $this->hasMorePages(),
            ],
            'links' => [
                'first' => $this->url(1),
                'last' => $this->url($this->lastPage()),
                'prev' => $this->previousPageUrl(),
                'next' => $this->nextPageUrl(),
            ],
            'filters' => $this->when($request->get('include_filters'), function () {
                return $this->getAvailableFilters();
            }),
        ];
    }

    /**
     * Get available filters based on current collection
     */
    protected function getAvailableFilters(): array
    {
        $foods = $this->collection;

        return [
            'categories' => $foods->pluck('category')->filter()->unique('id')->values(),
            'price_range' => [
                'min' => $foods->min('current_price'),
                'max' => $foods->max('current_price'),
            ],
            'dietary_options' => [
                'vegetarian' => $foods->where('is_vegetarian', true)->count(),
                'vegan' => $foods->where('is_vegan', true)->count(),
                'gluten_free' => $foods->where('is_gluten_free', true)->count(),
                'dairy_free' => $foods->where('is_dairy_free', true)->count(),
                'nut_free' => $foods->where('is_nut_free', true)->count(),
                'halal' => $foods->where('is_halal', true)->count(),
                'kosher' => $foods->where('is_kosher', true)->count(),
            ],
            'spice_levels' => $foods->pluck('spice_level')->filter()->unique()->values(),
            'features' => [
                'featured' => $foods->where('is_featured', true)->count(),
                'popular' => $foods->where('is_popular', true)->count(),
                'new' => $foods->where('is_new', true)->count(),
            ],
        ];
    }
}
