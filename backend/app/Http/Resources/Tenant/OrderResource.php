<?php

namespace App\Http\Resources\Tenant;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class OrderResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'order_number' => $this->order_number,
            'order_type' => $this->order_type,
            'status' => $this->status,
            'status_label' => $this->getStatusLabel(),
            
            // Customer information
            'customer_id' => $this->customer_id,
            'customer' => new CustomerResource($this->whenLoaded('customer')),
            'customer_name' => $this->customer_name,
            'customer_email' => $this->customer_email,
            'customer_phone' => $this->customer_phone,
            
            // Table information
            'table_id' => $this->table_id,
            'table' => $this->whenLoaded('table', function () {
                return [
                    'id' => $this->table->id,
                    'number' => $this->table->number,
                    'name' => $this->table->name,
                ];
            }),
            
            // Delivery information
            'delivery_address' => $this->delivery_address,
            'delivery_instructions' => $this->delivery_instructions,
            'delivery_latitude' => $this->delivery_latitude,
            'delivery_longitude' => $this->delivery_longitude,
            
            // Pricing
            'subtotal' => $this->subtotal,
            'tax_amount' => $this->tax_amount,
            'delivery_fee' => $this->delivery_fee,
            'service_fee' => $this->service_fee,
            'discount_amount' => $this->discount_amount,
            'tip_amount' => $this->tip_amount,
            'total_amount' => $this->total_amount,
            
            // Payment
            'payment_status' => $this->payment_status,
            'payment_method' => $this->payment_method,
            'payment_reference' => $this->payment_reference,
            'paid_at' => $this->paid_at?->toISOString(),
            
            // Timing
            'scheduled_at' => $this->scheduled_at?->toISOString(),
            'confirmed_at' => $this->confirmed_at?->toISOString(),
            'preparing_at' => $this->preparing_at?->toISOString(),
            'ready_at' => $this->ready_at?->toISOString(),
            'delivered_at' => $this->delivered_at?->toISOString(),
            'completed_at' => $this->completed_at?->toISOString(),
            'estimated_preparation_time' => $this->estimated_preparation_time,
            'estimated_delivery_time' => $this->estimated_delivery_time,
            
            // Instructions and notes
            'special_instructions' => $this->special_instructions,
            'kitchen_notes' => $this->kitchen_notes,
            'admin_notes' => $this->admin_notes,
            'cancellation_reason' => $this->cancellation_reason,
            
            // Staff assignments
            'assigned_to' => $this->assigned_to,
            'prepared_by' => $this->prepared_by,
            'served_by' => $this->served_by,
            
            // Feedback
            'rating' => $this->rating,
            'feedback' => $this->feedback,
            'feedback_at' => $this->feedback_at?->toISOString(),
            
            // Items
            'items' => OrderItemResource::collection($this->whenLoaded('items')),
            'total_items' => $this->total_items,
            
            // Status history
            'status_history' => $this->whenLoaded('statusHistory', function () {
                return $this->statusHistory->map(function ($history) {
                    return [
                        'id' => $history->id,
                        'from_status' => $history->from_status,
                        'to_status' => $history->to_status,
                        'status_change' => $history->status_change,
                        'notes' => $history->notes,
                        'changed_by' => $history->changedBy?->name,
                        'changed_at' => $history->changed_at->toISOString(),
                    ];
                });
            }),
            
            // Delivery information
            'delivery_order' => $this->whenLoaded('deliveryOrder', function () {
                return [
                    'id' => $this->deliveryOrder->id,
                    'status' => $this->deliveryOrder->status,
                    'driver' => $this->deliveryOrder->deliveryDriver?->full_name,
                    'estimated_delivery_time' => $this->deliveryOrder->estimated_delivery_time,
                    'actual_delivery_time' => $this->deliveryOrder->actual_delivery_time,
                    'distance_km' => $this->deliveryOrder->distance_km,
                ];
            }),
            
            // Coupon information
            'coupon_usage' => $this->whenLoaded('couponUsage', function () {
                return [
                    'coupon_code' => $this->couponUsage->coupon->code,
                    'coupon_name' => $this->couponUsage->coupon->name,
                    'discount_amount' => $this->couponUsage->discount_amount,
                ];
            }),
            
            // Timestamps
            'created_at' => $this->created_at->toISOString(),
            'updated_at' => $this->updated_at->toISOString(),
            
            // Computed properties
            'can_be_cancelled' => $this->canBeCancelled(),
            'is_active' => $this->isActive(),
        ];
    }

    /**
     * Get status label
     */
    protected function getStatusLabel(): string
    {
        return match ($this->status) {
            'pending' => 'Pending',
            'confirmed' => 'Confirmed',
            'preparing' => 'Preparing',
            'ready' => 'Ready',
            'out_for_delivery' => 'Out for Delivery',
            'delivered' => 'Delivered',
            'completed' => 'Completed',
            'cancelled' => 'Cancelled',
            'refunded' => 'Refunded',
            default => ucfirst($this->status),
        };
    }
}
