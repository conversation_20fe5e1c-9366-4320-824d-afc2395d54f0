<?php

namespace App\Http\Resources\Tenant;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class OrderItemResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            
            // Food information
            'food_id' => $this->food_id,
            'food' => $this->whenLoaded('food', function () {
                return [
                    'id' => $this->food->id,
                    'name' => $this->food->name,
                    'description' => $this->food->description,
                    'images' => $this->food->images,
                    'primary_image' => $this->food->primary_image,
                    'dietary_flags' => $this->food->dietary_flags,
                ];
            }),
            'food_name' => $this->food_name,
            'food_description' => $this->food_description,
            
            // Variant information
            'food_variant_id' => $this->food_variant_id,
            'food_variant' => $this->whenLoaded('foodVariant', function () {
                return [
                    'id' => $this->foodVariant->id,
                    'name' => $this->foodVariant->name,
                    'type' => $this->foodVariant->type,
                    'price_adjustment' => $this->foodVariant->price_adjustment,
                    'final_price' => $this->foodVariant->final_price,
                ];
            }),
            
            // Pricing and quantity
            'unit_price' => $this->unit_price,
            'quantity' => $this->quantity,
            'total_price' => $this->total_price,
            'total_price_with_addons' => $this->total_price_with_addons,
            
            // Customizations
            'special_instructions' => $this->special_instructions,
            'customizations' => $this->customizations,
            
            // Status and timing
            'status' => $this->status,
            'started_preparing_at' => $this->started_preparing_at?->toISOString(),
            'ready_at' => $this->ready_at?->toISOString(),
            'served_at' => $this->served_at?->toISOString(),
            'preparation_time' => $this->preparation_time,
            
            // Staff
            'prepared_by' => $this->prepared_by,
            'served_by' => $this->served_by,
            'prepared_by_employee' => $this->whenLoaded('preparedByEmployee', function () {
                return [
                    'id' => $this->preparedByEmployee->id,
                    'name' => $this->preparedByEmployee->name,
                ];
            }),
            'served_by_employee' => $this->whenLoaded('servedByEmployee', function () {
                return [
                    'id' => $this->servedByEmployee->id,
                    'name' => $this->servedByEmployee->name,
                ];
            }),
            
            // Addons
            'addons' => $this->whenLoaded('addons', function () {
                return $this->addons->map(function ($addon) {
                    return [
                        'id' => $addon->id,
                        'food_addon_id' => $addon->food_addon_id,
                        'addon_name' => $addon->addon_name,
                        'unit_price' => $addon->unit_price,
                        'quantity' => $addon->quantity,
                        'total_price' => $addon->total_price,
                        'food_addon' => $addon->whenLoaded('foodAddon', function () use ($addon) {
                            return [
                                'id' => $addon->foodAddon->id,
                                'name' => $addon->foodAddon->name,
                                'description' => $addon->foodAddon->description,
                                'category' => $addon->foodAddon->category,
                            ];
                        }),
                    ];
                });
            }),
            
            // Timestamps
            'created_at' => $this->created_at->toISOString(),
            'updated_at' => $this->updated_at->toISOString(),
            
            // Computed properties
            'can_be_cancelled' => $this->canBeCancelled(),
        ];
    }
}
