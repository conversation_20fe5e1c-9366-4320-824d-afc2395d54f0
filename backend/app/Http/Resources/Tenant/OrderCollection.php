<?php

namespace App\Http\Resources\Tenant;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\ResourceCollection;

class OrderCollection extends ResourceCollection
{
    /**
     * Transform the resource collection into an array.
     */
    public function toArray(Request $request): array
    {
        return [
            'data' => $this->collection,
            'meta' => [
                'total' => $this->total(),
                'count' => $this->count(),
                'per_page' => $this->perPage(),
                'current_page' => $this->currentPage(),
                'total_pages' => $this->lastPage(),
                'has_more_pages' => $this->hasMorePages(),
            ],
            'links' => [
                'first' => $this->url(1),
                'last' => $this->url($this->lastPage()),
                'prev' => $this->previousPageUrl(),
                'next' => $this->nextPageUrl(),
            ],
            'summary' => $this->when($request->get('include_summary'), function () {
                return $this->getOrderSummary();
            }),
        ];
    }

    /**
     * Get order summary statistics
     */
    protected function getOrderSummary(): array
    {
        $orders = $this->collection;

        return [
            'total_orders' => $orders->count(),
            'total_amount' => $orders->sum('total_amount'),
            'average_order_value' => $orders->avg('total_amount'),
            'status_breakdown' => $orders->groupBy('status')->map->count(),
            'order_type_breakdown' => $orders->groupBy('order_type')->map->count(),
            'payment_status_breakdown' => $orders->groupBy('payment_status')->map->count(),
        ];
    }
}
