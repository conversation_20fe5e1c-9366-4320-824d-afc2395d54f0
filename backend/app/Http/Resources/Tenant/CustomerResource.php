<?php

namespace App\Http\Resources\Tenant;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class CustomerResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'first_name' => $this->first_name,
            'last_name' => $this->last_name,
            'full_name' => $this->full_name,
            'email' => $this->email,
            'phone' => $this->phone,
            
            // Personal information
            'date_of_birth' => $this->date_of_birth?->toDateString(),
            'age' => $this->age,
            'gender' => $this->gender,
            'anniversary_date' => $this->anniversary_date?->toDateString(),
            
            // Preferences
            'preferred_language' => $this->preferred_language,
            'dietary_preferences' => $this->dietary_preferences,
            'allergies' => $this->allergies,
            'favorite_cuisines' => $this->favorite_cuisines,
            
            // Communication preferences
            'email_notifications' => $this->email_notifications,
            'sms_notifications' => $this->sms_notifications,
            'push_notifications' => $this->push_notifications,
            'marketing_emails' => $this->marketing_emails,
            'birthday_offers' => $this->birthday_offers,
            
            // Status and tier
            'status' => $this->status,
            'customer_tier' => $this->customer_tier,
            'customer_tier_label' => $this->customer_tier_label,
            'is_vip' => $this->is_vip,
            
            // Statistics
            'total_orders' => $this->total_orders,
            'total_spent' => $this->total_spent,
            'average_order_value' => $this->average_order_value,
            'loyalty_points' => $this->loyalty_points,
            'last_order_at' => $this->last_order_at?->toISOString(),
            'registration_date' => $this->created_at->toDateString(),
            'days_since_registration' => $this->created_at->diffInDays(now()),
            
            // Addresses
            'addresses' => $this->whenLoaded('addresses', function () {
                return $this->addresses->map(function ($address) {
                    return [
                        'id' => $address->id,
                        'type' => $address->type,
                        'label' => $address->label,
                        'address_line_1' => $address->address_line_1,
                        'address_line_2' => $address->address_line_2,
                        'city' => $address->city,
                        'state' => $address->state,
                        'postal_code' => $address->postal_code,
                        'country' => $address->country,
                        'full_address' => $address->full_address,
                        'latitude' => $address->latitude,
                        'longitude' => $address->longitude,
                        'delivery_instructions' => $address->delivery_instructions,
                        'is_default' => $address->is_default,
                        'is_active' => $address->is_active,
                    ];
                });
            }),
            
            // Default address
            'default_address' => $this->whenLoaded('addresses', function () {
                $defaultAddress = $this->addresses->where('is_default', true)->first();
                return $defaultAddress ? [
                    'id' => $defaultAddress->id,
                    'full_address' => $defaultAddress->full_address,
                    'delivery_instructions' => $defaultAddress->delivery_instructions,
                ] : null;
            }),
            
            // Recent orders (limited)
            'recent_orders' => $this->whenLoaded('orders', function () {
                return $this->orders->take(5)->map(function ($order) {
                    return [
                        'id' => $order->id,
                        'order_number' => $order->order_number,
                        'order_type' => $order->order_type,
                        'status' => $order->status,
                        'total_amount' => $order->total_amount,
                        'created_at' => $order->created_at->toISOString(),
                    ];
                });
            }),
            
            // Reviews
            'restaurant_reviews_count' => $this->whenCounted('restaurantReviews'),
            'food_reviews_count' => $this->whenCounted('foodReviews'),
            'recent_restaurant_reviews' => $this->whenLoaded('restaurantReviews', function () {
                return $this->restaurantReviews->take(3)->map(function ($review) {
                    return [
                        'id' => $review->id,
                        'overall_rating' => $review->overall_rating,
                        'title' => $review->title,
                        'review' => $review->review,
                        'created_at' => $review->created_at->toISOString(),
                    ];
                });
            }),
            
            // Notes
            'notes' => $this->notes,
            
            // Timestamps
            'created_at' => $this->created_at->toISOString(),
            'updated_at' => $this->updated_at->toISOString(),
            
            // Computed properties
            'is_recently_active' => $this->isRecentlyActive(),
            'next_tier_requirements' => $this->getNextTierRequirements(),
            'loyalty_points_value' => $this->getLoyaltyPointsValue(),
        ];
    }
}
