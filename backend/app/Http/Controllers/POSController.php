<?php

namespace App\Http\Controllers;

use App\Models\Tenant\Branch;
use App\Models\Tenant\Floor;
use App\Models\Tenant\Table;
use Illuminate\Http\Request;
use Inertia\Inertia;

class POSController extends Controller
{
    /**
     * Display the main POS interface.
     */
    public function index(Request $request)
    {
        $selectedBranchId = $request->session()->get('selected_branch_id')
            ?? Branch::active()->first()?->id;

        if ($selectedBranchId) {
            $request->session()->put('selected_branch_id', $selectedBranchId);
        }

        $branches = Branch::active()->ordered()->get(['id', 'name']);

        $floors = [];
        $tables = [];

        if ($selectedBranchId) {
            $floors = Floor::where('branch_id', $selectedBranchId)
                ->active()
                ->ordered()
                ->with(['tables' => function ($query) {
                    $query->active()->ordered();
                }])
                ->get();

            $tables = Table::where('branch_id', $selectedBranchId)
                ->active()
                ->ordered()
                ->with(['floor'])
                ->get()
                ->groupBy('floor_id');
        }

        return Inertia::render('POS/Index', [
            'branches' => $branches,
            'selectedBranchId' => $selectedBranchId,
            'floors' => $floors,
            'tables' => $tables,
        ]);
    }

    /**
     * Display the table view for POS.
     */
    public function tableView(Request $request)
    {
        $selectedBranchId = $request->session()->get('selected_branch_id')
            ?? Branch::active()->first()?->id;

        if (!$selectedBranchId) {
            return redirect()->route('pos.index')
                ->with('error', __('pos.no_branch_selected'));
        }

        $branch = Branch::findOrFail($selectedBranchId);

        $floors = Floor::where('branch_id', $selectedBranchId)
            ->active()
            ->ordered()
            ->with(['tables' => function ($query) {
                $query->active()->ordered();
            }])
            ->get();

        return Inertia::render('POS/TableView', [
            'branch' => $branch,
            'floors' => $floors,
        ]);
    }

    /**
     * Switch branch for POS session.
     */
    public function switchBranch(Request $request)
    {
        $validated = $request->validate([
            'branch_id' => ['required', 'exists:branches,id'],
        ]);

        $request->session()->put('selected_branch_id', $validated['branch_id']);

        return response()->json([
            'success' => true,
            'message' => __('pos.branch_switched_successfully'),
        ]);
    }

    /**
     * Get table details for POS.
     */
    public function getTable($tableId)
    {
        $table = Table::with(['floor', 'branch'])
            ->findOrFail($tableId);

        return response()->json($table);
    }
}
