<?php

namespace App\Http\Controllers\Manager;

use App\Http\Controllers\Controller;
use App\Models\Tenant\Floor;
use App\Models\Tenant\Branch;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use Illuminate\Validation\Rule;

class FloorController extends Controller
{
    /**
     * Display a listing of floors.
     */
    public function index(Request $request)
    {
        $query = Floor::with(['branch', 'tables']);

        // Search
        if ($request->search) {
            $query->where(function ($q) use ($request) {
                $q->where('name', 'like', '%' . $request->search . '%')
                  ->orWhere('description', 'like', '%' . $request->search . '%')
                  ->orWhereHas('branch', function ($branchQuery) use ($request) {
                      $branchQuery->where('name', 'like', '%' . $request->search . '%');
                  });
            });
        }

        // Filter by branch
        if ($request->branch_id) {
            $query->where('branch_id', $request->branch_id);
        }

        // Filter by status
        if ($request->status !== null) {
            $query->where('is_active', $request->status);
        }

        $floors = $query->orderBy('sort_order')->orderBy('name')->paginate(10)->withQueryString();

        // Add counts for each floor
        $floors->getCollection()->transform(function ($floor) {
            $floor->tables_count = $floor->tables->count();
            return $floor;
        });

        // Get branches for filter
        $branches = Branch::active()->ordered()->get(['id', 'name']);

        return view('manager.floors.index', compact('floors', 'branches'));
    }

    /**
     * Show the form for creating a new floor.
     */
    public function create()
    {
        $branches = Branch::active()->ordered()->get(['id', 'name']);
        
        return view('manager.floors.create', compact('branches'));
    }

    /**
     * Store a newly created floor.
     */
    public function store(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'name' => [
                'required',
                'string',
                'max:255',
                Rule::unique('floors')->where(function ($query) use ($request) {
                    return $query->where('branch_id', $request->branch_id);
                })
            ],
            'description' => 'nullable|string|max:1000',
            'branch_id' => 'required|exists:branches,id',
            'sort_order' => 'nullable|integer|min:0',
            'is_active' => 'boolean',
        ]);

        if ($validator->fails()) {
            return back()->withErrors($validator)->withInput();
        }

        $data = $validator->validated();

        // Set default sort order
        if (!isset($data['sort_order'])) {
            $data['sort_order'] = Floor::where('branch_id', $data['branch_id'])->max('sort_order') + 1;
        }

        $floor = Floor::create($data);

        return redirect()->route('manager.floors.index')
            ->with('success', 'Floor created successfully.');
    }

    /**
     * Display the specified floor.
     */
    public function show(Floor $floor)
    {
        $floor->load(['branch', 'tables.orders' => function ($query) {
            $query->latest()->take(5);
        }]);

        return view('manager.floors.show', compact('floor'));
    }

    /**
     * Show the form for editing the specified floor.
     */
    public function edit(Floor $floor)
    {
        $branches = Branch::active()->ordered()->get(['id', 'name']);
        
        return view('manager.floors.edit', compact('floor', 'branches'));
    }

    /**
     * Update the specified floor.
     */
    public function update(Request $request, Floor $floor)
    {
        $validator = Validator::make($request->all(), [
            'name' => [
                'required',
                'string',
                'max:255',
                Rule::unique('floors')->where(function ($query) use ($request) {
                    return $query->where('branch_id', $request->branch_id);
                })->ignore($floor->id)
            ],
            'description' => 'nullable|string|max:1000',
            'branch_id' => 'required|exists:branches,id',
            'sort_order' => 'nullable|integer|min:0',
            'is_active' => 'boolean',
        ]);

        if ($validator->fails()) {
            return back()->withErrors($validator)->withInput();
        }

        $data = $validator->validated();

        $floor->update($data);

        return redirect()->route('manager.floors.index')
            ->with('success', 'Floor updated successfully.');
    }

    /**
     * Remove the specified floor.
     */
    public function destroy(Floor $floor)
    {
        // Check if floor has tables
        if ($floor->tables()->count() > 0) {
            return back()->with('error', 'Cannot delete floor with tables. Please delete tables first.');
        }

        $floor->delete();

        return redirect()->route('manager.floors.index')
            ->with('success', 'Floor deleted successfully.');
    }

    /**
     * Toggle floor status.
     */
    public function toggleStatus(Floor $floor)
    {
        $floor->update(['is_active' => !$floor->is_active]);

        $status = $floor->is_active ? 'activated' : 'deactivated';

        return response()->json([
            'success' => true,
            'message' => "Floor {$status} successfully.",
            'is_active' => $floor->is_active,
        ]);
    }

    /**
     * Get floors for a specific branch (API endpoint).
     */
    public function getByBranch($branchId)
    {
        $floors = Floor::where('branch_id', $branchId)
            ->active()
            ->ordered()
            ->get(['id', 'name', 'description']);

        return response()->json($floors);
    }
}
