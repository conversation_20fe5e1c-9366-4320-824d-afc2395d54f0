<?php

namespace App\Http\Controllers\Manager;

use App\Http\Controllers\Controller;
use App\Models\Tenant\Branch;
use App\Models\Tenant\Floor;
use App\Models\Tenant\Table;
use App\Models\Tenant\Order;
use App\Models\Tenant\Customer;
use App\Models\Tenant\Employee;
use Illuminate\Http\Request;
use Carbon\Carbon;

class DashboardController extends Controller
{
    /**
     * Display the manager dashboard.
     */
    public function index()
    {
        // Get basic statistics
        $stats = [
            'total_branches' => Branch::count(),
            'active_branches' => Branch::active()->count(),
            'total_floors' => Floor::count(),
            'total_tables' => Table::count(),
            'active_tables' => Table::active()->count(),
            'total_orders_today' => Order::whereDate('created_at', today())->count(),
            'total_revenue_today' => Order::whereDate('created_at', today())->sum('total_amount'),
            'total_customers' => Customer::count(),
            'total_staff' => Employee::count(),
        ];

        // Get recent orders
        $recentOrders = Order::with(['customer', 'table.floor.branch'])
            ->latest()
            ->take(5)
            ->get();

        // Get branch statistics
        $branchStats = Branch::withCount(['floors', 'tables', 'orders' => function ($query) {
            $query->whereDate('created_at', today());
        }])
        ->with(['floors' => function ($query) {
            $query->withCount('tables');
        }])
        ->get()
        ->map(function ($branch) {
            $branch->revenue_today = $branch->orders()
                ->whereDate('created_at', today())
                ->sum('total_amount');
            return $branch;
        });

        // Get floor utilization
        $floorUtilization = Floor::with(['tables' => function ($query) {
            $query->with('currentOrder');
        }])
        ->get()
        ->map(function ($floor) {
            $totalTables = $floor->tables->count();
            $occupiedTables = $floor->tables->filter(function ($table) {
                return $table->currentOrder !== null;
            })->count();
            
            $floor->utilization_percentage = $totalTables > 0 ? round(($occupiedTables / $totalTables) * 100, 1) : 0;
            $floor->occupied_tables = $occupiedTables;
            $floor->total_tables = $totalTables;
            
            return $floor;
        });

        return view('manager.dashboard.index', compact(
            'stats',
            'recentOrders',
            'branchStats',
            'floorUtilization'
        ));
    }

    /**
     * Display analytics page.
     */
    public function analytics(Request $request)
    {
        $period = $request->get('period', '7days');
        
        // Calculate date range based on period
        switch ($period) {
            case '24hours':
                $startDate = Carbon::now()->subDay();
                break;
            case '7days':
                $startDate = Carbon::now()->subWeek();
                break;
            case '30days':
                $startDate = Carbon::now()->subMonth();
                break;
            case '90days':
                $startDate = Carbon::now()->subDays(90);
                break;
            default:
                $startDate = Carbon::now()->subWeek();
        }

        $endDate = Carbon::now();

        // Revenue analytics
        $revenueData = Order::whereBetween('created_at', [$startDate, $endDate])
            ->selectRaw('DATE(created_at) as date, SUM(total_amount) as revenue, COUNT(*) as orders')
            ->groupBy('date')
            ->orderBy('date')
            ->get();

        // Branch performance
        $branchPerformance = Branch::with(['orders' => function ($query) use ($startDate, $endDate) {
            $query->whereBetween('created_at', [$startDate, $endDate]);
        }])
        ->get()
        ->map(function ($branch) {
            $orders = $branch->orders;
            return [
                'name' => $branch->name,
                'revenue' => $orders->sum('total_amount'),
                'orders' => $orders->count(),
                'avg_order_value' => $orders->count() > 0 ? $orders->sum('total_amount') / $orders->count() : 0,
            ];
        });

        // Table utilization over time
        $tableUtilization = [];
        for ($date = $startDate->copy(); $date <= $endDate; $date->addDay()) {
            $dayOrders = Order::whereDate('created_at', $date)->count();
            $totalTables = Table::active()->count();
            
            $tableUtilization[] = [
                'date' => $date->format('Y-m-d'),
                'utilization' => $totalTables > 0 ? round(($dayOrders / $totalTables) * 100, 1) : 0,
            ];
        }

        // Popular time slots
        $timeSlots = Order::whereBetween('created_at', [$startDate, $endDate])
            ->selectRaw('HOUR(created_at) as hour, COUNT(*) as orders')
            ->groupBy('hour')
            ->orderBy('hour')
            ->get()
            ->map(function ($slot) {
                return [
                    'time' => sprintf('%02d:00', $slot->hour),
                    'orders' => $slot->orders,
                ];
            });

        // Order status distribution
        $orderStatusDistribution = Order::whereBetween('created_at', [$startDate, $endDate])
            ->selectRaw('status, COUNT(*) as count')
            ->groupBy('status')
            ->get()
            ->pluck('count', 'status');

        return view('manager.dashboard.analytics', compact(
            'period',
            'revenueData',
            'branchPerformance',
            'tableUtilization',
            'timeSlots',
            'orderStatusDistribution',
            'startDate',
            'endDate'
        ));
    }
}
