<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\Tenant\Order;
use App\Models\Tenant\Customer;
use App\Models\Tenant\Food;
use App\Models\Tenant\Table;
use App\Models\Tenant\Employee;
use App\Models\Payment;
use App\Services\NotificationService;
use Illuminate\Http\Request;
use Carbon\Carbon;

class DashboardApiController extends Controller
{
    protected $notificationService;

    public function __construct(NotificationService $notificationService)
    {
        $this->notificationService = $notificationService;
    }

    /**
     * Get real-time dashboard data based on user role
     */
    public function getDashboardData(Request $request)
    {
        $user = auth()->user();
        $role = $user->getPrimaryRoleAttribute();

        switch ($role) {
            case 'admin':
                return $this->getAdminDashboardData($request);
            case 'restaurant_manager':
                return $this->getManagerDashboardData($request);
            case 'waiter':
                return $this->getWaiterDashboardData($request);
            case 'kitchen':
                return $this->getKitchenDashboardData($request);
            case 'delivery':
                return $this->getDeliveryDashboardData($request);
            default:
                return response()->json(['error' => 'Invalid role'], 403);
        }
    }

    /**
     * Get admin dashboard data
     */
    private function getAdminDashboardData($request)
    {
        $dateRange = $this->getDateRange($request);
        
        return response()->json([
            'role' => 'admin',
            'metrics' => [
                'total_tenants' => \App\Models\Tenant::count(),
                'active_tenants' => \App\Models\Tenant::where('status', 'active')->count(),
                'total_revenue' => Payment::where('status', 'completed')
                    ->whereBetween('created_at', $dateRange)
                    ->sum('amount'),
                'pending_payments' => Payment::where('status', 'pending')->count(),
                'total_subscriptions' => \App\Models\Subscription::count(),
                'active_subscriptions' => \App\Models\Subscription::where('status', 'active')->count(),
            ],
            'recent_activities' => $this->getRecentAdminActivities(),
            'system_health' => $this->getSystemHealth(),
        ]);
    }

    /**
     * Get manager dashboard data
     */
    private function getManagerDashboardData($request)
    {
        $dateRange = $this->getDateRange($request);
        
        return response()->json([
            'role' => 'restaurant_manager',
            'metrics' => [
                'total_revenue' => Order::where('payment_status', 'paid')
                    ->whereBetween('created_at', $dateRange)
                    ->sum('total_amount'),
                'total_orders' => Order::whereBetween('created_at', $dateRange)->count(),
                'active_orders' => Order::whereIn('status', ['pending', 'confirmed', 'preparing'])->count(),
                'completed_orders' => Order::where('status', 'completed')
                    ->whereBetween('created_at', $dateRange)
                    ->count(),
                'total_customers' => Customer::whereBetween('created_at', $dateRange)->count(),
                'average_order_value' => Order::whereBetween('created_at', $dateRange)->avg('total_amount'),
                'table_occupancy' => $this->getTableOccupancyRate(),
                'staff_count' => Employee::where('status', 'active')->count(),
            ],
            'recent_orders' => Order::with(['customer', 'table'])
                ->latest()
                ->limit(10)
                ->get(),
            'top_selling_items' => $this->getTopSellingItems($dateRange),
            'revenue_chart' => $this->getRevenueChartData($dateRange),
            'alerts' => $this->getManagerAlerts(),
        ]);
    }

    /**
     * Get waiter dashboard data
     */
    private function getWaiterDashboardData($request)
    {
        $user = auth()->user();
        
        return response()->json([
            'role' => 'waiter',
            'assigned_tables' => Table::where('assigned_waiter_id', $user->id)
                ->with(['currentOrder', 'reservation'])
                ->get(),
            'active_orders' => Order::where('assigned_to', $user->id)
                ->whereIn('status', ['pending', 'confirmed', 'preparing', 'ready'])
                ->with(['customer', 'table', 'items.food'])
                ->get(),
            'today_metrics' => [
                'orders_served' => Order::where('served_by', $user->id)
                    ->whereDate('created_at', today())
                    ->count(),
                'total_sales' => Order::where('served_by', $user->id)
                    ->whereDate('created_at', today())
                    ->where('payment_status', 'paid')
                    ->sum('total_amount'),
                'tables_served' => Order::where('served_by', $user->id)
                    ->whereDate('created_at', today())
                    ->distinct('table_id')
                    ->count('table_id'),
            ],
            'pending_tasks' => $this->getWaiterPendingTasks($user),
            'notifications' => $this->notificationService->getUnreadNotifications($user),
        ]);
    }

    /**
     * Get kitchen dashboard data
     */
    private function getKitchenDashboardData($request)
    {
        $user = auth()->user();
        
        return response()->json([
            'role' => 'kitchen',
            'preparation_queue' => Order::whereIn('status', ['confirmed', 'preparing'])
                ->with(['items' => function ($query) {
                    $query->whereIn('status', ['pending', 'preparing'])
                        ->with(['food', 'addons.foodAddon']);
                }])
                ->orderBy('created_at')
                ->get(),
            'ready_orders' => Order::where('status', 'ready')
                ->with(['table', 'items.food'])
                ->orderBy('ready_at')
                ->get(),
            'today_metrics' => [
                'items_prepared' => \App\Models\Tenant\OrderItem::where('prepared_by', $user->id)
                    ->whereDate('created_at', today())
                    ->count(),
                'average_prep_time' => \App\Models\Tenant\OrderItem::where('prepared_by', $user->id)
                    ->whereDate('created_at', today())
                    ->whereNotNull('ready_at')
                    ->selectRaw('AVG(TIMESTAMPDIFF(MINUTE, started_preparing_at, ready_at)) as avg_time')
                    ->value('avg_time') ?? 0,
                'pending_items' => \App\Models\Tenant\OrderItem::whereIn('status', ['pending', 'preparing'])
                    ->count(),
            ],
            'inventory_alerts' => $this->getInventoryAlerts(),
            'notifications' => $this->notificationService->getUnreadNotifications($user),
        ]);
    }

    /**
     * Get delivery dashboard data
     */
    private function getDeliveryDashboardData($request)
    {
        $user = auth()->user();
        $deliveryDriver = \App\Models\Tenant\DeliveryDriver::where('employee_id', $user->id)->first();
        
        if (!$deliveryDriver) {
            return response()->json(['error' => 'Delivery driver profile not found'], 404);
        }
        
        return response()->json([
            'role' => 'delivery',
            'assigned_deliveries' => \App\Models\Tenant\DeliveryOrder::where('delivery_driver_id', $deliveryDriver->id)
                ->whereIn('status', ['assigned', 'picked_up', 'on_the_way'])
                ->with(['order.customer', 'deliveryZone'])
                ->get(),
            'available_deliveries' => \App\Models\Tenant\DeliveryOrder::where('status', 'pending')
                ->whereNull('delivery_driver_id')
                ->with(['order.customer', 'deliveryZone'])
                ->get(),
            'today_metrics' => [
                'deliveries_completed' => \App\Models\Tenant\DeliveryOrder::where('delivery_driver_id', $deliveryDriver->id)
                    ->where('status', 'delivered')
                    ->whereDate('delivered_at', today())
                    ->count(),
                'total_earnings' => $this->getTodayEarnings($deliveryDriver),
                'total_distance' => \App\Models\Tenant\DeliveryOrder::where('delivery_driver_id', $deliveryDriver->id)
                    ->where('status', 'delivered')
                    ->whereDate('delivered_at', today())
                    ->sum('distance_km'),
            ],
            'driver_status' => [
                'is_available' => $deliveryDriver->is_available,
                'current_location' => [
                    'latitude' => $deliveryDriver->current_latitude,
                    'longitude' => $deliveryDriver->current_longitude,
                ],
            ],
            'notifications' => $this->notificationService->getUnreadNotifications($user),
        ]);
    }

    /**
     * Get notifications for current user
     */
    public function getNotifications(Request $request)
    {
        $user = auth()->user();
        $limit = $request->get('limit', 10);
        
        return response()->json([
            'notifications' => $this->notificationService->getUnreadNotifications($user, $limit),
            'unread_count' => $user->unreadNotifications()->count(),
        ]);
    }

    /**
     * Mark notification as read
     */
    public function markNotificationRead(Request $request, $notificationId)
    {
        $user = auth()->user();
        $success = $this->notificationService->markAsRead($user, $notificationId);
        
        return response()->json([
            'success' => $success,
            'message' => $success ? 'Notification marked as read' : 'Notification not found'
        ]);
    }

    /**
     * Mark all notifications as read
     */
    public function markAllNotificationsRead(Request $request)
    {
        $user = auth()->user();
        $this->notificationService->markAllAsRead($user);
        
        return response()->json([
            'success' => true,
            'message' => 'All notifications marked as read'
        ]);
    }

    /**
     * Get real-time order updates
     */
    public function getOrderUpdates(Request $request)
    {
        $user = auth()->user();
        $role = $user->getPrimaryRoleAttribute();
        $lastUpdate = $request->get('last_update', now()->subMinutes(5));
        
        $query = Order::where('updated_at', '>', $lastUpdate);
        
        // Filter based on role
        switch ($role) {
            case 'waiter':
                $query->where('assigned_to', $user->id);
                break;
            case 'kitchen':
                $query->whereIn('status', ['confirmed', 'preparing', 'ready']);
                break;
            case 'delivery':
                $query->where('type', 'delivery');
                break;
        }
        
        $orders = $query->with(['customer', 'table', 'items.food'])->get();
        
        return response()->json([
            'orders' => $orders,
            'timestamp' => now(),
        ]);
    }

    // Helper methods
    private function getDateRange($request)
    {
        $startDate = $request->get('start_date', now()->startOfMonth());
        $endDate = $request->get('end_date', now()->endOfDay());
        
        return [Carbon::parse($startDate), Carbon::parse($endDate)];
    }

    private function getTableOccupancyRate()
    {
        $totalTables = Table::count();
        $occupiedTables = Table::where('status', 'occupied')->count();
        
        return $totalTables > 0 ? ($occupiedTables / $totalTables) * 100 : 0;
    }

    private function getTopSellingItems($dateRange)
    {
        return \DB::table('order_items')
            ->join('orders', 'order_items.order_id', '=', 'orders.id')
            ->join('foods', 'order_items.food_id', '=', 'foods.id')
            ->whereBetween('orders.created_at', $dateRange)
            ->where('orders.payment_status', 'paid')
            ->selectRaw('
                foods.name,
                SUM(order_items.quantity) as total_quantity,
                SUM(order_items.total_price) as total_revenue
            ')
            ->groupBy('foods.id', 'foods.name')
            ->orderByDesc('total_quantity')
            ->limit(5)
            ->get();
    }

    private function getRevenueChartData($dateRange)
    {
        // Implementation for revenue chart data
        return [];
    }

    private function getManagerAlerts()
    {
        return [
            'low_stock_items' => [],
            'pending_orders' => Order::where('status', 'pending')->count(),
            'overdue_orders' => Order::where('status', 'preparing')
                ->where('created_at', '<', now()->subMinutes(30))
                ->count(),
        ];
    }

    private function getWaiterPendingTasks($user)
    {
        return [
            'orders_to_serve' => Order::where('assigned_to', $user->id)
                ->where('status', 'ready')
                ->count(),
            'tables_to_clean' => Table::where('assigned_waiter_id', $user->id)
                ->where('status', 'cleaning')
                ->count(),
        ];
    }

    private function getInventoryAlerts()
    {
        // Mock data - integrate with actual inventory system
        return [];
    }

    private function getTodayEarnings($deliveryDriver)
    {
        $commissions = \App\Models\Tenant\DeliveryOrder::where('delivery_driver_id', $deliveryDriver->id)
            ->where('status', 'delivered')
            ->whereDate('delivered_at', today())
            ->sum('driver_commission');
            
        $tips = Order::whereHas('deliveryOrder', function ($query) use ($deliveryDriver) {
            $query->where('delivery_driver_id', $deliveryDriver->id)
                ->where('status', 'delivered')
                ->whereDate('delivered_at', today());
        })->sum('tip_amount');
        
        return $commissions + $tips;
    }

    private function getRecentAdminActivities()
    {
        // Mock data - implement based on activity log
        return [];
    }

    private function getSystemHealth()
    {
        return [
            'database_status' => 'healthy',
            'cache_status' => 'healthy',
            'queue_status' => 'healthy',
            'storage_usage' => '45%',
        ];
    }
}
