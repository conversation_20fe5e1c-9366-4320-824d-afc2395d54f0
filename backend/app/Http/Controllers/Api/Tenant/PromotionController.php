<?php

namespace App\Http\Controllers\Api\Tenant;

use App\Http\Controllers\Controller;
use App\Models\Promotion;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;

class PromotionController extends Controller
{
    public function __construct()
    {
        $this->middleware('auth:sanctum')->except(['index', 'show', 'getActive']);
        $this->middleware('role:admin|restaurant_manager')->except(['index', 'show', 'getActive']);
    }

    /**
     * Display a listing of promotions
     */
    public function index(Request $request): JsonResponse
    {
        $query = Promotion::query();

        // Apply filters
        if ($request->filled('type')) {
            $query->where('type', $request->type);
        }

        if ($request->filled('status')) {
            if ($request->status === 'active') {
                $query->active();
            } elseif ($request->status === 'expired') {
                $query->expired();
            } elseif ($request->status === 'scheduled') {
                $query->scheduled();
            } elseif ($request->status === 'inactive') {
                $query->where('is_active', false);
            }
        }

        if ($request->filled('search')) {
            $query->where(function ($q) use ($request) {
                $q->where('title', 'like', "%{$request->search}%")
                  ->orWhere('description', 'like', "%{$request->search}%");
            });
        }

        // Apply sorting
        $sortField = $request->get('sort', 'created_at');
        $sortDirection = $request->get('direction', 'desc');
        $query->orderBy($sortField, $sortDirection);

        $promotions = $query->paginate($request->get('per_page', 15));

        return response()->json([
            'success' => true,
            'data' => $promotions,
        ]);
    }

    /**
     * Store a newly created promotion
     */
    public function store(Request $request): JsonResponse
    {
        $validated = $request->validate([
            'title' => 'required|string|max:255',
            'description' => 'required|string|max:1000',
            'type' => 'required|in:discount,bogo,free_delivery,combo_deal,happy_hour',
            'discount_type' => 'nullable|in:fixed,percentage',
            'discount_value' => 'nullable|numeric|min:0',
            'minimum_order_amount' => 'nullable|numeric|min:0',
            'maximum_discount_amount' => 'nullable|numeric|min:0',
            'applicable_to' => 'nullable|in:all,specific_items,specific_categories',
            'applicable_items' => 'nullable|array',
            'applicable_items.*' => 'exists:menu_items,id',
            'applicable_categories' => 'nullable|array',
            'applicable_categories.*' => 'exists:food_categories,id',
            'start_date' => 'required|date|after_or_equal:today',
            'end_date' => 'required|date|after:start_date',
            'start_time' => 'nullable|date_format:H:i',
            'end_time' => 'nullable|date_format:H:i',
            'days_of_week' => 'nullable|array',
            'days_of_week.*' => 'in:monday,tuesday,wednesday,thursday,friday,saturday,sunday',
            'usage_limit' => 'nullable|integer|min:1',
            'usage_limit_per_customer' => 'nullable|integer|min:1',
            'image' => 'nullable|image|max:2048',
            'is_active' => 'boolean',
            'is_featured' => 'boolean',
            'priority' => 'integer|min:0',
        ]);

        // Handle image upload
        if ($request->hasFile('image')) {
            $validated['image'] = $request->file('image')->store('promotions', 'public');
        }

        $promotion = Promotion::create($validated);

        return response()->json([
            'success' => true,
            'message' => 'Promotion created successfully.',
            'data' => $promotion,
        ], 201);
    }

    /**
     * Display the specified promotion
     */
    public function show(Promotion $promotion): JsonResponse
    {
        return response()->json([
            'success' => true,
            'data' => $promotion,
        ]);
    }

    /**
     * Update the specified promotion
     */
    public function update(Request $request, Promotion $promotion): JsonResponse
    {
        $validated = $request->validate([
            'title' => 'required|string|max:255',
            'description' => 'required|string|max:1000',
            'type' => 'required|in:discount,bogo,free_delivery,combo_deal,happy_hour',
            'discount_type' => 'nullable|in:fixed,percentage',
            'discount_value' => 'nullable|numeric|min:0',
            'minimum_order_amount' => 'nullable|numeric|min:0',
            'maximum_discount_amount' => 'nullable|numeric|min:0',
            'applicable_to' => 'nullable|in:all,specific_items,specific_categories',
            'applicable_items' => 'nullable|array',
            'applicable_items.*' => 'exists:menu_items,id',
            'applicable_categories' => 'nullable|array',
            'applicable_categories.*' => 'exists:food_categories,id',
            'start_date' => 'required|date',
            'end_date' => 'required|date|after:start_date',
            'start_time' => 'nullable|date_format:H:i',
            'end_time' => 'nullable|date_format:H:i',
            'days_of_week' => 'nullable|array',
            'days_of_week.*' => 'in:monday,tuesday,wednesday,thursday,friday,saturday,sunday',
            'usage_limit' => 'nullable|integer|min:1',
            'usage_limit_per_customer' => 'nullable|integer|min:1',
            'image' => 'nullable|image|max:2048',
            'is_active' => 'boolean',
            'is_featured' => 'boolean',
            'priority' => 'integer|min:0',
        ]);

        // Handle image upload
        if ($request->hasFile('image')) {
            // Delete old image if exists
            if ($promotion->image) {
                \Storage::disk('public')->delete($promotion->image);
            }
            $validated['image'] = $request->file('image')->store('promotions', 'public');
        }

        $promotion->update($validated);

        return response()->json([
            'success' => true,
            'message' => 'Promotion updated successfully.',
            'data' => $promotion,
        ]);
    }

    /**
     * Remove the specified promotion
     */
    public function destroy(Promotion $promotion): JsonResponse
    {
        // Delete image if exists
        if ($promotion->image) {
            \Storage::disk('public')->delete($promotion->image);
        }

        $promotion->delete();

        return response()->json([
            'success' => true,
            'message' => 'Promotion deleted successfully.',
        ]);
    }

    /**
     * Get active promotions
     */
    public function getActive(): JsonResponse
    {
        $promotions = Promotion::active()
                              ->orderBy('priority', 'desc')
                              ->orderBy('created_at', 'desc')
                              ->get();

        return response()->json([
            'success' => true,
            'data' => $promotions,
        ]);
    }

    /**
     * Get featured promotions
     */
    public function getFeatured(): JsonResponse
    {
        $promotions = Promotion::active()
                              ->where('is_featured', true)
                              ->orderBy('priority', 'desc')
                              ->limit(5)
                              ->get();

        return response()->json([
            'success' => true,
            'data' => $promotions,
        ]);
    }

    /**
     * Toggle promotion status
     */
    public function toggleStatus(Promotion $promotion): JsonResponse
    {
        $promotion->update(['is_active' => !$promotion->is_active]);

        $status = $promotion->is_active ? 'activated' : 'deactivated';

        return response()->json([
            'success' => true,
            'message' => "Promotion {$status} successfully.",
            'data' => $promotion,
        ]);
    }

    /**
     * Toggle featured status
     */
    public function toggleFeatured(Promotion $promotion): JsonResponse
    {
        $promotion->update(['is_featured' => !$promotion->is_featured]);

        $status = $promotion->is_featured ? 'featured' : 'unfeatured';

        return response()->json([
            'success' => true,
            'message' => "Promotion {$status} successfully.",
            'data' => $promotion,
        ]);
    }

    /**
     * Get promotion statistics
     */
    public function getStatistics(Request $request): JsonResponse
    {
        $query = Promotion::query();

        // Apply date filter
        if ($request->filled('date_from')) {
            $query->whereDate('created_at', '>=', $request->date_from);
        }

        if ($request->filled('date_to')) {
            $query->whereDate('created_at', '<=', $request->date_to);
        }

        $stats = [
            'total_promotions' => $query->count(),
            'active_promotions' => (clone $query)->active()->count(),
            'expired_promotions' => (clone $query)->expired()->count(),
            'scheduled_promotions' => (clone $query)->scheduled()->count(),
            'featured_promotions' => (clone $query)->where('is_featured', true)->count(),
            'total_usage' => (clone $query)->sum('used_count'),
            'promotion_types' => [
                'discount' => (clone $query)->where('type', 'discount')->count(),
                'bogo' => (clone $query)->where('type', 'bogo')->count(),
                'free_delivery' => (clone $query)->where('type', 'free_delivery')->count(),
                'combo_deal' => (clone $query)->where('type', 'combo_deal')->count(),
                'happy_hour' => (clone $query)->where('type', 'happy_hour')->count(),
            ],
        ];

        return response()->json([
            'success' => true,
            'data' => $stats,
        ]);
    }

    /**
     * Check promotion applicability
     */
    public function checkApplicability(Request $request): JsonResponse
    {
        $validated = $request->validate([
            'promotion_id' => 'required|exists:promotions,id',
            'order_amount' => 'required|numeric|min:0',
            'items' => 'nullable|array',
            'items.*' => 'exists:menu_items,id',
            'customer_id' => 'nullable|exists:customers,id',
        ]);

        $promotion = Promotion::find($validated['promotion_id']);

        $applicability = $promotion->checkApplicability(
            $validated['order_amount'],
            $validated['items'] ?? [],
            $validated['customer_id'] ?? null
        );

        return response()->json([
            'success' => true,
            'data' => $applicability,
        ]);
    }

    /**
     * Bulk update promotions
     */
    public function bulkUpdate(Request $request): JsonResponse
    {
        $validated = $request->validate([
            'action' => 'required|in:activate,deactivate,feature,unfeature,delete',
            'promotion_ids' => 'required|array|min:1',
            'promotion_ids.*' => 'exists:promotions,id',
        ]);

        $promotions = Promotion::whereIn('id', $validated['promotion_ids']);

        switch ($validated['action']) {
            case 'activate':
                $promotions->update(['is_active' => true]);
                $message = 'Selected promotions activated successfully.';
                break;

            case 'deactivate':
                $promotions->update(['is_active' => false]);
                $message = 'Selected promotions deactivated successfully.';
                break;

            case 'feature':
                $promotions->update(['is_featured' => true]);
                $message = 'Selected promotions featured successfully.';
                break;

            case 'unfeature':
                $promotions->update(['is_featured' => false]);
                $message = 'Selected promotions unfeatured successfully.';
                break;

            case 'delete':
                // Delete images
                $promotionsToDelete = $promotions->get();
                foreach ($promotionsToDelete as $promotion) {
                    if ($promotion->image) {
                        \Storage::disk('public')->delete($promotion->image);
                    }
                }
                $promotions->delete();
                $message = 'Selected promotions deleted successfully.';
                break;
        }

        return response()->json([
            'success' => true,
            'message' => $message,
        ]);
    }
}
