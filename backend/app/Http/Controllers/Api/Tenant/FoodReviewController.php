<?php

namespace App\Http\Controllers\Api\Tenant;

use App\Http\Controllers\Controller;
use App\Models\Tenant\FoodReview;
use App\Models\MenuItem;
use App\Models\Customer;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Storage;

class FoodReviewController extends Controller
{
    public function __construct()
    {
        $this->middleware('auth:sanctum')->except(['index', 'show', 'getStatistics']);
        $this->middleware('permission:view_reviews')->only(['index', 'show']);
        $this->middleware('permission:create_reviews')->only(['store']);
        $this->middleware('permission:edit_reviews')->only(['update']);
        $this->middleware('permission:delete_reviews')->only(['destroy']);
        $this->middleware('permission:moderate_reviews')->only(['approve', 'reject', 'feature', 'respond']);
    }

    /**
     * Display a listing of food reviews
     */
    public function index(Request $request): JsonResponse
    {
        $query = FoodReview::with(['menuItem', 'customer', 'order']);

        // Apply filters
        if ($request->filled('menu_item_id')) {
            $query->where('menu_item_id', $request->menu_item_id);
        }

        if ($request->filled('rating')) {
            $query->where('rating', $request->rating);
        }

        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }

        if ($request->filled('customer_id')) {
            $query->where('customer_id', $request->customer_id);
        }

        if ($request->filled('date_from')) {
            $query->whereDate('created_at', '>=', $request->date_from);
        }

        if ($request->filled('date_to')) {
            $query->whereDate('created_at', '<=', $request->date_to);
        }

        if ($request->filled('featured')) {
            $query->where('featured', $request->boolean('featured'));
        }

        if ($request->filled('verified_purchase')) {
            $query->where('is_verified_purchase', $request->boolean('verified_purchase'));
        }

        // Search by review content or title
        if ($request->filled('search')) {
            $query->where(function ($q) use ($request) {
                $q->where('title', 'like', '%' . $request->search . '%')
                  ->orWhere('review', 'like', '%' . $request->search . '%')
                  ->orWhere('reviewer_name', 'like', '%' . $request->search . '%');
            });
        }

        // Sort options
        $sortBy = $request->get('sort_by', 'created_at');
        $sortOrder = $request->get('sort_order', 'desc');
        
        $allowedSorts = ['created_at', 'rating', 'helpful_count', 'moderated_at'];
        if (in_array($sortBy, $allowedSorts)) {
            $query->orderBy($sortBy, $sortOrder);
        }

        $reviews = $query->paginate($request->get('per_page', 15));

        return response()->json([
            'success' => true,
            'data' => $reviews,
            'meta' => [
                'stats' => $this->getReviewStats($request),
            ]
        ]);
    }

    /**
     * Store a newly created food review
     */
    public function store(Request $request): JsonResponse
    {
        $validated = $request->validate([
            'menu_item_id' => 'required|exists:menu_items,id',
            'customer_id' => 'nullable|exists:customers,id',
            'order_id' => 'nullable|exists:orders,id',
            'rating' => 'required|integer|min:1|max:5',
            'title' => 'nullable|string|max:255',
            'review' => 'required|string|max:2000',
            'images' => 'nullable|array|max:5',
            'images.*' => 'image|mimes:jpeg,png,jpg,gif|max:2048',
            'taste_rating' => 'nullable|integer|min:1|max:5',
            'presentation_rating' => 'nullable|integer|min:1|max:5',
            'value_rating' => 'nullable|integer|min:1|max:5',
            'portion_rating' => 'nullable|integer|min:1|max:5',
            'reviewer_name' => 'nullable|string|max:255',
            'is_anonymous' => 'boolean',
        ]);

        // Check if customer already reviewed this menu item
        if ($validated['customer_id'] && $validated['order_id']) {
            $existingReview = FoodReview::where('customer_id', $validated['customer_id'])
                                      ->where('menu_item_id', $validated['menu_item_id'])
                                      ->where('order_id', $validated['order_id'])
                                      ->first();

            if ($existingReview) {
                return response()->json([
                    'success' => false,
                    'message' => 'You have already reviewed this item for this order.',
                ], 422);
            }
        }

        // Handle image uploads
        $imageUrls = [];
        if ($request->hasFile('images')) {
            foreach ($request->file('images') as $image) {
                $path = $image->store('food-reviews', 'public');
                $imageUrls[] = Storage::url($path);
            }
        }

        $validated['images'] = $imageUrls;
        $validated['restaurant_id'] = tenant('id');
        $validated['is_verified_purchase'] = $validated['order_id'] ? true : false;
        $validated['status'] = 'pending'; // All reviews start as pending

        $review = FoodReview::create($validated);
        $review->load(['menuItem', 'customer', 'order']);

        return response()->json([
            'success' => true,
            'message' => 'Review submitted successfully and is pending approval.',
            'data' => $review,
        ], 201);
    }

    /**
     * Display the specified food review
     */
    public function show(FoodReview $foodReview): JsonResponse
    {
        $foodReview->load(['menuItem', 'customer', 'order', 'moderatedBy', 'respondedBy']);

        return response()->json([
            'success' => true,
            'data' => $foodReview,
        ]);
    }

    /**
     * Update the specified food review
     */
    public function update(Request $request, FoodReview $foodReview): JsonResponse
    {
        // Check if user can update this review
        if (!$this->canUpdateReview($foodReview)) {
            return response()->json([
                'success' => false,
                'message' => 'Unauthorized to update this review.',
            ], 403);
        }

        $validated = $request->validate([
            'rating' => 'integer|min:1|max:5',
            'title' => 'nullable|string|max:255',
            'review' => 'string|max:2000',
            'taste_rating' => 'nullable|integer|min:1|max:5',
            'presentation_rating' => 'nullable|integer|min:1|max:5',
            'value_rating' => 'nullable|integer|min:1|max:5',
            'portion_rating' => 'nullable|integer|min:1|max:5',
            'status' => 'in:pending,approved,rejected,hidden',
            'moderation_notes' => 'nullable|string|max:500',
            'featured' => 'boolean',
        ]);

        // Only admins can change status and moderation fields
        if (!Auth::user()->can('moderate_reviews')) {
            unset($validated['status'], $validated['moderation_notes'], $validated['featured']);
        } else {
            if (isset($validated['status']) && $validated['status'] !== $foodReview->status) {
                $validated['moderated_by'] = Auth::id();
                $validated['moderated_at'] = now();
            }
        }

        $foodReview->update($validated);
        $foodReview->load(['menuItem', 'customer', 'order']);

        return response()->json([
            'success' => true,
            'message' => 'Review updated successfully.',
            'data' => $foodReview,
        ]);
    }

    /**
     * Remove the specified food review
     */
    public function destroy(FoodReview $foodReview): JsonResponse
    {
        // Check if user can delete this review
        if (!$this->canDeleteReview($foodReview)) {
            return response()->json([
                'success' => false,
                'message' => 'Unauthorized to delete this review.',
            ], 403);
        }

        // Delete associated images
        if ($foodReview->images) {
            foreach ($foodReview->images as $imageUrl) {
                $path = str_replace('/storage/', '', parse_url($imageUrl, PHP_URL_PATH));
                Storage::disk('public')->delete($path);
            }
        }

        $foodReview->delete();

        return response()->json([
            'success' => true,
            'message' => 'Review deleted successfully.',
        ]);
    }

    /**
     * Approve a food review
     */
    public function approve(FoodReview $foodReview): JsonResponse
    {
        $foodReview->update([
            'status' => 'approved',
            'moderated_by' => Auth::id(),
            'moderated_at' => now(),
        ]);

        return response()->json([
            'success' => true,
            'message' => 'Review approved successfully.',
            'data' => $foodReview->fresh(),
        ]);
    }

    /**
     * Reject a food review
     */
    public function reject(Request $request, FoodReview $foodReview): JsonResponse
    {
        $validated = $request->validate([
            'moderation_notes' => 'nullable|string|max:500',
        ]);

        $foodReview->update([
            'status' => 'rejected',
            'moderated_by' => Auth::id(),
            'moderated_at' => now(),
            'moderation_notes' => $validated['moderation_notes'] ?? null,
        ]);

        return response()->json([
            'success' => true,
            'message' => 'Review rejected successfully.',
            'data' => $foodReview->fresh(),
        ]);
    }

    /**
     * Feature/unfeature a food review
     */
    public function feature(FoodReview $foodReview): JsonResponse
    {
        $foodReview->update([
            'featured' => !$foodReview->featured,
        ]);

        $message = $foodReview->featured ? 'Review featured successfully.' : 'Review unfeatured successfully.';

        return response()->json([
            'success' => true,
            'message' => $message,
            'data' => $foodReview->fresh(),
        ]);
    }

    /**
     * Add restaurant response to review
     */
    public function respond(Request $request, FoodReview $foodReview): JsonResponse
    {
        $validated = $request->validate([
            'restaurant_response' => 'required|string|max:1000',
        ]);

        $foodReview->update([
            'restaurant_response' => $validated['restaurant_response'],
            'responded_at' => now(),
            'responded_by' => Auth::id(),
        ]);

        return response()->json([
            'success' => true,
            'message' => 'Response added successfully.',
            'data' => $foodReview->fresh(),
        ]);
    }

    /**
     * Get review statistics
     */
    private function getReviewStats(Request $request): array
    {
        $query = FoodReview::query();

        // Apply same filters as main query
        if ($request->filled('menu_item_id')) {
            $query->where('menu_item_id', $request->menu_item_id);
        }

        if ($request->filled('date_from')) {
            $query->whereDate('created_at', '>=', $request->date_from);
        }

        if ($request->filled('date_to')) {
            $query->whereDate('created_at', '<=', $request->date_to);
        }

        return [
            'total' => $query->count(),
            'pending' => $query->where('status', 'pending')->count(),
            'approved' => $query->where('status', 'approved')->count(),
            'rejected' => $query->where('status', 'rejected')->count(),
            'featured' => $query->where('featured', true)->count(),
            'average_rating' => round($query->avg('rating'), 2),
            'verified_purchases' => $query->where('is_verified_purchase', true)->count(),
        ];
    }

    /**
     * Check if user can update review
     */
    private function canUpdateReview(FoodReview $review): bool
    {
        if (Auth::user()->can('moderate_reviews')) {
            return true;
        }

        $customerId = Auth::guard('customer')->id();
        return $customerId && $review->customer_id === $customerId;
    }

    /**
     * Check if user can delete review
     */
    private function canDeleteReview(FoodReview $review): bool
    {
        if (Auth::user()->can('moderate_reviews')) {
            return true;
        }

        $customerId = Auth::guard('customer')->id();
        return $customerId && $review->customer_id === $customerId;
    }
}
