<?php

namespace App\Http\Controllers\Api\Tenant;

use App\Http\Controllers\Controller;
use App\Services\Tenant\LoyaltyService;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;

class LoyaltyController extends Controller
{
    protected LoyaltyService $loyaltyService;

    public function __construct(LoyaltyService $loyaltyService)
    {
        $this->loyaltyService = $loyaltyService;
    }

    /**
     * Check if loyalty program is enabled
     */
    public function status(): JsonResponse
    {
        return response()->json([
            'enabled' => $this->loyaltyService->isEnabled(),
        ]);
    }

    /**
     * Lookup loyalty account by phone number
     */
    public function lookup(Request $request): JsonResponse
    {
        $request->validate([
            'phone_number' => 'required|string',
        ]);

        if (!$this->loyaltyService->isEnabled()) {
            return response()->json([
                'success' => false,
                'message' => 'Loyalty program is not enabled',
            ], 400);
        }

        $account = $this->loyaltyService->getAccountByPhone($request->phone_number);

        if (!$account) {
            return response()->json([
                'success' => false,
                'found' => false,
                'message' => 'No loyalty account found for this phone number',
            ]);
        }

        return response()->json([
            'success' => true,
            'found' => true,
            'account' => [
                'id' => $account->id,
                'phone_number' => $account->phone_number,
                'customer_name' => $account->customer_name,
                'email' => $account->email,
                'total_points' => $account->total_points,
                'available_points' => $account->available_points,
                'tier' => $account->tier,
                'tier_color' => $account->tier_color,
                'total_orders' => $account->total_orders,
                'total_spent' => $account->total_spent,
            ],
        ]);
    }

    /**
     * Calculate potential discount for points redemption
     */
    public function calculateDiscount(Request $request): JsonResponse
    {
        $request->validate([
            'phone_number' => 'required|string',
            'points_to_use' => 'required|integer|min:1',
            'order_amount' => 'required|numeric|min:0.01',
        ]);

        if (!$this->loyaltyService->isEnabled()) {
            return response()->json([
                'success' => false,
                'message' => 'Loyalty program is not enabled',
            ], 400);
        }

        try {
            $account = $this->loyaltyService->getAccountByPhone($request->phone_number);
            
            if (!$account) {
                return response()->json([
                    'success' => false,
                    'message' => 'Loyalty account not found',
                ], 404);
            }

            if ($request->points_to_use > $account->total_points) {
                return response()->json([
                    'success' => false,
                    'message' => 'Insufficient points balance',
                ], 400);
            }

            $discountInfo = $this->loyaltyService->calculatePotentialDiscount(
                $request->points_to_use,
                $request->order_amount
            );

            return response()->json([
                'success' => true,
                'discount_info' => $discountInfo,
                'account_balance' => $account->total_points,
                'remaining_balance' => $account->total_points - $request->points_to_use,
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Validate points redemption
     */
    public function validateRedemption(Request $request): JsonResponse
    {
        $request->validate([
            'phone_number' => 'required|string',
            'points_to_redeem' => 'required|integer|min:1',
            'order_amount' => 'required|numeric|min:0.01',
        ]);

        if (!$this->loyaltyService->isEnabled()) {
            return response()->json([
                'success' => false,
                'message' => 'Loyalty program is not enabled',
            ], 400);
        }

        try {
            $redemptionInfo = $this->loyaltyService->processPointsRedemption(
                $request->phone_number,
                $request->points_to_redeem,
                $request->order_amount
            );

            return response()->json([
                'success' => true,
                'redemption_info' => $redemptionInfo,
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => $e->getMessage(),
            ], 400);
        }
    }

    /**
     * Get account transaction history
     */
    public function transactions(Request $request): JsonResponse
    {
        $request->validate([
            'phone_number' => 'required|string',
            'limit' => 'nullable|integer|min:1|max:50',
        ]);

        if (!$this->loyaltyService->isEnabled()) {
            return response()->json([
                'success' => false,
                'message' => 'Loyalty program is not enabled',
            ], 400);
        }

        $account = $this->loyaltyService->getAccountByPhone($request->phone_number);

        if (!$account) {
            return response()->json([
                'success' => false,
                'message' => 'Loyalty account not found',
            ], 404);
        }

        $limit = $request->get('limit', 10);
        $transactions = $account->transactions()
            ->orderBy('created_at', 'desc')
            ->limit($limit)
            ->get()
            ->map(function ($transaction) {
                return [
                    'id' => $transaction->id,
                    'type' => $transaction->transaction_type,
                    'formatted_type' => $transaction->formatted_type,
                    'points' => $transaction->points,
                    'description' => $transaction->description,
                    'order_id' => $transaction->order_id,
                    'created_at' => $transaction->created_at->format('Y-m-d H:i:s'),
                    'icon' => $transaction->icon,
                ];
            });

        return response()->json([
            'success' => true,
            'transactions' => $transactions,
        ]);
    }

    /**
     * Create or update loyalty account
     */
    public function createAccount(Request $request): JsonResponse
    {
        $request->validate([
            'phone_number' => 'required|string',
            'customer_name' => 'required|string|max:255',
            'email' => 'nullable|email|max:255',
            'address' => 'nullable|string|max:500',
            'date_of_birth' => 'nullable|date',
        ]);

        if (!$this->loyaltyService->isEnabled()) {
            return response()->json([
                'success' => false,
                'message' => 'Loyalty program is not enabled',
            ], 400);
        }

        try {
            $account = $this->loyaltyService->createOrUpdateAccount(
                $request->phone_number,
                $request->only(['customer_name', 'email', 'address', 'date_of_birth'])
            );

            return response()->json([
                'success' => true,
                'message' => 'Loyalty account created/updated successfully',
                'account' => [
                    'id' => $account->id,
                    'phone_number' => $account->phone_number,
                    'customer_name' => $account->customer_name,
                    'email' => $account->email,
                    'total_points' => $account->total_points,
                    'tier' => $account->tier,
                ],
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to create/update loyalty account: ' . $e->getMessage(),
            ], 500);
        }
    }
}
