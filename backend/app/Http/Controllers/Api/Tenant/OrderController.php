<?php

namespace App\Http\Controllers\Api\Tenant;

use App\Http\Controllers\Controller;
use App\Http\Requests\Tenant\StoreOrderRequest;
use App\Http\Requests\Tenant\UpdateOrderRequest;
use App\Http\Resources\Tenant\OrderResource;
use App\Http\Resources\Tenant\OrderCollection;
use App\Models\Tenant\Order;
use App\Models\Tenant\Customer;
use App\Services\Tenant\OrderService;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;

class OrderController extends Controller
{
    public function __construct(
        protected OrderService $orderService
    ) {}

    /**
     * Display a listing of orders
     */
    public function index(Request $request): OrderCollection
    {
        $query = Order::with(['customer', 'table', 'items.food', 'statusHistory'])
            ->latest();

        // Filter by status
        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }

        // Filter by order type
        if ($request->filled('order_type')) {
            $query->where('order_type', $request->order_type);
        }

        // Filter by date range
        if ($request->filled('date_from')) {
            $query->whereDate('created_at', '>=', $request->date_from);
        }

        if ($request->filled('date_to')) {
            $query->whereDate('created_at', '<=', $request->date_to);
        }

        // Filter by customer
        if ($request->filled('customer_id')) {
            $query->where('customer_id', $request->customer_id);
        }

        // Search by order number or customer name
        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->where('order_number', 'like', "%{$search}%")
                  ->orWhere('customer_name', 'like', "%{$search}%")
                  ->orWhere('customer_phone', 'like', "%{$search}%");
            });
        }

        $orders = $query->paginate($request->get('per_page', 15));

        return new OrderCollection($orders);
    }

    /**
     * Store a newly created order
     */
    public function store(StoreOrderRequest $request): JsonResponse
    {
        try {
            $order = $this->orderService->createOrder($request->validated());
            
            return response()->json([
                'message' => 'Order created successfully',
                'data' => new OrderResource($order->load(['customer', 'items.food', 'items.addons']))
            ], 201);
        } catch (\Exception $e) {
            return response()->json([
                'message' => 'Failed to create order',
                'error' => $e->getMessage()
            ], 422);
        }
    }

    /**
     * Display the specified order
     */
    public function show(Order $order): OrderResource
    {
        $order->load([
            'customer',
            'table',
            'items.food',
            'items.foodVariant',
            'items.addons.foodAddon',
            'statusHistory.changedBy',
            'deliveryOrder.deliveryDriver.employee',
            'couponUsage.coupon'
        ]);

        return new OrderResource($order);
    }

    /**
     * Update the specified order
     */
    public function update(UpdateOrderRequest $request, Order $order): JsonResponse
    {
        try {
            $updatedOrder = $this->orderService->updateOrder($order, $request->validated());
            
            return response()->json([
                'message' => 'Order updated successfully',
                'data' => new OrderResource($updatedOrder)
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'message' => 'Failed to update order',
                'error' => $e->getMessage()
            ], 422);
        }
    }

    /**
     * Update order status
     */
    public function updateStatus(Request $request, Order $order): JsonResponse
    {
        $request->validate([
            'status' => 'required|string|in:pending,confirmed,preparing,ready,out_for_delivery,delivered,completed,cancelled',
            'notes' => 'nullable|string|max:500'
        ]);

        try {
            $this->orderService->updateOrderStatus(
                $order, 
                $request->status, 
                $request->user(), 
                $request->notes
            );
            
            return response()->json([
                'message' => 'Order status updated successfully',
                'data' => new OrderResource($order->fresh(['statusHistory.changedBy']))
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'message' => 'Failed to update order status',
                'error' => $e->getMessage()
            ], 422);
        }
    }

    /**
     * Cancel an order
     */
    public function cancel(Request $request, Order $order): JsonResponse
    {
        $request->validate([
            'reason' => 'required|string|max:500'
        ]);

        if (!$order->canBeCancelled()) {
            return response()->json([
                'message' => 'Order cannot be cancelled at this stage'
            ], 422);
        }

        try {
            $this->orderService->cancelOrder($order, $request->reason, $request->user());
            
            return response()->json([
                'message' => 'Order cancelled successfully',
                'data' => new OrderResource($order->fresh())
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'message' => 'Failed to cancel order',
                'error' => $e->getMessage()
            ], 422);
        }
    }

    /**
     * Get order statistics
     */
    public function statistics(Request $request): JsonResponse
    {
        $stats = $this->orderService->getOrderStatistics($request->all());
        
        return response()->json([
            'data' => $stats
        ]);
    }

    /**
     * Get kitchen orders (for kitchen display)
     */
    public function kitchen(): JsonResponse
    {
        $orders = Order::with(['items.food', 'table'])
            ->whereIn('status', ['confirmed', 'preparing'])
            ->orderBy('created_at')
            ->get();

        return response()->json([
            'data' => OrderResource::collection($orders)
        ]);
    }

    /**
     * Get active orders for customer
     */
    public function customerActive(Customer $customer): JsonResponse
    {
        $orders = $customer->orders()
            ->with(['items.food', 'statusHistory'])
            ->active()
            ->latest()
            ->get();

        return response()->json([
            'data' => OrderResource::collection($orders)
        ]);
    }
}
