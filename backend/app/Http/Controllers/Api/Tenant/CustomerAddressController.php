<?php

namespace App\Http\Controllers\Api\Tenant;

use App\Http\Controllers\Controller;
use App\Models\Customer;
use App\Models\CustomerAddress;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class CustomerAddressController extends Controller
{
    public function __construct()
    {
        $this->middleware(['auth:sanctum']);
    }

    /**
     * Display a listing of customer addresses
     */
    public function index(Request $request, Customer $customer = null)
    {
        // If customer is provided, get addresses for that customer (admin/manager view)
        if ($customer) {
            $this->authorize('view', $customer);
            $query = $customer->addresses();
        } else {
            // Get addresses for authenticated customer
            $customerId = Auth::guard('customer')->id();
            if (!$customerId) {
                return response()->json([
                    'success' => false,
                    'message' => 'Customer not authenticated.',
                ], 401);
            }
            $query = CustomerAddress::where('customer_id', $customerId);
        }

        // Filter by type
        if ($request->filled('type')) {
            $query->where('type', $request->type);
        }

        // Filter by status
        if ($request->filled('is_active')) {
            $query->where('is_active', $request->boolean('is_active'));
        }

        $addresses = $query->orderBy('is_default', 'desc')
                          ->orderBy('created_at', 'desc')
                          ->get();

        return response()->json([
            'success' => true,
            'data' => $addresses,
            'meta' => [
                'total' => $addresses->count(),
                'default_address' => $addresses->where('is_default', true)->first(),
            ]
        ]);
    }

    /**
     * Store a newly created customer address
     */
    public function store(Request $request, Customer $customer = null)
    {
        $request->validate([
            'type' => 'required|in:home,work,other',
            'label' => 'nullable|string|max:100',
            'address_line_1' => 'required|string|max:255',
            'address_line_2' => 'nullable|string|max:255',
            'city' => 'required|string|max:100',
            'state' => 'nullable|string|max:100',
            'postal_code' => 'required|string|max:20',
            'country' => 'required|string|max:100',
            'latitude' => 'nullable|numeric|between:-90,90',
            'longitude' => 'nullable|numeric|between:-180,180',
            'delivery_instructions' => 'nullable|string|max:500',
            'is_default' => 'boolean',
            'is_active' => 'boolean',
        ]);

        // Determine customer ID
        if ($customer) {
            $this->authorize('update', $customer);
            $customerId = $customer->id;
        } else {
            $customerId = Auth::guard('customer')->id();
            if (!$customerId) {
                return response()->json([
                    'success' => false,
                    'message' => 'Customer not authenticated.',
                ], 401);
            }
        }

        $data = $request->only([
            'type', 'label', 'address_line_1', 'address_line_2', 'city',
            'state', 'postal_code', 'country', 'latitude', 'longitude',
            'delivery_instructions', 'is_active'
        ]);

        $data['customer_id'] = $customerId;
        $data['is_default'] = $request->boolean('is_default');
        $data['is_active'] = $request->boolean('is_active', true);

        // If this is set as default, unset other default addresses
        if ($data['is_default']) {
            CustomerAddress::where('customer_id', $customerId)
                          ->where('is_default', true)
                          ->update(['is_default' => false]);
        }

        // If this is the first address, make it default
        $existingAddressCount = CustomerAddress::where('customer_id', $customerId)->count();
        if ($existingAddressCount === 0) {
            $data['is_default'] = true;
        }

        // Validate delivery area if coordinates provided
        if ($data['latitude'] && $data['longitude']) {
            $isInDeliveryArea = $this->validateDeliveryArea($data['latitude'], $data['longitude']);
            if (!$isInDeliveryArea) {
                return response()->json([
                    'success' => false,
                    'message' => 'Address is outside our delivery area.',
                    'errors' => ['location' => ['This location is outside our delivery area.']],
                ], 422);
            }
        }

        $address = CustomerAddress::create($data);

        return response()->json([
            'success' => true,
            'message' => 'Address created successfully.',
            'data' => $address,
        ], 201);
    }

    /**
     * Display the specified customer address
     */
    public function show(CustomerAddress $customerAddress)
    {
        // Check if user can view this address
        if (!$this->canAccessAddress($customerAddress)) {
            return response()->json([
                'success' => false,
                'message' => 'Unauthorized to view this address.',
            ], 403);
        }

        return response()->json([
            'success' => true,
            'data' => $customerAddress->load('customer'),
        ]);
    }

    /**
     * Update the specified customer address
     */
    public function update(Request $request, CustomerAddress $customerAddress)
    {
        // Check if user can update this address
        if (!$this->canAccessAddress($customerAddress)) {
            return response()->json([
                'success' => false,
                'message' => 'Unauthorized to update this address.',
            ], 403);
        }

        $request->validate([
            'type' => 'required|in:home,work,other',
            'label' => 'nullable|string|max:100',
            'address_line_1' => 'required|string|max:255',
            'address_line_2' => 'nullable|string|max:255',
            'city' => 'required|string|max:100',
            'state' => 'nullable|string|max:100',
            'postal_code' => 'required|string|max:20',
            'country' => 'required|string|max:100',
            'latitude' => 'nullable|numeric|between:-90,90',
            'longitude' => 'nullable|numeric|between:-180,180',
            'delivery_instructions' => 'nullable|string|max:500',
            'is_default' => 'boolean',
            'is_active' => 'boolean',
        ]);

        $data = $request->only([
            'type', 'label', 'address_line_1', 'address_line_2', 'city',
            'state', 'postal_code', 'country', 'latitude', 'longitude',
            'delivery_instructions', 'is_active'
        ]);

        $data['is_default'] = $request->boolean('is_default');

        // If this is set as default, unset other default addresses
        if ($data['is_default']) {
            CustomerAddress::where('customer_id', $customerAddress->customer_id)
                          ->where('id', '!=', $customerAddress->id)
                          ->where('is_default', true)
                          ->update(['is_default' => false]);
        }

        // Validate delivery area if coordinates provided
        if ($data['latitude'] && $data['longitude']) {
            $isInDeliveryArea = $this->validateDeliveryArea($data['latitude'], $data['longitude']);
            if (!$isInDeliveryArea) {
                return response()->json([
                    'success' => false,
                    'message' => 'Address is outside our delivery area.',
                    'errors' => ['location' => ['This location is outside our delivery area.']],
                ], 422);
            }
        }

        $customerAddress->update($data);

        return response()->json([
            'success' => true,
            'message' => 'Address updated successfully.',
            'data' => $customerAddress->fresh(),
        ]);
    }

    /**
     * Remove the specified customer address
     */
    public function destroy(CustomerAddress $customerAddress)
    {
        // Check if user can delete this address
        if (!$this->canAccessAddress($customerAddress)) {
            return response()->json([
                'success' => false,
                'message' => 'Unauthorized to delete this address.',
            ], 403);
        }

        // Prevent deleting the only address
        $addressCount = CustomerAddress::where('customer_id', $customerAddress->customer_id)->count();
        if ($addressCount === 1) {
            return response()->json([
                'success' => false,
                'message' => 'Cannot delete the only address. Please add another address first.',
            ], 422);
        }

        // If deleting default address, set another as default
        if ($customerAddress->is_default) {
            $newDefault = CustomerAddress::where('customer_id', $customerAddress->customer_id)
                                        ->where('id', '!=', $customerAddress->id)
                                        ->where('is_active', true)
                                        ->first();

            if ($newDefault) {
                $newDefault->update(['is_default' => true]);
            }
        }

        $customerAddress->delete();

        return response()->json([
            'success' => true,
            'message' => 'Address deleted successfully.',
        ]);
    }

    /**
     * Set address as default
     */
    public function setDefault(CustomerAddress $customerAddress)
    {
        // Check if user can update this address
        if (!$this->canAccessAddress($customerAddress)) {
            return response()->json([
                'success' => false,
                'message' => 'Unauthorized to update this address.',
            ], 403);
        }

        // Unset other default addresses
        CustomerAddress::where('customer_id', $customerAddress->customer_id)
                      ->where('is_default', true)
                      ->update(['is_default' => false]);

        // Set this address as default
        $customerAddress->update(['is_default' => true]);

        return response()->json([
            'success' => true,
            'message' => 'Default address updated successfully.',
            'data' => $customerAddress->fresh(),
        ]);
    }

    /**
     * Validate address coordinates
     */
    public function validateAddress(Request $request)
    {
        $request->validate([
            'latitude' => 'required|numeric|between:-90,90',
            'longitude' => 'required|numeric|between:-180,180',
        ]);

        $isValid = $this->validateDeliveryArea($request->latitude, $request->longitude);
        $deliveryFee = $this->calculateDeliveryFee($request->latitude, $request->longitude);

        return response()->json([
            'success' => true,
            'data' => [
                'is_valid' => $isValid,
                'delivery_fee' => $deliveryFee,
                'estimated_delivery_time' => $this->estimateDeliveryTime($request->latitude, $request->longitude),
                'message' => $isValid ? 'Address is within delivery area.' : 'Address is outside delivery area.',
            ]
        ]);
    }

    /**
     * Get addresses within delivery area
     */
    public function getDeliveryAddresses(Customer $customer = null)
    {
        $customerId = $customer ? $customer->id : Auth::guard('customer')->id();

        if (!$customerId) {
            return response()->json([
                'success' => false,
                'message' => 'Customer not authenticated.',
            ], 401);
        }

        $addresses = CustomerAddress::where('customer_id', $customerId)
                                   ->where('is_active', true)
                                   ->get()
                                   ->filter(function ($address) {
                                       if (!$address->latitude || !$address->longitude) {
                                           return true; // Include addresses without coordinates
                                       }
                                       return $this->validateDeliveryArea($address->latitude, $address->longitude);
                                   })
                                   ->map(function ($address) {
                                       $address->delivery_fee = $this->calculateDeliveryFee($address->latitude, $address->longitude);
                                       $address->estimated_delivery_time = $this->estimateDeliveryTime($address->latitude, $address->longitude);
                                       return $address;
                                   });

        return response()->json([
            'success' => true,
            'data' => $addresses->values(),
        ]);
    }

    /**
     * Search addresses by query
     */
    public function search(Request $request)
    {
        $request->validate([
            'query' => 'required|string|min:2|max:100',
            'customer_id' => 'nullable|exists:customers,id',
        ]);

        $query = CustomerAddress::query();

        // If customer_id provided and user has permission
        if ($request->customer_id) {
            $customer = Customer::find($request->customer_id);
            if ($customer && $this->canAccessCustomer($customer)) {
                $query->where('customer_id', $request->customer_id);
            }
        } else {
            // Search own addresses
            $customerId = Auth::guard('customer')->id();
            if ($customerId) {
                $query->where('customer_id', $customerId);
            }
        }

        $addresses = $query->where(function ($q) use ($request) {
                           $q->where('address_line_1', 'like', '%' . $request->query . '%')
                             ->orWhere('address_line_2', 'like', '%' . $request->query . '%')
                             ->orWhere('city', 'like', '%' . $request->query . '%')
                             ->orWhere('label', 'like', '%' . $request->query . '%');
                       })
                       ->where('is_active', true)
                       ->limit(10)
                       ->get();

        return response()->json([
            'success' => true,
            'data' => $addresses,
        ]);
    }

    /**
     * Check if user can access the address
     */
    private function canAccessAddress(CustomerAddress $address)
    {
        // Admin/Manager can access any address
        if (Auth::user() && Auth::user()->hasAnyRole(['admin', 'restaurant_manager'])) {
            return true;
        }

        // Customer can only access their own addresses
        $customerId = Auth::guard('customer')->id();
        return $customerId && $address->customer_id === $customerId;
    }

    /**
     * Check if user can access the customer
     */
    private function canAccessCustomer(Customer $customer)
    {
        // Admin/Manager can access any customer
        if (Auth::user() && Auth::user()->hasAnyRole(['admin', 'restaurant_manager'])) {
            return true;
        }

        // Customer can only access their own data
        $customerId = Auth::guard('customer')->id();
        return $customerId && $customer->id === $customerId;
    }

    /**
     * Validate if coordinates are within delivery area
     */
    private function validateDeliveryArea($latitude, $longitude)
    {
        // Get restaurant coordinates and delivery radius from tenant settings
        $tenant = tenant();
        $restaurantLat = $tenant->latitude ?? 0;
        $restaurantLng = $tenant->longitude ?? 0;
        $deliveryRadius = $tenant->delivery_radius ?? 10; // km

        if (!$restaurantLat || !$restaurantLng) {
            return true; // If restaurant coordinates not set, allow all
        }

        $distance = $this->calculateDistance($latitude, $longitude, $restaurantLat, $restaurantLng);
        return $distance <= $deliveryRadius;
    }

    /**
     * Calculate delivery fee based on distance
     */
    private function calculateDeliveryFee($latitude, $longitude)
    {
        $tenant = tenant();
        $baseDeliveryFee = $tenant->delivery_fee ?? 0;

        if (!$latitude || !$longitude) {
            return $baseDeliveryFee;
        }

        $restaurantLat = $tenant->latitude ?? 0;
        $restaurantLng = $tenant->longitude ?? 0;

        if (!$restaurantLat || !$restaurantLng) {
            return $baseDeliveryFee;
        }

        $distance = $this->calculateDistance($latitude, $longitude, $restaurantLat, $restaurantLng);

        // Add extra fee for distance beyond base radius
        $baseRadius = 5; // km
        if ($distance > $baseRadius) {
            $extraDistance = $distance - $baseRadius;
            $extraFee = $extraDistance * 0.5; // $0.5 per km
            return $baseDeliveryFee + $extraFee;
        }

        return $baseDeliveryFee;
    }

    /**
     * Estimate delivery time based on distance
     */
    private function estimateDeliveryTime($latitude, $longitude)
    {
        $tenant = tenant();
        $restaurantLat = $tenant->latitude ?? 0;
        $restaurantLng = $tenant->longitude ?? 0;

        if (!$latitude || !$longitude || !$restaurantLat || !$restaurantLng) {
            return 30; // Default 30 minutes
        }

        $distance = $this->calculateDistance($latitude, $longitude, $restaurantLat, $restaurantLng);

        // Base time + travel time (assuming 30 km/h average speed)
        $baseTime = 20; // minutes
        $travelTime = ($distance / 30) * 60; // minutes

        return round($baseTime + $travelTime);
    }

    /**
     * Calculate distance between two coordinates using Haversine formula
     */
    private function calculateDistance($lat1, $lon1, $lat2, $lon2)
    {
        $earthRadius = 6371; // km

        $dLat = deg2rad($lat2 - $lat1);
        $dLon = deg2rad($lon2 - $lon1);

        $a = sin($dLat/2) * sin($dLat/2) + cos(deg2rad($lat1)) * cos(deg2rad($lat2)) * sin($dLon/2) * sin($dLon/2);
        $c = 2 * atan2(sqrt($a), sqrt(1-$a));

        return $earthRadius * $c;
    }

    /**
     * Get address suggestions based on partial input
     */
    public function suggestions(Request $request)
    {
        $request->validate([
            'query' => 'required|string|min:3|max:100',
            'type' => 'nullable|in:city,street,postal_code',
        ]);

        // This would integrate with a geocoding service like Google Places API
        // For now, returning mock suggestions
        $suggestions = [
            [
                'description' => '123 Main Street, Downtown',
                'place_id' => 'place_123',
                'latitude' => 40.7128,
                'longitude' => -74.0060,
            ],
            [
                'description' => '456 Oak Avenue, Midtown',
                'place_id' => 'place_456',
                'latitude' => 40.7589,
                'longitude' => -73.9851,
            ],
        ];

        return response()->json([
            'success' => true,
            'data' => $suggestions,
        ]);
    }

    /**
     * Get address details from place ID
     */
    public function getPlaceDetails(Request $request)
    {
        $request->validate([
            'place_id' => 'required|string',
        ]);

        // This would integrate with a geocoding service
        // For now, returning mock data
        $placeDetails = [
            'place_id' => $request->place_id,
            'formatted_address' => '123 Main Street, Downtown, City, State 12345',
            'address_components' => [
                'street_number' => '123',
                'route' => 'Main Street',
                'locality' => 'Downtown',
                'administrative_area_level_1' => 'State',
                'postal_code' => '12345',
                'country' => 'Country',
            ],
            'geometry' => [
                'latitude' => 40.7128,
                'longitude' => -74.0060,
            ],
        ];

        return response()->json([
            'success' => true,
            'data' => $placeDetails,
        ]);
    }
}
