<?php

namespace App\Http\Controllers\Api\Tenant;

use App\Http\Controllers\Controller;
use App\Models\FoodAddon;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Validation\Rule;

class FoodAddonController extends Controller
{
    public function __construct()
    {
        $this->middleware('auth:sanctum')->except(['index', 'show']);
        $this->middleware('role:admin|restaurant_manager')->except(['index', 'show']);
    }

    /**
     * Display a listing of food addons
     */
    public function index(Request $request): JsonResponse
    {
        $query = FoodAddon::query();

        // Apply filters
        if ($request->filled('category')) {
            $query->where('category', $request->category);
        }

        if ($request->filled('is_active')) {
            $query->where('is_active', $request->boolean('is_active'));
        }

        if ($request->filled('search')) {
            $query->where(function ($q) use ($request) {
                $q->where('name', 'like', "%{$request->search}%")
                  ->orWhere('description', 'like', "%{$request->search}%");
            });
        }

        // Apply sorting
        $sortField = $request->get('sort', 'sort_order');
        $sortDirection = $request->get('direction', 'asc');
        $query->orderBy($sortField, $sortDirection);

        $addons = $query->paginate($request->get('per_page', 15));

        return response()->json([
            'success' => true,
            'data' => $addons,
        ]);
    }

    /**
     * Store a newly created food addon
     */
    public function store(Request $request): JsonResponse
    {
        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'description' => 'nullable|string|max:1000',
            'category' => 'required|string|max:100',
            'price' => 'required|numeric|min:0',
            'image' => 'nullable|image|max:2048',
            'is_vegetarian' => 'boolean',
            'is_vegan' => 'boolean',
            'is_gluten_free' => 'boolean',
            'allergens' => 'nullable|array',
            'allergens.*' => 'string|max:100',
            'is_active' => 'boolean',
            'sort_order' => 'integer|min:0',
        ]);

        // Handle image upload
        if ($request->hasFile('image')) {
            $validated['image'] = $request->file('image')->store('addons', 'public');
        }

        $addon = FoodAddon::create($validated);

        return response()->json([
            'success' => true,
            'message' => 'Food addon created successfully.',
            'data' => $addon,
        ], 201);
    }

    /**
     * Display the specified food addon
     */
    public function show(FoodAddon $foodAddon): JsonResponse
    {
        return response()->json([
            'success' => true,
            'data' => $foodAddon,
        ]);
    }

    /**
     * Update the specified food addon
     */
    public function update(Request $request, FoodAddon $foodAddon): JsonResponse
    {
        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'description' => 'nullable|string|max:1000',
            'category' => 'required|string|max:100',
            'price' => 'required|numeric|min:0',
            'image' => 'nullable|image|max:2048',
            'is_vegetarian' => 'boolean',
            'is_vegan' => 'boolean',
            'is_gluten_free' => 'boolean',
            'allergens' => 'nullable|array',
            'allergens.*' => 'string|max:100',
            'is_active' => 'boolean',
            'sort_order' => 'integer|min:0',
        ]);

        // Handle image upload
        if ($request->hasFile('image')) {
            // Delete old image if exists
            if ($foodAddon->image) {
                \Storage::disk('public')->delete($foodAddon->image);
            }
            $validated['image'] = $request->file('image')->store('addons', 'public');
        }

        $foodAddon->update($validated);

        return response()->json([
            'success' => true,
            'message' => 'Food addon updated successfully.',
            'data' => $foodAddon,
        ]);
    }

    /**
     * Remove the specified food addon
     */
    public function destroy(FoodAddon $foodAddon): JsonResponse
    {
        // Delete image if exists
        if ($foodAddon->image) {
            \Storage::disk('public')->delete($foodAddon->image);
        }

        $foodAddon->delete();

        return response()->json([
            'success' => true,
            'message' => 'Food addon deleted successfully.',
        ]);
    }

    /**
     * Toggle addon status
     */
    public function toggleStatus(FoodAddon $foodAddon): JsonResponse
    {
        $foodAddon->update(['is_active' => !$foodAddon->is_active]);

        $status = $foodAddon->is_active ? 'activated' : 'deactivated';

        return response()->json([
            'success' => true,
            'message' => "Food addon {$status} successfully.",
            'data' => $foodAddon,
        ]);
    }

    /**
     * Get addons by category
     */
    public function getByCategory(Request $request): JsonResponse
    {
        $request->validate([
            'category' => 'required|string|max:100',
        ]);

        $addons = FoodAddon::where('category', $request->category)
                          ->where('is_active', true)
                          ->orderBy('sort_order')
                          ->get();

        return response()->json([
            'success' => true,
            'data' => $addons,
        ]);
    }

    /**
     * Get addon categories
     */
    public function getCategories(): JsonResponse
    {
        $categories = FoodAddon::select('category')
                              ->distinct()
                              ->where('is_active', true)
                              ->orderBy('category')
                              ->pluck('category');

        return response()->json([
            'success' => true,
            'data' => $categories,
        ]);
    }

    /**
     * Bulk update addons
     */
    public function bulkUpdate(Request $request): JsonResponse
    {
        $validated = $request->validate([
            'action' => 'required|in:activate,deactivate,delete,update_category',
            'addon_ids' => 'required|array|min:1',
            'addon_ids.*' => 'exists:food_addons,id',
            'category' => 'required_if:action,update_category|string|max:100',
        ]);

        $addons = FoodAddon::whereIn('id', $validated['addon_ids']);

        switch ($validated['action']) {
            case 'activate':
                $addons->update(['is_active' => true]);
                $message = 'Selected addons activated successfully.';
                break;

            case 'deactivate':
                $addons->update(['is_active' => false]);
                $message = 'Selected addons deactivated successfully.';
                break;

            case 'delete':
                // Delete images
                $addonsToDelete = $addons->get();
                foreach ($addonsToDelete as $addon) {
                    if ($addon->image) {
                        \Storage::disk('public')->delete($addon->image);
                    }
                }
                $addons->delete();
                $message = 'Selected addons deleted successfully.';
                break;

            case 'update_category':
                $addons->update(['category' => $validated['category']]);
                $message = 'Selected addons category updated successfully.';
                break;
        }

        return response()->json([
            'success' => true,
            'message' => $message,
        ]);
    }

    /**
     * Update addon order
     */
    public function updateOrder(Request $request): JsonResponse
    {
        $validated = $request->validate([
            'addons' => 'required|array',
            'addons.*.id' => 'required|exists:food_addons,id',
            'addons.*.sort_order' => 'required|integer|min:0',
        ]);

        foreach ($validated['addons'] as $addonData) {
            FoodAddon::where('id', $addonData['id'])
                    ->update(['sort_order' => $addonData['sort_order']]);
        }

        return response()->json([
            'success' => true,
            'message' => 'Addon order updated successfully.',
        ]);
    }

    /**
     * Search addons
     */
    public function search(Request $request): JsonResponse
    {
        $request->validate([
            'query' => 'required|string|min:2|max:100',
        ]);

        $addons = FoodAddon::where('name', 'like', "%{$request->query}%")
                          ->orWhere('description', 'like', "%{$request->query}%")
                          ->where('is_active', true)
                          ->orderBy('name')
                          ->limit(20)
                          ->get();

        return response()->json([
            'success' => true,
            'data' => $addons,
        ]);
    }

    /**
     * Get addon statistics
     */
    public function getStatistics(): JsonResponse
    {
        $stats = [
            'total_addons' => FoodAddon::count(),
            'active_addons' => FoodAddon::where('is_active', true)->count(),
            'categories_count' => FoodAddon::distinct('category')->count(),
            'average_price' => FoodAddon::where('is_active', true)->avg('price'),
            'vegetarian_count' => FoodAddon::where('is_vegetarian', true)->where('is_active', true)->count(),
            'vegan_count' => FoodAddon::where('is_vegan', true)->where('is_active', true)->count(),
            'gluten_free_count' => FoodAddon::where('is_gluten_free', true)->where('is_active', true)->count(),
        ];

        return response()->json([
            'success' => true,
            'data' => $stats,
        ]);
    }
}
