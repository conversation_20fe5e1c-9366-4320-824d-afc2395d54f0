<?php

namespace App\Http\Controllers\Api\Tenant;

use App\Http\Controllers\Controller;
use App\Models\TableReservation;
use App\Models\Table;
use App\Models\Customer;
use Illuminate\Http\Request;
use Carbon\Carbon;
use Illuminate\Support\Facades\Auth;

class TableReservationController extends Controller
{
    public function __construct()
    {
        $this->middleware(['auth:sanctum']);
    }

    /**
     * Display a listing of table reservations
     */
    public function index(Request $request)
    {
        $query = TableReservation::with(['table', 'customer']);

        // Filter by date range
        if ($request->filled('date_from')) {
            $query->whereDate('reservation_date', '>=', $request->date_from);
        }
        if ($request->filled('date_to')) {
            $query->whereDate('reservation_date', '<=', $request->date_to);
        } else {
            // Default to next 30 days
            $query->whereDate('reservation_date', '>=', today())
                  ->whereDate('reservation_date', '<=', today()->addDays(30));
        }

        // Filter by status
        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }

        // Filter by table
        if ($request->filled('table_id')) {
            $query->where('table_id', $request->table_id);
        }

        // Filter by customer (admin/manager view)
        if ($request->filled('customer_id') && $this->canViewAllReservations()) {
            $query->where('customer_id', $request->customer_id);
        }

        // If customer is authenticated, show only their reservations
        if (!$this->canViewAllReservations()) {
            $customerId = Auth::guard('customer')->id();
            if (!$customerId) {
                return response()->json([
                    'success' => false,
                    'message' => 'Customer not authenticated.',
                ], 401);
            }
            $query->where('customer_id', $customerId);
        }

        // Search by customer name or phone
        if ($request->filled('search')) {
            $query->whereHas('customer', function ($q) use ($request) {
                $q->where('name', 'like', '%' . $request->search . '%')
                  ->orWhere('phone', 'like', '%' . $request->search . '%')
                  ->orWhere('email', 'like', '%' . $request->search . '%');
            });
        }

        $reservations = $query->orderBy('reservation_date')
                             ->orderBy('reservation_time')
                             ->paginate($request->get('per_page', 15));

        return response()->json([
            'success' => true,
            'data' => $reservations,
            'meta' => [
                'stats' => $this->getReservationStats($request),
            ]
        ]);
    }

    /**
     * Store a newly created table reservation
     */
    public function store(Request $request)
    {
        $request->validate([
            'table_id' => 'required|exists:tables,id',
            'customer_id' => 'nullable|exists:customers,id',
            'guest_name' => 'required_without:customer_id|string|max:255',
            'guest_phone' => 'required_without:customer_id|string|max:20',
            'guest_email' => 'nullable|email|max:255',
            'reservation_date' => 'required|date|after_or_equal:today',
            'reservation_time' => 'required|date_format:H:i',
            'party_size' => 'required|integer|min:1|max:20',
            'duration_minutes' => 'nullable|integer|min:30|max:480',
            'special_requests' => 'nullable|string|max:1000',
            'occasion' => 'nullable|string|max:100',
        ]);

        // Check if customer is authenticated
        $customerId = $request->customer_id;
        if (!$customerId && !$this->canCreateForOthers()) {
            $customerId = Auth::guard('customer')->id();
            if (!$customerId) {
                return response()->json([
                    'success' => false,
                    'message' => 'Customer authentication required.',
                ], 401);
            }
        }

        // Validate table availability
        $table = Table::findOrFail($request->table_id);
        if (!$table->is_active) {
            return response()->json([
                'success' => false,
                'message' => 'Selected table is not available.',
            ], 422);
        }

        // Check table capacity
        if ($request->party_size > $table->capacity) {
            return response()->json([
                'success' => false,
                'message' => "Table capacity is {$table->capacity}. Party size exceeds capacity.",
            ], 422);
        }

        // Check if table is available at requested time
        $reservationDateTime = Carbon::parse($request->reservation_date . ' ' . $request->reservation_time);
        $duration = $request->duration_minutes ?? 120; // Default 2 hours

        if (!$this->isTableAvailable($table->id, $reservationDateTime, $duration)) {
            return response()->json([
                'success' => false,
                'message' => 'Table is not available at the requested time.',
                'suggestions' => $this->generateAvailableTimeSlots($table->id, $request->reservation_date),
            ], 422);
        }

        // Check restaurant operating hours
        if (!$this->isWithinOperatingHours($reservationDateTime)) {
            return response()->json([
                'success' => false,
                'message' => 'Reservation time is outside restaurant operating hours.',
            ], 422);
        }

        $data = [
            'table_id' => $request->table_id,
            'customer_id' => $customerId,
            'guest_name' => $request->guest_name,
            'guest_phone' => $request->guest_phone,
            'guest_email' => $request->guest_email,
            'reservation_date' => $request->reservation_date,
            'reservation_time' => $request->reservation_time,
            'party_size' => $request->party_size,
            'duration_minutes' => $duration,
            'special_requests' => $request->special_requests,
            'occasion' => $request->occasion,
            'status' => 'confirmed',
            'confirmation_code' => $this->generateConfirmationCode(),
        ];

        $reservation = TableReservation::create($data);

        // Send confirmation notification (implement as needed)
        // $this->sendConfirmationNotification($reservation);

        return response()->json([
            'success' => true,
            'message' => 'Reservation created successfully.',
            'data' => $reservation->load(['table', 'customer']),
        ], 201);
    }

    /**
     * Display the specified table reservation
     */
    public function show(TableReservation $tableReservation)
    {
        // Check if user can view this reservation
        if (!$this->canViewReservation($tableReservation)) {
            return response()->json([
                'success' => false,
                'message' => 'Unauthorized to view this reservation.',
            ], 403);
        }

        $tableReservation->load(['table', 'customer']);

        return response()->json([
            'success' => true,
            'data' => $tableReservation,
        ]);
    }

    /**
     * Update the specified table reservation
     */
    public function update(Request $request, TableReservation $tableReservation)
    {
        // Check if user can update this reservation
        if (!$this->canUpdateReservation($tableReservation)) {
            return response()->json([
                'success' => false,
                'message' => 'Unauthorized to update this reservation.',
            ], 403);
        }

        // Check if reservation can be modified
        if (!$this->canModifyReservation($tableReservation)) {
            return response()->json([
                'success' => false,
                'message' => 'Reservation cannot be modified at this time.',
            ], 422);
        }

        $request->validate([
            'table_id' => 'sometimes|exists:tables,id',
            'reservation_date' => 'sometimes|date|after_or_equal:today',
            'reservation_time' => 'sometimes|date_format:H:i',
            'party_size' => 'sometimes|integer|min:1|max:20',
            'duration_minutes' => 'nullable|integer|min:30|max:480',
            'special_requests' => 'nullable|string|max:1000',
            'occasion' => 'nullable|string|max:100',
            'status' => 'sometimes|in:confirmed,cancelled,completed,no_show',
        ]);

        $data = $request->only([
            'table_id', 'reservation_date', 'reservation_time', 'party_size',
            'duration_minutes', 'special_requests', 'occasion', 'status'
        ]);

        // If changing table, date, or time, check availability
        if ($request->has(['table_id', 'reservation_date', 'reservation_time'])) {
            $tableId = $request->table_id ?? $tableReservation->table_id;
            $date = $request->reservation_date ?? $tableReservation->reservation_date;
            $time = $request->reservation_time ?? $tableReservation->reservation_time;
            $duration = $request->duration_minutes ?? $tableReservation->duration_minutes;

            $reservationDateTime = Carbon::parse($date . ' ' . $time);

            if (!$this->isTableAvailable($tableId, $reservationDateTime, $duration, $tableReservation->id)) {
                return response()->json([
                    'success' => false,
                    'message' => 'Table is not available at the requested time.',
                ], 422);
            }
        }

        $tableReservation->update($data);

        return response()->json([
            'success' => true,
            'message' => 'Reservation updated successfully.',
            'data' => $tableReservation->fresh()->load(['table', 'customer']),
        ]);
    }

    /**
     * Cancel the specified table reservation
     */
    public function cancel(TableReservation $tableReservation, Request $request)
    {
        // Check if user can cancel this reservation
        if (!$this->canUpdateReservation($tableReservation)) {
            return response()->json([
                'success' => false,
                'message' => 'Unauthorized to cancel this reservation.',
            ], 403);
        }

        // Check if reservation can be cancelled
        if (!$this->canCancelReservation($tableReservation)) {
            return response()->json([
                'success' => false,
                'message' => 'Reservation cannot be cancelled at this time.',
            ], 422);
        }

        $request->validate([
            'cancellation_reason' => 'nullable|string|max:500',
        ]);

        $tableReservation->update([
            'status' => 'cancelled',
            'cancellation_reason' => $request->cancellation_reason,
            'cancelled_at' => now(),
            'cancelled_by' => Auth::id() ?? Auth::guard('customer')->id(),
        ]);

        return response()->json([
            'success' => true,
            'message' => 'Reservation cancelled successfully.',
            'data' => $tableReservation->fresh(),
        ]);
    }

    /**
     * Get available tables for a specific date and time
     */
    public function getAvailableTables(Request $request)
    {
        $request->validate([
            'reservation_date' => 'required|date|after_or_equal:today',
            'reservation_time' => 'required|date_format:H:i',
            'party_size' => 'required|integer|min:1|max:20',
            'duration_minutes' => 'nullable|integer|min:30|max:480',
        ]);

        $reservationDateTime = Carbon::parse($request->reservation_date . ' ' . $request->reservation_time);
        $duration = $request->duration_minutes ?? 120;

        // Check if within operating hours
        if (!$this->isWithinOperatingHours($reservationDateTime)) {
            return response()->json([
                'success' => false,
                'message' => 'Requested time is outside restaurant operating hours.',
                'data' => [],
            ]);
        }

        $availableTables = Table::where('is_active', true)
                               ->where('capacity', '>=', $request->party_size)
                               ->get()
                               ->filter(function ($table) use ($reservationDateTime, $duration) {
                                   return $this->isTableAvailable($table->id, $reservationDateTime, $duration);
                               })
                               ->values();

        return response()->json([
            'success' => true,
            'data' => $availableTables,
            'meta' => [
                'requested_date' => $request->reservation_date,
                'requested_time' => $request->reservation_time,
                'party_size' => $request->party_size,
                'available_count' => $availableTables->count(),
            ]
        ]);
    }

    /**
     * Get available time slots for a specific date and table
     */
    public function getAvailableTimeSlots(Request $request)
    {
        $request->validate([
            'table_id' => 'required|exists:tables,id',
            'reservation_date' => 'required|date|after_or_equal:today',
            'duration_minutes' => 'nullable|integer|min:30|max:480',
        ]);

        $timeSlots = $this->generateAvailableTimeSlots($request->table_id, $request->reservation_date, $request->duration_minutes);

        return response()->json([
            'success' => true,
            'data' => $timeSlots,
        ]);
    }

    /**
     * Check if table is available at specific time
     */
    private function isTableAvailable($tableId, $reservationDateTime, $duration, $excludeReservationId = null)
    {
        $endTime = $reservationDateTime->copy()->addMinutes($duration);

        $conflictingReservations = TableReservation::where('table_id', $tableId)
            ->where('status', '!=', 'cancelled')
            ->whereDate('reservation_date', $reservationDateTime->toDateString())
            ->where(function ($query) use ($reservationDateTime, $endTime) {
                $query->where(function ($q) use ($reservationDateTime, $endTime) {
                    // Existing reservation starts before new ends and ends after new starts
                    $q->whereRaw("TIME(reservation_time) < ?", [$endTime->format('H:i:s')])
                      ->whereRaw("TIME(DATE_ADD(CONCAT(reservation_date, ' ', reservation_time), INTERVAL duration_minutes MINUTE)) > ?", [$reservationDateTime->format('H:i:s')]);
                });
            });

        if ($excludeReservationId) {
            $conflictingReservations->where('id', '!=', $excludeReservationId);
        }

        return !$conflictingReservations->exists();
    }

    /**
     * Check if time is within restaurant operating hours
     */
    private function isWithinOperatingHours($reservationDateTime)
    {
        $tenant = tenant();
        $openingHours = $tenant->opening_hours ?? [];

        $dayName = strtolower($reservationDateTime->format('l'));
        $dayHours = $openingHours[$dayName] ?? null;

        if (!$dayHours || !$dayHours['is_open']) {
            return false;
        }

        $openTime = Carbon::parse($dayHours['open_time']);
        $closeTime = Carbon::parse($dayHours['close_time']);
        $requestTime = Carbon::parse($reservationDateTime->format('H:i'));

        return $requestTime->between($openTime, $closeTime);
    }

    /**
     * Generate confirmation code
     */
    private function generateConfirmationCode()
    {
        return 'RES-' . strtoupper(substr(md5(uniqid()), 0, 8));
    }

    /**
     * Generate available time slots for a table on a specific date
     */
    private function generateAvailableTimeSlots($tableId, $date, $duration = 120)
    {
        $tenant = tenant();
        $openingHours = $tenant->opening_hours ?? [];

        $dayName = strtolower(Carbon::parse($date)->format('l'));
        $dayHours = $openingHours[$dayName] ?? null;

        if (!$dayHours || !$dayHours['is_open']) {
            return [];
        }

        $openTime = Carbon::parse($date . ' ' . $dayHours['open_time']);
        $closeTime = Carbon::parse($date . ' ' . $dayHours['close_time']);

        $timeSlots = [];
        $current = $openTime->copy();

        while ($current->addMinutes(30)->lte($closeTime->subMinutes($duration))) {
            if ($this->isTableAvailable($tableId, $current, $duration)) {
                $timeSlots[] = [
                    'time' => $current->format('H:i'),
                    'display_time' => $current->format('g:i A'),
                    'available' => true,
                ];
            }
        }

        return $timeSlots;
    }

    /**
     * Check if user can view all reservations
     */
    private function canViewAllReservations()
    {
        return Auth::user() && Auth::user()->hasAnyRole(['admin', 'restaurant_manager', 'waiter']);
    }

    /**
     * Check if user can create reservations for others
     */
    private function canCreateForOthers()
    {
        return Auth::user() && Auth::user()->hasAnyRole(['admin', 'restaurant_manager', 'waiter']);
    }

    /**
     * Check if user can view specific reservation
     */
    private function canViewReservation($reservation)
    {
        if ($this->canViewAllReservations()) {
            return true;
        }

        $customerId = Auth::guard('customer')->id();
        return $customerId && $reservation->customer_id === $customerId;
    }

    /**
     * Check if user can update specific reservation
     */
    private function canUpdateReservation($reservation)
    {
        if (Auth::user() && Auth::user()->hasAnyRole(['admin', 'restaurant_manager'])) {
            return true;
        }

        $customerId = Auth::guard('customer')->id();
        return $customerId && $reservation->customer_id === $customerId;
    }

    /**
     * Check if reservation can be modified
     */
    private function canModifyReservation($reservation)
    {
        // Can't modify cancelled or completed reservations
        if (in_array($reservation->status, ['cancelled', 'completed', 'no_show'])) {
            return false;
        }

        // Can't modify reservations less than 2 hours before the time
        $reservationDateTime = Carbon::parse($reservation->reservation_date . ' ' . $reservation->reservation_time);
        return now()->addHours(2)->lt($reservationDateTime);
    }

    /**
     * Check if reservation can be cancelled
     */
    private function canCancelReservation($reservation)
    {
        // Can't cancel already cancelled, completed, or no-show reservations
        if (in_array($reservation->status, ['cancelled', 'completed', 'no_show'])) {
            return false;
        }

        // Can cancel up to 1 hour before the reservation
        $reservationDateTime = Carbon::parse($reservation->reservation_date . ' ' . $reservation->reservation_time);
        return now()->addHour()->lt($reservationDateTime);
    }

    /**
     * Get reservation statistics
     */
    private function getReservationStats($request)
    {
        $query = TableReservation::query();

        if ($request->filled('date_from')) {
            $query->whereDate('reservation_date', '>=', $request->date_from);
        }
        if ($request->filled('date_to')) {
            $query->whereDate('reservation_date', '<=', $request->date_to);
        }

        return [
            'total' => $query->count(),
            'confirmed' => $query->where('status', 'confirmed')->count(),
            'cancelled' => $query->where('status', 'cancelled')->count(),
            'completed' => $query->where('status', 'completed')->count(),
            'no_show' => $query->where('status', 'no_show')->count(),
        ];
    }
}
