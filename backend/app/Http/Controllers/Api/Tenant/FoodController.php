<?php

namespace App\Http\Controllers\Api\Tenant;

use App\Http\Controllers\Controller;
use App\Http\Requests\Tenant\StoreFoodRequest;
use App\Http\Requests\Tenant\UpdateFoodRequest;
use App\Http\Resources\Tenant\FoodResource;
use App\Http\Resources\Tenant\FoodCollection;
use App\Models\Tenant\Food;
use App\Models\Tenant\FoodCategory;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;

class FoodController extends Controller
{
    /**
     * Display a listing of foods
     */
    public function index(Request $request): FoodCollection
    {
        $query = Food::with(['category', 'variants', 'addons', 'reviews'])
            ->active();

        // Filter by category
        if ($request->filled('category_id')) {
            $query->where('food_category_id', $request->category_id);
        }

        // Filter by availability
        if ($request->filled('available_only')) {
            $query->available();
        }

        // Filter by dietary preferences
        if ($request->filled('vegetarian')) {
            $query->vegetarian();
        }

        if ($request->filled('vegan')) {
            $query->vegan();
        }

        if ($request->filled('gluten_free')) {
            $query->where('is_gluten_free', true);
        }

        // Filter by price range
        if ($request->filled('min_price')) {
            $query->where('base_price', '>=', $request->min_price);
        }

        if ($request->filled('max_price')) {
            $query->where('base_price', '<=', $request->max_price);
        }

        // Search by name or description
        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('description', 'like', "%{$search}%")
                  ->orWhere('ingredients', 'like', "%{$search}%");
            });
        }

        // Filter by features
        if ($request->filled('featured')) {
            $query->featured();
        }

        if ($request->filled('popular')) {
            $query->popular();
        }

        if ($request->filled('new')) {
            $query->new();
        }

        // Sort options
        switch ($request->get('sort', 'name')) {
            case 'price_low':
                $query->orderBy('base_price', 'asc');
                break;
            case 'price_high':
                $query->orderBy('base_price', 'desc');
                break;
            case 'rating':
                $query->orderBy('average_rating', 'desc');
                break;
            case 'popular':
                $query->orderBy('order_count', 'desc');
                break;
            default:
                $query->ordered();
                break;
        }

        $foods = $query->paginate($request->get('per_page', 12));

        return new FoodCollection($foods);
    }

    /**
     * Store a newly created food
     */
    public function store(StoreFoodRequest $request): JsonResponse
    {
        $food = Food::create($request->validated());

        // Handle image uploads
        if ($request->hasFile('images')) {
            $images = [];
            foreach ($request->file('images') as $image) {
                $path = $image->store('foods', 'public');
                $images[] = $path;
            }
            $food->update(['images' => $images]);
        }

        return response()->json([
            'message' => 'Food item created successfully',
            'data' => new FoodResource($food->load(['category', 'variants', 'addons']))
        ], 201);
    }

    /**
     * Display the specified food
     */
    public function show(Food $food): FoodResource
    {
        // Increment view count
        $food->incrementViewCount();

        $food->load([
            'category',
            'variants' => function ($query) {
                $query->available()->ordered();
            },
            'addons' => function ($query) {
                $query->available()->ordered();
            },
            'reviews' => function ($query) {
                $query->approved()->with('customer')->latest()->limit(10);
            }
        ]);

        return new FoodResource($food);
    }

    /**
     * Update the specified food
     */
    public function update(UpdateFoodRequest $request, Food $food): JsonResponse
    {
        $food->update($request->validated());

        // Handle image uploads
        if ($request->hasFile('images')) {
            $images = $food->images ?? [];
            foreach ($request->file('images') as $image) {
                $path = $image->store('foods', 'public');
                $images[] = $path;
            }
            $food->update(['images' => $images]);
        }

        return response()->json([
            'message' => 'Food item updated successfully',
            'data' => new FoodResource($food->fresh(['category', 'variants', 'addons']))
        ]);
    }

    /**
     * Remove the specified food
     */
    public function destroy(Food $food): JsonResponse
    {
        $food->update(['is_active' => false]);

        return response()->json([
            'message' => 'Food item deactivated successfully'
        ]);
    }

    /**
     * Get foods by category
     */
    public function byCategory(FoodCategory $category, Request $request): FoodCollection
    {
        $query = $category->foods()
            ->with(['variants', 'addons'])
            ->active()
            ->available();

        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('description', 'like', "%{$search}%");
            });
        }

        $foods = $query->ordered()->paginate($request->get('per_page', 12));

        return new FoodCollection($foods);
    }

    /**
     * Get featured foods
     */
    public function featured(): JsonResponse
    {
        $foods = Food::with(['category', 'variants'])
            ->active()
            ->available()
            ->featured()
            ->ordered()
            ->limit(8)
            ->get();

        return response()->json([
            'data' => FoodResource::collection($foods)
        ]);
    }

    /**
     * Get popular foods
     */
    public function popular(): JsonResponse
    {
        $foods = Food::with(['category', 'variants'])
            ->active()
            ->available()
            ->popular()
            ->orderBy('order_count', 'desc')
            ->limit(8)
            ->get();

        return response()->json([
            'data' => FoodResource::collection($foods)
        ]);
    }

    /**
     * Get new foods
     */
    public function new(): JsonResponse
    {
        $foods = Food::with(['category', 'variants'])
            ->active()
            ->available()
            ->new()
            ->latest()
            ->limit(8)
            ->get();

        return response()->json([
            'data' => FoodResource::collection($foods)
        ]);
    }

    /**
     * Toggle food availability
     */
    public function toggleAvailability(Food $food): JsonResponse
    {
        $food->update(['is_available' => !$food->is_available]);

        return response()->json([
            'message' => 'Food availability updated successfully',
            'data' => new FoodResource($food)
        ]);
    }

    /**
     * Bulk update food availability
     */
    public function bulkUpdateAvailability(Request $request): JsonResponse
    {
        $request->validate([
            'food_ids' => 'required|array',
            'food_ids.*' => 'exists:foods,id',
            'is_available' => 'required|boolean'
        ]);

        Food::whereIn('id', $request->food_ids)
            ->update(['is_available' => $request->is_available]);

        return response()->json([
            'message' => 'Food availability updated successfully'
        ]);
    }
}
