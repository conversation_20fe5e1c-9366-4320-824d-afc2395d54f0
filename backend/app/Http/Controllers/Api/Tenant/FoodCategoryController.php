<?php

namespace App\Http\Controllers\Api\Tenant;

use App\Http\Controllers\Controller;
use App\Models\FoodCategory;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;

class FoodCategoryController extends Controller
{
    public function __construct()
    {
        $this->middleware(['role:admin|restaurant_manager'])->except(['index', 'show']);
    }

    /**
     * Display a listing of food categories
     */
    public function index(Request $request)
    {
        $query = FoodCategory::query();

        // Search functionality
        if ($request->filled('search')) {
            $query->where('name', 'like', '%' . $request->search . '%')
                  ->orWhere('description', 'like', '%' . $request->search . '%');
        }

        // Filter by status
        if ($request->filled('status')) {
            $query->where('is_active', $request->status === 'active');
        }

        // Filter by parent category
        if ($request->filled('parent_id')) {
            $query->where('parent_id', $request->parent_id);
        }

        // Include subcategories count
        if ($request->boolean('include_counts')) {
            $query->withCount(['subcategories', 'menuItems']);
        }

        // Include menu items
        if ($request->boolean('include_menu_items')) {
            $query->with(['menuItems' => function ($q) {
                $q->where('is_active', true)->orderBy('sort_order');
            }]);
        }

        $categories = $query->orderBy('sort_order')
                           ->orderBy('name')
                           ->get();

        return response()->json([
            'success' => true,
            'data' => $categories,
            'meta' => [
                'total' => $categories->count(),
                'active' => $categories->where('is_active', true)->count(),
                'inactive' => $categories->where('is_active', false)->count(),
            ]
        ]);
    }

    /**
     * Store a newly created food category
     */
    public function store(Request $request)
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'description' => 'nullable|string|max:1000',
            'parent_id' => 'nullable|exists:food_categories,id',
            'image' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
            'icon' => 'nullable|string|max:100',
            'color' => 'nullable|string|regex:/^#[a-fA-F0-9]{6}$/',
            'is_active' => 'boolean',
            'is_featured' => 'boolean',
            'sort_order' => 'nullable|integer|min:0',
        ]);

        $data = $request->only([
            'name', 'description', 'parent_id', 'icon', 'color', 
            'is_active', 'is_featured'
        ]);

        $data['slug'] = Str::slug($request->name);
        $data['sort_order'] = $request->sort_order ?? FoodCategory::max('sort_order') + 1;

        // Handle image upload
        if ($request->hasFile('image')) {
            $data['image'] = $request->file('image')->store('categories', 'public');
        }

        $category = FoodCategory::create($data);

        return response()->json([
            'success' => true,
            'message' => 'Food category created successfully.',
            'data' => $category->load('parent'),
        ], 201);
    }

    /**
     * Display the specified food category
     */
    public function show(FoodCategory $foodCategory, Request $request)
    {
        $category = $foodCategory;

        // Include relationships based on request
        $with = [];
        if ($request->boolean('include_parent')) {
            $with[] = 'parent';
        }
        if ($request->boolean('include_subcategories')) {
            $with[] = 'subcategories';
        }
        if ($request->boolean('include_menu_items')) {
            $with[] = 'menuItems';
        }

        if (!empty($with)) {
            $category->load($with);
        }

        return response()->json([
            'success' => true,
            'data' => $category,
        ]);
    }

    /**
     * Update the specified food category
     */
    public function update(Request $request, FoodCategory $foodCategory)
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'description' => 'nullable|string|max:1000',
            'parent_id' => 'nullable|exists:food_categories,id',
            'image' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
            'icon' => 'nullable|string|max:100',
            'color' => 'nullable|string|regex:/^#[a-fA-F0-9]{6}$/',
            'is_active' => 'boolean',
            'is_featured' => 'boolean',
            'sort_order' => 'nullable|integer|min:0',
        ]);

        // Prevent circular parent relationship
        if ($request->parent_id && $this->wouldCreateCircularReference($foodCategory, $request->parent_id)) {
            return response()->json([
                'success' => false,
                'message' => 'Cannot set parent category as it would create a circular reference.',
            ], 422);
        }

        $data = $request->only([
            'name', 'description', 'parent_id', 'icon', 'color', 
            'is_active', 'is_featured', 'sort_order'
        ]);

        $data['slug'] = Str::slug($request->name);

        // Handle image upload
        if ($request->hasFile('image')) {
            // Delete old image
            if ($foodCategory->image) {
                Storage::disk('public')->delete($foodCategory->image);
            }
            $data['image'] = $request->file('image')->store('categories', 'public');
        }

        $foodCategory->update($data);

        return response()->json([
            'success' => true,
            'message' => 'Food category updated successfully.',
            'data' => $foodCategory->fresh()->load('parent'),
        ]);
    }

    /**
     * Remove the specified food category
     */
    public function destroy(FoodCategory $foodCategory)
    {
        // Check if category has menu items
        if ($foodCategory->menuItems()->exists()) {
            return response()->json([
                'success' => false,
                'message' => 'Cannot delete category with menu items. Please move or delete menu items first.',
            ], 422);
        }

        // Check if category has subcategories
        if ($foodCategory->subcategories()->exists()) {
            return response()->json([
                'success' => false,
                'message' => 'Cannot delete category with subcategories. Please delete subcategories first.',
            ], 422);
        }

        // Delete image if exists
        if ($foodCategory->image) {
            Storage::disk('public')->delete($foodCategory->image);
        }

        $foodCategory->delete();

        return response()->json([
            'success' => true,
            'message' => 'Food category deleted successfully.',
        ]);
    }

    /**
     * Toggle category status
     */
    public function toggleStatus(FoodCategory $foodCategory)
    {
        $foodCategory->update(['is_active' => !$foodCategory->is_active]);

        $status = $foodCategory->is_active ? 'activated' : 'deactivated';

        return response()->json([
            'success' => true,
            'message' => "Category {$status} successfully.",
            'data' => ['is_active' => $foodCategory->is_active],
        ]);
    }

    /**
     * Update categories sort order
     */
    public function updateOrder(Request $request)
    {
        $request->validate([
            'categories' => 'required|array',
            'categories.*.id' => 'required|exists:food_categories,id',
            'categories.*.sort_order' => 'required|integer|min:0',
        ]);

        foreach ($request->categories as $categoryData) {
            FoodCategory::where('id', $categoryData['id'])
                        ->update(['sort_order' => $categoryData['sort_order']]);
        }

        return response()->json([
            'success' => true,
            'message' => 'Category order updated successfully.',
        ]);
    }

    /**
     * Get category tree structure
     */
    public function tree(Request $request)
    {
        $categories = FoodCategory::with('subcategories.subcategories')
                                 ->whereNull('parent_id')
                                 ->orderBy('sort_order')
                                 ->get();

        if ($request->boolean('include_counts')) {
            $categories->load('menuItems');
        }

        return response()->json([
            'success' => true,
            'data' => $categories,
        ]);
    }

    /**
     * Get featured categories
     */
    public function featured()
    {
        $categories = FoodCategory::where('is_featured', true)
                                 ->where('is_active', true)
                                 ->with(['menuItems' => function ($query) {
                                     $query->where('is_active', true)
                                           ->where('is_featured', true)
                                           ->limit(6);
                                 }])
                                 ->orderBy('sort_order')
                                 ->get();

        return response()->json([
            'success' => true,
            'data' => $categories,
        ]);
    }

    /**
     * Search categories
     */
    public function search(Request $request)
    {
        $request->validate([
            'query' => 'required|string|min:2|max:100',
            'limit' => 'nullable|integer|min:1|max:50',
        ]);

        $categories = FoodCategory::where('name', 'like', '%' . $request->query . '%')
                                 ->orWhere('description', 'like', '%' . $request->query . '%')
                                 ->where('is_active', true)
                                 ->limit($request->get('limit', 10))
                                 ->get();

        return response()->json([
            'success' => true,
            'data' => $categories,
            'meta' => [
                'query' => $request->query,
                'total_results' => $categories->count(),
            ]
        ]);
    }

    /**
     * Bulk update categories
     */
    public function bulkUpdate(Request $request)
    {
        $request->validate([
            'category_ids' => 'required|array',
            'category_ids.*' => 'exists:food_categories,id',
            'action' => 'required|in:activate,deactivate,delete,feature,unfeature',
        ]);

        $categories = FoodCategory::whereIn('id', $request->category_ids);

        switch ($request->action) {
            case 'activate':
                $categories->update(['is_active' => true]);
                $message = 'Categories activated successfully.';
                break;
            case 'deactivate':
                $categories->update(['is_active' => false]);
                $message = 'Categories deactivated successfully.';
                break;
            case 'feature':
                $categories->update(['is_featured' => true]);
                $message = 'Categories featured successfully.';
                break;
            case 'unfeature':
                $categories->update(['is_featured' => false]);
                $message = 'Categories unfeatured successfully.';
                break;
            case 'delete':
                // Check for dependencies
                $categoriesWithItems = $categories->whereHas('menuItems')->count();
                $categoriesWithSubcategories = $categories->whereHas('subcategories')->count();
                
                if ($categoriesWithItems > 0 || $categoriesWithSubcategories > 0) {
                    return response()->json([
                        'success' => false,
                        'message' => 'Some categories have menu items or subcategories and cannot be deleted.',
                    ], 422);
                }
                
                $categories->delete();
                $message = 'Categories deleted successfully.';
                break;
        }

        return response()->json([
            'success' => true,
            'message' => $message,
        ]);
    }

    /**
     * Check if setting parent would create circular reference
     */
    private function wouldCreateCircularReference(FoodCategory $category, $parentId)
    {
        $parent = FoodCategory::find($parentId);
        
        while ($parent) {
            if ($parent->id === $category->id) {
                return true;
            }
            $parent = $parent->parent;
        }
        
        return false;
    }

    /**
     * Get category statistics
     */
    public function statistics()
    {
        $stats = [
            'total_categories' => FoodCategory::count(),
            'active_categories' => FoodCategory::where('is_active', true)->count(),
            'featured_categories' => FoodCategory::where('is_featured', true)->count(),
            'categories_with_items' => FoodCategory::whereHas('menuItems')->count(),
            'empty_categories' => FoodCategory::whereDoesntHave('menuItems')->count(),
            'parent_categories' => FoodCategory::whereNull('parent_id')->count(),
            'subcategories' => FoodCategory::whereNotNull('parent_id')->count(),
        ];

        return response()->json([
            'success' => true,
            'data' => $stats,
        ]);
    }
}
