<?php

namespace App\Http\Controllers\Api\Tenant;

use App\Http\Controllers\Controller;
use App\Models\DeliveryOrder;
use App\Models\DeliveryDriver;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;

class DeliveryOrderController extends Controller
{
    public function __construct()
    {
        $this->middleware('auth:sanctum');
        $this->middleware('role:admin|restaurant_manager|delivery')->except(['track']);
    }

    /**
     * Display a listing of delivery orders
     */
    public function index(Request $request): JsonResponse
    {
        $query = DeliveryOrder::with(['order', 'driver', 'customer']);

        // Apply filters
        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }

        if ($request->filled('driver_id')) {
            $query->where('driver_id', $request->driver_id);
        }

        if ($request->filled('date_from')) {
            $query->whereDate('created_at', '>=', $request->date_from);
        }

        if ($request->filled('date_to')) {
            $query->whereDate('created_at', '<=', $request->date_to);
        }

        if ($request->filled('search')) {
            $query->whereHas('order', function ($q) use ($request) {
                $q->where('order_number', 'like', "%{$request->search}%");
            })->orWhereHas('customer', function ($q) use ($request) {
                $q->where('name', 'like', "%{$request->search}%")
                  ->orWhere('phone', 'like', "%{$request->search}%");
            });
        }

        // Apply sorting
        $sortField = $request->get('sort', 'created_at');
        $sortDirection = $request->get('direction', 'desc');
        $query->orderBy($sortField, $sortDirection);

        $deliveryOrders = $query->paginate($request->get('per_page', 15));

        return response()->json([
            'success' => true,
            'data' => $deliveryOrders,
        ]);
    }

    /**
     * Store a newly created delivery order
     */
    public function store(Request $request): JsonResponse
    {
        $validated = $request->validate([
            'order_id' => 'required|exists:orders,id',
            'customer_id' => 'required|exists:customers,id',
            'delivery_address' => 'required|string|max:500',
            'delivery_latitude' => 'required|numeric|between:-90,90',
            'delivery_longitude' => 'required|numeric|between:-180,180',
            'delivery_phone' => 'required|string|max:20',
            'delivery_instructions' => 'nullable|string|max:500',
            'delivery_fee' => 'required|numeric|min:0',
            'estimated_delivery_time' => 'required|integer|min:1',
            'driver_id' => 'nullable|exists:delivery_drivers,id',
        ]);

        $deliveryOrder = DeliveryOrder::create($validated);
        $deliveryOrder->load(['order', 'driver', 'customer']);

        return response()->json([
            'success' => true,
            'message' => 'Delivery order created successfully.',
            'data' => $deliveryOrder,
        ], 201);
    }

    /**
     * Display the specified delivery order
     */
    public function show(DeliveryOrder $deliveryOrder): JsonResponse
    {
        $deliveryOrder->load(['order.items', 'driver.user', 'customer']);

        return response()->json([
            'success' => true,
            'data' => $deliveryOrder,
        ]);
    }

    /**
     * Update the specified delivery order
     */
    public function update(Request $request, DeliveryOrder $deliveryOrder): JsonResponse
    {
        $validated = $request->validate([
            'delivery_address' => 'string|max:500',
            'delivery_latitude' => 'numeric|between:-90,90',
            'delivery_longitude' => 'numeric|between:-180,180',
            'delivery_phone' => 'string|max:20',
            'delivery_instructions' => 'nullable|string|max:500',
            'delivery_fee' => 'numeric|min:0',
            'estimated_delivery_time' => 'integer|min:1',
            'driver_id' => 'nullable|exists:delivery_drivers,id',
            'status' => 'in:pending,assigned,picked_up,in_transit,delivered,cancelled',
        ]);

        $deliveryOrder->update($validated);
        $deliveryOrder->load(['order', 'driver', 'customer']);

        return response()->json([
            'success' => true,
            'message' => 'Delivery order updated successfully.',
            'data' => $deliveryOrder,
        ]);
    }

    /**
     * Remove the specified delivery order
     */
    public function destroy(DeliveryOrder $deliveryOrder): JsonResponse
    {
        if ($deliveryOrder->status === 'in_transit') {
            return response()->json([
                'success' => false,
                'message' => 'Cannot delete delivery order that is in transit.',
            ], 422);
        }

        $deliveryOrder->delete();

        return response()->json([
            'success' => true,
            'message' => 'Delivery order deleted successfully.',
        ]);
    }

    /**
     * Assign driver to delivery order
     */
    public function assignDriver(Request $request, DeliveryOrder $deliveryOrder): JsonResponse
    {
        $validated = $request->validate([
            'driver_id' => 'required|exists:delivery_drivers,id',
        ]);

        $driver = DeliveryDriver::find($validated['driver_id']);

        // Check if driver is available
        if (!$driver->is_available || $driver->status !== 'active') {
            return response()->json([
                'success' => false,
                'message' => 'Driver is not available.',
            ], 422);
        }

        // Check if driver already has an active delivery
        if ($driver->hasActiveDeliveries()) {
            return response()->json([
                'success' => false,
                'message' => 'Driver already has an active delivery.',
            ], 422);
        }

        $deliveryOrder->update([
            'driver_id' => $validated['driver_id'],
            'status' => 'assigned',
            'assigned_at' => now(),
        ]);

        $driver->update(['current_delivery_id' => $deliveryOrder->id]);

        return response()->json([
            'success' => true,
            'message' => 'Driver assigned successfully.',
            'data' => $deliveryOrder->load('driver'),
        ]);
    }

    /**
     * Update delivery status
     */
    public function updateStatus(Request $request, DeliveryOrder $deliveryOrder): JsonResponse
    {
        $validated = $request->validate([
            'status' => 'required|in:assigned,picked_up,in_transit,delivered,cancelled',
            'notes' => 'nullable|string|max:500',
            'latitude' => 'nullable|numeric|between:-90,90',
            'longitude' => 'nullable|numeric|between:-180,180',
        ]);

        $oldStatus = $deliveryOrder->status;
        $newStatus = $validated['status'];

        // Update delivery order
        $updateData = ['status' => $newStatus];

        // Set timestamps based on status
        switch ($newStatus) {
            case 'picked_up':
                $updateData['picked_up_at'] = now();
                break;
            case 'in_transit':
                $updateData['in_transit_at'] = now();
                break;
            case 'delivered':
                $updateData['delivered_at'] = now();
                // Clear driver's current delivery
                if ($deliveryOrder->driver) {
                    $deliveryOrder->driver->update(['current_delivery_id' => null]);
                }
                break;
            case 'cancelled':
                $updateData['cancelled_at'] = now();
                // Clear driver's current delivery
                if ($deliveryOrder->driver) {
                    $deliveryOrder->driver->update(['current_delivery_id' => null]);
                }
                break;
        }

        if (isset($validated['notes'])) {
            $updateData['notes'] = $validated['notes'];
        }

        $deliveryOrder->update($updateData);

        // Update driver location if provided
        if (isset($validated['latitude']) && isset($validated['longitude']) && $deliveryOrder->driver) {
            $deliveryOrder->driver->update([
                'current_latitude' => $validated['latitude'],
                'current_longitude' => $validated['longitude'],
                'last_location_update' => now(),
            ]);
        }

        return response()->json([
            'success' => true,
            'message' => "Delivery status updated to {$newStatus}.",
            'data' => $deliveryOrder->load(['driver', 'order']),
        ]);
    }

    /**
     * Track delivery order (public endpoint)
     */
    public function track(Request $request): JsonResponse
    {
        $request->validate([
            'tracking_number' => 'required|string',
        ]);

        $deliveryOrder = DeliveryOrder::where('tracking_number', $request->tracking_number)
                                    ->with(['order', 'driver'])
                                    ->first();

        if (!$deliveryOrder) {
            return response()->json([
                'success' => false,
                'message' => 'Delivery order not found.',
            ], 404);
        }

        $trackingData = [
            'tracking_number' => $deliveryOrder->tracking_number,
            'status' => $deliveryOrder->status,
            'status_label' => $deliveryOrder->status_label,
            'estimated_delivery_time' => $deliveryOrder->estimated_delivery_time,
            'delivery_address' => $deliveryOrder->delivery_address,
            'driver' => $deliveryOrder->driver ? [
                'name' => $deliveryOrder->driver->display_name,
                'phone' => $deliveryOrder->driver->phone,
                'vehicle_type' => $deliveryOrder->driver->vehicle_type_label,
                'current_location' => $deliveryOrder->driver->current_location,
            ] : null,
            'timeline' => $this->getDeliveryTimeline($deliveryOrder),
        ];

        return response()->json([
            'success' => true,
            'data' => $trackingData,
        ]);
    }

    /**
     * Get delivery statistics
     */
    public function getStatistics(Request $request): JsonResponse
    {
        $query = DeliveryOrder::query();

        // Apply date filter
        if ($request->filled('date_from')) {
            $query->whereDate('created_at', '>=', $request->date_from);
        }

        if ($request->filled('date_to')) {
            $query->whereDate('created_at', '<=', $request->date_to);
        }

        $stats = [
            'total_deliveries' => $query->count(),
            'pending_deliveries' => (clone $query)->where('status', 'pending')->count(),
            'assigned_deliveries' => (clone $query)->where('status', 'assigned')->count(),
            'in_transit_deliveries' => (clone $query)->where('status', 'in_transit')->count(),
            'delivered_orders' => (clone $query)->where('status', 'delivered')->count(),
            'cancelled_deliveries' => (clone $query)->where('status', 'cancelled')->count(),
            'total_delivery_fees' => (clone $query)->where('status', 'delivered')->sum('delivery_fee'),
            'average_delivery_time' => $this->getAverageDeliveryTime($query),
        ];

        return response()->json([
            'success' => true,
            'data' => $stats,
        ]);
    }

    /**
     * Get delivery timeline
     */
    private function getDeliveryTimeline(DeliveryOrder $deliveryOrder): array
    {
        $timeline = [];

        $timeline[] = [
            'status' => 'pending',
            'label' => 'Order Placed',
            'timestamp' => $deliveryOrder->created_at,
            'completed' => true,
        ];

        if ($deliveryOrder->assigned_at) {
            $timeline[] = [
                'status' => 'assigned',
                'label' => 'Driver Assigned',
                'timestamp' => $deliveryOrder->assigned_at,
                'completed' => true,
            ];
        }

        if ($deliveryOrder->picked_up_at) {
            $timeline[] = [
                'status' => 'picked_up',
                'label' => 'Order Picked Up',
                'timestamp' => $deliveryOrder->picked_up_at,
                'completed' => true,
            ];
        }

        if ($deliveryOrder->in_transit_at) {
            $timeline[] = [
                'status' => 'in_transit',
                'label' => 'Out for Delivery',
                'timestamp' => $deliveryOrder->in_transit_at,
                'completed' => true,
            ];
        }

        if ($deliveryOrder->delivered_at) {
            $timeline[] = [
                'status' => 'delivered',
                'label' => 'Delivered',
                'timestamp' => $deliveryOrder->delivered_at,
                'completed' => true,
            ];
        } elseif ($deliveryOrder->cancelled_at) {
            $timeline[] = [
                'status' => 'cancelled',
                'label' => 'Cancelled',
                'timestamp' => $deliveryOrder->cancelled_at,
                'completed' => true,
            ];
        }

        return $timeline;
    }

    /**
     * Get average delivery time
     */
    private function getAverageDeliveryTime($query): ?float
    {
        $deliveredOrders = (clone $query)->where('status', 'delivered')
                                        ->whereNotNull('delivered_at')
                                        ->get();

        if ($deliveredOrders->isEmpty()) {
            return null;
        }

        $totalMinutes = 0;
        $count = 0;

        foreach ($deliveredOrders as $order) {
            if ($order->created_at && $order->delivered_at) {
                $totalMinutes += $order->created_at->diffInMinutes($order->delivered_at);
                $count++;
            }
        }

        return $count > 0 ? $totalMinutes / $count : null;
    }
}
