<?php

namespace App\Http\Controllers\Api\Tenant;

use App\Http\Controllers\Controller;
use App\Http\Requests\Tenant\StoreCustomerRequest;
use App\Http\Requests\Tenant\UpdateCustomerRequest;
use App\Http\Resources\Tenant\CustomerResource;
use App\Http\Resources\Tenant\CustomerCollection;
use App\Models\Tenant\Customer;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Hash;

class CustomerController extends Controller
{
    /**
     * Display a listing of customers
     */
    public function index(Request $request): CustomerCollection
    {
        $query = Customer::with(['addresses', 'orders'])
            ->withCount(['orders', 'restaurantReviews']);

        // Filter by status
        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }

        // Filter by customer tier
        if ($request->filled('tier')) {
            $query->where('customer_tier', $request->tier);
        }

        // Filter by registration date
        if ($request->filled('date_from')) {
            $query->whereDate('created_at', '>=', $request->date_from);
        }

        if ($request->filled('date_to')) {
            $query->whereDate('created_at', '<=', $request->date_to);
        }

        // Search by name, email, or phone
        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->where('first_name', 'like', "%{$search}%")
                  ->orWhere('last_name', 'like', "%{$search}%")
                  ->orWhere('email', 'like', "%{$search}%")
                  ->orWhere('phone', 'like', "%{$search}%");
            });
        }

        // Filter VIP customers
        if ($request->filled('vip_only')) {
            $query->vip();
        }

        // Filter recently active customers
        if ($request->filled('recently_active')) {
            $query->recentlyActive($request->get('days', 30));
        }

        // Sort options
        switch ($request->get('sort', 'name')) {
            case 'total_spent':
                $query->orderBy('total_spent', 'desc');
                break;
            case 'total_orders':
                $query->orderBy('total_orders', 'desc');
                break;
            case 'recent':
                $query->latest();
                break;
            default:
                $query->orderBy('first_name')->orderBy('last_name');
                break;
        }

        $customers = $query->paginate($request->get('per_page', 15));

        return new CustomerCollection($customers);
    }

    /**
     * Store a newly created customer
     */
    public function store(StoreCustomerRequest $request): JsonResponse
    {
        $data = $request->validated();
        
        if (isset($data['password'])) {
            $data['password'] = Hash::make($data['password']);
        }

        $customer = Customer::create($data);

        return response()->json([
            'message' => 'Customer created successfully',
            'data' => new CustomerResource($customer->load('addresses'))
        ], 201);
    }

    /**
     * Display the specified customer
     */
    public function show(Customer $customer): CustomerResource
    {
        $customer->load([
            'addresses',
            'orders' => function ($query) {
                $query->with('items.food')->latest()->limit(10);
            },
            'restaurantReviews' => function ($query) {
                $query->latest()->limit(5);
            },
            'foodReviews' => function ($query) {
                $query->with('food')->latest()->limit(5);
            }
        ]);

        return new CustomerResource($customer);
    }

    /**
     * Update the specified customer
     */
    public function update(UpdateCustomerRequest $request, Customer $customer): JsonResponse
    {
        $data = $request->validated();
        
        if (isset($data['password'])) {
            $data['password'] = Hash::make($data['password']);
        }

        $customer->update($data);

        return response()->json([
            'message' => 'Customer updated successfully',
            'data' => new CustomerResource($customer->fresh('addresses'))
        ]);
    }

    /**
     * Remove the specified customer
     */
    public function destroy(Customer $customer): JsonResponse
    {
        $customer->update(['status' => 'inactive']);

        return response()->json([
            'message' => 'Customer deactivated successfully'
        ]);
    }

    /**
     * Get customer statistics
     */
    public function statistics(): JsonResponse
    {
        $stats = [
            'total_customers' => Customer::count(),
            'active_customers' => Customer::active()->count(),
            'vip_customers' => Customer::vip()->count(),
            'new_this_month' => Customer::whereMonth('created_at', now()->month)->count(),
            'recently_active' => Customer::recentlyActive()->count(),
            'by_tier' => Customer::selectRaw('customer_tier, COUNT(*) as count')
                ->groupBy('customer_tier')
                ->pluck('count', 'customer_tier'),
            'top_spenders' => Customer::orderBy('total_spent', 'desc')
                ->limit(10)
                ->get(['id', 'first_name', 'last_name', 'total_spent', 'total_orders'])
        ];

        return response()->json(['data' => $stats]);
    }

    /**
     * Add loyalty points to customer
     */
    public function addLoyaltyPoints(Request $request, Customer $customer): JsonResponse
    {
        $request->validate([
            'points' => 'required|integer|min:1|max:10000',
            'reason' => 'nullable|string|max:255'
        ]);

        $customer->addLoyaltyPoints($request->points);

        return response()->json([
            'message' => 'Loyalty points added successfully',
            'data' => [
                'points_added' => $request->points,
                'total_points' => $customer->fresh()->loyalty_points,
                'reason' => $request->reason
            ]
        ]);
    }

    /**
     * Redeem loyalty points
     */
    public function redeemLoyaltyPoints(Request $request, Customer $customer): JsonResponse
    {
        $request->validate([
            'points' => 'required|integer|min:1',
            'reason' => 'nullable|string|max:255'
        ]);

        if (!$customer->redeemLoyaltyPoints($request->points)) {
            return response()->json([
                'message' => 'Insufficient loyalty points',
                'available_points' => $customer->loyalty_points
            ], 422);
        }

        return response()->json([
            'message' => 'Loyalty points redeemed successfully',
            'data' => [
                'points_redeemed' => $request->points,
                'remaining_points' => $customer->fresh()->loyalty_points,
                'reason' => $request->reason
            ]
        ]);
    }

    /**
     * Get customer order history
     */
    public function orderHistory(Customer $customer, Request $request): JsonResponse
    {
        $orders = $customer->orders()
            ->with(['items.food', 'statusHistory'])
            ->when($request->filled('status'), function ($query) use ($request) {
                $query->where('status', $request->status);
            })
            ->when($request->filled('order_type'), function ($query) use ($request) {
                $query->where('order_type', $request->order_type);
            })
            ->latest()
            ->paginate($request->get('per_page', 10));

        return response()->json([
            'data' => $orders
        ]);
    }

    /**
     * Get customer addresses
     */
    public function addresses(Customer $customer): JsonResponse
    {
        $addresses = $customer->addresses()->active()->get();

        return response()->json([
            'data' => $addresses
        ]);
    }

    /**
     * Update customer tier manually
     */
    public function updateTier(Request $request, Customer $customer): JsonResponse
    {
        $request->validate([
            'tier' => 'required|in:bronze,silver,gold,platinum',
            'reason' => 'nullable|string|max:255'
        ]);

        $oldTier = $customer->customer_tier;
        $customer->update(['customer_tier' => $request->tier]);

        return response()->json([
            'message' => 'Customer tier updated successfully',
            'data' => [
                'old_tier' => $oldTier,
                'new_tier' => $request->tier,
                'reason' => $request->reason
            ]
        ]);
    }

    /**
     * Block/unblock customer
     */
    public function toggleBlock(Customer $customer): JsonResponse
    {
        $newStatus = $customer->status === 'blocked' ? 'active' : 'blocked';
        $customer->update(['status' => $newStatus]);

        return response()->json([
            'message' => "Customer {$newStatus} successfully",
            'data' => new CustomerResource($customer)
        ]);
    }
}
