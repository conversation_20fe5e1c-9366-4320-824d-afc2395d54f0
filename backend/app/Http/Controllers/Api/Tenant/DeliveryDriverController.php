<?php

namespace App\Http\Controllers\Api\Tenant;

use App\Http\Controllers\Controller;
use App\Models\DeliveryDriver;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;

class DeliveryDriverController extends Controller
{
    public function __construct()
    {
        $this->middleware('auth:sanctum');
        $this->middleware('role:admin|restaurant_manager|delivery')->except(['updateLocation']);
    }

    /**
     * Display a listing of delivery drivers
     */
    public function index(Request $request): JsonResponse
    {
        $query = DeliveryDriver::with(['user']);

        // Apply filters
        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }

        if ($request->filled('is_available')) {
            $query->where('is_available', $request->boolean('is_available'));
        }

        if ($request->filled('search')) {
            $query->whereHas('user', function ($q) use ($request) {
                $q->where('name', 'like', "%{$request->search}%")
                  ->orWhere('phone', 'like', "%{$request->search}%");
            });
        }

        // Apply sorting
        $sortField = $request->get('sort', 'created_at');
        $sortDirection = $request->get('direction', 'desc');
        $query->orderBy($sortField, $sortDirection);

        $drivers = $query->paginate($request->get('per_page', 15));

        return response()->json([
            'success' => true,
            'data' => $drivers,
        ]);
    }

    /**
     * Store a newly created delivery driver
     */
    public function store(Request $request): JsonResponse
    {
        $validated = $request->validate([
            'user_id' => 'required|exists:users,id|unique:delivery_drivers,user_id',
            'vehicle_type' => 'required|in:bike,motorcycle,car,bicycle',
            'vehicle_number' => 'required|string|max:20',
            'license_number' => 'required|string|max:50',
            'phone' => 'required|string|max:20',
            'emergency_contact_name' => 'nullable|string|max:255',
            'emergency_contact_phone' => 'nullable|string|max:20',
            'is_available' => 'boolean',
        ]);

        $driver = DeliveryDriver::create($validated);
        $driver->load('user');

        return response()->json([
            'success' => true,
            'message' => 'Delivery driver created successfully.',
            'data' => $driver,
        ], 201);
    }

    /**
     * Display the specified delivery driver
     */
    public function show(DeliveryDriver $deliveryDriver): JsonResponse
    {
        $deliveryDriver->load(['user', 'currentDelivery']);

        return response()->json([
            'success' => true,
            'data' => $deliveryDriver,
        ]);
    }

    /**
     * Update the specified delivery driver
     */
    public function update(Request $request, DeliveryDriver $deliveryDriver): JsonResponse
    {
        $validated = $request->validate([
            'vehicle_type' => 'required|in:bike,motorcycle,car,bicycle',
            'vehicle_number' => 'required|string|max:20',
            'license_number' => 'required|string|max:50',
            'phone' => 'required|string|max:20',
            'emergency_contact_name' => 'nullable|string|max:255',
            'emergency_contact_phone' => 'nullable|string|max:20',
            'is_available' => 'boolean',
            'status' => 'in:active,inactive,suspended',
        ]);

        $deliveryDriver->update($validated);
        $deliveryDriver->load('user');

        return response()->json([
            'success' => true,
            'message' => 'Delivery driver updated successfully.',
            'data' => $deliveryDriver,
        ]);
    }

    /**
     * Remove the specified delivery driver
     */
    public function destroy(DeliveryDriver $deliveryDriver): JsonResponse
    {
        // Check if driver has active deliveries
        if ($deliveryDriver->hasActiveDeliveries()) {
            return response()->json([
                'success' => false,
                'message' => 'Cannot delete driver with active deliveries.',
            ], 422);
        }

        $deliveryDriver->delete();

        return response()->json([
            'success' => true,
            'message' => 'Delivery driver deleted successfully.',
        ]);
    }

    /**
     * Get available drivers
     */
    public function getAvailable(): JsonResponse
    {
        $drivers = DeliveryDriver::with(['user'])
                                ->where('is_available', true)
                                ->where('status', 'active')
                                ->whereNull('current_delivery_id')
                                ->get();

        return response()->json([
            'success' => true,
            'data' => $drivers,
        ]);
    }

    /**
     * Update driver location
     */
    public function updateLocation(Request $request, DeliveryDriver $deliveryDriver): JsonResponse
    {
        $validated = $request->validate([
            'latitude' => 'required|numeric|between:-90,90',
            'longitude' => 'required|numeric|between:-180,180',
        ]);

        $deliveryDriver->update([
            'current_latitude' => $validated['latitude'],
            'current_longitude' => $validated['longitude'],
            'last_location_update' => now(),
        ]);

        return response()->json([
            'success' => true,
            'message' => 'Location updated successfully.',
            'data' => [
                'latitude' => $deliveryDriver->current_latitude,
                'longitude' => $deliveryDriver->current_longitude,
                'updated_at' => $deliveryDriver->last_location_update,
            ],
        ]);
    }

    /**
     * Toggle driver availability
     */
    public function toggleAvailability(DeliveryDriver $deliveryDriver): JsonResponse
    {
        $deliveryDriver->update(['is_available' => !$deliveryDriver->is_available]);

        $status = $deliveryDriver->is_available ? 'available' : 'unavailable';

        return response()->json([
            'success' => true,
            'message' => "Driver marked as {$status}.",
            'data' => $deliveryDriver,
        ]);
    }

    /**
     * Get driver statistics
     */
    public function getStatistics(DeliveryDriver $deliveryDriver): JsonResponse
    {
        $stats = [
            'total_deliveries' => $deliveryDriver->deliveries()->count(),
            'completed_deliveries' => $deliveryDriver->deliveries()->where('status', 'delivered')->count(),
            'cancelled_deliveries' => $deliveryDriver->deliveries()->where('status', 'cancelled')->count(),
            'average_rating' => $deliveryDriver->deliveries()->avg('rating') ?? 0,
            'total_earnings' => $deliveryDriver->deliveries()->where('status', 'delivered')->sum('delivery_fee'),
            'deliveries_today' => $deliveryDriver->deliveries()->whereDate('created_at', today())->count(),
            'deliveries_this_week' => $deliveryDriver->deliveries()->whereBetween('created_at', [
                now()->startOfWeek(),
                now()->endOfWeek()
            ])->count(),
            'deliveries_this_month' => $deliveryDriver->deliveries()->whereMonth('created_at', now()->month)->count(),
        ];

        return response()->json([
            'success' => true,
            'data' => $stats,
        ]);
    }

    /**
     * Get driver delivery history
     */
    public function getDeliveryHistory(Request $request, DeliveryDriver $deliveryDriver): JsonResponse
    {
        $query = $deliveryDriver->deliveries()->with(['order']);

        // Apply date filter
        if ($request->filled('date_from')) {
            $query->whereDate('created_at', '>=', $request->date_from);
        }

        if ($request->filled('date_to')) {
            $query->whereDate('created_at', '<=', $request->date_to);
        }

        // Apply status filter
        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }

        $deliveries = $query->latest()->paginate($request->get('per_page', 15));

        return response()->json([
            'success' => true,
            'data' => $deliveries,
        ]);
    }

    /**
     * Assign delivery to driver
     */
    public function assignDelivery(Request $request, DeliveryDriver $deliveryDriver): JsonResponse
    {
        $validated = $request->validate([
            'delivery_order_id' => 'required|exists:delivery_orders,id',
        ]);

        // Check if driver is available
        if (!$deliveryDriver->is_available || $deliveryDriver->status !== 'active') {
            return response()->json([
                'success' => false,
                'message' => 'Driver is not available for delivery.',
            ], 422);
        }

        // Check if driver already has an active delivery
        if ($deliveryDriver->hasActiveDeliveries()) {
            return response()->json([
                'success' => false,
                'message' => 'Driver already has an active delivery.',
            ], 422);
        }

        $deliveryOrder = \App\Models\DeliveryOrder::find($validated['delivery_order_id']);
        
        // Assign delivery
        $deliveryOrder->update([
            'driver_id' => $deliveryDriver->id,
            'status' => 'assigned',
            'assigned_at' => now(),
        ]);

        $deliveryDriver->update(['current_delivery_id' => $deliveryOrder->id]);

        return response()->json([
            'success' => true,
            'message' => 'Delivery assigned successfully.',
            'data' => $deliveryOrder->load('driver'),
        ]);
    }

    /**
     * Get nearby drivers for a location
     */
    public function getNearbyDrivers(Request $request): JsonResponse
    {
        $validated = $request->validate([
            'latitude' => 'required|numeric|between:-90,90',
            'longitude' => 'required|numeric|between:-180,180',
            'radius' => 'nullable|numeric|min:1|max:50', // km
        ]);

        $radius = $validated['radius'] ?? 10; // Default 10km

        $drivers = DeliveryDriver::with(['user'])
                                ->where('is_available', true)
                                ->where('status', 'active')
                                ->whereNull('current_delivery_id')
                                ->whereNotNull('current_latitude')
                                ->whereNotNull('current_longitude')
                                ->get()
                                ->filter(function ($driver) use ($validated, $radius) {
                                    $distance = $this->calculateDistance(
                                        $validated['latitude'],
                                        $validated['longitude'],
                                        $driver->current_latitude,
                                        $driver->current_longitude
                                    );
                                    return $distance <= $radius;
                                })
                                ->values();

        return response()->json([
            'success' => true,
            'data' => $drivers,
        ]);
    }

    /**
     * Calculate distance between two coordinates
     */
    private function calculateDistance($lat1, $lon1, $lat2, $lon2): float
    {
        $earthRadius = 6371; // km

        $dLat = deg2rad($lat2 - $lat1);
        $dLon = deg2rad($lon2 - $lon1);

        $a = sin($dLat/2) * sin($dLat/2) + cos(deg2rad($lat1)) * cos(deg2rad($lat2)) * sin($dLon/2) * sin($dLon/2);
        $c = 2 * atan2(sqrt($a), sqrt(1-$a));

        return $earthRadius * $c;
    }
}
