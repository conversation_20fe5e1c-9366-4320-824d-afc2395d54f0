<?php

namespace App\Http\Controllers\Api\Tenant;

use App\Http\Controllers\Controller;
use App\Models\Coupon;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;

class CouponController extends Controller
{
    public function __construct()
    {
        $this->middleware('auth:sanctum')->except(['validateCoupon']);
        $this->middleware('role:admin|restaurant_manager')->except(['validateCoupon']);
    }

    /**
     * Display a listing of coupons
     */
    public function index(Request $request): JsonResponse
    {
        $query = Coupon::query();

        // Apply filters
        if ($request->filled('type')) {
            $query->where('type', $request->type);
        }

        if ($request->filled('status')) {
            if ($request->status === 'active') {
                $query->active();
            } elseif ($request->status === 'expired') {
                $query->expired();
            } elseif ($request->status === 'inactive') {
                $query->where('is_active', false);
            }
        }

        if ($request->filled('search')) {
            $query->where(function ($q) use ($request) {
                $q->where('code', 'like', "%{$request->search}%")
                  ->orWhere('name', 'like', "%{$request->search}%")
                  ->orWhere('description', 'like', "%{$request->search}%");
            });
        }

        // Apply sorting
        $sortField = $request->get('sort', 'created_at');
        $sortDirection = $request->get('direction', 'desc');
        $query->orderBy($sortField, $sortDirection);

        $coupons = $query->paginate($request->get('per_page', 15));

        return response()->json([
            'success' => true,
            'data' => $coupons,
        ]);
    }

    /**
     * Store a newly created coupon
     */
    public function store(Request $request): JsonResponse
    {
        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'code' => 'required|string|max:50|unique:coupons,code',
            'description' => 'nullable|string|max:1000',
            'type' => 'required|in:fixed,percentage',
            'value' => 'required|numeric|min:0',
            'minimum_order_amount' => 'nullable|numeric|min:0',
            'maximum_discount_amount' => 'nullable|numeric|min:0',
            'usage_limit' => 'nullable|integer|min:1',
            'usage_limit_per_customer' => 'nullable|integer|min:1',
            'valid_from' => 'required|date|after_or_equal:today',
            'valid_until' => 'required|date|after:valid_from',
            'is_active' => 'boolean',
            'applicable_to' => 'nullable|in:all,specific_items,specific_categories',
            'applicable_items' => 'nullable|array',
            'applicable_items.*' => 'exists:menu_items,id',
            'applicable_categories' => 'nullable|array',
            'applicable_categories.*' => 'exists:food_categories,id',
        ]);

        $coupon = Coupon::create($validated);

        return response()->json([
            'success' => true,
            'message' => 'Coupon created successfully.',
            'data' => $coupon,
        ], 201);
    }

    /**
     * Display the specified coupon
     */
    public function show(Coupon $coupon): JsonResponse
    {
        return response()->json([
            'success' => true,
            'data' => $coupon,
        ]);
    }

    /**
     * Update the specified coupon
     */
    public function update(Request $request, Coupon $coupon): JsonResponse
    {
        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'code' => 'required|string|max:50|unique:coupons,code,' . $coupon->id,
            'description' => 'nullable|string|max:1000',
            'type' => 'required|in:fixed,percentage',
            'value' => 'required|numeric|min:0',
            'minimum_order_amount' => 'nullable|numeric|min:0',
            'maximum_discount_amount' => 'nullable|numeric|min:0',
            'usage_limit' => 'nullable|integer|min:1',
            'usage_limit_per_customer' => 'nullable|integer|min:1',
            'valid_from' => 'required|date',
            'valid_until' => 'required|date|after:valid_from',
            'is_active' => 'boolean',
            'applicable_to' => 'nullable|in:all,specific_items,specific_categories',
            'applicable_items' => 'nullable|array',
            'applicable_items.*' => 'exists:menu_items,id',
            'applicable_categories' => 'nullable|array',
            'applicable_categories.*' => 'exists:food_categories,id',
        ]);

        $coupon->update($validated);

        return response()->json([
            'success' => true,
            'message' => 'Coupon updated successfully.',
            'data' => $coupon,
        ]);
    }

    /**
     * Remove the specified coupon
     */
    public function destroy(Coupon $coupon): JsonResponse
    {
        $coupon->delete();

        return response()->json([
            'success' => true,
            'message' => 'Coupon deleted successfully.',
        ]);
    }

    /**
     * Validate coupon code
     */
    public function validateCoupon(Request $request): JsonResponse
    {
        $validated = $request->validate([
            'code' => 'required|string|max:50',
            'order_amount' => 'required|numeric|min:0',
            'customer_id' => 'nullable|exists:customers,id',
            'items' => 'nullable|array',
            'items.*' => 'exists:menu_items,id',
        ]);

        $coupon = Coupon::where('code', $validated['code'])->first();

        if (!$coupon) {
            return response()->json([
                'success' => false,
                'message' => 'Invalid coupon code.',
            ], 404);
        }

        // Check if coupon is valid
        $validationResult = $coupon->validateForOrder(
            $validated['order_amount'],
            $validated['customer_id'] ?? null,
            $validated['items'] ?? []
        );

        if (!$validationResult['valid']) {
            return response()->json([
                'success' => false,
                'message' => $validationResult['message'],
            ], 422);
        }

        $discountAmount = $coupon->calculateDiscount($validated['order_amount']);

        return response()->json([
            'success' => true,
            'message' => 'Coupon is valid.',
            'data' => [
                'coupon' => $coupon,
                'discount_amount' => $discountAmount,
                'final_amount' => $validated['order_amount'] - $discountAmount,
            ],
        ]);
    }

    /**
     * Toggle coupon status
     */
    public function toggleStatus(Coupon $coupon): JsonResponse
    {
        $coupon->update(['is_active' => !$coupon->is_active]);

        $status = $coupon->is_active ? 'activated' : 'deactivated';

        return response()->json([
            'success' => true,
            'message' => "Coupon {$status} successfully.",
            'data' => $coupon,
        ]);
    }

    /**
     * Get coupon statistics
     */
    public function getStatistics(Request $request): JsonResponse
    {
        $query = Coupon::query();

        // Apply date filter
        if ($request->filled('date_from')) {
            $query->whereDate('created_at', '>=', $request->date_from);
        }

        if ($request->filled('date_to')) {
            $query->whereDate('created_at', '<=', $request->date_to);
        }

        $stats = [
            'total_coupons' => $query->count(),
            'active_coupons' => (clone $query)->active()->count(),
            'expired_coupons' => (clone $query)->expired()->count(),
            'used_coupons' => (clone $query)->where('used_count', '>', 0)->count(),
            'total_usage' => (clone $query)->sum('used_count'),
            'total_discount_given' => (clone $query)->sum('total_discount_amount'),
            'most_used_coupon' => (clone $query)->orderBy('used_count', 'desc')->first(),
        ];

        return response()->json([
            'success' => true,
            'data' => $stats,
        ]);
    }

    /**
     * Get active coupons for customers
     */
    public function getActiveCoupons(): JsonResponse
    {
        $coupons = Coupon::active()
                        ->where('valid_from', '<=', now())
                        ->where('valid_until', '>=', now())
                        ->orderBy('created_at', 'desc')
                        ->get();

        return response()->json([
            'success' => true,
            'data' => $coupons,
        ]);
    }

    /**
     * Generate coupon code
     */
    public function generateCode(): JsonResponse
    {
        $code = Coupon::generateUniqueCode();

        return response()->json([
            'success' => true,
            'data' => ['code' => $code],
        ]);
    }

    /**
     * Bulk update coupons
     */
    public function bulkUpdate(Request $request): JsonResponse
    {
        $validated = $request->validate([
            'action' => 'required|in:activate,deactivate,delete',
            'coupon_ids' => 'required|array|min:1',
            'coupon_ids.*' => 'exists:coupons,id',
        ]);

        $coupons = Coupon::whereIn('id', $validated['coupon_ids']);

        switch ($validated['action']) {
            case 'activate':
                $coupons->update(['is_active' => true]);
                $message = 'Selected coupons activated successfully.';
                break;

            case 'deactivate':
                $coupons->update(['is_active' => false]);
                $message = 'Selected coupons deactivated successfully.';
                break;

            case 'delete':
                $coupons->delete();
                $message = 'Selected coupons deleted successfully.';
                break;
        }

        return response()->json([
            'success' => true,
            'message' => $message,
        ]);
    }

    /**
     * Get coupon usage history
     */
    public function getUsageHistory(Coupon $coupon, Request $request): JsonResponse
    {
        // This would require a coupon_usage table to track individual uses
        // For now, return basic usage information
        $usageData = [
            'coupon' => $coupon,
            'total_uses' => $coupon->used_count,
            'remaining_uses' => $coupon->usage_limit ? $coupon->usage_limit - $coupon->used_count : null,
            'total_discount_given' => $coupon->total_discount_amount,
            'usage_percentage' => $coupon->usage_limit ? ($coupon->used_count / $coupon->usage_limit) * 100 : 0,
        ];

        return response()->json([
            'success' => true,
            'data' => $usageData,
        ]);
    }
}
