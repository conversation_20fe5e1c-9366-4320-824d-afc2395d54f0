<?php

namespace App\Http\Controllers\Api\Tenant;

use App\Http\Controllers\Controller;
use App\Models\Order;
use App\Models\Customer;
use App\Models\MenuItem;
use App\Models\User;
use App\Models\DeliveryOrder;
use App\Models\RestaurantReview;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Carbon\Carbon;

class AnalyticsController extends Controller
{
    public function __construct()
    {
        $this->middleware('auth:sanctum');
        $this->middleware('role:admin|restaurant_manager');
    }

    /**
     * Get dashboard overview analytics
     */
    public function overview(Request $request): JsonResponse
    {
        $dateRange = $this->getDateRange($request);
        
        $analytics = [
            'revenue' => $this->getRevenueAnalytics($dateRange),
            'orders' => $this->getOrderAnalytics($dateRange),
            'customers' => $this->getCustomerAnalytics($dateRange),
            'menu_performance' => $this->getMenuPerformanceAnalytics($dateRange),
            'delivery' => $this->getDeliveryAnalytics($dateRange),
            'reviews' => $this->getReviewAnalytics($dateRange),
        ];

        return response()->json([
            'success' => true,
            'data' => $analytics,
        ]);
    }

    /**
     * Get revenue analytics
     */
    public function revenue(Request $request): JsonResponse
    {
        $dateRange = $this->getDateRange($request);
        $groupBy = $request->get('group_by', 'day'); // day, week, month

        $revenueData = $this->getRevenueData($dateRange, $groupBy);
        
        return response()->json([
            'success' => true,
            'data' => $revenueData,
        ]);
    }

    /**
     * Get sales analytics
     */
    public function sales(Request $request): JsonResponse
    {
        $dateRange = $this->getDateRange($request);
        
        $salesData = [
            'total_sales' => $this->getTotalSales($dateRange),
            'sales_by_channel' => $this->getSalesByChannel($dateRange),
            'sales_by_payment_method' => $this->getSalesByPaymentMethod($dateRange),
            'hourly_sales' => $this->getHourlySales($dateRange),
            'daily_sales' => $this->getDailySales($dateRange),
            'top_selling_items' => $this->getTopSellingItems($dateRange),
        ];

        return response()->json([
            'success' => true,
            'data' => $salesData,
        ]);
    }

    /**
     * Get customer analytics
     */
    public function customers(Request $request): JsonResponse
    {
        $dateRange = $this->getDateRange($request);
        
        $customerData = [
            'total_customers' => Customer::count(),
            'new_customers' => Customer::whereBetween('created_at', $dateRange)->count(),
            'returning_customers' => $this->getReturningCustomers($dateRange),
            'customer_lifetime_value' => $this->getCustomerLifetimeValue(),
            'customer_acquisition' => $this->getCustomerAcquisition($dateRange),
            'customer_retention' => $this->getCustomerRetention($dateRange),
            'top_customers' => $this->getTopCustomers($dateRange),
        ];

        return response()->json([
            'success' => true,
            'data' => $customerData,
        ]);
    }

    /**
     * Get menu performance analytics
     */
    public function menuPerformance(Request $request): JsonResponse
    {
        $dateRange = $this->getDateRange($request);
        
        $menuData = [
            'top_selling_items' => $this->getTopSellingItems($dateRange, 20),
            'low_performing_items' => $this->getLowPerformingItems($dateRange),
            'category_performance' => $this->getCategoryPerformance($dateRange),
            'item_profitability' => $this->getItemProfitability($dateRange),
            'menu_item_trends' => $this->getMenuItemTrends($dateRange),
        ];

        return response()->json([
            'success' => true,
            'data' => $menuData,
        ]);
    }

    /**
     * Get staff performance analytics
     */
    public function staffPerformance(Request $request): JsonResponse
    {
        $dateRange = $this->getDateRange($request);
        
        $staffData = [
            'total_staff' => User::where('is_active', true)->count(),
            'staff_productivity' => $this->getStaffProductivity($dateRange),
            'attendance_rate' => $this->getAttendanceRate($dateRange),
            'top_performers' => $this->getTopPerformers($dateRange),
            'shift_coverage' => $this->getShiftCoverage($dateRange),
        ];

        return response()->json([
            'success' => true,
            'data' => $staffData,
        ]);
    }

    /**
     * Get delivery analytics
     */
    public function delivery(Request $request): JsonResponse
    {
        $dateRange = $this->getDateRange($request);
        
        $deliveryData = [
            'total_deliveries' => DeliveryOrder::whereBetween('created_at', $dateRange)->count(),
            'delivery_success_rate' => $this->getDeliverySuccessRate($dateRange),
            'average_delivery_time' => $this->getAverageDeliveryTime($dateRange),
            'delivery_areas' => $this->getDeliveryAreas($dateRange),
            'driver_performance' => $this->getDriverPerformance($dateRange),
        ];

        return response()->json([
            'success' => true,
            'data' => $deliveryData,
        ]);
    }

    /**
     * Get review analytics
     */
    public function reviews(Request $request): JsonResponse
    {
        $dateRange = $this->getDateRange($request);
        
        $reviewData = [
            'total_reviews' => RestaurantReview::whereBetween('created_at', $dateRange)->count(),
            'average_rating' => RestaurantReview::whereBetween('created_at', $dateRange)->avg('rating'),
            'rating_distribution' => $this->getRatingDistribution($dateRange),
            'review_trends' => $this->getReviewTrends($dateRange),
            'sentiment_analysis' => $this->getSentimentAnalysis($dateRange),
        ];

        return response()->json([
            'success' => true,
            'data' => $reviewData,
        ]);
    }

    /**
     * Get real-time analytics
     */
    public function realTime(): JsonResponse
    {
        $today = [now()->startOfDay(), now()->endOfDay()];
        
        $realTimeData = [
            'current_orders' => Order::whereDate('created_at', today())->count(),
            'today_revenue' => Order::whereDate('created_at', today())->sum('total_amount'),
            'active_customers' => $this->getActiveCustomers(),
            'pending_orders' => Order::where('status', 'pending')->count(),
            'in_progress_orders' => Order::where('status', 'preparing')->count(),
            'ready_orders' => Order::where('status', 'ready')->count(),
            'hourly_orders' => $this->getHourlyOrders(),
        ];

        return response()->json([
            'success' => true,
            'data' => $realTimeData,
        ]);
    }

    /**
     * Helper method to get date range
     */
    private function getDateRange(Request $request): array
    {
        $period = $request->get('period', 'week'); // day, week, month, year, custom
        
        return match ($period) {
            'today' => [now()->startOfDay(), now()->endOfDay()],
            'yesterday' => [now()->subDay()->startOfDay(), now()->subDay()->endOfDay()],
            'week' => [now()->startOfWeek(), now()->endOfWeek()],
            'month' => [now()->startOfMonth(), now()->endOfMonth()],
            'year' => [now()->startOfYear(), now()->endOfYear()],
            'custom' => [
                Carbon::parse($request->get('start_date', now()->subDays(30))),
                Carbon::parse($request->get('end_date', now()))
            ],
            default => [now()->subDays(7), now()],
        };
    }

    /**
     * Get revenue analytics
     */
    private function getRevenueAnalytics(array $dateRange): array
    {
        $currentRevenue = Order::whereBetween('created_at', $dateRange)->sum('total_amount');
        $previousPeriod = $this->getPreviousPeriod($dateRange);
        $previousRevenue = Order::whereBetween('created_at', $previousPeriod)->sum('total_amount');
        
        return [
            'current_revenue' => $currentRevenue,
            'previous_revenue' => $previousRevenue,
            'growth_rate' => $previousRevenue > 0 ? (($currentRevenue - $previousRevenue) / $previousRevenue) * 100 : 0,
            'average_order_value' => $this->getAverageOrderValue($dateRange),
        ];
    }

    /**
     * Get order analytics
     */
    private function getOrderAnalytics(array $dateRange): array
    {
        return [
            'total_orders' => Order::whereBetween('created_at', $dateRange)->count(),
            'completed_orders' => Order::whereBetween('created_at', $dateRange)->where('status', 'completed')->count(),
            'cancelled_orders' => Order::whereBetween('created_at', $dateRange)->where('status', 'cancelled')->count(),
            'average_order_value' => $this->getAverageOrderValue($dateRange),
            'order_frequency' => $this->getOrderFrequency($dateRange),
        ];
    }

    /**
     * Get customer analytics
     */
    private function getCustomerAnalytics(array $dateRange): array
    {
        return [
            'new_customers' => Customer::whereBetween('created_at', $dateRange)->count(),
            'returning_customers' => $this->getReturningCustomers($dateRange),
            'customer_retention_rate' => $this->getCustomerRetentionRate($dateRange),
        ];
    }

    /**
     * Get menu performance analytics
     */
    private function getMenuPerformanceAnalytics(array $dateRange): array
    {
        return [
            'top_selling_items' => $this->getTopSellingItems($dateRange, 5),
            'total_menu_items' => MenuItem::where('is_available', true)->count(),
        ];
    }

    /**
     * Get delivery analytics
     */
    private function getDeliveryAnalytics(array $dateRange): array
    {
        return [
            'total_deliveries' => DeliveryOrder::whereBetween('created_at', $dateRange)->count(),
            'successful_deliveries' => DeliveryOrder::whereBetween('created_at', $dateRange)->where('status', 'delivered')->count(),
            'average_delivery_time' => $this->getAverageDeliveryTime($dateRange),
        ];
    }

    /**
     * Get review analytics
     */
    private function getReviewAnalytics(array $dateRange): array
    {
        return [
            'total_reviews' => RestaurantReview::whereBetween('created_at', $dateRange)->count(),
            'average_rating' => RestaurantReview::whereBetween('created_at', $dateRange)->avg('rating') ?? 0,
        ];
    }

    /**
     * Helper methods for specific calculations
     */
    private function getPreviousPeriod(array $dateRange): array
    {
        $duration = $dateRange[1]->diffInDays($dateRange[0]);
        return [
            $dateRange[0]->copy()->subDays($duration + 1),
            $dateRange[0]->copy()->subDay()
        ];
    }

    private function getAverageOrderValue(array $dateRange): float
    {
        return Order::whereBetween('created_at', $dateRange)->avg('total_amount') ?? 0;
    }

    private function getTopSellingItems(array $dateRange, int $limit = 10): array
    {
        // This would require an order_items table to get actual data
        // For now, return placeholder data
        return [];
    }

    private function getReturningCustomers(array $dateRange): int
    {
        // This would require more complex logic to identify returning customers
        // For now, return placeholder data
        return 0;
    }

    private function getCustomerRetentionRate(array $dateRange): float
    {
        // This would require complex calculation based on customer behavior
        // For now, return placeholder data
        return 0;
    }

    private function getAverageDeliveryTime(array $dateRange): float
    {
        $deliveries = DeliveryOrder::whereBetween('created_at', $dateRange)
                                 ->whereNotNull('delivered_at')
                                 ->get();

        if ($deliveries->isEmpty()) {
            return 0;
        }

        $totalMinutes = 0;
        foreach ($deliveries as $delivery) {
            $totalMinutes += $delivery->created_at->diffInMinutes($delivery->delivered_at);
        }

        return $totalMinutes / $deliveries->count();
    }

    private function getOrderFrequency(array $dateRange): float
    {
        $days = $dateRange[1]->diffInDays($dateRange[0]) + 1;
        $totalOrders = Order::whereBetween('created_at', $dateRange)->count();
        
        return $days > 0 ? $totalOrders / $days : 0;
    }

    // Additional placeholder methods for complex analytics
    private function getRevenueData(array $dateRange, string $groupBy): array { return []; }
    private function getTotalSales(array $dateRange): array { return []; }
    private function getSalesByChannel(array $dateRange): array { return []; }
    private function getSalesByPaymentMethod(array $dateRange): array { return []; }
    private function getHourlySales(array $dateRange): array { return []; }
    private function getDailySales(array $dateRange): array { return []; }
    private function getCustomerLifetimeValue(): float { return 0; }
    private function getCustomerAcquisition(array $dateRange): array { return []; }
    private function getCustomerRetention(array $dateRange): array { return []; }
    private function getTopCustomers(array $dateRange): array { return []; }
    private function getLowPerformingItems(array $dateRange): array { return []; }
    private function getCategoryPerformance(array $dateRange): array { return []; }
    private function getItemProfitability(array $dateRange): array { return []; }
    private function getMenuItemTrends(array $dateRange): array { return []; }
    private function getStaffProductivity(array $dateRange): array { return []; }
    private function getAttendanceRate(array $dateRange): float { return 0; }
    private function getTopPerformers(array $dateRange): array { return []; }
    private function getShiftCoverage(array $dateRange): array { return []; }
    private function getDeliverySuccessRate(array $dateRange): float { return 0; }
    private function getDeliveryAreas(array $dateRange): array { return []; }
    private function getDriverPerformance(array $dateRange): array { return []; }
    private function getRatingDistribution(array $dateRange): array { return []; }
    private function getReviewTrends(array $dateRange): array { return []; }
    private function getSentimentAnalysis(array $dateRange): array { return []; }
    private function getActiveCustomers(): int { return 0; }
    private function getHourlyOrders(): array { return []; }
}
