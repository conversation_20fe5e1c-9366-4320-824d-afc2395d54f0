<?php

namespace App\Http\Controllers\Api\Tenant;

use App\Http\Controllers\Controller;
use App\Models\RestaurantReview;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;

class RestaurantReviewController extends Controller
{
    public function __construct()
    {
        $this->middleware('auth:sanctum')->except(['index', 'show', 'getStatistics']);
        $this->middleware('role:admin|restaurant_manager')->only(['update', 'destroy', 'toggleStatus']);
    }

    /**
     * Display a listing of restaurant reviews
     */
    public function index(Request $request): JsonResponse
    {
        $query = RestaurantReview::with(['customer', 'order']);

        // Apply filters
        if ($request->filled('rating')) {
            $query->where('rating', $request->rating);
        }

        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }

        if ($request->filled('customer_id')) {
            $query->where('customer_id', $request->customer_id);
        }

        if ($request->filled('date_from')) {
            $query->whereDate('created_at', '>=', $request->date_from);
        }

        if ($request->filled('date_to')) {
            $query->whereDate('created_at', '<=', $request->date_to);
        }

        if ($request->filled('search')) {
            $query->where(function ($q) use ($request) {
                $q->where('review', 'like', "%{$request->search}%")
                  ->orWhereHas('customer', function ($customerQuery) use ($request) {
                      $customerQuery->where('name', 'like', "%{$request->search}%");
                  });
            });
        }

        // Apply sorting
        $sortField = $request->get('sort', 'created_at');
        $sortDirection = $request->get('direction', 'desc');
        $query->orderBy($sortField, $sortDirection);

        $reviews = $query->paginate($request->get('per_page', 15));

        return response()->json([
            'success' => true,
            'data' => $reviews,
        ]);
    }

    /**
     * Store a newly created review
     */
    public function store(Request $request): JsonResponse
    {
        $validated = $request->validate([
            'customer_id' => 'required|exists:customers,id',
            'order_id' => 'nullable|exists:orders,id',
            'rating' => 'required|integer|min:1|max:5',
            'review' => 'required|string|max:1000',
            'food_quality' => 'nullable|integer|min:1|max:5',
            'service_quality' => 'nullable|integer|min:1|max:5',
            'delivery_quality' => 'nullable|integer|min:1|max:5',
            'value_for_money' => 'nullable|integer|min:1|max:5',
        ]);

        // Check if customer already reviewed this order
        if ($validated['order_id']) {
            $existingReview = RestaurantReview::where('customer_id', $validated['customer_id'])
                                            ->where('order_id', $validated['order_id'])
                                            ->first();

            if ($existingReview) {
                return response()->json([
                    'success' => false,
                    'message' => 'You have already reviewed this order.',
                ], 422);
            }
        }

        $review = RestaurantReview::create($validated);
        $review->load(['customer', 'order']);

        return response()->json([
            'success' => true,
            'message' => 'Review submitted successfully.',
            'data' => $review,
        ], 201);
    }

    /**
     * Display the specified review
     */
    public function show(RestaurantReview $restaurantReview): JsonResponse
    {
        $restaurantReview->load(['customer', 'order']);

        return response()->json([
            'success' => true,
            'data' => $restaurantReview,
        ]);
    }

    /**
     * Update the specified review
     */
    public function update(Request $request, RestaurantReview $restaurantReview): JsonResponse
    {
        $validated = $request->validate([
            'rating' => 'integer|min:1|max:5',
            'review' => 'string|max:1000',
            'food_quality' => 'nullable|integer|min:1|max:5',
            'service_quality' => 'nullable|integer|min:1|max:5',
            'delivery_quality' => 'nullable|integer|min:1|max:5',
            'value_for_money' => 'nullable|integer|min:1|max:5',
            'status' => 'in:pending,approved,rejected',
            'admin_response' => 'nullable|string|max:500',
        ]);

        $restaurantReview->update($validated);
        $restaurantReview->load(['customer', 'order']);

        return response()->json([
            'success' => true,
            'message' => 'Review updated successfully.',
            'data' => $restaurantReview,
        ]);
    }

    /**
     * Remove the specified review
     */
    public function destroy(RestaurantReview $restaurantReview): JsonResponse
    {
        $restaurantReview->delete();

        return response()->json([
            'success' => true,
            'message' => 'Review deleted successfully.',
        ]);
    }

    /**
     * Toggle review status
     */
    public function toggleStatus(RestaurantReview $restaurantReview): JsonResponse
    {
        $newStatus = $restaurantReview->status === 'approved' ? 'pending' : 'approved';
        $restaurantReview->update(['status' => $newStatus]);

        return response()->json([
            'success' => true,
            'message' => "Review {$newStatus} successfully.",
            'data' => $restaurantReview,
        ]);
    }

    /**
     * Approve review
     */
    public function approve(RestaurantReview $restaurantReview): JsonResponse
    {
        $restaurantReview->update(['status' => 'approved']);

        return response()->json([
            'success' => true,
            'message' => 'Review approved successfully.',
            'data' => $restaurantReview,
        ]);
    }

    /**
     * Reject review
     */
    public function reject(Request $request, RestaurantReview $restaurantReview): JsonResponse
    {
        $validated = $request->validate([
            'admin_response' => 'nullable|string|max:500',
        ]);

        $restaurantReview->update([
            'status' => 'rejected',
            'admin_response' => $validated['admin_response'] ?? null,
        ]);

        return response()->json([
            'success' => true,
            'message' => 'Review rejected successfully.',
            'data' => $restaurantReview,
        ]);
    }

    /**
     * Add admin response to review
     */
    public function addResponse(Request $request, RestaurantReview $restaurantReview): JsonResponse
    {
        $validated = $request->validate([
            'admin_response' => 'required|string|max:500',
        ]);

        $restaurantReview->update($validated);

        return response()->json([
            'success' => true,
            'message' => 'Response added successfully.',
            'data' => $restaurantReview,
        ]);
    }

    /**
     * Get review statistics
     */
    public function getStatistics(Request $request): JsonResponse
    {
        $query = RestaurantReview::query();

        // Apply date filter
        if ($request->filled('date_from')) {
            $query->whereDate('created_at', '>=', $request->date_from);
        }

        if ($request->filled('date_to')) {
            $query->whereDate('created_at', '<=', $request->date_to);
        }

        $stats = [
            'total_reviews' => $query->count(),
            'approved_reviews' => (clone $query)->where('status', 'approved')->count(),
            'pending_reviews' => (clone $query)->where('status', 'pending')->count(),
            'rejected_reviews' => (clone $query)->where('status', 'rejected')->count(),
            'average_rating' => (clone $query)->where('status', 'approved')->avg('rating') ?? 0,
            'rating_distribution' => [
                '5_star' => (clone $query)->where('status', 'approved')->where('rating', 5)->count(),
                '4_star' => (clone $query)->where('status', 'approved')->where('rating', 4)->count(),
                '3_star' => (clone $query)->where('status', 'approved')->where('rating', 3)->count(),
                '2_star' => (clone $query)->where('status', 'approved')->where('rating', 2)->count(),
                '1_star' => (clone $query)->where('status', 'approved')->where('rating', 1)->count(),
            ],
            'quality_ratings' => [
                'food_quality' => (clone $query)->where('status', 'approved')->avg('food_quality') ?? 0,
                'service_quality' => (clone $query)->where('status', 'approved')->avg('service_quality') ?? 0,
                'delivery_quality' => (clone $query)->where('status', 'approved')->avg('delivery_quality') ?? 0,
                'value_for_money' => (clone $query)->where('status', 'approved')->avg('value_for_money') ?? 0,
            ],
        ];

        return response()->json([
            'success' => true,
            'data' => $stats,
        ]);
    }

    /**
     * Get recent reviews
     */
    public function getRecent(Request $request): JsonResponse
    {
        $limit = $request->get('limit', 10);

        $reviews = RestaurantReview::with(['customer'])
                                 ->where('status', 'approved')
                                 ->latest()
                                 ->limit($limit)
                                 ->get();

        return response()->json([
            'success' => true,
            'data' => $reviews,
        ]);
    }

    /**
     * Get top rated reviews
     */
    public function getTopRated(Request $request): JsonResponse
    {
        $limit = $request->get('limit', 10);

        $reviews = RestaurantReview::with(['customer'])
                                 ->where('status', 'approved')
                                 ->where('rating', '>=', 4)
                                 ->latest()
                                 ->limit($limit)
                                 ->get();

        return response()->json([
            'success' => true,
            'data' => $reviews,
        ]);
    }

    /**
     * Bulk update reviews
     */
    public function bulkUpdate(Request $request): JsonResponse
    {
        $validated = $request->validate([
            'action' => 'required|in:approve,reject,delete',
            'review_ids' => 'required|array|min:1',
            'review_ids.*' => 'exists:restaurant_reviews,id',
            'admin_response' => 'nullable|string|max:500',
        ]);

        $reviews = RestaurantReview::whereIn('id', $validated['review_ids']);

        switch ($validated['action']) {
            case 'approve':
                $reviews->update(['status' => 'approved']);
                $message = 'Selected reviews approved successfully.';
                break;

            case 'reject':
                $updateData = ['status' => 'rejected'];
                if (isset($validated['admin_response'])) {
                    $updateData['admin_response'] = $validated['admin_response'];
                }
                $reviews->update($updateData);
                $message = 'Selected reviews rejected successfully.';
                break;

            case 'delete':
                $reviews->delete();
                $message = 'Selected reviews deleted successfully.';
                break;
        }

        return response()->json([
            'success' => true,
            'message' => $message,
        ]);
    }
}
