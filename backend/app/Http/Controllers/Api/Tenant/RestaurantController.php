<?php

namespace App\Http\Controllers\Api\Tenant;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;

class RestaurantController extends Controller
{
    /**
     * Get restaurant information
     */
    public function show(): JsonResponse
    {
        $tenant = tenant();
        
        $restaurant = [
            'id' => $tenant->id,
            'name' => $tenant->name,
            'description' => $tenant->description,
            'phone' => $tenant->phone,
            'email' => $tenant->email,
            'website' => $tenant->website,
            'address' => [
                'street' => $tenant->address,
                'city' => $tenant->city,
                'state' => $tenant->state,
                'postal_code' => $tenant->postal_code,
                'country' => $tenant->country,
                'full_address' => trim("{$tenant->address}, {$tenant->city}, {$tenant->state} {$tenant->postal_code}"),
            ],
            'location' => [
                'latitude' => $tenant->latitude,
                'longitude' => $tenant->longitude,
            ],
            'settings' => [
                'currency' => $tenant->currency ?? 'USD',
                'timezone' => $tenant->timezone ?? config('app.timezone'),
                'language' => $tenant->language ?? 'en',
                'delivery_enabled' => $tenant->delivery_enabled ?? true,
                'takeout_enabled' => $tenant->takeout_enabled ?? true,
                'dine_in_enabled' => $tenant->dine_in_enabled ?? true,
                'reservation_enabled' => $tenant->reservation_enabled ?? true,
                'online_ordering_enabled' => $tenant->online_ordering_enabled ?? true,
                'delivery_radius' => $tenant->delivery_radius ?? 10,
                'minimum_order_amount' => $tenant->minimum_order_amount ?? 0,
                'delivery_fee' => $tenant->delivery_fee ?? 0,
                'service_charge' => $tenant->service_charge ?? 0,
                'tax_rate' => $tenant->tax_rate ?? 0,
            ],
            'operating_hours' => $this->getOperatingHours($tenant),
            'payment_methods' => $this->getPaymentMethods($tenant),
            'branding' => [
                'logo' => $tenant->logo ? asset('storage/' . $tenant->logo) : null,
                'favicon' => $tenant->favicon ? asset('storage/' . $tenant->favicon) : null,
                'primary_color' => $tenant->primary_color ?? '#3B82F6',
                'secondary_color' => $tenant->secondary_color ?? '#10B981',
                'accent_color' => $tenant->accent_color ?? '#F59E0B',
            ],
            'features' => [
                'loyalty_program' => true,
                'table_reservations' => $tenant->reservation_enabled ?? true,
                'online_ordering' => $tenant->online_ordering_enabled ?? true,
                'delivery_tracking' => true,
                'reviews_enabled' => true,
                'promotions_enabled' => true,
            ],
            'social_media' => [
                'facebook' => $tenant->facebook_url,
                'instagram' => $tenant->instagram_url,
                'twitter' => $tenant->twitter_url,
                'youtube' => $tenant->youtube_url,
            ],
            'contact_info' => [
                'support_email' => $tenant->notification_email ?? $tenant->email,
                'support_phone' => $tenant->notification_phone ?? $tenant->phone,
                'emergency_contact' => $tenant->emergency_contact,
            ],
        ];

        return response()->json([
            'success' => true,
            'data' => $restaurant,
        ]);
    }

    /**
     * Get restaurant status (open/closed)
     */
    public function status(): JsonResponse
    {
        $tenant = tenant();
        $currentTime = now($tenant->timezone ?? config('app.timezone'));
        $currentDay = strtolower($currentTime->format('l'));
        
        $operatingHours = $tenant->opening_hours ?? [];
        $todayHours = $operatingHours[$currentDay] ?? null;
        
        $isOpen = false;
        $nextOpenTime = null;
        $closingTime = null;
        
        if ($todayHours && $todayHours['is_open']) {
            $openTime = $currentTime->copy()->setTimeFromTimeString($todayHours['open_time']);
            $closeTime = $currentTime->copy()->setTimeFromTimeString($todayHours['close_time']);
            
            // Handle overnight hours (e.g., 22:00 - 02:00)
            if ($closeTime->lt($openTime)) {
                $closeTime->addDay();
            }
            
            $isOpen = $currentTime->between($openTime, $closeTime);
            $closingTime = $isOpen ? $closeTime : null;
        }
        
        // Find next opening time if currently closed
        if (!$isOpen) {
            $nextOpenTime = $this->getNextOpenTime($tenant, $currentTime);
        }

        return response()->json([
            'success' => true,
            'data' => [
                'is_open' => $isOpen,
                'current_time' => $currentTime->format('Y-m-d H:i:s'),
                'timezone' => $tenant->timezone ?? config('app.timezone'),
                'closing_time' => $closingTime?->format('Y-m-d H:i:s'),
                'next_open_time' => $nextOpenTime,
                'status_message' => $isOpen ? 'We are currently open!' : 'We are currently closed.',
                'today_hours' => $todayHours,
            ],
        ]);
    }

    /**
     * Get restaurant menu categories
     */
    public function menuCategories(): JsonResponse
    {
        $categories = \App\Models\FoodCategory::active()
                                            ->parent()
                                            ->with(['subcategories' => function ($query) {
                                                $query->active()->ordered();
                                            }])
                                            ->ordered()
                                            ->get();

        return response()->json([
            'success' => true,
            'data' => $categories,
        ]);
    }

    /**
     * Get restaurant delivery areas
     */
    public function deliveryAreas(): JsonResponse
    {
        $tenant = tenant();
        
        $deliveryInfo = [
            'enabled' => $tenant->delivery_enabled ?? true,
            'radius' => $tenant->delivery_radius ?? 10,
            'minimum_order' => $tenant->minimum_order_amount ?? 0,
            'delivery_fee' => $tenant->delivery_fee ?? 0,
            'free_delivery_threshold' => $tenant->free_delivery_threshold ?? null,
            'estimated_time' => [
                'min' => 20,
                'max' => 45,
            ],
            'center_location' => [
                'latitude' => $tenant->latitude,
                'longitude' => $tenant->longitude,
                'address' => $tenant->address,
            ],
        ];

        return response()->json([
            'success' => true,
            'data' => $deliveryInfo,
        ]);
    }

    /**
     * Check if address is in delivery area
     */
    public function checkDeliveryArea(Request $request): JsonResponse
    {
        $request->validate([
            'latitude' => 'required|numeric|between:-90,90',
            'longitude' => 'required|numeric|between:-180,180',
        ]);

        $tenant = tenant();
        
        if (!$tenant->delivery_enabled) {
            return response()->json([
                'success' => false,
                'message' => 'Delivery is not available.',
                'data' => ['in_delivery_area' => false],
            ]);
        }

        $distance = $this->calculateDistance(
            $tenant->latitude,
            $tenant->longitude,
            $request->latitude,
            $request->longitude
        );

        $deliveryRadius = $tenant->delivery_radius ?? 10;
        $inDeliveryArea = $distance <= $deliveryRadius;
        
        $deliveryFee = $tenant->delivery_fee ?? 0;
        $estimatedTime = $this->calculateDeliveryTime($distance);

        return response()->json([
            'success' => true,
            'data' => [
                'in_delivery_area' => $inDeliveryArea,
                'distance' => round($distance, 2),
                'delivery_radius' => $deliveryRadius,
                'delivery_fee' => $deliveryFee,
                'estimated_delivery_time' => $estimatedTime,
            ],
        ]);
    }

    /**
     * Get operating hours
     */
    private function getOperatingHours($tenant): array
    {
        $defaultHours = [
            'monday' => ['is_open' => true, 'open_time' => '09:00', 'close_time' => '22:00'],
            'tuesday' => ['is_open' => true, 'open_time' => '09:00', 'close_time' => '22:00'],
            'wednesday' => ['is_open' => true, 'open_time' => '09:00', 'close_time' => '22:00'],
            'thursday' => ['is_open' => true, 'open_time' => '09:00', 'close_time' => '22:00'],
            'friday' => ['is_open' => true, 'open_time' => '09:00', 'close_time' => '23:00'],
            'saturday' => ['is_open' => true, 'open_time' => '09:00', 'close_time' => '23:00'],
            'sunday' => ['is_open' => true, 'open_time' => '10:00', 'close_time' => '21:00'],
        ];

        return $tenant->opening_hours ?? $defaultHours;
    }

    /**
     * Get payment methods
     */
    private function getPaymentMethods($tenant): array
    {
        $methods = [];

        if ($tenant->cash_enabled ?? true) {
            $methods[] = ['type' => 'cash', 'name' => 'Cash', 'enabled' => true];
        }

        if ($tenant->card_enabled ?? true) {
            $methods[] = ['type' => 'card', 'name' => 'Credit/Debit Card', 'enabled' => true];
        }

        if ($tenant->online_payment_enabled ?? false) {
            if ($tenant->stripe_enabled ?? false) {
                $methods[] = ['type' => 'stripe', 'name' => 'Stripe', 'enabled' => true];
            }
            
            if ($tenant->paypal_enabled ?? false) {
                $methods[] = ['type' => 'paypal', 'name' => 'PayPal', 'enabled' => true];
            }
        }

        return $methods;
    }

    /**
     * Get next opening time
     */
    private function getNextOpenTime($tenant, $currentTime): ?string
    {
        $operatingHours = $tenant->opening_hours ?? [];
        $daysOfWeek = ['monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday', 'sunday'];
        
        for ($i = 0; $i < 7; $i++) {
            $checkDate = $currentTime->copy()->addDays($i);
            $dayName = strtolower($checkDate->format('l'));
            
            if (isset($operatingHours[$dayName]) && $operatingHours[$dayName]['is_open']) {
                $openTime = $checkDate->setTimeFromTimeString($operatingHours[$dayName]['open_time']);
                
                if ($openTime->gt($currentTime)) {
                    return $openTime->format('Y-m-d H:i:s');
                }
            }
        }

        return null;
    }

    /**
     * Calculate distance between two coordinates
     */
    private function calculateDistance($lat1, $lon1, $lat2, $lon2): float
    {
        $earthRadius = 6371; // km

        $dLat = deg2rad($lat2 - $lat1);
        $dLon = deg2rad($lon2 - $lon1);

        $a = sin($dLat/2) * sin($dLat/2) + cos(deg2rad($lat1)) * cos(deg2rad($lat2)) * sin($dLon/2) * sin($dLon/2);
        $c = 2 * atan2(sqrt($a), sqrt(1-$a));

        return $earthRadius * $c;
    }

    /**
     * Calculate estimated delivery time
     */
    private function calculateDeliveryTime($distance): array
    {
        $baseTime = 20; // minutes
        $travelTime = ($distance / 30) * 60; // assuming 30 km/h average speed
        
        $totalTime = $baseTime + $travelTime;
        
        return [
            'min' => (int) $totalTime,
            'max' => (int) ($totalTime + 15),
        ];
    }
}
