<?php

namespace App\Http\Controllers;

use App\Models\Blog;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\View;
use Illuminate\Support\Str;

class BlogController extends Controller
{
    /**
     * Display a listing of published blogs
     */
    public function index(Request $request)
    {
        $query = Blog::published()->with(['featuredImage', 'author']);

        // Search functionality
        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->where('title', 'like', "%{$search}%")
                  ->orWhere('content', 'like', "%{$search}%")
                  ->orWhere('excerpt', 'like', "%{$search}%");
            });
        }

        // Filter by tags
        if ($request->filled('tag')) {
            $query->whereJsonContains('tags', $request->tag);
        }

        // Filter by category
        if ($request->filled('category')) {
            $query->whereJsonContains('categories', $request->category);
        }

        $blogs = $query->ordered()->paginate(12);

        // Get all unique tags and categories for filtering
        $allTags = Blog::published()
            ->whereNotNull('tags')
            ->pluck('tags')
            ->flatten()
            ->unique()
            ->sort()
            ->values();

        $allCategories = Blog::published()
            ->whereNotNull('categories')
            ->pluck('categories')
            ->flatten()
            ->unique()
            ->sort()
            ->values();

        $seoData = [
            'title' => __('blogs.title') . ' - ' . config('app.name'),
            'description' => __('blogs.meta_description_index', ['site' => config('app.name')]),
            'canonical' => url('/blog'),
            'og_type' => 'website',
        ];

        return view('blog.index', compact('blogs', 'allTags', 'allCategories', 'seoData'));
    }

    /**
     * Display the specified blog post
     */
    public function show(Request $request, $slug)
    {
        $blog = Blog::published()
            ->with(['featuredImage', 'author', 'creator'])
            ->where('slug', $slug)
            ->firstOrFail();

        // Get related blogs (same tags or categories)
        $relatedBlogs = Blog::published()
            ->with(['featuredImage', 'author'])
            ->where('id', '!=', $blog->id)
            ->where(function ($query) use ($blog) {
                if ($blog->tags) {
                    foreach ($blog->tags as $tag) {
                        $query->orWhereJsonContains('tags', $tag);
                    }
                }
                if ($blog->categories) {
                    foreach ($blog->categories as $category) {
                        $query->orWhereJsonContains('categories', $category);
                    }
                }
            })
            ->ordered()
            ->limit(3)
            ->get();

        // SEO data for this specific blog post
        $seoData = [
            'title' => $blog->title . ' - ' . config('app.name'),
            'description' => $blog->excerpt ?: Str::limit(strip_tags($blog->content), 160),
            'canonical' => url('/blog/' . $blog->slug),
            'og_type' => 'article',
            'og_image' => $blog->featured_image_url,
            'article_author' => $blog->author?->name,
            'article_published_time' => $blog->published_at?->toISOString(),
            'article_modified_time' => $blog->updated_at?->toISOString(),
            'article_tags' => $blog->tags,
            'article_section' => $blog->categories?->first(),
        ];

        // JSON-LD structured data
        $jsonLd = [
            '@context' => 'https://schema.org',
            '@type' => 'Article',
            'headline' => $blog->title,
            'description' => $blog->excerpt ?: Str::limit(strip_tags($blog->content), 160),
            'image' => $blog->featured_image_url ? [
                '@type' => 'ImageObject',
                'url' => $blog->featured_image_url,
            ] : null,
            'author' => [
                '@type' => 'Person',
                'name' => $blog->author?->name ?: 'Anonymous',
            ],
            'publisher' => [
                '@type' => 'Organization',
                'name' => config('app.name'),
                'logo' => [
                    '@type' => 'ImageObject',
                    'url' => asset('images/logo.png'),
                ],
            ],
            'datePublished' => $blog->published_at?->toISOString(),
            'dateModified' => $blog->updated_at?->toISOString(),
            'mainEntityOfPage' => [
                '@type' => 'WebPage',
                '@id' => url('/blog/' . $blog->slug),
            ],
            'keywords' => $blog->tags ? implode(', ', $blog->tags) : null,
            'articleSection' => $blog->categories ? implode(', ', $blog->categories) : null,
        ];

        // Breadcrumb JSON-LD
        $breadcrumbJsonLd = [
            '@context' => 'https://schema.org',
            '@type' => 'BreadcrumbList',
            'itemListElement' => [
                [
                    '@type' => 'ListItem',
                    'position' => 1,
                    'name' => 'Home',
                    'item' => url('/'),
                ],
                [
                    '@type' => 'ListItem',
                    'position' => 2,
                    'name' => __('blogs.title'),
                    'item' => url('/blog'),
                ],
                [
                    '@type' => 'ListItem',
                    'position' => 3,
                    'name' => $blog->title,
                    'item' => url('/blog/' . $blog->slug),
                ],
            ],
        ];

        return view('blog.show', compact('blog', 'relatedBlogs', 'seoData', 'jsonLd', 'breadcrumbJsonLd'));
    }

    /**
     * Display blogs by tag
     */
    public function tag(Request $request, $tag)
    {
        $blogs = Blog::published()
            ->with(['featuredImage', 'author'])
            ->whereJsonContains('tags', $tag)
            ->ordered()
            ->paginate(12);

        $seoData = [
            'title' => __('blogs.tag_title', ['tag' => $tag]) . ' - ' . config('app.name'),
            'description' => __('blogs.tag_description', ['tag' => $tag, 'site' => config('app.name')]),
            'canonical' => url('/blog/tag/' . $tag),
            'og_type' => 'website',
        ];

        return view('blog.tag', compact('blogs', 'tag', 'seoData'));
    }

    /**
     * Display blogs by category
     */
    public function category(Request $request, $category)
    {
        $blogs = Blog::published()
            ->with(['featuredImage', 'author'])
            ->whereJsonContains('categories', $category)
            ->ordered()
            ->paginate(12);

        $seoData = [
            'title' => __('blogs.category_title', ['category' => $category]) . ' - ' . config('app.name'),
            'description' => __('blogs.category_description', ['category' => $category, 'site' => config('app.name')]),
            'canonical' => url('/blog/category/' . $category),
            'og_type' => 'website',
        ];

        return view('blog.category', compact('blogs', 'category', 'seoData'));
    }
}
