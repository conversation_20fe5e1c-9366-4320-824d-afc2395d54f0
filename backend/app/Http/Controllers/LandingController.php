<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Inertia\Inertia;
use App\Models\SubscriptionPlan;
use App\Models\Tenant;
use App\Models\Domain;
use Illuminate\Support\Str;
use Illuminate\Validation\ValidationException;

class LandingController extends Controller
{
    /**
     * Show the landing page
     */
    public function index()
    {
        try {
            $plans = SubscriptionPlan::active()->ordered()->get();
        } catch (\Exception $e) {
            // If database query fails, use empty array
            $plans = collect([]);
        }

        return Inertia::render('Landing/Index', [
            'plans' => $plans,
        ]);
    }

    /**
     * Show the pricing page
     */
    public function pricing()
    {
        return Inertia::render('Landing/Pricing', [
            'plans' => SubscriptionPlan::active()->ordered()->get(),
        ]);
    }

    /**
     * Show the features page
     */
    public function features()
    {
        return Inertia::render('Landing/Features');
    }

    /**
     * Register a new tenant
     */
    public function registerTenant(Request $request)
    {
        $request->validate([
            'restaurant_name' => 'required|string|max:255',
            'subdomain' => 'required|string|max:50|alpha_dash|unique:domains,domain',
            'owner_name' => 'required|string|max:255',
            'email' => 'required|email|unique:tenants,email',
            'phone' => 'nullable|string|max:20',
            'plan_id' => 'required|exists:subscription_plans,id',
        ]);

        // Check if subdomain is available
        $fullDomain = $request->subdomain . '.myapp.com';
        if (Domain::where('domain', $fullDomain)->exists()) {
            throw ValidationException::withMessages([
                'subdomain' => 'This subdomain is already taken.',
            ]);
        }

        try {
            // Create tenant
            $tenant = Tenant::create([
                'id' => Str::uuid(),
                'name' => $request->restaurant_name,
                'email' => $request->email,
                'phone' => $request->phone,
                'subscription_plan_id' => $request->plan_id,
                'subscription_status' => 'trial',
                'trial_ends_at' => now()->addDays(14), // 14-day trial
            ]);

            // Create domain
            $tenant->domains()->create([
                'domain' => $fullDomain,
                'is_primary' => true,
            ]);

            return redirect()->route('landing')->with('success',
                'Restaurant registered successfully! You can now access your dashboard at ' . $fullDomain
            );

        } catch (\Exception $e) {
            return back()->withErrors([
                'general' => 'Failed to register restaurant. Please try again.',
            ]);
        }
    }
}
