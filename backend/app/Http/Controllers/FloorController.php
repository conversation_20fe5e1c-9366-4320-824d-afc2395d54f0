<?php

namespace App\Http\Controllers;

use App\Models\Tenant\Floor;
use App\Models\Tenant\Branch;
use Illuminate\Http\Request;
use Inertia\Inertia;

class FloorController extends Controller
{
    /**
     * Display a listing of floors.
     */
    public function index(Request $request)
    {
        $query = Floor::with(['branch', 'tables'])
            ->when($request->branch_id, function ($query, $branchId) {
                return $query->where('branch_id', $branchId);
            })
            ->when($request->search, function ($query, $search) {
                return $query->where('name', 'like', "%{$search}%")
                    ->orWhere('description', 'like', "%{$search}%");
            })
            ->when($request->status !== null, function ($query) use ($request) {
                return $query->where('is_active', $request->status);
            });

        $floors = $query->ordered()->paginate(15)->withQueryString();

        $branches = Branch::active()->ordered()->get(['id', 'name']);

        return Inertia::render('Tenant/Floors/Index', [
            'floors' => $floors,
            'branches' => $branches,
            'filters' => $request->only(['search', 'branch_id', 'status']),
        ]);
    }

    /**
     * Show the form for creating a new floor.
     */
    public function create()
    {
        $branches = Branch::active()->ordered()->get(['id', 'name']);

        return Inertia::render('Tenant/Floors/Create', [
            'branches' => $branches,
        ]);
    }

    /**
     * Store a newly created floor in storage.
     */
    public function store(Request $request)
    {
        $validated = $request->validate([
            'name' => ['required', 'string', 'max:255'],
            'description' => ['nullable', 'string', 'max:1000'],
            'branch_id' => ['required', 'exists:branches,id'],
            'sort_order' => ['nullable', 'integer', 'min:0'],
            'is_active' => ['boolean'],
        ]);

        $validated['sort_order'] = $validated['sort_order'] ?? Floor::max('sort_order') + 1;

        Floor::create($validated);

        return redirect()->route('manager.floors.index')
            ->with('success', __('floors.created_successfully'));
    }

    /**
     * Display the specified floor.
     */
    public function show(Floor $floor)
    {
        $floor->load(['branch', 'tables']);

        return Inertia::render('Tenant/Floors/Show', [
            'floor' => $floor,
        ]);
    }

    /**
     * Show the form for editing the specified floor.
     */
    public function edit(Floor $floor)
    {
        $branches = Branch::active()->ordered()->get(['id', 'name']);

        return Inertia::render('Tenant/Floors/Edit', [
            'floor' => $floor,
            'branches' => $branches,
        ]);
    }

    /**
     * Update the specified floor in storage.
     */
    public function update(Request $request, Floor $floor)
    {
        $validated = $request->validate([
            'name' => ['required', 'string', 'max:255'],
            'description' => ['nullable', 'string', 'max:1000'],
            'branch_id' => ['required', 'exists:branches,id'],
            'sort_order' => ['nullable', 'integer', 'min:0'],
            'is_active' => ['boolean'],
        ]);

        $floor->update($validated);

        return redirect()->route('manager.floors.index')
            ->with('success', __('floors.updated_successfully'));
    }

    /**
     * Remove the specified floor from storage.
     */
    public function destroy(Floor $floor)
    {
        // Check if floor has tables
        if ($floor->tables()->count() > 0) {
            return back()->with('error', __('floors.cannot_delete_has_tables'));
        }

        $floor->delete();

        return redirect()->route('manager.floors.index')
            ->with('success', __('floors.deleted_successfully'));
    }

    /**
     * Get floors for a specific branch (API endpoint).
     */
    public function getByBranch($branchId)
    {
        $floors = Floor::where('branch_id', $branchId)
            ->active()
            ->ordered()
            ->with(['tables' => function ($query) {
                $query->active()->ordered();
            }])
            ->get();

        return response()->json($floors);
    }
}
