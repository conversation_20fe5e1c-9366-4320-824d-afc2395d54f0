<?php

namespace App\Http\Controllers\Marketing;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Mail;
use App\Mail\ContactFormSubmitted;

class ContactController extends Controller
{
    /**
     * Display the contact form
     */
    public function index()
    {
        return view('marketing.contact.index');
    }

    /**
     * Handle contact form submission
     */
    public function submit(Request $request)
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'email' => 'required|email|max:255',
            'subject' => 'required|string|max:255',
            'message' => 'required|string|max:2000',
        ]);

        $contactData = $request->only(['name', 'email', 'subject', 'message']);

        try {
            // Send email to admin
            Mail::to(config('mail.admin_email', '<EMAIL>'))
                ->send(new ContactFormSubmitted($contactData));

            return redirect()->route('marketing.contact')
                           ->with('success', __('contact.message_sent_successfully'));
        } catch (\Exception $e) {
            return redirect()->route('marketing.contact')
                           ->with('error', __('contact.message_send_failed'))
                           ->withInput();
        }
    }
}
