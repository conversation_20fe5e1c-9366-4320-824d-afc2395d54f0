<?php

namespace App\Http\Controllers\Marketing;

use App\Http\Controllers\Controller;
use App\Models\DynamicPage;
use Illuminate\Http\Request;

class PageController extends Controller
{
    /**
     * Display the specified dynamic page
     */
    public function show($slug)
    {
        $page = DynamicPage::active()
                          ->where('slug', $slug)
                          ->firstOrFail();

        return view('marketing.pages.show', compact('page'));
    }
}
