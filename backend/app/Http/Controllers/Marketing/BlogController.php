<?php

namespace App\Http\Controllers\Marketing;

use App\Http\Controllers\Controller;
use App\Models\Blog;
use Illuminate\Http\Request;

class BlogController extends Controller
{
    /**
     * Display a listing of published blogs
     */
    public function index(Request $request)
    {
        $query = Blog::published()->with(['featuredImage', 'author']);

        // Search
        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->where('title', 'like', "%{$search}%")
                  ->orWhere('content', 'like', "%{$search}%")
                  ->orWhere('excerpt', 'like', "%{$search}%");
            });
        }

        // Filter by category
        if ($request->filled('category')) {
            $query->whereJsonContains('categories', $request->category);
        }

        // Filter by tag
        if ($request->filled('tag')) {
            $query->whereJsonContains('tags', $request->tag);
        }

        $blogs = $query->ordered()->paginate(12)->withQueryString();

        // Get all categories and tags for filters
        $allBlogs = Blog::published()->get();
        $categories = $allBlogs->pluck('categories')->flatten()->unique()->filter()->values();
        $tags = $allBlogs->pluck('tags')->flatten()->unique()->filter()->values();

        return view('marketing.blogs.index', compact('blogs', 'categories', 'tags'));
    }

    /**
     * Display the specified blog
     */
    public function show($slug)
    {
        $blog = Blog::published()
                   ->with(['featuredImage', 'author'])
                   ->where('slug', $slug)
                   ->firstOrFail();

        // Get related blogs (same categories or tags)
        $relatedBlogs = Blog::published()
                           ->with(['featuredImage', 'author'])
                           ->where('id', '!=', $blog->id)
                           ->where(function ($query) use ($blog) {
                               if ($blog->categories) {
                                   foreach ($blog->categories as $category) {
                                       $query->orWhereJsonContains('categories', $category);
                                   }
                               }
                               if ($blog->tags) {
                                   foreach ($blog->tags as $tag) {
                                       $query->orWhereJsonContains('tags', $tag);
                                   }
                               }
                           })
                           ->ordered()
                           ->limit(3)
                           ->get();

        return view('marketing.blogs.show', compact('blog', 'relatedBlogs'));
    }
}
