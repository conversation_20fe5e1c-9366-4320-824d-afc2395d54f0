<?php

namespace App\Http\Controllers\Marketing;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\SubscriptionPlan;

class HomeController extends Controller
{
    /**
     * Display the marketing homepage
     */
    public function index()
    {
        $plans = SubscriptionPlan::where('is_active', true)
                                ->orderBy('sort_order')
                                ->get();

        $stats = [
            'restaurants' => 150,
            'orders_processed' => 25000,
            'revenue_generated' => 5000000,
            'satisfied_customers' => 98,
        ];

        return view('marketing.home', compact('plans', 'stats'));
    }

    /**
     * Display the plans page
     */
    public function plans()
    {
        $plans = SubscriptionPlan::where('is_active', true)
                                ->orderBy('sort_order')
                                ->get();

        return view('marketing.plans', compact('plans'));
    }

    /**
     * Display the features page
     */
    public function features()
    {
        $features = [
            'pos' => [
                'title' => 'Point of Sale System',
                'description' => 'Complete POS solution with table management, order processing, and payment integration',
                'icon' => 'cash-register',
                'benefits' => [
                    'Touch-friendly interface for tablets',
                    'Real-time order tracking',
                    'Multiple payment methods',
                    'Receipt printing and email',
                ]
            ],
            'inventory' => [
                'title' => 'Inventory Management',
                'description' => 'Track ingredients, manage suppliers, and automate reordering',
                'icon' => 'warehouse',
                'benefits' => [
                    'Real-time stock tracking',
                    'Automated low-stock alerts',
                    'Supplier management',
                    'Cost analysis and reporting',
                ]
            ],
            'delivery' => [
                'title' => 'Delivery Management',
                'description' => 'Manage home delivery orders with rider tracking and route optimization',
                'icon' => 'truck',
                'benefits' => [
                    'Rider assignment and tracking',
                    'Route optimization',
                    'Customer notifications',
                    'Delivery analytics',
                ]
            ],
            'analytics' => [
                'title' => 'Advanced Analytics',
                'description' => 'Comprehensive reporting and business intelligence',
                'icon' => 'chart-bar',
                'benefits' => [
                    'Sales and revenue reports',
                    'Customer behavior analysis',
                    'Staff performance tracking',
                    'Profit margin analysis',
                ]
            ],
            'loyalty' => [
                'title' => 'Customer Loyalty',
                'description' => 'Build customer loyalty with points, rewards, and personalized offers',
                'icon' => 'heart',
                'benefits' => [
                    'Points-based reward system',
                    'Personalized offers',
                    'Customer segmentation',
                    'Retention analytics',
                ]
            ],
            'multi_location' => [
                'title' => 'Multi-Location Support',
                'description' => 'Manage multiple restaurant branches from a single dashboard',
                'icon' => 'building-office',
                'benefits' => [
                    'Centralized management',
                    'Branch-specific reporting',
                    'Staff assignment across locations',
                    'Unified customer database',
                ]
            ],
        ];

        return view('marketing.features', compact('features'));
    }

    /**
     * Display the contact page
     */
    public function contact()
    {
        return view('marketing.contact');
    }

    /**
     * Handle contact form submission
     */
    public function submitContact(Request $request)
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'email' => 'required|email|max:255',
            'phone' => 'nullable|string|max:20',
            'subject' => 'required|string|max:255',
            'message' => 'required|string|max:2000',
        ]);

        // In a real application, you would send an email or store in database
        // For demo purposes, we'll just redirect with success message

        return redirect()->back()->with('success', 'Thank you for your message! We will get back to you soon.');
    }
}
