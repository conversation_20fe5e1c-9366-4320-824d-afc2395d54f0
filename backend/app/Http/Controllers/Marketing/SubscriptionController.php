<?php

namespace App\Http\Controllers\Marketing;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Str;
use App\Models\User;
use App\Models\Tenant;
use App\Models\SubscriptionPlan;
use App\Models\TenantSubscription;
use App\Models\SubscriptionUsage;
use App\Services\TenantRoleService;

class SubscriptionController extends Controller
{
    protected $tenantRoleService;

    public function __construct(TenantRoleService $tenantRoleService)
    {
        $this->tenantRoleService = $tenantRoleService;
    }

    /**
     * Show subscription signup form
     */
    public function signup(Request $request)
    {
        $planId = $request->get('plan');
        $plan = null;

        if ($planId) {
            $plan = SubscriptionPlan::where('is_active', true)->find($planId);
        }

        $plans = SubscriptionPlan::where('is_active', true)
                                ->orderBy('sort_order')
                                ->get();

        return view('marketing.signup', compact('plan', 'plans'));
    }

    /**
     * Process subscription signup
     */
    public function processSignup(Request $request)
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'email' => 'required|email|unique:users,email',
            'password' => 'required|string|min:8|confirmed',
            'restaurant_name' => 'required|string|max:255',
            'restaurant_slug' => 'required|string|max:50|unique:tenants,id|regex:/^[a-z0-9-]+$/',
            'phone' => 'nullable|string|max:20',
            'address' => 'nullable|string|max:500',
            'plan_id' => 'required|exists:subscription_plans,id',
            'terms' => 'required|accepted',
        ]);

        DB::beginTransaction();

        try {
            // Create user
            $user = User::create([
                'name' => $request->name,
                'email' => $request->email,
                'password' => Hash::make($request->password),
                'email_verified_at' => now(), // Auto-verify for demo
                'is_active' => true,
            ]);

            // Assign restaurant_manager role
            $this->tenantRoleService->assignTenantOwnerRole($user, null);

            // Get selected plan
            $plan = SubscriptionPlan::findOrFail($request->plan_id);

            // Create tenant
            $tenant = Tenant::create([
                'id' => $request->restaurant_slug,
                'name' => $request->restaurant_name,
                'email' => $request->email,
                'phone' => $request->phone,
                'address' => $request->address,
                'subscription_plan_id' => $plan->id,
                'subscription_status' => 'trial',
                'trial_ends_at' => now()->addDays($plan->trial_days ?? 14),
                'subscription_ends_at' => now()->addMonth(),
                'is_active' => true,
            ]);

            // Create tenant subscription
            $subscription = TenantSubscription::create([
                'tenant_id' => $tenant->id,
                'subscription_plan_id' => $plan->id,
                'status' => 'trial',
                'trial_ends_at' => now()->addDays($plan->trial_days ?? 14),
                'current_period_start' => now()->startOfMonth(),
                'current_period_end' => now()->endOfMonth(),
                'amount' => $plan->price,
                'currency' => $plan->currency,
                'billing_cycle' => $plan->billing_cycle,
                'metadata' => [
                    'signup_date' => now()->toISOString(),
                    'signup_ip' => $request->ip(),
                    'user_agent' => $request->userAgent(),
                ],
            ]);

            // Initialize usage tracking
            SubscriptionUsage::create([
                'tenant_id' => $tenant->id,
                'subscription_plan_id' => $plan->id,
                'period_start' => now()->startOfMonth()->toDateString(),
                'period_end' => now()->endOfMonth()->toDateString(),
                'menu_items_used' => 0,
                'orders_used' => 0,
                'pages_used' => 0,
                'branches_used' => 1, // Default branch
                'staff_used' => 1, // Owner
                'total_menu_items' => 0,
                'total_pages' => 0,
                'total_branches' => 1,
                'revenue_generated' => 0,
                'customers_served' => 0,
            ]);

            DB::commit();

            // Redirect to tenant subdomain with success message
            $tenantUrl = "http://{$tenant->id}.localhost:8000/login";

            return redirect()->away($tenantUrl)->with('success',
                "Welcome to RestaurantPro! Your restaurant '{$tenant->name}' has been created successfully. Please login with your credentials to get started."
            );

        } catch (\Exception $e) {
            DB::rollback();

            return redirect()->back()
                           ->withInput()
                           ->withErrors(['error' => 'Failed to create your restaurant. Please try again.']);
        }
    }

    /**
     * Check if restaurant slug is available
     */
    public function checkSlug(Request $request)
    {
        $slug = $request->get('slug');

        if (!$slug) {
            return response()->json(['available' => false, 'message' => 'Slug is required']);
        }

        // Validate slug format
        if (!preg_match('/^[a-z0-9-]+$/', $slug)) {
            return response()->json(['available' => false, 'message' => 'Only lowercase letters, numbers, and hyphens allowed']);
        }

        // Check if slug is available
        $exists = Tenant::where('id', $slug)->exists();

        return response()->json([
            'available' => !$exists,
            'message' => $exists ? 'This restaurant name is already taken' : 'Available',
            'url' => $exists ? null : "http://{$slug}.localhost:8000"
        ]);
    }

    /**
     * Generate slug from restaurant name
     */
    public function generateSlug(Request $request)
    {
        $name = $request->get('name');

        if (!$name) {
            return response()->json(['slug' => '']);
        }

        $baseSlug = Str::slug($name);
        $slug = $baseSlug;
        $counter = 1;

        // Ensure uniqueness
        while (Tenant::where('id', $slug)->exists()) {
            $slug = $baseSlug . '-' . $counter;
            $counter++;
        }

        return response()->json([
            'slug' => $slug,
            'url' => "http://{$slug}.localhost:8000"
        ]);
    }
}
