<?php

namespace App\Http\Controllers\Tenant;

use App\Http\Controllers\Controller;
use App\Models\Tenant\Order;
use App\Models\Tenant\DeliveryZone;
use App\Models\Tenant\DeliveryPersonnel;
use App\Models\Tenant\DeliveryRoute;
use App\Models\Tenant\Restaurant;
use Illuminate\Http\Request;
use Inertia\Inertia;

class DeliveryController extends Controller
{
    /**
     * Display delivery dashboard.
     */
    public function index(Request $request)
    {
        $query = Order::with(['customer', 'deliveryPerson', 'deliveryZone', 'deliveryRoute'])
            ->where('order_type', 'delivery');

        // Filter by status
        if ($request->status) {
            $query->where('status', $request->status);
        }

        // Filter by delivery person
        if ($request->delivery_person_id) {
            $query->where('delivery_person_id', $request->delivery_person_id);
        }

        // Filter by delivery zone
        if ($request->delivery_zone_id) {
            $query->where('delivery_zone_id', $request->delivery_zone_id);
        }

        // Filter by date
        if ($request->date) {
            $query->whereDate('created_at', $request->date);
        } else {
            $query->today();
        }

        $orders = $query->latest()->paginate(20);

        // Get statistics
        $stats = $this->getDeliveryStats();

        // Get available delivery personnel
        $deliveryPersonnel = DeliveryPersonnel::active()->get();
        $deliveryZones = DeliveryZone::active()->get();

        return Inertia::render('Tenant/Delivery/Index', [
            'orders' => $orders,
            'stats' => $stats,
            'deliveryPersonnel' => $deliveryPersonnel,
            'deliveryZones' => $deliveryZones,
            'filters' => $request->only(['status', 'delivery_person_id', 'delivery_zone_id', 'date']),
        ]);
    }

    /**
     * Display live delivery tracking map.
     */
    public function map()
    {
        $activeOrders = Order::with(['customer', 'deliveryPerson', 'deliveryZone'])
            ->where('order_type', 'delivery')
            ->whereIn('status', ['confirmed', 'preparing', 'ready', 'out_for_delivery'])
            ->get();

        $deliveryPersonnel = DeliveryPersonnel::active()
            ->whereNotNull('current_latitude')
            ->whereNotNull('current_longitude')
            ->get();

        $deliveryZones = DeliveryZone::active()->get();

        return Inertia::render('Tenant/Delivery/Map', [
            'activeOrders' => $activeOrders,
            'deliveryPersonnel' => $deliveryPersonnel,
            'deliveryZones' => $deliveryZones,
        ]);
    }

    /**
     * Assign delivery person to order.
     */
    public function assignDeliveryPerson(Request $request, Order $order)
    {
        $request->validate([
            'delivery_person_id' => 'required|exists:delivery_personnel,id',
        ]);

        $deliveryPerson = DeliveryPersonnel::find($request->delivery_person_id);

        if (!$deliveryPerson->isAvailable()) {
            return back()->withErrors(['delivery_person_id' => 'Delivery person is not available.']);
        }

        $order->update([
            'delivery_person_id' => $deliveryPerson->id,
            'assigned_to_delivery_at' => now(),
            'status' => 'confirmed',
        ]);

        return response()->json([
            'success' => true,
            'message' => 'Delivery person assigned successfully.',
        ]);
    }

    /**
     * Auto-assign delivery person to order.
     */
    public function autoAssignDeliveryPerson(Order $order)
    {
        $deliveryPerson = DeliveryPersonnel::findBestForOrder($order);

        if (!$deliveryPerson) {
            return response()->json([
                'success' => false,
                'message' => 'No available delivery person found.',
            ]);
        }

        $order->update([
            'delivery_person_id' => $deliveryPerson->id,
            'assigned_to_delivery_at' => now(),
            'status' => 'confirmed',
        ]);

        return response()->json([
            'success' => true,
            'message' => 'Delivery person auto-assigned successfully.',
            'delivery_person' => $deliveryPerson,
        ]);
    }

    /**
     * Mark order as picked up.
     */
    public function markPickedUp(Order $order)
    {
        $order->update([
            'status' => 'out_for_delivery',
            'picked_up_at' => now(),
            'out_for_delivery_at' => now(),
        ]);

        return response()->json([
            'success' => true,
            'message' => 'Order marked as picked up.',
        ]);
    }

    /**
     * Mark order as delivered.
     */
    public function markDelivered(Request $request, Order $order)
    {
        $request->validate([
            'delivery_notes' => 'nullable|string',
            'delivery_rating' => 'nullable|numeric|min:1|max:5',
        ]);

        $order->update([
            'status' => 'delivered',
            'delivered_at' => now(),
            'delivery_notes' => $request->delivery_notes,
            'delivery_rating' => $request->delivery_rating,
        ]);

        // Update delivery person stats
        if ($order->deliveryPerson) {
            $order->deliveryPerson->increment('total_deliveries');
            
            // Check if delivery was on time
            $estimatedTime = $order->estimated_delivery_time ?? 30;
            $actualTime = $order->created_at->diffInMinutes($order->delivered_at);
            
            if ($actualTime <= $estimatedTime) {
                $order->deliveryPerson->increment('on_time_deliveries');
            }
        }

        return response()->json([
            'success' => true,
            'message' => 'Order marked as delivered.',
        ]);
    }

    /**
     * Create optimized delivery routes.
     */
    public function createRoutes(Request $request)
    {
        $request->validate([
            'order_ids' => 'required|array',
            'order_ids.*' => 'exists:orders,id',
            'max_orders_per_route' => 'nullable|integer|min:1|max:10',
        ]);

        $maxOrdersPerRoute = $request->max_orders_per_route ?? 5;
        $routes = DeliveryRoute::createOptimizedRoutes($request->order_ids, $maxOrdersPerRoute);

        return response()->json([
            'success' => true,
            'message' => count($routes) . ' delivery routes created successfully.',
            'routes' => $routes,
        ]);
    }

    /**
     * Start delivery route.
     */
    public function startRoute(DeliveryRoute $route)
    {
        $route->start();

        return response()->json([
            'success' => true,
            'message' => 'Delivery route started.',
        ]);
    }

    /**
     * Complete delivery route.
     */
    public function completeRoute(DeliveryRoute $route)
    {
        $route->complete();

        return response()->json([
            'success' => true,
            'message' => 'Delivery route completed.',
        ]);
    }

    /**
     * Update delivery person location.
     */
    public function updateLocation(Request $request, DeliveryPersonnel $deliveryPerson)
    {
        $request->validate([
            'latitude' => 'required|numeric|between:-90,90',
            'longitude' => 'required|numeric|between:-180,180',
        ]);

        $deliveryPerson->updateLocation($request->latitude, $request->longitude);

        return response()->json([
            'success' => true,
            'message' => 'Location updated successfully.',
        ]);
    }

    /**
     * Get delivery zones for address.
     */
    public function getZoneForAddress(Request $request)
    {
        $request->validate([
            'latitude' => 'required|numeric|between:-90,90',
            'longitude' => 'required|numeric|between:-180,180',
        ]);

        $zone = DeliveryZone::findZoneForLocation($request->latitude, $request->longitude);

        if (!$zone) {
            return response()->json([
                'success' => false,
                'message' => 'Address is outside delivery zones.',
            ]);
        }

        return response()->json([
            'success' => true,
            'zone' => $zone,
            'delivery_fee' => $zone->delivery_fee,
            'minimum_order' => $zone->minimum_order_amount,
            'estimated_time' => $zone->estimated_delivery_time,
        ]);
    }

    /**
     * Calculate delivery fee and time.
     */
    public function calculateDelivery(Request $request)
    {
        $request->validate([
            'latitude' => 'required|numeric|between:-90,90',
            'longitude' => 'required|numeric|between:-180,180',
            'order_total' => 'required|numeric|min:0',
        ]);

        $zone = DeliveryZone::findZoneForLocation($request->latitude, $request->longitude);

        if (!$zone) {
            return response()->json([
                'success' => false,
                'message' => 'Delivery not available to this location.',
            ]);
        }

        $deliveryFee = $zone->delivery_fee;
        $minimumOrder = $zone->minimum_order_amount;
        $estimatedTime = $zone->estimated_delivery_time;

        // Check minimum order requirement
        if ($request->order_total < $minimumOrder) {
            return response()->json([
                'success' => false,
                'message' => "Minimum order amount is {$minimumOrder} for this area.",
                'minimum_order' => $minimumOrder,
            ]);
        }

        // Free delivery for orders above certain amount (configurable)
        $restaurant = Restaurant::first();
        $freeDeliveryThreshold = $restaurant->free_delivery_threshold ?? 50;
        
        if ($request->order_total >= $freeDeliveryThreshold) {
            $deliveryFee = 0;
        }

        return response()->json([
            'success' => true,
            'delivery_fee' => $deliveryFee,
            'estimated_time' => $estimatedTime,
            'zone_name' => $zone->name,
        ]);
    }

    /**
     * Get delivery statistics.
     */
    protected function getDeliveryStats(): array
    {
        $today = today();
        
        $todayOrders = Order::where('order_type', 'delivery')
            ->whereDate('created_at', $today);

        $totalOrders = $todayOrders->count();
        $deliveredOrders = $todayOrders->where('status', 'delivered')->count();
        $pendingOrders = $todayOrders->whereIn('status', ['pending', 'confirmed', 'preparing'])->count();
        $outForDelivery = $todayOrders->where('status', 'out_for_delivery')->count();

        $totalRevenue = $todayOrders->where('payment_status', 'paid')->sum('total_amount');
        $averageDeliveryTime = $this->getAverageDeliveryTime();
        $onTimePercentage = $this->getOnTimeDeliveryPercentage();

        $activePersonnel = DeliveryPersonnel::where('status', 'available')->count();
        $busyPersonnel = DeliveryPersonnel::whereIn('status', ['busy', 'on_delivery'])->count();

        return [
            'total_orders' => $totalOrders,
            'delivered_orders' => $deliveredOrders,
            'pending_orders' => $pendingOrders,
            'out_for_delivery' => $outForDelivery,
            'total_revenue' => $totalRevenue,
            'average_delivery_time' => $averageDeliveryTime,
            'on_time_percentage' => $onTimePercentage,
            'active_personnel' => $activePersonnel,
            'busy_personnel' => $busyPersonnel,
        ];
    }

    /**
     * Get average delivery time for today.
     */
    protected function getAverageDeliveryTime(): int
    {
        $deliveredOrders = Order::where('order_type', 'delivery')
            ->where('status', 'delivered')
            ->whereDate('created_at', today())
            ->whereNotNull('delivered_at')
            ->get();

        if ($deliveredOrders->isEmpty()) {
            return 30; // Default
        }

        $totalTime = $deliveredOrders->sum(function ($order) {
            return $order->created_at->diffInMinutes($order->delivered_at);
        });

        return (int) ($totalTime / $deliveredOrders->count());
    }

    /**
     * Get on-time delivery percentage for today.
     */
    protected function getOnTimeDeliveryPercentage(): float
    {
        $deliveredOrders = Order::where('order_type', 'delivery')
            ->where('status', 'delivered')
            ->whereDate('created_at', today())
            ->whereNotNull('delivered_at')
            ->get();

        if ($deliveredOrders->isEmpty()) {
            return 100;
        }

        $onTimeOrders = $deliveredOrders->filter(function ($order) {
            $estimatedTime = $order->estimated_delivery_time ?? 30;
            $actualTime = $order->created_at->diffInMinutes($order->delivered_at);
            return $actualTime <= $estimatedTime;
        });

        return ($onTimeOrders->count() / $deliveredOrders->count()) * 100;
    }
}
