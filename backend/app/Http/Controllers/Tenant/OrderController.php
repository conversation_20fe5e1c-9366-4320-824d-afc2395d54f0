<?php

namespace App\Http\Controllers\Tenant;

use App\Http\Controllers\Controller;
use App\Models\Tenant\Order;
use App\Models\Tenant\MenuItem;
use App\Models\Tenant\Table;
use App\Models\Tenant\Customer;
use App\Models\Tenant\Restaurant;
use Illuminate\Http\Request;
use Inertia\Inertia;
use Illuminate\Support\Str;
use Illuminate\Support\Facades\Auth;
use Barryvdh\DomPDF\Facade\Pdf;

class OrderController extends Controller
{
    /**
     * Display a listing of orders.
     */
    public function index(Request $request)
    {
        $query = Order::with(['customer', 'table', 'waiter', 'items.menuItem']);

        // Search
        if ($request->search) {
            $query->where(function ($q) use ($request) {
                $q->where('order_number', 'like', '%' . $request->search . '%')
                  ->orWhere('customer_name', 'like', '%' . $request->search . '%')
                  ->orWhere('customer_phone', 'like', '%' . $request->search . '%');
            });
        }

        // Filter by status
        if ($request->status) {
            $query->where('status', $request->status);
        }

        // Filter by order type
        if ($request->order_type) {
            $query->where('order_type', $request->order_type);
        }

        // Filter by payment status
        if ($request->payment_status) {
            $query->where('payment_status', $request->payment_status);
        }

        // Filter by date range
        if ($request->date_from) {
            $query->whereDate('created_at', '>=', $request->date_from);
        }
        if ($request->date_to) {
            $query->whereDate('created_at', '<=', $request->date_to);
        }

        $orders = $query->latest()->paginate(20);

        return Inertia::render('Tenant/Orders/Index', [
            'orders' => $orders,
            'filters' => $request->only(['search', 'status', 'order_type', 'payment_status', 'date_from', 'date_to']),
            'statusOptions' => $this->getStatusOptions(),
            'orderTypeOptions' => $this->getOrderTypeOptions(),
            'paymentStatusOptions' => $this->getPaymentStatusOptions(),
        ]);
    }

    /**
     * Show the form for creating a new order.
     */
    public function create()
    {
        $restaurant = Restaurant::first();
        $menuItems = MenuItem::with('category')
            ->active()
            ->available()
            ->get()
            ->groupBy('category.name');

        $tables = Table::active()->available()->get();
        $customers = Customer::latest()->take(50)->get();

        return Inertia::render('Tenant/Orders/Create', [
            'restaurant' => $restaurant,
            'menuItems' => $menuItems,
            'tables' => $tables,
            'customers' => $customers,
        ]);
    }

    /**
     * Store a newly created order.
     */
    public function store(Request $request)
    {
        $request->validate([
            'order_type' => 'required|in:dine_in,takeaway,delivery',
            'table_id' => 'nullable|exists:tables,id',
            'customer_id' => 'nullable|exists:customers,id',
            'customer_name' => 'required|string|max:255',
            'customer_phone' => 'required|string|max:20',
            'customer_email' => 'nullable|email',
            'delivery_address' => 'required_if:order_type,delivery|string',
            'special_instructions' => 'nullable|string',
            'items' => 'required|array|min:1',
            'items.*.menu_item_id' => 'required|exists:menu_items,id',
            'items.*.quantity' => 'required|integer|min:1',
            'items.*.special_instructions' => 'nullable|string',
        ]);

        $restaurant = Restaurant::first();

        // Calculate order totals
        $subtotal = 0;
        $orderItems = [];

        foreach ($request->items as $item) {
            $menuItem = MenuItem::find($item['menu_item_id']);
            $itemTotal = $menuItem->effective_price * $item['quantity'];
            $subtotal += $itemTotal;

            $orderItems[] = [
                'menu_item_id' => $menuItem->id,
                'item_name' => $menuItem->name,
                'item_price' => $menuItem->effective_price,
                'quantity' => $item['quantity'],
                'total_price' => $itemTotal,
                'special_instructions' => $item['special_instructions'] ?? null,
            ];
        }

        $taxAmount = $subtotal * ($restaurant->tax_rate / 100);
        $serviceCharge = $request->order_type === 'dine_in' ? $subtotal * ($restaurant->service_charge / 100) : 0;
        $deliveryCharge = $request->order_type === 'delivery' ? $restaurant->delivery_charge : 0;
        $totalAmount = $subtotal + $taxAmount + $serviceCharge + $deliveryCharge;

        // Create or update customer
        $customer = null;
        if ($request->customer_id) {
            $customer = Customer::find($request->customer_id);
        } else {
            $customer = Customer::firstOrCreate(
                ['phone' => $request->customer_phone],
                [
                    'restaurant_id' => $restaurant->id,
                    'first_name' => explode(' ', $request->customer_name)[0],
                    'last_name' => implode(' ', array_slice(explode(' ', $request->customer_name), 1)),
                    'email' => $request->customer_email,
                ]
            );
        }

        // Create order
        $order = Order::create([
            'restaurant_id' => $restaurant->id,
            'customer_id' => $customer->id,
            'table_id' => $request->table_id,
            'waiter_id' => Auth::id() ?? null,
            'order_number' => $this->generateOrderNumber(),
            'order_type' => $request->order_type,
            'status' => 'pending',
            'payment_status' => 'pending',
            'subtotal' => $subtotal,
            'tax_amount' => $taxAmount,
            'service_charge' => $serviceCharge,
            'delivery_charge' => $deliveryCharge,
            'total_amount' => $totalAmount,
            'customer_name' => $request->customer_name,
            'customer_phone' => $request->customer_phone,
            'customer_email' => $request->customer_email,
            'delivery_address' => $request->delivery_address,
            'special_instructions' => $request->special_instructions,
        ]);

        // Create order items
        foreach ($orderItems as $item) {
            $order->items()->create($item);
        }

        // Update table availability if dine-in
        if ($request->order_type === 'dine_in' && $request->table_id) {
            Table::find($request->table_id)->update(['is_available' => false]);
        }

        return redirect()->route('orders.show', $order)
            ->with('success', 'Order created successfully.');
    }

    /**
     * Display the specified order.
     */
    public function show(Order $order)
    {
        $order->load([
            'customer',
            'table',
            'waiter',
            'chef',
            'items.menuItem',
            'restaurant',
            'payments',
            'updates' => function($query) {
                $query->orderBy('created_at', 'desc');
            }
        ]);

        return Inertia::render('Tenant/Orders/Show', [
            'order' => $order,
        ]);
    }

    /**
     * Show the form for editing the specified order.
     */
    public function edit(Order $order)
    {
        $order->load(['customer', 'table', 'waiter', 'items.menuItem']);

        $restaurant = Restaurant::first();
        $menuItems = MenuItem::with('category')
            ->active()
            ->available()
            ->get()
            ->groupBy('category.name');

        $tables = Table::active()->get();
        $customers = Customer::latest()->take(50)->get();

        return Inertia::render('Tenant/Orders/Edit', [
            'order' => $order,
            'restaurant' => $restaurant,
            'menuItems' => $menuItems,
            'tables' => $tables,
            'customers' => $customers,
        ]);
    }

    /**
     * Update the specified order.
     */
    public function update(Request $request, Order $order)
    {
        $request->validate([
            'status' => 'required|in:pending,confirmed,preparing,ready,served,delivered,cancelled',
            'special_instructions' => 'nullable|string',
        ]);

        $order->update($request->only(['status', 'special_instructions']));

        // Update timestamps based on status
        switch ($request->status) {
            case 'preparing':
                $order->update(['preparation_started_at' => now()]);
                break;
            case 'ready':
                $order->update(['ready_at' => now()]);
                break;
            case 'served':
                $order->update(['served_at' => now()]);
                if ($order->table) {
                    $order->table->update(['is_available' => true]);
                }
                break;
            case 'delivered':
                $order->update(['delivered_at' => now()]);
                break;
            case 'cancelled':
                $order->update(['cancelled_at' => now()]);
                if ($order->table) {
                    $order->table->update(['is_available' => true]);
                }
                break;
        }

        return back()->with('success', 'Order updated successfully.');
    }

    /**
     * Remove the specified order.
     */
    public function destroy(Order $order)
    {
        // Only allow deletion of pending orders
        if ($order->status !== 'pending') {
            return back()->withErrors(['error' => 'Only pending orders can be deleted.']);
        }

        $order->delete();

        return redirect()->route('orders.index')
            ->with('success', 'Order deleted successfully.');
    }

    /**
     * Update order status.
     */
    public function updateStatus(Request $request, Order $order)
    {
        $request->validate([
            'status' => 'required|in:pending,confirmed,preparing,ready,served,delivered,cancelled',
            'cancellation_reason' => 'required_if:status,cancelled|string',
        ]);

        $order->update([
            'status' => $request->status,
            'cancellation_reason' => $request->cancellation_reason,
        ]);

        // Update timestamps and related data
        $this->updateOrderTimestamps($order, $request->status);

        return response()->json([
            'success' => true,
            'message' => 'Order status updated successfully.',
            'order' => $order->fresh(),
        ]);
    }

    /**
     * Update payment status.
     */
    public function updatePayment(Request $request, Order $order)
    {
        $request->validate([
            'payment_status' => 'required|in:pending,paid,failed,refunded',
            'payment_method' => 'required_if:payment_status,paid|in:cash,card,mobile_banking,online',
            'paid_amount' => 'required_if:payment_status,paid|numeric|min:0',
            'change_amount' => 'nullable|numeric|min:0',
        ]);

        $order->update($request->only([
            'payment_status', 'payment_method', 'paid_amount', 'change_amount'
        ]));

        // Update customer stats if payment is successful
        if ($request->payment_status === 'paid' && $order->customer) {
            $order->customer->updateStats($order);
        }

        return response()->json([
            'success' => true,
            'message' => 'Payment updated successfully.',
            'order' => $order->fresh(),
        ]);
    }

    /**
     * Generate invoice PDF.
     */
    public function invoice(Order $order)
    {
        $order->load(['customer', 'table', 'waiter', 'items.menuItem', 'restaurant']);

        $pdf = Pdf::loadView('invoices.order', compact('order'));

        return $pdf->download("invoice-{$order->order_number}.pdf");
    }

    /**
     * Print order receipt.
     */
    public function print(Order $order)
    {
        $order->load(['customer', 'table', 'waiter', 'items.menuItem', 'restaurant']);

        return Inertia::render('Tenant/Orders/Print', [
            'order' => $order,
        ]);
    }

    /**
     * Kitchen view for order management.
     */
    public function kitchen()
    {
        $orders = Order::with(['customer', 'table', 'items.menuItem'])
            ->whereIn('status', ['confirmed', 'preparing'])
            ->orderBy('created_at')
            ->get();

        return Inertia::render('Tenant/Kitchen/Index', [
            'orders' => $orders,
        ]);
    }

    /**
     * Start order preparation.
     */
    public function startPreparation(Order $order)
    {
        $order->update([
            'status' => 'preparing',
            'preparation_started_at' => now(),
        ]);

        return response()->json([
            'success' => true,
            'message' => 'Order preparation started.',
        ]);
    }

    /**
     * Mark order as ready.
     */
    public function markReady(Order $order)
    {
        $order->update([
            'status' => 'ready',
            'ready_at' => now(),
        ]);

        return response()->json([
            'success' => true,
            'message' => 'Order marked as ready.',
        ]);
    }

    /**
     * Generate unique order number.
     */
    protected function generateOrderNumber(): string
    {
        $prefix = 'ORD';
        $date = now()->format('Ymd');
        $random = strtoupper(Str::random(4));

        return "{$prefix}-{$date}-{$random}";
    }

    /**
     * Update order timestamps based on status.
     */
    protected function updateOrderTimestamps(Order $order, string $status): void
    {
        $updates = [];

        switch ($status) {
            case 'preparing':
                $updates['preparation_started_at'] = now();
                break;
            case 'ready':
                $updates['ready_at'] = now();
                break;
            case 'served':
                $updates['served_at'] = now();
                if ($order->table) {
                    $order->table->update(['is_available' => true]);
                }
                break;
            case 'delivered':
                $updates['delivered_at'] = now();
                break;
            case 'cancelled':
                $updates['cancelled_at'] = now();
                if ($order->table) {
                    $order->table->update(['is_available' => true]);
                }
                break;
        }

        if (!empty($updates)) {
            $order->update($updates);
        }
    }

    /**
     * Get status options for filters.
     */
    protected function getStatusOptions(): array
    {
        return [
            ['value' => 'pending', 'label' => 'Pending'],
            ['value' => 'confirmed', 'label' => 'Confirmed'],
            ['value' => 'preparing', 'label' => 'Preparing'],
            ['value' => 'ready', 'label' => 'Ready'],
            ['value' => 'served', 'label' => 'Served'],
            ['value' => 'delivered', 'label' => 'Delivered'],
            ['value' => 'cancelled', 'label' => 'Cancelled'],
        ];
    }

    /**
     * Get order type options for filters.
     */
    protected function getOrderTypeOptions(): array
    {
        return [
            ['value' => 'dine_in', 'label' => 'Dine In'],
            ['value' => 'takeaway', 'label' => 'Takeaway'],
            ['value' => 'delivery', 'label' => 'Delivery'],
        ];
    }

    /**
     * Get payment status options for filters.
     */
    protected function getPaymentStatusOptions(): array
    {
        return [
            ['value' => 'pending', 'label' => 'Pending'],
            ['value' => 'paid', 'label' => 'Paid'],
            ['value' => 'failed', 'label' => 'Failed'],
            ['value' => 'refunded', 'label' => 'Refunded'],
        ];
    }
}
