<?php

namespace App\Http\Controllers\Tenant;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Inertia\Inertia;
use Inertia\Response;

class SettingsController extends Controller
{
    public function __construct()
    {
        $this->middleware(['auth', 'verified']);
        $this->middleware('role:admin|restaurant_manager');
    }

    /**
     * Display restaurant settings
     */
    public function index(): Response
    {
        $restaurant = \App\Models\Tenant\Restaurant::first();

        // Get current settings
        $settings = [
            'general' => [
                'restaurant_name' => $restaurant->name ?? '',
                'description' => $restaurant->description ?? '',
                'phone' => $restaurant->phone ?? '',
                'email' => $restaurant->email ?? '',
                'website' => $restaurant->website ?? '',
                'address' => $restaurant->address ?? '',
                'city' => $restaurant->city ?? '',
                'state' => $restaurant->state ?? '',
                'postal_code' => $restaurant->postal_code ?? '',
                'country' => $restaurant->country ?? '',
                'timezone' => $restaurant->timezone ?? config('app.timezone'),
                'currency' => $restaurant->currency ?? 'USD',
                'language' => $restaurant->language ?? 'en',
            ],
            'operational' => [
                'opening_hours' => $restaurant->opening_hours ?? [],
                'delivery_enabled' => $restaurant->is_delivery_enabled ?? true,
                'takeout_enabled' => $restaurant->is_takeaway_enabled ?? true,
                'dine_in_enabled' => $restaurant->is_dine_in_enabled ?? true,
                'delivery_radius' => $restaurant->delivery_radius ?? 10,
                'minimum_order_amount' => $restaurant->minimum_order_amount ?? 0,
                'delivery_fee' => $restaurant->delivery_charge ?? 0,
            ],
            'tax_vat' => [
                'tax_enabled' => $restaurant->tax_enabled ?? false,
                'tax_rate' => $restaurant->tax_rate ?? 0,
                'tax_name' => $restaurant->tax_name ?? 'Tax',
                'vat_enabled' => $restaurant->vat_enabled ?? false,
                'vat_rate' => $restaurant->vat_rate ?? 0,
                'vat_number' => $restaurant->vat_number ?? '',
                'service_charge_enabled' => $restaurant->service_charge_enabled ?? false,
                'service_charge' => $restaurant->service_charge ?? 0,
                'service_charge_name' => $restaurant->service_charge_name ?? 'Service Charge',
            ],
            'payment' => [
                'cash_enabled' => $tenant->cash_enabled ?? true,
                'card_enabled' => $tenant->card_enabled ?? true,
                'online_payment_enabled' => $tenant->online_payment_enabled ?? false,
                'stripe_enabled' => $tenant->stripe_enabled ?? false,
                'stripe_public_key' => $tenant->stripe_public_key ?? '',
                'stripe_secret_key' => $tenant->stripe_secret_key ?? '',
                'paypal_enabled' => $tenant->paypal_enabled ?? false,
                'paypal_client_id' => $tenant->paypal_client_id ?? '',
                'paypal_client_secret' => $tenant->paypal_client_secret ?? '',
            ],
            'notification' => [
                'email_notifications' => $tenant->email_notifications ?? true,
                'sms_notifications' => $tenant->sms_notifications ?? false,
                'push_notifications' => $tenant->push_notifications ?? true,
                'order_notifications' => $tenant->order_notifications ?? true,
                'reservation_notifications' => $tenant->reservation_notifications ?? true,
                'low_stock_notifications' => $tenant->low_stock_notifications ?? true,
                'notification_email' => $tenant->notification_email ?? '',
                'notification_phone' => $tenant->notification_phone ?? '',
            ],
            'branding' => [
                'logo' => $tenant->logo ?? '',
                'favicon' => $tenant->favicon ?? '',
                'primary_color' => $tenant->primary_color ?? '#3B82F6',
                'secondary_color' => $tenant->secondary_color ?? '#10B981',
                'accent_color' => $tenant->accent_color ?? '#F59E0B',
                'font_family' => $tenant->font_family ?? 'Inter',
                'custom_css' => $tenant->custom_css ?? '',
            ],
        ];

        return Inertia::render('Tenant/Settings/Index', [
            'settings' => $settings,
            'timezones' => $this->getTimezones(),
            'currencies' => $this->getCurrencies(),
            'languages' => $this->getLanguages(),
            'fonts' => $this->getFonts(),
        ]);
    }

    /**
     * Update general settings
     */
    public function updateGeneral(Request $request)
    {
        $validated = $request->validate([
            'restaurant_name' => 'required|string|max:255',
            'description' => 'nullable|string|max:1000',
            'phone' => 'required|string|max:20',
            'email' => 'required|email|max:255',
            'website' => 'nullable|url|max:255',
            'address' => 'required|string|max:255',
            'city' => 'required|string|max:100',
            'state' => 'nullable|string|max:100',
            'postal_code' => 'required|string|max:20',
            'country' => 'required|string|max:100',
            'timezone' => 'required|string|max:50',
            'currency' => 'required|string|size:3',
            'language' => 'required|string|size:2',
        ]);

        $restaurant = \App\Models\Tenant\Restaurant::first();

        // Update restaurant data
        $restaurant->update([
            'name' => $validated['restaurant_name'],
            'description' => $validated['description'],
            'phone' => $validated['phone'],
            'email' => $validated['email'],
            'website' => $validated['website'],
            'address' => $validated['address'],
            'city' => $validated['city'],
            'state' => $validated['state'],
            'postal_code' => $validated['postal_code'],
            'country' => $validated['country'],
            'timezone' => $validated['timezone'],
            'currency' => $validated['currency'],
            'language' => $validated['language'],
        ]);

        return back()->with('success', __('General settings updated successfully.'));
    }

    /**
     * Update operational settings
     */
    public function updateOperational(Request $request)
    {
        $validated = $request->validate([
            'opening_hours' => 'nullable|array',
            'delivery_enabled' => 'boolean',
            'takeout_enabled' => 'boolean',
            'dine_in_enabled' => 'boolean',
            'reservation_enabled' => 'boolean',
            'online_ordering_enabled' => 'boolean',
            'delivery_radius' => 'numeric|min:0|max:100',
            'minimum_order_amount' => 'numeric|min:0',
            'delivery_fee' => 'numeric|min:0',
        ]);

        $restaurant = \App\Models\Tenant\Restaurant::first();
        $restaurant->update($validated);

        return back()->with('success', __('Operational settings updated successfully.'));
    }

    /**
     * Update tax and VAT settings
     */
    public function updateTaxVat(Request $request)
    {
        $validated = $request->validate([
            'tax_enabled' => 'boolean',
            'tax_rate' => 'numeric|min:0|max:100',
            'tax_name' => 'required|string|max:50',
            'vat_enabled' => 'boolean',
            'vat_rate' => 'numeric|min:0|max:100',
            'vat_number' => 'nullable|string|max:50',
            'service_charge_enabled' => 'boolean',
            'service_charge' => 'numeric|min:0|max:100',
            'service_charge_name' => 'required|string|max:50',
        ]);

        $restaurant = \App\Models\Tenant\Restaurant::first();
        $restaurant->update($validated);

        return back()->with('success', __('Tax and VAT settings updated successfully.'));
    }

    /**
     * Update payment settings
     */
    public function updatePayment(Request $request)
    {
        $validated = $request->validate([
            'cash_enabled' => 'boolean',
            'card_enabled' => 'boolean',
            'online_payment_enabled' => 'boolean',
            'stripe_enabled' => 'boolean',
            'stripe_public_key' => 'nullable|string|max:255',
            'stripe_secret_key' => 'nullable|string|max:255',
            'paypal_enabled' => 'boolean',
            'paypal_client_id' => 'nullable|string|max:255',
            'paypal_client_secret' => 'nullable|string|max:255',
        ]);

        $tenant = tenant();
        $tenant->update($validated);

        return back()->with('success', __('Payment settings updated successfully.'));
    }

    /**
     * Update notification settings
     */
    public function updateNotification(Request $request)
    {
        $validated = $request->validate([
            'email_notifications' => 'boolean',
            'sms_notifications' => 'boolean',
            'push_notifications' => 'boolean',
            'order_notifications' => 'boolean',
            'reservation_notifications' => 'boolean',
            'low_stock_notifications' => 'boolean',
            'notification_email' => 'nullable|email|max:255',
            'notification_phone' => 'nullable|string|max:20',
        ]);

        $tenant = tenant();
        $tenant->update($validated);

        return back()->with('success', __('Notification settings updated successfully.'));
    }

    /**
     * Update branding settings
     */
    public function updateBranding(Request $request)
    {
        $validated = $request->validate([
            'logo' => 'nullable|image|max:2048',
            'favicon' => 'nullable|image|max:512',
            'primary_color' => 'required|string|regex:/^#[0-9A-Fa-f]{6}$/',
            'secondary_color' => 'required|string|regex:/^#[0-9A-Fa-f]{6}$/',
            'accent_color' => 'required|string|regex:/^#[0-9A-Fa-f]{6}$/',
            'font_family' => 'required|string|max:50',
            'custom_css' => 'nullable|string|max:10000',
        ]);

        $tenant = tenant();

        // Handle file uploads
        if ($request->hasFile('logo')) {
            $logoPath = $request->file('logo')->store('branding', 'public');
            $validated['logo'] = $logoPath;
        }

        if ($request->hasFile('favicon')) {
            $faviconPath = $request->file('favicon')->store('branding', 'public');
            $validated['favicon'] = $faviconPath;
        }

        $tenant->update($validated);

        return back()->with('success', __('Branding settings updated successfully.'));
    }

    /**
     * Get available timezones
     */
    private function getTimezones(): array
    {
        return [
            'UTC' => 'UTC',
            'America/New_York' => 'Eastern Time',
            'America/Chicago' => 'Central Time',
            'America/Denver' => 'Mountain Time',
            'America/Los_Angeles' => 'Pacific Time',
            'Europe/London' => 'London',
            'Europe/Paris' => 'Paris',
            'Asia/Tokyo' => 'Tokyo',
            'Asia/Shanghai' => 'Shanghai',
            'Asia/Dhaka' => 'Dhaka',
        ];
    }

    /**
     * Get available currencies
     */
    private function getCurrencies(): array
    {
        return [
            'USD' => 'US Dollar ($)',
            'EUR' => 'Euro (€)',
            'GBP' => 'British Pound (£)',
            'JPY' => 'Japanese Yen (¥)',
            'BDT' => 'Bangladeshi Taka (৳)',
            'INR' => 'Indian Rupee (₹)',
            'CAD' => 'Canadian Dollar (C$)',
            'AUD' => 'Australian Dollar (A$)',
        ];
    }

    /**
     * Get available languages
     */
    private function getLanguages(): array
    {
        return [
            'en' => 'English',
            'bn' => 'Bengali',
            'es' => 'Spanish',
            'fr' => 'French',
            'de' => 'German',
            'it' => 'Italian',
            'ja' => 'Japanese',
            'zh' => 'Chinese',
        ];
    }

    /**
     * Get available fonts
     */
    private function getFonts(): array
    {
        return [
            'Inter' => 'Inter',
            'Roboto' => 'Roboto',
            'Open Sans' => 'Open Sans',
            'Lato' => 'Lato',
            'Montserrat' => 'Montserrat',
            'Poppins' => 'Poppins',
            'Source Sans Pro' => 'Source Sans Pro',
            'Nunito' => 'Nunito',
        ];
    }
}
