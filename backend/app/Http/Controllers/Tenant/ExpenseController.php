<?php

namespace App\Http\Controllers\Tenant;

use App\Http\Controllers\Controller;
use App\Models\Tenant\Expense;
use App\Models\Tenant\ExpenseCategory;
use App\Models\Tenant\Vendor;
use App\Models\Tenant\Restaurant;
use Illuminate\Http\Request;
use Inertia\Inertia;
use Carbon\Carbon;

class ExpenseController extends Controller
{
    /**
     * Display a listing of expenses.
     */
    public function index(Request $request)
    {
        $query = Expense::with(['category', 'vendor', 'creator', 'approver', 'media']);

        // Search
        if ($request->search) {
            $query->where(function ($q) use ($request) {
                $q->where('title', 'like', '%' . $request->search . '%')
                  ->orWhere('description', 'like', '%' . $request->search . '%')
                  ->orWhere('expense_number', 'like', '%' . $request->search . '%')
                  ->orWhere('invoice_number', 'like', '%' . $request->search . '%');
            });
        }

        // Filter by status
        if ($request->status) {
            $query->where('status', $request->status);
        }

        // Filter by category
        if ($request->category_id) {
            $query->where('expense_category_id', $request->category_id);
        }

        // Filter by vendor
        if ($request->vendor_id) {
            $query->where('vendor_id', $request->vendor_id);
        }

        // Filter by priority
        if ($request->priority) {
            $query->where('priority', $request->priority);
        }

        // Filter by date range
        if ($request->date_from) {
            $query->whereDate('expense_date', '>=', $request->date_from);
        }
        if ($request->date_to) {
            $query->whereDate('expense_date', '<=', $request->date_to);
        }

        // Filter by amount range
        if ($request->amount_from) {
            $query->where('total_amount', '>=', $request->amount_from);
        }
        if ($request->amount_to) {
            $query->where('total_amount', '<=', $request->amount_to);
        }

        $expenses = $query->latest('expense_date')->paginate(20);

        // Get filter options
        $categories = ExpenseCategory::active()->ordered()->get();
        $vendors = Vendor::active()->orderBy('name')->get();

        // Get statistics
        $stats = $this->getExpenseStats($request);

        return Inertia::render('Tenant/Expenses/Index', [
            'expenses' => $expenses,
            'categories' => $categories,
            'vendors' => $vendors,
            'stats' => $stats,
            'filters' => $request->only([
                'search', 'status', 'category_id', 'vendor_id', 'priority',
                'date_from', 'date_to', 'amount_from', 'amount_to'
            ]),
            'statusOptions' => $this->getStatusOptions(),
            'priorityOptions' => $this->getPriorityOptions(),
        ]);
    }

    /**
     * Show the form for creating a new expense.
     */
    public function create()
    {
        $restaurant = Restaurant::first();
        $categories = ExpenseCategory::active()->ordered()->get();
        $vendors = Vendor::active()->orderBy('name')->get();

        return Inertia::render('Tenant/Expenses/Create', [
            'restaurant' => $restaurant,
            'categories' => $categories,
            'vendors' => $vendors,
        ]);
    }

    /**
     * Store a newly created expense.
     */
    public function store(Request $request)
    {
        $request->validate([
            'expense_category_id' => 'required|exists:expense_categories,id',
            'vendor_id' => 'nullable|exists:vendors,id',
            'title' => 'required|string|max:255',
            'description' => 'nullable|string',
            'amount' => 'required|numeric|min:0',
            'tax_amount' => 'nullable|numeric|min:0',
            'expense_date' => 'required|date',
            'due_date' => 'nullable|date|after_or_equal:expense_date',
            'payment_method' => 'nullable|in:cash,check,bank_transfer,credit_card,debit_card,online,other',
            'payment_reference' => 'nullable|string|max:255',
            'priority' => 'required|in:low,medium,high,urgent',
            'is_recurring' => 'boolean',
            'recurring_frequency' => 'nullable|required_if:is_recurring,true|in:daily,weekly,monthly,quarterly,yearly',
            'recurring_until' => 'nullable|date|after:expense_date',
            'invoice_number' => 'nullable|string|max:255',
            'receipt_number' => 'nullable|string|max:255',
            'notes' => 'nullable|string',
            'tags' => 'nullable|array',
            'media_ids' => 'nullable|array',
            'media_ids.*' => 'exists:media,id',
        ]);

        $restaurant = Restaurant::first();
        $totalAmount = $request->amount + ($request->tax_amount ?? 0);

        $expense = Expense::create([
            'restaurant_id' => $restaurant->id,
            'expense_category_id' => $request->expense_category_id,
            'vendor_id' => $request->vendor_id,
            'created_by' => auth()->id(),
            'title' => $request->title,
            'description' => $request->description,
            'amount' => $request->amount,
            'tax_amount' => $request->tax_amount ?? 0,
            'total_amount' => $totalAmount,
            'expense_date' => $request->expense_date,
            'due_date' => $request->due_date,
            'payment_method' => $request->payment_method,
            'payment_reference' => $request->payment_reference,
            'priority' => $request->priority,
            'is_recurring' => $request->boolean('is_recurring'),
            'recurring_frequency' => $request->recurring_frequency,
            'recurring_until' => $request->recurring_until,
            'invoice_number' => $request->invoice_number,
            'receipt_number' => $request->receipt_number,
            'notes' => $request->notes,
            'tags' => $request->tags,
        ]);

        // Attach media
        if ($request->media_ids) {
            foreach ($request->media_ids as $mediaId) {
                $expense->addMediaFromLibrary($mediaId, 'receipts');
            }
        }

        return redirect()->route('expenses.index')
            ->with('success', 'Expense created successfully.');
    }

    /**
     * Display the specified expense.
     */
    public function show(Expense $expense)
    {
        $expense->load([
            'category', 'vendor', 'creator', 'approver', 'media',
            'approvals' => function ($query) {
                $query->with('user')->latest();
            }
        ]);

        return Inertia::render('Tenant/Expenses/Show', [
            'expense' => $expense,
        ]);
    }

    /**
     * Show the form for editing the specified expense.
     */
    public function edit(Expense $expense)
    {
        // Only allow editing of pending expenses
        if ($expense->status !== 'pending') {
            return back()->withErrors(['error' => 'Only pending expenses can be edited.']);
        }

        $expense->load(['category', 'vendor', 'media']);
        $restaurant = Restaurant::first();
        $categories = ExpenseCategory::active()->ordered()->get();
        $vendors = Vendor::active()->orderBy('name')->get();

        return Inertia::render('Tenant/Expenses/Edit', [
            'expense' => $expense,
            'restaurant' => $restaurant,
            'categories' => $categories,
            'vendors' => $vendors,
        ]);
    }

    /**
     * Update the specified expense.
     */
    public function update(Request $request, Expense $expense)
    {
        // Only allow updating of pending expenses
        if ($expense->status !== 'pending') {
            return back()->withErrors(['error' => 'Only pending expenses can be updated.']);
        }

        $request->validate([
            'expense_category_id' => 'required|exists:expense_categories,id',
            'vendor_id' => 'nullable|exists:vendors,id',
            'title' => 'required|string|max:255',
            'description' => 'nullable|string',
            'amount' => 'required|numeric|min:0',
            'tax_amount' => 'nullable|numeric|min:0',
            'expense_date' => 'required|date',
            'due_date' => 'nullable|date|after_or_equal:expense_date',
            'payment_method' => 'nullable|in:cash,check,bank_transfer,credit_card,debit_card,online,other',
            'payment_reference' => 'nullable|string|max:255',
            'priority' => 'required|in:low,medium,high,urgent',
            'is_recurring' => 'boolean',
            'recurring_frequency' => 'nullable|required_if:is_recurring,true|in:daily,weekly,monthly,quarterly,yearly',
            'recurring_until' => 'nullable|date|after:expense_date',
            'invoice_number' => 'nullable|string|max:255',
            'receipt_number' => 'nullable|string|max:255',
            'notes' => 'nullable|string',
            'tags' => 'nullable|array',
            'media_ids' => 'nullable|array',
            'media_ids.*' => 'exists:media,id',
        ]);

        $totalAmount = $request->amount + ($request->tax_amount ?? 0);

        $expense->update([
            'expense_category_id' => $request->expense_category_id,
            'vendor_id' => $request->vendor_id,
            'title' => $request->title,
            'description' => $request->description,
            'amount' => $request->amount,
            'tax_amount' => $request->tax_amount ?? 0,
            'total_amount' => $totalAmount,
            'expense_date' => $request->expense_date,
            'due_date' => $request->due_date,
            'payment_method' => $request->payment_method,
            'payment_reference' => $request->payment_reference,
            'priority' => $request->priority,
            'is_recurring' => $request->boolean('is_recurring'),
            'recurring_frequency' => $request->recurring_frequency,
            'recurring_until' => $request->recurring_until,
            'invoice_number' => $request->invoice_number,
            'receipt_number' => $request->receipt_number,
            'notes' => $request->notes,
            'tags' => $request->tags,
        ]);

        // Update media
        $expense->clearMediaCollection('receipts');
        if ($request->media_ids) {
            foreach ($request->media_ids as $mediaId) {
                $expense->addMediaFromLibrary($mediaId, 'receipts');
            }
        }

        return redirect()->route('expenses.index')
            ->with('success', 'Expense updated successfully.');
    }

    /**
     * Remove the specified expense.
     */
    public function destroy(Expense $expense)
    {
        // Only allow deletion of pending expenses
        if ($expense->status !== 'pending') {
            return back()->withErrors(['error' => 'Only pending expenses can be deleted.']);
        }

        $expense->delete();

        return redirect()->route('expenses.index')
            ->with('success', 'Expense deleted successfully.');
    }

    /**
     * Approve expense.
     */
    public function approve(Request $request, Expense $expense)
    {
        $request->validate([
            'notes' => 'nullable|string',
        ]);

        $expense->approve(auth()->user(), $request->notes);

        return response()->json([
            'success' => true,
            'message' => 'Expense approved successfully.',
        ]);
    }

    /**
     * Reject expense.
     */
    public function reject(Request $request, Expense $expense)
    {
        $request->validate([
            'reason' => 'required|string',
        ]);

        $expense->reject(auth()->user(), $request->reason);

        return response()->json([
            'success' => true,
            'message' => 'Expense rejected successfully.',
        ]);
    }

    /**
     * Mark expense as paid.
     */
    public function markPaid(Request $request, Expense $expense)
    {
        $request->validate([
            'payment_method' => 'required|in:cash,check,bank_transfer,credit_card,debit_card,online,other',
            'payment_reference' => 'nullable|string|max:255',
        ]);

        $expense->markAsPaid($request->payment_method, $request->payment_reference);

        return response()->json([
            'success' => true,
            'message' => 'Expense marked as paid successfully.',
        ]);
    }

    /**
     * Duplicate expense.
     */
    public function duplicate(Expense $expense)
    {
        $newExpense = $expense->replicate();
        $newExpense->expense_number = Expense::generateExpenseNumber();
        $newExpense->status = 'pending';
        $newExpense->approved_by = null;
        $newExpense->paid_at = null;
        $newExpense->payment_method = null;
        $newExpense->payment_reference = null;
        $newExpense->expense_date = today();
        $newExpense->save();

        // Copy media
        foreach ($expense->getMedia('receipts') as $media) {
            $newExpense->addMediaFromLibrary($media->id, 'receipts');
        }

        return redirect()->route('expenses.edit', $newExpense)
            ->with('success', 'Expense duplicated successfully.');
    }

    /**
     * Bulk update expenses.
     */
    public function bulkUpdate(Request $request)
    {
        $request->validate([
            'ids' => 'required|array',
            'ids.*' => 'exists:expenses,id',
            'action' => 'required|in:approve,reject,delete,mark_paid',
            'notes' => 'nullable|string',
            'payment_method' => 'nullable|required_if:action,mark_paid|in:cash,check,bank_transfer,credit_card,debit_card,online,other',
        ]);

        $expenses = Expense::whereIn('id', $request->ids);

        switch ($request->action) {
            case 'approve':
                $expenses->where('status', 'pending')->get()->each(function ($expense) use ($request) {
                    $expense->approve(auth()->user(), $request->notes);
                });
                $message = 'Expenses approved successfully.';
                break;
            case 'reject':
                $expenses->where('status', 'pending')->get()->each(function ($expense) use ($request) {
                    $expense->reject(auth()->user(), $request->notes ?? 'Bulk rejection');
                });
                $message = 'Expenses rejected successfully.';
                break;
            case 'mark_paid':
                $expenses->where('status', 'approved')->get()->each(function ($expense) use ($request) {
                    $expense->markAsPaid($request->payment_method);
                });
                $message = 'Expenses marked as paid successfully.';
                break;
            case 'delete':
                $expenses->where('status', 'pending')->delete();
                $message = 'Expenses deleted successfully.';
                break;
        }

        return back()->with('success', $message);
    }

    /**
     * Get expense statistics.
     */
    protected function getExpenseStats(Request $request): array
    {
        $query = Expense::query();

        // Apply same filters as main query
        if ($request->category_id) {
            $query->where('expense_category_id', $request->category_id);
        }
        if ($request->vendor_id) {
            $query->where('vendor_id', $request->vendor_id);
        }
        if ($request->date_from) {
            $query->whereDate('expense_date', '>=', $request->date_from);
        }
        if ($request->date_to) {
            $query->whereDate('expense_date', '<=', $request->date_to);
        }

        $totalExpenses = $query->sum('total_amount');
        $pendingExpenses = $query->clone()->where('status', 'pending')->sum('total_amount');
        $approvedExpenses = $query->clone()->where('status', 'approved')->sum('total_amount');
        $paidExpenses = $query->clone()->where('status', 'paid')->sum('total_amount');
        $overdueExpenses = $query->clone()->overdue()->sum('total_amount');

        return [
            'total_expenses' => $totalExpenses,
            'pending_expenses' => $pendingExpenses,
            'approved_expenses' => $approvedExpenses,
            'paid_expenses' => $paidExpenses,
            'overdue_expenses' => $overdueExpenses,
            'expense_count' => $query->count(),
        ];
    }

    /**
     * Get status options.
     */
    protected function getStatusOptions(): array
    {
        return [
            ['value' => 'pending', 'label' => 'Pending'],
            ['value' => 'approved', 'label' => 'Approved'],
            ['value' => 'rejected', 'label' => 'Rejected'],
            ['value' => 'paid', 'label' => 'Paid'],
            ['value' => 'cancelled', 'label' => 'Cancelled'],
        ];
    }

    /**
     * Get priority options.
     */
    protected function getPriorityOptions(): array
    {
        return [
            ['value' => 'low', 'label' => 'Low'],
            ['value' => 'medium', 'label' => 'Medium'],
            ['value' => 'high', 'label' => 'High'],
            ['value' => 'urgent', 'label' => 'Urgent'],
        ];
    }
}
