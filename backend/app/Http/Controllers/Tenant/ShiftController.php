<?php

namespace App\Http\Controllers\Tenant;

use App\Http\Controllers\Controller;
use App\Models\Shift;
use App\Models\User;
use Illuminate\Http\Request;
use Inertia\Inertia;
use Carbon\Carbon;

class ShiftController extends Controller
{
    public function __construct()
    {
        $this->middleware(['role:admin|restaurant_manager']);
    }

    /**
     * Display a listing of shifts
     */
    public function index(Request $request)
    {
        $query = Shift::with(['user']);

        // Date range filter
        if ($request->filled('date_from')) {
            $query->whereDate('date', '>=', $request->date_from);
        }
        if ($request->filled('date_to')) {
            $query->whereDate('date', '<=', $request->date_to);
        } else {
            // Default to current week
            $query->whereBetween('date', [
                now()->startOfWeek(),
                now()->endOfWeek()
            ]);
        }

        // Employee filter
        if ($request->filled('employee_id')) {
            $query->where('user_id', $request->employee_id);
        }

        // Status filter
        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }

        // Department filter
        if ($request->filled('department')) {
            $query->whereHas('user', function ($q) use ($request) {
                $q->where('department', $request->department);
            });
        }

        $shifts = $query->orderBy('date')
                       ->orderBy('start_time')
                       ->paginate(20);

        $employees = User::where('is_active', true)
                        ->orderBy('name')
                        ->get(['id', 'name', 'department']);

        $departments = ['kitchen', 'service', 'management', 'delivery', 'cleaning'];

        return Inertia::render('Tenant/Shifts/Index', [
            'shifts' => $shifts,
            'employees' => $employees,
            'departments' => $departments,
            'filters' => $request->only(['date_from', 'date_to', 'employee_id', 'status', 'department']),
            'stats' => [
                'total_shifts' => Shift::whereDate('date', today())->count(),
                'active_shifts' => Shift::where('status', 'active')->count(),
                'completed_shifts' => Shift::where('status', 'completed')->whereDate('date', today())->count(),
                'total_hours_today' => Shift::whereDate('date', today())->sum('hours_worked'),
            ]
        ]);
    }

    /**
     * Show the form for creating a new shift
     */
    public function create()
    {
        $employees = User::where('is_active', true)
                        ->orderBy('name')
                        ->get(['id', 'name', 'department', 'hourly_rate']);

        return Inertia::render('Tenant/Shifts/Create', [
            'employees' => $employees,
        ]);
    }

    /**
     * Store a newly created shift
     */
    public function store(Request $request)
    {
        $request->validate([
            'user_id' => 'required|exists:users,id',
            'date' => 'required|date',
            'start_time' => 'required|date_format:H:i',
            'end_time' => 'required|date_format:H:i|after:start_time',
            'break_duration' => 'nullable|integer|min:0|max:480', // Max 8 hours break
            'notes' => 'nullable|string|max:500',
            'is_recurring' => 'boolean',
            'recurring_days' => 'nullable|array',
            'recurring_days.*' => 'in:monday,tuesday,wednesday,thursday,friday,saturday,sunday',
            'recurring_end_date' => 'nullable|date|after:date',
        ]);

        $employee = User::findOrFail($request->user_id);

        // Calculate hours worked
        $startTime = Carbon::createFromFormat('H:i', $request->start_time);
        $endTime = Carbon::createFromFormat('H:i', $request->end_time);
        $hoursWorked = $endTime->diffInHours($startTime) - ($request->break_duration / 60);

        $shiftData = [
            'user_id' => $request->user_id,
            'date' => $request->date,
            'start_time' => $request->start_time,
            'end_time' => $request->end_time,
            'break_duration' => $request->break_duration ?? 0,
            'hours_worked' => $hoursWorked,
            'hourly_rate' => $employee->hourly_rate ?? 0,
            'total_pay' => $hoursWorked * ($employee->hourly_rate ?? 0),
            'notes' => $request->notes,
            'status' => 'scheduled',
            'created_by' => auth()->id(),
        ];

        if ($request->boolean('is_recurring') && $request->recurring_days) {
            $this->createRecurringShifts($shiftData, $request);
        } else {
            Shift::create($shiftData);
        }

        return redirect()->route('tenant.shifts.index')
            ->with('success', 'Shift(s) created successfully.');
    }

    /**
     * Display the specified shift
     */
    public function show(Shift $shift)
    {
        $shift->load(['user', 'timeEntries']);

        return Inertia::render('Tenant/Shifts/Show', [
            'shift' => $shift,
        ]);
    }

    /**
     * Show the form for editing the specified shift
     */
    public function edit(Shift $shift)
    {
        $shift->load('user');
        $employees = User::where('is_active', true)
                        ->orderBy('name')
                        ->get(['id', 'name', 'department', 'hourly_rate']);

        return Inertia::render('Tenant/Shifts/Edit', [
            'shift' => $shift,
            'employees' => $employees,
        ]);
    }

    /**
     * Update the specified shift
     */
    public function update(Request $request, Shift $shift)
    {
        $request->validate([
            'user_id' => 'required|exists:users,id',
            'date' => 'required|date',
            'start_time' => 'required|date_format:H:i',
            'end_time' => 'required|date_format:H:i|after:start_time',
            'break_duration' => 'nullable|integer|min:0|max:480',
            'notes' => 'nullable|string|max:500',
            'status' => 'required|in:scheduled,active,completed,cancelled',
        ]);

        $employee = User::findOrFail($request->user_id);

        // Calculate hours worked
        $startTime = Carbon::createFromFormat('H:i', $request->start_time);
        $endTime = Carbon::createFromFormat('H:i', $request->end_time);
        $hoursWorked = $endTime->diffInHours($startTime) - ($request->break_duration / 60);

        $shift->update([
            'user_id' => $request->user_id,
            'date' => $request->date,
            'start_time' => $request->start_time,
            'end_time' => $request->end_time,
            'break_duration' => $request->break_duration ?? 0,
            'hours_worked' => $hoursWorked,
            'hourly_rate' => $employee->hourly_rate ?? 0,
            'total_pay' => $hoursWorked * ($employee->hourly_rate ?? 0),
            'notes' => $request->notes,
            'status' => $request->status,
        ]);

        return redirect()->route('tenant.shifts.index')
            ->with('success', 'Shift updated successfully.');
    }

    /**
     * Remove the specified shift
     */
    public function destroy(Shift $shift)
    {
        // Prevent deleting active shifts
        if ($shift->status === 'active') {
            return back()->withErrors([
                'shift' => 'Cannot delete an active shift. Please end the shift first.'
            ]);
        }

        $shift->delete();

        return redirect()->route('tenant.shifts.index')
            ->with('success', 'Shift deleted successfully.');
    }

    /**
     * Start a shift
     */
    public function start(Shift $shift)
    {
        if ($shift->status !== 'scheduled') {
            return response()->json([
                'success' => false,
                'message' => 'Only scheduled shifts can be started.',
            ], 400);
        }

        $shift->update([
            'status' => 'active',
            'actual_start_time' => now()->format('H:i:s'),
        ]);

        // Update user's shift status
        $shift->user->update(['is_on_shift' => true]);

        return response()->json([
            'success' => true,
            'message' => 'Shift started successfully.',
        ]);
    }

    /**
     * End a shift
     */
    public function end(Shift $shift)
    {
        if ($shift->status !== 'active') {
            return response()->json([
                'success' => false,
                'message' => 'Only active shifts can be ended.',
            ], 400);
        }

        $actualEndTime = now()->format('H:i:s');
        
        // Calculate actual hours worked
        $actualStart = Carbon::createFromFormat('H:i:s', $shift->actual_start_time);
        $actualEnd = Carbon::createFromFormat('H:i:s', $actualEndTime);
        $actualHours = $actualEnd->diffInHours($actualStart) - ($shift->break_duration / 60);

        $shift->update([
            'status' => 'completed',
            'actual_end_time' => $actualEndTime,
            'actual_hours_worked' => $actualHours,
            'actual_total_pay' => $actualHours * $shift->hourly_rate,
        ]);

        // Update user's shift status
        $shift->user->update(['is_on_shift' => false]);

        return response()->json([
            'success' => true,
            'message' => 'Shift ended successfully.',
        ]);
    }

    /**
     * Get shift calendar data
     */
    public function calendar(Request $request)
    {
        $startDate = $request->get('start', now()->startOfMonth());
        $endDate = $request->get('end', now()->endOfMonth());

        $shifts = Shift::with('user')
                      ->whereBetween('date', [$startDate, $endDate])
                      ->get()
                      ->map(function ($shift) {
                          return [
                              'id' => $shift->id,
                              'title' => $shift->user->name,
                              'start' => $shift->date . 'T' . $shift->start_time,
                              'end' => $shift->date . 'T' . $shift->end_time,
                              'backgroundColor' => $this->getShiftColor($shift->status),
                              'borderColor' => $this->getShiftColor($shift->status),
                              'extendedProps' => [
                                  'employee' => $shift->user->name,
                                  'department' => $shift->user->department,
                                  'status' => $shift->status,
                                  'hours' => $shift->hours_worked,
                              ],
                          ];
                      });

        return response()->json($shifts);
    }

    /**
     * Create recurring shifts
     */
    private function createRecurringShifts(array $shiftData, Request $request)
    {
        $startDate = Carbon::parse($request->date);
        $endDate = Carbon::parse($request->recurring_end_date);
        $recurringDays = $request->recurring_days;

        $currentDate = $startDate->copy();
        
        while ($currentDate->lte($endDate)) {
            $dayName = strtolower($currentDate->format('l'));
            
            if (in_array($dayName, $recurringDays)) {
                $shiftData['date'] = $currentDate->format('Y-m-d');
                Shift::create($shiftData);
            }
            
            $currentDate->addDay();
        }
    }

    /**
     * Get shift color based on status
     */
    private function getShiftColor($status)
    {
        return match ($status) {
            'scheduled' => '#3B82F6', // Blue
            'active' => '#10B981',     // Green
            'completed' => '#6B7280',  // Gray
            'cancelled' => '#EF4444',  // Red
            default => '#6B7280',
        };
    }

    /**
     * Export shifts data
     */
    public function export(Request $request)
    {
        $request->validate([
            'format' => 'required|in:csv,excel,pdf',
            'date_from' => 'required|date',
            'date_to' => 'required|date|after_or_equal:date_from',
        ]);

        // This would implement actual export logic
        return response()->json([
            'success' => true,
            'message' => 'Shifts data export will be sent to your email shortly.',
        ]);
    }

    /**
     * Get shift statistics
     */
    public function getStats(Request $request)
    {
        $dateFrom = $request->get('date_from', now()->startOfMonth());
        $dateTo = $request->get('date_to', now()->endOfMonth());

        $stats = [
            'total_shifts' => Shift::whereBetween('date', [$dateFrom, $dateTo])->count(),
            'completed_shifts' => Shift::whereBetween('date', [$dateFrom, $dateTo])
                                      ->where('status', 'completed')->count(),
            'total_hours' => Shift::whereBetween('date', [$dateFrom, $dateTo])
                                 ->sum('actual_hours_worked'),
            'total_payroll' => Shift::whereBetween('date', [$dateFrom, $dateTo])
                                   ->sum('actual_total_pay'),
            'average_hours_per_shift' => Shift::whereBetween('date', [$dateFrom, $dateTo])
                                             ->avg('actual_hours_worked'),
            'department_breakdown' => $this->getDepartmentBreakdown($dateFrom, $dateTo),
        ];

        return response()->json($stats);
    }

    /**
     * Get department breakdown
     */
    private function getDepartmentBreakdown($dateFrom, $dateTo)
    {
        return Shift::whereBetween('date', [$dateFrom, $dateTo])
                   ->join('users', 'shifts.user_id', '=', 'users.id')
                   ->selectRaw('users.department, COUNT(*) as shift_count, SUM(shifts.actual_hours_worked) as total_hours')
                   ->groupBy('users.department')
                   ->get();
    }
}
