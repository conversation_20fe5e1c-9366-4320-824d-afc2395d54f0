<?php

namespace App\Http\Controllers\Tenant;

use App\Http\Controllers\Controller;
use App\Models\Department;
use App\Models\User;
use Illuminate\Http\Request;
use Inertia\Inertia;
use Inertia\Response;

class DepartmentController extends Controller
{
    public function __construct()
    {
        $this->middleware(['auth', 'verified']);
        $this->middleware('role:admin|restaurant_manager');
    }

    /**
     * Display a listing of departments
     */
    public function index(Request $request): Response
    {
        $query = Department::with(['manager', 'employees'])
                          ->withCount(['employees']);

        // Apply search filter
        if ($request->filled('search')) {
            $query->where('name', 'like', "%{$request->search}%")
                  ->orWhere('description', 'like', "%{$request->search}%");
        }

        // Apply status filter
        if ($request->filled('status')) {
            if ($request->status === 'active') {
                $query->active();
            } elseif ($request->status === 'inactive') {
                $query->where('is_active', false);
            }
        }

        // Apply sorting
        $sortField = $request->get('sort', 'sort_order');
        $sortDirection = $request->get('direction', 'asc');
        $query->orderBy($sortField, $sortDirection);

        $departments = $query->paginate(15)->withQueryString();

        // Get statistics
        $stats = [
            'total_departments' => Department::count(),
            'active_departments' => Department::active()->count(),
            'total_employees' => User::where('is_active', true)->count(),
            'departments_with_managers' => Department::whereNotNull('manager_id')->count(),
        ];

        // Get available managers (users who can be department managers)
        $availableManagers = User::where('is_active', true)
                                ->whereIn('role', ['admin', 'restaurant_manager'])
                                ->get(['id', 'name', 'email']);

        return Inertia::render('Tenant/Departments/Index', [
            'departments' => $departments,
            'stats' => $stats,
            'availableManagers' => $availableManagers,
            'filters' => $request->only(['search', 'status', 'sort', 'direction']),
        ]);
    }

    /**
     * Show the form for creating a new department
     */
    public function create(): Response
    {
        $availableManagers = User::where('is_active', true)
                                ->whereIn('role', ['admin', 'restaurant_manager'])
                                ->get(['id', 'name', 'email']);

        return Inertia::render('Tenant/Departments/Create', [
            'availableManagers' => $availableManagers,
        ]);
    }

    /**
     * Store a newly created department
     */
    public function store(Request $request)
    {
        $validated = $request->validate([
            'name' => 'required|string|max:255|unique:departments,name',
            'description' => 'nullable|string|max:1000',
            'manager_id' => 'nullable|exists:users,id',
            'is_active' => 'boolean',
            'sort_order' => 'integer|min:0',
        ]);

        $department = Department::create($validated);

        return redirect()->route('departments.index')
                        ->with('success', __('Department created successfully.'));
    }

    /**
     * Display the specified department
     */
    public function show(Department $department): Response
    {
        $department->load([
            'manager',
            'employees' => function ($query) {
                $query->where('is_active', true)->orderBy('name');
            }
        ]);

        // Get department statistics
        $stats = [
            'total_employees' => $department->employees()->count(),
            'active_employees' => $department->activeEmployees()->count(),
            'on_shift_employees' => $department->activeEmployees()->where('is_on_shift', true)->count(),
            'has_manager' => !is_null($department->manager_id),
        ];

        return Inertia::render('Tenant/Departments/Show', [
            'department' => $department,
            'stats' => $stats,
        ]);
    }

    /**
     * Show the form for editing the department
     */
    public function edit(Department $department): Response
    {
        $availableManagers = User::where('is_active', true)
                                ->whereIn('role', ['admin', 'restaurant_manager'])
                                ->get(['id', 'name', 'email']);

        return Inertia::render('Tenant/Departments/Edit', [
            'department' => $department,
            'availableManagers' => $availableManagers,
        ]);
    }

    /**
     * Update the specified department
     */
    public function update(Request $request, Department $department)
    {
        $validated = $request->validate([
            'name' => 'required|string|max:255|unique:departments,name,' . $department->id,
            'description' => 'nullable|string|max:1000',
            'manager_id' => 'nullable|exists:users,id',
            'is_active' => 'boolean',
            'sort_order' => 'integer|min:0',
        ]);

        $department->update($validated);

        return redirect()->route('departments.index')
                        ->with('success', __('Department updated successfully.'));
    }

    /**
     * Remove the specified department
     */
    public function destroy(Department $department)
    {
        // Check if department has employees
        if ($department->hasEmployees()) {
            return back()->with('error', __('Cannot delete department with employees. Please reassign employees first.'));
        }

        $department->delete();

        return redirect()->route('departments.index')
                        ->with('success', __('Department deleted successfully.'));
    }

    /**
     * Toggle department status
     */
    public function toggleStatus(Department $department)
    {
        $department->update(['is_active' => !$department->is_active]);

        $status = $department->is_active ? 'activated' : 'deactivated';
        
        return back()->with('success', __("Department {$status} successfully."));
    }

    /**
     * Reorder departments
     */
    public function reorder(Request $request)
    {
        $validated = $request->validate([
            'departments' => 'required|array',
            'departments.*.id' => 'required|exists:departments,id',
            'departments.*.sort_order' => 'required|integer|min:0',
        ]);

        foreach ($validated['departments'] as $departmentData) {
            Department::where('id', $departmentData['id'])
                     ->update(['sort_order' => $departmentData['sort_order']]);
        }

        return back()->with('success', __('Departments reordered successfully.'));
    }

    /**
     * Bulk update departments
     */
    public function bulkUpdate(Request $request)
    {
        $validated = $request->validate([
            'action' => 'required|in:activate,deactivate,delete',
            'department_ids' => 'required|array|min:1',
            'department_ids.*' => 'exists:departments,id',
        ]);

        $departments = Department::whereIn('id', $validated['department_ids']);

        switch ($validated['action']) {
            case 'activate':
                $departments->update(['is_active' => true]);
                $message = __('Selected departments activated successfully.');
                break;

            case 'deactivate':
                $departments->update(['is_active' => false]);
                $message = __('Selected departments deactivated successfully.');
                break;

            case 'delete':
                // Check if any department has employees
                $departmentsWithEmployees = $departments->whereHas('employees')->count();
                if ($departmentsWithEmployees > 0) {
                    return back()->with('error', __('Cannot delete departments with employees. Please reassign employees first.'));
                }
                
                $departments->delete();
                $message = __('Selected departments deleted successfully.');
                break;
        }

        return back()->with('success', $message);
    }

    /**
     * Get department employees
     */
    public function getEmployees(Department $department)
    {
        $employees = $department->employees()
                               ->where('is_active', true)
                               ->select(['id', 'name', 'email', 'position', 'is_on_shift'])
                               ->get();

        return response()->json($employees);
    }

    /**
     * Assign manager to department
     */
    public function assignManager(Request $request, Department $department)
    {
        $validated = $request->validate([
            'manager_id' => 'required|exists:users,id',
        ]);

        // Verify the user can be a manager
        $user = User::find($validated['manager_id']);
        if (!in_array($user->role, ['admin', 'restaurant_manager'])) {
            return back()->with('error', __('Selected user cannot be assigned as department manager.'));
        }

        $department->update(['manager_id' => $validated['manager_id']]);

        return back()->with('success', __('Manager assigned successfully.'));
    }

    /**
     * Remove manager from department
     */
    public function removeManager(Department $department)
    {
        $department->update(['manager_id' => null]);

        return back()->with('success', __('Manager removed successfully.'));
    }
}
