<?php

namespace App\Http\Controllers\Tenant;

use App\Http\Controllers\Controller;
use App\Models\Tenant\Employee;
use App\Models\Tenant\Branch;
use App\Models\Tenant\Department;
use App\Models\Tenant\EmployeeDocument;
use App\Models\Tenant\Media;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Hash;
use Illuminate\Validation\Rule;
use Inertia\Inertia;

class EnhancedEmployeeController extends Controller
{
    /**
     * Display a listing of employees.
     */
    public function index(Request $request)
    {
        $query = Employee::with(['primaryBranch', 'department', 'documents']);

        // Search functionality
        if ($request->filled('search')) {
            $query->where(function ($q) use ($request) {
                $q->where('name', 'like', '%' . $request->search . '%')
                  ->orWhere('first_name', 'like', '%' . $request->search . '%')
                  ->orWhere('last_name', 'like', '%' . $request->search . '%')
                  ->orWhere('email', 'like', '%' . $request->search . '%')
                  ->orWhere('phone', 'like', '%' . $request->search . '%')
                  ->orWhere('employee_id', 'like', '%' . $request->search . '%');
            });
        }

        // Filter by branch
        if ($request->filled('branch_id')) {
            $query->where('primary_branch_id', $request->branch_id);
        }

        // Filter by department
        if ($request->filled('department_id')) {
            $query->where('department_id', $request->department_id);
        }

        // Filter by employee type
        if ($request->filled('employee_type')) {
            $query->where('employee_type', $request->employee_type);
        }

        // Filter by employment status
        if ($request->filled('employment_status')) {
            $query->where('employment_status', $request->employment_status);
        }

        $employees = $query->orderBy('name')
            ->paginate(15);

        $branches = Branch::active()->ordered()->get(['id', 'name']);
        $departments = Department::orderBy('name')->get(['id', 'name']);

        return Inertia::render('Tenant/Employees/Enhanced/Index', [
            'employees' => $employees,
            'branches' => $branches,
            'departments' => $departments,
            'filters' => $request->only(['search', 'branch_id', 'department_id', 'employee_type', 'employment_status']),
            'employeeTypes' => [
                'waiter' => 'Waiter',
                'chef' => 'Chef',
                'manager' => 'Manager',
                'cashier' => 'Cashier',
                'cleaner' => 'Cleaner',
                'security' => 'Security',
                'other' => 'Other',
            ],
            'employmentStatuses' => [
                'active' => 'Active',
                'inactive' => 'Inactive',
                'terminated' => 'Terminated',
                'resigned' => 'Resigned',
            ],
        ]);
    }

    /**
     * Show the form for creating a new employee.
     */
    public function create()
    {
        $branches = Branch::active()->ordered()->get(['id', 'name']);
        $departments = Department::orderBy('name')->get(['id', 'name']);

        return Inertia::render('Tenant/Employees/Enhanced/Create', [
            'branches' => $branches,
            'departments' => $departments,
            'employeeTypes' => [
                'waiter' => 'Waiter',
                'chef' => 'Chef',
                'manager' => 'Manager',
                'cashier' => 'Cashier',
                'cleaner' => 'Cleaner',
                'security' => 'Security',
                'other' => 'Other',
            ],
            'bloodGroups' => ['A+', 'A-', 'B+', 'B-', 'AB+', 'AB-', 'O+', 'O-'],
            'documentTypes' => EmployeeDocument::DOCUMENT_TYPES,
        ]);
    }

    /**
     * Store a newly created employee.
     */
    public function store(Request $request)
    {
        $validator = Validator::make($request->all(), [
            // Basic Information
            'name' => 'required|string|max:255',
            'username' => 'required|string|max:255|unique:employees,username',
            'email' => 'nullable|email|max:255|unique:employees,email',
            'phone' => 'nullable|string|max:20',
            'address' => 'nullable|string|max:500',
            'emergency_contact' => 'nullable|string|max:255',
            'nid' => 'nullable|string|max:50',
            'blood_group' => 'nullable|in:A+,A-,B+,B-,AB+,AB-,O+,O-',
            
            // Professional Information
            'primary_branch_id' => 'required|exists:branches,id',
            'department_id' => 'nullable|exists:departments,id',
            'employee_type' => 'required|in:waiter,chef,manager,cashier,cleaner,security,other',
            'experience_years' => 'nullable|integer|min:0|max:50',
            'salary' => 'nullable|numeric|min:0',
            'hire_date' => 'required|date',
            'employment_status' => 'required|in:active,inactive,terminated,resigned',
            
            // Additional Information
            'notes' => 'nullable|string|max:1000',
            'skills' => 'nullable|array',
            'certifications' => 'nullable|array',
            'is_active' => 'boolean',
            
            // Documents
            'documents' => 'nullable|array|max:5',
            'documents.*.media_id' => 'required|exists:media,id',
            'documents.*.document_type' => 'required|in:' . implode(',', array_keys(EmployeeDocument::DOCUMENT_TYPES)),
            'documents.*.title' => 'required|string|max:255',
            'documents.*.description' => 'nullable|string|max:500',
            'documents.*.is_required' => 'boolean',
            'documents.*.expiry_date' => 'nullable|date|after:today',
        ]);

        if ($validator->fails()) {
            return back()->withErrors($validator)->withInput();
        }

        $data = $validator->validated();
        $documents = $data['documents'] ?? [];
        unset($data['documents']);

        // Create employee
        $employee = Employee::create($data);

        // Sync documents
        if (!empty($documents)) {
            $employee->syncDocuments($documents);
        }

        return redirect()->route('enhanced-employees.index')
            ->with('success', 'Employee created successfully.');
    }

    /**
     * Display the specified employee.
     */
    public function show(Employee $enhancedEmployee)
    {
        $enhancedEmployee->load([
            'primaryBranch',
            'department',
            'documents.media',
            'employeeShifts' => function ($query) {
                $query->with('branch')->latest()->limit(10);
            }
        ]);

        return Inertia::render('Tenant/Employees/Enhanced/Show', [
            'employee' => $enhancedEmployee,
        ]);
    }

    /**
     * Show the form for editing the specified employee.
     */
    public function edit(Employee $enhancedEmployee)
    {
        $enhancedEmployee->load(['documents.media']);
        
        $branches = Branch::active()->ordered()->get(['id', 'name']);
        $departments = Department::orderBy('name')->get(['id', 'name']);

        return Inertia::render('Tenant/Employees/Enhanced/Edit', [
            'employee' => $enhancedEmployee,
            'branches' => $branches,
            'departments' => $departments,
            'employeeTypes' => [
                'waiter' => 'Waiter',
                'chef' => 'Chef',
                'manager' => 'Manager',
                'cashier' => 'Cashier',
                'cleaner' => 'Cleaner',
                'security' => 'Security',
                'other' => 'Other',
            ],
            'bloodGroups' => ['A+', 'A-', 'B+', 'B-', 'AB+', 'AB-', 'O+', 'O-'],
            'documentTypes' => EmployeeDocument::DOCUMENT_TYPES,
        ]);
    }

    /**
     * Update the specified employee.
     */
    public function update(Request $request, Employee $enhancedEmployee)
    {
        $validator = Validator::make($request->all(), [
            // Basic Information
            'name' => 'required|string|max:255',
            'username' => 'required|string|max:255|unique:employees,username,' . $enhancedEmployee->id,
            'email' => 'nullable|email|max:255|unique:employees,email,' . $enhancedEmployee->id,
            'phone' => 'nullable|string|max:20',
            'address' => 'nullable|string|max:500',
            'emergency_contact' => 'nullable|string|max:255',
            'nid' => 'nullable|string|max:50',
            'blood_group' => 'nullable|in:A+,A-,B+,B-,AB+,AB-,O+,O-',
            
            // Professional Information
            'primary_branch_id' => 'required|exists:branches,id',
            'department_id' => 'nullable|exists:departments,id',
            'employee_type' => 'required|in:waiter,chef,manager,cashier,cleaner,security,other',
            'experience_years' => 'nullable|integer|min:0|max:50',
            'salary' => 'nullable|numeric|min:0',
            'hire_date' => 'required|date',
            'employment_status' => 'required|in:active,inactive,terminated,resigned',
            
            // Additional Information
            'notes' => 'nullable|string|max:1000',
            'skills' => 'nullable|array',
            'certifications' => 'nullable|array',
            'is_active' => 'boolean',
            
            // Documents
            'documents' => 'nullable|array|max:5',
            'documents.*.media_id' => 'required|exists:media,id',
            'documents.*.document_type' => 'required|in:' . implode(',', array_keys(EmployeeDocument::DOCUMENT_TYPES)),
            'documents.*.title' => 'required|string|max:255',
            'documents.*.description' => 'nullable|string|max:500',
            'documents.*.is_required' => 'boolean',
            'documents.*.expiry_date' => 'nullable|date|after:today',
        ]);

        if ($validator->fails()) {
            return back()->withErrors($validator)->withInput();
        }

        $data = $validator->validated();
        $documents = $data['documents'] ?? [];
        unset($data['documents']);

        // Update employee
        $enhancedEmployee->update($data);

        // Sync documents
        $enhancedEmployee->syncDocuments($documents);

        return redirect()->route('enhanced-employees.index')
            ->with('success', 'Employee updated successfully.');
    }

    /**
     * Remove the specified employee.
     */
    public function destroy(Employee $enhancedEmployee)
    {
        // Check if employee has active shifts
        if ($enhancedEmployee->employeeShifts()->whereIn('status', ['scheduled', 'confirmed', 'started'])->exists()) {
            return back()->withErrors(['error' => 'Cannot delete employee with active or scheduled shifts.']);
        }

        $enhancedEmployee->delete();

        return redirect()->route('enhanced-employees.index')
            ->with('success', 'Employee deleted successfully.');
    }

    /**
     * Get employees for API/AJAX requests.
     */
    public function getEmployees(Request $request)
    {
        $query = Employee::query();

        if ($request->filled('branch_id')) {
            $query->where('primary_branch_id', $request->branch_id);
        }

        if ($request->filled('employee_type')) {
            $query->where('employee_type', $request->employee_type);
        }

        $employees = $query->where('employment_status', 'active')
            ->select('id', 'name', 'first_name', 'last_name', 'employee_type', 'primary_branch_id')
            ->get()
            ->map(function ($employee) {
                return [
                    'id' => $employee->id,
                    'name' => $employee->display_name,
                    'employee_type' => $employee->employee_type,
                    'branch_id' => $employee->primary_branch_id,
                ];
            });

        return response()->json($employees);
    }
}
