<?php

namespace App\Http\Controllers\Tenant;

use App\Http\Controllers\Controller;
use App\Services\Tenant\POSService;
use App\Services\BranchSelectionService;
use App\Models\Tenant\Order;
use App\Models\Tenant\OrderItem;
use App\Models\Tenant\Table;
use App\Models\Tenant\Floor;
use App\Models\Tenant\Branch;
use App\Models\Tenant\Category;
use App\Models\Tenant\Customer;
use App\Models\Tenant\MenuItem;
use App\Models\Tenant\User;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Hash;
use Inertia\Inertia;
use Inertia\Response;

class POSController extends Controller
{
    public function __construct(
        protected POSService $posService
    ) {}

    /**
     * Display the main POS interface
     */
    public function index(Request $request): Response
    {
        // Get current user
        $currentUser = auth('tenant')->user();

        // Get selected branch from session or user's primary branch
        $selectedBranchId = $request->session()->get('pos_selected_branch_id');

        if (!$selectedBranchId) {
            // Try to get user's primary branch or first available branch
            $selectedBranchId = $currentUser->primary_branch_id ?? Branch::active()->first()?->id;

            if ($selectedBranchId) {
                $request->session()->put('pos_selected_branch_id', $selectedBranchId);
            }
        }

        // Get all active branches for branch selection
        $allBranches = Branch::active()->ordered()->get(['id', 'name', 'is_main_branch']);

        // Get the selected branch
        $branch = Branch::find($selectedBranchId);

        if (!$branch) {
            // Fallback to first available branch
            $branch = $this->ensureBranchExists();
            if ($branch) {
                $selectedBranchId = $branch->id;
                $request->session()->put('pos_selected_branch_id', $selectedBranchId);
            }
        }

        if (!$branch) {
            return Inertia::render('Tenant/POS/NoBranch', [
                'error' => 'No branches found. Please create a branch first.'
            ]);
        }

        $branch->load(['floors.tables']);

        // Get menu items available for this branch
        $categories = Category::active()
            ->with(['menuItems'])
            ->ordered()
            ->get();
            // dd($categories);
        // Get combo items available for POS (now from menu_items table)
        $comboItems = MenuItem::with(['comboItems', 'primaryMedia', 'mediaItems'])
            ->where('is_combo', true)
            ->where('is_available', true)
            ->ordered()
            ->get()
            ->map(function ($combo) {
                return [
                    'id' => $combo->id,
                    'name' => $combo->name,
                    'description' => $combo->description,
                    'price' => $combo->price,
                    'primary_image_url' => $combo->primary_image_url,
                    'thumbnail_url' => $combo->thumbnail_url,
                    'is_combo' => true,
                    'components' => $combo->comboItems->map(function ($item) {
                        return [
                            'id' => $item->id,
                            'component_type' => $item->pivot->component_type,
                            'is_required' => $item->pivot->is_required,
                            'menu_item' => [
                                'id' => $item->id,
                                'name' => $item->name,
                                'price' => $item->price,
                            ]
                        ];
                    }),
                ];
            });

        // Add combo items as a special category
        if ($comboItems->isNotEmpty()) {
            $comboCategory = (object) [
                'id' => 'combo',
                'name' => 'Combo Deals',
                'description' => 'Special combo menu items',
                'menu_items' => $comboItems,
            ];
            $categories->prepend($comboCategory);
        }
            
            // dd($categories);
            // dd($categories);
        // Get active orders for this branch
        $activeOrders = Order::with(['table', 'items.menuItem', 'payments'])
            ->byBranch($selectedBranchId)
            ->open()
            ->posOrders()
            ->latest()
            ->get();

        // Get customers for the dropdown (create some sample data if none exist)
        $customers = Customer::orderBy('name')->limit(50)->get(['id', 'name', 'phone', 'email']);
        // dd($customers);

        // Get waiters for the dropdown (use real users from database)
        $waiters = $this->getWaiters();

        // Get current authenticated user
        $currentUser = auth('tenant')->user();

        // Get waiter's assigned table (if any) for default selection
        $waiterAssignedTable = null;
        if ($currentUser->role === 'waiter') {
            $waiterAssignedTable = \App\Models\Tenant\Table::where('assigned_waiter_id', $currentUser->id)
                ->where('branch_id', $selectedBranchId)
                ->first();
        }

        return Inertia::render('Tenant/POS/Index', [
            'branch' => $branch,
            'allBranches' => $allBranches,
            'selectedBranchId' => $selectedBranchId,
            'categories' => $categories,
            'activeOrders' => $activeOrders,
            'floors' => $branch->floors,
            'paymentMethods' => $this->getPaymentMethods(),
            'discountTypes' => $this->getDiscountTypes(),
            'customers' => $customers,
            'waiters' => $waiters,
            'waiterAssignedTable' => $waiterAssignedTable,
            'currentUser' => [
                'id' => $currentUser->id,
                'name' => $currentUser->name,
                'email' => $currentUser->email,
                'role' => $currentUser->role,
                'primary_branch_id' => $currentUser->primary_branch_id ?? null,
            ],
        ]);
    }

    /**
     * Display table view for POS
     */
    public function tableView(Request $request): Response
    {
        $currentUser = auth('tenant')->user();

        // Get selected branch from session
        $selectedBranchId = $request->session()->get('pos_selected_branch_id');

        if (!$selectedBranchId) {
            // Try to get user's primary branch or first available branch
            $selectedBranchId = $currentUser->primary_branch_id ?? Branch::active()->first()?->id;

            if ($selectedBranchId) {
                $request->session()->put('pos_selected_branch_id', $selectedBranchId);
            }
        }

        // Get all active branches for branch selection
        $allBranches = Branch::active()->ordered()->get(['id', 'name', 'is_main_branch']);

        // Get the selected branch
        $branch = Branch::find($selectedBranchId);

        if (!$branch) {
            // Fallback to first available branch
            $branch = $this->ensureBranchExists();
            if ($branch) {
                $selectedBranchId = $branch->id;
                $request->session()->put('pos_selected_branch_id', $selectedBranchId);
            }
        }

        if (!$branch) {
            return Inertia::render('Tenant/POS/NoBranch', [
                'error' => 'No branches found. Please create a branch first.'
            ]);
        }

        $branch->load(['floors.tables.orders' => function ($query) {
            $query->open()->posOrders()->with(['items', 'payments']);
        }]);

        return Inertia::render('Tenant/POS/TableView', [
            'branch' => $branch,
            'allBranches' => $allBranches,
            'selectedBranchId' => $selectedBranchId,
            'floors' => $branch->floors,
        ]);
    }



    /**
     * Display ongoing orders for POS
     */
    public function ongoingOrders(Request $request): Response
    {
        $currentUser = auth('tenant')->user();

        // Get selected branch from session
        $selectedBranchId = $request->session()->get('pos_selected_branch_id');

        if (!$selectedBranchId) {
            // Try to get user's primary branch or first available branch
            $selectedBranchId = $currentUser->primary_branch_id ?? Branch::active()->first()?->id;

            if ($selectedBranchId) {
                $request->session()->put('pos_selected_branch_id', $selectedBranchId);
            }
        }

        // Get all active branches for branch selection
        $allBranches = Branch::active()->ordered()->get(['id', 'name', 'is_main_branch']);

        // Get the selected branch
        $branch = Branch::find($selectedBranchId);

        if (!$branch) {
            // Fallback to first available branch
            $branch = $this->ensureBranchExists();
            if ($branch) {
                $selectedBranchId = $branch->id;
                $request->session()->put('pos_selected_branch_id', $selectedBranchId);
            }
        }

        if (!$branch) {
            return Inertia::render('Tenant/POS/NoBranch', [
                'error' => 'No branches found. Please create a branch first.'
            ]);
        }

        // Get ongoing orders for this branch
        $orders = \App\Models\Tenant\Order::where('branch_id', $branch->id)
            ->whereIn('status', ['pending', 'confirmed', 'preparing', 'ready'])
            ->with(['table', 'items', 'payments'])
            ->orderBy('created_at', 'desc')
            ->get();

        return Inertia::render('Tenant/POS/OngoingOrders', [
            'branch' => $branch,
            'allBranches' => $allBranches,
            'selectedBranchId' => $selectedBranchId,
            'orders' => $orders,
        ]);
    }



    /**
     * Switch branch for POS
     */
    public function switchBranch(Request $request): JsonResponse
    {
        $request->validate([
            'branch_id' => 'required|exists:branches,id',
        ]);

        $branch = Branch::active()->find($request->branch_id);

        if (!$branch) {
            return response()->json([
                'success' => false,
                'message' => 'Branch not found or inactive.',
            ], 404);
        }

        // Store selected branch in session for POS
        $request->session()->put('pos_selected_branch_id', $request->branch_id);

        // Also use the service for any additional logic
        $result = BranchSelectionService::switchBranch($request->branch_id);

        // Override with our success response
        return response()->json([
            'success' => true,
            'message' => "Switched to {$branch->name} branch successfully.",
            'branch' => [
                'id' => $branch->id,
                'name' => $branch->name,
                'is_main_branch' => $branch->is_main_branch
            ],
        ]);
    }

    /**
     * Create new order
     */
    public function createOrder(Request $request): JsonResponse
    {
        $request->validate([
            'branch_id' => 'required|exists:branches,id',
            'table_id' => 'nullable|exists:tables,id',
            'customer_name' => 'nullable|string|max:255',
            'customer_phone' => 'nullable|string|max:20',
            'customer_email' => 'nullable|email|max:255',
            'order_type' => 'required|in:dine_in,takeaway,delivery',
            'guest_count' => 'nullable|integer|min:1|max:20',
            'special_instructions' => 'nullable|string|max:1000',
            'waiter_id' => 'nullable|exists:users,id', // For manager/POS use
        ]);

        try {
            $user = auth('tenant')->user();
            $orderData = $request->all();

            // Auto-assign waiter if user is a waiter
            if ($user->role === 'waiter') {
                $orderData['waiter_id'] = $user->id;

                // Validate waiter can only create orders for their assigned tables or unassigned tables
                if ($request->table_id) {
                    $table = Table::find($request->table_id);
                    if ($table && $table->assigned_waiter_id && $table->assigned_waiter_id !== $user->id) {
                        return response()->json([
                            'success' => false,
                            'message' => 'You can only create orders for tables assigned to you.',
                        ], 403);
                    }
                }
            }

            $order = $this->posService->createOrder($orderData, $user);

            // Add items to the order if provided
            if (!empty($orderData['items'])) {
                foreach ($orderData['items'] as $itemData) {
                    $this->posService->addItemToOrder($order, [
                        'menu_item_id' => $itemData['id'],
                        'quantity' => $itemData['quantity'],
                        'special_instructions' => $itemData['special_instructions'] ?? null,
                    ], $user);
                }
            }

            return response()->json([
                'success' => true,
                'order' => $order->fresh(['table', 'items.menuItem', 'payments']),
                'message' => 'Order created successfully',
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to create order: ' . $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Create a delivery order
     */
    public function createDeliveryOrder(Request $request)
    {
        $user = auth('tenant')->user();

        $request->validate([
            'customer_name' => 'required|string|max:255',
            'customer_phone' => 'required|string|max:20',
            'customer_email' => 'nullable|email|max:255',
            'delivery_address' => 'required|string|max:500',
            'delivery_latitude' => 'required|numeric',
            'delivery_longitude' => 'required|numeric',
            'delivery_instructions' => 'nullable|string|max:1000',
            'delivery_rider_id' => 'nullable|exists:users,id',
            'delivery_fee' => 'required|numeric|min:0',
            'estimated_delivery_time' => 'required|integer|min:1',
            'items' => 'required|array|min:1',
            'items.*.menu_item_id' => 'required|exists:menu_items,id',
            'items.*.quantity' => 'required|integer|min:1',
            'items.*.unit_price' => 'required|numeric|min:0',
            'items.*.special_instructions' => 'nullable|string|max:500',
            'items.*.modifiers' => 'nullable|array',
        ]);

        try {
            // Create or find customer
            $customer = Customer::firstOrCreate(
                ['phone' => $request->customer_phone],
                [
                    'name' => $request->customer_name,
                    'email' => $request->customer_email,
                    'type' => 'regular',
                    'tier' => 'regular',
                ]
            );

            // Calculate subtotal
            $subtotal = collect($request->items)->sum(function ($item) {
                return $item['unit_price'] * $item['quantity'];
            });

            // Create delivery order data
            $orderData = [
                'branch_id' => $request->branch_id,
                'customer_id' => $customer->id,
                'order_type' => 'delivery',
                'status' => 'pending',
                'subtotal_amount' => $subtotal,
                'delivery_fee' => $request->delivery_fee,
                'total_amount' => $subtotal + $request->delivery_fee,
                'payment_status' => 'pending',
                'delivery_address' => $request->delivery_address,
                'delivery_latitude' => $request->delivery_latitude,
                'delivery_longitude' => $request->delivery_longitude,
                'delivery_instructions' => $request->delivery_instructions,
                'estimated_delivery_time' => $request->estimated_delivery_time,
                'delivery_status' => 'pending',
            ];

            // Auto-assign waiter if user is a waiter
            if ($user->role === 'waiter') {
                $orderData['waiter_id'] = $user->id;
            }

            // Assign delivery rider if specified
            if ($request->delivery_rider_id) {
                $orderData['assigned_rider_id'] = $request->delivery_rider_id;
                $orderData['delivery_status'] = 'assigned';
            }

            // Use POS service to create the order
            $order = $this->posService->createOrder($orderData, $user);

            // Add items to the order
            foreach ($request->items as $itemData) {
                $this->posService->addItemToOrder($order, [
                    'menu_item_id' => $itemData['menu_item_id'],
                    'quantity' => $itemData['quantity'],
                    'unit_price' => $itemData['unit_price'],
                    'special_instructions' => $itemData['special_instructions'] ?? null,
                    'modifiers' => $itemData['modifiers'] ?? null,
                ], $user);
            }

            return response()->json([
                'success' => true,
                'order' => $order->fresh(['customer', 'items.menuItem']),
                'message' => 'Delivery order created successfully',
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to create delivery order: ' . $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Add item to order
     */
    public function addItem(Request $request, Order $order): JsonResponse
    {
        $request->validate([
            'menu_item_id' => 'required|exists:menu_items,id',
            'quantity' => 'required|integer|min:1|max:99',
            'special_instructions' => 'nullable|string|max:500',
            'variant_id' => 'nullable|exists:menu_item_variants,id',
            'addons' => 'nullable|array',
            'addons.*' => 'exists:menu_item_addons,id',
            'unit_price' => 'nullable|numeric|min:0',
            'total_price' => 'nullable|numeric|min:0',
            'modifiers' => 'nullable|array',
        ]);

        try {
            $orderItem = $this->posService->addItemToOrder($order, $request->all(), auth('tenant')->user());

            return response()->json([
                'success' => true,
                'order_item' => $orderItem->load('menuItem'),
                'order' => $order->fresh(['items', 'payments']),
                'message' => 'Item added successfully',
            ]);
        } catch (\Exception $e) {
            Log::error('Failed to add item to order', [
                'order_id' => $order->id,
                'request_data' => $request->all(),
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to add item: ' . $e->getMessage(),
                'debug' => config('app.debug') ? [
                    'error' => $e->getMessage(),
                    'file' => $e->getFile(),
                    'line' => $e->getLine(),
                ] : null,
            ], 500);
        }
    }

    /**
     * Update item quantity
     */
    public function updateItemQuantity(Request $request, OrderItem $orderItem): JsonResponse
    {
        $request->validate([
            'quantity' => 'required|integer|min:1|max:99',
        ]);

        try {
            $this->posService->updateItemQuantity($orderItem, $request->quantity, auth('tenant')->user());

            return response()->json([
                'success' => true,
                'order_item' => $orderItem->fresh(),
                'order' => $orderItem->order->fresh(['items', 'payments']),
                'message' => 'Quantity updated successfully',
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to update quantity: ' . $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Remove item from order
     */
    public function removeItem(OrderItem $orderItem): JsonResponse
    {
        try {
            $order = $orderItem->order;
            $this->posService->removeItemFromOrder($orderItem, auth('tenant')->user());

            return response()->json([
                'success' => true,
                'order' => $order->fresh(['items', 'payments']),
                'message' => 'Item removed successfully',
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to remove item: ' . $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Apply discount to order
     */
    public function applyDiscount(Request $request, Order $order): JsonResponse
    {
        $request->validate([
            'discount_type' => 'required|in:percentage,fixed_amount',
            'discount_name' => 'required|string|max:255',
            'discount_value' => 'required|numeric|min:0',
            'reason' => 'nullable|string|max:500',
        ]);

        try {
            $discount = $this->posService->applyOrderDiscount($order, $request->all(), auth('tenant')->user());

            return response()->json([
                'success' => true,
                'discount' => $discount,
                'order' => $order->fresh(['items', 'payments', 'discounts']),
                'message' => 'Discount applied successfully',
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to apply discount: ' . $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Process payment
     */
    public function processPayment(Request $request, Order $order): JsonResponse
    {
        $request->validate([
            'payment_method' => 'required|in:cash,card,digital_wallet,bank_transfer',
            'amount' => 'required|numeric|min:0.01',
            'transaction_reference' => 'nullable|string|max:255',
            'notes' => 'nullable|string|max:500',
        ]);

        try {
            $payment = $this->posService->processPayment($order, $request->all(), auth('tenant')->user());

            return response()->json([
                'success' => true,
                'payment' => $payment,
                'order' => $order->fresh(['items', 'payments']),
                'message' => 'Payment processed successfully',
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to process payment: ' . $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Send order to kitchen
     */
    public function sendToKitchen(Order $order): JsonResponse
    {
        try {
            $this->posService->sendToKitchen($order, auth('tenant')->user());

            return response()->json([
                'success' => true,
                'order' => $order->fresh(['items']),
                'message' => 'Order sent to kitchen successfully',
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to send order to kitchen: ' . $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Close order
     */
    public function closeOrder(Order $order): JsonResponse
    {
        try {
            $this->posService->closeOrder($order, auth('tenant')->user());

            return response()->json([
                'success' => true,
                'message' => 'Order closed successfully',
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to close order: ' . $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Get order summary for receipt
     */
    public function getOrderSummary(Order $order): JsonResponse
    {
        try {
            $summary = $this->posService->getOrderSummary($order);

            return response()->json([
                'success' => true,
                'summary' => $summary,
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to get order summary: ' . $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Show order details page
     */
    public function showOrder(Order $order): Response
    {
        $order->load([
            'table',
            'branch',
            'customer',
            'items.menuItem',
            'payments',
            'discounts',
            'waiter',
            'openedBy'
        ]);

        return Inertia::render('Tenant/POS/OrderShow', [
            'order' => $order,
            'paymentMethods' => $this->getPaymentMethods(),
        ]);
    }

    /**
     * Update order status
     */
    public function updateOrderStatus(Request $request, Order $order): JsonResponse
    {
        $request->validate([
            'status' => 'required|in:pending,confirmed,preparing,ready,served,cancelled',
            'notes' => 'nullable|string|max:500',
        ]);

        try {
            $user = auth('tenant')->user();

            // Update order status
            $order->update([
                'status' => $request->status,
                'updated_by' => $user->id,
            ]);

            // Note: Status history tracking disabled for simplified schema
            // Could log status changes to application logs if needed

            // Handle specific status changes
            switch ($request->status) {
                case 'served':
                    $order->update(['order_closed_at' => now(), 'closed_by' => $user->id]);
                    // Free up table if dine-in
                    if ($order->isDineIn() && $order->table_id) {
                        Table::find($order->table_id)->update(['status' => 'available']);
                    }
                    break;
                case 'cancelled':
                    $order->update(['order_closed_at' => now(), 'closed_by' => $user->id]);
                    // Free up table if dine-in
                    if ($order->isDineIn() && $order->table_id) {
                        Table::find($order->table_id)->update(['status' => 'available']);
                    }
                    break;
            }

            return response()->json([
                'success' => true,
                'message' => 'Order status updated successfully',
                'order' => $order->fresh(['table', 'items.menuItem', 'payments']),
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to update order status: ' . $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Delete order
     */
    public function deleteOrder(Order $order): JsonResponse
    {
        try {
            $user = auth('tenant')->user();

            // Only allow deletion of pending orders
            if (!in_array($order->status, ['pending', 'draft'])) {
                return response()->json([
                    'success' => false,
                    'message' => 'Only pending orders can be deleted.',
                ], 422);
            }

            // Free up table if dine-in
            if ($order->isDineIn() && $order->table_id) {
                Table::find($order->table_id)->update(['status' => 'available']);
            }

            // Delete order items first
            $order->items()->delete();

            // Delete order payments
            $order->payments()->delete();

            // Delete order discounts
            $order->discounts()->delete();

            // Delete the order
            $order->delete();

            return response()->json([
                'success' => true,
                'message' => 'Order deleted successfully',
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to delete order: ' . $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Edit order page
     */
    public function editOrder(Order $order)
    {
        // Redirect to POS with order loaded for editing
        return redirect()->route('pos.index', ['order_id' => $order->id]);
    }

    /**
     * Print order receipt
     */
    public function printOrder(Order $order): Response
    {
        $order->load([
            'table',
            'branch',
            'customer',
            'items.menuItem',
            'payments',
            'discounts',
            'waiter',
            'openedBy'
        ]);

        return Inertia::render('Tenant/POS/OrderPrint', [
            'order' => $order,
        ]);
    }

    /**
     * Process payment for order
     */
    public function processOrderPayment(Request $request, Order $order): JsonResponse
    {
        $request->validate([
            'payment_method' => 'required|string',
            'amount' => 'required|numeric|min:0.01',
            'transaction_reference' => 'nullable|string|max:255',
            'notes' => 'nullable|string|max:500',
        ]);

        try {
            $user = auth('tenant')->user();

            $payment = $this->posService->processPayment($order, $request->all(), $user);

            return response()->json([
                'success' => true,
                'message' => 'Payment processed successfully',
                'payment' => $payment,
                'order' => $order->fresh(['payments']),
            ]);
        } catch (\Exception $e) {
            Log::error('Failed to process payment', [
                'order_id' => $order->id,
                'payment_data' => $request->all(),
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to process payment: ' . $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Get available payment methods
     */
    protected function getPaymentMethods(): array
    {
        return [
            ['value' => 'cash', 'label' => 'Cash'],
            ['value' => 'card', 'label' => 'Credit/Debit Card'],
            ['value' => 'mobile_payment', 'label' => 'Mobile Payment'],
            ['value' => 'bank_transfer', 'label' => 'Bank Transfer'],
            ['value' => 'check', 'label' => 'Check'],
        ];
    }

    /**
     * Get available discount types
     */
    protected function getDiscountTypes(): array
    {
        return [
            ['value' => 'percentage', 'label' => 'Percentage'],
            ['value' => 'fixed_amount', 'label' => 'Fixed Amount'],
        ];
    }

    /**
     * Get sample customers for demo purposes
     */
    protected function getSampleCustomers(): array
    {
        return [
            [
                'id' => 1,
                'name' => 'John Smith',
                'phone' => '+**********',
                'email' => '<EMAIL>',
                'tier' => 'vip',
                'total_orders' => 25,
                'loyalty_points' => 1250
            ],
            [
                'id' => 2,
                'name' => 'Sarah Johnson',
                'phone' => '+**********',
                'email' => '<EMAIL>',
                'tier' => 'gold',
                'total_orders' => 15,
                'loyalty_points' => 750
            ],
            [
                'id' => 3,
                'name' => 'Mike Davis',
                'phone' => '+1234567892',
                'email' => '<EMAIL>',
                'tier' => 'silver',
                'total_orders' => 8,
                'loyalty_points' => 400
            ],
            [
                'id' => 4,
                'name' => 'Emily Wilson',
                'phone' => '+1234567893',
                'email' => '<EMAIL>',
                'tier' => 'regular',
                'total_orders' => 3,
                'loyalty_points' => 150
            ],
            [
                'id' => 5,
                'name' => 'David Brown',
                'phone' => '+1234567894',
                'email' => '<EMAIL>',
                'tier' => 'regular',
                'total_orders' => 1,
                'loyalty_points' => 50
            ]
        ];
    }

    /**
     * Get waiters from database with fallback to sample data
     */
    protected function getWaiters(): array
    {
        try {
            // Try to get real users from database (tenant-specific)
            $users = User::select('id', 'name', 'email', 'role')
                ->where('is_active', true)
                ->whereIn('role', ['waiter', 'manager', 'staff'])
                ->orderBy('name')
                ->get()
                ->map(function ($user) {
                    return [
                        'id' => $user->id,
                        'name' => $user->name,
                        'email' => $user->email,
                        'role' => $user->role,
                        'department' => 'Service',
                        'status' => 'active'
                    ];
                })
                ->toArray();

            // If we have users, return them
            if (!empty($users)) {
                return $users;
            }

            // Fallback: Create sample users if none exist
            $this->createSampleUsers();

            // Try again after creating sample users
            return User::select('id', 'name', 'email', 'role')
                ->where('is_active', true)
                ->whereIn('role', ['waiter', 'manager', 'staff'])
                ->orderBy('name')
                ->get()
                ->map(function ($user) {
                    return [
                        'id' => $user->id,
                        'name' => $user->name,
                        'email' => $user->email,
                        'role' => $user->role,
                        'department' => 'Service',
                        'status' => 'active'
                    ];
                })
                ->toArray();

        } catch (\Exception $e) {
            Log::error('Failed to get waiters: ' . $e->getMessage());

            // Ultimate fallback: return empty array (no waiters)
            return [];
        }
    }

    /**
     * Create sample users for POS system
     */
    protected function createSampleUsers(): void
    {
        $sampleUsers = [
            [
                'name' => 'Alice Johnson',
                'email' => '<EMAIL>',
                'password' => Hash::make('password'),
                'role' => 'waiter',
                'department' => 'Service',
                'position' => 'Senior Waiter',
                'is_active' => true,
                'email_verified_at' => now(),
            ],
            [
                'name' => 'Bob Wilson',
                'email' => '<EMAIL>',
                'password' => Hash::make('password'),
                'role' => 'waiter',
                'department' => 'Service',
                'position' => 'Waiter',
                'is_active' => true,
                'email_verified_at' => now(),
            ],
            [
                'name' => 'Carol Davis',
                'email' => '<EMAIL>',
                'password' => Hash::make('password'),
                'role' => 'manager',
                'department' => 'Management',
                'position' => 'Floor Manager',
                'is_active' => true,
                'email_verified_at' => now(),
            ],
        ];

        foreach ($sampleUsers as $userData) {
            try {
                // Check if user already exists by email
                $existingUser = User::where('email', $userData['email'])->first();

                if (!$existingUser) {
                    User::create($userData);
                }
            } catch (\Exception $e) {
                Log::error('Failed to create sample user: ' . $e->getMessage());
            }
        }
    }

    /**
     * Ensure at least one branch exists for debugging
     */
    private function ensureBranchExists(): ?Branch
    {
        // First try to get an existing branch
        $branch = Branch::active()->first();

        if ($branch) {
            return $branch;
        }

        // If no branches exist, create a default one for debugging
        try {
            $branch = Branch::create([
                'name' => 'Main Branch',
                'address' => '123 Main Street',
                'phone' => '+**********',
                'email' => '<EMAIL>',
                'is_main_branch' => true,
                'is_active' => true,
                'sort_order' => 1,
                'operating_hours' => [
                    'monday' => ['open' => '09:00', 'close' => '22:00'],
                    'tuesday' => ['open' => '09:00', 'close' => '22:00'],
                    'wednesday' => ['open' => '09:00', 'close' => '22:00'],
                    'thursday' => ['open' => '09:00', 'close' => '22:00'],
                    'friday' => ['open' => '09:00', 'close' => '23:00'],
                    'saturday' => ['open' => '09:00', 'close' => '23:00'],
                    'sunday' => ['open' => '10:00', 'close' => '21:00'],
                ],
            ]);

            // Create a default floor and some tables
            $floor = Floor::create([
                'branch_id' => $branch->id,
                'name' => 'Ground Floor',
                'description' => 'Main dining area',
                'sort_order' => 1,
                'is_active' => true,
            ]);

            // Create some tables
            for ($i = 1; $i <= 5; $i++) {
                Table::create([
                    'floor_id' => $floor->id,
                    'name' => "Table {$i}",
                    'number' => $i,
                    'capacity' => rand(2, 6),
                    'status' => 'available',
                    'position_x' => rand(10, 90),
                    'position_y' => rand(10, 90),
                    'is_active' => true,
                ]);
            }

            return $branch;
        } catch (\Exception $e) {
            Log::error('Failed to create default branch: ' . $e->getMessage());
            return null;
        }
    }
}
