<?php

namespace App\Http\Controllers\Tenant;

use App\Http\Controllers\Controller;
use App\Models\Tenant\PurchaseOrder;
use App\Models\Tenant\PurchaseOrderItem;
use App\Models\Tenant\InventoryItem;
use App\Models\Tenant\Vendor;
use App\Models\Tenant\Restaurant;
use Illuminate\Http\Request;
use Inertia\Inertia;

class PurchaseOrderController extends Controller
{
    /**
     * Display a listing of purchase orders.
     */
    public function index(Request $request)
    {
        $query = PurchaseOrder::with(['vendor', 'creator', 'approver', 'items']);

        // Search
        if ($request->search) {
            $query->where(function ($q) use ($request) {
                $q->where('po_number', 'like', '%' . $request->search . '%')
                  ->orWhereHas('vendor', function ($vendorQuery) use ($request) {
                      $vendorQuery->where('name', 'like', '%' . $request->search . '%');
                  });
            });
        }

        // Filter by status
        if ($request->status) {
            $query->where('status', $request->status);
        }

        // Filter by vendor
        if ($request->vendor_id) {
            $query->where('vendor_id', $request->vendor_id);
        }

        // Filter by date range
        if ($request->date_from) {
            $query->whereDate('order_date', '>=', $request->date_from);
        }
        if ($request->date_to) {
            $query->whereDate('order_date', '<=', $request->date_to);
        }

        // Filter overdue orders
        if ($request->overdue) {
            $query->overdue();
        }

        $purchaseOrders = $query->latest('order_date')->paginate(20);

        // Get filter options
        $vendors = Vendor::active()->orderBy('name')->get();

        // Calculate stats
        $stats = [
            'total_orders' => PurchaseOrder::count(),
            'pending_orders' => PurchaseOrder::where('status', 'pending')->count(),
            'delivered_orders' => PurchaseOrder::where('status', 'received')->count(),
            'total_value' => PurchaseOrder::sum('total_amount'),
        ];

        return Inertia::render('Tenant/PurchaseOrders/Index', [
            'purchaseOrders' => $purchaseOrders,
            'vendors' => $vendors,
            'stats' => $stats,
            'filters' => $request->only(['search', 'status', 'vendor_id', 'date_from', 'date_to', 'overdue']),
            'statusOptions' => $this->getStatusOptions(),
        ]);
    }

    /**
     * Show the form for creating a new purchase order.
     */
    public function create()
    {
        $restaurant = Restaurant::first();
        $vendors = Vendor::active()->orderBy('name')->get();
        $inventoryItems = InventoryItem::with(['category', 'vendor'])
            ->active()
            ->orderBy('name')
            ->get();

        return Inertia::render('Tenant/PurchaseOrders/Create', [
            'restaurant' => $restaurant,
            'vendors' => $vendors,
            'inventoryItems' => $inventoryItems,
        ]);
    }

    /**
     * Store a newly created purchase order.
     */
    public function store(Request $request)
    {
        $request->validate([
            'vendor_id' => 'required|exists:vendors,id',
            'order_date' => 'required|date',
            'expected_delivery_date' => 'nullable|date|after_or_equal:order_date',
            'payment_terms' => 'nullable|string|max:255',
            'delivery_address' => 'nullable|string',
            'notes' => 'nullable|string',
            'items' => 'required|array|min:1',
            'items.*.inventory_item_id' => 'required|exists:inventory_items,id',
            'items.*.quantity' => 'required|numeric|min:0.01',
            'items.*.unit_cost' => 'required|numeric|min:0',
            'items.*.batch_number' => 'nullable|string|max:255',
            'items.*.expiry_date' => 'nullable|date|after:today',
            'items.*.notes' => 'nullable|string',
            'tax_amount' => 'nullable|numeric|min:0',
            'shipping_cost' => 'nullable|numeric|min:0',
            'discount_amount' => 'nullable|numeric|min:0',
        ]);

        $restaurant = Restaurant::first();

        $purchaseOrder = PurchaseOrder::create([
            'restaurant_id' => $restaurant->id,
            'vendor_id' => $request->vendor_id,
            'created_by' => auth()->id(),
            'order_date' => $request->order_date,
            'expected_delivery_date' => $request->expected_delivery_date,
            'payment_terms' => $request->payment_terms,
            'delivery_address' => $request->delivery_address,
            'notes' => $request->notes,
            'tax_amount' => $request->tax_amount ?? 0,
            'shipping_cost' => $request->shipping_cost ?? 0,
            'discount_amount' => $request->discount_amount ?? 0,
        ]);

        // Add items
        foreach ($request->items as $itemData) {
            $purchaseOrder->items()->create([
                'inventory_item_id' => $itemData['inventory_item_id'],
                'quantity' => $itemData['quantity'],
                'unit_cost' => $itemData['unit_cost'],
                'total_cost' => $itemData['quantity'] * $itemData['unit_cost'],
                'batch_number' => $itemData['batch_number'] ?? null,
                'expiry_date' => $itemData['expiry_date'] ?? null,
                'notes' => $itemData['notes'] ?? null,
            ]);
        }

        // Calculate totals
        $purchaseOrder->calculateTotals();

        return redirect()->route('purchase-orders.index')
            ->with('success', 'Purchase order created successfully.');
    }

    /**
     * Display the specified purchase order.
     */
    public function show(PurchaseOrder $purchaseOrder)
    {
        $purchaseOrder->load([
            'vendor', 'creator', 'approver', 'media',
            'items' => function ($query) {
                $query->with(['inventoryItem' => function ($itemQuery) {
                    $itemQuery->with('category');
                }]);
            }
        ]);

        return Inertia::render('Tenant/PurchaseOrders/Show', [
            'purchaseOrder' => $purchaseOrder,
        ]);
    }

    /**
     * Show the form for editing the specified purchase order.
     */
    public function edit(PurchaseOrder $purchaseOrder)
    {
        // Only allow editing of draft and pending orders
        if (!in_array($purchaseOrder->status, ['draft', 'pending'])) {
            return back()->withErrors(['error' => 'Only draft and pending purchase orders can be edited.']);
        }

        $purchaseOrder->load(['items.inventoryItem']);
        $restaurant = Restaurant::first();
        $vendors = Vendor::active()->orderBy('name')->get();
        $inventoryItems = InventoryItem::with(['category', 'vendor'])
            ->active()
            ->orderBy('name')
            ->get();

        return Inertia::render('Tenant/PurchaseOrders/Edit', [
            'purchaseOrder' => $purchaseOrder,
            'restaurant' => $restaurant,
            'vendors' => $vendors,
            'inventoryItems' => $inventoryItems,
        ]);
    }

    /**
     * Update the specified purchase order.
     */
    public function update(Request $request, PurchaseOrder $purchaseOrder)
    {
        // Only allow updating of draft and pending orders
        if (!in_array($purchaseOrder->status, ['draft', 'pending'])) {
            return back()->withErrors(['error' => 'Only draft and pending purchase orders can be updated.']);
        }

        $request->validate([
            'vendor_id' => 'required|exists:vendors,id',
            'order_date' => 'required|date',
            'expected_delivery_date' => 'nullable|date|after_or_equal:order_date',
            'payment_terms' => 'nullable|string|max:255',
            'delivery_address' => 'nullable|string',
            'notes' => 'nullable|string',
            'items' => 'required|array|min:1',
            'items.*.inventory_item_id' => 'required|exists:inventory_items,id',
            'items.*.quantity' => 'required|numeric|min:0.01',
            'items.*.unit_cost' => 'required|numeric|min:0',
            'items.*.batch_number' => 'nullable|string|max:255',
            'items.*.expiry_date' => 'nullable|date|after:today',
            'items.*.notes' => 'nullable|string',
            'tax_amount' => 'nullable|numeric|min:0',
            'shipping_cost' => 'nullable|numeric|min:0',
            'discount_amount' => 'nullable|numeric|min:0',
        ]);

        $purchaseOrder->update([
            'vendor_id' => $request->vendor_id,
            'order_date' => $request->order_date,
            'expected_delivery_date' => $request->expected_delivery_date,
            'payment_terms' => $request->payment_terms,
            'delivery_address' => $request->delivery_address,
            'notes' => $request->notes,
            'tax_amount' => $request->tax_amount ?? 0,
            'shipping_cost' => $request->shipping_cost ?? 0,
            'discount_amount' => $request->discount_amount ?? 0,
        ]);

        // Update items
        $purchaseOrder->items()->delete();
        foreach ($request->items as $itemData) {
            $purchaseOrder->items()->create([
                'inventory_item_id' => $itemData['inventory_item_id'],
                'quantity' => $itemData['quantity'],
                'unit_cost' => $itemData['unit_cost'],
                'total_cost' => $itemData['quantity'] * $itemData['unit_cost'],
                'batch_number' => $itemData['batch_number'] ?? null,
                'expiry_date' => $itemData['expiry_date'] ?? null,
                'notes' => $itemData['notes'] ?? null,
            ]);
        }

        // Calculate totals
        $purchaseOrder->calculateTotals();

        return redirect()->route('purchase-orders.index')
            ->with('success', 'Purchase order updated successfully.');
    }

    /**
     * Remove the specified purchase order.
     */
    public function destroy(PurchaseOrder $purchaseOrder)
    {
        // Only allow deletion of draft orders
        if ($purchaseOrder->status !== 'draft') {
            return back()->withErrors(['error' => 'Only draft purchase orders can be deleted.']);
        }

        $purchaseOrder->delete();

        return redirect()->route('purchase-orders.index')
            ->with('success', 'Purchase order deleted successfully.');
    }

    /**
     * Approve purchase order.
     */
    public function approve(PurchaseOrder $purchaseOrder)
    {
        if ($purchaseOrder->status !== 'pending') {
            return back()->withErrors(['error' => 'Only pending purchase orders can be approved.']);
        }

        $purchaseOrder->approve(auth()->user());

        return response()->json([
            'success' => true,
            'message' => 'Purchase order approved successfully.',
        ]);
    }

    /**
     * Send purchase order to vendor.
     */
    public function sendToVendor(PurchaseOrder $purchaseOrder)
    {
        if ($purchaseOrder->status !== 'approved') {
            return back()->withErrors(['error' => 'Only approved purchase orders can be sent to vendor.']);
        }

        $purchaseOrder->sendToVendor();

        return response()->json([
            'success' => true,
            'message' => 'Purchase order sent to vendor successfully.',
        ]);
    }

    /**
     * Mark purchase order as received.
     */
    public function markReceived(PurchaseOrder $purchaseOrder)
    {
        if (!in_array($purchaseOrder->status, ['sent', 'partially_received'])) {
            return back()->withErrors(['error' => 'Purchase order cannot be marked as received.']);
        }

        $purchaseOrder->markAsReceived();

        return response()->json([
            'success' => true,
            'message' => 'Purchase order marked as received successfully.',
        ]);
    }

    /**
     * Partially receive purchase order items.
     */
    public function partialReceive(Request $request, PurchaseOrder $purchaseOrder)
    {
        $request->validate([
            'items' => 'required|array',
            'items.*.id' => 'required|exists:purchase_order_items,id',
            'items.*.received_quantity' => 'required|numeric|min:0',
            'items.*.batch_number' => 'nullable|string|max:255',
            'items.*.expiry_date' => 'nullable|date|after:today',
        ]);

        $purchaseOrder->partiallyReceive($request->items);

        return response()->json([
            'success' => true,
            'message' => 'Items received successfully.',
        ]);
    }

    /**
     * Cancel purchase order.
     */
    public function cancel(Request $request, PurchaseOrder $purchaseOrder)
    {
        $request->validate([
            'reason' => 'nullable|string|max:500',
        ]);

        if (in_array($purchaseOrder->status, ['received', 'completed', 'cancelled'])) {
            return back()->withErrors(['error' => 'Purchase order cannot be cancelled.']);
        }

        $purchaseOrder->cancel($request->reason);

        return response()->json([
            'success' => true,
            'message' => 'Purchase order cancelled successfully.',
        ]);
    }

    /**
     * Generate automatic purchase orders for low stock items.
     */
    public function generateAutoOrders()
    {
        $createdOrders = PurchaseOrder::generateAutomaticOrders();

        return response()->json([
            'success' => true,
            'message' => count($createdOrders) . ' purchase orders generated successfully.',
            'orders' => $createdOrders,
        ]);
    }

    /**
     * Duplicate purchase order.
     */
    public function duplicate(PurchaseOrder $purchaseOrder)
    {
        $newPO = $purchaseOrder->replicate();
        $newPO->po_number = PurchaseOrder::generatePoNumber();
        $newPO->status = 'draft';
        $newPO->approved_by = null;
        $newPO->order_date = today();
        $newPO->expected_delivery_date = today()->addDays(3);
        $newPO->actual_delivery_date = null;
        $newPO->save();

        // Duplicate items
        foreach ($purchaseOrder->items as $item) {
            $newPO->items()->create([
                'inventory_item_id' => $item->inventory_item_id,
                'quantity' => $item->quantity,
                'unit_cost' => $item->unit_cost,
                'total_cost' => $item->total_cost,
                'notes' => $item->notes,
            ]);
        }

        $newPO->calculateTotals();

        return redirect()->route('purchase-orders.edit', $newPO)
            ->with('success', 'Purchase order duplicated successfully.');
    }

    /**
     * Get status options.
     */
    protected function getStatusOptions(): array
    {
        return [
            ['value' => 'draft', 'label' => 'Draft'],
            ['value' => 'pending', 'label' => 'Pending Approval'],
            ['value' => 'approved', 'label' => 'Approved'],
            ['value' => 'sent', 'label' => 'Sent to Vendor'],
            ['value' => 'partially_received', 'label' => 'Partially Received'],
            ['value' => 'received', 'label' => 'Received'],
            ['value' => 'completed', 'label' => 'Completed'],
            ['value' => 'cancelled', 'label' => 'Cancelled'],
        ];
    }
}
