<?php

namespace App\Http\Controllers\Tenant;

use App\Http\Controllers\Controller;
use App\Models\Tenant\WasteRecord;
use App\Models\Tenant\InventoryItem;
use App\Models\Tenant\InventoryCategory;
use App\Models\Tenant\Restaurant;
use Illuminate\Http\Request;
use Inertia\Inertia;
use Carbon\Carbon;

class WasteController extends Controller
{
    /**
     * Display waste dashboard.
     */
    public function dashboard()
    {
        $todayWaste = WasteRecord::today()->sum('total_cost');
        $weekWaste = WasteRecord::thisWeek()->sum('total_cost');
        $monthWaste = WasteRecord::thisMonth()->sum('total_cost');

        // Waste by reason
        $wasteByReason = WasteRecord::thisMonth()
            ->selectRaw('reason, SUM(total_cost) as total_cost, COUNT(*) as count')
            ->groupBy('reason')
            ->get()
            ->map(function ($item) {
                return [
                    'reason' => $item->reason,
                    'reason_label' => $item->reason_label,
                    'total_cost' => $item->total_cost,
                    'count' => $item->count,
                ];
            });

        // Waste by category
        $wasteByCategory = WasteRecord::with(['inventoryItem.category'])
            ->thisMonth()
            ->get()
            ->groupBy('inventoryItem.category.name')
            ->map(function ($group, $categoryName) {
                return [
                    'category' => $categoryName,
                    'total_cost' => $group->sum('total_cost'),
                    'count' => $group->count(),
                ];
            })
            ->values();

        // Waste trend
        $wasteTrend = WasteRecord::getWasteTrend(30);

        // Top wasted items
        $topWastedItems = WasteRecord::with(['inventoryItem'])
            ->thisMonth()
            ->selectRaw('inventory_item_id, SUM(total_cost) as total_cost, SUM(quantity) as total_quantity')
            ->groupBy('inventory_item_id')
            ->orderByDesc('total_cost')
            ->take(10)
            ->get();

        return Inertia::render('Tenant/Waste/Dashboard', [
            'stats' => [
                'today_waste' => $todayWaste,
                'week_waste' => $weekWaste,
                'month_waste' => $monthWaste,
            ],
            'wasteByReason' => $wasteByReason,
            'wasteByCategory' => $wasteByCategory,
            'wasteTrend' => $wasteTrend,
            'topWastedItems' => $topWastedItems,
        ]);
    }

    /**
     * Display a listing of waste records.
     */
    public function index(Request $request)
    {
        $query = WasteRecord::with(['inventoryItem.category', 'recordedBy', 'media']);

        // Search
        if ($request->search) {
            $query->where(function ($q) use ($request) {
                $q->whereHas('inventoryItem', function ($itemQuery) use ($request) {
                    $itemQuery->where('name', 'like', '%' . $request->search . '%')
                             ->orWhere('sku', 'like', '%' . $request->search . '%');
                })
                ->orWhere('batch_number', 'like', '%' . $request->search . '%')
                ->orWhere('notes', 'like', '%' . $request->search . '%');
            });
        }

        // Filter by reason
        if ($request->reason) {
            $query->where('reason', $request->reason);
        }

        // Filter by category
        if ($request->category_id) {
            $query->whereHas('inventoryItem', function ($itemQuery) use ($request) {
                $itemQuery->where('inventory_category_id', $request->category_id);
            });
        }

        // Filter by date range
        if ($request->date_from) {
            $query->whereDate('waste_date', '>=', $request->date_from);
        }
        if ($request->date_to) {
            $query->whereDate('waste_date', '<=', $request->date_to);
        }

        $wasteRecords = $query->latest('waste_date')->paginate(20);

        // Get filter options
        $categories = InventoryCategory::active()->ordered()->get();

        return Inertia::render('Tenant/Waste/Index', [
            'wasteRecords' => $wasteRecords,
            'categories' => $categories,
            'filters' => $request->only(['search', 'reason', 'category_id', 'date_from', 'date_to']),
            'reasonOptions' => $this->getReasonOptions(),
        ]);
    }

    /**
     * Show the form for creating a new waste record.
     */
    public function create()
    {
        $restaurant = Restaurant::first();
        $inventoryItems = InventoryItem::with(['category', 'stockBatches' => function ($query) {
            $query->where('quantity', '>', 0)->orderBy('expiry_date');
        }])
        ->active()
        ->orderBy('name')
        ->get();

        return Inertia::render('Tenant/Waste/Create', [
            'restaurant' => $restaurant,
            'inventoryItems' => $inventoryItems,
            'reasonOptions' => $this->getReasonOptions(),
        ]);
    }

    /**
     * Store a newly created waste record.
     */
    public function store(Request $request)
    {
        $request->validate([
            'inventory_item_id' => 'required|exists:inventory_items,id',
            'quantity' => 'required|numeric|min:0.01',
            'reason' => 'required|in:expired,damaged,spoiled,contaminated,overproduction,preparation_error,customer_return,quality_control,theft,other',
            'batch_number' => 'nullable|string|max:255',
            'waste_date' => 'required|date|before_or_equal:today',
            'notes' => 'nullable|string',
            'media_ids' => 'nullable|array',
            'media_ids.*' => 'exists:media,id',
        ]);

        $restaurant = Restaurant::first();
        $inventoryItem = InventoryItem::find($request->inventory_item_id);

        // Check if sufficient stock is available
        if ($inventoryItem->current_stock < $request->quantity) {
            return back()->withErrors(['quantity' => 'Insufficient stock available for waste recording.']);
        }

        $wasteRecord = WasteRecord::create([
            'restaurant_id' => $restaurant->id,
            'inventory_item_id' => $request->inventory_item_id,
            'batch_number' => $request->batch_number,
            'quantity' => $request->quantity,
            'unit_cost' => $inventoryItem->unit_cost,
            'total_cost' => $request->quantity * $inventoryItem->unit_cost,
            'reason' => $request->reason,
            'waste_date' => $request->waste_date,
            'recorded_by' => auth()->id(),
            'notes' => $request->notes,
        ]);

        // Attach media
        if ($request->media_ids) {
            foreach ($request->media_ids as $mediaId) {
                $wasteRecord->addMediaFromLibrary($mediaId, 'photos');
            }
        }

        // Remove stock from inventory
        $inventoryItem->removeStock($request->quantity, 'waste', $wasteRecord, $request->batch_number);

        return redirect()->route('waste.index')
            ->with('success', 'Waste record created successfully.');
    }

    /**
     * Display the specified waste record.
     */
    public function show(WasteRecord $waste)
    {
        $waste->load(['inventoryItem.category', 'recordedBy', 'media']);

        return Inertia::render('Tenant/Waste/Show', [
            'wasteRecord' => $waste,
        ]);
    }

    /**
     * Show the form for editing the specified waste record.
     */
    public function edit(WasteRecord $waste)
    {
        // Only allow editing of today's waste records
        if (!$waste->waste_date->isToday()) {
            return back()->withErrors(['error' => 'Only today\'s waste records can be edited.']);
        }

        $waste->load(['media']);
        $restaurant = Restaurant::first();
        $inventoryItems = InventoryItem::with(['category'])
            ->active()
            ->orderBy('name')
            ->get();

        return Inertia::render('Tenant/Waste/Edit', [
            'wasteRecord' => $waste,
            'restaurant' => $restaurant,
            'inventoryItems' => $inventoryItems,
            'reasonOptions' => $this->getReasonOptions(),
        ]);
    }

    /**
     * Update the specified waste record.
     */
    public function update(Request $request, WasteRecord $waste)
    {
        // Only allow updating of today's waste records
        if (!$waste->waste_date->isToday()) {
            return back()->withErrors(['error' => 'Only today\'s waste records can be updated.']);
        }

        $request->validate([
            'quantity' => 'required|numeric|min:0.01',
            'reason' => 'required|in:expired,damaged,spoiled,contaminated,overproduction,preparation_error,customer_return,quality_control,theft,other',
            'batch_number' => 'nullable|string|max:255',
            'notes' => 'nullable|string',
            'media_ids' => 'nullable|array',
            'media_ids.*' => 'exists:media,id',
        ]);

        $oldQuantity = $waste->quantity;
        $newQuantity = $request->quantity;
        $quantityDifference = $newQuantity - $oldQuantity;

        // Adjust inventory stock if quantity changed
        if ($quantityDifference != 0) {
            if ($quantityDifference > 0) {
                // Increased waste - remove more stock
                if ($waste->inventoryItem->current_stock < $quantityDifference) {
                    return back()->withErrors(['quantity' => 'Insufficient stock available for the increased waste quantity.']);
                }
                $waste->inventoryItem->removeStock($quantityDifference, 'waste', $waste);
            } else {
                // Decreased waste - add stock back
                $waste->inventoryItem->addStock(abs($quantityDifference), $waste->unit_cost, null, null, 'waste_adjustment', $waste);
            }
        }

        $waste->update([
            'quantity' => $request->quantity,
            'total_cost' => $request->quantity * $waste->unit_cost,
            'reason' => $request->reason,
            'batch_number' => $request->batch_number,
            'notes' => $request->notes,
        ]);

        // Update media
        $waste->clearMediaCollection('photos');
        if ($request->media_ids) {
            foreach ($request->media_ids as $mediaId) {
                $waste->addMediaFromLibrary($mediaId, 'photos');
            }
        }

        return redirect()->route('waste.index')
            ->with('success', 'Waste record updated successfully.');
    }

    /**
     * Remove the specified waste record.
     */
    public function destroy(WasteRecord $waste)
    {
        // Only allow deletion of today's waste records
        if (!$waste->waste_date->isToday()) {
            return back()->withErrors(['error' => 'Only today\'s waste records can be deleted.']);
        }

        // Add stock back to inventory
        $waste->inventoryItem->addStock($waste->quantity, $waste->unit_cost, null, null, 'waste_reversal', $waste);

        $waste->delete();

        return redirect()->route('waste.index')
            ->with('success', 'Waste record deleted successfully.');
    }

    /**
     * Bulk create waste records for expired items.
     */
    public function bulkCreateExpired()
    {
        $expiredBatches = \App\Models\Tenant\StockBatch::with(['inventoryItem'])
            ->where('expiry_date', '<', now())
            ->where('quantity', '>', 0)
            ->get();

        $createdRecords = 0;

        foreach ($expiredBatches as $batch) {
            // Create waste record
            WasteRecord::create([
                'restaurant_id' => $batch->restaurant_id,
                'inventory_item_id' => $batch->inventory_item_id,
                'batch_number' => $batch->batch_number,
                'quantity' => $batch->quantity,
                'unit_cost' => $batch->unit_cost,
                'total_cost' => $batch->quantity * $batch->unit_cost,
                'reason' => 'expired',
                'waste_date' => now(),
                'recorded_by' => auth()->id(),
                'notes' => 'Automatically created for expired batch',
            ]);

            // Remove stock from inventory
            $batch->inventoryItem->removeStock($batch->quantity, 'expired', null, $batch->batch_number);

            // Update batch quantity to 0
            $batch->update(['quantity' => 0]);

            $createdRecords++;
        }

        return response()->json([
            'success' => true,
            'message' => "{$createdRecords} waste records created for expired items.",
        ]);
    }

    /**
     * Get waste analytics.
     */
    public function analytics(Request $request)
    {
        $period = $request->get('period', 'month');
        $wasteStats = WasteRecord::getWasteStats($period);

        return Inertia::render('Tenant/Waste/Analytics', [
            'wasteStats' => $wasteStats,
            'period' => $period,
        ]);
    }

    /**
     * Get reason options.
     */
    protected function getReasonOptions(): array
    {
        return [
            ['value' => 'expired', 'label' => 'Expired'],
            ['value' => 'damaged', 'label' => 'Damaged'],
            ['value' => 'spoiled', 'label' => 'Spoiled'],
            ['value' => 'contaminated', 'label' => 'Contaminated'],
            ['value' => 'overproduction', 'label' => 'Overproduction'],
            ['value' => 'preparation_error', 'label' => 'Preparation Error'],
            ['value' => 'customer_return', 'label' => 'Customer Return'],
            ['value' => 'quality_control', 'label' => 'Quality Control'],
            ['value' => 'theft', 'label' => 'Theft'],
            ['value' => 'other', 'label' => 'Other'],
        ];
    }
}
