<?php

namespace App\Http\Controllers\Tenant;

use App\Http\Controllers\Controller;
use App\Models\TimeEntry;
use App\Models\User;
use App\Models\Shift;
use Illuminate\Http\Request;
use Inertia\Inertia;
use Carbon\Carbon;

class TimeEntryController extends Controller
{
    public function __construct()
    {
        $this->middleware(['role:admin|restaurant_manager'])->except(['clockIn', 'clockOut', 'myTimeEntries']);
        $this->middleware(['auth'])->only(['clockIn', 'clockOut', 'myTimeEntries']);
    }

    /**
     * Display a listing of time entries
     */
    public function index(Request $request)
    {
        $query = TimeEntry::with(['user', 'shift']);

        // Date range filter
        if ($request->filled('date_from')) {
            $query->whereDate('clock_in_time', '>=', $request->date_from);
        }
        if ($request->filled('date_to')) {
            $query->whereDate('clock_in_time', '<=', $request->date_to);
        } else {
            // Default to current week
            $query->whereBetween('clock_in_time', [
                now()->startOfWeek(),
                now()->endOfWeek()
            ]);
        }

        // Employee filter
        if ($request->filled('employee_id')) {
            $query->where('user_id', $request->employee_id);
        }

        // Status filter
        if ($request->filled('status')) {
            if ($request->status === 'active') {
                $query->whereNull('clock_out_time');
            } else {
                $query->whereNotNull('clock_out_time');
            }
        }

        $timeEntries = $query->orderBy('clock_in_time', 'desc')
                            ->paginate(20);

        $employees = User::where('is_active', true)
                        ->orderBy('name')
                        ->get(['id', 'name', 'department']);

        return Inertia::render('Tenant/TimeEntries/Index', [
            'timeEntries' => $timeEntries,
            'employees' => $employees,
            'filters' => $request->only(['date_from', 'date_to', 'employee_id', 'status']),
            'stats' => [
                'employees_clocked_in' => TimeEntry::whereNull('clock_out_time')->count(),
                'total_hours_today' => $this->getTotalHoursToday(),
                'late_clock_ins' => $this->getLateClockInsToday(),
                'early_departures' => $this->getEarlyDeparturesToday(),
            ]
        ]);
    }

    /**
     * Display time entries for the authenticated user
     */
    public function myTimeEntries(Request $request)
    {
        $query = TimeEntry::where('user_id', auth()->id())
                          ->with('shift');

        // Date range filter
        if ($request->filled('date_from')) {
            $query->whereDate('clock_in_time', '>=', $request->date_from);
        }
        if ($request->filled('date_to')) {
            $query->whereDate('clock_in_time', '<=', $request->date_to);
        } else {
            // Default to current month
            $query->whereBetween('clock_in_time', [
                now()->startOfMonth(),
                now()->endOfMonth()
            ]);
        }

        $timeEntries = $query->orderBy('clock_in_time', 'desc')
                            ->paginate(15);

        $currentEntry = TimeEntry::where('user_id', auth()->id())
                                 ->whereNull('clock_out_time')
                                 ->first();

        $stats = [
            'total_hours_this_month' => TimeEntry::where('user_id', auth()->id())
                                                ->whereMonth('clock_in_time', now()->month)
                                                ->sum('total_hours'),
            'days_worked_this_month' => TimeEntry::where('user_id', auth()->id())
                                                 ->whereMonth('clock_in_time', now()->month)
                                                 ->distinct('date')
                                                 ->count(),
            'average_hours_per_day' => $this->getAverageHoursPerDay(auth()->id()),
            'current_status' => $currentEntry ? 'clocked_in' : 'clocked_out',
        ];

        return Inertia::render('Tenant/TimeEntries/MyEntries', [
            'timeEntries' => $timeEntries,
            'currentEntry' => $currentEntry,
            'stats' => $stats,
            'filters' => $request->only(['date_from', 'date_to']),
        ]);
    }

    /**
     * Clock in an employee
     */
    public function clockIn(Request $request)
    {
        $user = auth()->user();

        // Check if user is already clocked in
        $existingEntry = TimeEntry::where('user_id', $user->id)
                                  ->whereNull('clock_out_time')
                                  ->first();

        if ($existingEntry) {
            return response()->json([
                'success' => false,
                'message' => 'You are already clocked in.',
            ], 400);
        }

        $request->validate([
            'notes' => 'nullable|string|max:500',
            'location' => 'nullable|string|max:255',
        ]);

        // Find today's shift for the user
        $shift = Shift::where('user_id', $user->id)
                     ->whereDate('date', today())
                     ->first();

        $clockInTime = now();
        $timeEntry = TimeEntry::create([
            'user_id' => $user->id,
            'shift_id' => $shift?->id,
            'clock_in_time' => $clockInTime,
            'date' => $clockInTime->format('Y-m-d'),
            'clock_in_notes' => $request->notes,
            'clock_in_location' => $request->location,
            'is_late' => $shift ? $clockInTime->gt(Carbon::parse($shift->start_time)->addMinutes(15)) : false,
        ]);

        // Update user status
        $user->update(['is_on_shift' => true]);

        // Start shift if it exists and is scheduled
        if ($shift && $shift->status === 'scheduled') {
            $shift->update([
                'status' => 'active',
                'actual_start_time' => $clockInTime->format('H:i:s'),
            ]);
        }

        return response()->json([
            'success' => true,
            'message' => 'Clocked in successfully.',
            'time_entry' => $timeEntry,
        ]);
    }

    /**
     * Clock out an employee
     */
    public function clockOut(Request $request)
    {
        $user = auth()->user();

        $timeEntry = TimeEntry::where('user_id', $user->id)
                              ->whereNull('clock_out_time')
                              ->first();

        if (!$timeEntry) {
            return response()->json([
                'success' => false,
                'message' => 'You are not currently clocked in.',
            ], 400);
        }

        $request->validate([
            'notes' => 'nullable|string|max:500',
            'location' => 'nullable|string|max:255',
        ]);

        $clockOutTime = now();
        $totalHours = $clockOutTime->diffInHours($timeEntry->clock_in_time);

        // Check if leaving early
        $shift = $timeEntry->shift;
        $isEarly = $shift ? $clockOutTime->lt(Carbon::parse($shift->end_time)->subMinutes(15)) : false;

        $timeEntry->update([
            'clock_out_time' => $clockOutTime,
            'clock_out_notes' => $request->notes,
            'clock_out_location' => $request->location,
            'total_hours' => $totalHours,
            'is_early_departure' => $isEarly,
        ]);

        // Update user status
        $user->update(['is_on_shift' => false]);

        // End shift if it exists and is active
        if ($shift && $shift->status === 'active') {
            $shift->update([
                'status' => 'completed',
                'actual_end_time' => $clockOutTime->format('H:i:s'),
                'actual_hours_worked' => $totalHours,
                'actual_total_pay' => $totalHours * $shift->hourly_rate,
            ]);
        }

        return response()->json([
            'success' => true,
            'message' => 'Clocked out successfully.',
            'time_entry' => $timeEntry->fresh(),
        ]);
    }

    /**
     * Show the form for creating a new time entry (manual entry)
     */
    public function create()
    {
        $employees = User::where('is_active', true)
                        ->orderBy('name')
                        ->get(['id', 'name', 'department']);

        return Inertia::render('Tenant/TimeEntries/Create', [
            'employees' => $employees,
        ]);
    }

    /**
     * Store a manually created time entry
     */
    public function store(Request $request)
    {
        $request->validate([
            'user_id' => 'required|exists:users,id',
            'date' => 'required|date',
            'clock_in_time' => 'required|date_format:H:i',
            'clock_out_time' => 'required|date_format:H:i|after:clock_in_time',
            'break_duration' => 'nullable|integer|min:0|max:480',
            'clock_in_notes' => 'nullable|string|max:500',
            'clock_out_notes' => 'nullable|string|max:500',
            'admin_notes' => 'nullable|string|max:500',
        ]);

        $clockInDateTime = Carbon::parse($request->date . ' ' . $request->clock_in_time);
        $clockOutDateTime = Carbon::parse($request->date . ' ' . $request->clock_out_time);
        $totalHours = $clockOutDateTime->diffInHours($clockInDateTime) - ($request->break_duration / 60);

        // Find shift for this date and user
        $shift = Shift::where('user_id', $request->user_id)
                     ->whereDate('date', $request->date)
                     ->first();

        TimeEntry::create([
            'user_id' => $request->user_id,
            'shift_id' => $shift?->id,
            'clock_in_time' => $clockInDateTime,
            'clock_out_time' => $clockOutDateTime,
            'date' => $request->date,
            'total_hours' => $totalHours,
            'break_duration' => $request->break_duration ?? 0,
            'clock_in_notes' => $request->clock_in_notes,
            'clock_out_notes' => $request->clock_out_notes,
            'admin_notes' => $request->admin_notes,
            'is_manual_entry' => true,
            'created_by' => auth()->id(),
        ]);

        return redirect()->route('tenant.time-entries.index')
            ->with('success', 'Time entry created successfully.');
    }

    /**
     * Display the specified time entry
     */
    public function show(TimeEntry $timeEntry)
    {
        $timeEntry->load(['user', 'shift', 'createdBy']);

        return Inertia::render('Tenant/TimeEntries/Show', [
            'timeEntry' => $timeEntry,
        ]);
    }

    /**
     * Show the form for editing the specified time entry
     */
    public function edit(TimeEntry $timeEntry)
    {
        $timeEntry->load('user');
        $employees = User::where('is_active', true)
                        ->orderBy('name')
                        ->get(['id', 'name', 'department']);

        return Inertia::render('Tenant/TimeEntries/Edit', [
            'timeEntry' => $timeEntry,
            'employees' => $employees,
        ]);
    }

    /**
     * Update the specified time entry
     */
    public function update(Request $request, TimeEntry $timeEntry)
    {
        $request->validate([
            'clock_in_time' => 'required|date',
            'clock_out_time' => 'nullable|date|after:clock_in_time',
            'break_duration' => 'nullable|integer|min:0|max:480',
            'clock_in_notes' => 'nullable|string|max:500',
            'clock_out_notes' => 'nullable|string|max:500',
            'admin_notes' => 'nullable|string|max:500',
        ]);

        $clockInTime = Carbon::parse($request->clock_in_time);
        $clockOutTime = $request->clock_out_time ? Carbon::parse($request->clock_out_time) : null;
        $totalHours = $clockOutTime ? $clockOutTime->diffInHours($clockInTime) - ($request->break_duration / 60) : null;

        $timeEntry->update([
            'clock_in_time' => $clockInTime,
            'clock_out_time' => $clockOutTime,
            'total_hours' => $totalHours,
            'break_duration' => $request->break_duration ?? 0,
            'clock_in_notes' => $request->clock_in_notes,
            'clock_out_notes' => $request->clock_out_notes,
            'admin_notes' => $request->admin_notes,
        ]);

        return redirect()->route('tenant.time-entries.index')
            ->with('success', 'Time entry updated successfully.');
    }

    /**
     * Remove the specified time entry
     */
    public function destroy(TimeEntry $timeEntry)
    {
        // Prevent deleting active time entries
        if (!$timeEntry->clock_out_time) {
            return back()->withErrors([
                'timeEntry' => 'Cannot delete an active time entry. Please clock out first.'
            ]);
        }

        $timeEntry->delete();

        return redirect()->route('tenant.time-entries.index')
            ->with('success', 'Time entry deleted successfully.');
    }

    /**
     * Get attendance report
     */
    public function attendanceReport(Request $request)
    {
        $request->validate([
            'date_from' => 'required|date',
            'date_to' => 'required|date|after_or_equal:date_from',
            'employee_id' => 'nullable|exists:users,id',
        ]);

        $query = TimeEntry::with('user')
                          ->whereBetween('date', [$request->date_from, $request->date_to]);

        if ($request->employee_id) {
            $query->where('user_id', $request->employee_id);
        }

        $timeEntries = $query->get();

        $report = [
            'summary' => [
                'total_entries' => $timeEntries->count(),
                'total_hours' => $timeEntries->sum('total_hours'),
                'late_arrivals' => $timeEntries->where('is_late', true)->count(),
                'early_departures' => $timeEntries->where('is_early_departure', true)->count(),
            ],
            'daily_breakdown' => $timeEntries->groupBy('date')->map(function ($entries, $date) {
                return [
                    'date' => $date,
                    'total_hours' => $entries->sum('total_hours'),
                    'employees_count' => $entries->count(),
                    'late_count' => $entries->where('is_late', true)->count(),
                ];
            })->values(),
            'employee_breakdown' => $timeEntries->groupBy('user.name')->map(function ($entries, $name) {
                return [
                    'employee' => $name,
                    'total_hours' => $entries->sum('total_hours'),
                    'days_worked' => $entries->count(),
                    'late_count' => $entries->where('is_late', true)->count(),
                    'average_hours' => $entries->avg('total_hours'),
                ];
            })->values(),
        ];

        return response()->json($report);
    }

    /**
     * Get total hours worked today
     */
    private function getTotalHoursToday()
    {
        return TimeEntry::whereDate('date', today())
                       ->sum('total_hours');
    }

    /**
     * Get late clock-ins today
     */
    private function getLateClockInsToday()
    {
        return TimeEntry::whereDate('date', today())
                       ->where('is_late', true)
                       ->count();
    }

    /**
     * Get early departures today
     */
    private function getEarlyDeparturesToday()
    {
        return TimeEntry::whereDate('date', today())
                       ->where('is_early_departure', true)
                       ->count();
    }

    /**
     * Get average hours per day for a user
     */
    private function getAverageHoursPerDay($userId)
    {
        return TimeEntry::where('user_id', $userId)
                       ->whereMonth('date', now()->month)
                       ->avg('total_hours') ?? 0;
    }
}
