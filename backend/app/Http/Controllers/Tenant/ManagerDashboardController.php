<?php

namespace App\Http\Controllers\Tenant;

use App\Http\Controllers\Controller;
use App\Models\Tenant\Order;
use App\Models\Tenant\Customer;
use App\Models\Tenant\Food;
use App\Models\Tenant\Employee;
use App\Models\Tenant\Restaurant;
use App\Models\Tenant\MenuItem;


use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Inertia\Inertia;
use Carbon\Carbon;

class ManagerDashboardController extends Controller
{
    /**
     * Display the restaurant manager dashboard
     */
    public function index(Request $request)
    {
        $restaurant = tenant();
        
        // Get date range for analytics
        $startDate = $request->get('start_date', now()->startOfMonth());
        $endDate = $request->get('end_date', now()->endOfDay());
        
        // Key metrics
        $metrics = $this->getKeyMetrics($startDate, $endDate);
        
        // Recent orders
        $recentOrders = Order::with(['customer', 'items.food'])
            ->latest()
            ->limit(10)
            ->get();
        
        // Top selling items
        $topSellingItems = $this->getTopSellingItems($startDate, $endDate);
        
        // Revenue chart data
        $revenueData = $this->getRevenueChartData($startDate, $endDate);
        
        // Order status breakdown
        $orderStatusBreakdown = Order::whereBetween('created_at', [$startDate, $endDate])
            ->selectRaw('status, COUNT(*) as count')
            ->groupBy('status')
            ->pluck('count', 'status');
        
        // Staff performance
        $staffPerformance = $this->getStaffPerformance($startDate, $endDate);
        
        // Customer insights
        $customerInsights = $this->getCustomerInsights($startDate, $endDate);

        return Inertia::render('Manager/Dashboard', [
            'restaurant' => $restaurant,
            'metrics' => $metrics,
            'recentOrders' => $recentOrders,
            'topSellingItems' => $topSellingItems,
            'revenueData' => $revenueData,
            'orderStatusBreakdown' => $orderStatusBreakdown,
            'staffPerformance' => $staffPerformance,
            'customerInsights' => $customerInsights,
            'dateRange' => [
                'start_date' => $startDate,
                'end_date' => $endDate,
            ],
        ]);
    }

    /**
     * Get key metrics for the dashboard
     */
    private function getKeyMetrics($startDate, $endDate): array
    {
        $orders = Order::whereBetween('created_at', [$startDate, $endDate]);
        
        return [
            'total_revenue' => $orders->where('payment_status', 'paid')->sum('total_amount'),
            'total_orders' => $orders->count(),
            'average_order_value' => $orders->avg('total_amount'),
            'total_customers' => Customer::whereBetween('created_at', [$startDate, $endDate])->count(),
            'active_menu_items' => MenuItem::active()->available()->count(),
            'total_staff' => Employee::active()->count(),
            'pending_orders' => Order::whereIn('status', ['pending', 'confirmed'])->count(),
            'completed_orders_today' => Order::whereDate('created_at', today())
                ->where('status', 'completed')->count(),
        ];
    }

    /**
     * Get top selling items
     */
    private function getTopSellingItems($startDate, $endDate): array
    {
        // Try to get data from foods table first (if food_id exists and is populated)
        $foodsData = DB::table('order_items')
            ->join('orders', 'order_items.order_id', '=', 'orders.id')
            ->join('menu_items', 'order_items.menu_item_id', '=', 'menu_items.id')
            ->whereBetween('orders.created_at', [$startDate, $endDate])
            ->where('orders.payment_status', 'paid')
            // ->whereNotNull('order_items.food_id')
            ->selectRaw('
                menu_items.id as menu_item_id,
                menu_items.name,
                SUM(order_items.quantity) as total_quantity,
                SUM(order_items.total_price) as total_revenue,
                AVG(order_items.item_price) as avg_price
            ')
            ->groupBy('menu_items.id', 'menu_items.name')
            ->orderByDesc('total_quantity')
            ->limit(10)
            ->get()
            ->map(function ($item) {
                // Get the primary media for this menu item using the relationship approach
                $primaryMedia = DB::table('menu_item_media')
                    ->join('media', 'menu_item_media.media_id', '=', 'media.id')
                    ->where('menu_item_media.menu_item_id', $item->menu_item_id)
                    ->where('menu_item_media.is_primary', true)
                    ->select('media.file_name')
                    ->first();

                // Generate proper image URL
                $imageUrl = null;
                if ($primaryMedia) {
                    $imageUrl = route('tenant.asset', ['path' => $primaryMedia->file_name]);
                }

                return [
                    'name' => $item->name,
                    'images' => $imageUrl,
                    'total_quantity' => $item->total_quantity,
                    'total_revenue' => $item->total_revenue,
                    'avg_price' => $item->avg_price,
                ];
            });

        // If no data from foods table, fallback to menu_items table
        if ($foodsData->isEmpty()) {
            $menuItemsData = DB::table('order_items')
                ->join('orders', 'order_items.order_id', '=', 'orders.id')
                ->join('menu_items', 'order_items.menu_item_id', '=', 'menu_items.id')
                ->whereBetween('orders.created_at', [$startDate, $endDate])
                ->where('orders.payment_status', 'paid')
                ->selectRaw('
                    menu_items.id as menu_item_id,
                    menu_items.name,
                    menu_items.image,
                    SUM(order_items.quantity) as total_quantity,
                    SUM(order_items.total_price) as total_revenue,
                    AVG(order_items.item_price) as avg_price
                ')
                ->groupBy('menu_items.id', 'menu_items.name', 'menu_items.image')
                ->orderByDesc('total_quantity')
                ->limit(10)
                ->get();

            // Post-process to get proper image URLs
            return $menuItemsData->map(function ($item) {
                // Get the primary media for this menu item
                $primaryMedia = DB::table('menu_item_media')
                    ->join('media', 'menu_item_media.media_id', '=', 'media.id')
                    ->where('menu_item_media.menu_item_id', $item->menu_item_id)
                    ->where('menu_item_media.is_primary', true)
                    ->select('media.file_name')
                    ->first();

                // Generate proper image URL
                $imageUrl = null;
                if ($primaryMedia) {
                    $imageUrl = route('tenant.asset', ['path' => $primaryMedia->file_name]);
                } elseif ($item->image) {
                    $imageUrl = $item->image;
                }

                return [
                    'name' => $item->name,
                    'images' => $imageUrl,
                    'total_quantity' => $item->total_quantity,
                    'total_revenue' => $item->total_revenue,
                    'avg_price' => $item->avg_price,
                ];
            })->toArray();
        }

        return $foodsData->toArray();
    }

    /**
     * Get revenue chart data
     */
    private function getRevenueChartData($startDate, $endDate): array
    {
        $days = [];
        $revenues = [];
        
        $period = Carbon::parse($startDate)->daysUntil($endDate);
        
        foreach ($period as $date) {
            $dayRevenue = Order::whereDate('created_at', $date)
                ->where('payment_status', 'paid')
                ->sum('total_amount');
            
            $days[] = $date->format('M d');
            $revenues[] = (float) $dayRevenue;
        }
        
        return [
            'labels' => $days,
            'datasets' => [
                [
                    'label' => 'Daily Revenue',
                    'data' => $revenues,
                    'borderColor' => 'rgb(59, 130, 246)',
                    'backgroundColor' => 'rgba(59, 130, 246, 0.1)',
                    'tension' => 0.1,
                ]
            ]
        ];
    }

    /**
     * Get staff performance data
     */
    private function getStaffPerformance($startDate, $endDate): array
    {
        return Employee::active()
            ->withCount([
                'handledOrders' => function ($query) use ($startDate, $endDate) {
                    $query->whereBetween('created_at', [$startDate, $endDate]);
                }
            ])
            ->limit(10)
            ->get()
            ->map(function ($employee) use ($startDate, $endDate) {
                // Calculate average service time for this employee
                $avgServiceTime = DB::table('orders')
                    ->where('waiter_id', $employee->id)
                    ->whereBetween('created_at', [$startDate, $endDate])
                    ->whereNotNull('served_at')
                    ->whereNotNull('created_at')
                    ->selectRaw('AVG(TIMESTAMPDIFF(MINUTE, created_at, served_at)) as avg_time')
                    ->value('avg_time') ?? 0;

                return [
                    'id' => $employee->id,
                    'name' => $employee->full_name,
                    'position' => $employee->position,
                    'orders_served' => $employee->handled_orders_count,
                    'avg_service_time' => round($avgServiceTime, 2),
                ];
            })
            ->toArray();
    }

    /**
     * Get customer insights
     */
    private function getCustomerInsights($startDate, $endDate): array
    {
        $newCustomers = Customer::whereBetween('created_at', [$startDate, $endDate])->count();
        $returningCustomers = Customer::whereHas('orders', function ($query) use ($startDate, $endDate) {
            $query->whereBetween('created_at', [$startDate, $endDate]);
        })->where('total_orders', '>', 1)->count();
        
        $topCustomers = Customer::withSum(['orders' => function ($query) use ($startDate, $endDate) {
            $query->whereBetween('created_at', [$startDate, $endDate])
                ->where('payment_status', 'paid');
        }], 'total_amount')
            ->orderByDesc('orders_sum_total_amount')
            ->limit(5)
            ->get();

        return [
            'new_customers' => $newCustomers,
            'returning_customers' => $returningCustomers,
            'customer_retention_rate' => $newCustomers > 0 ? ($returningCustomers / $newCustomers) * 100 : 0,
            'top_customers' => $topCustomers,
        ];
    }

    /**
     * Get sales analytics
     */
    public function analytics(Request $request)
    {
        // Detailed analytics for restaurant managers
        $period = $request->get('period', '30'); // days
        $startDate = now()->subDays($period);
        $endDate = now();

        $analytics = [
            'sales_overview' => $this->getSalesOverview($startDate, $endDate),
            'menu_performance' => $this->getMenuPerformance($startDate, $endDate),
            'customer_analytics' => $this->getCustomerAnalytics($startDate, $endDate),
            'operational_metrics' => $this->getOperationalMetrics($startDate, $endDate),
        ];

        return Inertia::render('Manager/Analytics', [
            'analytics' => $analytics,
            'period' => $period,
        ]);
    }

    private function getSalesOverview($startDate, $endDate): array
    {
        // Implementation for detailed sales analytics
        return [];
    }

    private function getMenuPerformance($startDate, $endDate): array
    {
        // Implementation for menu performance analytics
        return [];
    }

    private function getCustomerAnalytics($startDate, $endDate): array
    {
        // Implementation for customer analytics
        return [];
    }

    private function getOperationalMetrics($startDate, $endDate): array
    {
        // Implementation for operational metrics
        return [];
    }
}
