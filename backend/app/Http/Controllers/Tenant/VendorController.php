<?php

namespace App\Http\Controllers\Tenant;

use App\Http\Controllers\Controller;
use App\Models\Tenant\Vendor;
use App\Models\Tenant\Restaurant;
use Illuminate\Http\Request;
use Inertia\Inertia;

class VendorController extends Controller
{
    /**
     * Display a listing of vendors.
     */
    public function index(Request $request)
    {
        $query = Vendor::with(['restaurant', 'media']);

        // Search
        if ($request->search) {
            $query->where(function ($q) use ($request) {
                $q->where('name', 'like', '%' . $request->search . '%')
                  ->orWhere('company_name', 'like', '%' . $request->search . '%')
                  ->orWhere('contact_person', 'like', '%' . $request->search . '%')
                  ->orWhere('email', 'like', '%' . $request->search . '%')
                  ->orWhere('phone', 'like', '%' . $request->search . '%');
            });
        }

        // Filter by vendor type
        if ($request->vendor_type) {
            $query->where('vendor_type', $request->vendor_type);
        }

        // Filter by status
        if ($request->status) {
            if ($request->status === 'active') {
                $query->where('is_active', true);
            } elseif ($request->status === 'inactive') {
                $query->where('is_active', false);
            }
        }

        // Filter by outstanding balance
        if ($request->has_outstanding_balance) {
            $query->withOutstandingBalance();
        }

        // Filter by overdue payments
        if ($request->has_overdue_payments) {
            $query->withOverduePayments();
        }

        $vendors = $query->latest()->paginate(20);

        // Calculate stats
        $stats = [
            'total_vendors' => Vendor::count(),
            'active_vendors' => Vendor::where('is_active', true)->count(),
            'total_orders' => 0, // This would need to be calculated based on your purchase order relationship
            'total_spent' => 0, // This would need to be calculated based on your purchase order relationship
        ];

        return Inertia::render('Tenant/Vendors/Index', [
            'vendors' => $vendors,
            'stats' => $stats,
            'filters' => $request->only(['search', 'vendor_type', 'status', 'has_outstanding_balance', 'has_overdue_payments']),
            'vendorTypeOptions' => $this->getVendorTypeOptions(),
        ]);
    }

    /**
     * Show the form for creating a new vendor.
     */
    public function create()
    {
        $restaurant = Restaurant::first();

        return Inertia::render('Tenant/Vendors/Create', [
            'restaurant' => $restaurant,
            'vendorTypeOptions' => $this->getVendorTypeOptions(),
        ]);
    }

    /**
     * Store a newly created vendor.
     */
    public function store(Request $request)
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'company_name' => 'nullable|string|max:255',
            'vendor_type' => 'required|in:supplier,service_provider,contractor,utility,other',
            'contact_person' => 'nullable|string|max:255',
            'email' => 'nullable|email|max:255',
            'phone' => 'nullable|string|max:20',
            'mobile' => 'nullable|string|max:20',
            'website' => 'nullable|url|max:255',
            'tax_id' => 'nullable|string|max:255',
            'registration_number' => 'nullable|string|max:255',
            'address' => 'nullable|string',
            'city' => 'nullable|string|max:255',
            'state' => 'nullable|string|max:255',
            'country' => 'nullable|string|max:255',
            'postal_code' => 'nullable|string|max:20',
            'payment_terms' => 'nullable|string|max:255',
            'credit_limit' => 'nullable|numeric|min:0',
            'discount_percentage' => 'nullable|numeric|min:0|max:100',
            'notes' => 'nullable|string',
            'is_active' => 'boolean',
            'media_ids' => 'nullable|array',
            'media_ids.*' => 'exists:media,id',
        ]);

        $restaurant = Restaurant::first();

        $vendor = Vendor::create([
            'restaurant_id' => $restaurant->id,
            'name' => $request->name,
            'company_name' => $request->company_name,
            'vendor_type' => $request->vendor_type,
            'contact_person' => $request->contact_person,
            'email' => $request->email,
            'phone' => $request->phone,
            'mobile' => $request->mobile,
            'website' => $request->website,
            'tax_id' => $request->tax_id,
            'registration_number' => $request->registration_number,
            'address' => $request->address,
            'city' => $request->city,
            'state' => $request->state,
            'country' => $request->country,
            'postal_code' => $request->postal_code,
            'payment_terms' => $request->payment_terms,
            'credit_limit' => $request->credit_limit,
            'discount_percentage' => $request->discount_percentage ?? 0,
            'notes' => $request->notes,
            'is_active' => $request->boolean('is_active', true),
        ]);

        // Attach media
        if ($request->media_ids) {
            foreach ($request->media_ids as $mediaId) {
                $vendor->addMediaFromLibrary($mediaId, 'documents');
            }
        }

        return redirect()->route('vendors.index')
            ->with('success', 'Vendor created successfully.');
    }

    /**
     * Display the specified vendor.
     */
    public function show(Vendor $vendor)
    {
        $vendor->load([
            'restaurant', 'media',
            'expenses' => function ($query) {
                $query->with(['category', 'creator'])->latest()->take(10);
            }
        ]);

        // Get vendor performance metrics
        $performanceMetrics = $vendor->getPerformanceMetrics();
        $spendingTrend = $vendor->getSpendingTrend(6);

        return Inertia::render('Tenant/Vendors/Show', [
            'vendor' => $vendor,
            'performanceMetrics' => $performanceMetrics,
            'spendingTrend' => $spendingTrend,
        ]);
    }

    /**
     * Show the form for editing the specified vendor.
     */
    public function edit(Vendor $vendor)
    {
        $vendor->load(['media']);
        $restaurant = Restaurant::first();

        return Inertia::render('Tenant/Vendors/Edit', [
            'vendor' => $vendor,
            'restaurant' => $restaurant,
            'vendorTypeOptions' => $this->getVendorTypeOptions(),
        ]);
    }

    /**
     * Update the specified vendor.
     */
    public function update(Request $request, Vendor $vendor)
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'company_name' => 'nullable|string|max:255',
            'vendor_type' => 'required|in:supplier,service_provider,contractor,utility,other',
            'contact_person' => 'nullable|string|max:255',
            'email' => 'nullable|email|max:255',
            'phone' => 'nullable|string|max:20',
            'mobile' => 'nullable|string|max:20',
            'website' => 'nullable|url|max:255',
            'tax_id' => 'nullable|string|max:255',
            'registration_number' => 'nullable|string|max:255',
            'address' => 'nullable|string',
            'city' => 'nullable|string|max:255',
            'state' => 'nullable|string|max:255',
            'country' => 'nullable|string|max:255',
            'postal_code' => 'nullable|string|max:20',
            'payment_terms' => 'nullable|string|max:255',
            'credit_limit' => 'nullable|numeric|min:0',
            'discount_percentage' => 'nullable|numeric|min:0|max:100',
            'notes' => 'nullable|string',
            'is_active' => 'boolean',
            'media_ids' => 'nullable|array',
            'media_ids.*' => 'exists:media,id',
        ]);

        $vendor->update([
            'name' => $request->name,
            'company_name' => $request->company_name,
            'vendor_type' => $request->vendor_type,
            'contact_person' => $request->contact_person,
            'email' => $request->email,
            'phone' => $request->phone,
            'mobile' => $request->mobile,
            'website' => $request->website,
            'tax_id' => $request->tax_id,
            'registration_number' => $request->registration_number,
            'address' => $request->address,
            'city' => $request->city,
            'state' => $request->state,
            'country' => $request->country,
            'postal_code' => $request->postal_code,
            'payment_terms' => $request->payment_terms,
            'credit_limit' => $request->credit_limit,
            'discount_percentage' => $request->discount_percentage,
            'notes' => $request->notes,
            'is_active' => $request->boolean('is_active'),
        ]);

        // Update media
        $vendor->clearMediaCollection('documents');
        if ($request->media_ids) {
            foreach ($request->media_ids as $mediaId) {
                $vendor->addMediaFromLibrary($mediaId, 'documents');
            }
        }

        return redirect()->route('vendors.index')
            ->with('success', 'Vendor updated successfully.');
    }

    /**
     * Remove the specified vendor.
     */
    public function destroy(Vendor $vendor)
    {
        // Check if vendor has expenses
        if ($vendor->expenses()->exists()) {
            return back()->withErrors(['error' => 'Cannot delete vendor with existing expenses.']);
        }

        $vendor->delete();

        return redirect()->route('vendors.index')
            ->with('success', 'Vendor deleted successfully.');
    }

    /**
     * Toggle vendor status.
     */
    public function toggleStatus(Vendor $vendor)
    {
        $vendor->update(['is_active' => !$vendor->is_active]);

        $status = $vendor->is_active ? 'activated' : 'deactivated';
        
        return response()->json([
            'success' => true,
            'message' => "Vendor {$status} successfully.",
            'is_active' => $vendor->is_active,
        ]);
    }

    /**
     * Update vendor rating.
     */
    public function updateRating(Request $request, Vendor $vendor)
    {
        $request->validate([
            'rating' => 'required|numeric|min:1|max:5',
        ]);

        $vendor->update(['rating' => $request->rating]);

        return response()->json([
            'success' => true,
            'message' => 'Vendor rating updated successfully.',
            'rating' => $vendor->rating,
        ]);
    }

    /**
     * Bulk update vendors.
     */
    public function bulkUpdate(Request $request)
    {
        $request->validate([
            'ids' => 'required|array',
            'ids.*' => 'exists:vendors,id',
            'action' => 'required|in:activate,deactivate,delete',
            'vendor_type' => 'nullable|in:supplier,service_provider,contractor,utility,other',
        ]);

        $vendors = Vendor::whereIn('id', $request->ids);

        switch ($request->action) {
            case 'activate':
                $vendors->update(['is_active' => true]);
                $message = 'Vendors activated successfully.';
                break;
            case 'deactivate':
                $vendors->update(['is_active' => false]);
                $message = 'Vendors deactivated successfully.';
                break;
            case 'delete':
                // Check if any vendor has expenses
                $hasExpenses = $vendors->whereHas('expenses')->exists();
                if ($hasExpenses) {
                    return back()->withErrors(['error' => 'Cannot delete vendors with existing expenses.']);
                }
                $vendors->delete();
                $message = 'Vendors deleted successfully.';
                break;
        }

        // Update vendor type if provided
        if ($request->vendor_type && $request->action !== 'delete') {
            Vendor::whereIn('id', $request->ids)->update(['vendor_type' => $request->vendor_type]);
        }

        return back()->with('success', $message);
    }

    /**
     * Get vendor payment summary.
     */
    public function paymentSummary()
    {
        $vendors = Vendor::active()
            ->withOutstandingBalance()
            ->get()
            ->map(function ($vendor) {
                return [
                    'id' => $vendor->id,
                    'name' => $vendor->name,
                    'outstanding_balance' => $vendor->outstanding_balance,
                    'overdue_amount' => $vendor->overdue_amount,
                    'payment_terms' => $vendor->payment_terms,
                    'last_payment_date' => $vendor->expenses()->whereNotNull('paid_at')->latest('paid_at')->first()?->paid_at,
                ];
            });

        return Inertia::render('Tenant/Vendors/PaymentSummary', [
            'vendors' => $vendors,
        ]);
    }

    /**
     * Get vendor type options.
     */
    protected function getVendorTypeOptions(): array
    {
        return [
            ['value' => 'supplier', 'label' => 'Supplier'],
            ['value' => 'service_provider', 'label' => 'Service Provider'],
            ['value' => 'contractor', 'label' => 'Contractor'],
            ['value' => 'utility', 'label' => 'Utility'],
            ['value' => 'other', 'label' => 'Other'],
        ];
    }
}
