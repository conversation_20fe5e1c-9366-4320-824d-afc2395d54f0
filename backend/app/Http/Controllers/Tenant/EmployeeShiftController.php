<?php

namespace App\Http\Controllers\Tenant;

use App\Http\Controllers\Controller;
use App\Models\Tenant\EmployeeShift;
use App\Models\Tenant\Branch;
use App\Models\Tenant\Employee;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use Carbon\Carbon;
use Inertia\Inertia;

class EmployeeShiftController extends Controller
{
    /**
     * Display a listing of shifts.
     */
    public function index(Request $request)
    {
        $query = EmployeeShift::with(['branch', 'employee', 'creator']);

        // Filter by date range
        if ($request->filled('start_date')) {
            $query->where('shift_date', '>=', $request->start_date);
        }
        if ($request->filled('end_date')) {
            $query->where('shift_date', '<=', $request->end_date);
        }

        // Filter by branch
        if ($request->filled('branch_id')) {
            $query->where('branch_id', $request->branch_id);
        }

        // Filter by employee
        if ($request->filled('employee_id')) {
            $query->where('employee_id', $request->employee_id);
        }

        // Filter by status
        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }

        $shifts = $query->orderBy('shift_date', 'desc')
            ->orderBy('start_time')
            ->paginate(20);

        $branches = Branch::active()->ordered()->get(['id', 'name']);
        $employees = Employee::active()->get(['id', 'name', 'first_name', 'last_name']);

        return Inertia::render('Tenant/EmployeeShifts/Index', [
            'shifts' => $shifts,
            'branches' => $branches,
            'employees' => $employees,
            'filters' => $request->only(['start_date', 'end_date', 'branch_id', 'employee_id', 'status']),
        ]);
    }

    /**
     * Show the form for creating a new shift.
     */
    public function create()
    {
        $branches = Branch::active()->ordered()->get(['id', 'name']);
        $employees = Employee::active()
            ->with('primaryBranch')
            ->get(['id', 'name', 'first_name', 'last_name', 'primary_branch_id', 'employee_type']);

        return Inertia::render('Tenant/EmployeeShifts/Create', [
            'branches' => $branches,
            'employees' => $employees,
            'shiftTypes' => EmployeeShift::SHIFT_TYPES,
        ]);
    }

    /**
     * Store a newly created shift.
     */
    public function store(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'branch_id' => 'required|exists:branches,id',
            'employee_id' => 'required|exists:employees,id',
            'shift_date' => 'required|date|after_or_equal:today',
            'start_time' => 'required|date_format:H:i',
            'end_time' => 'required|date_format:H:i',
            'shift_type' => 'required|in:' . implode(',', array_keys(EmployeeShift::SHIFT_TYPES)),
            'notes' => 'nullable|string|max:1000',
            'break_duration' => 'nullable|numeric|min:0|max:8',
            'is_holiday' => 'boolean',
            'hourly_rate' => 'nullable|numeric|min:0',
        ]);

        if ($validator->fails()) {
            return back()->withErrors($validator)->withInput();
        }

        $data = $validator->validated();
        $data['created_by'] = auth()->id();
        $data['status'] = 'scheduled';

        // Check for overlapping shifts
        $overlapping = EmployeeShift::where('employee_id', $data['employee_id'])
            ->where('shift_date', $data['shift_date'])
            ->where('status', '!=', 'cancelled')
            ->where(function ($query) use ($data) {
                $query->whereBetween('start_time', [$data['start_time'], $data['end_time']])
                    ->orWhereBetween('end_time', [$data['start_time'], $data['end_time']])
                    ->orWhere(function ($q) use ($data) {
                        $q->where('start_time', '<=', $data['start_time'])
                          ->where('end_time', '>=', $data['end_time']);
                    });
            })
            ->exists();

        if ($overlapping) {
            return back()->withErrors(['error' => 'Employee already has a shift during this time period.']);
        }

        EmployeeShift::create($data);

        return redirect()->route('employee-shifts.index')
            ->with('success', 'Shift created successfully.');
    }

    /**
     * Display the specified shift.
     */
    public function show(EmployeeShift $employeeShift)
    {
        $employeeShift->load(['branch', 'employee', 'creator']);

        return Inertia::render('Tenant/EmployeeShifts/Show', [
            'shift' => $employeeShift,
        ]);
    }

    /**
     * Show the form for editing the specified shift.
     */
    public function edit(EmployeeShift $employeeShift)
    {
        $branches = Branch::active()->ordered()->get(['id', 'name']);
        $employees = Employee::active()
            ->with('primaryBranch')
            ->get(['id', 'name', 'first_name', 'last_name', 'primary_branch_id', 'employee_type']);

        return Inertia::render('Tenant/EmployeeShifts/Edit', [
            'shift' => $employeeShift,
            'branches' => $branches,
            'employees' => $employees,
            'shiftTypes' => EmployeeShift::SHIFT_TYPES,
            'statuses' => EmployeeShift::STATUSES,
        ]);
    }

    /**
     * Update the specified shift.
     */
    public function update(Request $request, EmployeeShift $employeeShift)
    {
        $validator = Validator::make($request->all(), [
            'branch_id' => 'required|exists:branches,id',
            'employee_id' => 'required|exists:employees,id',
            'shift_date' => 'required|date',
            'start_time' => 'required|date_format:H:i',
            'end_time' => 'required|date_format:H:i',
            'shift_type' => 'required|in:' . implode(',', array_keys(EmployeeShift::SHIFT_TYPES)),
            'status' => 'required|in:' . implode(',', array_keys(EmployeeShift::STATUSES)),
            'actual_start_time' => 'nullable|date_format:H:i',
            'actual_end_time' => 'nullable|date_format:H:i',
            'notes' => 'nullable|string|max:1000',
            'break_duration' => 'nullable|numeric|min:0|max:8',
            'overtime_hours' => 'nullable|numeric|min:0|max:8',
            'is_holiday' => 'boolean',
            'hourly_rate' => 'nullable|numeric|min:0',
        ]);

        if ($validator->fails()) {
            return back()->withErrors($validator)->withInput();
        }

        $data = $validator->validated();

        // Check for overlapping shifts (excluding current shift)
        if ($data['employee_id'] != $employeeShift->employee_id || 
            $data['shift_date'] != $employeeShift->shift_date->format('Y-m-d') ||
            $data['start_time'] != $employeeShift->start_time ||
            $data['end_time'] != $employeeShift->end_time) {
            
            $overlapping = EmployeeShift::where('employee_id', $data['employee_id'])
                ->where('shift_date', $data['shift_date'])
                ->where('id', '!=', $employeeShift->id)
                ->where('status', '!=', 'cancelled')
                ->where(function ($query) use ($data) {
                    $query->whereBetween('start_time', [$data['start_time'], $data['end_time']])
                        ->orWhereBetween('end_time', [$data['start_time'], $data['end_time']])
                        ->orWhere(function ($q) use ($data) {
                            $q->where('start_time', '<=', $data['start_time'])
                              ->where('end_time', '>=', $data['end_time']);
                        });
                })
                ->exists();

            if ($overlapping) {
                return back()->withErrors(['error' => 'Employee already has a shift during this time period.']);
            }
        }

        $employeeShift->update($data);

        return redirect()->route('employee-shifts.index')
            ->with('success', 'Shift updated successfully.');
    }

    /**
     * Remove the specified shift.
     */
    public function destroy(EmployeeShift $employeeShift)
    {
        // Only allow deletion of future shifts or scheduled shifts
        if ($employeeShift->shift_date->isPast() && $employeeShift->status !== 'scheduled') {
            return back()->withErrors(['error' => 'Cannot delete past shifts that have been started or completed.']);
        }

        $employeeShift->delete();

        return redirect()->route('employee-shifts.index')
            ->with('success', 'Shift deleted successfully.');
    }

    /**
     * Get shifts for calendar view.
     */
    public function calendar(Request $request)
    {
        $startDate = $request->get('start', now()->startOfMonth());
        $endDate = $request->get('end', now()->endOfMonth());

        $shifts = EmployeeShift::with(['employee', 'branch'])
            ->betweenDates($startDate, $endDate)
            ->get()
            ->map(function ($shift) {
                return [
                    'id' => $shift->id,
                    'title' => $shift->employee->display_name . ' - ' . $shift->branch->name,
                    'start' => $shift->shift_date->format('Y-m-d') . 'T' . $shift->start_time,
                    'end' => $shift->shift_date->format('Y-m-d') . 'T' . $shift->end_time,
                    'backgroundColor' => $this->getShiftColor($shift->status),
                    'borderColor' => $this->getShiftColor($shift->status),
                    'extendedProps' => [
                        'employee' => $shift->employee->display_name,
                        'branch' => $shift->branch->name,
                        'status' => $shift->status,
                        'shift_type' => $shift->shift_type,
                    ],
                ];
            });

        return response()->json($shifts);
    }

    /**
     * Get color for shift status.
     */
    private function getShiftColor(string $status): string
    {
        return match($status) {
            'scheduled' => '#6b7280',
            'confirmed' => '#3b82f6',
            'started' => '#10b981',
            'completed' => '#059669',
            'cancelled' => '#ef4444',
            'no_show' => '#f59e0b',
            default => '#6b7280'
        };
    }
}
