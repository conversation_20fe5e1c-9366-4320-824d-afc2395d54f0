<?php

namespace App\Http\Controllers\Tenant;

use App\Http\Controllers\Controller;
use App\Models\Tenant\ComboMenu;
use App\Models\Tenant\ComboComponent;
use App\Models\Tenant\MenuItem;
use Illuminate\Http\Request;
use Inertia\Inertia;
use Illuminate\Support\Facades\DB;

class ComboMenuController extends Controller
{
    /**
     * Display a listing of the combo menus.
     */
    public function index(Request $request)
    {
        $query = ComboMenu::with(['components.menuItem', 'primaryMedia', 'mediaItems']);

        // Search functionality
        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('description', 'like', "%{$search}%");
            });
        }

        // Filter by availability
        if ($request->filled('is_available')) {
            $query->where('is_available', $request->boolean('is_available'));
        }

        $combos = $query->ordered()->paginate(15)->withQueryString();

        return Inertia::render('Tenant/ComboMenus/Index', [
            'combos' => $combos,
            'filters' => $request->only(['search', 'is_available']),
        ]);
    }

    /**
     * Show the form for creating a new combo menu.
     */
    public function create()
    {
        $menuItems = MenuItem::available()
            ->with(['category', 'primaryMedia'])
            ->ordered()
            ->get();

        $componentTypes = ComboComponent::getComponentTypes();

        return Inertia::render('Tenant/ComboMenus/Create', [
            'menuItems' => $menuItems,
            'componentTypes' => $componentTypes,
        ]);
    }

    /**
     * Store a newly created combo menu in storage.
     */
    public function store(Request $request)
    {
        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'description' => 'nullable|string|max:1000',
            'price' => 'required|numeric|min:0',
            'is_available' => 'boolean',
            'sort_order' => 'integer|min:0',
            'image_url' => 'nullable|url', // Deprecated - for backward compatibility
            'media_id' => 'nullable|exists:media,id',
            'media_ids' => 'nullable|array|max:5',
            'media_ids.*' => 'exists:media,id',
            'customization_rules' => 'nullable|array',
            'components' => 'required|array|min:1',
            'components.*.component_type' => 'required|in:main,side,drink,dessert',
            'components.*.menu_item_id' => 'required|exists:menu_items,id',
            'components.*.is_required' => 'boolean',
            'components.*.max_selections' => 'integer|min:1|max:10',
            'components.*.sort_order' => 'integer|min:0',
        ]);

        DB::transaction(function () use ($validated) {
            $combo = ComboMenu::create([
                'name' => $validated['name'],
                'description' => $validated['description'],
                'price' => $validated['price'],
                'is_available' => $validated['is_available'] ?? true,
                'sort_order' => $validated['sort_order'] ?? 0,
                'image_url' => $validated['image_url'], // Deprecated
                'media_id' => $validated['media_id'],
                'customization_rules' => $validated['customization_rules'],
            ]);

            // Handle media relationships
            if (!empty($validated['media_ids']) && is_array($validated['media_ids'])) {
                $combo->syncMedia($validated['media_ids']);
            }

            // Create components
            foreach ($validated['components'] as $componentData) {
                $combo->components()->create([
                    'component_type' => $componentData['component_type'],
                    'menu_item_id' => $componentData['menu_item_id'],
                    'is_required' => $componentData['is_required'] ?? false,
                    'max_selections' => $componentData['max_selections'] ?? 1,
                    'sort_order' => $componentData['sort_order'] ?? 0,
                ]);
            }
        });

        return redirect()->route('combo-menus.index')
            ->with('success', 'Combo menu created successfully.');
    }

    /**
     * Display the specified combo menu.
     */
    public function show(ComboMenu $comboMenu)
    {
        $comboMenu->load([
            'components.menuItem.category',
            'components.menuItem.primaryMedia',
            'primaryMedia',
            'mediaItems'
        ]);

        return Inertia::render('Tenant/ComboMenus/Show', [
            'combo' => $comboMenu,
        ]);
    }

    /**
     * Show the form for editing the specified combo menu.
     */
    public function edit(ComboMenu $comboMenu)
    {
        $comboMenu->load(['components.menuItem', 'primaryMedia', 'mediaItems']);

        $menuItems = MenuItem::available()
            ->with(['category', 'primaryMedia'])
            ->ordered()
            ->get();

        $componentTypes = ComboComponent::getComponentTypes();

        return Inertia::render('Tenant/ComboMenus/Edit', [
            'combo' => $comboMenu,
            'menuItems' => $menuItems,
            'componentTypes' => $componentTypes,
        ]);
    }

    /**
     * Update the specified combo menu in storage.
     */
    public function update(Request $request, ComboMenu $comboMenu)
    {
        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'description' => 'nullable|string|max:1000',
            'price' => 'required|numeric|min:0',
            'is_available' => 'boolean',
            'sort_order' => 'integer|min:0',
            'image_url' => 'nullable|url', // Deprecated - for backward compatibility
            'media_id' => 'nullable|exists:media,id',
            'media_ids' => 'nullable|array|max:5',
            'media_ids.*' => 'exists:media,id',
            'customization_rules' => 'nullable|array',
            'components' => 'required|array|min:1',
            'components.*.component_type' => 'required|in:main,side,drink,dessert',
            'components.*.menu_item_id' => 'required|exists:menu_items,id',
            'components.*.is_required' => 'boolean',
            'components.*.max_selections' => 'integer|min:1|max:10',
            'components.*.sort_order' => 'integer|min:0',
        ]);

        DB::transaction(function () use ($validated, $comboMenu) {
            $comboMenu->update([
                'name' => $validated['name'],
                'description' => $validated['description'],
                'price' => $validated['price'],
                'is_available' => $validated['is_available'] ?? true,
                'sort_order' => $validated['sort_order'] ?? 0,
                'image_url' => $validated['image_url'], // Deprecated
                'media_id' => $validated['media_id'],
                'customization_rules' => $validated['customization_rules'],
            ]);

            // Handle media relationships
            if (isset($validated['media_ids']) && is_array($validated['media_ids'])) {
                $comboMenu->syncMedia($validated['media_ids']);
            }

            // Delete existing components and recreate
            $comboMenu->components()->delete();

            // Create new components
            foreach ($validated['components'] as $componentData) {
                $comboMenu->components()->create([
                    'component_type' => $componentData['component_type'],
                    'menu_item_id' => $componentData['menu_item_id'],
                    'is_required' => $componentData['is_required'] ?? false,
                    'max_selections' => $componentData['max_selections'] ?? 1,
                    'sort_order' => $componentData['sort_order'] ?? 0,
                ]);
            }
        });

        return redirect()->route('combo-menus.index')
            ->with('success', 'Combo menu updated successfully.');
    }

    /**
     * Remove the specified combo menu from storage.
     */
    public function destroy(ComboMenu $comboMenu)
    {
        $comboMenu->delete();

        return redirect()->route('combo-menus.index')
            ->with('success', 'Combo menu deleted successfully.');
    }

    /**
     * Toggle combo menu availability.
     */
    public function toggleAvailability(ComboMenu $comboMenu)
    {
        $comboMenu->update([
            'is_available' => !$comboMenu->is_available
        ]);

        $status = $comboMenu->is_available ? 'enabled' : 'disabled';

        return back()->with('success', "Combo menu {$status} successfully.");
    }

    /**
     * Get available combo menus for API.
     */
    public function getAvailableCombos(Request $request)
    {
        $combos = ComboMenu::available()
            ->with(['components.menuItem'])
            ->ordered()
            ->get();

        return response()->json($combos);
    }
}
