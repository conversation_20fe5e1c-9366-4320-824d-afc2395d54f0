<?php

namespace App\Http\Controllers\Tenant;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Inertia\Inertia;
use App\Models\Tenant\Order;
use App\Models\Tenant\MenuItem;
use App\Models\Tenant\Customer;
use App\Models\Tenant\Staff;
use App\Models\Tenant\Table;
use Carbon\Carbon;

class DashboardController extends Controller
{
    /**
     * Show the tenant dashboard
     */
    public function index()
    {
        $stats = $this->getDashboardStats();
        
        return Inertia::render('Tenant/Dashboard', [
            'stats' => $stats,
            'recentOrders' => $this->getRecentOrders(),
            'topSellingItems' => $this->getTopSellingItems(),
            'todaysSales' => $this->getTodaysSales(),
            'ordersByStatus' => $this->getOrdersByStatus(),
        ]);
    }

    /**
     * Get dashboard statistics
     */
    private function getDashboardStats()
    {
        $today = Carbon::today();
        
        return [
            'todays_orders' => Order::whereDate('created_at', $today)->count(),
            'todays_revenue' => Order::whereDate('created_at', $today)
                ->where('payment_status', 'paid')
                ->sum('total_amount'),
            'pending_orders' => Order::where('status', 'pending')->count(),
            'preparing_orders' => Order::where('status', 'preparing')->count(),
            'total_customers' => Customer::count(),
            'total_menu_items' => MenuItem::active()->count(),
            'total_staff' => Staff::where('is_active', true)->count(),
            'available_tables' => Table::where('status', 'available')->count(),
        ];
    }

    /**
     * Get recent orders
     */
    private function getRecentOrders()
    {
        return Order::with(['customer', 'table', 'waiter'])
            ->latest()
            ->take(10)
            ->get()
            ->map(function ($order) {
                return [
                    'id' => $order->id,
                    'order_number' => $order->order_number,
                    'customer_name' => $order->customer_name ?? $order->customer?->first_name . ' ' . $order->customer?->last_name,
                    'table' => $order->table?->name,
                    'total_amount' => $order->total_amount,
                    'status' => $order->status,
                    'payment_status' => $order->payment_status,
                    'order_type' => $order->order_type,
                    'created_at' => $order->created_at->format('H:i'),
                ];
            });
    }

    /**
     * Get top selling items
     */
    private function getTopSellingItems()
    {
        return MenuItem::withCount(['orderItems' => function ($query) {
                $query->whereHas('order', function ($q) {
                    $q->whereDate('created_at', '>=', Carbon::now()->subDays(30));
                });
            }])
            ->having('order_items_count', '>', 0)
            ->orderBy('order_items_count', 'desc')
            ->take(5)
            ->get()
            ->map(function ($item) {
                return [
                    'name' => $item->name,
                    'price' => $item->price,
                    'orders_count' => $item->order_items_count,
                    'image' => $item->image,
                ];
            });
    }

    /**
     * Get today's sales by hour
     */
    private function getTodaysSales()
    {
        $hours = [];
        for ($i = 0; $i < 24; $i++) {
            $hour = str_pad($i, 2, '0', STR_PAD_LEFT) . ':00';
            $sales = Order::whereDate('created_at', Carbon::today())
                ->whereTime('created_at', '>=', $hour)
                ->whereTime('created_at', '<', str_pad($i + 1, 2, '0', STR_PAD_LEFT) . ':00')
                ->where('payment_status', 'paid')
                ->sum('total_amount');
            
            $hours[] = [
                'hour' => $hour,
                'sales' => $sales,
            ];
        }
        
        return $hours;
    }

    /**
     * Get orders by status
     */
    private function getOrdersByStatus()
    {
        $statuses = ['pending', 'confirmed', 'preparing', 'ready', 'served', 'delivered', 'cancelled'];
        $data = [];
        
        foreach ($statuses as $status) {
            $count = Order::where('status', $status)
                ->whereDate('created_at', Carbon::today())
                ->count();
            
            $data[] = [
                'status' => ucfirst($status),
                'count' => $count,
            ];
        }
        
        return $data;
    }

    /**
     * Get real-time stats for AJAX requests
     */
    public function stats()
    {
        return response()->json([
            'stats' => $this->getDashboardStats(),
            'recent_orders' => $this->getRecentOrders(),
        ]);
    }
}
