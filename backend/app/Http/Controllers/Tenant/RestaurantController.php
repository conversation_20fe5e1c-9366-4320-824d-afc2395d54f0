<?php

namespace App\Http\Controllers\Tenant;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Inertia\Inertia;
use Illuminate\Support\Facades\Storage;
use Illuminate\Validation\Rule;

class RestaurantController extends Controller
{
    public function __construct()
    {
        $this->middleware(['role:admin|restaurant_manager']);
    }

    /**
     * Display restaurant profile and settings
     */
    public function index()
    {
        $tenant = tenant();
        
        return Inertia::render('Tenant/Restaurant/Profile', [
            'restaurant' => [
                'id' => $tenant->id,
                'name' => $tenant->name,
                'email' => $tenant->email,
                'phone' => $tenant->phone,
                'address' => $tenant->address ?? '',
                'city' => $tenant->city ?? '',
                'state' => $tenant->state ?? '',
                'postal_code' => $tenant->postal_code ?? '',
                'country' => $tenant->country ?? '',
                'website' => $tenant->website ?? '',
                'description' => $tenant->description ?? '',
                'logo' => $tenant->logo ? Storage::url($tenant->logo) : null,
                'cover_image' => $tenant->cover_image ? Storage::url($tenant->cover_image) : null,
                'cuisine_type' => $tenant->cuisine_type ?? '',
                'opening_hours' => $tenant->opening_hours ?? [],
                'delivery_radius' => $tenant->delivery_radius ?? 5,
                'minimum_order' => $tenant->minimum_order ?? 0,
                'delivery_fee' => $tenant->delivery_fee ?? 0,
                'tax_rate' => $tenant->tax_rate ?? 0,
                'service_charge' => $tenant->service_charge ?? 0,
                'currency' => $tenant->currency ?? 'USD',
                'timezone' => $tenant->timezone ?? 'UTC',
                'is_active' => $tenant->is_active ?? true,
                'accepts_online_orders' => $tenant->accepts_online_orders ?? true,
                'accepts_reservations' => $tenant->accepts_reservations ?? true,
                'social_media' => $tenant->social_media ?? [],
            ],
            'subscription' => [
                'plan_name' => $tenant->subscriptionPlan->name ?? 'No Plan',
                'status' => $tenant->subscription_status,
                'trial_ends_at' => $tenant->trial_ends_at,
                'next_billing_date' => $tenant->next_billing_date,
            ],
        ]);
    }

    /**
     * Update restaurant profile
     */
    public function updateProfile(Request $request)
    {
        $tenant = tenant();

        $request->validate([
            'name' => 'required|string|max:255',
            'email' => ['required', 'email', Rule::unique('tenants')->ignore($tenant->id)],
            'phone' => 'nullable|string|max:20',
            'address' => 'nullable|string|max:500',
            'city' => 'nullable|string|max:100',
            'state' => 'nullable|string|max:100',
            'postal_code' => 'nullable|string|max:20',
            'country' => 'nullable|string|max:100',
            'website' => 'nullable|url|max:255',
            'description' => 'nullable|string|max:1000',
            'cuisine_type' => 'nullable|string|max:100',
            'logo' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
            'cover_image' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:5120',
        ]);

        $data = $request->only([
            'name', 'email', 'phone', 'address', 'city', 'state', 
            'postal_code', 'country', 'website', 'description', 'cuisine_type'
        ]);

        // Handle logo upload
        if ($request->hasFile('logo')) {
            if ($tenant->logo) {
                Storage::delete($tenant->logo);
            }
            $data['logo'] = $request->file('logo')->store('restaurants/logos', 'public');
        }

        // Handle cover image upload
        if ($request->hasFile('cover_image')) {
            if ($tenant->cover_image) {
                Storage::delete($tenant->cover_image);
            }
            $data['cover_image'] = $request->file('cover_image')->store('restaurants/covers', 'public');
        }

        $tenant->update($data);

        return back()->with('success', 'Restaurant profile updated successfully.');
    }

    /**
     * Update restaurant settings
     */
    public function updateSettings(Request $request)
    {
        $tenant = tenant();

        $request->validate([
            'opening_hours' => 'nullable|array',
            'opening_hours.*.day' => 'required|string|in:monday,tuesday,wednesday,thursday,friday,saturday,sunday',
            'opening_hours.*.is_open' => 'boolean',
            'opening_hours.*.open_time' => 'nullable|date_format:H:i',
            'opening_hours.*.close_time' => 'nullable|date_format:H:i',
            'delivery_radius' => 'nullable|numeric|min:0|max:100',
            'minimum_order' => 'nullable|numeric|min:0',
            'delivery_fee' => 'nullable|numeric|min:0',
            'tax_rate' => 'nullable|numeric|min:0|max:100',
            'service_charge' => 'nullable|numeric|min:0|max:100',
            'currency' => 'nullable|string|size:3',
            'timezone' => 'nullable|string|max:50',
            'accepts_online_orders' => 'boolean',
            'accepts_reservations' => 'boolean',
            'social_media' => 'nullable|array',
            'social_media.facebook' => 'nullable|url',
            'social_media.instagram' => 'nullable|url',
            'social_media.twitter' => 'nullable|url',
            'social_media.youtube' => 'nullable|url',
        ]);

        $tenant->update($request->only([
            'opening_hours', 'delivery_radius', 'minimum_order', 'delivery_fee',
            'tax_rate', 'service_charge', 'currency', 'timezone',
            'accepts_online_orders', 'accepts_reservations', 'social_media'
        ]));

        return back()->with('success', 'Restaurant settings updated successfully.');
    }

    /**
     * Update restaurant status
     */
    public function updateStatus(Request $request)
    {
        $request->validate([
            'is_active' => 'required|boolean',
        ]);

        $tenant = tenant();
        $tenant->update(['is_active' => $request->is_active]);

        $status = $request->is_active ? 'activated' : 'deactivated';
        
        return response()->json([
            'success' => true,
            'message' => "Restaurant {$status} successfully.",
            'is_active' => $tenant->is_active,
        ]);
    }

    /**
     * Get restaurant analytics
     */
    public function analytics()
    {
        $tenant = tenant();
        
        // This would typically fetch from orders, customers, etc.
        // For now, returning mock data structure
        $analytics = [
            'overview' => [
                'total_orders' => 0, // Order::count()
                'total_revenue' => 0, // Order::sum('total')
                'total_customers' => 0, // Customer::count()
                'average_order_value' => 0,
            ],
            'monthly_revenue' => [],
            'popular_items' => [],
            'peak_hours' => [],
            'customer_satisfaction' => 0,
        ];

        return response()->json($analytics);
    }

    /**
     * Export restaurant data
     */
    public function exportData(Request $request)
    {
        $request->validate([
            'type' => 'required|in:orders,customers,menu,all',
            'format' => 'required|in:csv,excel,pdf',
            'date_from' => 'nullable|date',
            'date_to' => 'nullable|date|after_or_equal:date_from',
        ]);

        // This would implement actual data export logic
        // For now, return success response
        return response()->json([
            'success' => true,
            'message' => 'Export will be sent to your email shortly.',
        ]);
    }

    /**
     * Get restaurant QR code for menu
     */
    public function getQRCode()
    {
        $tenant = tenant();
        $menuUrl = route('tenant.menu.public', ['tenant' => $tenant->id]);
        
        // Generate QR code (you'd use a QR code library here)
        // For now, return the URL
        return response()->json([
            'qr_code_url' => $menuUrl,
            'menu_url' => $menuUrl,
        ]);
    }

    /**
     * Update restaurant branding
     */
    public function updateBranding(Request $request)
    {
        $request->validate([
            'primary_color' => 'nullable|string|regex:/^#[a-fA-F0-9]{6}$/',
            'secondary_color' => 'nullable|string|regex:/^#[a-fA-F0-9]{6}$/',
            'accent_color' => 'nullable|string|regex:/^#[a-fA-F0-9]{6}$/',
            'font_family' => 'nullable|string|max:100',
            'custom_css' => 'nullable|string|max:5000',
        ]);

        $tenant = tenant();
        $branding = $tenant->branding ?? [];
        
        $branding = array_merge($branding, $request->only([
            'primary_color', 'secondary_color', 'accent_color', 
            'font_family', 'custom_css'
        ]));

        $tenant->update(['branding' => $branding]);

        return back()->with('success', 'Branding updated successfully.');
    }

    /**
     * Get restaurant statistics
     */
    public function getStats()
    {
        $tenant = tenant();
        
        // Mock statistics - replace with actual queries
        $stats = [
            'orders_today' => 0,
            'revenue_today' => 0,
            'orders_this_month' => 0,
            'revenue_this_month' => 0,
            'total_menu_items' => 0,
            'total_tables' => 0,
            'total_staff' => 0,
            'average_rating' => 0,
            'total_reviews' => 0,
        ];

        return response()->json($stats);
    }
}
