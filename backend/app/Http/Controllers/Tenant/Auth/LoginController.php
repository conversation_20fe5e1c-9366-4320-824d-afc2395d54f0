<?php

namespace App\Http\Controllers\Tenant\Auth;

use App\Http\Controllers\Controller;
use App\Models\Tenant\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Log;
use Illuminate\Validation\ValidationException;
use Inertia\Inertia;
use Inertia\Response;
use Illuminate\Http\RedirectResponse;

class LoginController extends Controller
{
    /**
     * Show the login form
     */
    public function showLoginForm(): Response
    {
        return Inertia::render('Tenant/Auth/Login', [
            'tenant' => [
                'name' => tenant('name'),
                'id' => tenant('id'),
            ],
            'canResetPassword' => true,
        ]);
    }

    /**
     * Handle a login request
     */
    public function login(Request $request): RedirectResponse
    {
        $request->validate([
            'email' => 'required|email',
            'password' => 'required|string',
        ]);

        $credentials = $request->only('email', 'password');
        $remember = $request->boolean('remember');

        // Log the login attempt for debugging
        Log::info('Tenant login attempt', [
            'tenant_id' => tenant('id'),
            'email' => $credentials['email'],
            'ip' => $request->ip(),
        ]);

        // Ensure we're in tenant context
        if (!tenant()) {
            Log::error('Login attempt outside tenant context');
            throw ValidationException::withMessages([
                'email' => ['Unable to authenticate. Please try again.'],
            ]);
        }

        // First, check if user exists in tenant database
        $user = User::where('email', $credentials['email'])->first();

        if (!$user) {
            Log::info('User not found in tenant database', [
                'tenant_id' => tenant('id'),
                'email' => $credentials['email'],
            ]);

            throw ValidationException::withMessages([
                'email' => ['These credentials do not match our records.'],
            ]);
        }

        // Check if user is active
        if (!$user->is_active) {
            Log::info('Inactive user login attempt', [
                'tenant_id' => tenant('id'),
                'user_id' => $user->id,
                'email' => $credentials['email'],
            ]);

            throw ValidationException::withMessages([
                'email' => ['Your account has been deactivated. Please contact your manager.'],
            ]);
        }

        // Verify password manually to ensure we're checking against tenant database
        if (!Hash::check($credentials['password'], $user->password)) {
            Log::info('Invalid password for tenant user', [
                'tenant_id' => tenant('id'),
                'user_id' => $user->id,
                'email' => $credentials['email'],
            ]);

            throw ValidationException::withMessages([
                'email' => ['These credentials do not match our records.'],
            ]);
        }

        // Check if user has appropriate role for restaurant access
        $allowedRoles = ['restaurant_manager', 'manager', 'waiter', 'chef', 'rider', 'cashier'];
        if (!in_array($user->role, $allowedRoles)) {
            Log::warning('User with invalid role attempted login', [
                'tenant_id' => tenant('id'),
                'user_id' => $user->id,
                'role' => $user->role,
            ]);

            throw ValidationException::withMessages([
                'email' => ['You do not have permission to access this restaurant.'],
            ]);
        }

        // Auto-verify email for tenant users if not already verified
        if (!$user->hasVerifiedEmail()) {
            $user->markEmailAsVerified();
            Log::info('Auto-verified email for tenant user', [
                'tenant_id' => tenant('id'),
                'user_id' => $user->id,
                'email' => $user->email,
            ]);
        }

        // Manually log the user in using the tenant guard
        Auth::guard('tenant')->login($user, $remember);
        $request->session()->regenerate();

        // Update last login timestamp
        $user->updateLastLogin();

        Log::info('Successful tenant login', [
            'tenant_id' => tenant('id'),
            'user_id' => $user->id,
            'role' => $user->role,
        ]);

        // Get dashboard route from user model
        $dashboardRoute = $user->getDashboardRoute();
        // dd($dashboardRoute);
        return redirect()->intended($dashboardRoute);
    }

    /**
     * Log the user out
     */
    public function logout(Request $request): RedirectResponse
    {
        $user = Auth::guard('tenant')->user();

        if ($user) {
            Log::info('Tenant user logout', [
                'tenant_id' => tenant('id'),
                'user_id' => $user->id,
                'email' => $user->email,
            ]);
        }

        Auth::guard('tenant')->logout();

        $request->session()->invalidate();
        $request->session()->regenerateToken();

        return redirect('/login');
    }
}
