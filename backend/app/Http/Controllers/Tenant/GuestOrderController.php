<?php

namespace App\Http\Controllers\Tenant;

use App\Http\Controllers\Controller;
use App\Models\Tenant\Order;
use App\Models\Tenant\OrderItem;
use App\Models\Tenant\Customer;
use App\Notifications\NewOrderNotification;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Str;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Notification;

class GuestOrderController extends Controller
{
    /**
     * Store a new guest order
     */
    public function store(Request $request)
    {
        $request->validate([
            'customer_name' => 'required|string|max:255',
            'customer_phone' => 'required|string|max:20',
            'customer_email' => 'nullable|email|max:255',
            'delivery_address' => 'required|string|max:500',
            'special_instructions' => 'nullable|string|max:1000',
            'payment_method' => 'required|in:cash_on_delivery,bkash,nagad',
            'location_enabled' => 'boolean',
            'delivery_latitude' => 'nullable|numeric|between:-90,90',
            'delivery_longitude' => 'nullable|numeric|between:-180,180',
            'delivery_formatted_address' => 'nullable|string|max:500',
            'items' => 'required|array|min:1',
            'items.*.menuItemId' => 'required|exists:menu_items,id',
            'items.*.quantity' => 'required|integer|min:1',
            'items.*.basePrice' => 'required|numeric|min:0',
            'items.*.totalPrice' => 'required|numeric|min:0',
            'items.*.variations' => 'nullable|array',
            'items.*.addons' => 'nullable|array',
            'items.*.specialInstructions' => 'nullable|string|max:500',
        ]);

        DB::beginTransaction();

        try {
            // Create or find customer
            $customer = Customer::firstOrCreate(
                ['phone' => $request->customer_phone],
                [
                    'name' => $request->customer_name,
                    'email' => $request->customer_email,
                ]
            );

            // Update customer info if provided
            if ($request->customer_email && !$customer->email) {
                $customer->update(['email' => $request->customer_email]);
            }

            // Generate order number
            $orderNumber = $this->generateOrderNumber();

            // Calculate totals
            $subtotal = collect($request->items)->sum('totalPrice');
            $taxRate = 10; // 10% tax rate (you can get this from restaurant settings)
            $taxAmount = $subtotal * ($taxRate / 100);
            $deliveryFee = 50; // Default delivery fee (you can calculate based on distance)
            $total = $subtotal + $taxAmount + $deliveryFee;

            // Create order
            $order = Order::create([
                'order_number' => $orderNumber,
                'customer_id' => $customer->id,
                'order_type' => 'delivery',
                'status' => 'pending',
                'payment_method' => $request->payment_method,
                'payment_status' => 'pending',
                'delivery_address' => $request->delivery_address,
                'delivery_latitude' => $request->delivery_latitude,
                'delivery_longitude' => $request->delivery_longitude,
                'delivery_formatted_address' => $request->delivery_formatted_address,
                'location_enabled' => $request->location_enabled ?? false,
                'special_instructions' => $request->special_instructions,
                'subtotal' => $subtotal,
                'tax_amount' => $taxAmount,
                'delivery_fee' => $deliveryFee,
                'total_amount' => $total,
                'customer_name' => $request->customer_name,
                'customer_phone' => $request->customer_phone,
                'customer_email' => $request->customer_email,
            ]);

            // Create order items
            foreach ($request->items as $itemData) {
                OrderItem::create([
                    'order_id' => $order->id,
                    'menu_item_id' => $itemData['menuItemId'],
                    'quantity' => $itemData['quantity'],
                    'unit_price' => $itemData['basePrice'],
                    'total_price' => $itemData['totalPrice'],
                    'customizations' => [
                        'variations' => $itemData['variations'] ?? [],
                        'addons' => $itemData['addons'] ?? [],
                    ],
                    'special_instructions' => $itemData['specialInstructions'] ?? null,
                ]);
            }

            // Send notification to managers
            $this->notifyManagers($order);

            DB::commit();

            return response()->json([
                'success' => true,
                'message' => 'Order placed successfully!',
                'order' => $order->load(['customer', 'items'])
            ]);

        } catch (\Exception $e) {
            DB::rollback();

            return response()->json([
                'success' => false,
                'message' => 'Failed to place order. Please try again.',
                'error' => $e->getMessage()
            ], 422);
        }
    }

    /**
     * Generate unique order number
     */
    private function generateOrderNumber(): string
    {
        do {
            $orderNumber = 'ORD-' . date('Ymd') . '-' . strtoupper(Str::random(6));
        } while (Order::where('order_number', $orderNumber)->exists());

        return $orderNumber;
    }

    /**
     * Send notification to restaurant managers
     */
    private function notifyManagers(Order $order): void
    {
        $managers = User::role('restaurant_manager')->get();

        if ($managers->count() > 0) {
            Notification::send($managers, new NewOrderNotification($order));
        }
    }
}
