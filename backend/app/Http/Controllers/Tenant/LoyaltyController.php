<?php

namespace App\Http\Controllers\Tenant;

use App\Http\Controllers\Controller;
use App\Models\Tenant\LoyaltySettings;
use App\Models\Tenant\LoyaltyAccount;
use App\Models\Tenant\LoyaltyTransaction;
use App\Services\Tenant\LoyaltyService;
use Illuminate\Http\Request;
use Inertia\Inertia;
use Illuminate\Support\Facades\DB;

class LoyaltyController extends Controller
{
    protected LoyaltyService $loyaltyService;

    public function __construct(LoyaltyService $loyaltyService)
    {
        $this->loyaltyService = $loyaltyService;
        // $this->middleware(['role:Manager']);
    }

    /**
     * Display loyalty program settings
     */
    public function settings()
    {
        $settings = LoyaltySettings::getInstance();
        $statistics = $this->loyaltyService->getLoyaltyStatistics();

        return Inertia::render('Tenant/Loyalty/Settings', [
            'settings' => $settings,
            'statistics' => $statistics,
        ]);
    }

    /**
     * Update loyalty program settings
     */
    public function updateSettings(Request $request)
    {
        $request->validate([
            'enabled' => 'required|boolean',
            'points_per_dollar' => 'required|numeric|min:0|max:100',
            'points_for_dollar_discount' => 'required|integer|min:1|max:1000',
            'max_discount_percentage' => 'required|numeric|min:0|max:100',
            'minimum_order_amount' => 'required|numeric|min:0',
            'points_expiry_days' => 'nullable|integer|min:1|max:3650',
            'birthday_bonus_enabled' => 'required|boolean',
            'birthday_bonus_points' => 'required_if:birthday_bonus_enabled,true|integer|min:0|max:10000',
            'referral_program_enabled' => 'required|boolean',
            'referral_bonus_points' => 'required_if:referral_program_enabled,true|integer|min:0|max:10000',
        ]);

        $settings = LoyaltySettings::getInstance();
        $settings->update($request->all());

        return back()->with('success', 'Loyalty program settings updated successfully.');
    }

    /**
     * Display loyalty accounts management
     */
    public function accounts(Request $request)
    {
        $query = LoyaltyAccount::query();

        // Search functionality
        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->where('phone_number', 'like', "%{$search}%")
                  ->orWhere('customer_name', 'like', "%{$search}%")
                  ->orWhere('email', 'like', "%{$search}%");
            });
        }

        // Filter by tier
        if ($request->filled('tier')) {
            $query->where('tier', $request->tier);
        }

        // Filter by status
        if ($request->filled('status')) {
            $query->where('is_active', $request->status === 'active');
        }

        // Sort
        $sortField = $request->get('sort', 'created_at');
        $sortDirection = $request->get('direction', 'desc');
        $query->orderBy($sortField, $sortDirection);

        $accounts = $query->paginate(20)->withQueryString();

        return Inertia::render('Tenant/Loyalty/Accounts', [
            'accounts' => $accounts,
            'filters' => $request->only(['search', 'tier', 'status', 'sort', 'direction']),
            'tiers' => ['regular', 'bronze', 'silver', 'gold', 'platinum'],
        ]);
    }

    /**
     * Show loyalty account details
     */
    public function showAccount(LoyaltyAccount $account)
    {
        $account->load(['transactions' => function ($query) {
            $query->orderBy('created_at', 'desc')->limit(50);
        }]);

        return Inertia::render('Tenant/Loyalty/AccountDetails', [
            'account' => $account,
            'recentTransactions' => $account->transactions,
        ]);
    }

    /**
     * Manual points adjustment
     */
    public function adjustPoints(Request $request, LoyaltyAccount $account)
    {
        $request->validate([
            'points' => 'required|integer|not_in:0',
            'reason' => 'required|string|max:255',
        ]);

        try {
            $this->loyaltyService->manualPointsAdjustment(
                $account->phone_number,
                $request->points,
                $request->reason,
                auth()->id()
            );

            return back()->with('success', 'Points adjustment completed successfully.');
        } catch (\Exception $e) {
            return back()->withErrors(['error' => $e->getMessage()]);
        }
    }

    /**
     * Lookup loyalty account by phone
     */
    public function lookupByPhone(Request $request)
    {
        $request->validate([
            'phone_number' => 'required|string',
        ]);

        $account = $this->loyaltyService->getAccountByPhone($request->phone_number);

        if (!$account) {
            return response()->json([
                'found' => false,
                'message' => 'No loyalty account found for this phone number.',
            ]);
        }

        return response()->json([
            'found' => true,
            'account' => [
                'id' => $account->id,
                'phone_number' => $account->phone_number,
                'customer_name' => $account->customer_name,
                'total_points' => $account->total_points,
                'tier' => $account->tier,
                'tier_color' => $account->tier_color,
            ],
        ]);
    }

    /**
     * Calculate potential discount
     */
    public function calculateDiscount(Request $request)
    {
        $request->validate([
            'phone_number' => 'required|string',
            'points_to_use' => 'required|integer|min:1',
            'order_amount' => 'required|numeric|min:0.01',
        ]);

        try {
            $account = $this->loyaltyService->getAccountByPhone($request->phone_number);
            
            if (!$account) {
                return response()->json(['error' => 'Loyalty account not found'], 404);
            }

            if ($request->points_to_use > $account->total_points) {
                return response()->json(['error' => 'Insufficient points balance'], 400);
            }

            $discountInfo = $this->loyaltyService->calculatePotentialDiscount(
                $request->points_to_use,
                $request->order_amount
            );

            return response()->json([
                'success' => true,
                'discount_info' => $discountInfo,
                'account_balance' => $account->total_points,
            ]);
        } catch (\Exception $e) {
            return response()->json(['error' => $e->getMessage()], 500);
        }
    }

    /**
     * Export loyalty accounts data
     */
    public function exportAccounts(Request $request)
    {
        $query = LoyaltyAccount::query();

        // Apply same filters as accounts method
        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->where('phone_number', 'like', "%{$search}%")
                  ->orWhere('customer_name', 'like', "%{$search}%")
                  ->orWhere('email', 'like', "%{$search}%");
            });
        }

        if ($request->filled('tier')) {
            $query->where('tier', $request->tier);
        }

        if ($request->filled('status')) {
            $query->where('is_active', $request->status === 'active');
        }

        $accounts = $query->orderBy('created_at', 'desc')->get();

        $csvData = [];
        $csvData[] = [
            'Phone Number',
            'Customer Name',
            'Email',
            'Address',
            'Total Points',
            'Tier',
            'Total Spent',
            'Total Orders',
            'Last Transaction',
            'Status',
            'Created At',
        ];

        foreach ($accounts as $account) {
            $csvData[] = [
                $account->phone_number,
                $account->customer_name,
                $account->email ?? '',
                $account->address ?? '',
                $account->total_points,
                ucfirst($account->tier),
                '$' . number_format($account->total_spent, 2),
                $account->total_orders,
                $account->last_transaction_at ? $account->last_transaction_at->format('Y-m-d H:i:s') : '',
                $account->is_active ? 'Active' : 'Inactive',
                $account->created_at->format('Y-m-d H:i:s'),
            ];
        }

        $filename = 'loyalty_accounts_' . now()->format('Y-m-d_H-i-s') . '.csv';
        
        $headers = [
            'Content-Type' => 'text/csv',
            'Content-Disposition' => "attachment; filename=\"{$filename}\"",
        ];

        $callback = function () use ($csvData) {
            $file = fopen('php://output', 'w');
            foreach ($csvData as $row) {
                fputcsv($file, $row);
            }
            fclose($file);
        };

        return response()->stream($callback, 200, $headers);
    }

    /**
     * Get loyalty transactions for account
     */
    public function getTransactions(LoyaltyAccount $account, Request $request)
    {
        $query = $account->transactions();

        if ($request->filled('type')) {
            $query->where('transaction_type', $request->type);
        }

        $transactions = $query->orderBy('created_at', 'desc')
            ->paginate(20)
            ->withQueryString();

        return response()->json($transactions);
    }
}
