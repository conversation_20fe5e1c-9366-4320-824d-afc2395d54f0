<?php

namespace App\Http\Controllers\Tenant;

use App\Http\Controllers\Controller;
use App\Models\Tenant\Rider;
use App\Models\Tenant\Order;
use Illuminate\Http\Request;
use Inertia\Inertia;
use Illuminate\Support\Facades\Auth;

class RiderDashboardController extends Controller
{
    public function __construct()
    {
        $this->middleware(['auth', 'role:Rider']);
    }

    /**
     * Display the rider dashboard
     */
    public function index()
    {
        $user = Auth::user();
        $rider = Rider::where('user_id', $user->id)->first();

        if (!$rider) {
            return redirect()->route('dashboard')
                ->with('error', 'You are not registered as a delivery rider.');
        }

        // Get assigned orders
        $assignedOrders = $rider->activeOrders()
            ->with(['customer', 'items.menuItem'])
            ->orderBy('created_at')
            ->get();

        // Get today's statistics
        $todayStats = [
            'deliveries_completed' => $rider->todayOrders()->count(),
            'earnings' => $rider->getEarningsForPeriod('today'),
            'average_rating' => $rider->average_rating,
            'active_orders' => $rider->current_delivery_count,
        ];

        // Get recent order history
        $recentOrders = $rider->orders()
            ->where('delivery_status', 'delivered')
            ->with(['customer'])
            ->orderBy('delivery_time', 'desc')
            ->limit(10)
            ->get();

        return Inertia::render('Tenant/Rider/Dashboard', [
            'rider' => $rider,
            'assignedOrders' => $assignedOrders,
            'todayStats' => $todayStats,
            'recentOrders' => $recentOrders,
        ]);
    }

    /**
     * Show rider profile
     */
    public function profile()
    {
        $user = Auth::user();
        $rider = Rider::where('user_id', $user->id)->first();

        if (!$rider) {
            return redirect()->route('dashboard')
                ->with('error', 'You are not registered as a delivery rider.');
        }

        return Inertia::render('Tenant/Rider/Profile', [
            'rider' => $rider,
        ]);
    }

    /**
     * Update rider profile
     */
    public function updateProfile(Request $request)
    {
        $user = Auth::user();
        $rider = Rider::where('user_id', $user->id)->first();

        if (!$rider) {
            return back()->withErrors(['error' => 'Rider profile not found.']);
        }

        $request->validate([
            'phone' => 'required|string|max:20|unique:riders,phone,' . $rider->id,
            'email' => 'nullable|email|unique:riders,email,' . $rider->id,
            'address' => 'nullable|string|max:500',
            'emergency_contact_name' => 'nullable|string|max:255',
            'emergency_contact_phone' => 'nullable|string|max:20',
        ]);

        $rider->update([
            'phone' => $request->phone,
            'email' => $request->email,
            'address' => $request->address,
            'emergency_contact_name' => $request->emergency_contact_name,
            'emergency_contact_phone' => $request->emergency_contact_phone,
        ]);

        // Update user email if changed
        if ($request->email && $request->email !== $user->email) {
            $user->update(['email' => $request->email]);
        }

        return back()->with('success', 'Profile updated successfully.');
    }

    /**
     * Update delivery status
     */
    public function updateDeliveryStatus(Request $request)
    {
        $user = Auth::user();
        $rider = Rider::where('user_id', $user->id)->first();

        if (!$rider) {
            return response()->json(['error' => 'Rider not found'], 404);
        }

        $request->validate([
            'delivery_status' => 'required|in:available,busy,offline,on_break',
        ]);

        $rider->update([
            'delivery_status' => $request->delivery_status,
            'last_active_at' => now(),
        ]);

        return response()->json([
            'success' => true,
            'message' => 'Status updated successfully',
            'delivery_status' => $rider->delivery_status,
        ]);
    }

    /**
     * Update location
     */
    public function updateLocation(Request $request)
    {
        $user = Auth::user();
        $rider = Rider::where('user_id', $user->id)->first();

        if (!$rider) {
            return response()->json(['error' => 'Rider not found'], 404);
        }

        $request->validate([
            'latitude' => 'required|numeric|between:-90,90',
            'longitude' => 'required|numeric|between:-180,180',
        ]);

        $rider->updateLocation($request->latitude, $request->longitude);

        return response()->json([
            'success' => true,
            'message' => 'Location updated successfully',
        ]);
    }

    /**
     * Accept order
     */
    public function acceptOrder(Request $request, Order $order)
    {
        $user = Auth::user();
        $rider = Rider::where('user_id', $user->id)->first();

        if (!$rider) {
            return response()->json(['error' => 'Rider not found'], 404);
        }

        if (!$rider->isAvailable()) {
            return response()->json(['error' => 'You are not available to accept orders'], 422);
        }

        if ($order->delivery_status !== 'pending') {
            return response()->json(['error' => 'This order is no longer available'], 422);
        }

        $rider->assignOrder($order);

        return response()->json([
            'success' => true,
            'message' => 'Order accepted successfully',
            'order' => $order->fresh(['customer', 'items.menuItem']),
        ]);
    }

    /**
     * Update order status
     */
    public function updateOrderStatus(Request $request, Order $order)
    {
        $user = Auth::user();
        $rider = Rider::where('user_id', $user->id)->first();

        if (!$rider || $order->delivery_rider_id !== $rider->id) {
            return response()->json(['error' => 'Unauthorized'], 403);
        }

        $request->validate([
            'status' => 'required|in:picked_up,en_route,delivered',
            'notes' => 'nullable|string|max:500',
            'latitude' => 'nullable|numeric|between:-90,90',
            'longitude' => 'nullable|numeric|between:-180,180',
        ]);

        $status = $request->status;
        $notes = $request->notes;

        // Update order status
        $order->update([
            'delivery_status' => $status,
            'delivery_notes' => $notes,
        ]);

        // Log the action
        $rider->orderHistory()->create([
            'order_id' => $order->id,
            'action' => $status,
            'action_time' => now(),
            'latitude' => $request->latitude,
            'longitude' => $request->longitude,
            'notes' => $notes,
        ]);

        // Handle specific status updates
        switch ($status) {
            case 'picked_up':
                $order->update(['pickup_time' => now()]);
                break;
            case 'delivered':
                $rider->completeDelivery($order);
                break;
        }

        return response()->json([
            'success' => true,
            'message' => 'Order status updated successfully',
            'order' => $order->fresh(['customer', 'items.menuItem']),
        ]);
    }

    /**
     * Get order history
     */
    public function orderHistory(Request $request)
    {
        $user = Auth::user();
        $rider = Rider::where('user_id', $user->id)->first();

        if (!$rider) {
            return response()->json(['error' => 'Rider not found'], 404);
        }

        $query = $rider->orders()->with(['customer']);

        // Filter by status
        if ($request->filled('status')) {
            $query->where('delivery_status', $request->status);
        }

        // Filter by date range
        if ($request->filled('start_date')) {
            $query->whereDate('created_at', '>=', $request->start_date);
        }

        if ($request->filled('end_date')) {
            $query->whereDate('created_at', '<=', $request->end_date);
        }

        $orders = $query->orderBy('created_at', 'desc')
                       ->paginate(20);

        return response()->json($orders);
    }

    /**
     * Get earnings summary
     */
    public function earnings(Request $request)
    {
        $user = Auth::user();
        $rider = Rider::where('user_id', $user->id)->first();

        if (!$rider) {
            return response()->json(['error' => 'Rider not found'], 404);
        }

        $period = $request->get('period', 'today');

        $earnings = [
            'today' => $rider->getEarningsForPeriod('today'),
            'week' => $rider->getEarningsForPeriod('week'),
            'month' => $rider->getEarningsForPeriod('month'),
            'total' => $rider->total_earnings,
        ];

        // Get detailed breakdown for the requested period
        $query = $rider->orders()->where('delivery_status', 'delivered');

        switch ($period) {
            case 'today':
                $query->whereDate('delivery_time', today());
                break;
            case 'week':
                $query->whereBetween('delivery_time', [now()->startOfWeek(), now()->endOfWeek()]);
                break;
            case 'month':
                $query->whereMonth('delivery_time', now()->month)
                      ->whereYear('delivery_time', now()->year);
                break;
        }

        $detailedEarnings = $query->select(['id', 'order_number', 'delivery_time', 'rider_commission', 'customer_name'])
                                 ->orderBy('delivery_time', 'desc')
                                 ->get();

        return response()->json([
            'summary' => $earnings,
            'detailed' => $detailedEarnings,
        ]);
    }

    /**
     * Get performance statistics
     */
    public function statistics(Request $request)
    {
        $user = Auth::user();
        $rider = Rider::where('user_id', $user->id)->first();

        if (!$rider) {
            return response()->json(['error' => 'Rider not found'], 404);
        }

        $period = $request->get('period', 'month');

        $query = $rider->orders()->where('delivery_status', 'delivered');

        switch ($period) {
            case 'today':
                $query->whereDate('delivery_time', today());
                break;
            case 'week':
                $query->whereBetween('delivery_time', [now()->startOfWeek(), now()->endOfWeek()]);
                break;
            case 'month':
                $query->whereMonth('delivery_time', now()->month)
                      ->whereYear('delivery_time', now()->year);
                break;
        }

        $orders = $query->get();

        $stats = [
            'total_deliveries' => $orders->count(),
            'total_earnings' => $orders->sum('rider_commission'),
            'average_delivery_time' => $orders->avg('actual_delivery_time'),
            'average_rating' => $rider->average_rating,
            'rating_count' => $rider->rating_count,
            'on_time_percentage' => $orders->count() > 0 
                ? ($orders->where('actual_delivery_time', '<=', 'estimated_delivery_time')->count() / $orders->count()) * 100 
                : 0,
        ];

        return response()->json($stats);
    }
}
