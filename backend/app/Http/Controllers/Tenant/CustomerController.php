<?php

namespace App\Http\Controllers\Tenant;

use App\Http\Controllers\Controller;
use App\Models\Tenant\Customer;
use Illuminate\Http\Request;
use Inertia\Inertia;
use Inertia\Response;

class CustomerController extends Controller
{
    public function __construct()
    {
        $this->middleware('auth');
        $this->middleware('verified');
        // Note: Role middleware should be applied in routes, not controller constructor
    }

    /**
     * Display a listing of customers
     */
    public function index(Request $request): Response
    {
        $query = Customer::with(['addresses', 'orders'])
                        ->withCount(['orders', 'addresses']);

        // Apply filters
        if ($request->filled('search')) {
            $query->search($request->search);
        }

        if ($request->filled('tier')) {
            $query->where('tier', $request->tier);
        }

        // if ($request->filled('status')) {
        //     if ($request->status === 'active') {
        //         $query->active();
        //     } elseif ($request->status === 'blocked') {
        //         $query->blocked();
        //     }
        // }

        // Apply sorting
        $sortField = $request->get('sort', 'created_at');
        $sortDirection = $request->get('direction', 'desc');
        $query->orderBy($sortField, $sortDirection);

        $customers = $query->paginate(15)->withQueryString();

        // Get statistics
        $stats = [
            'total_customers' => Customer::count(),
            'active_customers' => Customer::count(),
            'blocked_customers' => Customer::count(),
            'vip_customers' => Customer::count(),
            'total_orders' => Customer::sum('total_orders'),
            'total_revenue' => Customer::sum('total_spent'),
        ];

        return Inertia::render('Tenant/Customers/Index', [
            'customers' => $customers,
            'stats' => $stats,
            'filters' => $request->only(['search', 'tier', 'status', 'sort', 'direction']),
            'tiers' => [
                'regular' => 'Regular',
                'bronze' => 'Bronze',
                'silver' => 'Silver',
                'gold' => 'Gold',
                'platinum' => 'Platinum',
                'vip' => 'VIP',
            ],
        ]);
    }

    /**
     * Show the form for creating a new customer
     */
    public function create(): Response
    {
        return Inertia::render('Tenant/Customers/Create');
    }

    /**
     * Store a newly created customer
     */
    public function store(Request $request)
    {
        // Custom validation: at least one of name, phone, or email must be provided
        $request->validate([
            'name' => 'nullable|string|max:255',
            'email' => 'nullable|email|unique:customers,email',
            'phone' => 'nullable|string|max:20',
            'address' => 'nullable|string|max:500',
            'date_of_birth' => 'nullable|date|before:today',
            'tier' => 'nullable|in:regular,silver,gold,vip',
            'notes' => 'nullable|string|max:1000',
        ]);

        // Ensure at least one of name, phone, or email is provided
        if (!$request->name && !$request->phone && !$request->email) {
            return back()->withErrors([
                'general' => 'At least one of Name, Phone, or Email must be provided.'
            ])->withInput();
        }

        $customer = Customer::create($request->only([
            'name', 'email', 'phone', 'address', 'date_of_birth', 'tier', 'notes'
        ]));

        // For regular form submission, redirect with flash data
        return redirect()->route('customers.show', $customer)
                        ->with('success', 'Customer created successfully.');
    }

    /**
     * Store a newly created customer via API (for POS modal)
     */
    public function storeApi(Request $request)
    {
        // Custom validation: at least one of name, phone, or email must be provided
        $request->validate([
            'name' => 'nullable|string|max:255',
            'email' => 'nullable|email|unique:customers,email',
            'phone' => 'nullable|string|max:20',
            'address' => 'nullable|string|max:500',
            'date_of_birth' => 'nullable|date|before:today',
            'tier' => 'nullable|in:regular,silver,gold,vip',
            'notes' => 'nullable|string|max:1000',
        ]);

        // Ensure at least one of name, phone, or email is provided
        if (!$request->name && !$request->phone && !$request->email) {
            return response()->json([
                'success' => false,
                'errors' => [
                    'general' => 'At least one of Name, Phone, or Email must be provided.'
                ]
            ], 422);
        }

        $customer = Customer::create($request->only([
            'name', 'email', 'phone', 'address', 'date_of_birth', 'tier', 'notes'
        ]));

        return response()->json([
            'success' => true,
            'customer' => $customer,
            'message' => 'Customer created successfully.'
        ]);
    }

    /**
     * Display the specified customer
     */
    public function show(Customer $customer): Response
    {
        $customer->load([
            'addresses',
            'orders' => function ($query) {
                $query->latest()->limit(10);
            },
            'tableReservations' => function ($query) {
                $query->latest()->limit(5);
            }
        ]);

        // Get customer statistics
        $stats = [
            'total_orders' => $customer->total_orders,
            'total_spent' => $customer->total_spent,
            'average_order_value' => $customer->total_orders > 0 ? $customer->total_spent / $customer->total_orders : 0,
            'loyalty_points' => $customer->loyalty_points,
            'addresses_count' => $customer->addresses->count(),
            'last_order_days_ago' => $customer->last_order_at ? $customer->last_order_at->diffInDays() : null,
        ];

        return Inertia::render('Tenant/Customers/Show', [
            'customer' => $customer,
            'stats' => $stats,
        ]);
    }

    /**
     * Show the form for editing the customer
     */
    public function edit(Customer $customer): Response
    {
        return Inertia::render('Tenant/Customers/Edit', [
            'customer' => $customer,
        ]);
    }

    /**
     * Update the specified customer
     */
    public function update(Request $request, Customer $customer)
    {
        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'email' => 'required|email|unique:customers,email,' . $customer->id,
            'phone' => 'required|string|max:20',
            'date_of_birth' => 'nullable|date|before:today',
            'gender' => 'nullable|in:male,female,other',
            'tier' => 'nullable|in:regular,bronze,silver,gold,platinum,vip',
            'loyalty_points' => 'nullable|integer|min:0',
            'notes' => 'nullable|string|max:1000',
        ]);

        $customer->update($validated);

        return redirect()->route('customers.show', $customer)
                        ->with('success', __('Customer updated successfully.'));
    }

    /**
     * Remove the specified customer
     */
    public function destroy(Customer $customer)
    {
        // Check if customer has orders
        if ($customer->orders()->exists()) {
            return back()->with('error', __('Cannot delete customer with existing orders.'));
        }

        $customer->delete();

        return redirect()->route('customers.index')
                        ->with('success', __('Customer deleted successfully.'));
    }

    /**
     * Toggle customer block status
     */
    public function toggleBlock(Customer $customer)
    {
        $customer->update(['is_blocked' => !$customer->is_blocked]);

        $status = $customer->is_blocked ? 'blocked' : 'unblocked';

        return back()->with('success', __("Customer {$status} successfully."));
    }

    /**
     * Add loyalty points to customer
     */
    public function addLoyaltyPoints(Request $request, Customer $customer)
    {
        $validated = $request->validate([
            'points' => 'required|integer|min:1|max:10000',
            'reason' => 'nullable|string|max:255',
        ]);

        $customer->addLoyaltyPoints($validated['points']);

        return back()->with('success', __('Loyalty points added successfully.'));
    }

    /**
     * Redeem loyalty points for customer
     */
    public function redeemLoyaltyPoints(Request $request, Customer $customer)
    {
        $validated = $request->validate([
            'points' => 'required|integer|min:1|max:' . $customer->loyalty_points,
            'reason' => 'nullable|string|max:255',
        ]);

        if ($customer->redeemLoyaltyPoints($validated['points'])) {
            return back()->with('success', __('Loyalty points redeemed successfully.'));
        }

        return back()->with('error', __('Insufficient loyalty points.'));
    }

    /**
     * Get customer orders
     */
    public function orders(Customer $customer)
    {
        $orders = $customer->orders()
                          ->with(['items', 'table'])
                          ->latest()
                          ->paginate(10);

        return response()->json($orders);
    }

    /**
     * Export customers data
     */
    public function export(Request $request)
    {
        $query = Customer::with(['addresses']);

        // Apply same filters as index
        if ($request->filled('search')) {
            $query->search($request->search);
        }

        if ($request->filled('tier')) {
            $query->where('tier', $request->tier);
        }

        if ($request->filled('status')) {
            if ($request->status === 'active') {
                $query->active();
            } elseif ($request->status === 'blocked') {
                $query->blocked();
            }
        }

        $customers = $query->get();

        // For now, return JSON response - can be enhanced with actual CSV export
        return response()->json([
            'message' => 'Export functionality will be implemented with proper CSV generation',
            'count' => $customers->count(),
            'data' => $customers->take(10) // Sample data
        ]);
    }
}
