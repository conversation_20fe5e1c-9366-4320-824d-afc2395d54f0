<?php

namespace App\Http\Controllers\Tenant;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Inertia\Inertia;
use App\Models\Tenant;
use App\Models\SubscriptionPlan;
use App\Models\TenantSubscription;
use App\Models\SubscriptionUsage;

class SubscriptionController extends Controller
{
    /**
     * Display subscription management page
     */
    public function index()
    {
        $tenant = tenant();

        // Get current subscription
        $currentSubscription = TenantSubscription::where('tenant_id', $tenant->id)
                                                ->with('plan')
                                                ->first();

        // Get all available plans
        $availablePlans = SubscriptionPlan::where('is_active', true)
                                         ->orderBy('sort_order')
                                         ->get();

        // Get current usage
        $currentUsage = SubscriptionUsage::getCurrentPeriodUsage($tenant->id);

        // Get usage statistics from tenant database
        $usageStats = $this->calculateUsageStats($tenant, $currentSubscription);

        return Inertia::render('Tenant/Subscription/Index', [
            'currentSubscription' => $currentSubscription,
            'availablePlans' => $availablePlans,
            'currentUsage' => $currentUsage,
            'usageStats' => $usageStats,
            'tenant' => $tenant,
        ]);
    }

    /**
     * Calculate current usage statistics
     */
    private function calculateUsageStats(Tenant $tenant, ?TenantSubscription $subscription): array
    {
        if (!$subscription || !$subscription->plan) {
            return [];
        }

        $plan = $subscription->plan;

        // Get counts from tenant database
        $menuItemsCount = \App\Models\Tenant\MenuItem::count();
        $pagesCount = \App\Models\Tenant\Page::count();
        $branchesCount = \App\Models\Tenant\Branch::count();
        $staffCount = \App\Models\User::whereHas('roles', function($q) {
            $q->whereIn('name', ['waiter', 'chef', 'manager']);
        })->count();
        $ordersThisMonth = \App\Models\Tenant\Order::whereMonth('created_at', now()->month)
                                                  ->whereYear('created_at', now()->year)
                                                  ->count();

        return [
            'menu_items' => [
                'used' => $menuItemsCount,
                'limit' => $plan->max_menu_items,
                'percentage' => $plan->max_menu_items ? min(100, ($menuItemsCount / $plan->max_menu_items) * 100) : 0,
                'is_unlimited' => $plan->max_menu_items === null,
            ],
            'orders' => [
                'used' => $ordersThisMonth,
                'limit' => $plan->max_orders_per_month,
                'percentage' => $plan->max_orders_per_month ? min(100, ($ordersThisMonth / $plan->max_orders_per_month) * 100) : 0,
                'is_unlimited' => $plan->max_orders_per_month === null,
            ],
            'pages' => [
                'used' => $pagesCount,
                'limit' => $plan->max_pages,
                'percentage' => $plan->max_pages ? min(100, ($pagesCount / $plan->max_pages) * 100) : 0,
                'is_unlimited' => $plan->max_pages === null,
            ],
            'branches' => [
                'used' => $branchesCount,
                'limit' => $plan->max_branches,
                'percentage' => $plan->max_branches ? min(100, ($branchesCount / $plan->max_branches) * 100) : 0,
                'is_unlimited' => $plan->max_branches === null,
            ],
            'staff' => [
                'used' => $staffCount,
                'limit' => $plan->max_staff,
                'percentage' => $plan->max_staff ? min(100, ($staffCount / $plan->max_staff) * 100) : 0,
                'is_unlimited' => $plan->max_staff === null,
            ],
        ];
    }

    /**
     * Show plan comparison
     */
    public function plans()
    {
        $tenant = tenant();
        $currentSubscription = TenantSubscription::where('tenant_id', $tenant->id)
                                                ->with('plan')
                                                ->first();

        $availablePlans = SubscriptionPlan::where('is_active', true)
                                         ->orderBy('sort_order')
                                         ->get();

        return Inertia::render('Tenant/Subscription/Plans', [
            'currentSubscription' => $currentSubscription,
            'availablePlans' => $availablePlans,
            'tenant' => $tenant,
        ]);
    }

    /**
     * Show billing history
     */
    public function billing()
    {
        $tenant = tenant();

        // Get subscription history
        $subscriptions = TenantSubscription::where('tenant_id', $tenant->id)
                                          ->with('plan')
                                          ->orderBy('created_at', 'desc')
                                          ->paginate(20);

        return Inertia::render('Tenant/Subscription/Billing', [
            'subscriptions' => $subscriptions,
            'tenant' => $tenant,
        ]);
    }

    /**
     * Request plan upgrade/downgrade
     */
    public function requestPlanChange(Request $request)
    {
        $request->validate([
            'plan_id' => 'required|exists:subscription_plans,id',
            'reason' => 'nullable|string|max:500',
        ]);

        $newPlan = SubscriptionPlan::findOrFail($request->plan_id);

        // For demo purposes, we'll just create a notification
        // In production, this would integrate with payment processing

        return redirect()->back()->with('success',
            "Plan change request submitted. You will be contacted shortly to complete the upgrade to {$newPlan->name}."
        );
    }
}
