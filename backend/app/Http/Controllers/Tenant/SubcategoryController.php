<?php

namespace App\Http\Controllers\Tenant;

use App\Http\Controllers\Controller;
use App\Models\Tenant\Category;
use App\Models\Tenant\Subcategory;
use Illuminate\Http\Request;
use Illuminate\Support\Str;
use Inertia\Inertia;

class SubcategoryController extends Controller
{
    /**
     * Display a listing of subcategories.
     */
    public function index(Request $request)
    {
        $query = Subcategory::with(['category']);

        // Filter by category if provided
        if ($request->has('category_id') && $request->category_id) {
            $query->where('category_id', $request->category_id);
        }

        // Search functionality
        if ($request->has('search') && $request->search) {
            $query->where('name', 'like', '%' . $request->search . '%');
        }

        // Status filter
        if ($request->has('status') && $request->status !== '') {
            $query->where('is_active', $request->status === 'active');
        }

        $subcategories = $query->ordered()->paginate(15);
        $categories = Category::active()->ordered()->get();

        return Inertia::render('Tenant/Subcategories/Index', [
            'subcategories' => $subcategories,
            'categories' => $categories,
            'filters' => $request->only(['search', 'status', 'category_id']),
        ]);
    }

    /**
     * Show the form for creating a new subcategory.
     */
    public function create()
    {
        $categories = Category::active()->ordered()->get();

        return Inertia::render('Tenant/Subcategories/Create', [
            'categories' => $categories,
        ]);
    }

    /**
     * Store a newly created subcategory.
     */
    public function store(Request $request)
    {
        $request->validate([
            'category_id' => 'required|exists:categories,id',
            'name' => 'required|string|max:255',
            'slug' => 'nullable|string|max:255|unique:subcategories,slug',
            'description' => 'nullable|string',
            'image' => 'nullable|string|max:255',
            'is_active' => 'boolean',
            'sort_order' => 'nullable|integer|min:0',
        ]);

        $subcategory = Subcategory::create([
            'category_id' => $request->category_id,
            'name' => $request->name,
            'slug' => $request->slug ?: Str::slug($request->name),
            'description' => $request->description,
            'image' => $request->image,
            'is_active' => $request->boolean('is_active', true),
            'sort_order' => $request->sort_order ?? 0,
        ]);

        return redirect()->route('subcategories.index')
            ->with('success', 'Subcategory created successfully.');
    }

    /**
     * Display the specified subcategory.
     */
    public function show(Subcategory $subcategory)
    {
        $subcategory->load(['category', 'menuItems' => function ($query) {
            $query->with('media')->active()->ordered();
        }]);

        return Inertia::render('Tenant/Subcategories/Show', [
            'subcategory' => $subcategory,
        ]);
    }

    /**
     * Show the form for editing the specified subcategory.
     */
    public function edit(Subcategory $subcategory)
    {
        $subcategory->load('category');
        $categories = Category::active()->ordered()->get();

        return Inertia::render('Tenant/Subcategories/Edit', [
            'subcategory' => $subcategory,
            'categories' => $categories,
        ]);
    }

    /**
     * Update the specified subcategory.
     */
    public function update(Request $request, Subcategory $subcategory)
    {
        $request->validate([
            'category_id' => 'required|exists:categories,id',
            'name' => 'required|string|max:255',
            'slug' => 'nullable|string|max:255|unique:subcategories,slug,' . $subcategory->id,
            'description' => 'nullable|string',
            'image' => 'nullable|string|max:255',
            'is_active' => 'boolean',
            'sort_order' => 'nullable|integer|min:0',
        ]);

        $subcategory->update([
            'category_id' => $request->category_id,
            'name' => $request->name,
            'slug' => $request->slug ?: Str::slug($request->name),
            'description' => $request->description,
            'image' => $request->image,
            'is_active' => $request->boolean('is_active'),
            'sort_order' => $request->sort_order ?? 0,
        ]);

        return redirect()->route('subcategories.index')
            ->with('success', 'Subcategory updated successfully.');
    }

    /**
     * Remove the specified subcategory.
     */
    public function destroy(Subcategory $subcategory)
    {
        // Check if subcategory has menu items
        if ($subcategory->menuItems()->exists()) {
            return back()->withErrors(['error' => 'Cannot delete subcategory with existing menu items.']);
        }

        $subcategory->delete();

        return redirect()->route('subcategories.index')
            ->with('success', 'Subcategory deleted successfully.');
    }

    /**
     * Get subcategories by category (AJAX)
     */
    public function getByCategory(Request $request, $categoryId)
    {
        $subcategories = Subcategory::where('category_id', $categoryId)
            ->active()
            ->ordered()
            ->get(['id', 'name']);

        return response()->json($subcategories);
    }

    /**
     * Toggle subcategory status.
     */
    public function toggleStatus(Subcategory $subcategory)
    {
        $subcategory->update(['is_active' => !$subcategory->is_active]);

        $status = $subcategory->is_active ? 'activated' : 'deactivated';

        return response()->json([
            'success' => true,
            'message' => "Subcategory {$status} successfully.",
            'is_active' => $subcategory->is_active,
        ]);
    }

    /**
     * Bulk update subcategories.
     */
    public function bulkUpdate(Request $request)
    {
        $request->validate([
            'ids' => 'required|array',
            'ids.*' => 'exists:subcategories,id',
            'action' => 'required|in:activate,deactivate,delete',
        ]);

        $subcategories = Subcategory::whereIn('id', $request->ids);

        switch ($request->action) {
            case 'activate':
                $subcategories->update(['is_active' => true]);
                $message = 'Subcategories activated successfully.';
                break;
            case 'deactivate':
                $subcategories->update(['is_active' => false]);
                $message = 'Subcategories deactivated successfully.';
                break;
            case 'delete':
                // Check if any subcategory has menu items
                $hasMenuItems = $subcategories->whereHas('menuItems')->exists();
                if ($hasMenuItems) {
                    return back()->withErrors(['error' => 'Cannot delete subcategories with existing menu items.']);
                }
                $subcategories->delete();
                $message = 'Subcategories deleted successfully.';
                break;
        }

        return back()->with('success', $message);
    }
}
