<?php

namespace App\Http\Controllers\Tenant\HRM;

use App\Http\Controllers\Controller;
use App\Models\Tenant\Employee;
use App\Models\Tenant\LeaveType;
use App\Models\Tenant\LeaveRequest;
use App\Models\Tenant\EmployeeLeaveEntitlement;
use App\Services\HRM\LeaveService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Inertia\Inertia;
use Inertia\Response;
use Carbon\Carbon;

class LeaveController extends Controller
{
    protected $leaveService;

    public function __construct(LeaveService $leaveService)
    {
        $this->middleware('auth');
        $this->middleware('verified');
        $this->leaveService = $leaveService;
    }

    /**
     * Display a listing of leave requests
     */
    public function index(Request $request): Response
    {
        $query = LeaveRequest::with(['employee', 'leaveType', 'approver', 'rejector'])
                            ->latest();

        // Apply filters
        if ($request->filled('search')) {
            $query->whereHas('employee', function ($q) use ($request) {
                $q->where('first_name', 'like', '%' . $request->search . '%')
                  ->orWhere('last_name', 'like', '%' . $request->search . '%');
            });
        }

        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }

        if ($request->filled('leave_type_id')) {
            $query->where('leave_type_id', $request->leave_type_id);
        }

        if ($request->filled('employee_id')) {
            $query->where('employee_id', $request->employee_id);
        }

        if ($request->filled('date_from')) {
            $query->whereDate('start_date', '>=', $request->date_from);
        }

        if ($request->filled('date_to')) {
            $query->whereDate('end_date', '<=', $request->date_to);
        }

        $leaveRequests = $query->paginate(15)->withQueryString();

        $stats = [
            'total_requests' => LeaveRequest::count(),
            'pending_requests' => LeaveRequest::where('status', LeaveRequest::STATUS_PENDING)->count(),
            'approved_requests' => LeaveRequest::where('status', LeaveRequest::STATUS_APPROVED)->count(),
            'rejected_requests' => LeaveRequest::where('status', LeaveRequest::STATUS_REJECTED)->count(),
        ];

        return Inertia::render('Tenant/HRM/Leave/Index', [
            'leaveRequests' => $leaveRequests,
            'stats' => $stats,
            'leaveTypes' => LeaveType::active()->get(),
            'employees' => Employee::active()->select('id', 'first_name', 'last_name')->get(),
            'filters' => $request->only(['search', 'status', 'leave_type_id', 'employee_id', 'date_from', 'date_to']),
        ]);
    }

    /**
     * Show the form for creating a new leave request
     */
    public function create(): Response
    {
        return Inertia::render('Tenant/HRM/Leave/Create', [
            'leaveTypes' => LeaveType::active()->get(),
            'employees' => Employee::active()->select('id', 'first_name', 'last_name')->get(),
        ]);
    }

    /**
     * Store a newly created leave request
     */
    public function store(Request $request)
    {
        $request->validate([
            'employee_id' => 'required|exists:employees,id',
            'leave_type_id' => 'required|exists:leave_types,id',
            'start_date' => 'required|date|after_or_equal:today',
            'end_date' => 'required|date|after_or_equal:start_date',
            'reason' => 'required|string|max:1000',
            'comments' => 'nullable|string|max:500',
            'is_half_day' => 'boolean',
            'half_day_period' => 'nullable|in:morning,afternoon',
            'emergency_contact_name' => 'nullable|string|max:255',
            'emergency_contact_phone' => 'nullable|string|max:20',
        ]);

        try {
            $employee = Employee::findOrFail($request->employee_id);
            
            $leaveRequest = $this->leaveService->submitLeaveRequest($employee, $request->all());

            return redirect()->route('hrm.leave.index')
                ->with('success', 'Leave request submitted successfully.');

        } catch (\Exception $e) {
            return back()
                ->withInput()
                ->withErrors(['error' => $e->getMessage()]);
        }
    }

    /**
     * Display the specified leave request
     */
    public function show(LeaveRequest $leave): Response
    {
        $leave->load(['employee', 'leaveType', 'approver', 'rejector']);

        return Inertia::render('Tenant/HRM/Leave/Show', [
            'leaveRequest' => $leave,
        ]);
    }

    /**
     * Show the form for editing the specified leave request
     */
    public function edit(LeaveRequest $leave): Response
    {
        // Only allow editing of pending requests
        if (!$leave->isPending()) {
            return redirect()->route('hrm.leave.show', $leave)
                ->with('error', 'Only pending leave requests can be edited.');
        }

        return Inertia::render('Tenant/HRM/Leave/Edit', [
            'leaveRequest' => $leave,
            'leaveTypes' => LeaveType::active()->get(),
            'employees' => Employee::active()->select('id', 'first_name', 'last_name')->get(),
        ]);
    }

    /**
     * Update the specified leave request
     */
    public function update(Request $request, LeaveRequest $leave)
    {
        // Only allow updating of pending requests
        if (!$leave->isPending()) {
            return redirect()->route('hrm.leave.show', $leave)
                ->with('error', 'Only pending leave requests can be updated.');
        }

        $request->validate([
            'leave_type_id' => 'required|exists:leave_types,id',
            'start_date' => 'required|date|after_or_equal:today',
            'end_date' => 'required|date|after_or_equal:start_date',
            'reason' => 'required|string|max:1000',
            'comments' => 'nullable|string|max:500',
            'is_half_day' => 'boolean',
            'half_day_period' => 'nullable|in:morning,afternoon',
            'emergency_contact_name' => 'nullable|string|max:255',
            'emergency_contact_phone' => 'nullable|string|max:20',
        ]);

        try {
            DB::beginTransaction();

            // Release previously reserved days
            $entitlement = $leave->employee->getLeaveEntitlement($leave->leave_type_id, $leave->start_date->year);
            if ($entitlement) {
                $entitlement->releaseDays($leave->total_days);
            }

            // Calculate new total days
            $startDate = Carbon::parse($request->start_date);
            $endDate = Carbon::parse($request->end_date);
            $isHalfDay = $request->boolean('is_half_day');
            $totalDays = LeaveRequest::calculateWorkingDays($startDate, $endDate, $isHalfDay);

            // Update leave request
            $leave->update([
                'leave_type_id' => $request->leave_type_id,
                'start_date' => $startDate,
                'end_date' => $endDate,
                'total_days' => $totalDays,
                'reason' => $request->reason,
                'comments' => $request->comments,
                'is_half_day' => $isHalfDay,
                'half_day_period' => $request->half_day_period,
                'emergency_contact_name' => $request->emergency_contact_name,
                'emergency_contact_phone' => $request->emergency_contact_phone,
            ]);

            // Reserve new days
            $newEntitlement = $leave->employee->getLeaveEntitlement($request->leave_type_id, $startDate->year);
            if ($newEntitlement) {
                $newEntitlement->reserveDays($totalDays);
            }

            DB::commit();

            return redirect()->route('hrm.leave.show', $leave)
                ->with('success', 'Leave request updated successfully.');

        } catch (\Exception $e) {
            DB::rollBack();
            
            return back()
                ->withInput()
                ->withErrors(['error' => $e->getMessage()]);
        }
    }

    /**
     * Approve a leave request
     */
    public function approve(Request $request, LeaveRequest $leave)
    {
        $request->validate([
            'notes' => 'nullable|string|max:500',
        ]);

        try {
            $approver = Employee::where('user_id', Auth::id())->firstOrFail();
            
            $this->leaveService->approveLeaveRequest($leave, $approver, $request->notes);

            return redirect()->route('hrm.leave.show', $leave)
                ->with('success', 'Leave request approved successfully.');

        } catch (\Exception $e) {
            return back()->withErrors(['error' => $e->getMessage()]);
        }
    }

    /**
     * Reject a leave request
     */
    public function reject(Request $request, LeaveRequest $leave)
    {
        $request->validate([
            'reason' => 'required|string|max:500',
        ]);

        try {
            $rejector = Employee::where('user_id', Auth::id())->firstOrFail();
            
            $this->leaveService->rejectLeaveRequest($leave, $rejector, $request->reason);

            return redirect()->route('hrm.leave.show', $leave)
                ->with('success', 'Leave request rejected.');

        } catch (\Exception $e) {
            return back()->withErrors(['error' => $e->getMessage()]);
        }
    }

    /**
     * Cancel a leave request
     */
    public function cancel(LeaveRequest $leave)
    {
        try {
            $this->leaveService->cancelLeaveRequest($leave);

            return redirect()->route('hrm.leave.show', $leave)
                ->with('success', 'Leave request cancelled.');

        } catch (\Exception $e) {
            return back()->withErrors(['error' => $e->getMessage()]);
        }
    }

    /**
     * Get leave calendar data
     */
    public function calendar(Request $request)
    {
        $startDate = Carbon::parse($request->get('start', now()->startOfMonth()));
        $endDate = Carbon::parse($request->get('end', now()->endOfMonth()));
        
        $employeeId = $request->get('employee_id');
        $employee = $employeeId ? Employee::find($employeeId) : null;

        $events = $this->leaveService->getLeaveCalendar($startDate, $endDate, $employee);

        if ($request->expectsJson()) {
            return response()->json($events);
        }

        return Inertia::render('Tenant/HRM/Leave/Calendar', [
            'events' => $events,
            'employees' => Employee::active()->select('id', 'first_name', 'last_name')->get(),
        ]);
    }

    /**
     * Get leave statistics
     */
    public function statistics(Request $request)
    {
        $year = $request->get('year', now()->year);
        $employeeId = $request->get('employee_id');
        $employee = $employeeId ? Employee::find($employeeId) : null;

        $statistics = $this->leaveService->getLeaveStatistics($year, $employee);

        if ($request->expectsJson()) {
            return response()->json($statistics);
        }

        return Inertia::render('Tenant/HRM/Leave/Statistics', [
            'statistics' => $statistics,
            'employees' => Employee::active()->select('id', 'first_name', 'last_name')->get(),
            'year' => $year,
        ]);
    }
}
