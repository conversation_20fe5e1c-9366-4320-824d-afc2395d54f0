<?php

namespace App\Http\Controllers\Tenant\HRM;

use App\Http\Controllers\Controller;
use App\Models\Tenant\Employee;
use App\Models\Tenant\AttendanceRecord;
use App\Models\Tenant\Branch;
use App\Services\HRM\AttendanceService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Inertia\Inertia;
use Inertia\Response;
use Carbon\Carbon;

class AttendanceController extends Controller
{
    protected $attendanceService;

    public function __construct(AttendanceService $attendanceService)
    {
        $this->middleware('auth');
        $this->middleware('verified');
        $this->attendanceService = $attendanceService;
    }

    /**
     * Display attendance dashboard
     */
    public function index(Request $request): Response
    {
        $date = $request->get('date', now()->toDateString());
        $branchId = $request->get('branch_id');
        $branch = $branchId ? Branch::find($branchId) : null;

        $teamAttendance = $this->attendanceService->getTeamAttendance(Carbon::parse($date), $branch);

        $stats = [
            'total_employees' => count($teamAttendance),
            'present_count' => collect($teamAttendance)->where('status', AttendanceRecord::STATUS_PRESENT)->count(),
            'late_count' => collect($teamAttendance)->where('status', AttendanceRecord::STATUS_LATE)->count(),
            'absent_count' => collect($teamAttendance)->where('status', AttendanceRecord::STATUS_ABSENT)->count(),
            'on_leave_count' => collect($teamAttendance)->where('status', AttendanceRecord::STATUS_ON_LEAVE)->count(),
            'clocked_in_count' => collect($teamAttendance)->where('is_clocked_in', true)->count(),
        ];

        return Inertia::render('Tenant/HRM/Attendance/Index', [
            'teamAttendance' => $teamAttendance,
            'stats' => $stats,
            'branches' => Branch::active()->get(),
            'selectedDate' => $date,
            'selectedBranch' => $branch,
        ]);
    }

    /**
     * Clock in an employee
     */
    public function clockIn(Request $request)
    {
        $request->validate([
            'employee_id' => 'required|exists:employees,id',
            'location' => 'nullable|string|max:255',
            'notes' => 'nullable|string|max:500',
        ]);

        try {
            $employee = Employee::findOrFail($request->employee_id);
            
            $attendance = $this->attendanceService->clockIn($employee, [
                'location' => $request->location,
                'ip' => $request->ip(),
            ]);

            return response()->json([
                'success' => true,
                'message' => 'Clocked in successfully.',
                'attendance' => $attendance,
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => $e->getMessage(),
            ], 400);
        }
    }

    /**
     * Clock out an employee
     */
    public function clockOut(Request $request)
    {
        $request->validate([
            'employee_id' => 'required|exists:employees,id',
            'location' => 'nullable|string|max:255',
            'notes' => 'nullable|string|max:500',
        ]);

        try {
            $employee = Employee::findOrFail($request->employee_id);
            
            $attendance = $this->attendanceService->clockOut($employee, [
                'location' => $request->location,
                'ip' => $request->ip(),
            ]);

            return response()->json([
                'success' => true,
                'message' => 'Clocked out successfully.',
                'attendance' => $attendance,
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => $e->getMessage(),
            ], 400);
        }
    }

    /**
     * Mark employee as absent
     */
    public function markAbsent(Request $request)
    {
        $request->validate([
            'employee_id' => 'required|exists:employees,id',
            'date' => 'required|date',
            'reason' => 'nullable|string|max:500',
        ]);

        try {
            $employee = Employee::findOrFail($request->employee_id);
            $date = Carbon::parse($request->date);
            
            $attendance = $this->attendanceService->markAbsent($employee, $date, $request->reason);

            return response()->json([
                'success' => true,
                'message' => 'Employee marked as absent.',
                'attendance' => $attendance,
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => $e->getMessage(),
            ], 400);
        }
    }

    /**
     * Mark employee as on leave
     */
    public function markOnLeave(Request $request)
    {
        $request->validate([
            'employee_id' => 'required|exists:employees,id',
            'date' => 'required|date',
        ]);

        try {
            $employee = Employee::findOrFail($request->employee_id);
            $date = Carbon::parse($request->date);
            
            $attendance = $this->attendanceService->markOnLeave($employee, $date);

            return response()->json([
                'success' => true,
                'message' => 'Employee marked as on leave.',
                'attendance' => $attendance,
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => $e->getMessage(),
            ], 400);
        }
    }

    /**
     * Show attendance records for an employee
     */
    public function show(Request $request, Employee $employee): Response
    {
        $startDate = Carbon::parse($request->get('start_date', now()->startOfMonth()));
        $endDate = Carbon::parse($request->get('end_date', now()->endOfMonth()));

        $records = $this->attendanceService->getAttendanceRecords($employee, $startDate, $endDate);
        $summary = $this->attendanceService->getAttendanceSummary($employee, $startDate, $endDate);

        return Inertia::render('Tenant/HRM/Attendance/Show', [
            'employee' => $employee,
            'records' => $records,
            'summary' => $summary,
            'startDate' => $startDate->toDateString(),
            'endDate' => $endDate->toDateString(),
        ]);
    }

    /**
     * Update attendance record
     */
    public function update(Request $request, AttendanceRecord $attendance)
    {
        $request->validate([
            'clock_in_time' => 'nullable|date_format:H:i',
            'clock_out_time' => 'nullable|date_format:H:i|after:clock_in_time',
            'break_start_time' => 'nullable|date_format:H:i',
            'break_end_time' => 'nullable|date_format:H:i|after:break_start_time',
            'status' => 'required|in:present,absent,late,half_day,on_leave',
            'notes' => 'nullable|string|max:500',
        ]);

        try {
            // Update basic fields
            $attendance->update($request->only([
                'status', 'notes'
            ]));

            // Update times if provided
            if ($request->filled('clock_in_time')) {
                $attendance->clock_in_time = Carbon::parse($attendance->date . ' ' . $request->clock_in_time);
            }

            if ($request->filled('clock_out_time')) {
                $attendance->clock_out_time = Carbon::parse($attendance->date . ' ' . $request->clock_out_time);
            }

            if ($request->filled('break_start_time')) {
                $attendance->break_start_time = Carbon::parse($attendance->date . ' ' . $request->break_start_time);
            }

            if ($request->filled('break_end_time')) {
                $attendance->break_end_time = Carbon::parse($attendance->date . ' ' . $request->break_end_time);
            }

            // Recalculate hours
            $attendance->calculateHours();
            $attendance->save();

            return response()->json([
                'success' => true,
                'message' => 'Attendance record updated successfully.',
                'attendance' => $attendance,
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => $e->getMessage(),
            ], 400);
        }
    }

    /**
     * Generate attendance report
     */
    public function report(Request $request): Response
    {
        $request->validate([
            'start_date' => 'required|date',
            'end_date' => 'required|date|after_or_equal:start_date',
            'branch_id' => 'nullable|exists:branches,id',
            'employee_id' => 'nullable|exists:employees,id',
        ]);

        $startDate = Carbon::parse($request->start_date);
        $endDate = Carbon::parse($request->end_date);
        $branch = $request->branch_id ? Branch::find($request->branch_id) : null;

        $report = $this->attendanceService->generateAttendanceReport($startDate, $endDate, $branch);

        // Filter by employee if specified
        if ($request->employee_id) {
            $report['records'] = $report['records']->where('employee_id', $request->employee_id);
            $report['employee_summary'] = $report['employee_summary']->where('employee_id', $request->employee_id);
        }

        return Inertia::render('Tenant/HRM/Attendance/Report', [
            'report' => $report,
            'startDate' => $startDate->toDateString(),
            'endDate' => $endDate->toDateString(),
            'branches' => Branch::active()->get(),
            'employees' => Employee::active()->select('id', 'first_name', 'last_name')->get(),
            'filters' => $request->only(['start_date', 'end_date', 'branch_id', 'employee_id']),
        ]);
    }

    /**
     * Export attendance data
     */
    public function export(Request $request)
    {
        $request->validate([
            'start_date' => 'required|date',
            'end_date' => 'required|date|after_or_equal:start_date',
            'format' => 'required|in:csv,excel,pdf',
            'branch_id' => 'nullable|exists:branches,id',
        ]);

        // This would typically use a job or export class
        // For now, return a simple response
        return response()->json([
            'message' => 'Export functionality will be implemented',
            'start_date' => $request->start_date,
            'end_date' => $request->end_date,
            'format' => $request->format,
        ]);
    }

    /**
     * Get current user's attendance status
     */
    public function myStatus()
    {
        $user = Auth::user();
        $employee = Employee::where('user_id', $user->id)->first();

        if (!$employee) {
            return response()->json([
                'success' => false,
                'message' => 'Employee record not found.',
            ], 404);
        }

        $todayAttendance = $employee->getTodayAttendance();
        $isClockedIn = $employee->isClockedIn();

        return response()->json([
            'success' => true,
            'employee' => $employee,
            'today_attendance' => $todayAttendance,
            'is_clocked_in' => $isClockedIn,
        ]);
    }

    /**
     * Self clock in/out for current user
     */
    public function selfClockInOut(Request $request)
    {
        $request->validate([
            'action' => 'required|in:clock_in,clock_out',
            'location' => 'nullable|string|max:255',
        ]);

        $user = Auth::user();
        $employee = Employee::where('user_id', $user->id)->first();

        if (!$employee) {
            return response()->json([
                'success' => false,
                'message' => 'Employee record not found.',
            ], 404);
        }

        try {
            if ($request->action === 'clock_in') {
                $attendance = $this->attendanceService->clockIn($employee, [
                    'location' => $request->location,
                    'ip' => $request->ip(),
                ]);
                $message = 'Clocked in successfully.';
            } else {
                $attendance = $this->attendanceService->clockOut($employee, [
                    'location' => $request->location,
                    'ip' => $request->ip(),
                ]);
                $message = 'Clocked out successfully.';
            }

            return response()->json([
                'success' => true,
                'message' => $message,
                'attendance' => $attendance,
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => $e->getMessage(),
            ], 400);
        }
    }
}
