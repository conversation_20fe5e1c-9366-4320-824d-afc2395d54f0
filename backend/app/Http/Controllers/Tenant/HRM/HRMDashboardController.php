<?php

namespace App\Http\Controllers\Tenant\HRM;

use App\Http\Controllers\Controller;
use App\Models\Tenant\Employee;
use App\Models\Tenant\LeaveRequest;
use App\Models\Tenant\AttendanceRecord;
use App\Models\Tenant\PayrollRecord;
use App\Models\Tenant\EmployeeLoan;
use App\Services\HRM\AttendanceService;
use App\Services\HRM\LeaveService;
use App\Services\HRM\PayrollService;
use Illuminate\Http\Request;
use Inertia\Inertia;
use Inertia\Response;
use Carbon\Carbon;

class HRMDashboardController extends Controller
{
    protected $attendanceService;
    protected $leaveService;
    protected $payrollService;

    public function __construct(
        AttendanceService $attendanceService,
        LeaveService $leaveService,
        PayrollService $payrollService
    ) {
        $this->middleware('auth');
        $this->middleware('verified');
        $this->attendanceService = $attendanceService;
        $this->leaveService = $leaveService;
        $this->payrollService = $payrollService;
    }

    /**
     * Display the HRM dashboard
     */
    public function index(): Response
    {
        $today = now();
        $thisMonth = $today->copy()->startOfMonth();
        $thisYear = $today->year;

        // Employee Statistics
        $employeeStats = [
            'total_employees' => Employee::count(),
            'active_employees' => Employee::where('employment_status', 'active')->count(),
            'on_probation' => Employee::whereNotNull('probation_end_date')
                ->where('probation_end_date', '>', $today)
                ->count(),
            'new_hires_this_month' => Employee::whereMonth('hire_date', $today->month)
                ->whereYear('hire_date', $today->year)
                ->count(),
        ];

        // Attendance Statistics
        $attendanceStats = [
            'present_today' => AttendanceRecord::whereDate('date', $today)
                ->where('status', AttendanceRecord::STATUS_PRESENT)
                ->count(),
            'late_today' => AttendanceRecord::whereDate('date', $today)
                ->where('status', AttendanceRecord::STATUS_LATE)
                ->count(),
            'absent_today' => AttendanceRecord::whereDate('date', $today)
                ->where('status', AttendanceRecord::STATUS_ABSENT)
                ->count(),
            'on_leave_today' => AttendanceRecord::whereDate('date', $today)
                ->where('status', AttendanceRecord::STATUS_ON_LEAVE)
                ->count(),
            'clocked_in_now' => AttendanceRecord::whereDate('date', $today)
                ->whereNotNull('clock_in_time')
                ->whereNull('clock_out_time')
                ->count(),
        ];

        // Leave Statistics
        $leaveStats = [
            'pending_requests' => LeaveRequest::where('status', LeaveRequest::STATUS_PENDING)->count(),
            'approved_this_month' => LeaveRequest::where('status', LeaveRequest::STATUS_APPROVED)
                ->whereMonth('start_date', $today->month)
                ->whereYear('start_date', $today->year)
                ->count(),
            'on_leave_today' => LeaveRequest::where('status', LeaveRequest::STATUS_APPROVED)
                ->whereDate('start_date', '<=', $today)
                ->whereDate('end_date', '>=', $today)
                ->count(),
            'leave_days_this_month' => LeaveRequest::where('status', LeaveRequest::STATUS_APPROVED)
                ->whereMonth('start_date', $today->month)
                ->whereYear('start_date', $today->year)
                ->sum('total_days'),
        ];

        // Payroll Statistics
        $payrollStats = [
            'total_payroll_this_month' => PayrollRecord::whereBetween('pay_period_start', [$thisMonth, $today])
                ->sum('net_pay'),
            'pending_payrolls' => PayrollRecord::where('status', PayrollRecord::STATUS_DRAFT)->count(),
            'approved_payrolls' => PayrollRecord::where('status', PayrollRecord::STATUS_APPROVED)->count(),
            'paid_payrolls_this_month' => PayrollRecord::where('status', PayrollRecord::STATUS_PAID)
                ->whereBetween('pay_date', [$thisMonth, $today])
                ->count(),
        ];

        // Loan Statistics
        $loanStats = [
            'active_loans' => EmployeeLoan::where('status', 'active')->count(),
            'pending_loans' => EmployeeLoan::where('status', 'pending')->count(),
            'total_outstanding' => EmployeeLoan::where('status', 'active')->sum('outstanding_balance'),
            'overdue_payments' => EmployeeLoan::where('status', 'active')
                ->where('next_payment_date', '<', $today)
                ->count(),
        ];

        // Recent Activities
        $recentActivities = $this->getRecentActivities();

        // Upcoming Events
        $upcomingEvents = $this->getUpcomingEvents();

        // Quick Actions Data
        $quickActionsData = [
            'employees_without_attendance_today' => Employee::active()
                ->whereDoesntHave('attendanceRecords', function ($query) use ($today) {
                    $query->whereDate('date', $today);
                })
                ->count(),
            'pending_leave_approvals' => LeaveRequest::where('status', LeaveRequest::STATUS_PENDING)->count(),
            'overdue_loan_payments' => EmployeeLoan::where('status', 'active')
                ->where('next_payment_date', '<', $today)
                ->count(),
        ];

        return Inertia::render('Tenant/HRM/Dashboard', [
            'employeeStats' => $employeeStats,
            'attendanceStats' => $attendanceStats,
            'leaveStats' => $leaveStats,
            'payrollStats' => $payrollStats,
            'loanStats' => $loanStats,
            'recentActivities' => $recentActivities,
            'upcomingEvents' => $upcomingEvents,
            'quickActionsData' => $quickActionsData,
        ]);
    }

    /**
     * Get recent HRM activities
     */
    protected function getRecentActivities(): array
    {
        $activities = [];

        // Recent leave requests
        $recentLeaveRequests = LeaveRequest::with('employee')
            ->latest()
            ->limit(5)
            ->get();

        foreach ($recentLeaveRequests as $request) {
            $activities[] = [
                'type' => 'leave_request',
                'title' => 'Leave Request ' . ucfirst($request->status),
                'description' => $request->employee->full_name . ' requested ' . $request->total_days . ' days leave',
                'timestamp' => $request->created_at,
                'status' => $request->status,
                'url' => route('hrm.leave.show', $request),
            ];
        }

        // Recent loan applications
        $recentLoans = EmployeeLoan::with('employee')
            ->latest()
            ->limit(3)
            ->get();

        foreach ($recentLoans as $loan) {
            $activities[] = [
                'type' => 'loan',
                'title' => 'Loan Application ' . ucfirst($loan->status),
                'description' => $loan->employee->full_name . ' applied for ' . getCurrency() . number_format($loan->loan_amount, 2) . ' loan',
                'timestamp' => $loan->created_at,
                'status' => $loan->status,
                'url' => route('hrm.loans.show', $loan),
            ];
        }

        // Recent payroll records
        $recentPayrolls = PayrollRecord::with('employee')
            ->latest()
            ->limit(3)
            ->get();

        foreach ($recentPayrolls as $payroll) {
            $activities[] = [
                'type' => 'payroll',
                'title' => 'Payroll ' . ucfirst($payroll->status),
                'description' => $payroll->employee->full_name . ' - ' . getCurrency() . number_format($payroll->net_pay, 2),
                'timestamp' => $payroll->created_at,
                'status' => $payroll->status,
                'url' => route('hrm.payroll.show', $payroll),
            ];
        }

        // Sort by timestamp and limit
        usort($activities, function ($a, $b) {
            return $b['timestamp']->timestamp - $a['timestamp']->timestamp;
        });

        return array_slice($activities, 0, 10);
    }

    /**
     * Get upcoming HRM events
     */
    protected function getUpcomingEvents(): array
    {
        $events = [];
        $today = now();
        $nextWeek = $today->copy()->addWeek();

        // Upcoming leave requests
        $upcomingLeaves = LeaveRequest::with('employee', 'leaveType')
            ->where('status', LeaveRequest::STATUS_APPROVED)
            ->whereBetween('start_date', [$today, $nextWeek])
            ->get();

        foreach ($upcomingLeaves as $leave) {
            $events[] = [
                'type' => 'leave',
                'title' => $leave->employee->full_name . ' - ' . $leave->leaveType->name,
                'date' => $leave->start_date,
                'description' => $leave->total_days . ' days leave',
                'url' => route('hrm.leave.show', $leave),
            ];
        }

        // Upcoming loan payments
        $upcomingPayments = EmployeeLoan::with('employee')
            ->where('status', 'active')
            ->whereBetween('next_payment_date', [$today, $nextWeek])
            ->get();

        foreach ($upcomingPayments as $loan) {
            $events[] = [
                'type' => 'loan_payment',
                'title' => $loan->employee->full_name . ' - Loan Payment Due',
                'date' => $loan->next_payment_date,
                'description' => getCurrency() . number_format($loan->monthly_installment, 2) . ' payment due',
                'url' => route('hrm.loans.show', $loan),
            ];
        }

        // Employee probation endings
        $probationEndings = Employee::whereNotNull('probation_end_date')
            ->whereBetween('probation_end_date', [$today, $nextWeek])
            ->get();

        foreach ($probationEndings as $employee) {
            $events[] = [
                'type' => 'probation_end',
                'title' => $employee->full_name . ' - Probation Ending',
                'date' => $employee->probation_end_date,
                'description' => 'Probation period ends',
                'url' => route('staff.show', $employee),
            ];
        }

        // Sort by date
        usort($events, function ($a, $b) {
            return $a['date']->timestamp - $b['date']->timestamp;
        });

        return array_slice($events, 0, 10);
    }

    /**
     * Get HRM analytics data
     */
    public function analytics(Request $request)
    {
        $period = $request->get('period', 'month'); // month, quarter, year
        $startDate = $this->getStartDate($period);
        $endDate = now();

        $analytics = [
            'attendance_trends' => $this->getAttendanceTrends($startDate, $endDate),
            'leave_trends' => $this->getLeaveTrends($startDate, $endDate),
            'payroll_trends' => $this->getPayrollTrends($startDate, $endDate),
            'employee_growth' => $this->getEmployeeGrowth($startDate, $endDate),
        ];

        if ($request->expectsJson()) {
            return response()->json($analytics);
        }

        return Inertia::render('Tenant/HRM/Analytics', [
            'analytics' => $analytics,
            'period' => $period,
        ]);
    }

    /**
     * Get start date based on period
     */
    protected function getStartDate(string $period): Carbon
    {
        switch ($period) {
            case 'quarter':
                return now()->startOfQuarter();
            case 'year':
                return now()->startOfYear();
            default:
                return now()->startOfMonth();
        }
    }

    /**
     * Get attendance trends
     */
    protected function getAttendanceTrends(Carbon $startDate, Carbon $endDate): array
    {
        return AttendanceRecord::selectRaw('DATE(date) as date, status, COUNT(*) as count')
            ->whereBetween('date', [$startDate, $endDate])
            ->groupBy('date', 'status')
            ->orderBy('date')
            ->get()
            ->groupBy('date')
            ->map(function ($records) {
                return $records->pluck('count', 'status');
            })
            ->toArray();
    }

    /**
     * Get leave trends
     */
    protected function getLeaveTrends(Carbon $startDate, Carbon $endDate): array
    {
        return LeaveRequest::with('leaveType')
            ->where('status', LeaveRequest::STATUS_APPROVED)
            ->whereBetween('start_date', [$startDate, $endDate])
            ->selectRaw('DATE(start_date) as date, leave_type_id, SUM(total_days) as total_days')
            ->groupBy('date', 'leave_type_id')
            ->orderBy('date')
            ->get()
            ->groupBy('date')
            ->toArray();
    }

    /**
     * Get payroll trends
     */
    protected function getPayrollTrends(Carbon $startDate, Carbon $endDate): array
    {
        return PayrollRecord::selectRaw('DATE(pay_date) as date, SUM(gross_pay) as gross_pay, SUM(net_pay) as net_pay, COUNT(*) as count')
            ->whereBetween('pay_date', [$startDate, $endDate])
            ->groupBy('date')
            ->orderBy('date')
            ->get()
            ->toArray();
    }

    /**
     * Get employee growth
     */
    protected function getEmployeeGrowth(Carbon $startDate, Carbon $endDate): array
    {
        return Employee::selectRaw('DATE(hire_date) as date, COUNT(*) as new_hires')
            ->whereBetween('hire_date', [$startDate, $endDate])
            ->groupBy('date')
            ->orderBy('date')
            ->get()
            ->toArray();
    }
}
