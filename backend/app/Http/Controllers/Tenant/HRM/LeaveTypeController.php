<?php

namespace App\Http\Controllers\Tenant\HRM;

use App\Http\Controllers\Controller;
use App\Models\Tenant\LeaveType;
use App\Models\Tenant\Employee;
use App\Services\HRM\LeaveService;
use Illuminate\Http\Request;
use Inertia\Inertia;
use Inertia\Response;

class LeaveTypeController extends Controller
{
    protected $leaveService;

    public function __construct(LeaveService $leaveService)
    {
        $this->middleware('auth');
        $this->middleware('verified');
        $this->leaveService = $leaveService;
    }

    /**
     * Display a listing of leave types
     */
    public function index(Request $request): Response
    {
        $query = LeaveType::withCount(['entitlements', 'leaveRequests']);

        // Apply filters
        if ($request->filled('search')) {
            $query->where('name', 'like', '%' . $request->search . '%')
                  ->orWhere('description', 'like', '%' . $request->search . '%');
        }

        if ($request->filled('status')) {
            $query->where('is_active', $request->status === 'active');
        }

        $leaveTypes = $query->orderBy('name')->paginate(15)->withQueryString();

        $stats = [
            'total_types' => LeaveType::count(),
            'active_types' => LeaveType::where('is_active', true)->count(),
            'inactive_types' => LeaveType::where('is_active', false)->count(),
        ];

        return Inertia::render('Tenant/HRM/LeaveTypes/Index', [
            'leaveTypes' => $leaveTypes,
            'stats' => $stats,
            'filters' => $request->only(['search', 'status']),
        ]);
    }

    /**
     * Show the form for creating a new leave type
     */
    public function create(): Response
    {
        return Inertia::render('Tenant/HRM/LeaveTypes/Create');
    }

    /**
     * Store a newly created leave type
     */
    public function store(Request $request)
    {
        $request->validate([
            'name' => 'required|string|max:255|unique:leave_types,name',
            'description' => 'nullable|string|max:1000',
            'default_days_per_year' => 'required|integer|min:0|max:365',
            'max_consecutive_days' => 'nullable|integer|min:1|max:365',
            'min_notice_days' => 'required|integer|min:0|max:365',
            'max_carry_forward_days' => 'required|integer|min:0|max:365',
            'requires_medical_certificate' => 'boolean',
            'medical_certificate_days' => 'nullable|integer|min:1|max:365',
            'color_code' => 'nullable|string|max:7|regex:/^#[0-9A-Fa-f]{6}$/',
            'is_active' => 'boolean',
        ]);

        $leaveType = LeaveType::create($request->all());

        // Create entitlements for all active employees
        $employees = Employee::active()->get();
        foreach ($employees as $employee) {
            $this->leaveService->createLeaveEntitlements($employee, now()->year);
        }

        return redirect()->route('hrm.leave-types.index')
            ->with('success', 'Leave type created successfully.');
    }

    /**
     * Display the specified leave type
     */
    public function show(LeaveType $leaveType): Response
    {
        $leaveType->load(['entitlements.employee', 'leaveRequests.employee']);

        $stats = [
            'total_entitlements' => $leaveType->entitlements()->count(),
            'total_requests' => $leaveType->leaveRequests()->count(),
            'pending_requests' => $leaveType->leaveRequests()->where('status', 'pending')->count(),
            'approved_requests' => $leaveType->leaveRequests()->where('status', 'approved')->count(),
            'total_days_used' => $leaveType->leaveRequests()->where('status', 'approved')->sum('total_days'),
        ];

        return Inertia::render('Tenant/HRM/LeaveTypes/Show', [
            'leaveType' => $leaveType,
            'stats' => $stats,
        ]);
    }

    /**
     * Show the form for editing the specified leave type
     */
    public function edit(LeaveType $leaveType): Response
    {
        return Inertia::render('Tenant/HRM/LeaveTypes/Edit', [
            'leaveType' => $leaveType,
        ]);
    }

    /**
     * Update the specified leave type
     */
    public function update(Request $request, LeaveType $leaveType)
    {
        $request->validate([
            'name' => 'required|string|max:255|unique:leave_types,name,' . $leaveType->id,
            'description' => 'nullable|string|max:1000',
            'default_days_per_year' => 'required|integer|min:0|max:365',
            'max_consecutive_days' => 'nullable|integer|min:1|max:365',
            'min_notice_days' => 'required|integer|min:0|max:365',
            'max_carry_forward_days' => 'required|integer|min:0|max:365',
            'requires_medical_certificate' => 'boolean',
            'medical_certificate_days' => 'nullable|integer|min:1|max:365',
            'color_code' => 'nullable|string|max:7|regex:/^#[0-9A-Fa-f]{6}$/',
            'is_active' => 'boolean',
        ]);

        $leaveType->update($request->all());

        return redirect()->route('hrm.leave-types.show', $leaveType)
            ->with('success', 'Leave type updated successfully.');
    }

    /**
     * Remove the specified leave type
     */
    public function destroy(LeaveType $leaveType)
    {
        // Check if leave type has any associated requests
        if ($leaveType->leaveRequests()->exists()) {
            return back()->withErrors([
                'error' => 'Cannot delete leave type that has associated leave requests.'
            ]);
        }

        // Check if leave type has any entitlements
        if ($leaveType->entitlements()->exists()) {
            return back()->withErrors([
                'error' => 'Cannot delete leave type that has associated entitlements.'
            ]);
        }

        $leaveType->delete();

        return redirect()->route('hrm.leave-types.index')
            ->with('success', 'Leave type deleted successfully.');
    }

    /**
     * Toggle the active status of a leave type
     */
    public function toggleStatus(LeaveType $leaveType)
    {
        $leaveType->update(['is_active' => !$leaveType->is_active]);

        $status = $leaveType->is_active ? 'activated' : 'deactivated';

        return back()->with('success', "Leave type {$status} successfully.");
    }

    /**
     * Bulk create entitlements for all employees
     */
    public function createEntitlements(Request $request)
    {
        $request->validate([
            'year' => 'required|integer|min:2020|max:2050',
        ]);

        $year = $request->year;
        $employees = Employee::active()->get();
        $created = 0;

        foreach ($employees as $employee) {
            $entitlements = $this->leaveService->createLeaveEntitlements($employee, $year);
            $created += count($entitlements);
        }

        return back()->with('success', "Created {$created} leave entitlements for year {$year}.");
    }

    /**
     * Get leave type usage statistics
     */
    public function usage(Request $request, LeaveType $leaveType)
    {
        $year = $request->get('year', now()->year);

        $usage = [
            'total_entitled_days' => $leaveType->entitlements()
                ->where('year', $year)
                ->sum('entitled_days'),
            'total_used_days' => $leaveType->entitlements()
                ->where('year', $year)
                ->sum('used_days'),
            'total_pending_days' => $leaveType->entitlements()
                ->where('year', $year)
                ->sum('pending_days'),
            'total_available_days' => $leaveType->entitlements()
                ->where('year', $year)
                ->get()
                ->sum('available_days'),
            'usage_by_month' => $leaveType->leaveRequests()
                ->where('status', 'approved')
                ->whereYear('start_date', $year)
                ->selectRaw('MONTH(start_date) as month, SUM(total_days) as total_days')
                ->groupBy('month')
                ->orderBy('month')
                ->get()
                ->pluck('total_days', 'month'),
            'top_users' => $leaveType->leaveRequests()
                ->with('employee')
                ->where('status', 'approved')
                ->whereYear('start_date', $year)
                ->selectRaw('employee_id, SUM(total_days) as total_days')
                ->groupBy('employee_id')
                ->orderByDesc('total_days')
                ->limit(10)
                ->get(),
        ];

        if ($request->expectsJson()) {
            return response()->json($usage);
        }

        return Inertia::render('Tenant/HRM/LeaveTypes/Usage', [
            'leaveType' => $leaveType,
            'usage' => $usage,
            'year' => $year,
        ]);
    }

    /**
     * Export leave type data
     */
    public function export(Request $request, LeaveType $leaveType)
    {
        $year = $request->get('year', now()->year);
        $format = $request->get('format', 'csv');

        // This would typically use a job or export class
        // For now, return a simple response
        return response()->json([
            'message' => 'Export functionality will be implemented',
            'leave_type' => $leaveType->name,
            'year' => $year,
            'format' => $format,
        ]);
    }
}
