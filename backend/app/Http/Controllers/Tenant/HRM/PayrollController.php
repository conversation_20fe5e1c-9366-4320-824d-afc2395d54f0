<?php

namespace App\Http\Controllers\Tenant\HRM;

use App\Http\Controllers\Controller;
use App\Models\Tenant\Employee;
use App\Models\Tenant\PayrollRecord;
use App\Services\HRM\PayrollService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Inertia\Inertia;
use Inertia\Response;
use Carbon\Carbon;

class PayrollController extends Controller
{
    protected $payrollService;

    public function __construct(PayrollService $payrollService)
    {
        $this->middleware('auth');
        $this->middleware('verified');
        $this->payrollService = $payrollService;
    }

    /**
     * Display a listing of payroll records
     */
    public function index(Request $request): Response
    {
        $query = PayrollRecord::with(['employee', 'approver', 'paidBy'])
                              ->latest('pay_period_start');

        // Apply filters
        if ($request->filled('search')) {
            $query->whereHas('employee', function ($q) use ($request) {
                $q->where('first_name', 'like', '%' . $request->search . '%')
                  ->orWhere('last_name', 'like', '%' . $request->search . '%');
            });
        }

        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }

        if ($request->filled('employee_id')) {
            $query->where('employee_id', $request->employee_id);
        }

        if ($request->filled('pay_period_start')) {
            $query->whereDate('pay_period_start', '>=', $request->pay_period_start);
        }

        if ($request->filled('pay_period_end')) {
            $query->whereDate('pay_period_end', '<=', $request->pay_period_end);
        }

        $payrollRecords = $query->paginate(15)->withQueryString();

        $stats = [
            'total_records' => PayrollRecord::count(),
            'draft_records' => PayrollRecord::where('status', PayrollRecord::STATUS_DRAFT)->count(),
            'approved_records' => PayrollRecord::where('status', PayrollRecord::STATUS_APPROVED)->count(),
            'paid_records' => PayrollRecord::where('status', PayrollRecord::STATUS_PAID)->count(),
            'total_gross_pay' => PayrollRecord::sum('gross_pay'),
            'total_net_pay' => PayrollRecord::sum('net_pay'),
        ];

        return Inertia::render('Tenant/HRM/Payroll/Index', [
            'payrollRecords' => $payrollRecords,
            'stats' => $stats,
            'employees' => Employee::active()->select('id', 'first_name', 'last_name')->get(),
            'filters' => $request->only(['search', 'status', 'employee_id', 'pay_period_start', 'pay_period_end']),
        ]);
    }

    /**
     * Show the form for creating a new payroll record
     */
    public function create(): Response
    {
        $payrollPeriods = $this->payrollService->getMonthlyPayrollPeriods();

        return Inertia::render('Tenant/HRM/Payroll/Create', [
            'employees' => Employee::active()->with(['department', 'primaryBranch'])->get(),
            'payrollPeriods' => $payrollPeriods,
        ]);
    }

    /**
     * Store a newly created payroll record
     */
    public function store(Request $request)
    {
        $request->validate([
            'employee_id' => 'required|exists:employees,id',
            'pay_period_start' => 'required|date',
            'pay_period_end' => 'required|date|after_or_equal:pay_period_start',
            'pay_date' => 'required|date|after_or_equal:pay_period_end',
            'bonus' => 'nullable|numeric|min:0',
            'commission' => 'nullable|numeric|min:0',
            'allowances' => 'nullable|numeric|min:0',
            'insurance_deduction' => 'nullable|numeric|min:0',
            'advance_deductions' => 'nullable|numeric|min:0',
            'other_deductions' => 'nullable|numeric|min:0',
        ]);

        try {
            $employee = Employee::findOrFail($request->employee_id);
            $startDate = Carbon::parse($request->pay_period_start);
            $endDate = Carbon::parse($request->pay_period_end);

            // Check if payroll already exists for this period
            $existingPayroll = PayrollRecord::where('employee_id', $employee->id)
                ->where('pay_period_start', $startDate)
                ->where('pay_period_end', $endDate)
                ->first();

            if ($existingPayroll) {
                return back()->withErrors([
                    'error' => 'Payroll record already exists for this employee and period.'
                ]);
            }

            $payroll = $this->payrollService->generatePayroll($employee, $startDate, $endDate, $request->all());

            return redirect()->route('hrm.payroll.show', $payroll)
                ->with('success', 'Payroll record created successfully.');

        } catch (\Exception $e) {
            return back()
                ->withInput()
                ->withErrors(['error' => $e->getMessage()]);
        }
    }

    /**
     * Display the specified payroll record
     */
    public function show(PayrollRecord $payroll): Response
    {
        $payroll->load(['employee.department', 'employee.primaryBranch', 'approver', 'paidBy', 'loanPayments']);

        return Inertia::render('Tenant/HRM/Payroll/Show', [
            'payroll' => $payroll,
        ]);
    }

    /**
     * Show the form for editing the specified payroll record
     */
    public function edit(PayrollRecord $payroll): Response
    {
        // Only allow editing of draft records
        if (!$payroll->isEditable()) {
            return redirect()->route('hrm.payroll.show', $payroll)
                ->with('error', 'Only draft payroll records can be edited.');
        }

        return Inertia::render('Tenant/HRM/Payroll/Edit', [
            'payroll' => $payroll,
        ]);
    }

    /**
     * Update the specified payroll record
     */
    public function update(Request $request, PayrollRecord $payroll)
    {
        // Only allow updating of draft records
        if (!$payroll->isEditable()) {
            return redirect()->route('hrm.payroll.show', $payroll)
                ->with('error', 'Only draft payroll records can be updated.');
        }

        $request->validate([
            'bonus' => 'nullable|numeric|min:0',
            'commission' => 'nullable|numeric|min:0',
            'allowances' => 'nullable|numeric|min:0',
            'insurance_deduction' => 'nullable|numeric|min:0',
            'advance_deductions' => 'nullable|numeric|min:0',
            'other_deductions' => 'nullable|numeric|min:0',
        ]);

        try {
            // Update the payroll record
            $payroll->update($request->only([
                'bonus', 'commission', 'allowances', 
                'insurance_deduction', 'advance_deductions', 'other_deductions'
            ]));

            // Recalculate payroll
            $payroll->calculatePayroll();
            $payroll->save();

            return redirect()->route('hrm.payroll.show', $payroll)
                ->with('success', 'Payroll record updated successfully.');

        } catch (\Exception $e) {
            return back()
                ->withInput()
                ->withErrors(['error' => $e->getMessage()]);
        }
    }

    /**
     * Approve a payroll record
     */
    public function approve(Request $request, PayrollRecord $payroll)
    {
        if ($payroll->status !== PayrollRecord::STATUS_DRAFT) {
            return back()->withErrors([
                'error' => 'Only draft payroll records can be approved.'
            ]);
        }

        try {
            $approver = Employee::where('user_id', Auth::id())->firstOrFail();
            
            $payroll->approve($approver);

            return redirect()->route('hrm.payroll.show', $payroll)
                ->with('success', 'Payroll record approved successfully.');

        } catch (\Exception $e) {
            return back()->withErrors(['error' => $e->getMessage()]);
        }
    }

    /**
     * Mark payroll record as paid
     */
    public function markAsPaid(Request $request, PayrollRecord $payroll)
    {
        if ($payroll->status !== PayrollRecord::STATUS_APPROVED) {
            return back()->withErrors([
                'error' => 'Only approved payroll records can be marked as paid.'
            ]);
        }

        try {
            $paidBy = Employee::where('user_id', Auth::id())->firstOrFail();
            
            $payroll->markAsPaid($paidBy);

            return redirect()->route('hrm.payroll.show', $payroll)
                ->with('success', 'Payroll record marked as paid successfully.');

        } catch (\Exception $e) {
            return back()->withErrors(['error' => $e->getMessage()]);
        }
    }

    /**
     * Generate bulk payroll for all employees
     */
    public function bulkGenerate(Request $request)
    {
        $request->validate([
            'pay_period_start' => 'required|date',
            'pay_period_end' => 'required|date|after_or_equal:pay_period_start',
            'pay_date' => 'required|date|after_or_equal:pay_period_end',
        ]);

        try {
            $startDate = Carbon::parse($request->pay_period_start);
            $endDate = Carbon::parse($request->pay_period_end);

            $payrolls = $this->payrollService->generateBulkPayroll($startDate, $endDate, [
                'pay_date' => Carbon::parse($request->pay_date),
            ]);

            return redirect()->route('hrm.payroll.index')
                ->with('success', 'Generated ' . count($payrolls) . ' payroll records successfully.');

        } catch (\Exception $e) {
            return back()->withErrors(['error' => $e->getMessage()]);
        }
    }

    /**
     * Bulk approve payroll records
     */
    public function bulkApprove(Request $request)
    {
        $request->validate([
            'payroll_ids' => 'required|array',
            'payroll_ids.*' => 'exists:payroll_records,id',
        ]);

        try {
            $approver = Employee::where('user_id', Auth::id())->firstOrFail();
            
            $approved = $this->payrollService->approveBulkPayroll($request->payroll_ids, $approver);

            return back()->with('success', 'Approved ' . count($approved) . ' payroll records successfully.');

        } catch (\Exception $e) {
            return back()->withErrors(['error' => $e->getMessage()]);
        }
    }

    /**
     * Bulk mark as paid
     */
    public function bulkMarkAsPaid(Request $request)
    {
        $request->validate([
            'payroll_ids' => 'required|array',
            'payroll_ids.*' => 'exists:payroll_records,id',
        ]);

        try {
            $paidBy = Employee::where('user_id', Auth::id())->firstOrFail();
            
            $paid = $this->payrollService->markBulkAsPaid($request->payroll_ids, $paidBy);

            return back()->with('success', 'Marked ' . count($paid) . ' payroll records as paid successfully.');

        } catch (\Exception $e) {
            return back()->withErrors(['error' => $e->getMessage()]);
        }
    }

    /**
     * Get payroll summary
     */
    public function summary(Request $request)
    {
        $request->validate([
            'start_date' => 'required|date',
            'end_date' => 'required|date|after_or_equal:start_date',
        ]);

        $startDate = Carbon::parse($request->start_date);
        $endDate = Carbon::parse($request->end_date);

        $summary = $this->payrollService->getPayrollSummary($startDate, $endDate);

        if ($request->expectsJson()) {
            return response()->json($summary);
        }

        return Inertia::render('Tenant/HRM/Payroll/Summary', [
            'summary' => $summary,
            'startDate' => $startDate->toDateString(),
            'endDate' => $endDate->toDateString(),
        ]);
    }

    /**
     * Export payroll data
     */
    public function export(Request $request)
    {
        $request->validate([
            'start_date' => 'required|date',
            'end_date' => 'required|date|after_or_equal:start_date',
            'format' => 'required|in:csv,excel,pdf',
            'employee_id' => 'nullable|exists:employees,id',
        ]);

        // This would typically use a job or export class
        // For now, return a simple response
        return response()->json([
            'message' => 'Export functionality will be implemented',
            'start_date' => $request->start_date,
            'end_date' => $request->end_date,
            'format' => $request->format,
        ]);
    }

    /**
     * Generate payslip PDF
     */
    public function payslip(PayrollRecord $payroll)
    {
        $payroll->load(['employee.department', 'employee.primaryBranch']);

        // This would typically generate a PDF
        // For now, return the payroll data
        return response()->json([
            'message' => 'Payslip PDF generation will be implemented',
            'payroll' => $payroll,
        ]);
    }
}
