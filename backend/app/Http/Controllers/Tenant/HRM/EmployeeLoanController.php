<?php

namespace App\Http\Controllers\Tenant\HRM;

use App\Http\Controllers\Controller;
use App\Models\Tenant\Employee;
use App\Models\Tenant\EmployeeLoan;
use App\Models\Tenant\LoanPayment;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Inertia\Inertia;
use Inertia\Response;
use Carbon\Carbon;

class EmployeeLoanController extends Controller
{
    public function __construct()
    {
        $this->middleware('auth');
        $this->middleware('verified');
    }

    /**
     * Display a listing of employee loans
     */
    public function index(Request $request): Response
    {
        $query = EmployeeLoan::with(['employee', 'approver', 'guarantor'])
                            ->latest();

        // Apply filters
        if ($request->filled('search')) {
            $query->where(function ($q) use ($request) {
                $q->where('loan_number', 'like', '%' . $request->search . '%')
                  ->orWhereHas('employee', function ($empQuery) use ($request) {
                      $empQuery->where('first_name', 'like', '%' . $request->search . '%')
                               ->orWhere('last_name', 'like', '%' . $request->search . '%');
                  });
            });
        }

        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }

        if ($request->filled('employee_id')) {
            $query->where('employee_id', $request->employee_id);
        }

        if ($request->filled('loan_type')) {
            $query->where('loan_type', $request->loan_type);
        }

        $loans = $query->paginate(15)->withQueryString();

        $stats = [
            'total_loans' => EmployeeLoan::count(),
            'active_loans' => EmployeeLoan::where('status', 'active')->count(),
            'pending_loans' => EmployeeLoan::where('status', 'pending')->count(),
            'completed_loans' => EmployeeLoan::where('status', 'completed')->count(),
            'total_amount' => EmployeeLoan::sum('loan_amount'),
            'outstanding_balance' => EmployeeLoan::where('status', 'active')->sum('outstanding_balance'),
        ];

        return Inertia::render('Tenant/HRM/Loans/Index', [
            'loans' => $loans,
            'stats' => $stats,
            'employees' => Employee::active()->select('id', 'first_name', 'last_name')->get(),
            'filters' => $request->only(['search', 'status', 'employee_id', 'loan_type']),
        ]);
    }

    /**
     * Show the form for creating a new loan
     */
    public function create(): Response
    {
        return Inertia::render('Tenant/HRM/Loans/Create', [
            'employees' => Employee::active()->with(['department', 'primaryBranch'])->get(),
            'loanTypes' => [
                ['value' => 'personal', 'label' => 'Personal Loan'],
                ['value' => 'emergency', 'label' => 'Emergency Loan'],
                ['value' => 'advance', 'label' => 'Salary Advance'],
                ['value' => 'medical', 'label' => 'Medical Loan'],
                ['value' => 'education', 'label' => 'Education Loan'],
                ['value' => 'other', 'label' => 'Other'],
            ],
        ]);
    }

    /**
     * Store a newly created loan
     */
    public function store(Request $request)
    {
        $request->validate([
            'employee_id' => 'required|exists:employees,id',
            'loan_type' => 'required|in:personal,emergency,advance,medical,education,other',
            'loan_amount' => 'required|numeric|min:1',
            'interest_rate' => 'required|numeric|min:0|max:100',
            'loan_term_months' => 'required|integer|min:1|max:120',
            'purpose' => 'required|string|max:1000',
            'guarantor_id' => 'nullable|exists:employees,id',
            'guarantor_name' => 'nullable|string|max:255',
            'guarantor_phone' => 'nullable|string|max:20',
            'guarantor_relationship' => 'nullable|string|max:100',
            'notes' => 'nullable|string|max:1000',
        ]);

        try {
            DB::beginTransaction();

            $loan = EmployeeLoan::create([
                'employee_id' => $request->employee_id,
                'loan_type' => $request->loan_type,
                'loan_amount' => $request->loan_amount,
                'interest_rate' => $request->interest_rate,
                'loan_term_months' => $request->loan_term_months,
                'purpose' => $request->purpose,
                'guarantor_id' => $request->guarantor_id,
                'guarantor_name' => $request->guarantor_name,
                'guarantor_phone' => $request->guarantor_phone,
                'guarantor_relationship' => $request->guarantor_relationship,
                'notes' => $request->notes,
                'status' => 'pending',
                'application_date' => now(),
            ]);

            // Calculate loan details
            $loan->calculateLoanDetails();
            $loan->save();

            DB::commit();

            return redirect()->route('hrm.loans.show', $loan)
                ->with('success', 'Loan application created successfully.');

        } catch (\Exception $e) {
            DB::rollBack();
            
            return back()
                ->withInput()
                ->withErrors(['error' => $e->getMessage()]);
        }
    }

    /**
     * Display the specified loan
     */
    public function show(EmployeeLoan $loan): Response
    {
        $loan->load(['employee.department', 'employee.primaryBranch', 'approver', 'guarantor', 'payments']);

        return Inertia::render('Tenant/HRM/Loans/Show', [
            'loan' => $loan,
        ]);
    }

    /**
     * Show the form for editing the specified loan
     */
    public function edit(EmployeeLoan $loan): Response
    {
        // Only allow editing of pending loans
        if ($loan->status !== 'pending') {
            return redirect()->route('hrm.loans.show', $loan)
                ->with('error', 'Only pending loans can be edited.');
        }

        return Inertia::render('Tenant/HRM/Loans/Edit', [
            'loan' => $loan,
            'employees' => Employee::active()->with(['department', 'primaryBranch'])->get(),
            'loanTypes' => [
                ['value' => 'personal', 'label' => 'Personal Loan'],
                ['value' => 'emergency', 'label' => 'Emergency Loan'],
                ['value' => 'advance', 'label' => 'Salary Advance'],
                ['value' => 'medical', 'label' => 'Medical Loan'],
                ['value' => 'education', 'label' => 'Education Loan'],
                ['value' => 'other', 'label' => 'Other'],
            ],
        ]);
    }

    /**
     * Update the specified loan
     */
    public function update(Request $request, EmployeeLoan $loan)
    {
        // Only allow updating of pending loans
        if ($loan->status !== 'pending') {
            return redirect()->route('hrm.loans.show', $loan)
                ->with('error', 'Only pending loans can be updated.');
        }

        $request->validate([
            'loan_type' => 'required|in:personal,emergency,advance,medical,education,other',
            'loan_amount' => 'required|numeric|min:1',
            'interest_rate' => 'required|numeric|min:0|max:100',
            'loan_term_months' => 'required|integer|min:1|max:120',
            'purpose' => 'required|string|max:1000',
            'guarantor_id' => 'nullable|exists:employees,id',
            'guarantor_name' => 'nullable|string|max:255',
            'guarantor_phone' => 'nullable|string|max:20',
            'guarantor_relationship' => 'nullable|string|max:100',
            'notes' => 'nullable|string|max:1000',
        ]);

        try {
            $loan->update($request->only([
                'loan_type', 'loan_amount', 'interest_rate', 'loan_term_months',
                'purpose', 'guarantor_id', 'guarantor_name', 'guarantor_phone',
                'guarantor_relationship', 'notes'
            ]));

            // Recalculate loan details
            $loan->calculateLoanDetails();
            $loan->save();

            return redirect()->route('hrm.loans.show', $loan)
                ->with('success', 'Loan updated successfully.');

        } catch (\Exception $e) {
            return back()
                ->withInput()
                ->withErrors(['error' => $e->getMessage()]);
        }
    }

    /**
     * Approve a loan
     */
    public function approve(Request $request, EmployeeLoan $loan)
    {
        if ($loan->status !== 'pending') {
            return back()->withErrors([
                'error' => 'Only pending loans can be approved.'
            ]);
        }

        $request->validate([
            'approved_amount' => 'nullable|numeric|min:1|max:' . $loan->loan_amount,
            'notes' => 'nullable|string|max:500',
        ]);

        try {
            $approver = Employee::where('user_id', Auth::id())->firstOrFail();
            
            $approvedAmount = $request->approved_amount ?? $loan->loan_amount;

            $loan->update([
                'status' => 'active',
                'approved_amount' => $approvedAmount,
                'outstanding_balance' => $approvedAmount,
                'approved_by' => $approver->id,
                'approved_date' => now(),
                'disbursement_date' => now(),
                'approval_notes' => $request->notes,
            ]);

            // Recalculate with approved amount
            if ($approvedAmount !== $loan->loan_amount) {
                $loan->loan_amount = $approvedAmount;
                $loan->calculateLoanDetails();
                $loan->save();
            }

            return redirect()->route('hrm.loans.show', $loan)
                ->with('success', 'Loan approved successfully.');

        } catch (\Exception $e) {
            return back()->withErrors(['error' => $e->getMessage()]);
        }
    }

    /**
     * Reject a loan
     */
    public function reject(Request $request, EmployeeLoan $loan)
    {
        if ($loan->status !== 'pending') {
            return back()->withErrors([
                'error' => 'Only pending loans can be rejected.'
            ]);
        }

        $request->validate([
            'reason' => 'required|string|max:500',
        ]);

        try {
            $rejector = Employee::where('user_id', Auth::id())->firstOrFail();

            $loan->update([
                'status' => 'rejected',
                'approved_by' => $rejector->id,
                'approved_date' => now(),
                'approval_notes' => $request->reason,
            ]);

            return redirect()->route('hrm.loans.show', $loan)
                ->with('success', 'Loan rejected.');

        } catch (\Exception $e) {
            return back()->withErrors(['error' => $e->getMessage()]);
        }
    }

    /**
     * Make a loan payment
     */
    public function makePayment(Request $request, EmployeeLoan $loan)
    {
        if ($loan->status !== 'active') {
            return back()->withErrors([
                'error' => 'Only active loans can receive payments.'
            ]);
        }

        $request->validate([
            'payment_amount' => 'required|numeric|min:0.01|max:' . $loan->outstanding_balance,
            'payment_method' => 'required|in:salary_deduction,cash,bank_transfer,other',
            'payment_date' => 'required|date|before_or_equal:today',
            'notes' => 'nullable|string|max:500',
        ]);

        try {
            DB::beginTransaction();

            $payment = LoanPayment::create([
                'employee_loan_id' => $loan->id,
                'payment_amount' => $request->payment_amount,
                'payment_method' => $request->payment_method,
                'payment_date' => Carbon::parse($request->payment_date),
                'notes' => $request->notes,
                'status' => 'paid',
            ]);

            // Update loan balance
            $loan->makePayment($request->payment_amount);

            DB::commit();

            return redirect()->route('hrm.loans.show', $loan)
                ->with('success', 'Payment recorded successfully.');

        } catch (\Exception $e) {
            DB::rollBack();
            
            return back()->withErrors(['error' => $e->getMessage()]);
        }
    }

    /**
     * Get loan payment schedule
     */
    public function paymentSchedule(EmployeeLoan $loan)
    {
        if ($loan->status === 'pending') {
            return response()->json([
                'error' => 'Payment schedule not available for pending loans.'
            ], 400);
        }

        $schedule = [];
        $balance = $loan->loan_amount;
        $monthlyPayment = $loan->monthly_installment;
        $interestRate = $loan->interest_rate / 100 / 12; // Monthly interest rate

        for ($month = 1; $month <= $loan->loan_term_months; $month++) {
            $interestPayment = $balance * $interestRate;
            $principalPayment = $monthlyPayment - $interestPayment;
            $balance -= $principalPayment;

            $schedule[] = [
                'month' => $month,
                'payment_amount' => round($monthlyPayment, 2),
                'principal_amount' => round($principalPayment, 2),
                'interest_amount' => round($interestPayment, 2),
                'remaining_balance' => round(max(0, $balance), 2),
                'due_date' => $loan->disbursement_date->copy()->addMonths($month)->format('Y-m-d'),
            ];

            if ($balance <= 0) {
                break;
            }
        }

        return response()->json([
            'loan' => $loan,
            'schedule' => $schedule,
        ]);
    }

    /**
     * Export loan data
     */
    public function export(Request $request)
    {
        $request->validate([
            'format' => 'required|in:csv,excel,pdf',
            'employee_id' => 'nullable|exists:employees,id',
            'status' => 'nullable|in:pending,active,completed,rejected',
        ]);

        // This would typically use a job or export class
        // For now, return a simple response
        return response()->json([
            'message' => 'Export functionality will be implemented',
            'format' => $request->format,
            'filters' => $request->only(['employee_id', 'status']),
        ]);
    }
}
