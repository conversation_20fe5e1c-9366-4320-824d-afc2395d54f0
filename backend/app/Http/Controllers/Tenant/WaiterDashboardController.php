<?php

namespace App\Http\Controllers\Tenant;

use App\Http\Controllers\Controller;
use App\Models\Tenant\Branch;
use App\Models\Tenant\Category;
use App\Models\Tenant\Customer;
use App\Models\Tenant\MenuItem;
use App\Models\Tenant\Order;
use App\Models\Tenant\Table;
use App\Models\Tenant\TableReservation;
use Illuminate\Http\Request;
use Inertia\Inertia;

class WaiterDashboardController extends Controller
{
    /**
     * Display the waiter dashboard
     */
    public function index(Request $request)
    {
        $waiter = auth('tenant')->user();
        
        // Get assigned tables
        // dd(Table::all());
        $assignedTables = Table::
        // where('assigned_waiter_id', $waiter->id)
        //     ->
            with(['currentOrder', 'reservation'])
            ->get();
        
        // Get active orders assigned to this waiter
        $activeOrders = Order::where('waiter_id', $waiter->id)
            ->whereIn('status', ['pending', 'confirmed', 'preparing', 'ready'])
            ->with(['customer', 'table', 'items.menuItem'])
            ->orderBy('created_at')
            ->get();
        
        // Get today's reservations for assigned tables
        $todayReservations = TableReservation::whereIn('table_id', $assignedTables->pluck('id'))
            ->whereDate('reservation_date', today())
            ->with(['customer', 'table'])
            ->orderBy('reservation_time')
            ->get();
        
        // Get pending orders that need attention
        $pendingOrders = Order::whereIn('status', ['ready', 'preparing'])
            ->where(function ($query) use ($waiter) {
                $query->where('waiter_id', $waiter->id)
                    ->orWhereIn('table_id', Table::where('assigned_waiter_id', $waiter->id)->pluck('id'));
            })
            ->with(['customer', 'table', 'items'])
            ->get();
        
        // Get waiter's performance metrics for today
        $todayMetrics = $this->getTodayMetrics($waiter);
        
        // Get menu items for quick order taking
        $menuItems = MenuItem::with(['category', 'variations', 'addons'])
            ->available()
            ->orderBy('sort_order')
            ->get()
            ->groupBy(function ($item) {
                return $item->category ? $item->category->name : 'Uncategorized';
            });

        return Inertia::render('Waiter/Dashboard', [
            'assignedTables' => $assignedTables,
            'activeOrders' => $activeOrders,
            'todayReservations' => $todayReservations,
            'pendingOrders' => $pendingOrders,
            'todayMetrics' => $todayMetrics,
            'menuItems' => $menuItems,
            'waiter' => $waiter,
        ]);
    }

    /**
     * Get today's metrics for the waiter
     */
    private function getTodayMetrics($waiter): array
    {
        $today = today();
        
        $ordersServed = Order::where('waiter_id', $waiter->id)
            ->whereDate('created_at', $today)
            ->count();
        
        $totalSales = Order::where('waiter_id', $waiter->id)
            ->whereDate('created_at', $today)
            ->where('payment_status', 'paid')
            ->sum('total_amount');
        
        $averageServiceTime = Order::where('waiter_id', $waiter->id)
            ->whereDate('created_at', $today)
            ->whereNotNull('served_at')
            ->selectRaw('AVG(TIMESTAMPDIFF(MINUTE, created_at, served_at)) as avg_time')
            ->value('avg_time');
        
        $tablesServed = Order::where('waiter_id', $waiter->id)
            ->whereDate('created_at', $today)
            ->distinct('table_id')
            ->count('table_id');

        return [
            'orders_served' => $ordersServed,
            'total_sales' => $totalSales,
            'average_service_time' => round($averageServiceTime ?? 0, 1),
            'tables_served' => $tablesServed,
        ];
    }

    /**
     * Get table management view
     */
    public function tables(Request $request)
    {
        $waiter = auth('tenant')->user();
        
        // Get all tables with their current status
        $tables = Table::with([
            'currentOrder.customer',
            'currentOrder.items.menuItem',
            'reservation' => function ($query) {
                $query->whereDate('reservation_date', today())
                    ->whereIn('status', ['confirmed', 'seated']);
            }
        ])->get();
        
        // Separate assigned and unassigned tables
        $assignedTables = $tables->where('assigned_waiter_id', $waiter->id);
        $availableTables = $tables->where('assigned_waiter_id', null)->where('status', 'available');

        return Inertia::render('Waiter/Tables', [
            'assignedTables' => $assignedTables,
            'availableTables' => $availableTables,
            'waiter' => $waiter,
        ]);
    }

    /**
     * Take a new order
     */
    public function takeOrder(Request $request)
    {
        $waiter = auth('tenant')->user();
        
        // Get available tables
        $availableTables = Table::where('status', 'available')
            ->orWhere('assigned_waiter_id', $waiter->id)
            ->get();
        
        // Get menu with categories
        $menuCategories = MenuItem::with(['category', 'variations', 'addons'])
            ->available()
            ->get()
            ->groupBy(function ($item) {
                return $item->category ? $item->category->name : 'Uncategorized';
            });
        
        // Get recent customers for quick selection
        $recentCustomers = Customer::whereHas('orders', function ($query) {
            $query->where('created_at', '>=', now()->subDays(30));
        })
            ->orderBy('last_order_at', 'desc')
            ->limit(20)
            ->get();

        return Inertia::render('Waiter/TakeOrder', [
            'availableTables' => $availableTables,
            'menuCategories' => $menuCategories,
            'recentCustomers' => $recentCustomers,
            'waiter' => $waiter,
        ]);
    }

    /**
     * View order details and manage order status
     */
    public function orderDetails(Order $order)
    {
        $order->load([
            'customer',
            'table',
            'items.menuItem',
            'items.addons.foodAddon',
            'statusHistory.changedBy'
        ]);

        return Inertia::render('Waiter/OrderDetails', [
            'order' => $order,
        ]);
    }

    /**
     * Update order status
     */
    public function updateOrderStatus(Request $request, Order $order)
    {
        $request->validate([
            'status' => 'required|in:confirmed,preparing,ready,served,completed',
            'notes' => 'nullable|string|max:500'
        ]);

        $waiter = auth('tenant')->user();

        // Update order status
        $order->updateStatus($request->status, $waiter, $request->notes);
        
        // If marking as served, update served_by
        if ($request->status === 'served') {
            $order->update(['served_by' => $waiter->id]);
        }

        return response()->json([
            'message' => 'Order status updated successfully',
            'order' => $order->fresh(['statusHistory.changedBy'])
        ]);
    }

    /**
     * Get customer information
     */
    public function customerInfo(Customer $customer)
    {
        $customer->load([
            'addresses',
            'orders' => function ($query) {
                $query->latest()->limit(5);
            }
        ]);

        return response()->json([
            'customer' => $customer
        ]);
    }

    /**
     * Get waiter's orders list
     */
    public function orders(Request $request)
    {
        $waiter = auth('tenant')->user();

        // Get orders assigned to this waiter with filtering
        $query = Order::where('waiter_id', $waiter->id)
            ->with(['customer', 'table', 'items.menuItem', 'payments']);

        // Apply status filter if provided
        if ($request->has('status') && $request->status !== 'all') {
            $query->where('status', $request->status);
        }

        // Apply date filter if provided
        if ($request->has('date')) {
            $query->whereDate('created_at', $request->date);
        } else {
            // Default to today's orders
            $query->whereDate('created_at', today());
        }

        $orders = $query->orderBy('created_at', 'desc')->paginate(20);

        // Get status counts for filter tabs
        $statusCounts = [
            'all' => Order::where('waiter_id', $waiter->id)->whereDate('created_at', today())->count(),
            'pending' => Order::where('waiter_id', $waiter->id)->where('status', 'pending')->whereDate('created_at', today())->count(),
            'confirmed' => Order::where('waiter_id', $waiter->id)->where('status', 'confirmed')->whereDate('created_at', today())->count(),
            'preparing' => Order::where('waiter_id', $waiter->id)->where('status', 'preparing')->whereDate('created_at', today())->count(),
            'ready' => Order::where('waiter_id', $waiter->id)->where('status', 'ready')->whereDate('created_at', today())->count(),
            'served' => Order::where('waiter_id', $waiter->id)->where('status', 'served')->whereDate('created_at', today())->count(),
            'completed' => Order::where('waiter_id', $waiter->id)->where('status', 'completed')->whereDate('created_at', today())->count(),
        ];

        return Inertia::render('Waiter/Orders', [
            'orders' => $orders,
            'statusCounts' => $statusCounts,
            'currentStatus' => $request->status ?? 'all',
            'currentDate' => $request->date ?? today()->format('Y-m-d'),
            'waiter' => $waiter,
        ]);
    }

    /**
     * Show create order form (reusing POS interface)
     */
    public function createOrder(Request $request)
    {
        $waiter = auth('tenant')->user();

        // Get the waiter's branch (assuming waiter belongs to a branch)
        $branch = Branch::active()->first(); // You might want to get this from waiter's assignment

        if (!$branch) {
            return redirect()->route('waiter.dashboard')
                ->with('error', 'No active branch found. Please contact your manager.');
        }

        $branch->load(['floors.tables']);

        // Get menu items available for this branch
        $categories = Category::active()
            ->with(['menuItems' => function ($query) use ($branch) {
                $query->with(['primaryMedia', 'mediaItems'])
                    ->whereHas('branches', function ($q) use ($branch) {
                        $q->where('branch_id', $branch->id)
                          ->where('is_available', true);
                    })->available()->ordered();
            }])
            ->ordered()
            ->get()
            ->filter(function ($category) {
                return $category->menuItems->isNotEmpty();
            });

        // Get customers for the dropdown
        $customers = Customer::orderBy('name')->limit(50)->get(['id', 'name', 'phone', 'email']);

        // Get tables available to this waiter
        $availableTables = Table::where(function ($query) use ($waiter) {
                $query->where('assigned_waiter_id', $waiter->id)
                    ->orWhereNull('assigned_waiter_id');
            })
            ->where('is_active', true)
            ->with(['floor'])
            ->get();

        return Inertia::render('Waiter/CreateOrder', [
            'branch' => $branch,
            'categories' => $categories,
            'floors' => $branch->floors,
            'availableTables' => $availableTables,
            'customers' => $customers,
            'waiter' => $waiter,
            'paymentMethods' => $this->getPaymentMethods(),
            'discountTypes' => $this->getDiscountTypes(),
        ]);
    }

    /**
     * Get payment methods
     */
    private function getPaymentMethods(): array
    {
        return [
            ['value' => 'cash', 'label' => 'Cash'],
            ['value' => 'card', 'label' => 'Credit/Debit Card'],
            ['value' => 'digital_wallet', 'label' => 'Digital Wallet'],
            ['value' => 'bank_transfer', 'label' => 'Bank Transfer'],
        ];
    }

    /**
     * Get discount types
     */
    private function getDiscountTypes(): array
    {
        return [
            ['value' => 'percentage', 'label' => 'Percentage (%)'],
            ['value' => 'fixed', 'label' => 'Fixed Amount'],
        ];
    }

    /**
     * Handle table assignment
     */
    public function assignTable(Request $request, Table $table)
    {
        $request->validate([
            'customer_id' => 'nullable|exists:customers,id',
            'party_size' => 'required|integer|min:1|max:20'
        ]);

        $waiter = auth('tenant')->user();

        // Assign table to waiter if not already assigned
        if (!$table->assigned_waiter_id) {
            $table->update(['assigned_waiter_id' => $waiter->id]);
        }
        
        // Update table status
        $table->update([
            'status' => 'occupied',
            'current_party_size' => $request->party_size,
            'occupied_at' => now()
        ]);

        return response()->json([
            'message' => 'Table assigned successfully',
            'table' => $table->fresh()
        ]);
    }

    /**
     * Clear table
     */
    public function clearTable(Table $table)
    {
        $table->update([
            'status' => 'available',
            'current_party_size' => null,
            'occupied_at' => null
        ]);

        return response()->json([
            'message' => 'Table cleared successfully',
            'table' => $table->fresh()
        ]);
    }

    /**
     * Get menu items for waiter
     */
    public function menu(Request $request)
    {
        $waiter = auth('tenant')->user();

        // Get menu items grouped by category
        $categories = Category::active()
            ->with(['menuItems' => function ($query) {
                $query->available()->ordered();
            }])
            ->ordered()
            ->get()
            ->filter(function ($category) {
                return $category->menuItems->isNotEmpty();
            });

        return Inertia::render('Waiter/Menu', [
            'categories' => $categories,
            'waiter' => $waiter,
        ]);
    }

    /**
     * Get customers for waiter
     */
    public function customers(Request $request)
    {
        $waiter = auth('tenant')->user();

        // Get customers with recent orders
        $customers = Customer::with(['orders' => function ($query) {
                $query->latest()->limit(5);
            }])
            ->orderBy('name')
            ->paginate(20);

        return Inertia::render('Waiter/Customers', [
            'customers' => $customers,
            'waiter' => $waiter,
        ]);
    }

    /**
     * Get reservations for waiter
     */
    public function reservations(Request $request)
    {
        $waiter = auth('tenant')->user();

        // Get today's reservations for tables assigned to this waiter
        $reservations = TableReservation::whereHas('table', function ($query) use ($waiter) {
                $query->where('assigned_waiter_id', $waiter->id);
            })
            ->whereDate('reservation_date', today())
            ->with(['customer', 'table'])
            ->orderBy('reservation_time')
            ->get();

        return Inertia::render('Waiter/Reservations', [
            'reservations' => $reservations,
            'waiter' => $waiter,
        ]);
    }
}
