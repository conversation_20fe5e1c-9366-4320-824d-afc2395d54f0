<?php

namespace App\Http\Controllers\Tenant;

use App\Http\Controllers\Controller;
use App\Models\Tenant\PaymentHistory;
use App\Models\Tenant\PurchaseOrder;
use App\Models\Tenant\Expense;
use App\Models\Tenant\Vendor;
use App\Models\Tenant\Branch;
use App\Models\Tenant\SupplierPaymentSummary;
use App\Services\Tenant\MediaService;
use Illuminate\Http\Request;
use Inertia\Inertia;
use Illuminate\Support\Facades\DB;

class PaymentHistoryController extends Controller
{
    protected MediaService $mediaService;

    public function __construct(MediaService $mediaService)
    {
        $this->mediaService = $mediaService;
        $this->middleware(['role:restaurant_manager']);
    }

    /**
     * Display a listing of payment history
     */
    public function index(Request $request)
    {
        $query = PaymentHistory::with(['branch', 'purchaseOrder', 'expense', 'supplier', 'creator']);

        // Search functionality
        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->where('payment_number', 'like', "%{$search}%")
                  ->orWhere('reference_number', 'like', "%{$search}%")
                  ->orWhereHas('supplier', function ($sq) use ($search) {
                      $sq->where('name', 'like', "%{$search}%");
                  });
            });
        }

        // Filter by status
        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }

        // Filter by payment method
        if ($request->filled('payment_method')) {
            $query->where('payment_method', $request->payment_method);
        }

        // Filter by branch
        if ($request->filled('branch_id')) {
            $query->where('branch_id', $request->branch_id);
        }

        // Filter by supplier
        if ($request->filled('supplier_id')) {
            $query->where('supplier_id', $request->supplier_id);
        }

        // Filter by date range
        if ($request->filled('start_date')) {
            $query->whereDate('payment_date', '>=', $request->start_date);
        }

        if ($request->filled('end_date')) {
            $query->whereDate('payment_date', '<=', $request->end_date);
        }

        // Filter by payment type
        if ($request->filled('payment_type')) {
            if ($request->payment_type === 'purchase_order') {
                $query->whereNotNull('purchase_order_id');
            } elseif ($request->payment_type === 'expense') {
                $query->whereNotNull('expense_id');
            }
        }

        // Sort
        $sortField = $request->get('sort', 'payment_date');
        $sortDirection = $request->get('direction', 'desc');
        $query->orderBy($sortField, $sortDirection);

        $payments = $query->paginate(20)->withQueryString();

        // Get filter options
        $branches = Branch::active()->ordered()->get(['id', 'name']);
        $suppliers = Vendor::active()->orderBy('name')->get(['id', 'name']);

        // Get summary statistics
        $stats = [
            'total_payments' => PaymentHistory::completed()->sum('amount'),
            'pending_payments' => PaymentHistory::pending()->sum('amount'),
            'failed_payments' => PaymentHistory::failed()->sum('amount'),
            'monthly_payments' => PaymentHistory::completed()
                                              ->whereMonth('payment_date', now()->month)
                                              ->whereYear('payment_date', now()->year)
                                              ->sum('amount'),
            'payment_count' => PaymentHistory::count(),
        ];

        return Inertia::render('Tenant/Accounting/Payments/Index', [
            'payments' => $payments,
            'filters' => $request->only(['search', 'status', 'payment_method', 'branch_id', 'supplier_id', 'start_date', 'end_date', 'payment_type', 'sort', 'direction']),
            'branches' => $branches,
            'suppliers' => $suppliers,
            'stats' => $stats,
            'paymentMethods' => [
                'cash' => 'Cash',
                'bank_transfer' => 'Bank Transfer',
                'check' => 'Check',
                'credit_card' => 'Credit Card',
                'debit_card' => 'Debit Card',
                'mobile_payment' => 'Mobile Payment',
                'other' => 'Other',
            ],
            'statuses' => ['pending', 'completed', 'failed', 'cancelled'],
            'paymentTypes' => [
                'purchase_order' => 'Purchase Order',
                'expense' => 'Expense',
            ],
        ]);
    }

    /**
     * Show the form for creating a new payment
     */
    public function create(Request $request)
    {
        $branches = Branch::active()->ordered()->get(['id', 'name']);
        $suppliers = Vendor::active()->orderBy('name')->get(['id', 'name']);

        // Get unpaid purchase orders and expenses
        $unpaidPOs = PurchaseOrder::whereIn('status', ['confirmed', 'delivered', 'invoiced'])
                                 ->with(['vendor'])
                                 ->orderBy('expected_delivery_date')
                                 ->get(['id', 'po_number', 'vendor_id', 'total_amount', 'expected_delivery_date']);

        $unpaidExpenses = Expense::where('status', 'approved')
                                ->whereNull('paid_at')
                                ->with(['supplier'])
                                ->orderBy('due_date')
                                ->get(['id', 'expense_number', 'supplier_id', 'total_amount', 'due_date']);

        return Inertia::render('Tenant/Accounting/Payments/Create', [
            'branches' => $branches,
            'suppliers' => $suppliers,
            'unpaidPOs' => $unpaidPOs,
            'unpaidExpenses' => $unpaidExpenses,
            'paymentMethods' => [
                'cash' => 'Cash',
                'bank_transfer' => 'Bank Transfer',
                'check' => 'Check',
                'credit_card' => 'Credit Card',
                'debit_card' => 'Debit Card',
                'mobile_payment' => 'Mobile Payment',
                'other' => 'Other',
            ],
        ]);
    }

    /**
     * Store a newly created payment
     */
    public function store(Request $request)
    {
        $request->validate([
            'branch_id' => 'required|exists:branches,id',
            'purchase_order_id' => 'nullable|exists:purchase_orders,id',
            'expense_id' => 'nullable|exists:expenses,id',
            'supplier_id' => 'required|exists:vendors,id',
            'amount' => 'required|numeric|min:0.01',
            'payment_method' => 'required|in:cash,bank_transfer,check,credit_card,debit_card,mobile_payment,other',
            'payment_date' => 'required|date',
            'reference_number' => 'nullable|string|max:255',
            'notes' => 'nullable|string|max:1000',
            'receipt_image' => 'nullable|string', // Media ID
        ]);

        // Validate that either PO or expense is provided
        if (!$request->purchase_order_id && !$request->expense_id) {
            return back()->withErrors(['error' => 'Either Purchase Order or Expense must be selected.']);
        }

        try {
            return DB::transaction(function () use ($request) {
                // Handle receipt image
                $receiptImage = null;
                if ($request->receipt_image) {
                    $media = $this->mediaService->getMediaById($request->receipt_image);
                    if ($media) {
                        $receiptImage = $media->file_path;
                    }
                }

                // Create payment
                $payment = PaymentHistory::create([
                    'branch_id' => $request->branch_id,
                    'purchase_order_id' => $request->purchase_order_id,
                    'expense_id' => $request->expense_id,
                    'supplier_id' => $request->supplier_id,
                    'created_by' => auth()->id(),
                    'amount' => $request->amount,
                    'payment_method' => $request->payment_method,
                    'payment_date' => $request->payment_date,
                    'reference_number' => $request->reference_number,
                    'notes' => $request->notes,
                    'receipt_image' => $receiptImage,
                    'status' => 'completed',
                ]);

                // Update related records
                if ($request->purchase_order_id) {
                    $po = PurchaseOrder::find($request->purchase_order_id);
                    if ($po && $po->total_amount <= $request->amount) {
                        $po->update(['status' => 'paid']);
                    }
                }

                if ($request->expense_id) {
                    $expense = Expense::find($request->expense_id);
                    if ($expense && $expense->total_amount <= $request->amount) {
                        $expense->update([
                            'status' => 'paid',
                            'paid_at' => now(),
                            'payment_method' => $request->payment_method,
                            'payment_reference' => $request->reference_number,
                        ]);
                    }
                }

                // Update supplier payment summary
                $summary = SupplierPaymentSummary::getOrCreateForSupplier($request->branch_id, $request->supplier_id);
                $summary->updateSummary();

                return redirect()->route('payments.show', $payment)
                    ->with('success', 'Payment recorded successfully.');
            });
        } catch (\Exception $e) {
            return back()->withErrors(['error' => 'Failed to record payment: ' . $e->getMessage()]);
        }
    }

    /**
     * Display the specified payment
     */
    public function show(PaymentHistory $payment)
    {
        $payment->load(['branch', 'purchaseOrder', 'expense', 'supplier', 'creator']);

        return Inertia::render('Tenant/Accounting/Payments/Show', [
            'payment' => $payment,
        ]);
    }

    /**
     * Show supplier payment summary
     */
    public function supplierSummary(Request $request)
    {
        $query = SupplierPaymentSummary::with(['branch', 'supplier']);

        // Filter by branch
        if ($request->filled('branch_id')) {
            $query->where('branch_id', $request->branch_id);
        }

        // Filter by payment status
        if ($request->filled('payment_status')) {
            switch ($request->payment_status) {
                case 'overdue':
                    $query->overdue();
                    break;
                case 'outstanding':
                    $query->outstanding();
                    break;
                case 'current':
                    $query->current();
                    break;
            }
        }

        // Sort
        $sortField = $request->get('sort', 'total_outstanding');
        $sortDirection = $request->get('direction', 'desc');
        $query->orderBy($sortField, $sortDirection);

        $summaries = $query->paginate(20)->withQueryString();

        // Get filter options
        $branches = Branch::active()->ordered()->get(['id', 'name']);

        // Get overall statistics
        $stats = [
            'total_outstanding' => SupplierPaymentSummary::sum('total_outstanding'),
            'total_overdue' => SupplierPaymentSummary::sum('total_overdue'),
            'total_paid' => SupplierPaymentSummary::sum('total_paid'),
            'overdue_suppliers' => SupplierPaymentSummary::overdue()->count(),
        ];

        return Inertia::render('Tenant/Accounting/Payments/SupplierSummary', [
            'summaries' => $summaries,
            'filters' => $request->only(['branch_id', 'payment_status', 'sort', 'direction']),
            'branches' => $branches,
            'stats' => $stats,
        ]);
    }

    /**
     * Export payment history
     */
    public function export(Request $request)
    {
        $query = PaymentHistory::with(['branch', 'purchaseOrder', 'expense', 'supplier']);

        // Apply same filters as index
        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }

        if ($request->filled('branch_id')) {
            $query->where('branch_id', $request->branch_id);
        }

        if ($request->filled('supplier_id')) {
            $query->where('supplier_id', $request->supplier_id);
        }

        if ($request->filled('start_date')) {
            $query->whereDate('payment_date', '>=', $request->start_date);
        }

        if ($request->filled('end_date')) {
            $query->whereDate('payment_date', '<=', $request->end_date);
        }

        $payments = $query->orderBy('payment_date', 'desc')->get();

        $csvData = [];
        $csvData[] = [
            'Payment Number',
            'Date',
            'Supplier',
            'Amount',
            'Payment Method',
            'Reference',
            'Type',
            'Document Number',
            'Status',
            'Branch',
            'Created At',
        ];

        foreach ($payments as $payment) {
            $csvData[] = [
                $payment->payment_number,
                $payment->payment_date->format('Y-m-d'),
                $payment->supplier?->name ?? '',
                '$' . number_format($payment->amount, 2),
                $payment->formatted_payment_method,
                $payment->reference_number ?? '',
                $payment->payment_type,
                $payment->document_number ?? '',
                ucfirst($payment->status),
                $payment->branch?->name ?? '',
                $payment->created_at->format('Y-m-d H:i:s'),
            ];
        }

        $filename = 'payment_history_' . now()->format('Y-m-d_H-i-s') . '.csv';
        
        $headers = [
            'Content-Type' => 'text/csv',
            'Content-Disposition' => "attachment; filename=\"{$filename}\"",
        ];

        $callback = function () use ($csvData) {
            $file = fopen('php://output', 'w');
            foreach ($csvData as $row) {
                fputcsv($file, $row);
            }
            fclose($file);
        };

        return response()->stream($callback, 200, $headers);
    }
}
