<?php

namespace App\Http\Controllers\Tenant;

use App\Http\Controllers\Controller;
use App\Models\User;
use Illuminate\Http\Request;
use Inertia\Inertia;
use Illuminate\Support\Facades\Hash;
use Illuminate\Validation\Rule;
use Spatie\Permission\Models\Role;

class EmployeeController extends Controller
{
    public function __construct()
    {
        $this->middleware(['role:admin|restaurant_manager']);
    }

    /**
     * Display a listing of employees
     */
    public function index(Request $request)
    {
        $query = User::query();

        // Search functionality
        if ($request->filled('search')) {
            $query->where(function ($q) use ($request) {
                $q->where('name', 'like', '%' . $request->search . '%')
                  ->orWhere('email', 'like', '%' . $request->search . '%')
                  ->orWhere('phone', 'like', '%' . $request->search . '%');
            });
        }

        // Filter by role
        if ($request->filled('role')) {
            $query->role($request->role);
        }

        // Filter by status
        if ($request->filled('status')) {
            $query->where('is_active', $request->status === 'active');
        }

        // Filter by department
        if ($request->filled('department')) {
            $query->where('department', $request->department);
        }

        $employees = $query->with(['roles'])
                          ->where('id', '!=', auth()->id()) // Exclude current user
                          ->orderBy('name')
                          ->paginate(15);

        // Check if roles table exists, if not provide default roles
        try {
            $roles = Role::where('name', '!=', 'admin')->get();
        } catch (\Exception $e) {
            // Fallback to default roles if table doesn't exist
            $roles = collect([
                (object)['id' => 1, 'name' => 'manager', 'display_name' => 'Manager'],
                (object)['id' => 2, 'name' => 'waiter', 'display_name' => 'Waiter'],
                (object)['id' => 3, 'name' => 'chef', 'display_name' => 'Chef'],
                (object)['id' => 4, 'name' => 'cashier', 'display_name' => 'Cashier'],
                (object)['id' => 5, 'name' => 'kitchen_staff', 'display_name' => 'Kitchen Staff'],
            ]);
        }
        $departments = ['kitchen', 'service', 'management', 'delivery', 'cleaning'];

        return Inertia::render('Tenant/Employees/Index', [
            'employees' => $employees,
            'roles' => $roles,
            'departments' => $departments,
            'filters' => $request->only(['search', 'role', 'status', 'department']),
            'stats' => [
                'total_employees' => User::count() - 1, // Exclude admin
                'active_employees' => User::where('is_active', true)->count() - 1,
                'on_shift' => User::where('is_on_shift', true)->count(),
            ]
        ]);
    }

    /**
     * Show the form for creating a new employee
     */
    public function create()
    {
        // Check if roles table exists, if not provide default roles
        try {
            $roles = Role::where('name', '!=', 'admin')->get();
        } catch (\Exception $e) {
            // Fallback to default roles if table doesn't exist
            $roles = collect([
                (object)['id' => 1, 'name' => 'manager', 'display_name' => 'Manager'],
                (object)['id' => 2, 'name' => 'waiter', 'display_name' => 'Waiter'],
                (object)['id' => 3, 'name' => 'chef', 'display_name' => 'Chef'],
                (object)['id' => 4, 'name' => 'cashier', 'display_name' => 'Cashier'],
                (object)['id' => 5, 'name' => 'kitchen_staff', 'display_name' => 'Kitchen Staff'],
            ]);
        }
        $departments = ['kitchen', 'service', 'management', 'delivery', 'cleaning'];

        return Inertia::render('Tenant/Employees/Create', [
            'roles' => $roles,
            'departments' => $departments,
        ]);
    }

    /**
     * Store a newly created employee
     */
    public function store(Request $request)
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'email' => 'required|email|unique:users,email',
            'phone' => 'nullable|string|max:20',
            'password' => 'required|string|min:8|confirmed',
            'role' => 'required|exists:roles,name',
            'department' => 'nullable|string|max:100',
            'position' => 'nullable|string|max:100',
            'hire_date' => 'required|date',
            'salary' => 'nullable|numeric|min:0',
            'hourly_rate' => 'nullable|numeric|min:0',
            'address' => 'nullable|string|max:500',
            'emergency_contact_name' => 'nullable|string|max:255',
            'emergency_contact_phone' => 'nullable|string|max:20',
            'notes' => 'nullable|string|max:1000',
            'is_active' => 'boolean',
        ]);

        $employee = User::create([
            'name' => $request->name,
            'email' => $request->email,
            'phone' => $request->phone,
            'password' => Hash::make($request->password),
            'department' => $request->department,
            'position' => $request->position,
            'hire_date' => $request->hire_date,
            'salary' => $request->salary,
            'hourly_rate' => $request->hourly_rate,
            'address' => $request->address,
            'emergency_contact_name' => $request->emergency_contact_name,
            'emergency_contact_phone' => $request->emergency_contact_phone,
            'notes' => $request->notes,
            'is_active' => $request->boolean('is_active', true),
            'preferred_language' => 'en',
            'theme_preference' => 'light',
        ]);

        $employee->assignRole($request->role);

        return redirect()->route('tenant.employees.index')
            ->with('success', 'Employee created successfully.');
    }

    /**
     * Display the specified employee
     */
    public function show(User $employee)
    {
        $employee->load(['roles', 'shifts' => function ($query) {
            $query->latest()->limit(10);
        }]);

        // Calculate employee statistics
        $stats = [
            'total_shifts' => $employee->shifts()->count(),
            'total_hours_worked' => $employee->shifts()->sum('hours_worked'),
            'average_hours_per_week' => $employee->shifts()
                ->where('date', '>=', now()->subWeeks(4))
                ->avg('hours_worked') * 7,
            'attendance_rate' => $this->calculateAttendanceRate($employee),
        ];

        return Inertia::render('Tenant/Employees/Show', [
            'employee' => $employee,
            'stats' => $stats,
        ]);
    }

    /**
     * Show the form for editing the specified employee
     */
    public function edit(User $employee)
    {
        $employee->load('roles');

        // Check if roles table exists, if not provide default roles
        try {
            $roles = Role::where('name', '!=', 'admin')->get();
        } catch (\Exception $e) {
            // Fallback to default roles if table doesn't exist
            $roles = collect([
                (object)['id' => 1, 'name' => 'manager', 'display_name' => 'Manager'],
                (object)['id' => 2, 'name' => 'waiter', 'display_name' => 'Waiter'],
                (object)['id' => 3, 'name' => 'chef', 'display_name' => 'Chef'],
                (object)['id' => 4, 'name' => 'cashier', 'display_name' => 'Cashier'],
                (object)['id' => 5, 'name' => 'kitchen_staff', 'display_name' => 'Kitchen Staff'],
            ]);
        }
        $departments = ['kitchen', 'service', 'management', 'delivery', 'cleaning'];

        return Inertia::render('Tenant/Employees/Edit', [
            'employee' => $employee,
            'roles' => $roles,
            'departments' => $departments,
        ]);
    }

    /**
     * Update the specified employee
     */
    public function update(Request $request, User $employee)
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'email' => ['required', 'email', Rule::unique('users')->ignore($employee->id)],
            'phone' => 'nullable|string|max:20',
            'role' => 'required|exists:roles,name',
            'department' => 'nullable|string|max:100',
            'position' => 'nullable|string|max:100',
            'hire_date' => 'required|date',
            'salary' => 'nullable|numeric|min:0',
            'hourly_rate' => 'nullable|numeric|min:0',
            'address' => 'nullable|string|max:500',
            'emergency_contact_name' => 'nullable|string|max:255',
            'emergency_contact_phone' => 'nullable|string|max:20',
            'notes' => 'nullable|string|max:1000',
            'is_active' => 'boolean',
        ]);

        $employee->update($request->only([
            'name', 'email', 'phone', 'department', 'position', 'hire_date',
            'salary', 'hourly_rate', 'address', 'emergency_contact_name',
            'emergency_contact_phone', 'notes', 'is_active'
        ]));

        // Update role if changed
        if ($employee->roles->first()->name !== $request->role) {
            $employee->syncRoles([$request->role]);
        }

        return redirect()->route('tenant.employees.index')
            ->with('success', 'Employee updated successfully.');
    }

    /**
     * Remove the specified employee
     */
    public function destroy(User $employee)
    {
        // Prevent deleting the current user
        if ($employee->id === auth()->id()) {
            return back()->withErrors(['employee' => 'You cannot delete your own account.']);
        }

        // Check if employee has active shifts
        if ($employee->shifts()->where('status', 'active')->exists()) {
            return back()->withErrors([
                'employee' => 'Cannot delete employee with active shifts. Please end all shifts first.'
            ]);
        }

        $employee->delete();

        return redirect()->route('tenant.employees.index')
            ->with('success', 'Employee deleted successfully.');
    }

    /**
     * Toggle employee status
     */
    public function toggleStatus(User $employee)
    {
        $employee->update(['is_active' => !$employee->is_active]);

        $status = $employee->is_active ? 'activated' : 'deactivated';
        
        return response()->json([
            'success' => true,
            'message' => "Employee {$status} successfully.",
            'is_active' => $employee->is_active,
        ]);
    }

    /**
     * Reset employee password
     */
    public function resetPassword(Request $request, User $employee)
    {
        $request->validate([
            'password' => 'required|string|min:8|confirmed',
        ]);

        $employee->update([
            'password' => Hash::make($request->password),
        ]);

        return response()->json([
            'success' => true,
            'message' => 'Password reset successfully.',
        ]);
    }

    /**
     * Get employee performance data
     */
    public function getPerformance(User $employee)
    {
        // Mock performance data - replace with actual calculations
        $performance = [
            'attendance_rate' => $this->calculateAttendanceRate($employee),
            'punctuality_score' => 95, // Calculate based on clock-in times
            'productivity_score' => 88, // Calculate based on tasks/orders completed
            'customer_rating' => 4.5, // Average customer feedback
            'monthly_performance' => [],
        ];

        return response()->json($performance);
    }

    /**
     * Calculate attendance rate
     */
    private function calculateAttendanceRate(User $employee)
    {
        // Mock calculation - replace with actual logic
        $scheduledShifts = 20; // Get from shifts table
        $attendedShifts = 18; // Get from time entries
        
        return $scheduledShifts > 0 ? ($attendedShifts / $scheduledShifts) * 100 : 0;
    }

    /**
     * Export employee data
     */
    public function export(Request $request)
    {
        $request->validate([
            'format' => 'required|in:csv,excel,pdf',
            'include_inactive' => 'boolean',
        ]);

        // This would implement actual export logic
        return response()->json([
            'success' => true,
            'message' => 'Employee data export will be sent to your email shortly.',
        ]);
    }

    /**
     * Bulk update employees
     */
    public function bulkUpdate(Request $request)
    {
        $request->validate([
            'employee_ids' => 'required|array',
            'employee_ids.*' => 'exists:users,id',
            'action' => 'required|in:activate,deactivate,delete,update_role',
            'role' => 'required_if:action,update_role|exists:roles,name',
        ]);

        $employees = User::whereIn('id', $request->employee_ids);

        switch ($request->action) {
            case 'activate':
                $employees->update(['is_active' => true]);
                $message = 'Employees activated successfully.';
                break;
            case 'deactivate':
                $employees->update(['is_active' => false]);
                $message = 'Employees deactivated successfully.';
                break;
            case 'delete':
                $employees->delete();
                $message = 'Employees deleted successfully.';
                break;
            case 'update_role':
                foreach ($employees->get() as $employee) {
                    $employee->syncRoles([$request->role]);
                }
                $message = 'Employee roles updated successfully.';
                break;
        }

        return response()->json([
            'success' => true,
            'message' => $message,
        ]);
    }
}
