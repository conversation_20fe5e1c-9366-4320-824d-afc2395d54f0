<?php

namespace App\Http\Controllers\Tenant;

use App\Http\Controllers\Controller;
use App\Models\Tenant\Media;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;
use Intervention\Image\ImageManager;
use Intervention\Image\Drivers\Gd\Driver;
use Intervention\Image\Facades\Image;

class MediaController extends Controller
{
    public function __construct()
    {
        $this->middleware('auth');
        $this->middleware('verified');
    }

    /**
     * Display a listing of the user's media.
     */
    public function index(Request $request)
    {
        // Get all media for the media library, but group by file_name to avoid showing duplicates
        $query = Media::query();

        // Group by file_name and select the first record for each unique file
        // This prevents showing multiple copies of the same file in the library
        $query->select('*')
              ->whereIn('id', function($subQuery) {
                  $subQuery->select(DB::raw('MIN(id)'))
                           ->from('media')
                           ->groupBy('file_name');
              });

        // Apply search filter if provided
        if ($request->has('search') && !empty($request->search)) {
            $search = $request->search;
            $query->where('name', 'like', "%{$search}%");
        }

        // Apply file type filter if provided
        if ($request->has('type') && !empty($request->type)) {
            $query->where('mime_type', 'like', $request->type . '%');
        }

        // Pagination
        $perPage = $request->input('per_page', 20);
        $media = $query->latest()->paginate($perPage);

        // Transform each media item to ensure URLs are properly generated
        $items = $media->items();

        // Log the first item for debugging
        if (count($items) > 0) {
            Log::debug('First media item:', [
                'id' => $items[0]->id,
                'name' => $items[0]->name,
                'thumbnail_path' => $items[0]->thumbnail_path,
                'thumbnail_url' => $items[0]->thumbnail_url,
                'file_path' => $items[0]->file_name,
                'url' => $items[0]->url
            ]);
        }

        return response()->json([
            'data' => $items,
            'current_page' => $media->currentPage(),
            'last_page' => $media->lastPage(),
            'per_page' => $media->perPage(),
            'total' => $media->total(),
            'meta' => [
                'pagination' => [
                    'total' => $media->total(),
                    'per_page' => $media->perPage(),
                    'current_page' => $media->currentPage(),
                    'last_page' => $media->lastPage(),
                    'from' => $media->firstItem(),
                    'to' => $media->lastItem()
                ]
            ]
        ]);
    }

    /**
     * Store a newly created media in storage.
     */
    public function store(Request $request)
    {
        // Check if user is authenticated
        if (!Auth::check()) {
            return response()->json(['message' => 'Unauthenticated'], 401);
        }

        $userId = Auth::id();
        if (!$userId) {
            return response()->json(['message' => 'User ID not found'], 401);
        }

        $request->validate([
            'file' => 'nullable|file|mimes:jpeg,png,jpg,gif|max:10240', // 10MB max
            'files.*' => 'nullable|file|mimes:jpeg,png,jpg,gif|max:10240', // 10MB max
            'name' => 'nullable|string|max:255'
        ]);

        try {
            $uploadedMedia = [];
            $files = [];

            // Handle single file upload
            if ($request->hasFile('file')) {
                $files[] = $request->file('file');
            }

            // Handle multiple files upload
            if ($request->hasFile('files')) {
                $files = array_merge($files, $request->file('files'));
            }

            if (empty($files)) {
                return response()->json(['message' => 'No files uploaded'], 400);
            }

            // Create image manager instance with desired driver
            $manager = new ImageManager(new Driver());

            // Process each file
            foreach ($files as $file) {
                $fileType = $file->getMimeType();
                $fileSize = $file->getSize();
                $name = pathinfo($file->getClientOriginalName(), PATHINFO_FILENAME);

                // Create directories for WebP and thumbnails only
                $webpPath = 'media/webp/' . date('Y/m');
                $thumbnailPath = 'media/thumbnails/' . date('Y/m');

                // Process image to WebP format directly from uploaded file
                $image = $manager->read($file);
                $webpFileName = uniqid() . '.webp'; // Generate unique WebP filename

                // Save WebP version (this is our primary storage)
                $webpFilePath = $webpPath . '/' . $webpFileName;
                Storage::disk('public')->put(
                    $webpFilePath,
                    $image->toWebp(80)->toString()
                );

                // Create thumbnail from the same image object
                $thumbnail = $image->cover(300, 300);
                $thumbnailFilePath = $thumbnailPath . '/' . $webpFileName;
                Storage::disk('public')->put(
                    $thumbnailFilePath,
                    $thumbnail->toWebp(60)->toString()
                );

                // Save media record to database (using 2025_05_28_200000 schema)
                $media = Media::create([
                    'name' => $name,
                    'file_name' => $webpFilePath, // Use WebP as primary and only storage
                    'mime_type' => 'image/webp', // Always WebP since we convert everything
                    'size' => $fileSize, // Keep original file size for reference
                    'disk' => 'public',
                    'manipulations' => [],
                    'custom_properties' => [
                        'original_name' => $file->getClientOriginalName(),
                        'original_mime_type' => $fileType, // Store original format for reference
                        'thumbnail_path' => $thumbnailFilePath,
                        'dimensions' => [
                            'width' => $image->width(),
                            'height' => $image->height()
                        ]
                    ],
                    'generated_conversions' => [],
                    'responsive_images' => [],
                    'uploaded_by' => $userId,
                ]);

                $uploadedMedia[] = $media;
            }

            // Return JSON response for API consistency
            return response()->json([
                'success' => true,
                'message' => 'Media uploaded successfully',
                'data' => $uploadedMedia
            ], 201);
        } catch (\Exception $e) {
            Log::error('Media upload failed: ' . $e->getMessage());
            return response()->json([
                'success' => false,
                'message' => 'Failed to upload media: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Display the specified media.
     */
    public function show($id)
    {
        $media = Media::findOrFail($id);

        // Check if user owns this media
        if ($media->uploaded_by !== Auth::id()) {
            return response()->json(['message' => 'Unauthorized'], 403);
        }

        return response()->json($media);
    }

    /**
     * Update media metadata.
     */
    public function update(Request $request, Media $media)
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'alt_text' => 'nullable|string|max:255',
            'caption' => 'nullable|string',
            'tags' => 'nullable|array',
            'tags.*' => 'string|max:50',
            'folder' => 'nullable|string|max:255',
            'is_featured' => 'boolean',
        ]);

        $media->update($request->only([
            'name', 'alt_text', 'caption', 'tags', 'folder', 'is_featured'
        ]));

        return back()->with('success', 'Media updated successfully.');
    }

    /**
     * Remove the specified media from storage.
     */
    public function destroy($id)
    {
        $media = Media::findOrFail($id);

        // Check if user owns this media
        if ($media->uploaded_by !== Auth::id()) {
            return response()->json(['message' => 'Unauthorized'], 403);
        }

        // Delete files from storage (only WebP files since we don't store originals)
        Storage::disk('public')->delete($media->file_name);
        if (isset($media->custom_properties['thumbnail_path'])) {
            Storage::disk('public')->delete($media->custom_properties['thumbnail_path']);
        }

        // Delete database record
        $media->delete();

        return response()->json(['message' => 'Media deleted successfully']);
    }

    /**
     * Bulk delete media.
     */
    public function bulkDelete(Request $request)
    {
        $request->validate([
            'ids' => 'required|array',
            'ids.*' => 'exists:media,id',
        ]);

        Media::whereIn('id', $request->ids)->each(function ($media) {
            $media->delete();
        });

        return back()->with('success', count($request->ids) . ' media files deleted successfully.');
    }

    /**
     * Get media for picker.
     */
    public function picker(Request $request)
    {
        $query = Media::query();

        if ($request->search) {
            $query->search($request->search);
        }

        if ($request->folder) {
            $query->inFolder($request->folder);
        }

        if ($request->type === 'images') {
            $query->images();
        }

        $media = $query->latest()->paginate(12);

        return response()->json($media);
    }

    /**
     * Create folder.
     */
    public function createFolder(Request $request)
    {
        $request->validate([
            'name' => 'required|string|max:255|unique:media,folder',
        ]);

        return response()->json([
            'success' => true,
            'folder' => $request->name,
        ]);
    }

    /**
     * Check if file type is allowed.
     */
    protected function isAllowedFileType($file): bool
    {
        $allowedMimes = [
            'image/jpeg',
            'image/png',
            'image/gif',
            'image/svg+xml',
            'image/webp',
            'video/mp4',
            'video/mpeg',
            'video/quicktime',
            'application/pdf',
            'application/msword',
            'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
        ];

        return in_array($file->getMimeType(), $allowedMimes);
    }

    /**
     * Generate unique file name.
     */
    protected function generateFileName($file, string $tenantId): string
    {
        $extension = $file->getClientOriginalExtension();
        $name = Str::slug(pathinfo($file->getClientOriginalName(), PATHINFO_FILENAME));
        $timestamp = now()->format('Y-m-d-H-i-s');
        $random = Str::random(8);

        return "{$name}-{$timestamp}-{$random}.{$extension}";
    }

    /**
     * Serve tenant-specific assets (images, files)
     */
    public function serveAsset($path)
    {
        try {
            // Ensure we're in tenant context
            if (!tenancy()->initialized) {
                abort(404);
            }

            // Check if file exists in tenant storage
            if (!Storage::disk('public')->exists($path)) {
                abort(404);
            }

            // Get the file
            $file = Storage::disk('public')->get($path);

            // Determine mime type based on file extension
            $extension = pathinfo($path, PATHINFO_EXTENSION);
            $mimeType = match(strtolower($extension)) {
                'webp' => 'image/webp',
                'jpg', 'jpeg' => 'image/jpeg',
                'png' => 'image/png',
                'gif' => 'image/gif',
                'svg' => 'image/svg+xml',
                'pdf' => 'application/pdf',
                default => 'application/octet-stream'
            };

            // Return the file with appropriate headers
            return response($file, 200)
                ->header('Content-Type', $mimeType)
                ->header('Cache-Control', 'public, max-age=31536000') // Cache for 1 year
                ->header('Expires', now()->addYear()->toRfc7231String());

        } catch (\Exception $e) {
            Log::error('Failed to serve tenant asset: ' . $e->getMessage());
            abort(404);
        }
    }

    /**
     * Generate image conversions.
     */
    protected function generateConversions(Media $media): void
    {
        if (!$media->isImage()) {
            return;
        }

        $conversions = [
            'thumbnail' => ['width' => 150, 'height' => 150],
            'medium' => ['width' => 500, 'height' => 500],
            'large' => ['width' => 1200, 'height' => 1200],
        ];

        $generatedConversions = [];
        $originalPath = Storage::disk($media->disk)->path($media->file_name);

        foreach ($conversions as $name => $dimensions) {
            try {
                $image = Image::make($originalPath);

                $image->resize($dimensions['width'], $dimensions['height'], function ($constraint) {
                    $constraint->aspectRatio();
                    $constraint->upsize();
                });

                $conversionPath = str_replace(
                    pathinfo($media->file_name, PATHINFO_BASENAME),
                    $name . '/' . pathinfo($media->file_name, PATHINFO_BASENAME),
                    $media->file_name
                );

                $fullConversionPath = Storage::disk($media->disk)->path($conversionPath);
                $conversionDir = dirname($fullConversionPath);

                if (!is_dir($conversionDir)) {
                    mkdir($conversionDir, 0755, true);
                }

                $image->save($fullConversionPath, 85);
                $generatedConversions[$name] = true;

            } catch (\Exception $e) {
                Log::error("Failed to generate {$name} conversion for media {$media->id}: " . $e->getMessage());
            }
        }

        $media->update(['generated_conversions' => $generatedConversions]);
    }
}
