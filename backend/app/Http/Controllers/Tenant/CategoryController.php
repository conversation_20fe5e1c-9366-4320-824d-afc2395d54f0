<?php

namespace App\Http\Controllers\Tenant;

use App\Http\Controllers\Controller;
use App\Models\Tenant\Category;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Inertia\Inertia;
use Illuminate\Support\Str;

class CategoryController extends Controller
{
    /**
     * Display a listing of categories.
     */
    public function index(Request $request)
    {
        $query = Category::with(['media', 'menuItems', 'parent', 'activeSubcategories.menuItems']);
        
        // Search
        if ($request->search) {
            $query->where(function ($q) use ($request) {
                $q->where('name', 'like', '%' . $request->search . '%')
                  ->orWhere('description', 'like', '%' . $request->search . '%');
            });
        }

        // Filter by status
        if ($request->status) {
            if ($request->status === 'active') {
                $query->where('is_active', true);
            } elseif ($request->status === 'inactive') {
                $query->where('is_active', false);
            }
        }

        $categories = $query->ordered()->paginate(20);

        return Inertia::render('Tenant/Categories/Index', [
            'categories' => $categories,
            'filters' => $request->only(['search', 'status']),
        ]);
    }

    /**
     * Show the form for creating a new category.
     */
    public function create()
    {
        // Get existing categories for parent selection (excluding current category to prevent circular references)
        $categories = Category::active()->ordered()->get();

        return Inertia::render('Tenant/Categories/Create', [
            'categories' => $categories,
        ]);
    }

    /**
     * Store a newly created category.
     */
    public function store(Request $request)
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'description' => 'nullable|string',
            'parent_id' => 'nullable|exists:categories,id',
            'sort_order' => 'nullable|integer|min:0',
            'is_active' => 'boolean',
            'media_ids' => 'nullable|array',
            'media_ids.*' => 'exists:media,id',
        ]);

        $category = Category::create([
            'name' => $request->name,
            'slug' => Str::slug($request->name),
            'description' => $request->description,
            'parent_id' => $request->parent_id,
            'sort_order' => $request->sort_order ?? 0,
            'is_active' => $request->boolean('is_active', true),
        ]);

        // Attach media
        if ($request->media_ids) {
            foreach ($request->media_ids as $mediaId) {
                $category->addMediaFromLibrary($mediaId, 'images');
            }
        }

        return redirect()->route('categories.index')
            ->with('success', 'Category created successfully.');
    }

    /**
     * Display the specified category.
     */
    public function show(Category $category)
    {
        $category->load([
            'media',
            'menuItems' => function ($query) {
                $query->with('media')->active()->ordered();
            },
            'activeSubcategories' => function ($query) {
                $query->with(['menuItems' => function ($q) {
                    $q->with('media')->active()->ordered();
                }]);
            }
        ]);

        return Inertia::render('Tenant/Categories/Show', [
            'category' => $category,
        ]);
    }

    /**
     * Show the form for editing the specified category.
     */
    public function edit(Category $category)
    {
        $category->load(['media']);

        return Inertia::render('Tenant/Categories/Edit', [
            'category' => $category,
        ]);
    }

    /**
     * Update the specified category.
     */
    public function update(Request $request, Category $category)
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'description' => 'nullable|string',
            'sort_order' => 'nullable|integer|min:0',
            'is_active' => 'boolean',
            'media_ids' => 'nullable|array',
            'media_ids.*' => 'exists:media,id',
        ]);

        $category->update([
            'name' => $request->name,
            'slug' => Str::slug($request->name),
            'description' => $request->description,
            'sort_order' => $request->sort_order ?? 0,
            'is_active' => $request->boolean('is_active'),
        ]);

        // Update media
        $category->clearMediaCollection('images');
        if ($request->media_ids) {
            foreach ($request->media_ids as $mediaId) {
                $category->addMediaFromLibrary($mediaId, 'images');
            }
        }

        return redirect()->route('categories.index')
            ->with('success', 'Category updated successfully.');
    }

    /**
     * Remove the specified category.
     */
    public function destroy(Category $category)
    {
        // Check if category has menu items
        if ($category->menuItems()->exists()) {
            return back()->withErrors(['error' => 'Cannot delete category with menu items. Please move or delete the menu items first.']);
        }

        $category->delete();

        return redirect()->route('categories.index')
            ->with('success', 'Category deleted successfully.');
    }

    /**
     * Toggle category status.
     */
    public function toggleStatus(Category $category)
    {
        $category->update(['is_active' => !$category->is_active]);

        $status = $category->is_active ? 'activated' : 'deactivated';
        
        return response()->json([
            'success' => true,
            'message' => "Category {$status} successfully.",
            'is_active' => $category->is_active,
        ]);
    }

    /**
     * Reorder categories.
     */
    public function reorder(Request $request)
    {
        $request->validate([
            'categories' => 'required|array',
            'categories.*.id' => 'required|exists:categories,id',
            'categories.*.sort_order' => 'required|integer|min:0',
        ]);

        foreach ($request->categories as $categoryData) {
            Category::where('id', $categoryData['id'])
                ->update(['sort_order' => $categoryData['sort_order']]);
        }

        return response()->json([
            'success' => true,
            'message' => 'Categories reordered successfully.',
        ]);
    }

    /**
     * Bulk update categories.
     */
    public function bulkUpdate(Request $request)
    {
        try {
            Log::info('Bulk update request received', [
                'ids' => $request->ids,
                'action' => $request->action,
                'all_data' => $request->all()
            ]);

            $request->validate([
                'ids' => 'required|array',
                'ids.*' => 'exists:categories,id',
                'action' => 'required|in:activate,deactivate,delete',
            ]);

            Log::info('Validation passed');
        } catch (\Exception $e) {
            Log::error('Bulk update error', ['error' => $e->getMessage()]);

            if ($request->expectsJson()) {
                return response()->json(['error' => $e->getMessage()], 422);
            }

            return back()->withErrors(['error' => $e->getMessage()]);
        }

        $categories = Category::whereIn('id', $request->ids);

        switch ($request->action) {
            case 'activate':
                $categories->update(['is_active' => true]);
                $message = 'Categories activated successfully.';
                break;
            case 'deactivate':
                $categories->update(['is_active' => false]);
                $message = 'Categories deactivated successfully.';
                break;
            case 'delete':
                // Check if any category has menu items
                $categoriesWithMenuItems = [];
                $categoriesToDelete = Category::whereIn('id', $request->ids)->get();

                foreach ($categoriesToDelete as $category) {
                    $menuItemsCount = $category->menuItems()->count();
                    Log::info("Category {$category->id} ({$category->name}) has {$menuItemsCount} menu items");

                    if ($menuItemsCount > 0) {
                        $categoriesWithMenuItems[] = $category->name;
                    }
                }

                if (!empty($categoriesWithMenuItems)) {
                    $errorMessage = 'Cannot delete categories with menu items: ' . implode(', ', $categoriesWithMenuItems);
                    Log::info('Blocking deletion due to menu items', ['categories' => $categoriesWithMenuItems]);

                    if ($request->expectsJson()) {
                        return response()->json(['error' => $errorMessage], 422);
                    }

                    return back()->withErrors(['error' => $errorMessage]);
                }

                // Delete categories that don't have menu items
                $deletedCount = $categoriesToDelete->count();
                foreach ($categoriesToDelete as $category) {
                    $category->delete();
                }

                Log::info("Successfully deleted {$deletedCount} categories");
                $message = "Successfully deleted {$deletedCount} categories.";
                break;
        }

        if ($request->expectsJson()) {
            return response()->json(['success' => true, 'message' => $message]);
        }

        return back()->with('success', $message);
    }

    /**
     * Get categories for API.
     */
    public function api()
    {
        $categories = Category::with(['activeMenuItems' => function ($query) {
            $query->available()->ordered();
        }])
        ->active()
        ->ordered()
        ->get();

        return response()->json($categories);
    }
}
