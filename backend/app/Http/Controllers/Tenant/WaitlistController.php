<?php

namespace App\Http\Controllers\Tenant;

use App\Http\Controllers\Controller;
use App\Models\Tenant\Waitlist;
use App\Models\Tenant\Table;
use App\Models\Tenant\Customer;
use App\Models\Tenant\Restaurant;
use Illuminate\Http\Request;
use Inertia\Inertia;

class WaitlistController extends Controller
{
    /**
     * Display a listing of waitlist entries.
     */
    public function index(Request $request)
    {
        $query = Waitlist::with(['customer', 'creator']);

        // Search
        if ($request->search) {
            $query->where(function ($q) use ($request) {
                $q->where('waitlist_number', 'like', '%' . $request->search . '%')
                  ->orWhere('customer_name', 'like', '%' . $request->search . '%')
                  ->orWhere('customer_phone', 'like', '%' . $request->search . '%');
            });
        }

        // Filter by status
        if ($request->status) {
            $query->where('status', $request->status);
        }

        // Filter by party size
        if ($request->party_size) {
            $query->where('party_size', $request->party_size);
        }

        // Filter by date
        if ($request->date) {
            $query->whereDate('joined_at', $request->date);
        } else {
            // Default to today
            $query->today();
        }

        $waitlistEntries = $query->orderBy('joined_at')->paginate(20);

        // Get statistics
        $stats = [
            'total_waiting' => Waitlist::active()->count(),
            'average_wait_time' => $this->getAverageWaitTime(),
            'longest_wait' => $this->getLongestWaitTime(),
            'tables_available' => Table::where('status', 'available')->count(),
        ];

        return Inertia::render('Tenant/Waitlist/Index', [
            'waitlistEntries' => $waitlistEntries,
            'stats' => $stats,
            'filters' => $request->only(['search', 'status', 'party_size', 'date']),
            'statusOptions' => $this->getStatusOptions(),
        ]);
    }

    /**
     * Show the live waitlist board.
     */
    public function board()
    {
        $waitingCustomers = Waitlist::with(['customer'])
            ->active()
            ->orderBy('joined_at')
            ->get()
            ->map(function ($entry) {
                return [
                    'id' => $entry->id,
                    'waitlist_number' => $entry->waitlist_number,
                    'customer_name' => $entry->customer_name,
                    'party_size' => $entry->party_size,
                    'wait_time' => $entry->current_wait_time,
                    'estimated_wait_time' => $entry->estimated_wait_time,
                    'queue_position' => $entry->queue_position,
                ];
            });

        $availableTables = Table::where('status', 'available')->count();

        return Inertia::render('Tenant/Waitlist/Board', [
            'waitingCustomers' => $waitingCustomers,
            'availableTables' => $availableTables,
        ]);
    }

    /**
     * Show the form for creating a new waitlist entry.
     */
    public function create()
    {
        $restaurant = Restaurant::first();
        $customers = Customer::active()->latest()->take(50)->get();

        return Inertia::render('Tenant/Waitlist/Create', [
            'restaurant' => $restaurant,
            'customers' => $customers,
        ]);
    }

    /**
     * Store a newly created waitlist entry.
     */
    public function store(Request $request)
    {
        $request->validate([
            'customer_id' => 'nullable|exists:customers,id',
            'customer_name' => 'required|string|max:255',
            'customer_phone' => 'required|string|max:20',
            'party_size' => 'required|integer|min:1|max:20',
            'preferred_table_type' => 'nullable|string|max:255',
            'special_requests' => 'nullable|string',
            'notes' => 'nullable|string',
        ]);

        $restaurant = Restaurant::first();

        // Create or update customer
        $customer = null;
        if ($request->customer_id) {
            $customer = Customer::find($request->customer_id);
        } else {
            $customer = Customer::firstOrCreate(
                ['phone' => $request->customer_phone],
                [
                    'restaurant_id' => $restaurant->id,
                    'first_name' => explode(' ', $request->customer_name)[0],
                    'last_name' => implode(' ', array_slice(explode(' ', $request->customer_name), 1)),
                ]
            );
        }

        // Check if customer is already on waitlist
        $existingEntry = Waitlist::where('customer_phone', $request->customer_phone)
            ->active()
            ->first();

        if ($existingEntry) {
            return back()->withErrors(['customer_phone' => 'Customer is already on the waitlist.']);
        }

        // Create waitlist entry
        $waitlistEntry = Waitlist::create([
            'restaurant_id' => $restaurant->id,
            'customer_id' => $customer->id,
            'customer_name' => $request->customer_name,
            'customer_phone' => $request->customer_phone,
            'party_size' => $request->party_size,
            'preferred_table_type' => $request->preferred_table_type,
            'special_requests' => $request->special_requests,
            'notes' => $request->notes,
            'created_by' => auth()->id(),
        ]);

        return redirect()->route('waitlist.index')
            ->with('success', "Customer added to waitlist. Position: #{$waitlistEntry->queue_position}");
    }

    /**
     * Display the specified waitlist entry.
     */
    public function show(Waitlist $waitlist)
    {
        $waitlist->load(['customer', 'creator', 'restaurant']);

        return Inertia::render('Tenant/Waitlist/Show', [
            'waitlist' => $waitlist,
        ]);
    }

    /**
     * Show the form for editing the specified waitlist entry.
     */
    public function edit(Waitlist $waitlist)
    {
        $waitlist->load(['customer']);
        $restaurant = Restaurant::first();
        $customers = Customer::active()->latest()->take(50)->get();

        return Inertia::render('Tenant/Waitlist/Edit', [
            'waitlist' => $waitlist,
            'restaurant' => $restaurant,
            'customers' => $customers,
        ]);
    }

    /**
     * Update the specified waitlist entry.
     */
    public function update(Request $request, Waitlist $waitlist)
    {
        $request->validate([
            'customer_name' => 'required|string|max:255',
            'customer_phone' => 'required|string|max:20',
            'party_size' => 'required|integer|min:1|max:20',
            'preferred_table_type' => 'nullable|string|max:255',
            'estimated_wait_time' => 'nullable|integer|min:0',
            'special_requests' => 'nullable|string',
            'notes' => 'nullable|string',
            'status' => 'required|in:waiting,notified,seated,left,no_show',
        ]);

        $waitlist->update([
            'customer_name' => $request->customer_name,
            'customer_phone' => $request->customer_phone,
            'party_size' => $request->party_size,
            'preferred_table_type' => $request->preferred_table_type,
            'estimated_wait_time' => $request->estimated_wait_time,
            'special_requests' => $request->special_requests,
            'notes' => $request->notes,
            'status' => $request->status,
        ]);

        return redirect()->route('waitlist.index')
            ->with('success', 'Waitlist entry updated successfully.');
    }

    /**
     * Remove the specified waitlist entry.
     */
    public function destroy(Waitlist $waitlist)
    {
        $waitlist->delete();

        return redirect()->route('waitlist.index')
            ->with('success', 'Waitlist entry removed successfully.');
    }

    /**
     * Notify customer that table is ready.
     */
    public function notify(Waitlist $waitlist)
    {
        $waitlist->notifyCustomer();

        return response()->json([
            'success' => true,
            'message' => 'Customer notified successfully.',
        ]);
    }

    /**
     * Mark customer as seated.
     */
    public function seat(Request $request, Waitlist $waitlist)
    {
        $request->validate([
            'table_id' => 'required|exists:tables,id',
        ]);

        $table = Table::find($request->table_id);
        
        // Update table status
        $table->update(['status' => 'occupied']);
        
        // Mark customer as seated
        $waitlist->markSeated();

        return response()->json([
            'success' => true,
            'message' => 'Customer seated successfully.',
        ]);
    }

    /**
     * Mark customer as left.
     */
    public function markLeft(Waitlist $waitlist)
    {
        $waitlist->markLeft();

        return response()->json([
            'success' => true,
            'message' => 'Customer marked as left.',
        ]);
    }

    /**
     * Mark customer as no-show.
     */
    public function markNoShow(Waitlist $waitlist)
    {
        $waitlist->markNoShow();

        return response()->json([
            'success' => true,
            'message' => 'Customer marked as no-show.',
        ]);
    }

    /**
     * Update estimated wait times for all waiting customers.
     */
    public function updateWaitTimes(Request $request)
    {
        $request->validate([
            'entries' => 'required|array',
            'entries.*.id' => 'required|exists:waitlists,id',
            'entries.*.estimated_wait_time' => 'required|integer|min:0',
        ]);

        foreach ($request->entries as $entry) {
            Waitlist::where('id', $entry['id'])
                ->update(['estimated_wait_time' => $entry['estimated_wait_time']]);
        }

        return response()->json([
            'success' => true,
            'message' => 'Wait times updated successfully.',
        ]);
    }

    /**
     * Get next customer in queue for specific party size.
     */
    public function nextCustomer(Request $request)
    {
        $request->validate([
            'party_size' => 'nullable|integer|min:1',
            'table_capacity' => 'nullable|integer|min:1',
        ]);

        $query = Waitlist::active()->orderBy('joined_at');

        if ($request->party_size) {
            $query->where('party_size', $request->party_size);
        } elseif ($request->table_capacity) {
            $query->where('party_size', '<=', $request->table_capacity);
        }

        $nextCustomer = $query->first();

        return response()->json([
            'customer' => $nextCustomer,
        ]);
    }

    /**
     * Get available tables for party size.
     */
    public function availableTables(Request $request)
    {
        $request->validate([
            'party_size' => 'required|integer|min:1',
        ]);

        $tables = Table::where('status', 'available')
            ->where('capacity', '>=', $request->party_size)
            ->where('min_capacity', '<=', $request->party_size)
            ->orderBy('capacity')
            ->get();

        return response()->json($tables);
    }

    /**
     * Get status options.
     */
    protected function getStatusOptions(): array
    {
        return [
            ['value' => 'waiting', 'label' => 'Waiting'],
            ['value' => 'notified', 'label' => 'Notified'],
            ['value' => 'seated', 'label' => 'Seated'],
            ['value' => 'left', 'label' => 'Left'],
            ['value' => 'no_show', 'label' => 'No Show'],
        ];
    }

    /**
     * Get average wait time.
     */
    protected function getAverageWaitTime(): int
    {
        $seatedToday = Waitlist::where('status', 'seated')
            ->whereDate('joined_at', today())
            ->get();

        if ($seatedToday->isEmpty()) {
            return 0;
        }

        $totalWaitTime = $seatedToday->sum(function ($entry) {
            return $entry->joined_at->diffInMinutes($entry->seated_at);
        });

        return (int) ($totalWaitTime / $seatedToday->count());
    }

    /**
     * Get longest current wait time.
     */
    protected function getLongestWaitTime(): int
    {
        $longestWaiting = Waitlist::active()
            ->orderBy('joined_at')
            ->first();

        return $longestWaiting ? $longestWaiting->current_wait_time : 0;
    }
}
