<?php

namespace App\Http\Controllers\Tenant;

use App\Http\Controllers\Controller;
use App\Models\Tenant\ExpenseCategory;
use App\Models\Tenant\Restaurant;
use Illuminate\Http\Request;
use Inertia\Inertia;

class ExpenseCategoryController extends Controller
{
    /**
     * Display a listing of expense categories.
     */
    public function index(Request $request)
    {
        $query = ExpenseCategory::with(['restaurant', 'parent', 'children', 'media']);

        // Search
        if ($request->search) {
            $query->where(function ($q) use ($request) {
                $q->where('name', 'like', '%' . $request->search . '%')
                  ->orWhere('description', 'like', '%' . $request->search . '%');
            });
        }

        // Filter by status
        if ($request->status) {
            if ($request->status === 'active') {
                $query->where('is_active', true);
            } elseif ($request->status === 'inactive') {
                $query->where('is_active', false);
            }
        }

        // Filter by parent category
        if ($request->parent_id) {
            $query->where('parent_id', $request->parent_id);
        } elseif ($request->has('parent_only')) {
            $query->whereNull('parent_id');
        }

        $categories = $query->ordered()->paginate(20);

        // Get parent categories for filter
        $parentCategories = ExpenseCategory::parents()->active()->ordered()->get();

        return Inertia::render('Tenant/ExpenseCategories/Index', [
            'categories' => $categories,
            'parentCategories' => $parentCategories,
            'filters' => $request->only(['search', 'status', 'parent_id', 'parent_only']),
        ]);
    }

    /**
     * Show the form for creating a new expense category.
     */
    public function create()
    {
        $restaurant = Restaurant::first();
        $parentCategories = ExpenseCategory::parents()->active()->ordered()->get();

        return Inertia::render('Tenant/ExpenseCategories/Create', [
            'restaurant' => $restaurant,
            'parentCategories' => $parentCategories,
        ]);
    }

    /**
     * Store a newly created expense category.
     */
    public function store(Request $request)
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'description' => 'nullable|string',
            'color_code' => 'nullable|string|regex:/^#[0-9A-Fa-f]{6}$/',
            'icon' => 'nullable|string|max:255',
            'budget_limit' => 'nullable|numeric|min:0',
            'parent_id' => 'nullable|exists:expense_categories,id',
            'sort_order' => 'nullable|integer|min:0',
            'is_active' => 'boolean',
            'media_ids' => 'nullable|array',
            'media_ids.*' => 'exists:media,id',
        ]);

        $restaurant = Restaurant::first();

        $category = ExpenseCategory::create([
            'restaurant_id' => $restaurant->id,
            'name' => $request->name,
            'description' => $request->description,
            'color_code' => $request->color_code,
            'icon' => $request->icon,
            'budget_limit' => $request->budget_limit,
            'parent_id' => $request->parent_id,
            'sort_order' => $request->sort_order ?? 0,
            'is_active' => $request->boolean('is_active', true),
        ]);

        // Attach media
        if ($request->media_ids) {
            foreach ($request->media_ids as $mediaId) {
                $category->addMediaFromLibrary($mediaId, 'images');
            }
        }

        return redirect()->route('expense-categories.index')
            ->with('success', 'Expense category created successfully.');
    }

    /**
     * Display the specified expense category.
     */
    public function show(ExpenseCategory $expenseCategory)
    {
        $expenseCategory->load([
            'restaurant', 'parent', 'children', 'media',
            'expenses' => function ($query) {
                $query->with(['vendor', 'creator'])->latest()->take(10);
            }
        ]);

        // Get category statistics
        $stats = [
            'total_expenses' => $expenseCategory->getTotalExpenses('month'),
            'budget_utilization' => $expenseCategory->budget_utilization,
            'remaining_budget' => $expenseCategory->remaining_budget,
            'expense_trend' => $expenseCategory->getExpenseTrend('month'),
        ];

        return Inertia::render('Tenant/ExpenseCategories/Show', [
            'category' => $expenseCategory,
            'stats' => $stats,
        ]);
    }

    /**
     * Show the form for editing the specified expense category.
     */
    public function edit(ExpenseCategory $expenseCategory)
    {
        $expenseCategory->load(['media']);
        $restaurant = Restaurant::first();
        $parentCategories = ExpenseCategory::parents()
            ->active()
            ->where('id', '!=', $expenseCategory->id)
            ->ordered()
            ->get();

        return Inertia::render('Tenant/ExpenseCategories/Edit', [
            'category' => $expenseCategory,
            'restaurant' => $restaurant,
            'parentCategories' => $parentCategories,
        ]);
    }

    /**
     * Update the specified expense category.
     */
    public function update(Request $request, ExpenseCategory $expenseCategory)
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'description' => 'nullable|string',
            'color_code' => 'nullable|string|regex:/^#[0-9A-Fa-f]{6}$/',
            'icon' => 'nullable|string|max:255',
            'budget_limit' => 'nullable|numeric|min:0',
            'parent_id' => 'nullable|exists:expense_categories,id|not_in:' . $expenseCategory->id,
            'sort_order' => 'nullable|integer|min:0',
            'is_active' => 'boolean',
            'media_ids' => 'nullable|array',
            'media_ids.*' => 'exists:media,id',
        ]);

        $expenseCategory->update([
            'name' => $request->name,
            'description' => $request->description,
            'color_code' => $request->color_code,
            'icon' => $request->icon,
            'budget_limit' => $request->budget_limit,
            'parent_id' => $request->parent_id,
            'sort_order' => $request->sort_order ?? 0,
            'is_active' => $request->boolean('is_active'),
        ]);

        // Update media
        $expenseCategory->clearMediaCollection('images');
        if ($request->media_ids) {
            foreach ($request->media_ids as $mediaId) {
                $expenseCategory->addMediaFromLibrary($mediaId, 'images');
            }
        }

        return redirect()->route('expense-categories.index')
            ->with('success', 'Expense category updated successfully.');
    }

    /**
     * Remove the specified expense category.
     */
    public function destroy(ExpenseCategory $expenseCategory)
    {
        // Check if category has expenses
        if ($expenseCategory->expenses()->exists()) {
            return back()->withErrors(['error' => 'Cannot delete category with existing expenses.']);
        }

        // Check if category has child categories
        if ($expenseCategory->children()->exists()) {
            return back()->withErrors(['error' => 'Cannot delete category with subcategories. Please delete or move subcategories first.']);
        }

        $expenseCategory->delete();

        return redirect()->route('expense-categories.index')
            ->with('success', 'Expense category deleted successfully.');
    }

    /**
     * Toggle category status.
     */
    public function toggleStatus(ExpenseCategory $expenseCategory)
    {
        $expenseCategory->update(['is_active' => !$expenseCategory->is_active]);

        $status = $expenseCategory->is_active ? 'activated' : 'deactivated';
        
        return response()->json([
            'success' => true,
            'message' => "Expense category {$status} successfully.",
            'is_active' => $expenseCategory->is_active,
        ]);
    }

    /**
     * Reorder categories.
     */
    public function reorder(Request $request)
    {
        $request->validate([
            'categories' => 'required|array',
            'categories.*.id' => 'required|exists:expense_categories,id',
            'categories.*.sort_order' => 'required|integer|min:0',
        ]);

        foreach ($request->categories as $categoryData) {
            ExpenseCategory::where('id', $categoryData['id'])
                ->update(['sort_order' => $categoryData['sort_order']]);
        }

        return response()->json([
            'success' => true,
            'message' => 'Categories reordered successfully.',
        ]);
    }

    /**
     * Bulk update categories.
     */
    public function bulkUpdate(Request $request)
    {
        $request->validate([
            'ids' => 'required|array',
            'ids.*' => 'exists:expense_categories,id',
            'action' => 'required|in:activate,deactivate,delete',
            'budget_limit' => 'nullable|numeric|min:0',
        ]);

        $categories = ExpenseCategory::whereIn('id', $request->ids);

        switch ($request->action) {
            case 'activate':
                $categories->update(['is_active' => true]);
                $message = 'Categories activated successfully.';
                break;
            case 'deactivate':
                $categories->update(['is_active' => false]);
                $message = 'Categories deactivated successfully.';
                break;
            case 'delete':
                // Check if any category has expenses or children
                $hasExpenses = $categories->whereHas('expenses')->exists();
                $hasChildren = $categories->whereHas('children')->exists();
                
                if ($hasExpenses) {
                    return back()->withErrors(['error' => 'Cannot delete categories with existing expenses.']);
                }
                if ($hasChildren) {
                    return back()->withErrors(['error' => 'Cannot delete categories with subcategories.']);
                }
                
                $categories->delete();
                $message = 'Categories deleted successfully.';
                break;
        }

        // Update budget limit if provided
        if ($request->budget_limit !== null && $request->action !== 'delete') {
            ExpenseCategory::whereIn('id', $request->ids)->update(['budget_limit' => $request->budget_limit]);
        }

        return back()->with('success', $message);
    }

    /**
     * Get category hierarchy for API.
     */
    public function hierarchy()
    {
        $categories = ExpenseCategory::with(['children' => function ($query) {
            $query->active()->ordered();
        }])
        ->parents()
        ->active()
        ->ordered()
        ->get();

        return response()->json($categories);
    }

    /**
     * Get budget overview for all categories.
     */
    public function budgetOverview()
    {
        $categories = ExpenseCategory::active()
            ->whereNotNull('budget_limit')
            ->where('budget_limit', '>', 0)
            ->get()
            ->map(function ($category) {
                return [
                    'id' => $category->id,
                    'name' => $category->hierarchy_path,
                    'budget_limit' => $category->budget_limit,
                    'spent_amount' => $category->getTotalExpenses('month'),
                    'remaining_budget' => $category->remaining_budget,
                    'utilization_percentage' => $category->budget_utilization,
                    'status' => $category->isBudgetExceeded() ? 'exceeded' : 'within_budget',
                    'color_code' => $category->color_code,
                ];
            });

        return Inertia::render('Tenant/ExpenseCategories/BudgetOverview', [
            'categories' => $categories,
        ]);
    }
}
