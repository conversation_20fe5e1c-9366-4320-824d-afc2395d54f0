<?php

namespace App\Http\Controllers\Tenant;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Inertia\Inertia;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;

class ReportController extends Controller
{
    public function __construct()
    {
        $this->middleware(['role:admin|restaurant_manager']);
    }

    /**
     * Display reports dashboard
     */
    public function index()
    {
        return Inertia::render('Tenant/Reports/Index', [
            'reportTypes' => [
                'sales' => 'Sales Reports',
                'inventory' => 'Inventory Reports',
                'staff' => 'Staff Reports',
                'customer' => 'Customer Reports',
                'financial' => 'Financial Reports',
                'operational' => 'Operational Reports',
            ],
            'quickStats' => $this->getQuickStats(),
        ]);
    }

    /**
     * Generate sales report
     */
    public function salesReport(Request $request)
    {
        $request->validate([
            'date_from' => 'required|date',
            'date_to' => 'required|date|after_or_equal:date_from',
            'group_by' => 'nullable|in:day,week,month',
            'include_cancelled' => 'boolean',
        ]);

        $dateFrom = Carbon::parse($request->date_from);
        $dateTo = Carbon::parse($request->date_to);
        $groupBy = $request->get('group_by', 'day');

        // Mock data - replace with actual order queries
        $salesData = $this->generateMockSalesData($dateFrom, $dateTo, $groupBy);

        return Inertia::render('Tenant/Reports/Sales', [
            'salesData' => $salesData,
            'filters' => $request->only(['date_from', 'date_to', 'group_by', 'include_cancelled']),
            'summary' => [
                'total_revenue' => $salesData['chartData']->sum('revenue'),
                'total_orders' => $salesData['chartData']->sum('orders'),
                'average_order_value' => $salesData['chartData']->avg('average_order_value'),
                'growth_rate' => $this->calculateGrowthRate($salesData['chartData']),
            ],
        ]);
    }

    /**
     * Generate inventory report
     */
    public function inventoryReport(Request $request)
    {
        $request->validate([
            'category' => 'nullable|string',
            'low_stock_only' => 'boolean',
            'include_inactive' => 'boolean',
        ]);

        // Mock inventory data
        $inventoryData = [
            'items' => collect([
                [
                    'name' => 'Chicken Breast',
                    'category' => 'Meat',
                    'current_stock' => 25,
                    'minimum_stock' => 10,
                    'unit' => 'kg',
                    'cost_per_unit' => 8.50,
                    'total_value' => 212.50,
                    'status' => 'in_stock',
                ],
                [
                    'name' => 'Tomatoes',
                    'category' => 'Vegetables',
                    'current_stock' => 5,
                    'minimum_stock' => 15,
                    'unit' => 'kg',
                    'cost_per_unit' => 3.00,
                    'total_value' => 15.00,
                    'status' => 'low_stock',
                ],
            ]),
            'summary' => [
                'total_items' => 150,
                'low_stock_items' => 12,
                'out_of_stock_items' => 3,
                'total_inventory_value' => 15420.50,
            ],
            'categories' => ['Meat', 'Vegetables', 'Dairy', 'Beverages', 'Spices'],
        ];

        return Inertia::render('Tenant/Reports/Inventory', [
            'inventoryData' => $inventoryData,
            'filters' => $request->only(['category', 'low_stock_only', 'include_inactive']),
        ]);
    }

    /**
     * Generate staff report
     */
    public function staffReport(Request $request)
    {
        $request->validate([
            'date_from' => 'required|date',
            'date_to' => 'required|date|after_or_equal:date_from',
            'department' => 'nullable|string',
            'include_inactive' => 'boolean',
        ]);

        // Mock staff data
        $staffData = [
            'attendance' => [
                'total_employees' => 25,
                'present_today' => 22,
                'absent_today' => 3,
                'late_arrivals' => 2,
                'early_departures' => 1,
            ],
            'productivity' => collect([
                [
                    'employee' => 'John Doe',
                    'department' => 'Kitchen',
                    'hours_worked' => 168,
                    'orders_completed' => 245,
                    'efficiency_score' => 92,
                    'customer_rating' => 4.8,
                ],
                [
                    'employee' => 'Jane Smith',
                    'department' => 'Service',
                    'hours_worked' => 160,
                    'orders_served' => 180,
                    'efficiency_score' => 88,
                    'customer_rating' => 4.6,
                ],
            ]),
            'payroll' => [
                'total_hours' => 3200,
                'total_payroll' => 48000,
                'overtime_hours' => 120,
                'overtime_pay' => 2400,
            ],
        ];

        return Inertia::render('Tenant/Reports/Staff', [
            'staffData' => $staffData,
            'filters' => $request->only(['date_from', 'date_to', 'department', 'include_inactive']),
        ]);
    }

    /**
     * Generate customer report
     */
    public function customerReport(Request $request)
    {
        $request->validate([
            'date_from' => 'required|date',
            'date_to' => 'required|date|after_or_equal:date_from',
            'customer_type' => 'nullable|in:new,returning,vip',
        ]);

        // Mock customer data
        $customerData = [
            'overview' => [
                'total_customers' => 1250,
                'new_customers' => 85,
                'returning_customers' => 1165,
                'customer_retention_rate' => 93.2,
                'average_order_frequency' => 2.3,
            ],
            'demographics' => [
                'age_groups' => [
                    '18-25' => 15,
                    '26-35' => 35,
                    '36-45' => 30,
                    '46-55' => 15,
                    '55+' => 5,
                ],
                'order_preferences' => [
                    'dine_in' => 45,
                    'takeaway' => 35,
                    'delivery' => 20,
                ],
            ],
            'top_customers' => collect([
                [
                    'name' => 'Alice Johnson',
                    'total_orders' => 45,
                    'total_spent' => 1250.00,
                    'average_order_value' => 27.78,
                    'last_order' => '2024-05-27',
                ],
                [
                    'name' => 'Bob Wilson',
                    'total_orders' => 38,
                    'total_spent' => 980.00,
                    'average_order_value' => 25.79,
                    'last_order' => '2024-05-26',
                ],
            ]),
        ];

        return Inertia::render('Tenant/Reports/Customer', [
            'customerData' => $customerData,
            'filters' => $request->only(['date_from', 'date_to', 'customer_type']),
        ]);
    }

    /**
     * Generate financial report
     */
    public function financialReport(Request $request)
    {
        $request->validate([
            'date_from' => 'required|date',
            'date_to' => 'required|date|after_or_equal:date_from',
            'include_taxes' => 'boolean',
        ]);

        // Mock financial data
        $financialData = [
            'revenue' => [
                'gross_revenue' => 125000.00,
                'net_revenue' => 112500.00,
                'taxes' => 12500.00,
                'discounts' => 5000.00,
                'refunds' => 2500.00,
            ],
            'expenses' => [
                'food_costs' => 37500.00,
                'labor_costs' => 35000.00,
                'rent' => 8000.00,
                'utilities' => 2500.00,
                'marketing' => 3000.00,
                'other' => 5000.00,
            ],
            'profit' => [
                'gross_profit' => 87500.00,
                'net_profit' => 21000.00,
                'profit_margin' => 18.7,
            ],
            'payment_methods' => [
                'cash' => 25000.00,
                'card' => 85000.00,
                'digital_wallet' => 15000.00,
            ],
        ];

        return Inertia::render('Tenant/Reports/Financial', [
            'financialData' => $financialData,
            'filters' => $request->only(['date_from', 'date_to', 'include_taxes']),
        ]);
    }

    /**
     * Generate operational report
     */
    public function operationalReport(Request $request)
    {
        $request->validate([
            'date_from' => 'required|date',
            'date_to' => 'required|date|after_or_equal:date_from',
        ]);

        // Mock operational data
        $operationalData = [
            'kitchen_performance' => [
                'average_prep_time' => 12.5,
                'order_accuracy' => 96.8,
                'food_waste_percentage' => 3.2,
                'peak_hours' => ['12:00-13:00', '19:00-20:00'],
            ],
            'service_metrics' => [
                'average_service_time' => 8.3,
                'table_turnover_rate' => 2.8,
                'customer_satisfaction' => 4.6,
                'complaint_rate' => 1.2,
            ],
            'delivery_metrics' => [
                'average_delivery_time' => 28.5,
                'on_time_delivery_rate' => 92.3,
                'delivery_radius_coverage' => 85.0,
                'driver_efficiency' => 88.5,
            ],
            'equipment_status' => [
                'operational' => 18,
                'maintenance_required' => 2,
                'out_of_order' => 1,
            ],
        ];

        return Inertia::render('Tenant/Reports/Operational', [
            'operationalData' => $operationalData,
            'filters' => $request->only(['date_from', 'date_to']),
        ]);
    }

    /**
     * Export report data
     */
    public function exportReport(Request $request)
    {
        $request->validate([
            'report_type' => 'required|in:sales,inventory,staff,customer,financial,operational',
            'format' => 'required|in:csv,excel,pdf',
            'date_from' => 'required|date',
            'date_to' => 'required|date|after_or_equal:date_from',
        ]);

        // This would implement actual export logic
        return response()->json([
            'success' => true,
            'message' => 'Report export will be sent to your email shortly.',
            'download_url' => null, // Would contain actual download URL
        ]);
    }

    /**
     * Schedule automated report
     */
    public function scheduleReport(Request $request)
    {
        $request->validate([
            'report_type' => 'required|in:sales,inventory,staff,customer,financial,operational',
            'frequency' => 'required|in:daily,weekly,monthly',
            'format' => 'required|in:csv,excel,pdf',
            'email_recipients' => 'required|array',
            'email_recipients.*' => 'email',
            'is_active' => 'boolean',
        ]);

        // This would create a scheduled report entry
        return response()->json([
            'success' => true,
            'message' => 'Report scheduled successfully.',
        ]);
    }

    /**
     * Get quick statistics for dashboard
     */
    private function getQuickStats()
    {
        return [
            'today_revenue' => 2450.00,
            'today_orders' => 87,
            'monthly_revenue' => 45600.00,
            'monthly_orders' => 1250,
            'top_selling_item' => 'Margherita Pizza',
            'customer_satisfaction' => 4.6,
        ];
    }

    /**
     * Generate mock sales data
     */
    private function generateMockSalesData($dateFrom, $dateTo, $groupBy)
    {
        $chartData = collect();
        $current = $dateFrom->copy();

        while ($current->lte($dateTo)) {
            $chartData->push([
                'date' => $current->format('Y-m-d'),
                'revenue' => rand(800, 2500),
                'orders' => rand(30, 100),
                'average_order_value' => rand(20, 35),
            ]);

            switch ($groupBy) {
                case 'week':
                    $current->addWeek();
                    break;
                case 'month':
                    $current->addMonth();
                    break;
                default:
                    $current->addDay();
                    break;
            }
        }

        return [
            'chartData' => $chartData,
            'topItems' => collect([
                ['name' => 'Margherita Pizza', 'quantity' => 145, 'revenue' => 2175.00],
                ['name' => 'Caesar Salad', 'quantity' => 98, 'revenue' => 1176.00],
                ['name' => 'Pasta Carbonara', 'quantity' => 87, 'revenue' => 1305.00],
            ]),
        ];
    }

    /**
     * Calculate growth rate
     */
    private function calculateGrowthRate($data)
    {
        if ($data->count() < 2) {
            return 0;
        }

        $first = $data->first()['revenue'];
        $last = $data->last()['revenue'];

        return $first > 0 ? (($last - $first) / $first) * 100 : 0;
    }

    /**
     * Get custom report builder
     */
    public function customReportBuilder()
    {
        return Inertia::render('Tenant/Reports/CustomBuilder', [
            'availableFields' => [
                'orders' => ['id', 'total', 'status', 'created_at', 'customer_name'],
                'customers' => ['id', 'name', 'email', 'phone', 'total_orders'],
                'menu_items' => ['id', 'name', 'category', 'price', 'orders_count'],
                'employees' => ['id', 'name', 'department', 'hours_worked', 'salary'],
            ],
            'availableFilters' => [
                'date_range', 'status', 'category', 'department', 'customer_type'
            ],
            'availableGroupings' => [
                'day', 'week', 'month', 'category', 'department', 'status'
            ],
        ]);
    }

    /**
     * Generate custom report
     */
    public function generateCustomReport(Request $request)
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'fields' => 'required|array',
            'filters' => 'nullable|array',
            'grouping' => 'nullable|string',
            'date_from' => 'required|date',
            'date_to' => 'required|date|after_or_equal:date_from',
        ]);

        // This would implement custom report generation logic
        return response()->json([
            'success' => true,
            'message' => 'Custom report generated successfully.',
            'data' => [], // Would contain actual report data
        ]);
    }
}
