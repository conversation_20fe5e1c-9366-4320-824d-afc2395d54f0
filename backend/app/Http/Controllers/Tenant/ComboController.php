<?php

namespace App\Http\Controllers\Tenant;

use App\Http\Controllers\Controller;
use App\Models\Tenant\MenuItem;
use App\Models\Tenant\MenuItemCombo;
use App\Models\Tenant\Category;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Inertia\Inertia;
use Inertia\Response;

class ComboController extends Controller
{
    /**
     * Display a listing of combo items.
     */
    public function index(Request $request): Response
    {
        $query = MenuItem::with(['comboItems', 'primaryMedia', 'mediaItems'])
            ->where('is_combo', true);

        // Search functionality
        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('description', 'like', "%{$search}%");
            });
        }

        // Filter by availability
        if ($request->filled('is_available')) {
            $query->where('is_available', $request->boolean('is_available'));
        }

        $combos = $query->ordered()->paginate(15)->withQueryString();

        return Inertia::render('Tenant/ComboMenus/Index', [
            'combos' => $combos,
            'filters' => $request->only(['search', 'is_available']),
        ]);
    }

    /**
     * Show the form for creating a new combo item.
     */
    public function create(): Response
    {
        $menuItems = MenuItem::available()
            ->where('is_combo', false) // Only regular items can be part of combos
            ->with(['category', 'primaryMedia'])
            ->ordered()
            ->get();

        $categories = Category::active()->ordered()->get();
        $componentTypes = MenuItemCombo::getComponentTypes();

        return Inertia::render('Tenant/ComboMenus/Create', [
            'menuItems' => $menuItems,
            'categories' => $categories,
            'componentTypes' => $componentTypes,
        ]);
    }

    /**
     * Store a newly created combo item.
     */
    public function store(Request $request)
    {
        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'description' => 'nullable|string|max:1000',
            'price' => 'required|numeric|min:0',
            'category_id' => 'nullable|exists:categories,id',
            'is_available' => 'boolean',
            'sort_order' => 'integer|min:0',
            'media_id' => 'nullable|exists:media,id',
            'media_ids' => 'nullable|array|max:5',
            'media_ids.*' => 'exists:media,id',
            'components' => 'required|array|min:1',
            'components.*.component_type' => 'required|in:main,side,drink,dessert',
            'components.*.menu_item_id' => 'required|exists:menu_items,id',
            'components.*.is_required' => 'boolean',
            'components.*.sort_order' => 'integer|min:0',
        ]);

        DB::transaction(function () use ($validated) {
            // Create the combo as a menu item
            $combo = MenuItem::create([
                'name' => $validated['name'],
                'description' => $validated['description'],
                'price' => $validated['price'],
                'category_id' => $validated['category_id'],
                'is_available' => $validated['is_available'] ?? true,
                'is_combo' => true,
                'sort_order' => $validated['sort_order'] ?? 0,
                'media_id' => $validated['media_id'],
            ]);

            // Handle media relationships
            if (!empty($validated['media_ids']) && is_array($validated['media_ids'])) {
                $combo->syncMedia($validated['media_ids']);
            }

            // Add combo components
            $combo->syncComboItems($validated['components']);
        });

        return redirect()->route('combo-menus.index')
            ->with('success', 'Combo item created successfully.');
    }

    /**
     * Display the specified combo item.
     */
    public function show(MenuItem $comboMenu): Response
    {
        if (!$comboMenu->is_combo) {
            abort(404, 'This is not a combo item.');
        }

        $comboMenu->load([
            'comboItems.category',
            'comboItems.primaryMedia',
            'primaryMedia',
            'mediaItems'
        ]);

        return Inertia::render('Tenant/ComboMenus/Show', [
            'combo' => $comboMenu,
        ]);
    }

    /**
     * Show the form for editing the specified combo item.
     */
    public function edit(MenuItem $comboMenu): Response
    {
        if (!$comboMenu->is_combo) {
            abort(404, 'This is not a combo item.');
        }

        $comboMenu->load(['comboItems', 'primaryMedia', 'mediaItems']);

        $menuItems = MenuItem::available()
            ->where('is_combo', false) // Only regular items can be part of combos
            ->with(['category', 'primaryMedia'])
            ->ordered()
            ->get();

        $categories = Category::active()->ordered()->get();
        $componentTypes = MenuItemCombo::getComponentTypes();

        return Inertia::render('Tenant/ComboMenus/Edit', [
            'combo' => $comboMenu,
            'menuItems' => $menuItems,
            'categories' => $categories,
            'componentTypes' => $componentTypes,
        ]);
    }

    /**
     * Update the specified combo item.
     */
    public function update(Request $request, MenuItem $comboMenu)
    {
        if (!$comboMenu->is_combo) {
            abort(404, 'This is not a combo item.');
        }

        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'description' => 'nullable|string|max:1000',
            'price' => 'required|numeric|min:0',
            'category_id' => 'nullable|exists:categories,id',
            'is_available' => 'boolean',
            'sort_order' => 'integer|min:0',
            'media_id' => 'nullable|exists:media,id',
            'media_ids' => 'nullable|array|max:5',
            'media_ids.*' => 'exists:media,id',
            'components' => 'required|array|min:1',
            'components.*.component_type' => 'required|in:main,side,drink,dessert',
            'components.*.menu_item_id' => 'required|exists:menu_items,id',
            'components.*.is_required' => 'boolean',
            'components.*.sort_order' => 'integer|min:0',
        ]);

        DB::transaction(function () use ($validated, $comboMenu) {
            $comboMenu->update([
                'name' => $validated['name'],
                'description' => $validated['description'],
                'price' => $validated['price'],
                'category_id' => $validated['category_id'],
                'is_available' => $validated['is_available'] ?? true,
                'sort_order' => $validated['sort_order'] ?? 0,
                'media_id' => $validated['media_id'],
            ]);

            // Handle media relationships
            if (isset($validated['media_ids']) && is_array($validated['media_ids'])) {
                $comboMenu->syncMedia($validated['media_ids']);
            }

            // Update combo components
            $comboMenu->syncComboItems($validated['components']);
        });

        return redirect()->route('combo-menus.index')
            ->with('success', 'Combo item updated successfully.');
    }

    /**
     * Remove the specified combo item.
     */
    public function destroy(MenuItem $comboMenu)
    {
        if (!$comboMenu->is_combo) {
            abort(404, 'This is not a combo item.');
        }

        $comboMenu->delete();

        return redirect()->route('combo-menus.index')
            ->with('success', 'Combo item deleted successfully.');
    }

    /**
     * Toggle availability of the specified combo item.
     */
    public function toggleAvailability(MenuItem $comboMenu)
    {
        if (!$comboMenu->is_combo) {
            abort(404, 'This is not a combo item.');
        }

        $comboMenu->update([
            'is_available' => !$comboMenu->is_available
        ]);

        $status = $comboMenu->is_available ? 'enabled' : 'disabled';
        
        return redirect()->back()
            ->with('success', "Combo item has been {$status}.");
    }
}
