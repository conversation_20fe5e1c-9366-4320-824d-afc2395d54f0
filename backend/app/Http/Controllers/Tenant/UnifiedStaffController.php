<?php

namespace App\Http\Controllers\Tenant;

use App\Http\Controllers\Controller;
use App\Models\Tenant\Employee;
use App\Models\Tenant\Department;
use App\Models\Tenant\Branch;
use App\Models\Tenant\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Hash;
use Illuminate\Validation\Rules;
use Inertia\Inertia;
use Inertia\Response;
use DB;
class UnifiedStaffController extends Controller
{
    /**
     * Get departments for dropdowns
     */
    private function getDepartments()
    {
        return Department::orderBy('name')->get();
    }

    /**
     * Get branches for dropdowns
     */
    private function getBranches()
    {
        return Branch::active()->orderBy('name')->get();
    }

    /**
     * Display unified staff management dashboard
     */
    public function index(Request $request)
    {
        $query = Employee::with(['department', 'primaryBranch', 'user']);

        // Search functionality
        if ($request->filled('search')) {
            $query->where(function ($q) use ($request) {
                $q->where('first_name', 'like', '%' . $request->search . '%')
                  ->orWhere('last_name', 'like', '%' . $request->search . '%')
                  ->orWhere('position', 'like', '%' . $request->search . '%')
                  ->orWhere('employee_id', 'like', '%' . $request->search . '%')
                  ->orWhereHas('user', function ($userQuery) use ($request) {
                      $userQuery->where('name', 'like', '%' . $request->search . '%')
                               ->orWhere('email', 'like', '%' . $request->search . '%')
                               ->orWhere('phone', 'like', '%' . $request->search . '%');
                  });
            });
        }

        // Filter by role (only if column exists)
        if ($request->filled('role') && \Illuminate\Support\Facades\Schema::hasColumn('employees', 'role')) {
            $query->where('role', $request->role);
        }

        // Filter by status
        if ($request->filled('status')) {
            $query->where('employment_status', $request->status);
        }

        // Filter by department
        if ($request->filled('department_id')) {
            $query->where('department_id', $request->department_id);
        }

        // Filter by branch
        if ($request->filled('branch_id')) {
            $query->where('primary_branch_id', $request->branch_id);
        }

        $staff = $query->orderBy('first_name')
                      ->orderBy('first_name')
                      ->paginate(15)
                      ->withQueryString();

        // Check if role column exists before using it
        $hasRoleColumn = \Illuminate\Support\Facades\Schema::hasColumn('employees', 'role');

        $stats = [
            'total_staff' => Employee::where('is_active', true)->count(),
            'active_staff' => Employee::where('employment_status', 'active')->count(),
            'waiters' => $hasRoleColumn ? Employee::where('role', 'waiter')->where('is_active', true)->count() : 0,
            'chefs' => $hasRoleColumn ? Employee::where('role', 'chef')->where('is_active', true)->count() : 0,
            'riders' => $hasRoleColumn ? Employee::where('role', 'rider')->where('is_active', true)->count() : 0,
            'managers' => $hasRoleColumn ? Employee::where('role', 'manager')->where('is_active', true)->count() : 0,
        ];

        return Inertia::render('Tenant/Staff/Index', [
            'staff' => $staff,
            'stats' => $stats,
            'departments' => $this->getDepartments(),
            'branches' => $this->getBranches(),
            'roles' => [
                ['value' => 'waiter', 'label' => 'Waiter', 'description' => 'Can place orders through POS system, view table assignments'],
                ['value' => 'chef', 'label' => 'Chef', 'description' => 'Can view and process orders from kitchen display, update order status'],
                ['value' => 'rider', 'label' => 'Rider', 'description' => 'Can view delivery orders, update delivery status, handle home delivery'],
                ['value' => 'manager', 'label' => 'Manager', 'description' => 'Can manage all staff, view analytics, full system access'],
            ],
            'filters' => $request->only(['search', 'role', 'status', 'department_id', 'branch_id']),
        ]);
    }

    /**
     * Show the form for creating a new staff member
     */
    public function create()
    {
        return Inertia::render('Tenant/Staff/Create', [
            'departments' => $this->getDepartments(),
            'branches' => $this->getBranches(),
            'roles' => [
                ['value' => 'waiter', 'label' => 'Waiter', 'description' => 'Can place orders through POS system, view table assignments'],
                ['value' => 'chef', 'label' => 'Chef', 'description' => 'Can view and process orders from kitchen display, update order status'],
                ['value' => 'rider', 'label' => 'Rider', 'description' => 'Can view delivery orders, update delivery status, handle home delivery'],
                ['value' => 'manager', 'label' => 'Manager', 'description' => 'Can manage all staff, view analytics, full system access'],
            ],
        ]);
    }

    /**
     * Store a newly created staff member
     */
    public function store(Request $request)
    {
        $request->validate([
            'first_name' => 'required|string|max:255',
            'last_name' => 'required|string|max:255',
            'email' => 'required|email|unique:users,email', // Check uniqueness in users table
            'password' => ['required', 'confirmed', Rules\Password::defaults()],
            'phone' => 'nullable|string|max:20',
            'role' => 'required|in:waiter,chef,rider,manager',
            'department_id' => 'nullable|exists:departments,id',
            'primary_branch_id' => 'required|exists:branches,id',
            'position' => 'required|string|max:100',
            'hire_date' => 'required|date',
            'salary' => 'nullable|numeric|min:0',
            'hourly_rate' => 'nullable|numeric|min:0',
            'address' => 'nullable|string|max:500',
            'emergency_contact_name' => 'nullable|string|max:255',
            'emergency_contact_phone' => 'nullable|string|max:20',
            'notes' => 'nullable|string|max:1000',
            'is_active' => 'boolean',
        ]);

        try {
            DB::beginTransaction();

            // Step 1: Create user record with authentication data
            $user = User::create([
                'name' => trim($request->first_name . ' ' . $request->last_name),
                'email' => $request->email,
                'password' => Hash::make($request->password),
                'phone' => $request->phone,
                'role' => $request->role,
                'department' => $request->department_id ? Department::find($request->department_id)?->name : null,
                'position' => $request->position,
                'hire_date' => $request->hire_date,
                'salary' => $request->salary,
                'hourly_rate' => $request->hourly_rate,
                'address' => $request->address,
                'emergency_contact_name' => $request->emergency_contact_name,
                'emergency_contact_phone' => $request->emergency_contact_phone,
                'notes' => $request->notes,
                'is_active' => $request->boolean('is_active', true),
                'email_verified_at' => now(),
            ]);

            // Step 2: Create employee record with employment-specific data
            $employee = Employee::create([
                'user_id' => $user->id, // Link to user
                'first_name' => $request->first_name,
                'last_name' => $request->last_name,
                'department_id' => $request->department_id,
                'primary_branch_id' => $request->primary_branch_id,
                'position' => $request->position,
                'hire_date' => $request->hire_date,
                'salary' => $request->salary,
                'hourly_rate' => $request->hourly_rate,
                'is_active' => $request->boolean('is_active', true),
                'employment_status' => 'active',
                'status' => 'active',
                'employment_type' => 'full_time', // Default
            ]);

            DB::commit();

            return redirect()->route('staff.index')
                ->with('success', 'Staff member created successfully.');

        } catch (\Exception $e) {
            DB::rollBack();

            return back()
                ->withInput()
                ->withErrors(['error' => 'Failed to create staff member: ' . $e->getMessage()]);
        }
    }

    /**
     * Display the specified staff member
     */
    public function show(Employee $staff)
    {
        $staff->load(['department', 'primaryBranch']);

        return Inertia::render('Tenant/Staff/Show', [
            'staff' => $staff,
        ]);
    }

    /**
     * Show the form for editing the specified staff member
     */
    public function edit(Employee $staff)
    {
        return Inertia::render('Tenant/Staff/Edit', [
            'staff' => $staff,
            'departments' => $this->getDepartments(),
            'branches' => $this->getBranches(),
            'roles' => [
                ['value' => 'waiter', 'label' => 'Waiter', 'description' => 'Can place orders through POS system, view table assignments'],
                ['value' => 'chef', 'label' => 'Chef', 'description' => 'Can view and process orders from kitchen display, update order status'],
                ['value' => 'rider', 'label' => 'Rider', 'description' => 'Can view delivery orders, update delivery status, handle home delivery'],
                ['value' => 'manager', 'label' => 'Manager', 'description' => 'Can manage all staff, view analytics, full system access'],
            ],
        ]);
    }

    /**
     * Update the specified staff member
     */
    public function update(Request $request, Employee $staff)
    {
        $request->validate([
            'first_name' => 'required|string|max:255',
            'last_name' => 'required|string|max:255',
            'email' => 'required|email|unique:employees,email,' . $staff->id,
            'password' => ['nullable', 'confirmed', Rules\Password::defaults()],
            'phone' => 'nullable|string|max:20',
            'role' => 'required|in:waiter,chef,rider,manager',
            'department_id' => 'nullable|exists:departments,id',
            'primary_branch_id' => 'required|exists:branches,id',
            'position' => 'required|string|max:100',
            'hire_date' => 'required|date',
            'salary' => 'nullable|numeric|min:0',
            'hourly_rate' => 'nullable|numeric|min:0',
            'address' => 'nullable|string|max:500',
            'emergency_contact_name' => 'nullable|string|max:255',
            'emergency_contact_phone' => 'nullable|string|max:20',
            'notes' => 'nullable|string|max:1000',
            'is_active' => 'boolean',
        ]);

        // Generate combined name
        $name = trim($request->first_name . ' ' . $request->last_name);

        $updateData = [
            'first_name' => $request->first_name,
            'last_name' => $request->last_name,
            'name' => $name,
            'email' => $request->email,
            'phone' => $request->phone,
            'role' => $request->role,
            'department_id' => $request->department_id,
            'primary_branch_id' => $request->primary_branch_id,
            'position' => $request->position,
            'hire_date' => $request->hire_date,
            'salary' => $request->salary,
            'hourly_rate' => $request->hourly_rate,
            'address' => $request->address,
            'emergency_contact_name' => $request->emergency_contact_name,
            'emergency_contact_phone' => $request->emergency_contact_phone,
            'notes' => $request->notes,
            'is_active' => $request->boolean('is_active', true),
        ];

        // Only update password if provided
        if ($request->filled('password')) {
            $updateData['password'] = Hash::make($request->password);
        }

        $staff->update($updateData);

        return redirect()->route('staff.index')
            ->with('success', 'Staff member updated successfully.');
    }

    /**
     * Remove the specified staff member
     */
    public function destroy(Employee $staff)
    {
        // Soft delete by setting status to terminated
        $staff->update([
            'status' => 'terminated',
            'employment_status' => 'terminated',
            'termination_date' => now(),
            'is_active' => false,
        ]);

        return redirect()->route('staff.index')
            ->with('success', 'Staff member terminated successfully.');
    }

    /**
     * Toggle staff member status
     */
    public function toggleStatus(Employee $staff)
    {
        $newStatus = $staff->is_active ? false : true;
        $employmentStatus = $newStatus ? 'active' : 'inactive';

        $staff->update([
            'is_active' => $newStatus,
            'employment_status' => $employmentStatus,
            'status' => $newStatus ? 'active' : 'inactive',
        ]);

        $message = $newStatus ? 'Staff member activated successfully.' : 'Staff member deactivated successfully.';

        return back()->with('success', $message);
    }

    /**
     * Reset staff member password
     */
    public function resetPassword(Employee $staff)
    {
        $newPassword = 'password123'; // In production, generate a secure random password
        
        $staff->update([
            'password' => Hash::make($newPassword),
        ]);

        return back()->with('success', 'Password reset successfully. New password: ' . $newPassword);
    }
}
