<?php

namespace App\Http\Controllers\Tenant;

use App\Http\Controllers\Controller;
use App\Models\Tenant\PaymentMethod;
use App\Models\Tenant\PaymentMethodField;
use Illuminate\Http\Request;
use Inertia\Inertia;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Storage;

class PaymentMethodController extends Controller
{
    /**
     * Display a listing of payment methods.
     */
    public function index(Request $request)
    {
        $query = PaymentMethod::with(['fields'])
            ->withCount('orders')
            ->ordered();

        // Apply filters
        if ($request->filled('search')) {
            $query->where(function ($q) use ($request) {
                $q->where('name', 'like', '%' . $request->search . '%')
                  ->orWhere('instructions', 'like', '%' . $request->search . '%');
            });
        }

        if ($request->filled('type')) {
            $query->where('type', $request->type);
        }

        if ($request->filled('status')) {
            $query->where('is_active', $request->status === 'active');
        }

        $paymentMethods = $query->paginate(20)->withQueryString();

        // Calculate stats
        $stats = [
            'total_methods' => PaymentMethod::count(),
            'active_methods' => PaymentMethod::active()->count(),
            'inactive_methods' => PaymentMethod::where('is_active', false)->count(),
            'total_orders' => PaymentMethod::withCount('orders')->get()->sum('orders_count'),
        ];

        return Inertia::render('Tenant/PaymentMethods/Index', [
            'paymentMethods' => $paymentMethods,
            'stats' => $stats,
            'filters' => $request->only(['search', 'type', 'status']),
            'paymentTypes' => PaymentMethod::TYPES,
        ]);
    }

    /**
     * Show the form for creating a new payment method.
     */
    public function create()
    {
        return Inertia::render('Tenant/PaymentMethods/Create', [
            'paymentTypes' => PaymentMethod::TYPES,
            'fieldTypes' => PaymentMethodField::FIELD_TYPES,
        ]);
    }

    /**
     * Store a newly created payment method.
     */
    public function store(Request $request)
    {
        $request->validate([
            'name' => 'required|string|max:255|unique:payment_methods,name',
            'type' => 'required|in:' . implode(',', array_keys(PaymentMethod::TYPES)),
            'instructions' => 'nullable|string|max:1000',
            'is_active' => 'boolean',
            'display_order' => 'integer|min:0',
            'icon' => 'nullable|image|mimes:jpeg,png,jpg,gif,svg|max:2048',
            'image' => 'nullable|image|mimes:jpeg,png,jpg,gif,svg|max:2048',
            'fields' => 'nullable|array',
            'fields.*.field_name' => 'required_with:fields|string|max:255',
            'fields.*.field_type' => 'required_with:fields|in:' . implode(',', array_keys(PaymentMethodField::FIELD_TYPES)),
            'fields.*.field_label' => 'required_with:fields|string|max:255',
            'fields.*.is_required' => 'boolean',
            'fields.*.default_value' => 'nullable|string',
            'fields.*.validation_rules' => 'nullable|array',
            'fields.*.select_options' => 'nullable|array',
            'fields.*.display_order' => 'integer|min:0',
            'fields.*.placeholder' => 'nullable|string|max:255',
            'fields.*.help_text' => 'nullable|string|max:500',
        ]);

        DB::beginTransaction();

        try {
            $data = $request->only(['name', 'type', 'instructions', 'is_active', 'display_order']);

            // Handle file uploads
            if ($request->hasFile('icon')) {
                $data['icon'] = $request->file('icon')->store('payment-methods/icons', 'public');
            }

            if ($request->hasFile('image')) {
                $data['image'] = $request->file('image')->store('payment-methods/images', 'public');
            }

            // Set default display order if not provided
            if (!isset($data['display_order'])) {
                $data['display_order'] = PaymentMethod::max('display_order') + 1;
            }

            $paymentMethod = PaymentMethod::create($data);

            // Create fields if provided
            if ($request->has('fields') && is_array($request->fields)) {
                foreach ($request->fields as $fieldData) {
                    $paymentMethod->fields()->create([
                        'field_name' => $fieldData['field_name'],
                        'field_type' => $fieldData['field_type'],
                        'field_label' => $fieldData['field_label'],
                        'is_required' => $fieldData['is_required'] ?? false,
                        'default_value' => $fieldData['default_value'] ?? null,
                        'validation_rules' => $fieldData['validation_rules'] ?? null,
                        'select_options' => $fieldData['select_options'] ?? null,
                        'display_order' => $fieldData['display_order'] ?? 0,
                        'placeholder' => $fieldData['placeholder'] ?? null,
                        'help_text' => $fieldData['help_text'] ?? null,
                    ]);
                }
            }

            DB::commit();

            return redirect()->route('payment-methods.index')
                ->with('success', 'Payment method created successfully.');

        } catch (\Exception $e) {
            DB::rollBack();
            
            return back()->withErrors(['error' => 'Failed to create payment method: ' . $e->getMessage()]);
        }
    }

    /**
     * Display the specified payment method.
     */
    public function show(PaymentMethod $paymentMethod)
    {
        $paymentMethod->load(['fields' => function ($query) {
            $query->ordered();
        }]);

        // Get usage statistics
        $stats = [
            'total_orders' => $paymentMethod->orders()->count(),
            'total_amount' => $paymentMethod->orders()->sum('total_amount'),
            'recent_orders' => $paymentMethod->orders()
                ->with(['customer', 'table'])
                ->latest()
                ->limit(10)
                ->get(),
        ];

        return Inertia::render('Tenant/PaymentMethods/Show', [
            'paymentMethod' => $paymentMethod,
            'stats' => $stats,
        ]);
    }

    /**
     * Show the form for editing the specified payment method.
     */
    public function edit(PaymentMethod $paymentMethod)
    {
        $paymentMethod->load(['fields' => function ($query) {
            $query->ordered();
        }]);

        return Inertia::render('Tenant/PaymentMethods/Edit', [
            'paymentMethod' => $paymentMethod,
            'paymentTypes' => PaymentMethod::TYPES,
            'fieldTypes' => PaymentMethodField::FIELD_TYPES,
        ]);
    }

    /**
     * Update the specified payment method.
     */
    public function update(Request $request, PaymentMethod $paymentMethod)
    {
        $request->validate([
            'name' => 'required|string|max:255|unique:payment_methods,name,' . $paymentMethod->id,
            'type' => 'required|in:' . implode(',', array_keys(PaymentMethod::TYPES)),
            'instructions' => 'nullable|string|max:1000',
            'is_active' => 'boolean',
            'display_order' => 'integer|min:0',
            'icon' => 'nullable|image|mimes:jpeg,png,jpg,gif,svg|max:2048',
            'image' => 'nullable|image|mimes:jpeg,png,jpg,gif,svg|max:2048',
            'fields' => 'nullable|array',
            'fields.*.id' => 'nullable|exists:payment_method_fields,id',
            'fields.*.field_name' => 'required_with:fields|string|max:255',
            'fields.*.field_type' => 'required_with:fields|in:' . implode(',', array_keys(PaymentMethodField::FIELD_TYPES)),
            'fields.*.field_label' => 'required_with:fields|string|max:255',
            'fields.*.is_required' => 'boolean',
            'fields.*.default_value' => 'nullable|string',
            'fields.*.validation_rules' => 'nullable|array',
            'fields.*.select_options' => 'nullable|array',
            'fields.*.display_order' => 'integer|min:0',
            'fields.*.placeholder' => 'nullable|string|max:255',
            'fields.*.help_text' => 'nullable|string|max:500',
        ]);

        DB::beginTransaction();

        try {
            $data = $request->only(['name', 'type', 'instructions', 'is_active', 'display_order']);

            // Handle file uploads
            if ($request->hasFile('icon')) {
                // Delete old icon
                if ($paymentMethod->icon) {
                    Storage::disk('public')->delete($paymentMethod->icon);
                }
                $data['icon'] = $request->file('icon')->store('payment-methods/icons', 'public');
            }

            if ($request->hasFile('image')) {
                // Delete old image
                if ($paymentMethod->image) {
                    Storage::disk('public')->delete($paymentMethod->image);
                }
                $data['image'] = $request->file('image')->store('payment-methods/images', 'public');
            }

            $paymentMethod->update($data);

            // Update fields
            if ($request->has('fields') && is_array($request->fields)) {
                $existingFieldIds = [];
                
                foreach ($request->fields as $fieldData) {
                    if (isset($fieldData['id']) && $fieldData['id']) {
                        // Update existing field
                        $field = $paymentMethod->fields()->find($fieldData['id']);
                        if ($field) {
                            $field->update([
                                'field_name' => $fieldData['field_name'],
                                'field_type' => $fieldData['field_type'],
                                'field_label' => $fieldData['field_label'],
                                'is_required' => $fieldData['is_required'] ?? false,
                                'default_value' => $fieldData['default_value'] ?? null,
                                'validation_rules' => $fieldData['validation_rules'] ?? null,
                                'select_options' => $fieldData['select_options'] ?? null,
                                'display_order' => $fieldData['display_order'] ?? 0,
                                'placeholder' => $fieldData['placeholder'] ?? null,
                                'help_text' => $fieldData['help_text'] ?? null,
                            ]);
                            $existingFieldIds[] = $field->id;
                        }
                    } else {
                        // Create new field
                        $field = $paymentMethod->fields()->create([
                            'field_name' => $fieldData['field_name'],
                            'field_type' => $fieldData['field_type'],
                            'field_label' => $fieldData['field_label'],
                            'is_required' => $fieldData['is_required'] ?? false,
                            'default_value' => $fieldData['default_value'] ?? null,
                            'validation_rules' => $fieldData['validation_rules'] ?? null,
                            'select_options' => $fieldData['select_options'] ?? null,
                            'display_order' => $fieldData['display_order'] ?? 0,
                            'placeholder' => $fieldData['placeholder'] ?? null,
                            'help_text' => $fieldData['help_text'] ?? null,
                        ]);
                        $existingFieldIds[] = $field->id;
                    }
                }
                
                // Delete fields that are no longer present
                $paymentMethod->fields()->whereNotIn('id', $existingFieldIds)->delete();
            } else {
                // Delete all fields if none provided
                $paymentMethod->fields()->delete();
            }

            DB::commit();

            return redirect()->route('payment-methods.index')
                ->with('success', 'Payment method updated successfully.');

        } catch (\Exception $e) {
            DB::rollBack();
            
            return back()->withErrors(['error' => 'Failed to update payment method: ' . $e->getMessage()]);
        }
    }

    /**
     * Remove the specified payment method.
     */
    public function destroy(PaymentMethod $paymentMethod)
    {
        // Check if payment method is being used
        if ($paymentMethod->orders()->exists()) {
            return back()->withErrors(['error' => 'Cannot delete payment method that has been used in orders.']);
        }

        try {
            // Delete associated files
            if ($paymentMethod->icon) {
                Storage::disk('public')->delete($paymentMethod->icon);
            }
            
            if ($paymentMethod->image) {
                Storage::disk('public')->delete($paymentMethod->image);
            }

            $paymentMethod->delete();

            return redirect()->route('payment-methods.index')
                ->with('success', 'Payment method deleted successfully.');

        } catch (\Exception $e) {
            return back()->withErrors(['error' => 'Failed to delete payment method: ' . $e->getMessage()]);
        }
    }

    /**
     * Toggle the active status of a payment method.
     */
    public function toggleStatus(PaymentMethod $paymentMethod)
    {
        $paymentMethod->update(['is_active' => !$paymentMethod->is_active]);

        $status = $paymentMethod->is_active ? 'activated' : 'deactivated';

        return response()->json([
            'success' => true,
            'message' => "Payment method {$status} successfully.",
            'is_active' => $paymentMethod->is_active,
        ]);
    }

    /**
     * Update display order of payment methods.
     */
    public function updateOrder(Request $request)
    {
        $request->validate([
            'payment_methods' => 'required|array',
            'payment_methods.*.id' => 'required|exists:payment_methods,id',
            'payment_methods.*.display_order' => 'required|integer|min:0',
        ]);

        try {
            foreach ($request->payment_methods as $methodData) {
                PaymentMethod::where('id', $methodData['id'])
                    ->update(['display_order' => $methodData['display_order']]);
            }

            return response()->json([
                'success' => true,
                'message' => 'Payment method order updated successfully.',
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to update order: ' . $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Get active payment methods for frontend use.
     */
    public function getActive()
    {
        $paymentMethods = PaymentMethod::active()
            ->with(['fields' => function ($query) {
                $query->ordered();
            }])
            ->ordered()
            ->get();

        return response()->json([
            'success' => true,
            'payment_methods' => $paymentMethods,
        ]);
    }
}
