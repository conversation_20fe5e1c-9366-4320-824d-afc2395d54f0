<?php

namespace App\Http\Controllers\Tenant;

use App\Http\Controllers\Controller;
use App\Models\Tenant\DeliveryPersonnel;
use App\Models\Tenant\DeliveryZone;
use App\Models\Tenant\Restaurant;
use App\Models\User;
use Illuminate\Http\Request;
use Inertia\Inertia;
use Illuminate\Support\Str;

class DeliveryPersonnelController extends Controller
{
    /**
     * Display a listing of delivery personnel.
     */
    public function index(Request $request)
    {
        $query = DeliveryPersonnel::with(['restaurant', 'user', 'deliveryZones']);

        // Search
        if ($request->search) {
            $query->where(function ($q) use ($request) {
                $q->where('first_name', 'like', '%' . $request->search . '%')
                  ->orWhere('last_name', 'like', '%' . $request->search . '%')
                  ->orWhere('phone', 'like', '%' . $request->search . '%')
                  ->orWhere('employee_id', 'like', '%' . $request->search . '%');
            });
        }

        // Filter by status
        if ($request->status) {
            $query->where('status', $request->status);
        }

        // Filter by vehicle type
        if ($request->vehicle_type) {
            $query->where('vehicle_type', $request->vehicle_type);
        }

        // Filter by active status
        if ($request->active_status) {
            if ($request->active_status === 'active') {
                $query->where('is_active', true);
            } elseif ($request->active_status === 'inactive') {
                $query->where('is_active', false);
            }
        }

        $personnel = $query->latest()->paginate(20);

        return Inertia::render('Tenant/DeliveryPersonnel/Index', [
            'personnel' => $personnel,
            'filters' => $request->only(['search', 'status', 'vehicle_type', 'active_status']),
            'statusOptions' => $this->getStatusOptions(),
            'vehicleTypeOptions' => $this->getVehicleTypeOptions(),
        ]);
    }

    /**
     * Show the form for creating new delivery personnel.
     */
    public function create()
    {
        $restaurant = Restaurant::first();
        $deliveryZones = DeliveryZone::active()->get();

        return Inertia::render('Tenant/DeliveryPersonnel/Create', [
            'restaurant' => $restaurant,
            'deliveryZones' => $deliveryZones,
        ]);
    }

    /**
     * Store newly created delivery personnel.
     */
    public function store(Request $request)
    {
        $request->validate([
            'first_name' => 'required|string|max:255',
            'last_name' => 'required|string|max:255',
            'phone' => 'required|string|max:20|unique:delivery_personnel,phone',
            'email' => 'nullable|email|unique:delivery_personnel,email',
            'vehicle_type' => 'required|in:bicycle,motorcycle,car,van',
            'vehicle_number' => 'nullable|string|max:255',
            'license_number' => 'nullable|string|max:255',
            'hourly_rate' => 'nullable|numeric|min:0',
            'commission_rate' => 'nullable|numeric|min:0|max:100',
            'max_concurrent_orders' => 'nullable|integer|min:1|max:10',
            'delivery_radius' => 'nullable|numeric|min:1|max:50',
            'shift_start_time' => 'nullable|date_format:H:i',
            'shift_end_time' => 'nullable|date_format:H:i',
            'emergency_contact_name' => 'nullable|string|max:255',
            'emergency_contact_phone' => 'nullable|string|max:20',
            'delivery_zone_ids' => 'nullable|array',
            'delivery_zone_ids.*' => 'exists:delivery_zones,id',
            'is_active' => 'boolean',
        ]);

        $restaurant = Restaurant::first();

        // Generate unique employee ID
        $employeeId = $this->generateEmployeeId();

        $personnel = DeliveryPersonnel::create([
            'restaurant_id' => $restaurant->id,
            'employee_id' => $employeeId,
            'first_name' => $request->first_name,
            'last_name' => $request->last_name,
            'phone' => $request->phone,
            'email' => $request->email,
            'vehicle_type' => $request->vehicle_type,
            'vehicle_number' => $request->vehicle_number,
            'license_number' => $request->license_number,
            'hourly_rate' => $request->hourly_rate,
            'commission_rate' => $request->commission_rate ?? 0,
            'max_concurrent_orders' => $request->max_concurrent_orders ?? 3,
            'delivery_radius' => $request->delivery_radius ?? 10,
            'shift_start_time' => $request->shift_start_time,
            'shift_end_time' => $request->shift_end_time,
            'emergency_contact_name' => $request->emergency_contact_name,
            'emergency_contact_phone' => $request->emergency_contact_phone,
            'is_active' => $request->boolean('is_active', true),
        ]);

        // Assign delivery zones
        if ($request->delivery_zone_ids) {
            $personnel->deliveryZones()->sync($request->delivery_zone_ids);
        }

        return redirect()->route('delivery-personnel.index')
            ->with('success', 'Delivery personnel created successfully.');
    }

    /**
     * Display the specified delivery personnel.
     */
    public function show(DeliveryPersonnel $deliveryPersonnel)
    {
        $deliveryPersonnel->load([
            'restaurant', 
            'user', 
            'deliveryZones',
            'orders' => function ($query) {
                $query->with('customer')->latest()->take(10);
            }
        ]);

        // Get performance statistics
        $stats = [
            'today' => $deliveryPersonnel->getDeliveryStats('today'),
            'week' => $deliveryPersonnel->getDeliveryStats('week'),
            'month' => $deliveryPersonnel->getDeliveryStats('month'),
        ];

        return Inertia::render('Tenant/DeliveryPersonnel/Show', [
            'personnel' => $deliveryPersonnel,
            'stats' => $stats,
        ]);
    }

    /**
     * Show the form for editing delivery personnel.
     */
    public function edit(DeliveryPersonnel $deliveryPersonnel)
    {
        $deliveryPersonnel->load(['deliveryZones']);
        $restaurant = Restaurant::first();
        $deliveryZones = DeliveryZone::active()->get();

        return Inertia::render('Tenant/DeliveryPersonnel/Edit', [
            'personnel' => $deliveryPersonnel,
            'restaurant' => $restaurant,
            'deliveryZones' => $deliveryZones,
        ]);
    }

    /**
     * Update the specified delivery personnel.
     */
    public function update(Request $request, DeliveryPersonnel $deliveryPersonnel)
    {
        $request->validate([
            'first_name' => 'required|string|max:255',
            'last_name' => 'required|string|max:255',
            'phone' => 'required|string|max:20|unique:delivery_personnel,phone,' . $deliveryPersonnel->id,
            'email' => 'nullable|email|unique:delivery_personnel,email,' . $deliveryPersonnel->id,
            'vehicle_type' => 'required|in:bicycle,motorcycle,car,van',
            'vehicle_number' => 'nullable|string|max:255',
            'license_number' => 'nullable|string|max:255',
            'hourly_rate' => 'nullable|numeric|min:0',
            'commission_rate' => 'nullable|numeric|min:0|max:100',
            'max_concurrent_orders' => 'nullable|integer|min:1|max:10',
            'delivery_radius' => 'nullable|numeric|min:1|max:50',
            'shift_start_time' => 'nullable|date_format:H:i',
            'shift_end_time' => 'nullable|date_format:H:i',
            'emergency_contact_name' => 'nullable|string|max:255',
            'emergency_contact_phone' => 'nullable|string|max:20',
            'delivery_zone_ids' => 'nullable|array',
            'delivery_zone_ids.*' => 'exists:delivery_zones,id',
            'is_active' => 'boolean',
        ]);

        $deliveryPersonnel->update([
            'first_name' => $request->first_name,
            'last_name' => $request->last_name,
            'phone' => $request->phone,
            'email' => $request->email,
            'vehicle_type' => $request->vehicle_type,
            'vehicle_number' => $request->vehicle_number,
            'license_number' => $request->license_number,
            'hourly_rate' => $request->hourly_rate,
            'commission_rate' => $request->commission_rate,
            'max_concurrent_orders' => $request->max_concurrent_orders,
            'delivery_radius' => $request->delivery_radius,
            'shift_start_time' => $request->shift_start_time,
            'shift_end_time' => $request->shift_end_time,
            'emergency_contact_name' => $request->emergency_contact_name,
            'emergency_contact_phone' => $request->emergency_contact_phone,
            'is_active' => $request->boolean('is_active'),
        ]);

        // Update delivery zones
        if ($request->delivery_zone_ids) {
            $deliveryPersonnel->deliveryZones()->sync($request->delivery_zone_ids);
        } else {
            $deliveryPersonnel->deliveryZones()->detach();
        }

        return redirect()->route('delivery-personnel.index')
            ->with('success', 'Delivery personnel updated successfully.');
    }

    /**
     * Remove the specified delivery personnel.
     */
    public function destroy(DeliveryPersonnel $deliveryPersonnel)
    {
        // Check if personnel has active orders
        if ($deliveryPersonnel->activeOrders()->exists()) {
            return back()->withErrors(['error' => 'Cannot delete delivery personnel with active orders.']);
        }

        $deliveryPersonnel->delete();

        return redirect()->route('delivery-personnel.index')
            ->with('success', 'Delivery personnel deleted successfully.');
    }

    /**
     * Toggle personnel status.
     */
    public function toggleStatus(DeliveryPersonnel $deliveryPersonnel)
    {
        $deliveryPersonnel->update(['is_active' => !$deliveryPersonnel->is_active]);

        $status = $deliveryPersonnel->is_active ? 'activated' : 'deactivated';
        
        return response()->json([
            'success' => true,
            'message' => "Delivery personnel {$status} successfully.",
            'is_active' => $deliveryPersonnel->is_active,
        ]);
    }

    /**
     * Update personnel availability status.
     */
    public function updateStatus(Request $request, DeliveryPersonnel $deliveryPersonnel)
    {
        $request->validate([
            'status' => 'required|in:available,busy,on_delivery,offline',
        ]);

        $deliveryPersonnel->update(['status' => $request->status]);

        return response()->json([
            'success' => true,
            'message' => 'Status updated successfully.',
            'status' => $deliveryPersonnel->status,
        ]);
    }

    /**
     * Get available personnel for assignment.
     */
    public function available(Request $request)
    {
        $query = DeliveryPersonnel::available();

        // Filter by delivery zone if provided
        if ($request->delivery_zone_id) {
            $query->whereHas('deliveryZones', function ($q) use ($request) {
                $q->where('delivery_zones.id', $request->delivery_zone_id);
            });
        }

        // Filter by location proximity if provided
        if ($request->latitude && $request->longitude) {
            $personnel = $query->get()->filter(function ($person) use ($request) {
                if (!$person->current_latitude || !$person->current_longitude) {
                    return true; // Include if location unknown
                }
                
                $distance = $person->distanceTo($request->latitude, $request->longitude);
                return $distance <= $person->delivery_radius;
            });
        } else {
            $personnel = $query->get();
        }

        return response()->json($personnel);
    }

    /**
     * Bulk update delivery personnel.
     */
    public function bulkUpdate(Request $request)
    {
        $request->validate([
            'ids' => 'required|array',
            'ids.*' => 'exists:delivery_personnel,id',
            'action' => 'required|in:activate,deactivate,set_available,set_offline,delete',
        ]);

        $personnel = DeliveryPersonnel::whereIn('id', $request->ids);

        switch ($request->action) {
            case 'activate':
                $personnel->update(['is_active' => true]);
                $message = 'Delivery personnel activated successfully.';
                break;
            case 'deactivate':
                $personnel->update(['is_active' => false]);
                $message = 'Delivery personnel deactivated successfully.';
                break;
            case 'set_available':
                $personnel->update(['status' => 'available']);
                $message = 'Delivery personnel marked as available.';
                break;
            case 'set_offline':
                $personnel->update(['status' => 'offline']);
                $message = 'Delivery personnel marked as offline.';
                break;
            case 'delete':
                // Check if any personnel has active orders
                $hasActiveOrders = $personnel->whereHas('activeOrders')->exists();
                if ($hasActiveOrders) {
                    return back()->withErrors(['error' => 'Cannot delete delivery personnel with active orders.']);
                }
                $personnel->delete();
                $message = 'Delivery personnel deleted successfully.';
                break;
        }

        return back()->with('success', $message);
    }

    /**
     * Generate unique employee ID.
     */
    protected function generateEmployeeId(): string
    {
        $prefix = 'DEL';
        $year = now()->format('y');
        
        do {
            $number = str_pad(mt_rand(1, 9999), 4, '0', STR_PAD_LEFT);
            $employeeId = "{$prefix}{$year}{$number}";
        } while (DeliveryPersonnel::where('employee_id', $employeeId)->exists());

        return $employeeId;
    }

    /**
     * Get status options.
     */
    protected function getStatusOptions(): array
    {
        return [
            ['value' => 'available', 'label' => 'Available'],
            ['value' => 'busy', 'label' => 'Busy'],
            ['value' => 'on_delivery', 'label' => 'On Delivery'],
            ['value' => 'offline', 'label' => 'Offline'],
        ];
    }

    /**
     * Get vehicle type options.
     */
    protected function getVehicleTypeOptions(): array
    {
        return [
            ['value' => 'bicycle', 'label' => 'Bicycle'],
            ['value' => 'motorcycle', 'label' => 'Motorcycle'],
            ['value' => 'car', 'label' => 'Car'],
            ['value' => 'van', 'label' => 'Van'],
        ];
    }
}
