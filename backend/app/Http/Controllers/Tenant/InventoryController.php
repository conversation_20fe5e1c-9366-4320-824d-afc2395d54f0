<?php

namespace App\Http\Controllers\Tenant;

use App\Http\Controllers\Controller;
use App\Models\Tenant\InventoryItem;
use App\Models\Tenant\InventoryCategory;
use App\Models\Tenant\Vendor;
use App\Models\Tenant\Restaurant;
use App\Models\Tenant\StockMovement;
use App\Models\Tenant\WasteRecord;
use Illuminate\Http\Request;
use Inertia\Inertia;
use Carbon\Carbon;

class InventoryController extends Controller
{
    /**
     * Display inventory dashboard.
     */
    public function dashboard()
    {
        $totalItems = InventoryItem::active()->count();
        $totalValue = InventoryItem::active()->get()->sum('current_value');
        $lowStockItems = InventoryItem::lowStock()->count();
        $outOfStockItems = InventoryItem::outOfStock()->count();
        $expiredItems = InventoryItem::active()
            ->whereHas('stockBatches', function ($query) {
                $query->where('expiry_date', '<', now())
                      ->where('quantity', '>', 0);
            })->count();

        // Recent stock movements
        $recentMovements = StockMovement::with(['inventoryItem', 'performedBy'])
            ->latest()
            ->take(10)
            ->get();

        // Low stock alerts
        $lowStockAlerts = InventoryItem::with(['category', 'vendor'])
            ->lowStock()
            ->active()
            ->take(10)
            ->get();

        // Expiring items
        $expiringItems = InventoryItem::with(['category', 'stockBatches'])
            ->whereHas('stockBatches', function ($query) {
                $query->whereBetween('expiry_date', [now(), now()->addDays(7)])
                      ->where('quantity', '>', 0);
            })
            ->take(10)
            ->get();

        // Waste statistics
        $wasteStats = WasteRecord::getWasteStats('month');

        return Inertia::render('Tenant/Inventory/Dashboard', [
            'stats' => [
                'total_items' => $totalItems,
                'total_value' => $totalValue,
                'low_stock_items' => $lowStockItems,
                'out_of_stock_items' => $outOfStockItems,
                'expired_items' => $expiredItems,
            ],
            'recentMovements' => $recentMovements,
            'lowStockAlerts' => $lowStockAlerts,
            'expiringItems' => $expiringItems,
            'wasteStats' => $wasteStats,
        ]);
    }

    /**
     * Display a listing of inventory items.
     */
    public function index(Request $request)
    {
        $query = InventoryItem::with(['category', 'vendor', 'media']);

        // Search
        if ($request->search) {
            $query->where(function ($q) use ($request) {
                $q->where('name', 'like', '%' . $request->search . '%')
                  ->orWhere('description', 'like', '%' . $request->search . '%')
                  ->orWhere('sku', 'like', '%' . $request->search . '%')
                  ->orWhere('barcode', 'like', '%' . $request->search . '%');
            });
        }

        // Filter by category
        if ($request->category_id) {
            $query->where('inventory_category_id', $request->category_id);
        }

        // Filter by vendor
        if ($request->vendor_id) {
            $query->where('vendor_id', $request->vendor_id);
        }

        // Filter by stock status
        if ($request->stock_status) {
            switch ($request->stock_status) {
                case 'low_stock':
                    $query->lowStock();
                    break;
                case 'out_of_stock':
                    $query->outOfStock();
                    break;
                case 'needs_reorder':
                    $query->needsReorder();
                    break;
                case 'in_stock':
                    $query->where('current_stock', '>', 0)
                          ->whereRaw('current_stock > minimum_stock');
                    break;
            }
        }

        // Filter by perishable
        if ($request->has('is_perishable')) {
            $query->where('is_perishable', $request->boolean('is_perishable'));
        }

        // Filter by active status
        if ($request->has('is_active')) {
            $query->where('is_active', $request->boolean('is_active'));
        }

        $items = $query->latest()->paginate(20);

        // Get filter options
        $categories = InventoryCategory::active()->ordered()->get();
        $vendors = Vendor::active()->orderBy('name')->get();

        // Calculate stats for the index page
        $stats = [
            'total_items' => InventoryItem::count(),
            'low_stock_items' => InventoryItem::lowStock()->count(),
            'expiring_items' => InventoryItem::whereHas('stockBatches', function ($query) {
                $query->whereBetween('expiry_date', [now(), now()->addDays(7)])
                      ->where('quantity', '>', 0);
            })->count(),
            'total_value' => InventoryItem::active()->get()->sum('current_value'),
        ];

        return Inertia::render('Tenant/Inventory/Index', [
            'inventory' => $items,
            'categories' => $categories,
            'vendors' => $vendors,
            'stats' => $stats,
            'filters' => $request->only([
                'search', 'category_id', 'vendor_id', 'stock_status',
                'is_perishable', 'is_active'
            ]),
            'stockStatusOptions' => $this->getStockStatusOptions(),
        ]);
    }

    /**
     * Show the form for creating a new inventory item.
     */
    public function create()
    {
        $restaurant = Restaurant::first();
        $categories = InventoryCategory::active()->ordered()->get();
        $vendors = Vendor::active()->orderBy('name')->get();

        return Inertia::render('Tenant/Inventory/Create', [
            'restaurant' => $restaurant,
            'categories' => $categories,
            'vendors' => $vendors,
            'unitOptions' => $this->getUnitOptions(),
            'storageOptions' => $this->getStorageOptions(),
        ]);
    }

    /**
     * Store a newly created inventory item.
     */
    public function store(Request $request)
    {
        $request->validate([
            'inventory_category_id' => 'required|exists:inventory_categories,id',
            'vendor_id' => 'nullable|exists:vendors,id',
            'name' => 'required|string|max:255',
            'description' => 'nullable|string',
            'sku' => 'nullable|string|max:255|unique:inventory_items',
            'barcode' => 'nullable|string|max:255',
            'unit_of_measurement' => 'required|string|max:50',
            'unit_cost' => 'required|numeric|min:0',
            'selling_price' => 'nullable|numeric|min:0',
            'current_stock' => 'required|numeric|min:0',
            'minimum_stock' => 'required|numeric|min:0',
            'maximum_stock' => 'nullable|numeric|min:0',
            'reorder_point' => 'required|numeric|min:0',
            'reorder_quantity' => 'required|numeric|min:0',
            'shelf_life_days' => 'nullable|integer|min:1',
            'storage_requirements' => 'nullable|in:ambient,refrigerated,frozen,dry,controlled',
            'is_perishable' => 'boolean',
            'track_expiry' => 'boolean',
            'track_batches' => 'boolean',
            'notes' => 'nullable|string',
            'media_ids' => 'nullable|array',
            'media_ids.*' => 'exists:media,id',
        ]);

        $restaurant = Restaurant::first();

        $item = InventoryItem::create([
            'restaurant_id' => $restaurant->id,
            'inventory_category_id' => $request->inventory_category_id,
            'vendor_id' => $request->vendor_id,
            'name' => $request->name,
            'description' => $request->description,
            'sku' => $request->sku,
            'barcode' => $request->barcode,
            'unit_of_measurement' => $request->unit_of_measurement,
            'unit_cost' => $request->unit_cost,
            'selling_price' => $request->selling_price,
            'current_stock' => $request->current_stock,
            'minimum_stock' => $request->minimum_stock,
            'maximum_stock' => $request->maximum_stock,
            'reorder_point' => $request->reorder_point,
            'reorder_quantity' => $request->reorder_quantity,
            'shelf_life_days' => $request->shelf_life_days,
            'storage_requirements' => $request->storage_requirements,
            'is_perishable' => $request->boolean('is_perishable'),
            'track_expiry' => $request->boolean('track_expiry'),
            'track_batches' => $request->boolean('track_batches'),
            'notes' => $request->notes,
            'last_counted_at' => now(),
        ]);

        // Attach media
        if ($request->media_ids) {
            foreach ($request->media_ids as $mediaId) {
                $item->addMediaFromLibrary($mediaId, 'images');
            }
        }

        // Create initial stock movement if stock > 0
        if ($request->current_stock > 0) {
            $item->addStock(
                $request->current_stock,
                $request->unit_cost,
                null,
                null,
                'initial_stock'
            );
        }

        return redirect()->route('inventory.index')
            ->with('success', 'Inventory item created successfully.');
    }

    /**
     * Display the specified inventory item.
     */
    public function show(InventoryItem $inventory)
    {
        $inventory->load([
            'category', 'vendor', 'media',
            'stockMovements' => function ($query) {
                $query->with(['performedBy'])->latest()->take(20);
            },
            'stockBatches' => function ($query) {
                $query->where('quantity', '>', 0)->orderBy('expiry_date');
            }
        ]);

        // Get usage trend
        $usageTrend = $inventory->getUsageTrend(30);
        $turnoverRate = $inventory->getTurnoverRate();

        return Inertia::render('Tenant/Inventory/Show', [
            'item' => $inventory,
            'usageTrend' => $usageTrend,
            'turnoverRate' => $turnoverRate,
        ]);
    }

    /**
     * Show the form for editing the specified inventory item.
     */
    public function edit(InventoryItem $inventory)
    {
        $inventory->load(['media']);
        $restaurant = Restaurant::first();
        $categories = InventoryCategory::active()->ordered()->get();
        $vendors = Vendor::active()->orderBy('name')->get();

        return Inertia::render('Tenant/Inventory/Edit', [
            'item' => $inventory,
            'restaurant' => $restaurant,
            'categories' => $categories,
            'vendors' => $vendors,
            'unitOptions' => $this->getUnitOptions(),
            'storageOptions' => $this->getStorageOptions(),
        ]);
    }

    /**
     * Update the specified inventory item.
     */
    public function update(Request $request, InventoryItem $inventory)
    {
        $request->validate([
            'inventory_category_id' => 'required|exists:inventory_categories,id',
            'vendor_id' => 'nullable|exists:vendors,id',
            'name' => 'required|string|max:255',
            'description' => 'nullable|string',
            'sku' => 'nullable|string|max:255|unique:inventory_items,sku,' . $inventory->id,
            'barcode' => 'nullable|string|max:255',
            'unit_of_measurement' => 'required|string|max:50',
            'unit_cost' => 'required|numeric|min:0',
            'selling_price' => 'nullable|numeric|min:0',
            'minimum_stock' => 'required|numeric|min:0',
            'maximum_stock' => 'nullable|numeric|min:0',
            'reorder_point' => 'required|numeric|min:0',
            'reorder_quantity' => 'required|numeric|min:0',
            'shelf_life_days' => 'nullable|integer|min:1',
            'storage_requirements' => 'nullable|in:ambient,refrigerated,frozen,dry,controlled',
            'is_perishable' => 'boolean',
            'track_expiry' => 'boolean',
            'track_batches' => 'boolean',
            'notes' => 'nullable|string',
            'media_ids' => 'nullable|array',
            'media_ids.*' => 'exists:media,id',
        ]);

        $inventory->update([
            'inventory_category_id' => $request->inventory_category_id,
            'vendor_id' => $request->vendor_id,
            'name' => $request->name,
            'description' => $request->description,
            'sku' => $request->sku,
            'barcode' => $request->barcode,
            'unit_of_measurement' => $request->unit_of_measurement,
            'unit_cost' => $request->unit_cost,
            'selling_price' => $request->selling_price,
            'minimum_stock' => $request->minimum_stock,
            'maximum_stock' => $request->maximum_stock,
            'reorder_point' => $request->reorder_point,
            'reorder_quantity' => $request->reorder_quantity,
            'shelf_life_days' => $request->shelf_life_days,
            'storage_requirements' => $request->storage_requirements,
            'is_perishable' => $request->boolean('is_perishable'),
            'track_expiry' => $request->boolean('track_expiry'),
            'track_batches' => $request->boolean('track_batches'),
            'notes' => $request->notes,
        ]);

        // Update media
        $inventory->clearMediaCollection('images');
        if ($request->media_ids) {
            foreach ($request->media_ids as $mediaId) {
                $inventory->addMediaFromLibrary($mediaId, 'images');
            }
        }

        return redirect()->route('inventory.index')
            ->with('success', 'Inventory item updated successfully.');
    }

    /**
     * Remove the specified inventory item.
     */
    public function destroy(InventoryItem $inventory)
    {
        // Check if item has stock movements
        if ($inventory->stockMovements()->exists()) {
            return back()->withErrors(['error' => 'Cannot delete item with existing stock movements.']);
        }

        $inventory->delete();

        return redirect()->route('inventory.index')
            ->with('success', 'Inventory item deleted successfully.');
    }

    /**
     * Adjust stock for an inventory item.
     */
    public function adjustStock(Request $request, InventoryItem $inventory)
    {
        $request->validate([
            'new_quantity' => 'required|numeric|min:0',
            'reason' => 'required|string|max:255',
            'notes' => 'nullable|string',
        ]);

        $inventory->adjustStock($request->new_quantity, $request->reason);

        return response()->json([
            'success' => true,
            'message' => 'Stock adjusted successfully.',
            'current_stock' => $inventory->fresh()->current_stock,
        ]);
    }

    /**
     * Add stock to an inventory item.
     */
    public function addStock(Request $request, InventoryItem $inventory)
    {
        $request->validate([
            'quantity' => 'required|numeric|min:0.01',
            'unit_cost' => 'nullable|numeric|min:0',
            'batch_number' => 'nullable|string|max:255',
            'expiry_date' => 'nullable|date|after:today',
            'reason' => 'required|string|max:255',
            'notes' => 'nullable|string',
        ]);

        $inventory->addStock(
            $request->quantity,
            $request->unit_cost,
            $request->batch_number,
            $request->expiry_date,
            $request->reason
        );

        return response()->json([
            'success' => true,
            'message' => 'Stock added successfully.',
            'current_stock' => $inventory->fresh()->current_stock,
        ]);
    }

    /**
     * Remove stock from an inventory item.
     */
    public function removeStock(Request $request, InventoryItem $inventory)
    {
        $request->validate([
            'quantity' => 'required|numeric|min:0.01',
            'reason' => 'required|string|max:255',
            'batch_number' => 'nullable|string|max:255',
            'notes' => 'nullable|string',
        ]);

        $success = $inventory->removeStock(
            $request->quantity,
            $request->reason,
            null,
            $request->batch_number
        );

        if (!$success) {
            return response()->json([
                'success' => false,
                'message' => 'Insufficient stock available.',
            ], 400);
        }

        return response()->json([
            'success' => true,
            'message' => 'Stock removed successfully.',
            'current_stock' => $inventory->fresh()->current_stock,
        ]);
    }

    /**
     * Get stock status options.
     */
    protected function getStockStatusOptions(): array
    {
        return [
            ['value' => 'in_stock', 'label' => 'In Stock'],
            ['value' => 'low_stock', 'label' => 'Low Stock'],
            ['value' => 'out_of_stock', 'label' => 'Out of Stock'],
            ['value' => 'needs_reorder', 'label' => 'Needs Reorder'],
        ];
    }

    /**
     * Get unit of measurement options.
     */
    protected function getUnitOptions(): array
    {
        return [
            ['value' => 'kg', 'label' => 'Kilogram (kg)'],
            ['value' => 'g', 'label' => 'Gram (g)'],
            ['value' => 'lb', 'label' => 'Pound (lb)'],
            ['value' => 'oz', 'label' => 'Ounce (oz)'],
            ['value' => 'l', 'label' => 'Liter (l)'],
            ['value' => 'ml', 'label' => 'Milliliter (ml)'],
            ['value' => 'gal', 'label' => 'Gallon (gal)'],
            ['value' => 'qt', 'label' => 'Quart (qt)'],
            ['value' => 'pt', 'label' => 'Pint (pt)'],
            ['value' => 'cup', 'label' => 'Cup'],
            ['value' => 'tbsp', 'label' => 'Tablespoon (tbsp)'],
            ['value' => 'tsp', 'label' => 'Teaspoon (tsp)'],
            ['value' => 'piece', 'label' => 'Piece'],
            ['value' => 'dozen', 'label' => 'Dozen'],
            ['value' => 'case', 'label' => 'Case'],
            ['value' => 'box', 'label' => 'Box'],
            ['value' => 'bag', 'label' => 'Bag'],
            ['value' => 'bottle', 'label' => 'Bottle'],
            ['value' => 'can', 'label' => 'Can'],
            ['value' => 'jar', 'label' => 'Jar'],
        ];
    }

    /**
     * Get storage requirement options.
     */
    protected function getStorageOptions(): array
    {
        return [
            ['value' => 'ambient', 'label' => 'Ambient Temperature'],
            ['value' => 'refrigerated', 'label' => 'Refrigerated (2-8°C)'],
            ['value' => 'frozen', 'label' => 'Frozen (-18°C or below)'],
            ['value' => 'dry', 'label' => 'Dry Storage'],
            ['value' => 'controlled', 'label' => 'Controlled Environment'],
        ];
    }
}
