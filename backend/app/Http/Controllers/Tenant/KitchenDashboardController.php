<?php

namespace App\Http\Controllers\Tenant;

use App\Http\Controllers\Controller;
use App\Models\Tenant\Order;
use App\Models\Tenant\OrderItem;
use App\Models\Tenant\Food;
use Illuminate\Http\Request;
use Inertia\Inertia;

class KitchenDashboardController extends Controller
{
    /**
     * Display the kitchen dashboard
     */
    public function index(Request $request)
    {
        $kitchen = auth()->user();
        
        // Get active orders that need kitchen attention
        $activeOrders = Order::whereIn('status', ['confirmed', 'preparing'])
            ->with([
                'customer',
                'table',
                'items' => function ($query) {
                    $query->whereIn('status', ['pending', 'preparing'])
                        ->with(['food', 'addons.foodAddon']);
                }
            ])
            ->orderBy('created_at')
            ->get();
        
        // Get individual order items for kitchen display
        $orderItems = OrderItem::whereIn('status', ['pending', 'preparing'])
            ->whereHas('order', function ($query) {
                $query->whereIn('status', ['confirmed', 'preparing']);
            })
            ->with(['order.table', 'food', 'addons.foodAddon'])
            ->orderBy('created_at')
            ->get();
        
        // Get ready orders waiting for pickup
        $readyOrders = Order::where('status', 'ready')
            ->with(['customer', 'table', 'items.food'])
            ->orderBy('ready_at')
            ->get();
        
        // Get kitchen performance metrics for today
        $todayMetrics = $this->getTodayKitchenMetrics($kitchen);
        
        // Get preparation time analytics
        $preparationAnalytics = $this->getPreparationAnalytics();
        
        // Get inventory alerts (if implemented)
        $inventoryAlerts = $this->getInventoryAlerts();

        return Inertia::render('Kitchen/Dashboard', [
            'activeOrders' => $activeOrders,
            'orderItems' => $orderItems,
            'readyOrders' => $readyOrders,
            'todayMetrics' => $todayMetrics,
            'preparationAnalytics' => $preparationAnalytics,
            'inventoryAlerts' => $inventoryAlerts,
            'kitchen' => $kitchen,
        ]);
    }

    /**
     * Get today's kitchen metrics
     */
    private function getTodayKitchenMetrics($kitchen): array
    {
        $today = today();
        
        $itemsPrepared = OrderItem::where('prepared_by', $kitchen->id)
            ->whereDate('created_at', $today)
            ->count();
        
        $averagePreparationTime = OrderItem::where('prepared_by', $kitchen->id)
            ->whereDate('created_at', $today)
            ->whereNotNull('ready_at')
            ->selectRaw('AVG(TIMESTAMPDIFF(MINUTE, started_preparing_at, ready_at)) as avg_time')
            ->value('avg_time');
        
        $ordersCompleted = Order::whereHas('items', function ($query) use ($kitchen) {
            $query->where('prepared_by', $kitchen->id);
        })
            ->whereDate('created_at', $today)
            ->where('status', 'completed')
            ->count();
        
        $pendingItems = OrderItem::whereIn('status', ['pending', 'preparing'])
            ->whereHas('order', function ($query) {
                $query->whereIn('status', ['confirmed', 'preparing']);
            })
            ->count();

        return [
            'items_prepared' => $itemsPrepared,
            'average_preparation_time' => round($averagePreparationTime ?? 0, 1),
            'orders_completed' => $ordersCompleted,
            'pending_items' => $pendingItems,
        ];
    }

    /**
     * Get preparation time analytics
     */
    private function getPreparationAnalytics(): array
    {
        // Get average preparation times by food category
        $categoryTimes = \DB::table('order_items')
            ->join('foods', 'order_items.food_id', '=', 'foods.id')
            ->join('food_categories', 'foods.food_category_id', '=', 'food_categories.id')
            ->whereNotNull('order_items.started_preparing_at')
            ->whereNotNull('order_items.ready_at')
            ->whereDate('order_items.created_at', '>=', now()->subDays(7))
            ->selectRaw('
                food_categories.name as category_name,
                AVG(TIMESTAMPDIFF(MINUTE, order_items.started_preparing_at, order_items.ready_at)) as avg_time,
                COUNT(*) as item_count
            ')
            ->groupBy('food_categories.id', 'food_categories.name')
            ->orderBy('avg_time', 'desc')
            ->get();
        
        // Get slowest items
        $slowestItems = \DB::table('order_items')
            ->join('foods', 'order_items.food_id', '=', 'foods.id')
            ->whereNotNull('order_items.started_preparing_at')
            ->whereNotNull('order_items.ready_at')
            ->whereDate('order_items.created_at', '>=', now()->subDays(7))
            ->selectRaw('
                foods.name as food_name,
                AVG(TIMESTAMPDIFF(MINUTE, order_items.started_preparing_at, order_items.ready_at)) as avg_time,
                COUNT(*) as order_count
            ')
            ->groupBy('foods.id', 'foods.name')
            ->having('order_count', '>=', 3)
            ->orderBy('avg_time', 'desc')
            ->limit(10)
            ->get();

        return [
            'category_times' => $categoryTimes,
            'slowest_items' => $slowestItems,
        ];
    }

    /**
     * Get inventory alerts
     */
    private function getInventoryAlerts(): array
    {
        // This would integrate with inventory management system
        // For now, return mock data
        return [
            'low_stock_items' => [],
            'out_of_stock_items' => [],
        ];
    }

    /**
     * Start preparing an order item
     */
    public function startPreparation(Request $request, OrderItem $orderItem)
    {
        $kitchen = auth()->user();
        
        $orderItem->updateStatus('preparing', $kitchen);
        
        // If this is the first item being prepared for the order, update order status
        if ($orderItem->order->status === 'confirmed') {
            $orderItem->order->updateStatus('preparing', $kitchen);
        }

        return response()->json([
            'message' => 'Item preparation started',
            'orderItem' => $orderItem->fresh(['food', 'addons.foodAddon'])
        ]);
    }

    /**
     * Mark order item as ready
     */
    public function markItemReady(Request $request, OrderItem $orderItem)
    {
        $kitchen = auth()->user();
        
        $orderItem->updateStatus('ready', $kitchen);
        
        // Check if all items in the order are ready
        $order = $orderItem->order;
        $allItemsReady = $order->items()->where('status', '!=', 'ready')->count() === 0;
        
        if ($allItemsReady) {
            $order->updateStatus('ready', $kitchen);
        }

        return response()->json([
            'message' => 'Item marked as ready',
            'orderItem' => $orderItem->fresh(['food', 'addons.foodAddon']),
            'orderReady' => $allItemsReady
        ]);
    }

    /**
     * Get order details for kitchen view
     */
    public function orderDetails(Order $order)
    {
        $order->load([
            'customer',
            'table',
            'items' => function ($query) {
                $query->with(['food', 'addons.foodAddon']);
            }
        ]);

        return response()->json([
            'order' => $order
        ]);
    }

    /**
     * Kitchen display system - real-time view
     */
    public function kitchenDisplay(Request $request)
    {
        // Get orders in preparation queue
        $preparationQueue = Order::whereIn('status', ['confirmed', 'preparing'])
            ->with([
                'table',
                'items' => function ($query) {
                    $query->whereIn('status', ['pending', 'preparing'])
                        ->with(['food', 'addons.foodAddon'])
                        ->orderBy('created_at');
                }
            ])
            ->orderBy('created_at')
            ->get();
        
        // Get ready orders
        $readyOrders = Order::where('status', 'ready')
            ->with(['table', 'items.food'])
            ->orderBy('ready_at')
            ->get();
        
        // Get individual items grouped by preparation stage
        $itemsByStage = [
            'pending' => OrderItem::where('status', 'pending')
                ->whereHas('order', function ($query) {
                    $query->whereIn('status', ['confirmed', 'preparing']);
                })
                ->with(['order.table', 'food', 'addons.foodAddon'])
                ->orderBy('created_at')
                ->get(),
            
            'preparing' => OrderItem::where('status', 'preparing')
                ->with(['order.table', 'food', 'addons.foodAddon'])
                ->orderBy('started_preparing_at')
                ->get(),
        ];

        return Inertia::render('Kitchen/Display', [
            'preparationQueue' => $preparationQueue,
            'readyOrders' => $readyOrders,
            'itemsByStage' => $itemsByStage,
        ]);
    }

    /**
     * Update multiple items status (bulk operations)
     */
    public function bulkUpdateItems(Request $request)
    {
        $request->validate([
            'item_ids' => 'required|array',
            'item_ids.*' => 'exists:order_items,id',
            'status' => 'required|in:preparing,ready',
        ]);

        $kitchen = auth()->user();
        
        $updatedItems = [];
        
        foreach ($request->item_ids as $itemId) {
            $orderItem = OrderItem::find($itemId);
            if ($orderItem) {
                $orderItem->updateStatus($request->status, $kitchen);
                $updatedItems[] = $orderItem->fresh(['food', 'order.table']);
            }
        }

        return response()->json([
            'message' => 'Items updated successfully',
            'updatedItems' => $updatedItems
        ]);
    }
}
