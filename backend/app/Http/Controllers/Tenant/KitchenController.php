<?php

namespace App\Http\Controllers\Tenant;

use App\Http\Controllers\Controller;
use App\Services\BranchSelectionService;
use App\Models\Tenant\Order;
use App\Models\Tenant\OrderItem;
use App\Models\Tenant\Branch;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Inertia\Inertia;
use Inertia\Response;

class KitchenController extends Controller
{
    /**
     * Display kitchen dashboard
     */
    public function index(Request $request): Response
    {
        // For debugging: Try to get or create a branch
        $branch = \App\Models\Tenant\Branch::active()->first();

        if (!$branch) {
            return Inertia::render('Tenant/Kitchen/NoBranch', [
                'error' => 'No branches found. Please create a branch first.'
            ]);
        }

        $selectedBranchId = $branch->id;

        // Get orders by kitchen status
        $pendingOrders = $this->getOrdersByKitchenStatus($selectedBranchId, 'pending');
        $receivedOrders = $this->getOrdersByKitchenStatus($selectedBranchId, 'received');
        $preparingOrders = $this->getOrdersByKitchenStatus($selectedBranchId, 'preparing');
        $readyOrders = $this->getOrdersByKitchenStatus($selectedBranchId, 'ready');

        return Inertia::render('Tenant/Kitchen/Index', [
            'branch' => $branch,
            'pendingOrders' => $pendingOrders,
            'receivedOrders' => $receivedOrders,
            'preparingOrders' => $preparingOrders,
            'readyOrders' => $readyOrders,
            'kitchenStats' => $this->getKitchenStats($selectedBranchId),
        ]);
    }

    /**
     * Display order details for kitchen
     */
    public function showOrder(Order $order): Response
    {
        $order->load([
            'items' => function ($query) {
                $query->notCancelled()->with('menuItem');
            },
            'table',
            'branch'
        ]);

        return Inertia::render('Tenant/Kitchen/OrderDetails', [
            'order' => $order,
        ]);
    }

    /**
     * Update order kitchen status
     */
    public function updateOrderStatus(Request $request, Order $order): JsonResponse
    {
        $request->validate([
            'kitchen_status' => 'required|in:received,preparing,ready,served',
            'estimated_prep_time' => 'nullable|integer|min:1|max:300',
        ]);

        try {
            $order->updateKitchenStatus($request->kitchen_status, auth()->user());

            if ($request->estimated_prep_time) {
                $order->update(['estimated_prep_time' => $request->estimated_prep_time]);
            }

            // Update all order items to the same status
            $order->items()->notCancelled()->update([
                'kitchen_status' => $request->kitchen_status
            ]);

            return response()->json([
                'success' => true,
                'order' => $order->fresh(['items']),
                'message' => 'Order status updated successfully',
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to update order status: ' . $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Update order item kitchen status
     */
    public function updateItemStatus(Request $request, OrderItem $orderItem): JsonResponse
    {
        $request->validate([
            'kitchen_status' => 'required|in:received,preparing,ready,served',
            'cook_time_minutes' => 'nullable|integer|min:1|max:300',
        ]);

        try {
            $orderItem->updateKitchenStatus($request->kitchen_status, auth()->user());

            if ($request->cook_time_minutes) {
                $orderItem->update(['cook_time_minutes' => $request->cook_time_minutes]);
            }

            // Check if all items are ready to update order status
            $this->checkAndUpdateOrderStatus($orderItem->order);

            return response()->json([
                'success' => true,
                'order_item' => $orderItem->fresh(),
                'order' => $orderItem->order->fresh(['items']),
                'message' => 'Item status updated successfully',
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to update item status: ' . $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Start preparing order
     */
    public function startPreparing(Order $order): JsonResponse
    {
        try {
            $order->updateKitchenStatus('preparing', auth()->user());
            $order->items()->notCancelled()->update([
                'kitchen_status' => 'preparing',
                'started_preparing_at' => now(),
            ]);

            return response()->json([
                'success' => true,
                'order' => $order->fresh(['items']),
                'message' => 'Order preparation started',
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to start preparation: ' . $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Mark order as ready
     */
    public function markReady(Order $order): JsonResponse
    {
        try {
            $order->updateKitchenStatus('ready', auth()->user());
            $order->items()->notCancelled()->update([
                'kitchen_status' => 'ready',
                'ready_at' => now(),
            ]);

            return response()->json([
                'success' => true,
                'order' => $order->fresh(['items']),
                'message' => 'Order marked as ready',
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to mark order as ready: ' . $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Mark order as served
     */
    public function markServed(Order $order): JsonResponse
    {
        try {
            $order->updateKitchenStatus('served', auth()->user());
            $order->items()->notCancelled()->update([
                'kitchen_status' => 'served',
                'served_at' => now(),
                'served_by' => auth()->id(),
            ]);

            return response()->json([
                'success' => true,
                'order' => $order->fresh(['items']),
                'message' => 'Order marked as served',
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to mark order as served: ' . $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Get orders by kitchen status
     */
    protected function getOrdersByKitchenStatus(int $branchId, string $status): \Illuminate\Database\Eloquent\Collection
    {
        return Order::with([
            'items' => function ($query) {
                $query->notCancelled()->with('menuItem');
            },
            'table',
            'openedBy'
        ])
        ->byBranch($branchId)
        ->byKitchenStatus($status)
        ->active()
        ->orderBy('created_at')
        ->get();
    }

    /**
     * Get kitchen statistics
     */
    protected function getKitchenStats(int $branchId): array
    {
        $today = now()->startOfDay();

        return [
            'pending_orders' => Order::byBranch($branchId)->byKitchenStatus('pending')->active()->count(),
            'preparing_orders' => Order::byBranch($branchId)->byKitchenStatus('preparing')->active()->count(),
            'ready_orders' => Order::byBranch($branchId)->byKitchenStatus('ready')->active()->count(),
            'completed_today' => Order::byBranch($branchId)->byKitchenStatus('served')->where('created_at', '>=', $today)->count(),
            'average_prep_time' => $this->getAveragePrepTime($branchId),
            'pending_items' => OrderItem::whereHas('order', function ($query) use ($branchId) {
                $query->byBranch($branchId)->active();
            })->byKitchenStatus('pending')->notCancelled()->count(),
        ];
    }

    /**
     * Get average preparation time
     */
    protected function getAveragePrepTime(int $branchId): ?int
    {
        $orders = Order::byBranch($branchId)
            ->whereNotNull('kitchen_notified_at')
            ->whereNotNull('ready_at')
            ->where('created_at', '>=', now()->subDays(7))
            ->get();

        if ($orders->isEmpty()) {
            return null;
        }

        $totalMinutes = $orders->sum(function ($order) {
            return $order->kitchen_notified_at->diffInMinutes($order->ready_at);
        });

        return round($totalMinutes / $orders->count());
    }

    /**
     * Check and update order status based on item statuses
     */
    protected function checkAndUpdateOrderStatus(Order $order): void
    {
        $items = $order->items()->notCancelled()->get();
        
        if ($items->isEmpty()) {
            return;
        }

        $allReady = $items->every(function ($item) {
            return $item->kitchen_status === 'ready';
        });

        $allServed = $items->every(function ($item) {
            return $item->kitchen_status === 'served';
        });

        if ($allServed) {
            $order->updateKitchenStatus('served', auth()->user());
        } elseif ($allReady) {
            $order->updateKitchenStatus('ready', auth()->user());
        }
    }

    /**
     * Get kitchen queue
     */
    public function getQueue(Request $request): JsonResponse
    {
        // Get branch data using the service
        $branchData = BranchSelectionService::getBranchDataForController();

        if (!$branchData['has_access']) {
            return response()->json(['error' => $branchData['error']], 400);
        }

        $selectedBranchId = $branchData['current_branch_id'];

        $orders = Order::with([
            'items' => function ($query) {
                $query->notCancelled()->with('menuItem');
            },
            'table'
        ])
        ->byBranch($selectedBranchId)
        ->whereIn('kitchen_status', ['pending', 'received', 'preparing'])
        ->active()
        ->orderBy('created_at')
        ->get();

        return response()->json([
            'success' => true,
            'orders' => $orders,
        ]);
    }
}
