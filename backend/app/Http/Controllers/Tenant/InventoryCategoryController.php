<?php

namespace App\Http\Controllers\Tenant;

use App\Http\Controllers\Controller;
use App\Models\Tenant\InventoryCategory;
use App\Models\Tenant\Restaurant;
use Illuminate\Http\Request;
use Inertia\Inertia;

class InventoryCategoryController extends Controller
{
    /**
     * Display a listing of inventory categories.
     */
    public function index(Request $request)
    {
        $query = InventoryCategory::with(['restaurant', 'parent', 'children', 'media']);

        // Search
        if ($request->search) {
            $query->where(function ($q) use ($request) {
                $q->where('name', 'like', '%' . $request->search . '%')
                  ->orWhere('description', 'like', '%' . $request->search . '%');
            });
        }

        // Filter by status
        if ($request->status) {
            if ($request->status === 'active') {
                $query->where('is_active', true);
            } elseif ($request->status === 'inactive') {
                $query->where('is_active', false);
            }
        }

        // Filter by parent category
        if ($request->parent_id) {
            $query->where('parent_id', $request->parent_id);
        } elseif ($request->has('parent_only')) {
            $query->whereNull('parent_id');
        }

        $categories = $query->ordered()->paginate(20);

        // Get parent categories for filter
        $parentCategories = InventoryCategory::parents()->active()->ordered()->get();

        // Get category statistics
        $categoryStats = $categories->getCollection()->map(function ($category) {
            return [
                'id' => $category->id,
                'total_value' => $category->getTotalInventoryValue(),
                'low_stock_count' => $category->getLowStockItemsCount(),
                'expired_count' => $category->getExpiredItemsCount(),
                'expiring_soon_count' => $category->getExpiringSoonItemsCount(),
            ];
        })->keyBy('id');

        return Inertia::render('Tenant/InventoryCategories/Index', [
            'categories' => $categories,
            'parentCategories' => $parentCategories,
            'categoryStats' => $categoryStats,
            'filters' => $request->only(['search', 'status', 'parent_id', 'parent_only']),
        ]);
    }

    /**
     * Show the form for creating a new inventory category.
     */
    public function create()
    {
        $restaurant = Restaurant::first();
        $parentCategories = InventoryCategory::parents()->active()->ordered()->get();

        return Inertia::render('Tenant/InventoryCategories/Create', [
            'restaurant' => $restaurant,
            'parentCategories' => $parentCategories,
            'storageOptions' => $this->getStorageOptions(),
        ]);
    }

    /**
     * Store a newly created inventory category.
     */
    public function store(Request $request)
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'description' => 'nullable|string',
            'color_code' => 'nullable|string|regex:/^#[0-9A-Fa-f]{6}$/',
            'icon' => 'nullable|string|max:255',
            'parent_id' => 'nullable|exists:inventory_categories,id',
            'storage_requirements' => 'nullable|in:ambient,refrigerated,frozen,dry,controlled',
            'shelf_life_days' => 'nullable|integer|min:1',
            'sort_order' => 'nullable|integer|min:0',
            'is_active' => 'boolean',
            'media_ids' => 'nullable|array',
            'media_ids.*' => 'exists:media,id',
        ]);

        $restaurant = Restaurant::first();

        $category = InventoryCategory::create([
            'restaurant_id' => $restaurant->id,
            'name' => $request->name,
            'description' => $request->description,
            'color_code' => $request->color_code,
            'icon' => $request->icon,
            'parent_id' => $request->parent_id,
            'storage_requirements' => $request->storage_requirements,
            'shelf_life_days' => $request->shelf_life_days,
            'sort_order' => $request->sort_order ?? 0,
            'is_active' => $request->boolean('is_active', true),
        ]);

        // Attach media
        if ($request->media_ids) {
            foreach ($request->media_ids as $mediaId) {
                $category->addMediaFromLibrary($mediaId, 'images');
            }
        }

        return redirect()->route('inventory-categories.index')
            ->with('success', 'Inventory category created successfully.');
    }

    /**
     * Display the specified inventory category.
     */
    public function show(InventoryCategory $inventoryCategory)
    {
        $inventoryCategory->load([
            'restaurant', 'parent', 'children', 'media',
            'inventoryItems' => function ($query) {
                $query->with(['vendor'])->active()->latest()->take(10);
            }
        ]);

        // Get category statistics
        $stats = [
            'total_items' => $inventoryCategory->inventoryItems()->active()->count(),
            'total_value' => $inventoryCategory->getTotalInventoryValue(),
            'low_stock_items' => $inventoryCategory->getLowStockItemsCount(),
            'expired_items' => $inventoryCategory->getExpiredItemsCount(),
            'expiring_soon_items' => $inventoryCategory->getExpiringSoonItemsCount(),
        ];

        return Inertia::render('Tenant/InventoryCategories/Show', [
            'category' => $inventoryCategory,
            'stats' => $stats,
        ]);
    }

    /**
     * Show the form for editing the specified inventory category.
     */
    public function edit(InventoryCategory $inventoryCategory)
    {
        $inventoryCategory->load(['media']);
        $restaurant = Restaurant::first();
        $parentCategories = InventoryCategory::parents()
            ->active()
            ->where('id', '!=', $inventoryCategory->id)
            ->ordered()
            ->get();

        return Inertia::render('Tenant/InventoryCategories/Edit', [
            'category' => $inventoryCategory,
            'restaurant' => $restaurant,
            'parentCategories' => $parentCategories,
            'storageOptions' => $this->getStorageOptions(),
        ]);
    }

    /**
     * Update the specified inventory category.
     */
    public function update(Request $request, InventoryCategory $inventoryCategory)
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'description' => 'nullable|string',
            'color_code' => 'nullable|string|regex:/^#[0-9A-Fa-f]{6}$/',
            'icon' => 'nullable|string|max:255',
            'parent_id' => 'nullable|exists:inventory_categories,id|not_in:' . $inventoryCategory->id,
            'storage_requirements' => 'nullable|in:ambient,refrigerated,frozen,dry,controlled',
            'shelf_life_days' => 'nullable|integer|min:1',
            'sort_order' => 'nullable|integer|min:0',
            'is_active' => 'boolean',
            'media_ids' => 'nullable|array',
            'media_ids.*' => 'exists:media,id',
        ]);

        $inventoryCategory->update([
            'name' => $request->name,
            'description' => $request->description,
            'color_code' => $request->color_code,
            'icon' => $request->icon,
            'parent_id' => $request->parent_id,
            'storage_requirements' => $request->storage_requirements,
            'shelf_life_days' => $request->shelf_life_days,
            'sort_order' => $request->sort_order ?? 0,
            'is_active' => $request->boolean('is_active'),
        ]);

        // Update media
        $inventoryCategory->clearMediaCollection('images');
        if ($request->media_ids) {
            foreach ($request->media_ids as $mediaId) {
                $inventoryCategory->addMediaFromLibrary($mediaId, 'images');
            }
        }

        return redirect()->route('inventory-categories.index')
            ->with('success', 'Inventory category updated successfully.');
    }

    /**
     * Remove the specified inventory category.
     */
    public function destroy(InventoryCategory $inventoryCategory)
    {
        // Check if category has inventory items
        if ($inventoryCategory->inventoryItems()->exists()) {
            return back()->withErrors(['error' => 'Cannot delete category with existing inventory items.']);
        }

        // Check if category has child categories
        if ($inventoryCategory->children()->exists()) {
            return back()->withErrors(['error' => 'Cannot delete category with subcategories. Please delete or move subcategories first.']);
        }

        $inventoryCategory->delete();

        return redirect()->route('inventory-categories.index')
            ->with('success', 'Inventory category deleted successfully.');
    }

    /**
     * Toggle category status.
     */
    public function toggleStatus(InventoryCategory $inventoryCategory)
    {
        $inventoryCategory->update(['is_active' => !$inventoryCategory->is_active]);

        $status = $inventoryCategory->is_active ? 'activated' : 'deactivated';
        
        return response()->json([
            'success' => true,
            'message' => "Inventory category {$status} successfully.",
            'is_active' => $inventoryCategory->is_active,
        ]);
    }

    /**
     * Reorder categories.
     */
    public function reorder(Request $request)
    {
        $request->validate([
            'categories' => 'required|array',
            'categories.*.id' => 'required|exists:inventory_categories,id',
            'categories.*.sort_order' => 'required|integer|min:0',
        ]);

        foreach ($request->categories as $categoryData) {
            InventoryCategory::where('id', $categoryData['id'])
                ->update(['sort_order' => $categoryData['sort_order']]);
        }

        return response()->json([
            'success' => true,
            'message' => 'Categories reordered successfully.',
        ]);
    }

    /**
     * Bulk update categories.
     */
    public function bulkUpdate(Request $request)
    {
        $request->validate([
            'ids' => 'required|array',
            'ids.*' => 'exists:inventory_categories,id',
            'action' => 'required|in:activate,deactivate,delete',
            'storage_requirements' => 'nullable|in:ambient,refrigerated,frozen,dry,controlled',
        ]);

        $categories = InventoryCategory::whereIn('id', $request->ids);

        switch ($request->action) {
            case 'activate':
                $categories->update(['is_active' => true]);
                $message = 'Categories activated successfully.';
                break;
            case 'deactivate':
                $categories->update(['is_active' => false]);
                $message = 'Categories deactivated successfully.';
                break;
            case 'delete':
                // Check if any category has inventory items or children
                $hasItems = $categories->whereHas('inventoryItems')->exists();
                $hasChildren = $categories->whereHas('children')->exists();
                
                if ($hasItems) {
                    return back()->withErrors(['error' => 'Cannot delete categories with existing inventory items.']);
                }
                if ($hasChildren) {
                    return back()->withErrors(['error' => 'Cannot delete categories with subcategories.']);
                }
                
                $categories->delete();
                $message = 'Categories deleted successfully.';
                break;
        }

        // Update storage requirements if provided
        if ($request->storage_requirements && $request->action !== 'delete') {
            InventoryCategory::whereIn('id', $request->ids)
                ->update(['storage_requirements' => $request->storage_requirements]);
        }

        return back()->with('success', $message);
    }

    /**
     * Get category hierarchy for API.
     */
    public function hierarchy()
    {
        $categories = InventoryCategory::with(['children' => function ($query) {
            $query->active()->ordered();
        }])
        ->parents()
        ->active()
        ->ordered()
        ->get();

        return response()->json($categories);
    }

    /**
     * Get storage requirement options.
     */
    protected function getStorageOptions(): array
    {
        return [
            ['value' => 'ambient', 'label' => 'Ambient Temperature'],
            ['value' => 'refrigerated', 'label' => 'Refrigerated (2-8°C)'],
            ['value' => 'frozen', 'label' => 'Frozen (-18°C or below)'],
            ['value' => 'dry', 'label' => 'Dry Storage'],
            ['value' => 'controlled', 'label' => 'Controlled Environment'],
        ];
    }
}
