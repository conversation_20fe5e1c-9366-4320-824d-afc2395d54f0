<?php

namespace App\Http\Controllers\Tenant;

use App\Http\Controllers\Controller;
use App\Models\Tenant\Branch;
use App\Models\Tenant\Employee;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Validator;
use Inertia\Inertia;

class BranchController extends Controller
{
    /**
     * Display a listing of branches.
     */
    public function index(Request $request)
    {
        $query = Branch::with(['manager', 'employees']);

        // Search
        if ($request->search) {
            $query->where(function ($q) use ($request) {
                $q->where('name', 'like', '%' . $request->search . '%')
                  ->orWhere('address', 'like', '%' . $request->search . '%')
                  ->orWhere('city', 'like', '%' . $request->search . '%')
                  ->orWhere('phone', 'like', '%' . $request->search . '%');
            });
        }

        // Filter by status
        if ($request->status) {
            if ($request->status === 'active') {
                $query->where('is_active', true);
            } elseif ($request->status === 'inactive') {
                $query->where('is_active', false);
            }
        }

        // Filter by manager
        if ($request->manager_id) {
            $query->where('manager_id', $request->manager_id);
        }

        // Sort
        $sortField = $request->sort ?? 'sort_order';
        $sortDirection = $request->direction ?? 'asc';

        if ($sortField === 'manager_name') {
            $query->join('employees', 'branches.manager_id', '=', 'employees.id')
                  ->orderBy('employees.name', $sortDirection)
                  ->select('branches.*');
        } else {
            $query->orderBy($sortField, $sortDirection);
        }

        $branches = $query->paginate(20);

        // Get managers for filter dropdown
        try {
            $managers = Employee::whereHas('roles', function ($q) {
                $q->where('name', 'Manager');
            })->select('id', 'name')->get();
        } catch (\Exception $e) {
            // If there's an issue with roles, just get all employees
            $managers = Employee::select('id', 'first_name')->get();
        }

        // Ensure we always have valid data structures
        $branchesData = $branches ?? Branch::paginate(20);
        $managersData = $managers ?? collect();
        $filtersData = $request->only(['search', 'status', 'manager_id', 'sort', 'direction']) ?? [];

        // Debug logging to help identify issues
        Log::info('Branches Index Data', [
            'branches_count' => $branchesData->count(),
            'managers_count' => $managersData->count(),
            'filters' => $filtersData,
            'has_branches_data' => isset($branchesData->data),
        ]);

        return Inertia::render('Tenant/Branches/Index', [
            'branches' => $branchesData,
            'managers' => $managersData,
            'filters' => $filtersData,
        ]);
    }

    /**
     * Show the form for creating a new branch.
     */
    public function create()
    {
        return Inertia::render('Tenant/Branches/Create');
    }

    /**
     * Store a newly created branch.
     */
    public function store(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'name' => 'required|string|max:255',
            'address' => 'required|string|max:500',
            'city' => 'required|string|max:100',
            'phone' => 'nullable|string|max:20',
            'email' => 'nullable|email|max:255',
            'opening_time' => 'required|date_format:H:i',
            'closing_time' => 'required|date_format:H:i|after:opening_time',
            'latitude' => 'nullable|numeric|between:-90,90',
            'longitude' => 'nullable|numeric|between:-180,180',
            'formatted_address' => 'nullable|string|max:1000',
        ]);

        if ($validator->fails()) {
            return back()->withErrors($validator)->withInput();
        }

        $data = $validator->validated();

        // Set default values for simplified form
        $data['is_active'] = true;
        $data['is_main_branch'] = false;
        $data['sort_order'] = 0;

        $branch = Branch::create($data);

        return redirect()->route('manager.branches.index')
            ->with('success', 'Branch created successfully.');
    }

    /**
     * Display the specified branch.
     */
    public function show(Branch $branch)
    {
        $branch->load(['manager', 'employees.department', 'shifts' => function ($query) {
            $query->with('employee')->latest()->limit(10);
        }]);

        return Inertia::render('Tenant/Branches/Show', [
            'branch' => $branch,
        ]);
    }

    /**
     * Show the form for editing the specified branch.
     */
    public function edit(Branch $branch)
    {
        return Inertia::render('Tenant/Branches/Edit', [
            'branch' => $branch,
        ]);
    }

    /**
     * Update the specified branch.
     */
    public function update(Request $request, Branch $branch)
    {
        $validator = Validator::make($request->all(), [
            'name' => 'required|string|max:255',
            'address' => 'required|string|max:500',
            'city' => 'required|string|max:100',
            'phone' => 'nullable|string|max:20',
            'email' => 'nullable|email|max:255',
            'opening_time' => 'required|date_format:H:i',
            'closing_time' => 'required|date_format:H:i|after:opening_time',
            'latitude' => 'nullable|numeric|between:-90,90',
            'longitude' => 'nullable|numeric|between:-180,180',
            'formatted_address' => 'nullable|string|max:1000',
        ]);

        if ($validator->fails()) {
            return back()->withErrors($validator)->withInput();
        }

        $data = $validator->validated();

        $branch->update($data);

        return redirect()->route('manager.branches.index')
            ->with('success', 'Branch updated successfully.');
    }

    /**
     * Remove the specified branch.
     */
    public function destroy(Branch $branch)
    {
        // Check if branch has employees
        if ($branch->employees()->count() > 0) {
            return back()->withErrors(['error' => 'Cannot delete branch with assigned employees.']);
        }

        // Check if it's the main branch
        if ($branch->is_main_branch) {
            return back()->withErrors(['error' => 'Cannot delete the main branch.']);
        }

        $branch->delete();

        return redirect()->route('manager.branches.index')
            ->with('success', 'Branch deleted successfully.');
    }

    /**
     * Toggle branch status.
     */
    public function toggleStatus(Branch $branch)
    {
        $branch->update(['is_active' => !$branch->is_active]);

        $status = $branch->is_active ? 'activated' : 'deactivated';

        return response()->json([
            'success' => true,
            'message' => "Branch {$status} successfully.",
            'is_active' => $branch->is_active,
        ]);
    }

    /**
     * Bulk update branches.
     */
    public function bulkUpdate(Request $request)
    {
        $request->validate([
            'ids' => 'required|array',
            'ids.*' => 'exists:branches,id',
            'action' => 'required|in:activate,deactivate,delete',
        ]);

        $branches = Branch::whereIn('id', $request->ids);

        switch ($request->action) {
            case 'activate':
                $branches->update(['is_active' => true]);
                $message = 'Branches activated successfully.';
                break;
            case 'deactivate':
                $branches->update(['is_active' => false]);
                $message = 'Branches deactivated successfully.';
                break;
            case 'delete':
                // Check if any branch has employees or is main branch
                $hasEmployees = $branches->whereHas('employees')->exists();
                $hasMainBranch = $branches->where('is_main_branch', true)->exists();

                if ($hasEmployees) {
                    return back()->withErrors(['error' => 'Cannot delete branches with assigned employees.']);
                }

                if ($hasMainBranch) {
                    return back()->withErrors(['error' => 'Cannot delete the main branch.']);
                }

                $branches->delete();
                $message = 'Branches deleted successfully.';
                break;
        }

        return back()->with('success', $message);
    }

    /**
     * Get branches for API/AJAX requests.
     */
    public function getBranches()
    {
        $branches = Branch::active()
            ->ordered()
            ->select('id', 'name', 'address', 'city')
            ->get();

        return response()->json($branches);
    }
}
