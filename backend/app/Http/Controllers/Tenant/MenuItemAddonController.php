<?php

namespace App\Http\Controllers\Tenant;

use App\Http\Controllers\Controller;
use App\Models\Tenant\MenuItemAddon;
use Illuminate\Http\Request;
use Inertia\Inertia;
use Illuminate\Validation\Rule;

class MenuItemAddonController extends Controller
{
    /**
     * Display a listing of the addons.
     */
    public function index(Request $request)
    {
        $query = MenuItemAddon::query();

        // Search functionality
        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('name_bn', 'like', "%{$search}%")
                  ->orWhere('category', 'like', "%{$search}%");
            });
        }

        // Filter by category
        if ($request->filled('category')) {
            $query->where('category', $request->category);
        }

        // Filter by availability
        if ($request->filled('is_available')) {
            $query->where('is_available', $request->boolean('is_available'));
        }

        $addons = $query->ordered()->paginate(15)->withQueryString();

        // Get categories for filter dropdown
        $categories = MenuItemAddon::getCategories();

        return Inertia::render('Tenant/MenuItemAddons/Index', [
            'addons' => $addons,
            'categories' => $categories,
            'filters' => $request->only(['search', 'category', 'is_available']),
        ]);
    }

    /**
     * Show the form for creating a new addon.
     */
    public function create()
    {
        $categories = MenuItemAddon::getCategories();

        return Inertia::render('Tenant/MenuItemAddons/Create', [
            'categories' => $categories,
        ]);
    }

    /**
     * Store a newly created addon in storage.
     */
    public function store(Request $request)
    {
        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'name_bn' => 'nullable|string|max:255',
            'price' => 'required|numeric|min:0',
            'category' => 'required|string|max:100',
            'category_bn' => 'nullable|string|max:100',
            'max_quantity' => 'required|integer|min:1|max:10',
            'is_available' => 'boolean',
            'sort_order' => 'integer|min:0',
            'description' => 'nullable|string|max:1000',
            'description_bn' => 'nullable|string|max:1000',
        ]);

        $addon = MenuItemAddon::create($validated);

        return redirect()->route('menu-item-addons.index')
            ->with('success', 'Addon created successfully.');
    }

    /**
     * Display the specified addon.
     */
    public function show(MenuItemAddon $menuItemAddon)
    {
        $menuItemAddon->load('menuItems');

        return Inertia::render('Tenant/MenuItemAddons/Show', [
            'addon' => $menuItemAddon,
        ]);
    }

    /**
     * Show the form for editing the specified addon.
     */
    public function edit(MenuItemAddon $menuItemAddon)
    {
        $categories = MenuItemAddon::getCategories();

        return Inertia::render('Tenant/MenuItemAddons/Edit', [
            'addon' => $menuItemAddon,
            'categories' => $categories,
        ]);
    }

    /**
     * Update the specified addon in storage.
     */
    public function update(Request $request, MenuItemAddon $menuItemAddon)
    {
        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'name_bn' => 'nullable|string|max:255',
            'price' => 'required|numeric|min:0',
            'category' => 'required|string|max:100',
            'category_bn' => 'nullable|string|max:100',
            'max_quantity' => 'required|integer|min:1|max:10',
            'is_available' => 'boolean',
            'sort_order' => 'integer|min:0',
            'description' => 'nullable|string|max:1000',
            'description_bn' => 'nullable|string|max:1000',
        ]);

        $menuItemAddon->update($validated);

        return redirect()->route('menu-item-addons.index')
            ->with('success', 'Addon updated successfully.');
    }

    /**
     * Remove the specified addon from storage.
     */
    public function destroy(MenuItemAddon $menuItemAddon)
    {
        $menuItemAddon->delete();

        return redirect()->route('menu-item-addons.index')
            ->with('success', 'Addon deleted successfully.');
    }

    /**
     * Toggle addon availability.
     */
    public function toggleAvailability(MenuItemAddon $menuItemAddon)
    {
        $menuItemAddon->update([
            'is_available' => !$menuItemAddon->is_available
        ]);

        $status = $menuItemAddon->is_available ? 'enabled' : 'disabled';

        return back()->with('success', "Addon {$status} successfully.");
    }

    /**
     * Bulk update addon availability.
     */
    public function bulkToggleAvailability(Request $request)
    {
        $validated = $request->validate([
            'addon_ids' => 'required|array',
            'addon_ids.*' => 'exists:menu_item_addons,id',
            'is_available' => 'required|boolean',
        ]);

        MenuItemAddon::whereIn('id', $validated['addon_ids'])
            ->update(['is_available' => $validated['is_available']]);

        $status = $validated['is_available'] ? 'enabled' : 'disabled';
        $count = count($validated['addon_ids']);

        return back()->with('success', "{$count} addons {$status} successfully.");
    }

    /**
     * Get addons for API (used in menu item forms).
     */
    public function getAddons(Request $request)
    {
        $query = MenuItemAddon::available();

        if ($request->filled('category')) {
            $query->byCategory($request->category);
        }

        $addons = $query->ordered()->get();

        return response()->json($addons);
    }
}
