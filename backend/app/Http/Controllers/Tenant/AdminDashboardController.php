<?php

namespace App\Http\Controllers\Tenant;

use App\Http\Controllers\Controller;
use App\Models\User;
use App\Models\Tenant\Order;
use App\Models\Customer;
use App\Models\MenuItem;
use Illuminate\Http\Request;
use Inertia\Inertia;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;

class AdminDashboardController extends Controller
{
    public function __construct()
    {
        $this->middleware(['auth', 'role:admin']);
    }

    /**
     * Display the admin dashboard
     */
    public function index(Request $request)
    {
        $stats = $this->getStats();
        $recentTenants = $this->getRecentTenants();
        $recentPayments = $this->getRecentPayments();
        $monthlyRevenue = $this->getMonthlyRevenue();

        return Inertia::render('Admin/Dashboard', [
            'stats' => $stats,
            'recentTenants' => $recentTenants,
            'recentPayments' => $recentPayments,
            'monthlyRevenue' => $monthlyRevenue,
        ]);
    }

    /**
     * Get dashboard statistics
     */
    private function getStats(): array
    {
        $today = Carbon::today();
        $thisMonth = Carbon::now()->startOfMonth();
        $lastMonth = Carbon::now()->subMonth()->startOfMonth();

        return [
            'total_tenants' => 1, // Current tenant
            'active_tenants' => 1, // Current tenant is active
            'trial_tenants' => 0, // Not applicable for tenant admin
            'monthly_revenue' => Order::where('created_at', '>=', $thisMonth)
                ->where('status', 'completed')
                ->sum('total_amount'),
            'total_orders' => Order::count(),
            'todays_orders' => Order::whereDate('created_at', $today)->count(),
            'total_customers' => Customer::count(),
            'total_menu_items' => MenuItem::count(),
            'pending_orders' => Order::where('status', 'pending')->count(),
            'completed_orders_today' => Order::whereDate('created_at', $today)
                ->where('status', 'completed')
                ->count(),
            'revenue_today' => Order::whereDate('created_at', $today)
                ->where('status', 'completed')
                ->sum('total_amount'),
            'average_order_value' => Order::where('status', 'completed')
                ->avg('total_amount'),
            'total_staff' => User::count(),
            'active_staff' => User::where('is_active', true)->count(),
        ];
    }

    /**
     * Get recent tenants (for tenant admin, this would be recent customers or staff)
     */
    private function getRecentTenants(): array
    {
        // For tenant admin, show recent customers instead of tenants
        return Customer::latest()
            ->take(5)
            ->get()
            ->map(function ($customer) {
                return [
                    'id' => $customer->id,
                    'name' => $customer->name,
                    'email' => $customer->email,
                    'subscription_status' => 'active', // Customers are always active
                    'created_at' => $customer->created_at,
                ];
            })
            ->toArray();
    }

    /**
     * Get recent payments (orders for tenant admin)
     */
    private function getRecentPayments(): array
    {
        return Order::with(['customer'])
            ->where('status', 'completed')
            ->latest()
            ->take(10)
            ->get()
            ->map(function ($order) {
                return [
                    'id' => $order->id,
                    'tenant_name' => $order->customer->name ?? 'Guest',
                    'amount' => $order->total_amount,
                    'status' => 'completed',
                    'created_at' => $order->created_at,
                ];
            })
            ->toArray();
    }

    /**
     * Get monthly revenue data
     */
    private function getMonthlyRevenue(): array
    {
        $months = [];
        for ($i = 11; $i >= 0; $i--) {
            $date = Carbon::now()->subMonths($i);
            $revenue = Order::whereYear('created_at', $date->year)
                ->whereMonth('created_at', $date->month)
                ->where('status', 'completed')
                ->sum('total_amount');
            
            $months[] = [
                'month' => $date->format('M Y'),
                'revenue' => $revenue,
            ];
        }

        return $months;
    }

    /**
     * Get analytics data
     */
    public function analytics(Request $request)
    {
        $period = $request->get('period', '30'); // days
        $startDate = Carbon::now()->subDays($period);

        $analytics = [
            'sales_trend' => $this->getSalesTrend($startDate),
            'top_menu_items' => $this->getTopMenuItems($startDate),
            'customer_analytics' => $this->getCustomerAnalytics($startDate),
            'order_analytics' => $this->getOrderAnalytics($startDate),
            'staff_performance' => $this->getStaffPerformance($startDate),
        ];

        return response()->json([
            'success' => true,
            'data' => $analytics,
        ]);
    }

    /**
     * Get sales trend data
     */
    private function getSalesTrend(Carbon $startDate): array
    {
        return Order::where('created_at', '>=', $startDate)
            ->where('status', 'completed')
            ->selectRaw('DATE(created_at) as date, SUM(total_amount) as revenue, COUNT(*) as orders')
            ->groupBy('date')
            ->orderBy('date')
            ->get()
            ->toArray();
    }

    /**
     * Get top performing menu items
     */
    private function getTopMenuItems(Carbon $startDate): array
    {
        return DB::table('order_items')
            ->join('orders', 'order_items.order_id', '=', 'orders.id')
            ->join('menu_items', 'order_items.menu_item_id', '=', 'menu_items.id')
            ->where('orders.created_at', '>=', $startDate)
            ->where('orders.status', 'completed')
            ->selectRaw('
                menu_items.name,
                menu_items.id,
                SUM(order_items.quantity) as total_quantity,
                SUM(order_items.price * order_items.quantity) as total_revenue,
                COUNT(DISTINCT orders.id) as order_count
            ')
            ->groupBy('menu_items.id', 'menu_items.name')
            ->orderByDesc('total_quantity')
            ->limit(10)
            ->get()
            ->toArray();
    }

    /**
     * Get customer analytics
     */
    private function getCustomerAnalytics(Carbon $startDate): array
    {
        $totalCustomers = Customer::count();
        $newCustomers = Customer::where('created_at', '>=', $startDate)->count();
        $returningCustomers = Customer::whereHas('orders', function ($query) use ($startDate) {
            $query->where('created_at', '>=', $startDate);
        })->whereHas('orders', function ($query) use ($startDate) {
            $query->where('created_at', '<', $startDate);
        })->count();

        return [
            'total_customers' => $totalCustomers,
            'new_customers' => $newCustomers,
            'returning_customers' => $returningCustomers,
            'customer_retention_rate' => $totalCustomers > 0 ? round(($returningCustomers / $totalCustomers) * 100, 2) : 0,
        ];
    }

    /**
     * Get order analytics
     */
    private function getOrderAnalytics(Carbon $startDate): array
    {
        $orders = Order::where('created_at', '>=', $startDate);

        return [
            'total_orders' => $orders->count(),
            'completed_orders' => $orders->where('status', 'completed')->count(),
            'pending_orders' => $orders->where('status', 'pending')->count(),
            'cancelled_orders' => $orders->where('status', 'cancelled')->count(),
            'average_order_value' => $orders->where('status', 'completed')->avg('total_amount'),
            'total_revenue' => $orders->where('status', 'completed')->sum('total_amount'),
        ];
    }

    /**
     * Get staff performance data
     */
    private function getStaffPerformance(Carbon $startDate): array
    {
        // This would require additional tracking of staff activities
        // For now, return basic staff counts
        return [
            'total_staff' => User::count(),
            'active_staff' => User::where('is_active', true)->count(),
            'managers' => User::role('restaurant_manager')->count(),
            'waiters' => User::role('waiter')->count(),
            'kitchen_staff' => User::role('kitchen')->count(),
            'delivery_staff' => User::role('delivery')->count(),
        ];
    }

    /**
     * Export dashboard data
     */
    public function export(Request $request)
    {
        $format = $request->get('format', 'csv');
        $data = [
            'stats' => $this->getStats(),
            'monthly_revenue' => $this->getMonthlyRevenue(),
            'exported_at' => now()->toISOString(),
        ];

        $filename = 'admin_dashboard_' . now()->format('Y_m_d_H_i_s') . '.' . $format;

        if ($format === 'json') {
            return response()->json($data)
                ->header('Content-Disposition', 'attachment; filename="' . $filename . '"');
        }

        // For CSV format, flatten the data
        $csvData = [];
        foreach ($data['stats'] as $key => $value) {
            $csvData[] = [$key, $value];
        }

        $headers = [
            'Content-Type' => 'text/csv',
            'Content-Disposition' => 'attachment; filename="' . $filename . '"',
        ];

        $callback = function () use ($csvData) {
            $file = fopen('php://output', 'w');
            fputcsv($file, ['Metric', 'Value']);
            foreach ($csvData as $row) {
                fputcsv($file, $row);
            }
            fclose($file);
        };

        return response()->stream($callback, 200, $headers);
    }
}
