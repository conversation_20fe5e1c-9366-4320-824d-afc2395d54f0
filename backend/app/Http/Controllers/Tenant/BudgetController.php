<?php

namespace App\Http\Controllers\Tenant;

use App\Http\Controllers\Controller;
use App\Models\Tenant\Budget;
use App\Models\Tenant\ExpenseCategory;
use App\Models\Tenant\Restaurant;
use Illuminate\Http\Request;
use Inertia\Inertia;
use Carbon\Carbon;

class BudgetController extends Controller
{
    /**
     * Display a listing of budgets.
     */
    public function index(Request $request)
    {
        $query = Budget::with(['restaurant', 'category', 'creator']);

        // Search
        if ($request->search) {
            $query->where(function ($q) use ($request) {
                $q->where('name', 'like', '%' . $request->search . '%')
                  ->orWhere('description', 'like', '%' . $request->search . '%');
            });
        }

        // Filter by budget type
        if ($request->budget_type) {
            $query->where('budget_type', $request->budget_type);
        }

        // Filter by period type
        if ($request->period_type) {
            $query->where('period_type', $request->period_type);
        }

        // Filter by status
        if ($request->status) {
            switch ($request->status) {
                case 'active':
                    $query->where('is_active', true);
                    break;
                case 'inactive':
                    $query->where('is_active', false);
                    break;
                case 'current':
                    $query->current();
                    break;
                case 'exceeded':
                    $query->exceeded();
                    break;
            }
        }

        // Filter by category
        if ($request->category_id) {
            $query->where('expense_category_id', $request->category_id);
        }

        $budgets = $query->latest('start_date')->paginate(20);

        // Update spent amounts for all budgets
        $budgets->getCollection()->each(function ($budget) {
            $budget->updateSpentAmount();
        });

        $categories = ExpenseCategory::active()->ordered()->get();

        return Inertia::render('Tenant/Budgets/Index', [
            'budgets' => $budgets,
            'categories' => $categories,
            'filters' => $request->only(['search', 'budget_type', 'period_type', 'status', 'category_id']),
            'budgetTypeOptions' => $this->getBudgetTypeOptions(),
            'periodTypeOptions' => $this->getPeriodTypeOptions(),
        ]);
    }

    /**
     * Show budget dashboard.
     */
    public function dashboard()
    {
        $currentBudgets = Budget::current()->active()->get();
        
        // Update spent amounts
        $currentBudgets->each(function ($budget) {
            $budget->updateSpentAmount();
        });

        $totalAllocated = $currentBudgets->sum('allocated_amount');
        $totalSpent = $currentBudgets->sum('spent_amount');
        $totalRemaining = $currentBudgets->sum('remaining_amount');
        $exceededBudgets = $currentBudgets->filter(function ($budget) {
            return $budget->isExceeded();
        });

        $budgetSummary = [
            'total_allocated' => $totalAllocated,
            'total_spent' => $totalSpent,
            'total_remaining' => $totalRemaining,
            'utilization_percentage' => $totalAllocated > 0 ? ($totalSpent / $totalAllocated) * 100 : 0,
            'exceeded_count' => $exceededBudgets->count(),
            'warning_count' => $currentBudgets->filter(function ($budget) {
                return $budget->isAlertThresholdReached() && !$budget->isExceeded();
            })->count(),
        ];

        return Inertia::render('Tenant/Budgets/Dashboard', [
            'budgets' => $currentBudgets,
            'budgetSummary' => $budgetSummary,
        ]);
    }

    /**
     * Show the form for creating a new budget.
     */
    public function create()
    {
        $restaurant = Restaurant::first();
        $categories = ExpenseCategory::active()->ordered()->get();

        return Inertia::render('Tenant/Budgets/Create', [
            'restaurant' => $restaurant,
            'categories' => $categories,
            'budgetTypeOptions' => $this->getBudgetTypeOptions(),
            'periodTypeOptions' => $this->getPeriodTypeOptions(),
        ]);
    }

    /**
     * Store a newly created budget.
     */
    public function store(Request $request)
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'description' => 'nullable|string',
            'budget_type' => 'required|in:operational,capital,project,department',
            'period_type' => 'required|in:monthly,quarterly,yearly,custom',
            'start_date' => 'required|date',
            'end_date' => 'required|date|after:start_date',
            'allocated_amount' => 'required|numeric|min:0',
            'expense_category_id' => 'nullable|exists:expense_categories,id',
            'alert_threshold' => 'nullable|numeric|min:0|max:100',
            'is_active' => 'boolean',
        ]);

        $restaurant = Restaurant::first();

        $budget = Budget::create([
            'restaurant_id' => $restaurant->id,
            'expense_category_id' => $request->expense_category_id,
            'name' => $request->name,
            'description' => $request->description,
            'budget_type' => $request->budget_type,
            'period_type' => $request->period_type,
            'start_date' => $request->start_date,
            'end_date' => $request->end_date,
            'allocated_amount' => $request->allocated_amount,
            'alert_threshold' => $request->alert_threshold ?? 80,
            'is_active' => $request->boolean('is_active', true),
            'created_by' => auth()->id(),
        ]);

        // Update spent amount
        $budget->updateSpentAmount();

        return redirect()->route('budgets.index')
            ->with('success', 'Budget created successfully.');
    }

    /**
     * Display the specified budget.
     */
    public function show(Budget $budget)
    {
        $budget->load(['restaurant', 'category', 'creator', 'alerts' => function ($query) {
            $query->latest()->take(10);
        }]);

        // Update spent amount
        $budget->updateSpentAmount();

        // Get performance comparison
        $performanceComparison = $budget->getPerformanceComparison();

        return Inertia::render('Tenant/Budgets/Show', [
            'budget' => $budget,
            'performanceComparison' => $performanceComparison,
        ]);
    }

    /**
     * Show the form for editing the specified budget.
     */
    public function edit(Budget $budget)
    {
        $restaurant = Restaurant::first();
        $categories = ExpenseCategory::active()->ordered()->get();

        return Inertia::render('Tenant/Budgets/Edit', [
            'budget' => $budget,
            'restaurant' => $restaurant,
            'categories' => $categories,
            'budgetTypeOptions' => $this->getBudgetTypeOptions(),
            'periodTypeOptions' => $this->getPeriodTypeOptions(),
        ]);
    }

    /**
     * Update the specified budget.
     */
    public function update(Request $request, Budget $budget)
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'description' => 'nullable|string',
            'budget_type' => 'required|in:operational,capital,project,department',
            'period_type' => 'required|in:monthly,quarterly,yearly,custom',
            'start_date' => 'required|date',
            'end_date' => 'required|date|after:start_date',
            'allocated_amount' => 'required|numeric|min:0',
            'expense_category_id' => 'nullable|exists:expense_categories,id',
            'alert_threshold' => 'nullable|numeric|min:0|max:100',
            'is_active' => 'boolean',
        ]);

        $budget->update([
            'expense_category_id' => $request->expense_category_id,
            'name' => $request->name,
            'description' => $request->description,
            'budget_type' => $request->budget_type,
            'period_type' => $request->period_type,
            'start_date' => $request->start_date,
            'end_date' => $request->end_date,
            'allocated_amount' => $request->allocated_amount,
            'alert_threshold' => $request->alert_threshold ?? 80,
            'is_active' => $request->boolean('is_active'),
        ]);

        // Update spent amount
        $budget->updateSpentAmount();

        return redirect()->route('budgets.index')
            ->with('success', 'Budget updated successfully.');
    }

    /**
     * Remove the specified budget.
     */
    public function destroy(Budget $budget)
    {
        $budget->delete();

        return redirect()->route('budgets.index')
            ->with('success', 'Budget deleted successfully.');
    }

    /**
     * Toggle budget status.
     */
    public function toggleStatus(Budget $budget)
    {
        $budget->update(['is_active' => !$budget->is_active]);

        $status = $budget->is_active ? 'activated' : 'deactivated';
        
        return response()->json([
            'success' => true,
            'message' => "Budget {$status} successfully.",
            'is_active' => $budget->is_active,
        ]);
    }

    /**
     * Create next period budget.
     */
    public function createNextPeriod(Request $request, Budget $budget)
    {
        $request->validate([
            'adjustment_percentage' => 'nullable|numeric|min:-100|max:1000',
        ]);

        $adjustmentPercentage = $request->adjustment_percentage ?? 0;
        $newBudget = $budget->createNextPeriodBudget($adjustmentPercentage);

        return response()->json([
            'success' => true,
            'message' => 'Next period budget created successfully.',
            'budget' => $newBudget,
        ]);
    }

    /**
     * Update all budget spent amounts.
     */
    public function updateSpentAmounts()
    {
        $budgets = Budget::active()->get();
        
        $budgets->each(function ($budget) {
            $budget->updateSpentAmount();
        });

        return response()->json([
            'success' => true,
            'message' => 'Budget spent amounts updated successfully.',
        ]);
    }

    /**
     * Get budget alerts.
     */
    public function alerts()
    {
        $alerts = \App\Models\Tenant\BudgetAlert::with(['budget'])
            ->where('is_read', false)
            ->latest()
            ->paginate(20);

        return Inertia::render('Tenant/Budgets/Alerts', [
            'alerts' => $alerts,
        ]);
    }

    /**
     * Mark alert as read.
     */
    public function markAlertRead(\App\Models\Tenant\BudgetAlert $alert)
    {
        $alert->update([
            'is_read' => true,
            'read_at' => now(),
        ]);

        return response()->json([
            'success' => true,
            'message' => 'Alert marked as read.',
        ]);
    }

    /**
     * Get budget type options.
     */
    protected function getBudgetTypeOptions(): array
    {
        return [
            ['value' => 'operational', 'label' => 'Operational'],
            ['value' => 'capital', 'label' => 'Capital'],
            ['value' => 'project', 'label' => 'Project'],
            ['value' => 'department', 'label' => 'Department'],
        ];
    }

    /**
     * Get period type options.
     */
    protected function getPeriodTypeOptions(): array
    {
        return [
            ['value' => 'monthly', 'label' => 'Monthly'],
            ['value' => 'quarterly', 'label' => 'Quarterly'],
            ['value' => 'yearly', 'label' => 'Yearly'],
            ['value' => 'custom', 'label' => 'Custom'],
        ];
    }
}
