<?php

namespace App\Http\Controllers\Tenant;

use App\Http\Controllers\Controller;
use App\Models\Tenant\Reservation;
use App\Models\Tenant\Table;
use App\Models\Tenant\Customer;
use App\Models\Tenant\Restaurant;
use Illuminate\Http\Request;
use Inertia\Inertia;
use Carbon\Carbon;

class ReservationController extends Controller
{
    /**
     * Display a listing of reservations.
     */
    public function index(Request $request)
    {
        $query = Reservation::with(['customer', 'table', 'creator']);

        // Search
        if ($request->search) {
            $query->where(function ($q) use ($request) {
                $q->where('reservation_number', 'like', '%' . $request->search . '%')
                  ->orWhere('customer_name', 'like', '%' . $request->search . '%')
                  ->orWhere('customer_phone', 'like', '%' . $request->search . '%');
            });
        }

        // Filter by status
        if ($request->status) {
            $query->where('status', $request->status);
        }

        // Filter by date range
        if ($request->date_from) {
            $query->whereDate('reservation_date', '>=', $request->date_from);
        }
        if ($request->date_to) {
            $query->whereDate('reservation_date', '<=', $request->date_to);
        }

        // Filter by table
        if ($request->table_id) {
            $query->where('table_id', $request->table_id);
        }

        // Filter by party size
        if ($request->party_size) {
            $query->where('party_size', $request->party_size);
        }

        $reservations = $query->latest('reservation_date')->latest('reservation_time')->paginate(20);
        $tables = Table::active()->ordered()->get();

        return Inertia::render('Tenant/Reservations/Index', [
            'reservations' => $reservations,
            'tables' => $tables,
            'filters' => $request->only(['search', 'status', 'date_from', 'date_to', 'table_id', 'party_size']),
            'statusOptions' => $this->getStatusOptions(),
        ]);
    }

    /**
     * Show the calendar view of reservations.
     */
    public function calendar(Request $request)
    {
        $date = $request->date ? Carbon::parse($request->date) : today();
        $startDate = $date->copy()->startOfMonth();
        $endDate = $date->copy()->endOfMonth();

        $reservations = Reservation::with(['customer', 'table'])
            ->whereBetween('reservation_date', [$startDate, $endDate])
            ->get()
            ->map(function ($reservation) {
                return [
                    'id' => $reservation->id,
                    'title' => $reservation->customer_name . ' (' . $reservation->party_size . ' guests)',
                    'start' => $reservation->reservation_datetime->toISOString(),
                    'end' => $reservation->end_time->toISOString(),
                    'backgroundColor' => $this->getStatusColor($reservation->status),
                    'borderColor' => $this->getStatusColor($reservation->status),
                    'extendedProps' => [
                        'reservation' => $reservation,
                    ],
                ];
            });

        return Inertia::render('Tenant/Reservations/Calendar', [
            'reservations' => $reservations,
            'currentDate' => $date->toDateString(),
        ]);
    }

    /**
     * Show the form for creating a new reservation.
     */
    public function create()
    {
        $restaurant = Restaurant::first();
        $tables = Table::active()->ordered()->get();
        $customers = Customer::active()->latest()->take(50)->get();

        return Inertia::render('Tenant/Reservations/Create', [
            'restaurant' => $restaurant,
            'tables' => $tables,
            'customers' => $customers,
        ]);
    }

    /**
     * Store a newly created reservation.
     */
    public function store(Request $request)
    {
        $request->validate([
            'customer_id' => 'nullable|exists:customers,id',
            'customer_name' => 'required|string|max:255',
            'customer_phone' => 'required|string|max:20',
            'customer_email' => 'nullable|email',
            'party_size' => 'required|integer|min:1|max:20',
            'reservation_date' => 'required|date|after_or_equal:today',
            'reservation_time' => 'required|date_format:H:i',
            'duration_minutes' => 'nullable|integer|min:30|max:480',
            'table_id' => 'nullable|exists:tables,id',
            'special_requests' => 'nullable|string',
            'notes' => 'nullable|string',
        ]);

        $restaurant = Restaurant::first();
        $reservationDateTime = Carbon::parse($request->reservation_date . ' ' . $request->reservation_time);

        // Check if restaurant is open
        if (!$this->isRestaurantOpen($restaurant, $reservationDateTime)) {
            return back()->withErrors(['reservation_time' => 'Restaurant is closed at this time.']);
        }

        // Check table availability if specific table is requested
        if ($request->table_id) {
            $table = Table::find($request->table_id);
            if (!$this->isTableAvailable($table, $reservationDateTime, $request->duration_minutes ?? 120)) {
                return back()->withErrors(['table_id' => 'Table is not available at this time.']);
            }
        } else {
            // Find available table for party size
            $table = $this->findAvailableTable($request->party_size, $reservationDateTime, $request->duration_minutes ?? 120);
            if (!$table) {
                return back()->withErrors(['party_size' => 'No tables available for this party size at the requested time.']);
            }
        }

        // Create or update customer
        $customer = null;
        if ($request->customer_id) {
            $customer = Customer::find($request->customer_id);
        } else {
            $customer = Customer::firstOrCreate(
                ['phone' => $request->customer_phone],
                [
                    'restaurant_id' => $restaurant->id,
                    'first_name' => explode(' ', $request->customer_name)[0],
                    'last_name' => implode(' ', array_slice(explode(' ', $request->customer_name), 1)),
                    'email' => $request->customer_email,
                ]
            );
        }

        // Create reservation
        $reservation = Reservation::create([
            'restaurant_id' => $restaurant->id,
            'table_id' => $table->id,
            'customer_id' => $customer->id,
            'customer_name' => $request->customer_name,
            'customer_phone' => $request->customer_phone,
            'customer_email' => $request->customer_email,
            'party_size' => $request->party_size,
            'reservation_date' => $request->reservation_date,
            'reservation_time' => $request->reservation_time,
            'duration_minutes' => $request->duration_minutes ?? 120,
            'special_requests' => $request->special_requests,
            'notes' => $request->notes,
            'created_by' => auth()->id(),
        ]);

        // Update table status
        $table->update(['status' => 'reserved']);

        return redirect()->route('reservations.show', $reservation)
            ->with('success', 'Reservation created successfully.');
    }

    /**
     * Display the specified reservation.
     */
    public function show(Reservation $reservation)
    {
        $reservation->load(['customer', 'table', 'creator', 'restaurant']);

        return Inertia::render('Tenant/Reservations/Show', [
            'reservation' => $reservation,
        ]);
    }

    /**
     * Show the form for editing the specified reservation.
     */
    public function edit(Reservation $reservation)
    {
        $reservation->load(['customer', 'table']);
        $restaurant = Restaurant::first();
        $tables = Table::active()->ordered()->get();
        $customers = Customer::active()->latest()->take(50)->get();

        return Inertia::render('Tenant/Reservations/Edit', [
            'reservation' => $reservation,
            'restaurant' => $restaurant,
            'tables' => $tables,
            'customers' => $customers,
        ]);
    }

    /**
     * Update the specified reservation.
     */
    public function update(Request $request, Reservation $reservation)
    {
        $request->validate([
            'customer_name' => 'required|string|max:255',
            'customer_phone' => 'required|string|max:20',
            'customer_email' => 'nullable|email',
            'party_size' => 'required|integer|min:1|max:20',
            'reservation_date' => 'required|date',
            'reservation_time' => 'required|date_format:H:i',
            'duration_minutes' => 'nullable|integer|min:30|max:480',
            'table_id' => 'nullable|exists:tables,id',
            'special_requests' => 'nullable|string',
            'notes' => 'nullable|string',
            'status' => 'required|in:pending,confirmed,checked_in,completed,cancelled,no_show',
        ]);

        $oldTableId = $reservation->table_id;
        $newTableId = $request->table_id;

        $reservation->update([
            'customer_name' => $request->customer_name,
            'customer_phone' => $request->customer_phone,
            'customer_email' => $request->customer_email,
            'party_size' => $request->party_size,
            'reservation_date' => $request->reservation_date,
            'reservation_time' => $request->reservation_time,
            'duration_minutes' => $request->duration_minutes ?? 120,
            'table_id' => $request->table_id,
            'special_requests' => $request->special_requests,
            'notes' => $request->notes,
            'status' => $request->status,
        ]);

        // Update table statuses if table changed
        if ($oldTableId !== $newTableId) {
            if ($oldTableId) {
                Table::find($oldTableId)->update(['status' => 'available']);
            }
            if ($newTableId && in_array($request->status, ['pending', 'confirmed'])) {
                Table::find($newTableId)->update(['status' => 'reserved']);
            }
        }

        return redirect()->route('reservations.index')
            ->with('success', 'Reservation updated successfully.');
    }

    /**
     * Remove the specified reservation.
     */
    public function destroy(Reservation $reservation)
    {
        // Free up the table
        if ($reservation->table) {
            $reservation->table->update(['status' => 'available']);
        }

        $reservation->delete();

        return redirect()->route('reservations.index')
            ->with('success', 'Reservation deleted successfully.');
    }

    /**
     * Confirm reservation.
     */
    public function confirm(Reservation $reservation)
    {
        $reservation->confirm();

        return response()->json([
            'success' => true,
            'message' => 'Reservation confirmed successfully.',
        ]);
    }

    /**
     * Check in customer.
     */
    public function checkIn(Reservation $reservation)
    {
        $reservation->checkIn();

        return response()->json([
            'success' => true,
            'message' => 'Customer checked in successfully.',
        ]);
    }

    /**
     * Complete reservation.
     */
    public function complete(Reservation $reservation)
    {
        $reservation->complete();

        return response()->json([
            'success' => true,
            'message' => 'Reservation completed successfully.',
        ]);
    }

    /**
     * Cancel reservation.
     */
    public function cancel(Request $request, Reservation $reservation)
    {
        $request->validate([
            'cancellation_reason' => 'nullable|string',
        ]);

        $reservation->cancel($request->cancellation_reason);

        return response()->json([
            'success' => true,
            'message' => 'Reservation cancelled successfully.',
        ]);
    }

    /**
     * Mark as no-show.
     */
    public function noShow(Reservation $reservation)
    {
        $reservation->markNoShow();

        return response()->json([
            'success' => true,
            'message' => 'Reservation marked as no-show.',
        ]);
    }

    /**
     * Get available time slots for a date.
     */
    public function availableSlots(Request $request)
    {
        $request->validate([
            'date' => 'required|date',
            'party_size' => 'required|integer|min:1',
            'duration' => 'nullable|integer|min:30|max:480',
        ]);

        $date = Carbon::parse($request->date);
        $partySize = $request->party_size;
        $duration = $request->duration ?? 120;

        $restaurant = Restaurant::first();
        $slots = [];

        // Generate time slots from opening to closing time
        $openingTime = Carbon::parse($date->format('Y-m-d') . ' ' . $restaurant->opening_time);
        $closingTime = Carbon::parse($date->format('Y-m-d') . ' ' . $restaurant->closing_time);

        $currentSlot = $openingTime->copy();
        while ($currentSlot->addMinutes(30)->lte($closingTime->subMinutes($duration))) {
            $availableTable = $this->findAvailableTable($partySize, $currentSlot, $duration);
            
            if ($availableTable) {
                $slots[] = [
                    'time' => $currentSlot->format('H:i'),
                    'available' => true,
                    'table_id' => $availableTable->id,
                    'table_name' => $availableTable->name,
                ];
            } else {
                $slots[] = [
                    'time' => $currentSlot->format('H:i'),
                    'available' => false,
                ];
            }
        }

        return response()->json($slots);
    }

    /**
     * Check if restaurant is open at given time.
     */
    protected function isRestaurantOpen(Restaurant $restaurant, Carbon $dateTime): bool
    {
        $openingTime = Carbon::parse($dateTime->format('Y-m-d') . ' ' . $restaurant->opening_time);
        $closingTime = Carbon::parse($dateTime->format('Y-m-d') . ' ' . $restaurant->closing_time);

        return $dateTime->between($openingTime, $closingTime);
    }

    /**
     * Check if table is available at given time.
     */
    protected function isTableAvailable(Table $table, Carbon $dateTime, int $duration): bool
    {
        $endTime = $dateTime->copy()->addMinutes($duration);

        $conflictingReservations = Reservation::where('table_id', $table->id)
            ->where('reservation_date', $dateTime->toDateString())
            ->active()
            ->where(function ($query) use ($dateTime, $endTime) {
                $query->where(function ($q) use ($dateTime, $endTime) {
                    // Reservation starts during our time slot
                    $q->whereBetween('reservation_time', [$dateTime->format('H:i:s'), $endTime->format('H:i:s')]);
                })->orWhere(function ($q) use ($dateTime, $endTime) {
                    // Our reservation starts during existing reservation
                    $q->where('reservation_time', '<=', $dateTime->format('H:i:s'))
                      ->whereRaw('TIME_ADD(reservation_time, INTERVAL duration_minutes MINUTE) > ?', [$dateTime->format('H:i:s')]);
                });
            })
            ->exists();

        return !$conflictingReservations;
    }

    /**
     * Find available table for party size and time.
     */
    protected function findAvailableTable(int $partySize, Carbon $dateTime, int $duration): ?Table
    {
        $tables = Table::active()
            ->where('capacity', '>=', $partySize)
            ->where('min_capacity', '<=', $partySize)
            ->orderBy('capacity')
            ->get();

        foreach ($tables as $table) {
            if ($this->isTableAvailable($table, $dateTime, $duration)) {
                return $table;
            }
        }

        return null;
    }

    /**
     * Get status options.
     */
    protected function getStatusOptions(): array
    {
        return [
            ['value' => 'pending', 'label' => 'Pending'],
            ['value' => 'confirmed', 'label' => 'Confirmed'],
            ['value' => 'checked_in', 'label' => 'Checked In'],
            ['value' => 'completed', 'label' => 'Completed'],
            ['value' => 'cancelled', 'label' => 'Cancelled'],
            ['value' => 'no_show', 'label' => 'No Show'],
        ];
    }

    /**
     * Get status color for calendar.
     */
    protected function getStatusColor(string $status): string
    {
        return match($status) {
            'pending' => '#fbbf24',
            'confirmed' => '#3b82f6',
            'checked_in' => '#10b981',
            'completed' => '#6b7280',
            'cancelled' => '#ef4444',
            'no_show' => '#f87171',
            default => '#6b7280',
        };
    }
}
