<?php

namespace App\Http\Controllers\Tenant;

use App\Http\Controllers\Controller;
use App\Models\Tenant\Branch;
use App\Models\Tenant\Floor;
use App\Models\Tenant\Table;
use App\Models\Tenant\User;
use App\Models\Tenant\Restaurant;
use Illuminate\Http\Request;
use Inertia\Inertia;
use SimpleSoftwareIO\QrCode\Facades\QrCode;
use Illuminate\Support\Str;

class TableController extends Controller
{
    /**
     * Display a listing of tables.
     */
    public function index(Request $request)
    {
        $query = Table::with(['branch', 'floor', 'assignedWaiter', 'orders' => function ($q) {
            $q->latest()->take(1);
        }]);

        // Search
        if ($request->search) {
            $query->where(function ($q) use ($request) {
                $q->where('name', 'like', '%' . $request->search . '%')
                  ->orWhere('description', 'like', '%' . $request->search . '%');
            });
        }

        // Filter by branch
        if ($request->branch_id) {
            $query->where('branch_id', $request->branch_id);
        }

        // Filter by floor
        if ($request->floor_id) {
            $query->where('floor_id', $request->floor_id);
        }

        // Filter by status (is_active)
        if ($request->status !== null) {
            $query->where('is_active', $request->status);
        }

        $tables = $query->orderBy('sort_order')->orderBy('name')->paginate(20)->withQueryString();

        // Get branches and floors for filters
        $branches = Branch::active()->ordered()->get(['id', 'name']);
        $floors = Floor::with('branch:id,name')->active()->ordered()->get(['id', 'name', 'branch_id']);

        return Inertia::render('Tenant/Tables/Index', [
            'tables' => $tables,
            'branches' => $branches,
            'floors' => $floors,
            'filters' => $request->only(['search', 'branch_id', 'floor_id', 'status']),
        ]);
    }

    /**
     * Show the table layout designer.
     */
    public function layout()
    {
        $tables = Table::active()->get();
        $restaurant = Restaurant::first();

        return Inertia::render('Tenant/Tables/Layout', [
            'tables' => $tables,
            'restaurant' => $restaurant,
        ]);
    }

    /**
     * Show the form for creating a new table.
     */
    public function create(Request $request)
    {
        $branches = Branch::active()->ordered()->get(['id', 'name']);
        $floors = Floor::with('branch:id,name')->active()->ordered()->get(['id', 'name', 'branch_id']);

        // Get active waiters for assignment
        $waiters = User::where('role', 'waiter')
            ->where('is_active', true)
            ->orderBy('name')
            ->get(['id', 'name', 'email']);

        return Inertia::render('Tenant/Tables/Create', [
            'branches' => $branches,
            'floors' => $floors,
            'waiters' => $waiters,
            'preselected_floor_id' => $request->floor_id,
        ]);
    }

    /**
     * Store a newly created table.
     */
    public function store(Request $request)
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'table_number' => 'nullable|string|max:50',
            'branch_id' => 'required|exists:branches,id',
            'floor_id' => 'required|exists:floors,id',
            'capacity' => 'nullable|integer|min:1|max:20',
            'description' => 'nullable|string|max:1000',
            'sort_order' => 'nullable|integer|min:0',
            'assigned_waiter_id' => 'nullable|exists:users,id',
            'is_active' => 'boolean',
        ]);

        // Verify floor belongs to branch
        $floor = Floor::where('id', $request->floor_id)
            ->where('branch_id', $request->branch_id)
            ->firstOrFail();

        $table = Table::create([
            'name' => $request->name,
            'table_number' => $request->table_number,
            'branch_id' => $request->branch_id,
            'floor_id' => $request->floor_id,
            'capacity' => $request->capacity ?? 4,
            'description' => $request->description,
            'assigned_waiter_id' => $request->assigned_waiter_id,
            'is_active' => $request->boolean('is_active', true),
            'sort_order' => $request->sort_order ?? 0,
        ]);

        return redirect()->route('manager.tables.index')
            ->with('success', 'Table created successfully.');
    }

    /**
     * Display the specified table.
     */
    public function show(Table $table)
    {
        $table->load([
            'branch',
            'floor',
            'assignedWaiter',
            'orders' => function ($query) {
                $query->latest()->take(10);
            },
            'reservations' => function ($query) {
                $query->latest()->take(10);
            }
        ]);

        return Inertia::render('Tenant/Tables/Show', [
            'table' => $table,
        ]);
    }

    /**
     * Show the form for editing the specified table.
     */
    public function edit(Table $table)
    {
        $branches = Branch::active()->ordered()->get(['id', 'name']);
        $floors = Floor::with('branch:id,name')->active()->ordered()->get(['id', 'name', 'branch_id']);

        // Get active waiters for assignment
        $waiters = User::where('role', 'waiter')
            ->where('is_active', true)
            ->orderBy('name')
            ->get(['id', 'name', 'email']);

        return Inertia::render('Tenant/Tables/Edit', [
            'table' => $table,
            'branches' => $branches,
            'floors' => $floors,
            'waiters' => $waiters,
        ]);
    }

    /**
     * Update the specified table.
     */
    public function update(Request $request, Table $table)
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'table_number' => 'nullable|string|max:50',
            'branch_id' => 'required|exists:branches,id',
            'floor_id' => 'required|exists:floors,id',
            'capacity' => 'nullable|integer|min:1|max:20',
            'description' => 'nullable|string|max:1000',
            'sort_order' => 'nullable|integer|min:0',
            'assigned_waiter_id' => 'nullable|exists:users,id',
            'is_active' => 'boolean',
        ]);

        // Verify floor belongs to branch
        $floor = Floor::where('id', $request->floor_id)
            ->where('branch_id', $request->branch_id)
            ->firstOrFail();

        $table->update([
            'name' => $request->name,
            'table_number' => $request->table_number,
            'branch_id' => $request->branch_id,
            'floor_id' => $request->floor_id,
            'capacity' => $request->capacity ?? 4,
            'description' => $request->description,
            'assigned_waiter_id' => $request->assigned_waiter_id,
            'is_active' => $request->boolean('is_active'),
            'sort_order' => $request->sort_order ?? 0,
        ]);

        return redirect()->route('manager.tables.index')
            ->with('success', 'Table updated successfully.');
    }

    /**
     * Remove the specified table.
     */
    public function destroy(Table $table)
    {
        // Check if table has active orders or reservations
        if ($table->orders()->whereIn('status', ['pending', 'confirmed', 'preparing', 'ready'])->exists()) {
            return back()->withErrors(['error' => 'Cannot delete table with active orders.']);
        }

        if ($table->activeReservations()->exists()) {
            return back()->withErrors(['error' => 'Cannot delete table with active reservations.']);
        }

        $table->delete();

        return redirect()->route('manager.tables.index')
            ->with('success', 'Table deleted successfully.');
    }

    /**
     * Toggle table status.
     */
    public function toggleStatus(Table $table)
    {
        $table->update(['is_active' => !$table->is_active]);

        $status = $table->is_active ? 'activated' : 'deactivated';
        
        return response()->json([
            'success' => true,
            'message' => "Table {$status} successfully.",
            'is_active' => $table->is_active,
        ]);
    }

    /**
     * Generate QR code for table.
     */
    public function generateQrCode(Table $table)
    {
        if (!$table->qr_code) {
            $table->generateQrCode();
        }

        $qrCodeUrl = route('guest.table.qr', $table);
        $qrCode = QrCode::size(300)->generate($qrCodeUrl);

        return response($qrCode)->header('Content-Type', 'image/svg+xml');
    }

    /**
     * Update table layout positions.
     */
    public function updateLayout(Request $request)
    {
        $request->validate([
            'tables' => 'required|array',
            'tables.*.id' => 'required|exists:tables,id',
            'tables.*.position_x' => 'required|numeric',
            'tables.*.position_y' => 'required|numeric',
            'tables.*.rotation' => 'nullable|integer|min:0|max:359',
        ]);

        foreach ($request->tables as $tableData) {
            Table::where('id', $tableData['id'])->update([
                'position_x' => $tableData['position_x'],
                'position_y' => $tableData['position_y'],
                'rotation' => $tableData['rotation'] ?? 0,
            ]);
        }

        return response()->json([
            'success' => true,
            'message' => 'Table layout updated successfully.',
        ]);
    }

    /**
     * Bulk update tables.
     */
    public function bulkUpdate(Request $request)
    {
        $request->validate([
            'ids' => 'required|array',
            'ids.*' => 'exists:tables,id',
            'action' => 'required|in:activate,deactivate,set_available,set_maintenance,delete',
        ]);

        $tables = Table::whereIn('id', $request->ids);

        switch ($request->action) {
            case 'activate':
                $tables->update(['is_active' => true]);
                $message = 'Tables activated successfully.';
                break;
            case 'deactivate':
                $tables->update(['is_active' => false]);
                $message = 'Tables deactivated successfully.';
                break;
            case 'set_available':
                $tables->update(['status' => 'available']);
                $message = 'Tables marked as available.';
                break;
            case 'set_maintenance':
                $tables->update(['status' => 'maintenance']);
                $message = 'Tables marked for maintenance.';
                break;
            case 'delete':
                // Check if any table has active orders or reservations
                $hasActiveOrders = $tables->whereHas('orders', function ($query) {
                    $query->whereIn('status', ['pending', 'confirmed', 'preparing', 'ready']);
                })->exists();
                
                if ($hasActiveOrders) {
                    return back()->withErrors(['error' => 'Cannot delete tables with active orders.']);
                }
                
                $tables->delete();
                $message = 'Tables deleted successfully.';
                break;
        }

        return back()->with('success', $message);
    }

    /**
     * Get status options for filters.
     */
    protected function getStatusOptions(): array
    {
        return [
            ['value' => 'available', 'label' => 'Available'],
            ['value' => 'occupied', 'label' => 'Occupied'],
            ['value' => 'reserved', 'label' => 'Reserved'],
            ['value' => 'maintenance', 'label' => 'Maintenance'],
            ['value' => 'cleaning', 'label' => 'Cleaning'],
        ];
    }

    /**
     * Get feature options for filters.
     */
    protected function getFeatureOptions(): array
    {
        return [
            ['value' => 'has_power_outlet', 'label' => 'Power Outlet'],
            ['value' => 'has_window_view', 'label' => 'Window View'],
            ['value' => 'is_wheelchair_accessible', 'label' => 'Wheelchair Accessible'],
            ['value' => 'is_high_top', 'label' => 'High Top'],
            ['value' => 'is_booth', 'label' => 'Booth'],
            ['value' => 'is_outdoor', 'label' => 'Outdoor'],
        ];
    }

    /**
     * Get average table duration.
     */
    protected function getAverageTableDuration(Table $table): int
    {
        $completedOrders = $table->orders()
            ->whereIn('status', ['served', 'completed'])
            ->whereNotNull('served_at')
            ->whereDate('created_at', '>=', now()->subDays(30))
            ->get();

        if ($completedOrders->isEmpty()) {
            return 90; // Default 90 minutes
        }

        $totalDuration = $completedOrders->sum(function ($order) {
            return $order->created_at->diffInMinutes($order->served_at);
        });

        return (int) ($totalDuration / $completedOrders->count());
    }
}
