<?php

namespace App\Http\Controllers\Tenant;

use App\Http\Controllers\Controller;
use App\Models\Tenant\DeliveryZone;
use App\Models\Tenant\Restaurant;
use Illuminate\Http\Request;
use Inertia\Inertia;

class DeliveryZoneController extends Controller
{
    /**
     * Display a listing of delivery zones.
     */
    public function index(Request $request)
    {
        $query = DeliveryZone::with(['restaurant']);

        // Search
        if ($request->search) {
            $query->where(function ($q) use ($request) {
                $q->where('name', 'like', '%' . $request->search . '%')
                  ->orWhere('description', 'like', '%' . $request->search . '%');
            });
        }

        // Filter by status
        if ($request->status) {
            if ($request->status === 'active') {
                $query->where('is_active', true);
            } elseif ($request->status === 'inactive') {
                $query->where('is_active', false);
            }
        }

        $zones = $query->ordered()->paginate(20);

        return Inertia::render('Tenant/DeliveryZones/Index', [
            'zones' => $zones,
            'filters' => $request->only(['search', 'status']),
        ]);
    }

    /**
     * Show the zone map designer.
     */
    public function map()
    {
        $zones = DeliveryZone::active()->get();
        $restaurant = Restaurant::first();

        return Inertia::render('Tenant/DeliveryZones/Map', [
            'zones' => $zones,
            'restaurant' => $restaurant,
        ]);
    }

    /**
     * Show the form for creating a new delivery zone.
     */
    public function create()
    {
        $restaurant = Restaurant::first();

        return Inertia::render('Tenant/DeliveryZones/Create', [
            'restaurant' => $restaurant,
        ]);
    }

    /**
     * Store a newly created delivery zone.
     */
    public function store(Request $request)
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'description' => 'nullable|string',
            'polygon_coordinates' => 'required|array|min:3',
            'polygon_coordinates.*.lat' => 'required|numeric|between:-90,90',
            'polygon_coordinates.*.lng' => 'required|numeric|between:-180,180',
            'delivery_fee' => 'required|numeric|min:0',
            'minimum_order_amount' => 'required|numeric|min:0',
            'estimated_delivery_time' => 'required|integer|min:10|max:180',
            'priority_order' => 'nullable|integer|min:0',
            'color_code' => 'nullable|string|regex:/^#[0-9A-Fa-f]{6}$/',
            'is_active' => 'boolean',
        ]);

        $restaurant = Restaurant::first();

        $zone = DeliveryZone::create([
            'restaurant_id' => $restaurant->id,
            'name' => $request->name,
            'description' => $request->description,
            'polygon_coordinates' => $request->polygon_coordinates,
            'delivery_fee' => $request->delivery_fee,
            'minimum_order_amount' => $request->minimum_order_amount,
            'estimated_delivery_time' => $request->estimated_delivery_time,
            'priority_order' => $request->priority_order,
            'color_code' => $request->color_code,
            'is_active' => $request->boolean('is_active', true),
        ]);

        return redirect()->route('delivery-zones.index')
            ->with('success', 'Delivery zone created successfully.');
    }

    /**
     * Display the specified delivery zone.
     */
    public function show(DeliveryZone $deliveryZone)
    {
        $deliveryZone->load(['restaurant', 'orders' => function ($query) {
            $query->with('customer')->latest()->take(10);
        }]);

        // Get zone statistics
        $stats = [
            'today' => $deliveryZone->getDeliveryStats('today'),
            'week' => $deliveryZone->getDeliveryStats('week'),
            'month' => $deliveryZone->getDeliveryStats('month'),
        ];

        return Inertia::render('Tenant/DeliveryZones/Show', [
            'zone' => $deliveryZone,
            'stats' => $stats,
        ]);
    }

    /**
     * Show the form for editing the specified delivery zone.
     */
    public function edit(DeliveryZone $deliveryZone)
    {
        $restaurant = Restaurant::first();

        return Inertia::render('Tenant/DeliveryZones/Edit', [
            'zone' => $deliveryZone,
            'restaurant' => $restaurant,
        ]);
    }

    /**
     * Update the specified delivery zone.
     */
    public function update(Request $request, DeliveryZone $deliveryZone)
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'description' => 'nullable|string',
            'polygon_coordinates' => 'required|array|min:3',
            'polygon_coordinates.*.lat' => 'required|numeric|between:-90,90',
            'polygon_coordinates.*.lng' => 'required|numeric|between:-180,180',
            'delivery_fee' => 'required|numeric|min:0',
            'minimum_order_amount' => 'required|numeric|min:0',
            'estimated_delivery_time' => 'required|integer|min:10|max:180',
            'priority_order' => 'nullable|integer|min:0',
            'color_code' => 'nullable|string|regex:/^#[0-9A-Fa-f]{6}$/',
            'is_active' => 'boolean',
        ]);

        $deliveryZone->update([
            'name' => $request->name,
            'description' => $request->description,
            'polygon_coordinates' => $request->polygon_coordinates,
            'delivery_fee' => $request->delivery_fee,
            'minimum_order_amount' => $request->minimum_order_amount,
            'estimated_delivery_time' => $request->estimated_delivery_time,
            'priority_order' => $request->priority_order,
            'color_code' => $request->color_code,
            'is_active' => $request->boolean('is_active'),
        ]);

        return redirect()->route('delivery-zones.index')
            ->with('success', 'Delivery zone updated successfully.');
    }

    /**
     * Remove the specified delivery zone.
     */
    public function destroy(DeliveryZone $deliveryZone)
    {
        // Check if zone has active orders
        if ($deliveryZone->orders()->whereIn('status', ['pending', 'confirmed', 'preparing', 'ready', 'out_for_delivery'])->exists()) {
            return back()->withErrors(['error' => 'Cannot delete delivery zone with active orders.']);
        }

        $deliveryZone->delete();

        return redirect()->route('delivery-zones.index')
            ->with('success', 'Delivery zone deleted successfully.');
    }

    /**
     * Toggle zone status.
     */
    public function toggleStatus(DeliveryZone $deliveryZone)
    {
        $deliveryZone->update(['is_active' => !$deliveryZone->is_active]);

        $status = $deliveryZone->is_active ? 'activated' : 'deactivated';
        
        return response()->json([
            'success' => true,
            'message' => "Delivery zone {$status} successfully.",
            'is_active' => $deliveryZone->is_active,
        ]);
    }

    /**
     * Reorder delivery zones.
     */
    public function reorder(Request $request)
    {
        $request->validate([
            'zones' => 'required|array',
            'zones.*.id' => 'required|exists:delivery_zones,id',
            'zones.*.priority_order' => 'required|integer|min:0',
        ]);

        foreach ($request->zones as $zoneData) {
            DeliveryZone::where('id', $zoneData['id'])
                ->update(['priority_order' => $zoneData['priority_order']]);
        }

        return response()->json([
            'success' => true,
            'message' => 'Delivery zones reordered successfully.',
        ]);
    }

    /**
     * Test if coordinates are within zone.
     */
    public function testCoordinates(Request $request, DeliveryZone $deliveryZone)
    {
        $request->validate([
            'latitude' => 'required|numeric|between:-90,90',
            'longitude' => 'required|numeric|between:-180,180',
        ]);

        $isWithin = $deliveryZone->containsPoint($request->latitude, $request->longitude);

        return response()->json([
            'success' => true,
            'is_within_zone' => $isWithin,
            'zone_name' => $deliveryZone->name,
            'delivery_fee' => $isWithin ? $deliveryZone->delivery_fee : null,
            'estimated_time' => $isWithin ? $deliveryZone->estimated_delivery_time : null,
        ]);
    }

    /**
     * Get zone coverage statistics.
     */
    public function coverage()
    {
        $zones = DeliveryZone::active()->get();
        
        $coverage = $zones->map(function ($zone) {
            $stats = $zone->getDeliveryStats('month');
            
            return [
                'id' => $zone->id,
                'name' => $zone->name,
                'area' => $zone->area,
                'center_point' => $zone->center_point,
                'total_orders' => $stats['total_orders'],
                'total_revenue' => $stats['total_revenue'],
                'average_delivery_time' => $stats['average_delivery_time'],
                'on_time_percentage' => $stats['on_time_percentage'],
                'delivery_fee' => $zone->delivery_fee,
                'minimum_order' => $zone->minimum_order_amount,
            ];
        });

        return Inertia::render('Tenant/DeliveryZones/Coverage', [
            'zones' => $coverage,
        ]);
    }

    /**
     * Bulk update delivery zones.
     */
    public function bulkUpdate(Request $request)
    {
        $request->validate([
            'ids' => 'required|array',
            'ids.*' => 'exists:delivery_zones,id',
            'action' => 'required|in:activate,deactivate,delete',
            'delivery_fee' => 'nullable|numeric|min:0',
            'estimated_delivery_time' => 'nullable|integer|min:10|max:180',
        ]);

        $zones = DeliveryZone::whereIn('id', $request->ids);

        switch ($request->action) {
            case 'activate':
                $zones->update(['is_active' => true]);
                $message = 'Delivery zones activated successfully.';
                break;
            case 'deactivate':
                $zones->update(['is_active' => false]);
                $message = 'Delivery zones deactivated successfully.';
                break;
            case 'delete':
                // Check if any zone has active orders
                $hasActiveOrders = $zones->whereHas('orders', function ($query) {
                    $query->whereIn('status', ['pending', 'confirmed', 'preparing', 'ready', 'out_for_delivery']);
                })->exists();
                
                if ($hasActiveOrders) {
                    return back()->withErrors(['error' => 'Cannot delete delivery zones with active orders.']);
                }
                
                $zones->delete();
                $message = 'Delivery zones deleted successfully.';
                break;
        }

        // Update delivery fee if provided
        if ($request->delivery_fee !== null && $request->action !== 'delete') {
            DeliveryZone::whereIn('id', $request->ids)->update(['delivery_fee' => $request->delivery_fee]);
        }

        // Update estimated delivery time if provided
        if ($request->estimated_delivery_time !== null && $request->action !== 'delete') {
            DeliveryZone::whereIn('id', $request->ids)->update(['estimated_delivery_time' => $request->estimated_delivery_time]);
        }

        return back()->with('success', $message);
    }

    /**
     * Export zones as GeoJSON.
     */
    public function exportGeoJson()
    {
        $zones = DeliveryZone::active()->get();
        
        $features = $zones->map(function ($zone) {
            return [
                'type' => 'Feature',
                'properties' => [
                    'id' => $zone->id,
                    'name' => $zone->name,
                    'description' => $zone->description,
                    'delivery_fee' => $zone->delivery_fee,
                    'minimum_order_amount' => $zone->minimum_order_amount,
                    'estimated_delivery_time' => $zone->estimated_delivery_time,
                    'color_code' => $zone->color_code,
                ],
                'geometry' => [
                    'type' => 'Polygon',
                    'coordinates' => [
                        array_map(function ($point) {
                            return [$point['lng'], $point['lat']];
                        }, $zone->polygon_coordinates)
                    ],
                ],
            ];
        });

        $geoJson = [
            'type' => 'FeatureCollection',
            'features' => $features,
        ];

        return response()->json($geoJson)
            ->header('Content-Disposition', 'attachment; filename="delivery-zones.geojson"');
    }
}
