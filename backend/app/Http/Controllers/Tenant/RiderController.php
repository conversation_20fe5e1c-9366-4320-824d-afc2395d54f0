<?php

namespace App\Http\Controllers\Tenant;

use App\Http\Controllers\Controller;
use App\Models\Tenant\Rider;
use App\Models\User;
use App\Services\Tenant\MediaService;
use Illuminate\Http\Request;
use Inertia\Inertia;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\DB;
use Spatie\Permission\Models\Role;

class RiderController extends Controller
{
    protected MediaService $mediaService;

    public function __construct(MediaService $mediaService)
    {
        $this->mediaService = $mediaService;
        $this->middleware(['role:Manager']);
    }

    /**
     * Display a listing of riders
     */
    public function index(Request $request)
    {
        $query = Rider::query();

        // Search functionality
        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('phone', 'like', "%{$search}%")
                  ->orWhere('email', 'like', "%{$search}%")
                  ->orWhere('employee_id', 'like', "%{$search}%");
            });
        }

        // Filter by status
        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }

        // Filter by delivery status
        if ($request->filled('delivery_status')) {
            $query->where('delivery_status', $request->delivery_status);
        }

        // Filter by vehicle type
        if ($request->filled('vehicle_type')) {
            $query->where('vehicle_type', $request->vehicle_type);
        }

        // Sort
        $sortField = $request->get('sort', 'created_at');
        $sortDirection = $request->get('direction', 'desc');
        $query->orderBy($sortField, $sortDirection);

        $riders = $query->withCount(['activeOrders', 'todayOrders'])
                       ->paginate(20)
                       ->withQueryString();

        return Inertia::render('Tenant/Riders/Index', [
            'riders' => $riders,
            'filters' => $request->only(['search', 'status', 'delivery_status', 'vehicle_type', 'sort', 'direction']),
            'vehicleTypes' => ['bike', 'car', 'scooter', 'bicycle'],
            'statuses' => ['active', 'inactive', 'suspended'],
            'deliveryStatuses' => ['available', 'busy', 'offline', 'on_break'],
        ]);
    }

    /**
     * Show the form for creating a new rider
     */
    public function create()
    {
        return Inertia::render('Tenant/Riders/Create', [
            'vehicleTypes' => ['bike', 'car', 'scooter', 'bicycle'],
            'workingDays' => [
                ['value' => 0, 'label' => 'Sunday'],
                ['value' => 1, 'label' => 'Monday'],
                ['value' => 2, 'label' => 'Tuesday'],
                ['value' => 3, 'label' => 'Wednesday'],
                ['value' => 4, 'label' => 'Thursday'],
                ['value' => 5, 'label' => 'Friday'],
                ['value' => 6, 'label' => 'Saturday'],
            ],
        ]);
    }

    /**
     * Store a newly created rider
     */
    public function store(Request $request)
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'phone' => 'required|string|max:20|unique:riders,phone',
            'email' => 'nullable|email|unique:riders,email',
            'vehicle_type' => 'required|in:bike,car,scooter,bicycle',
            'license_number' => 'nullable|string|max:255',
            'hire_date' => 'required|date',
            'address' => 'nullable|string|max:500',
            'profile_photo' => 'nullable|string', // Media ID
            'max_concurrent_deliveries' => 'nullable|integer|min:1|max:10',
            'commission_rate' => 'nullable|numeric|min:0|max:100',
            'shift_start_time' => 'nullable|date_format:H:i',
            'shift_end_time' => 'nullable|date_format:H:i',
            'working_days' => 'nullable|array',
            'working_days.*' => 'integer|min:0|max:6',
            'emergency_contact_name' => 'nullable|string|max:255',
            'emergency_contact_phone' => 'nullable|string|max:20',
            'notes' => 'nullable|string|max:1000',
            'create_user_account' => 'boolean',
            'password' => 'required_if:create_user_account,true|nullable|string|min:8',
        ]);

        try {
            return DB::transaction(function () use ($request) {
                $userId = null;

                // Create user account if requested
                if ($request->create_user_account && $request->email) {
                    $user = User::create([
                        'name' => $request->name,
                        'email' => $request->email,
                        'password' => Hash::make($request->password),
                        'email_verified_at' => now(),
                    ]);

                    // Assign Rider role
                    $riderRole = Role::firstOrCreate(['name' => 'Rider']);
                    $user->assignRole($riderRole);

                    $userId = $user->id;
                }

                // Handle profile photo
                $profilePhoto = null;
                if ($request->profile_photo) {
                    $media = $this->mediaService->getMediaById($request->profile_photo);
                    if ($media) {
                        $profilePhoto = $media->file_path;
                    }
                }

                // Create rider
                $rider = Rider::create([
                    'user_id' => $userId,
                    'employee_id' => Rider::generateEmployeeId(),
                    'name' => $request->name,
                    'phone' => $request->phone,
                    'email' => $request->email,
                    'vehicle_type' => $request->vehicle_type,
                    'license_number' => $request->license_number,
                    'hire_date' => $request->hire_date,
                    'address' => $request->address,
                    'profile_photo' => $profilePhoto,
                    'max_concurrent_deliveries' => $request->max_concurrent_deliveries ?? 3,
                    'commission_rate' => $request->commission_rate ?? 10.0,
                    'shift_start_time' => $request->shift_start_time,
                    'shift_end_time' => $request->shift_end_time,
                    'working_days' => $request->working_days,
                    'emergency_contact_name' => $request->emergency_contact_name,
                    'emergency_contact_phone' => $request->emergency_contact_phone,
                    'notes' => $request->notes,
                    'status' => 'active',
                    'delivery_status' => 'offline',
                ]);

                return redirect()->route('riders.index')
                    ->with('success', 'Rider created successfully.');
            });
        } catch (\Exception $e) {
            return back()->withErrors(['error' => 'Failed to create rider: ' . $e->getMessage()]);
        }
    }

    /**
     * Display the specified rider
     */
    public function show(Rider $rider)
    {
        $rider->load(['user', 'activeOrders.customer', 'orderHistory' => function ($query) {
            $query->with('order')->orderBy('action_time', 'desc')->limit(20);
        }]);

        // Get performance statistics
        $stats = [
            'today_deliveries' => $rider->todayOrders()->count(),
            'today_earnings' => $rider->getEarningsForPeriod('today'),
            'week_deliveries' => $rider->orders()->where('delivery_status', 'delivered')
                ->whereBetween('delivery_time', [now()->startOfWeek(), now()->endOfWeek()])
                ->count(),
            'week_earnings' => $rider->getEarningsForPeriod('week'),
            'month_deliveries' => $rider->orders()->where('delivery_status', 'delivered')
                ->whereMonth('delivery_time', now()->month)
                ->whereYear('delivery_time', now()->year)
                ->count(),
            'month_earnings' => $rider->getEarningsForPeriod('month'),
        ];

        return Inertia::render('Tenant/Riders/Show', [
            'rider' => $rider,
            'stats' => $stats,
        ]);
    }

    /**
     * Show the form for editing the specified rider
     */
    public function edit(Rider $rider)
    {
        $rider->load('user');

        return Inertia::render('Tenant/Riders/Edit', [
            'rider' => $rider,
            'vehicleTypes' => ['bike', 'car', 'scooter', 'bicycle'],
            'workingDays' => [
                ['value' => 0, 'label' => 'Sunday'],
                ['value' => 1, 'label' => 'Monday'],
                ['value' => 2, 'label' => 'Tuesday'],
                ['value' => 3, 'label' => 'Wednesday'],
                ['value' => 4, 'label' => 'Thursday'],
                ['value' => 5, 'label' => 'Friday'],
                ['value' => 6, 'label' => 'Saturday'],
            ],
        ]);
    }

    /**
     * Update the specified rider
     */
    public function update(Request $request, Rider $rider)
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'phone' => 'required|string|max:20|unique:riders,phone,' . $rider->id,
            'email' => 'nullable|email|unique:riders,email,' . $rider->id,
            'vehicle_type' => 'required|in:bike,car,scooter,bicycle',
            'license_number' => 'nullable|string|max:255',
            'status' => 'required|in:active,inactive,suspended',
            'hire_date' => 'required|date',
            'address' => 'nullable|string|max:500',
            'profile_photo' => 'nullable|string', // Media ID
            'max_concurrent_deliveries' => 'nullable|integer|min:1|max:10',
            'commission_rate' => 'nullable|numeric|min:0|max:100',
            'shift_start_time' => 'nullable|date_format:H:i',
            'shift_end_time' => 'nullable|date_format:H:i',
            'working_days' => 'nullable|array',
            'working_days.*' => 'integer|min:0|max:6',
            'emergency_contact_name' => 'nullable|string|max:255',
            'emergency_contact_phone' => 'nullable|string|max:20',
            'notes' => 'nullable|string|max:1000',
        ]);

        try {
            return DB::transaction(function () use ($request, $rider) {
                // Handle profile photo
                $profilePhoto = $rider->profile_photo;
                if ($request->profile_photo && $request->profile_photo !== $rider->profile_photo) {
                    $media = $this->mediaService->getMediaById($request->profile_photo);
                    if ($media) {
                        $profilePhoto = $media->file_path;
                    }
                }

                // Update rider
                $rider->update([
                    'name' => $request->name,
                    'phone' => $request->phone,
                    'email' => $request->email,
                    'vehicle_type' => $request->vehicle_type,
                    'license_number' => $request->license_number,
                    'status' => $request->status,
                    'hire_date' => $request->hire_date,
                    'address' => $request->address,
                    'profile_photo' => $profilePhoto,
                    'max_concurrent_deliveries' => $request->max_concurrent_deliveries ?? 3,
                    'commission_rate' => $request->commission_rate ?? 10.0,
                    'shift_start_time' => $request->shift_start_time,
                    'shift_end_time' => $request->shift_end_time,
                    'working_days' => $request->working_days,
                    'emergency_contact_name' => $request->emergency_contact_name,
                    'emergency_contact_phone' => $request->emergency_contact_phone,
                    'notes' => $request->notes,
                ]);

                // Update user account if exists
                if ($rider->user) {
                    $rider->user->update([
                        'name' => $request->name,
                        'email' => $request->email,
                    ]);
                }

                return redirect()->route('riders.show', $rider)
                    ->with('success', 'Rider updated successfully.');
            });
        } catch (\Exception $e) {
            return back()->withErrors(['error' => 'Failed to update rider: ' . $e->getMessage()]);
        }
    }

    /**
     * Remove the specified rider
     */
    public function destroy(Rider $rider)
    {
        try {
            return DB::transaction(function () use ($rider) {
                // Check if rider has active orders
                if ($rider->activeOrders()->count() > 0) {
                    return back()->withErrors(['error' => 'Cannot delete rider with active orders.']);
                }

                // Delete user account if exists
                if ($rider->user) {
                    $rider->user->delete();
                }

                $rider->delete();

                return redirect()->route('riders.index')
                    ->with('success', 'Rider deleted successfully.');
            });
        } catch (\Exception $e) {
            return back()->withErrors(['error' => 'Failed to delete rider: ' . $e->getMessage()]);
        }
    }

    /**
     * Update rider status
     */
    public function updateStatus(Request $request, Rider $rider)
    {
        $request->validate([
            'status' => 'required|in:active,inactive,suspended',
        ]);

        $rider->update(['status' => $request->status]);

        return back()->with('success', 'Rider status updated successfully.');
    }

    /**
     * Update rider delivery status
     */
    public function updateDeliveryStatus(Request $request, Rider $rider)
    {
        $request->validate([
            'delivery_status' => 'required|in:available,busy,offline,on_break',
        ]);

        $rider->update([
            'delivery_status' => $request->delivery_status,
            'last_active_at' => now(),
        ]);

        return back()->with('success', 'Rider delivery status updated successfully.');
    }

    /**
     * Get available riders for assignment
     */
    public function getAvailableRiders(Request $request)
    {
        $riders = Rider::available()
                      ->inShift()
                      ->select(['id', 'name', 'phone', 'current_delivery_count', 'max_concurrent_deliveries', 'average_rating'])
                      ->get();

        return response()->json($riders);
    }

    /**
     * Reset daily counters for all riders
     */
    public function resetDailyCounters()
    {
        Rider::query()->update(['completed_orders_today' => 0]);

        return back()->with('success', 'Daily counters reset for all riders.');
    }

    /**
     * Export riders data
     */
    public function export(Request $request)
    {
        $query = Rider::query();

        // Apply same filters as index
        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('phone', 'like', "%{$search}%")
                  ->orWhere('email', 'like', "%{$search}%")
                  ->orWhere('employee_id', 'like', "%{$search}%");
            });
        }

        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }

        if ($request->filled('delivery_status')) {
            $query->where('delivery_status', $request->delivery_status);
        }

        if ($request->filled('vehicle_type')) {
            $query->where('vehicle_type', $request->vehicle_type);
        }

        $riders = $query->orderBy('created_at', 'desc')->get();

        $csvData = [];
        $csvData[] = [
            'Employee ID',
            'Name',
            'Phone',
            'Email',
            'Vehicle Type',
            'License Number',
            'Status',
            'Delivery Status',
            'Total Deliveries',
            'Average Rating',
            'Total Earnings',
            'Hire Date',
            'Created At',
        ];

        foreach ($riders as $rider) {
            $csvData[] = [
                $rider->employee_id,
                $rider->name,
                $rider->phone,
                $rider->email ?? '',
                ucfirst($rider->vehicle_type),
                $rider->license_number ?? '',
                ucfirst($rider->status),
                ucfirst(str_replace('_', ' ', $rider->delivery_status)),
                $rider->total_deliveries,
                number_format($rider->average_rating, 2),
                '$' . number_format($rider->total_earnings, 2),
                $rider->hire_date->format('Y-m-d'),
                $rider->created_at->format('Y-m-d H:i:s'),
            ];
        }

        $filename = 'riders_' . now()->format('Y-m-d_H-i-s') . '.csv';

        $headers = [
            'Content-Type' => 'text/csv',
            'Content-Disposition' => "attachment; filename=\"{$filename}\"",
        ];

        $callback = function () use ($csvData) {
            $file = fopen('php://output', 'w');
            foreach ($csvData as $row) {
                fputcsv($file, $row);
            }
            fclose($file);
        };

        return response()->stream($callback, 200, $headers);
    }
}
