<?php

namespace App\Http\Controllers\Tenant;

use App\Http\Controllers\Controller;
use App\Models\Tenant\Setting;
use App\Models\Tenant\Media;
use Illuminate\Http\Request;
use Inertia\Inertia;

class SettingController extends Controller
{
    /**
     * Display the settings page
     */
    public function index()
    {
        // Get all settings grouped by category
        $settings = [
            'general' => Setting::where('group', 'general')->pluck('value', 'key')->toArray(),
            'appearance' => Setting::where('group', 'appearance')->pluck('value', 'key')->toArray(),
            'contact' => Setting::where('group', 'contact')->pluck('value', 'key')->toArray(),
            'social' => Setting::where('group', 'social')->pluck('value', 'key')->toArray(),
        ];

        // Get logo media if exists
        $logoMediaId = Setting::get('logo_media_id');
        $logoMedia = $logoMediaId ? Media::find($logoMediaId) : null;

        return Inertia::render('Tenant/Settings/Index', [
            'settings' => $settings,
            'logoMedia' => $logoMedia,
        ]);
    }

    /**
     * Update settings
     */
    public function update(Request $request)
    {
        $request->validate([
            'general.site_name' => 'required|string|max:255',
            'general.site_description' => 'nullable|string|max:500',
            'general.logo_media_id' => 'nullable|exists:media,id',
            'contact.phone' => 'nullable|string|max:20',
            'contact.email' => 'nullable|email|max:255',
            'contact.address' => 'nullable|string|max:500',
            'contact.city' => 'nullable|string|max:100',
            'contact.state' => 'nullable|string|max:100',
            'contact.postal_code' => 'nullable|string|max:20',
            'contact.country' => 'nullable|string|max:100',
            'appearance.primary_color' => 'nullable|string|max:7',
            'appearance.secondary_color' => 'nullable|string|max:7',
            'appearance.theme_mode' => 'nullable|in:light,dark,auto',
            'social.facebook_url' => 'nullable|url|max:255',
            'social.instagram_url' => 'nullable|url|max:255',
            'social.twitter_url' => 'nullable|url|max:255',
            'social.youtube_url' => 'nullable|url|max:255',
            'social.linkedin_url' => 'nullable|url|max:255',
        ]);

        // Update general settings
        if ($request->has('general')) {
            foreach ($request->general as $key => $value) {
                Setting::set($key, $value, 'string', 'general');
            }
        }

        // Update contact settings
        if ($request->has('contact')) {
            foreach ($request->contact as $key => $value) {
                Setting::set($key, $value, 'string', 'contact');
            }
        }

        // Update appearance settings
        if ($request->has('appearance')) {
            foreach ($request->appearance as $key => $value) {
                Setting::set($key, $value, 'string', 'appearance');
            }
        }

        // Update social settings
        if ($request->has('social')) {
            foreach ($request->social as $key => $value) {
                Setting::set($key, $value, 'string', 'social');
            }
        }

        return back()->with('success', 'Settings updated successfully.');
    }

    /**
     * Get public settings for guest pages
     */
    public function getPublicSettings()
    {
        return response()->json(Setting::getPublicSettings());
    }
}
