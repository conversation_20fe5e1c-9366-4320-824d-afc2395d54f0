<?php

namespace App\Http\Controllers\Tenant;

use App\Http\Controllers\Controller;
use App\Models\Tenant\MenuItem;
use App\Models\Tenant\Category;
use App\Models\Tenant\Restaurant;
use App\Models\Tenant\MenuItemAddon;
use Illuminate\Http\Request;
use Inertia\Inertia;
use Illuminate\Support\Str;

class MenuItemController extends Controller
{
    /**
     * Display a listing of menu items.
     */
    public function index(Request $request)
    {
        $query = MenuItem::with(['category', 'primaryMedia', 'mediaItems', 'variations', 'addons']);

        // Search
        if ($request->search) {
            $query->where(function ($q) use ($request) {
                $q->where('name', 'like', '%' . $request->search . '%')
                  ->orWhere('description', 'like', '%' . $request->search . '%');
            });
        }

        // Filter by category
        if ($request->category_id) {
            $query->where('category_id', $request->category_id);
        }

        // Filter by status
        if ($request->status) {
            if ($request->status === 'active') {
                $query->where('is_active', true);
            } elseif ($request->status === 'inactive') {
                $query->where('is_active', false);
            }
        }

        // Filter by availability
        if ($request->availability) {
            if ($request->availability === 'available') {
                $query->where('is_available', true);
            } elseif ($request->availability === 'unavailable') {
                $query->where('is_available', false);
            }
        }

        // Filter by dietary preferences
        if ($request->dietary) {
            switch ($request->dietary) {
                case 'vegetarian':
                    $query->where('is_vegetarian', true);
                    break;
                case 'vegan':
                    $query->where('is_vegan', true);
                    break;
                case 'gluten_free':
                    $query->where('is_gluten_free', true);
                    break;
            }
        }

        $menuItems = $query->latest()->paginate(20);
        $categories = Category::active()->ordered()->get();

        return Inertia::render('Tenant/MenuItems/Index', [
            'menuItems' => $menuItems,
            'categories' => $categories,
            'filters' => $request->only(['search', 'category_id', 'status', 'availability', 'dietary']),
        ]);
    }

    /**
     * Display a public listing of menu items and combo items for customers.
     */
    public function publicIndex(Request $request)
    {
        // Get regular menu items
        $menuItemsQuery = MenuItem::with(['category', 'primaryMedia', 'categories'])
            ->where('is_active', true)
            ->where('is_available', true);

        // Get combo items (now from menu_items table)
        $comboItemsQuery = MenuItem::with(['comboItems.primaryMedia', 'comboItems.mediaItems', 'primaryMedia', 'mediaItems'])
            ->where('is_combo', true)
            ->where('is_available', true);

        // Search functionality
        if ($request->filled('search')) {
            $search = $request->search;
            $menuItemsQuery->where(function ($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('description', 'like', "%{$search}%");
            });

            $comboItemsQuery->where(function ($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('description', 'like', "%{$search}%");
            });
        }

        // Filter by category (for menu items only)
        if ($request->filled('category_id')) {
            $menuItemsQuery->where('category_id', $request->category_id);
        }

        // Get the data
        $menuItems = $menuItemsQuery->ordered()->get();
        $comboItems = $comboItemsQuery->ordered()->get();

        // Transform combo items to have a consistent structure with menu items
        $transformedComboItems = $comboItems->map(function ($combo) {
            return [
                'id' => $combo->id,
                'name' => $combo->name,
                'description' => $combo->description,
                'price' => $combo->price,
                'primary_image_url' => $combo->primary_image_url,
                'thumbnail_url' => $combo->thumbnail_url,
                'is_combo' => true,
                'components' => $combo->comboItems->map(function ($item) {
                    return [
                        'id' => $item->id,
                        'component_type' => $item->pivot->component_type,
                        'is_required' => $item->pivot->is_required,
                        'menu_item' => [
                            'id' => $item->id,
                            'name' => $item->name,
                            'price' => $item->price,
                            'primary_image_url' => $item->primary_image_url,
                            'thumbnail_url' => $item->thumbnail_url,
                            'image_url' => $item->image_url, // Deprecated but kept for compatibility
                        ]
                    ];
                }),
                'individual_total' => $combo->individual_total,
                'savings' => $combo->savings,
            ];
        });

        // Transform regular menu items
        $transformedMenuItems = $menuItems->map(function ($item) {
            return [
                'id' => $item->id,
                'name' => $item->name,
                'description' => $item->description,
                'price' => $item->price,
                'image_url' => $item->primary_image_url,
                'is_combo' => false,
                'category' => $item->category,
                'categories' => $item->categories,
            ];
        });

        // Combine and sort all items
        $allItems = $transformedMenuItems->concat($transformedComboItems)
            ->sortBy('name')
            ->values();

        $categories = \App\Models\Tenant\Category::active()->ordered()->get();

        return Inertia::render('Tenant/Menu/PublicIndex', [
            'items' => $allItems,
            'categories' => $categories,
            'filters' => $request->only(['search', 'category_id']),
        ]);
    }

    /**
     * Show the form for creating a new menu item.
     */
    public function create()
    {
        $restaurant = Restaurant::first();
        $categories = Category::with('activeSubcategories')->active()->ordered()->get();
        $addons = MenuItemAddon::available()->ordered()->get();

        // Get available menu items for combo creation (exclude combo items to prevent nesting)
        $availableMenuItems = MenuItem::available()
            ->where('is_combo', false)
            ->with(['category', 'primaryMedia'])
            ->ordered()
            ->get();

        return Inertia::render('Tenant/MenuItems/Create', [
            'restaurant' => $restaurant,
            'categories' => $categories,
            'addons' => $addons,
            'addonCategories' => MenuItemAddon::getCategories(),
            'availableMenuItems' => $availableMenuItems,
        ]);
    }

    /**
     * Store a newly created menu item.
     */
    public function store(Request $request)
    {
        $request->validate([
            'category_id' => 'nullable|exists:categories,id', // Keep for backward compatibility
            'category_ids' => 'nullable|array|min:1', // New multi-category field
            'category_ids.*' => 'exists:categories,id',
            'name' => 'required|string|max:255',
            'slug' => 'nullable|string|max:255',
            'description' => 'nullable|string',
            'price' => 'required|numeric|min:0',
            'preparation_time' => 'nullable|integer|min:1|max:180',
            'calories' => 'nullable|integer|min:0',
            'ingredients' => 'nullable|string',
            'is_vegetarian' => 'boolean',
            'is_vegan' => 'boolean',
            'is_gluten_free' => 'boolean',
            'is_spicy' => 'boolean',
            'is_available' => 'boolean',
            'is_featured' => 'boolean',
            'sort_order' => 'nullable|integer|min:0',
            'media_id' => 'nullable|exists:media,id',
            'media_ids' => 'nullable|array|max:5',
            'media_ids.*' => 'exists:media,id',
            'variations' => 'nullable|array',
            'variations.*.name' => 'required|string|max:255',
            'variations.*.name_bn' => 'nullable|string|max:255',
            'variations.*.price_modifier' => 'required|numeric',
            'variations.*.is_available' => 'boolean',
            'variations.*.sort_order' => 'integer|min:0',
            'variations.*.description' => 'nullable|string|max:1000',
            'variations.*.description_bn' => 'nullable|string|max:1000',
            'addon_ids' => 'nullable|array',
            'addon_ids.*' => 'exists:menu_item_addons,id',
            // New size variations
            'sizes' => 'nullable|array',
            'sizes.*.name' => 'required|string|max:255',
            'sizes.*.name_bn' => 'nullable|string|max:255',
            'sizes.*.price_modifier' => 'required|numeric',
            'sizes.*.is_available' => 'boolean',
            'sizes.*.sort_order' => 'integer|min:0',
            'sizes.*.description' => 'nullable|string|max:1000',
            'sizes.*.description_bn' => 'nullable|string|max:1000',
            // Combo validation
            'is_combo' => 'boolean',
            'combo_components' => 'nullable|array|min:1',
            'combo_components.*.menu_item_id' => 'required|exists:menu_items,id',
            'combo_components.*.component_type' => 'required|in:main,side,drink,dessert',
            'combo_components.*.quantity' => 'required|integer|min:1|max:10',
            'combo_components.*.is_required' => 'boolean',
            'combo_components.*.sort_order' => 'integer|min:0',

        ]);

        $restaurant = Restaurant::first();

        // If no restaurant exists, create a default one
        if (!$restaurant) {
            $restaurant = Restaurant::create([
                'name' => 'Default Restaurant',
                'slug' => 'default-restaurant',
                'description' => 'Default restaurant for this tenant',
                'email' => '<EMAIL>',
                'phone' => '******-0123',
                'address' => '123 Main Street',
                'city' => 'New York',
                'state' => 'NY',
                'country' => 'USA',
                'postal_code' => '10001',
                'currency' => 'USD',
                'timezone' => 'UTC',
                'language' => 'en',
                'tax_rate' => 0,
                'service_charge' => 0,
                'delivery_charge' => 0,
                'minimum_order_amount' => 0,
                'opening_time' => '09:00',
                'closing_time' => '22:00',
                'is_open' => true,
                'is_delivery_enabled' => true,
                'is_takeaway_enabled' => true,
                'is_dine_in_enabled' => true,
            ]);
        }

        // Generate unique slug
        $baseSlug = $request->slug ?: Str::slug($request->name);
        $slug = $this->generateUniqueSlug($baseSlug);

        $menuItem = MenuItem::create([
            'category_id' => $request->category_id,
            'media_id' => $request->media_id,
            'name' => $request->name,
            'slug' => $slug,
            'description' => $request->description,
            'price' => $request->price,
            'preparation_time' => $request->preparation_time,
            'calories' => $request->calories,
            'ingredients' => $request->ingredients,
            'is_vegetarian' => $request->boolean('is_vegetarian'),
            'is_vegan' => $request->boolean('is_vegan'),
            'is_gluten_free' => $request->boolean('is_gluten_free'),
            'is_spicy' => $request->boolean('is_spicy'),
            'is_available' => $request->boolean('is_available', true),
            'is_featured' => $request->boolean('is_featured'),
            'is_combo' => $request->boolean('is_combo'),
            'sort_order' => $request->sort_order ?? 0,
        ]);

        // Handle multiple images
        if ($request->has('media_ids') && is_array($request->media_ids)) {
            $menuItem->syncMedia($request->media_ids);
        }

        // Handle variations (simplified for new structure)
        if ($request->has('variations') && is_array($request->variations)) {
            foreach ($request->variations as $variationData) {
                $menuItem->variations()->create([
                    'name' => $variationData['name'],
                    'name_bn' => $variationData['name_bn'] ?? null,
                    'price_modifier' => $variationData['price_modifier'] ?? 0,
                    'is_available' => $variationData['is_available'] ?? true,
                    'sort_order' => $variationData['sort_order'] ?? 0,
                    'description' => $variationData['description'] ?? null,
                    'description_bn' => $variationData['description_bn'] ?? null,
                ]);
            }
        }



        // Handle new size variations
        if ($request->has('sizes') && is_array($request->sizes)) {
            foreach ($request->sizes as $sizeData) {
                $menuItem->sizes()->create([
                    'name' => $sizeData['name'],
                    'name_bn' => $sizeData['name_bn'] ?? null,
                    'price_modifier' => $sizeData['price_modifier'],
                    'is_available' => $sizeData['is_available'] ?? true,
                    'sort_order' => $sizeData['sort_order'] ?? 0,
                    'description' => $sizeData['description'] ?? null,
                    'description_bn' => $sizeData['description_bn'] ?? null,
                ]);
            }
        }

        // Handle categories (many-to-many)
        $categoryIds = [];
        if ($request->has('category_ids') && is_array($request->category_ids)) {
            $categoryIds = $request->category_ids;
        } elseif ($request->category_id) {
            // Backward compatibility: if single category_id is provided
            $categoryIds = [$request->category_id];
        }

        if (!empty($categoryIds)) {
            $menuItem->categories()->sync($categoryIds);
        }

        // Handle addons
        if ($request->has('addon_ids') && is_array($request->addon_ids)) {
            $menuItem->addons()->sync($request->addon_ids);
        }

        // Handle combo components
        if ($request->boolean('is_combo') && $request->has('combo_components') && is_array($request->combo_components)) {
            foreach ($request->combo_components as $index => $component) {
                // Create multiple entries for quantity > 1
                for ($i = 0; $i < $component['quantity']; $i++) {
                    $menuItem->comboItems()->attach($component['menu_item_id'], [
                        'component_type' => $component['component_type'],
                        'is_required' => $component['is_required'] ?? false,
                        'sort_order' => ($component['sort_order'] ?? $index) * 10 + $i, // Ensure unique sort order
                    ]);
                }
            }
        }

        return redirect()->route('menu-items.index')
            ->with('success', 'Menu item created successfully.');
    }

    /**
     * Display the specified menu item.
     */
    public function show(MenuItem $menuItem)
    {
        $menuItem->load([
            'category',
            'categories', // Load many-to-many categories
            'media',
            'mediaItems',
            'variations' => function ($query) {
                $query->available()->ordered();
            },
            'addons' => function ($query) {
                $query->available();
            },
            'orderItems' => function ($query) {
                $query->with('order')->latest()->take(10);
            }
        ]);

        // Calculate sales statistics
        $totalOrders = $menuItem->orderItems()->count();
        $totalRevenue = $menuItem->orderItems()->sum('total_price');
        $averageRating = 4.5; // TODO: Implement rating system

        return Inertia::render('Tenant/MenuItems/Show', [
            'menuItem' => $menuItem,
            'statistics' => [
                'total_orders' => $totalOrders,
                'total_revenue' => $totalRevenue,
                'average_rating' => $averageRating,
                'minimum_price' => $menuItem->minimum_price,
                'has_variations' => $menuItem->hasVariations(),
                'has_addons' => $menuItem->hasAddons(),
            ],
        ]);
    }

    /**
     * Show the form for editing the specified menu item.
     */
    public function edit(MenuItem $menuItem)
    {
        $menuItem->load([
            'category',
            'categories', // Load many-to-many categories
            'media',
            'mediaItems',
            'variations' => function ($query) {
                $query->available()->ordered();
            },
            'addons',
            'sizes' => function ($query) {
                $query->ordered();
            },
            'comboItems' => function ($query) {
                $query->with(['category', 'primaryMedia']);
            }
        ]);


        $categories = Category::with('activeSubcategories')->active()->ordered()->get();
        $addons = MenuItemAddon::available()->ordered()->get();

        // Get available menu items for combo creation (exclude combo items and current item to prevent nesting)
        $availableMenuItems = MenuItem::available()
            ->where('is_combo', false)
            ->where('id', '!=', $menuItem->id) // Exclude current item
            ->with(['category', 'primaryMedia'])
            ->ordered()
            ->get();

        return Inertia::render('Tenant/MenuItems/Edit', [
            'menuItem' => $menuItem,
            'categories' => $categories,
            'addons' => $addons,
            'addonCategories' => MenuItemAddon::getCategories(),
            'availableMenuItems' => $availableMenuItems,
        ]);
    }

    /**
     * Update the specified menu item.
     */
    public function update(Request $request, MenuItem $menuItem)
    {
        $request->validate([
            'category_id' => 'nullable|exists:categories,id', // Keep for backward compatibility
            'category_ids' => 'nullable|array|min:1', // New multi-category field
            'category_ids.*' => 'exists:categories,id',
            'name' => 'required|string|max:255',
            'slug' => 'nullable|string|max:255',
            'description' => 'nullable|string',
            'short_description' => 'nullable|string|max:500',
            'price' => 'required|numeric|min:0',
            'cost_price' => 'nullable|numeric|min:0',
            'preparation_time' => 'nullable|integer|min:1|max:180',
            'calories' => 'nullable|integer|min:0',
            'ingredients' => 'nullable|string',
            'is_vegetarian' => 'boolean',
            'is_vegan' => 'boolean',
            'is_gluten_free' => 'boolean',
            'is_spicy' => 'boolean',
            'is_available' => 'boolean',
            'is_featured' => 'boolean',
            'is_active' => 'boolean',
            'sort_order' => 'nullable|integer|min:0',
            'media_id' => 'nullable|exists:media,id',
            'media_ids' => 'nullable|array|max:5',
            'media_ids.*' => 'exists:media,id',
            // New size variations
            'sizes' => 'nullable|array',
            'sizes.*.name' => 'required|string|max:255',
            'sizes.*.name_bn' => 'nullable|string|max:255',
            'sizes.*.price_modifier' => 'required|numeric',
            'sizes.*.is_available' => 'boolean',
            'sizes.*.sort_order' => 'integer|min:0',
            'sizes.*.description' => 'nullable|string|max:1000',
            'sizes.*.description_bn' => 'nullable|string|max:1000',
            // Addons
            'addon_ids' => 'nullable|array',
            'addon_ids.*' => 'exists:menu_item_addons,id',
            // Combo validation
            'is_combo' => 'boolean',
            'combo_components' => 'nullable|array|min:1',
            'combo_components.*.menu_item_id' => 'required|exists:menu_items,id',
            'combo_components.*.component_type' => 'required|in:main,side,drink,dessert',
            'combo_components.*.quantity' => 'required|integer|min:1|max:10',
            'combo_components.*.is_required' => 'boolean',
            'combo_components.*.sort_order' => 'integer|min:0',
        ]);

        // Generate unique slug if needed
        $baseSlug = $request->slug ?: Str::slug($request->name);
        $slug = $this->generateUniqueSlug($baseSlug, $menuItem->id);

        $menuItem->update([
            'category_id' => $request->category_id,
            'media_id' => $request->media_id,
            'name' => $request->name,
            'slug' => $slug,
            'description' => $request->description,
            'price' => $request->price,
            'cost_price' => $request->cost_price,
            'preparation_time' => $request->preparation_time,
            'calories' => $request->calories,
            'ingredients' => $request->ingredients,
            'is_vegetarian' => $request->boolean('is_vegetarian'),
            'is_vegan' => $request->boolean('is_vegan'),
            'is_gluten_free' => $request->boolean('is_gluten_free'),
            'is_spicy' => $request->boolean('is_spicy'),
            'is_available' => $request->boolean('is_available'),
            'is_featured' => $request->boolean('is_featured'),
            'is_active' => $request->boolean('is_active'),
            'is_combo' => $request->boolean('is_combo'),
            'sort_order' => $request->sort_order ?? 0,
        ]);

        // Handle multiple images
        if ($request->has('media_ids') && is_array($request->media_ids)) {
            $menuItem->syncMedia($request->media_ids);
        }

        // Handle size variations - delete existing and create new ones
        if ($request->has('sizes')) {
            // Delete existing sizes
            $menuItem->sizes()->delete();

            // Create new sizes
            if (is_array($request->sizes)) {
                foreach ($request->sizes as $sizeData) {
                    $menuItem->sizes()->create([
                        'name' => $sizeData['name'],
                        'name_bn' => $sizeData['name_bn'] ?? null,
                        'price_modifier' => $sizeData['price_modifier'],
                        'is_available' => $sizeData['is_available'] ?? true,
                        'sort_order' => $sizeData['sort_order'] ?? 0,
                        'description' => $sizeData['description'] ?? null,
                        'description_bn' => $sizeData['description_bn'] ?? null,
                    ]);
                }
            }
        }

        // Handle categories (many-to-many)
        $categoryIds = [];
        if ($request->has('category_ids') && is_array($request->category_ids)) {
            $categoryIds = $request->category_ids;
        } elseif ($request->category_id) {
            // Backward compatibility: if single category_id is provided
            $categoryIds = [$request->category_id];
        }

        // Always sync categories (empty array will detach all)
        $menuItem->categories()->sync($categoryIds);

        // Handle addons
        if ($request->has('addon_ids') && is_array($request->addon_ids)) {
            $menuItem->addons()->sync($request->addon_ids);
        }

        // Handle combo components
        if ($request->boolean('is_combo')) {
            // Clear existing combo components
            $menuItem->comboItems()->detach();

            // Add new combo components
            if ($request->has('combo_components') && is_array($request->combo_components)) {
                foreach ($request->combo_components as $index => $component) {
                    // Create multiple entries for quantity > 1
                    for ($i = 0; $i < $component['quantity']; $i++) {
                        $menuItem->comboItems()->attach($component['menu_item_id'], [
                            'component_type' => $component['component_type'],
                            'is_required' => $component['is_required'] ?? false,
                            'sort_order' => ($component['sort_order'] ?? $index) * 10 + $i, // Ensure unique sort order
                        ]);
                    }
                }
            }
        } else {
            // If not a combo anymore, clear all combo components
            $menuItem->comboItems()->detach();
        }

        return redirect()->route('menu-items.index')
            ->with('success', 'Menu item updated successfully.');
    }

    /**
     * Remove the specified menu item.
     */
    public function destroy(MenuItem $menuItem)
    {
        // Check if menu item has orders
        if ($menuItem->orderItems()->exists()) {
            return back()->withErrors(['error' => 'Cannot delete menu item with existing orders. Consider deactivating it instead.']);
        }

        $menuItem->delete();

        return redirect()->route('menu-items.index')
            ->with('success', 'Menu item deleted successfully.');
    }

    /**
     * Toggle menu item status.
     */
    public function toggleStatus(MenuItem $menuItem)
    {
        $menuItem->update(['is_active' => !$menuItem->is_active]);

        $status = $menuItem->is_active ? 'activated' : 'deactivated';

        return response()->json([
            'success' => true,
            'message' => "Menu item {$status} successfully.",
            'is_active' => $menuItem->is_active,
        ]);
    }

    /**
     * Toggle menu item availability.
     */
    public function toggleAvailability(MenuItem $menuItem)
    {
        $menuItem->update(['is_available' => !$menuItem->is_available]);

        $status = $menuItem->is_available ? 'available' : 'unavailable';

        return response()->json([
            'success' => true,
            'message' => "Menu item marked as {$status}.",
            'is_available' => $menuItem->is_available,
        ]);
    }

    /**
     * Bulk update menu items.
     */
    public function bulkUpdate(Request $request)
    {
        $request->validate([
            'ids' => 'required|array',
            'ids.*' => 'exists:menu_items,id',
            'action' => 'required|in:activate,deactivate,make_available,make_unavailable,delete',
        ]);

        $menuItems = MenuItem::whereIn('id', $request->ids);

        switch ($request->action) {
            case 'activate':
                $menuItems->update(['is_active' => true]);
                $message = 'Menu items activated successfully.';
                break;
            case 'deactivate':
                $menuItems->update(['is_active' => false]);
                $message = 'Menu items deactivated successfully.';
                break;
            case 'make_available':
                $menuItems->update(['is_available' => true]);
                $message = 'Menu items marked as available.';
                break;
            case 'make_unavailable':
                $menuItems->update(['is_available' => false]);
                $message = 'Menu items marked as unavailable.';
                break;
            case 'delete':
                // Check if any menu item has orders
                $hasOrders = $menuItems->whereHas('orderItems')->exists();
                if ($hasOrders) {
                    return back()->withErrors(['error' => 'Cannot delete menu items with existing orders.']);
                }
                $menuItems->delete();
                $message = 'Menu items deleted successfully.';
                break;
        }

        return back()->with('success', $message);
    }

    /**
     * Duplicate menu item.
     */
    public function duplicate(MenuItem $menuItem)
    {
        $newMenuItem = $menuItem->replicate();
        $newMenuItem->name = $menuItem->name . ' (Copy)';
        $newMenuItem->slug = $this->generateUniqueSlug(Str::slug($newMenuItem->name));
        $newMenuItem->is_active = false;
        $newMenuItem->save();

        // Copy categories (many-to-many)
        $categoryIds = $menuItem->categories()->pluck('categories.id')->toArray();
        if (!empty($categoryIds)) {
            $newMenuItem->categories()->sync($categoryIds);
        }

        // Copy media using the proper many-to-many relationship
        $mediaIds = $menuItem->mediaItems()->pluck('media.id')->toArray();
        if (!empty($mediaIds)) {
            $newMenuItem->syncMedia($mediaIds);
        }

        return redirect()->route('menu-items.edit', $newMenuItem)
            ->with('success', 'Menu item duplicated successfully.');
    }

    /**
     * Generate a unique slug for menu items
     */
    private function generateUniqueSlug(string $baseSlug, ?int $excludeId = null): string
    {
        $slug = $baseSlug;
        $counter = 1;

        while (true) {
            $query = MenuItem::where('slug', $slug);

            if ($excludeId) {
                $query->where('id', '!=', $excludeId);
            }

            if (!$query->exists()) {
                return $slug;
            }

            // Generate random suffix for uniqueness
            $randomSuffix = strtolower(Str::random(4));
            $slug = $baseSlug . '-' . $randomSuffix;

            $counter++;

            // Fallback: if we've tried many times, use timestamp
            if ($counter > 10) {
                $slug = $baseSlug . '-' . time();
                break;
            }
        }

        return $slug;
    }
}
