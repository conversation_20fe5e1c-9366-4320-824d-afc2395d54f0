<?php

namespace App\Http\Controllers\Tenant;

use App\Http\Controllers\Controller;
use App\Models\Tenant\Page;
use Illuminate\Http\Request;
use Illuminate\Support\Str;
use Inertia\Inertia;

class PageController extends Controller
{
    /**
     * Display a listing of pages
     */
    public function index(Request $request)
    {
        $query = Page::with('bannerMedia');

        // Search
        if ($request->search) {
            $query->where(function ($q) use ($request) {
                $q->where('title', 'like', '%' . $request->search . '%')
                  ->orWhere('content', 'like', '%' . $request->search . '%');
            });
        }

        // Filter by status
        if ($request->status === 'published') {
            $query->published();
        } elseif ($request->status === 'draft') {
            $query->where('is_published', false);
        }

        $pages = $query->ordered()->paginate(20);

        return Inertia::render('Tenant/Pages/Index', [
            'pages' => $pages,
            'filters' => $request->only(['search', 'status']),
        ]);
    }

    /**
     * Show the form for creating a new page
     */
    public function create()
    {
        return Inertia::render('Tenant/Pages/Create');
    }

    /**
     * Store a newly created page
     */
    public function store(Request $request)
    {
        $request->validate([
            'title' => 'required|string|max:255',
            'slug' => 'nullable|string|max:255|unique:pages,slug',
            'content' => 'nullable|string',
            'excerpt' => 'nullable|string|max:500',
            'meta_title' => 'nullable|string|max:255',
            'meta_description' => 'nullable|string|max:500',
            'meta_keywords' => 'nullable|array',
            'banner_media_id' => 'nullable|exists:media,id',
            'is_published' => 'boolean',
            'is_featured' => 'boolean',
            'show_in_menu' => 'boolean',
            'sort_order' => 'nullable|integer|min:0',
        ]);

        $page = Page::create([
            'title' => $request->title,
            'slug' => $request->slug ?: Str::slug($request->title),
            'content' => $request->content,
            'excerpt' => $request->excerpt,
            'meta_title' => $request->meta_title ?: $request->title,
            'meta_description' => $request->meta_description,
            'meta_keywords' => $request->meta_keywords,
            'banner_media_id' => $request->banner_media_id,
            'is_published' => $request->boolean('is_published', true),
            'is_featured' => $request->boolean('is_featured'),
            'show_in_menu' => $request->boolean('show_in_menu', true),
            'sort_order' => $request->sort_order ?? 0,
            'published_at' => $request->boolean('is_published', true) ? now() : null,
        ]);

        return redirect()->route('pages.index')
            ->with('success', 'Page created successfully.');
    }

    /**
     * Display the specified page
     */
    public function show(Page $page)
    {
        $page->load('bannerMedia');

        return Inertia::render('Tenant/Pages/Show', [
            'page' => $page,
        ]);
    }

    /**
     * Show the form for editing the specified page
     */
    public function edit(Page $page)
    {
        $page->load('bannerMedia');

        return Inertia::render('Tenant/Pages/Edit', [
            'page' => $page,
        ]);
    }

    /**
     * Update the specified page
     */
    public function update(Request $request, Page $page)
    {
        $request->validate([
            'title' => 'required|string|max:255',
            'slug' => 'nullable|string|max:255|unique:pages,slug,' . $page->id,
            'content' => 'nullable|string',
            'excerpt' => 'nullable|string|max:500',
            'meta_title' => 'nullable|string|max:255',
            'meta_description' => 'nullable|string|max:500',
            'meta_keywords' => 'nullable|array',
            'banner_media_id' => 'nullable|exists:media,id',
            'is_published' => 'boolean',
            'is_featured' => 'boolean',
            'show_in_menu' => 'boolean',
            'sort_order' => 'nullable|integer|min:0',
        ]);

        $page->update([
            'title' => $request->title,
            'slug' => $request->slug ?: Str::slug($request->title),
            'content' => $request->content,
            'excerpt' => $request->excerpt,
            'meta_title' => $request->meta_title ?: $request->title,
            'meta_description' => $request->meta_description,
            'meta_keywords' => $request->meta_keywords,
            'banner_media_id' => $request->banner_media_id,
            'is_published' => $request->boolean('is_published'),
            'is_featured' => $request->boolean('is_featured'),
            'show_in_menu' => $request->boolean('show_in_menu'),
            'sort_order' => $request->sort_order ?? 0,
        ]);

        return redirect()->route('pages.index')
            ->with('success', 'Page updated successfully.');
    }

    /**
     * Remove the specified page
     */
    public function destroy(Page $page)
    {
        $page->delete();

        return redirect()->route('pages.index')
            ->with('success', 'Page deleted successfully.');
    }

    /**
     * Toggle page publication status
     */
    public function toggleStatus(Page $page)
    {
        $page->update([
            'is_published' => !$page->is_published,
            'published_at' => !$page->is_published ? now() : $page->published_at,
        ]);

        $status = $page->is_published ? 'published' : 'unpublished';

        return response()->json([
            'success' => true,
            'message' => "Page {$status} successfully.",
            'is_published' => $page->is_published,
        ]);
    }
}
