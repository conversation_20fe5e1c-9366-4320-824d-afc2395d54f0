<?php

namespace App\Http\Controllers\Tenant;

use App\Http\Controllers\Controller;
use App\Models\Tenant\Employee;
use App\Models\Tenant\Department;
use App\Models\Shift;
use App\Models\TimeEntry;
use Illuminate\Http\Request;
use Inertia\Inertia;
use Carbon\Carbon;

class StaffController extends Controller
{
    public function __construct()
    {
        $this->middleware('auth');
        $this->middleware('verified');
        // Note: Role middleware should be applied in routes, not controller constructor
    }

    /**
     * Display staff dashboard
     */
    public function index()
    {
        $staff = Employee::with(['department', 'primaryBranch'])
                        ->where('is_active', true)
                        ->orderBy('name')
                        ->paginate(15);

        $stats = [
            'total_staff' => Employee::where('is_active', true)->count(),
            'active_staff' => Employee::where('employment_status', 'active')->count(),
            'departments' => Department::count(),
            'recent_hires' => Employee::where('hire_date', '>=', now()->subDays(30))->count(),
        ];

        return Inertia::render('Tenant/Staff/Index', [
            'staff' => $staff,
            'stats' => $stats,
            'departments' => $this->getDepartments(),
        ]);
    }

    /**
     * Show the form for creating a new staff member.
     */
    public function create()
    {
        return Inertia::render('Tenant/Staff/Create', [
            'departments' => $this->getDepartments(),
        ]);
    }

    /**
     * Store a newly created staff member.
     */
    public function store(Request $request)
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'email' => 'nullable|email|unique:employees,email',
            'phone' => 'nullable|string|max:20',
            'department' => 'required|string|max:100',
            'position' => 'required|string|max:100',
            'hourly_rate' => 'required|numeric|min:0',
            'hire_date' => 'required|date',
        ]);

        // Get or create department
        $department = Department::firstOrCreate(['name' => $request->department]);

        $employee = Employee::create([
            'name' => $request->name,
            'email' => $request->email,
            'phone' => $request->phone,
            'department_id' => $department->id,
            'position' => $request->position,
            'hourly_rate' => $request->hourly_rate,
            'hire_date' => $request->hire_date,
            'is_active' => true,
            'employment_status' => 'active',
            'employee_type' => 'other', // Default type
        ]);

        return redirect()->route('staff.index')
            ->with('success', 'Staff member created successfully.');
    }

    /**
     * Display the specified staff member.
     */
    public function show(Employee $staff)
    {
        $staff->load(['employeeShifts' => function ($query) {
            $query->latest()->limit(10);
        }]);

        return Inertia::render('Tenant/Staff/Show', [
            'staff' => $staff,
        ]);
    }

    /**
     * Show the form for editing the specified staff member.
     */
    public function edit(Employee $staff)
    {
        return Inertia::render('Tenant/Staff/Edit', [
            'staff' => $staff,
            'departments' => $this->getDepartments(),
        ]);
    }

    /**
     * Update the specified staff member.
     */
    public function update(Request $request, Employee $staff)
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'email' => 'nullable|email|unique:employees,email,' . $staff->id,
            'phone' => 'nullable|string|max:20',
            'department' => 'required|string|max:100',
            'position' => 'required|string|max:100',
            'hourly_rate' => 'required|numeric|min:0',
            'hire_date' => 'required|date',
            'is_active' => 'boolean',
        ]);

        // Get or create department
        $department = Department::firstOrCreate(['name' => $request->department]);

        $staff->update([
            'name' => $request->name,
            'email' => $request->email,
            'phone' => $request->phone,
            'department_id' => $department->id,
            'position' => $request->position,
            'hourly_rate' => $request->hourly_rate,
            'hire_date' => $request->hire_date,
            'is_active' => $request->boolean('is_active', true),
        ]);

        return redirect()->route('staff.index')
            ->with('success', 'Staff member updated successfully.');
    }

    /**
     * Remove the specified staff member.
     */
    public function destroy(Employee $staff)
    {
        // Check if staff has active shifts
        if ($staff->employeeShifts()->whereIn('status', ['scheduled', 'confirmed', 'started'])->exists()) {
            return back()->withErrors(['error' => 'Cannot delete staff member with active shifts.']);
        }

        $staff->delete();

        return redirect()->route('staff.index')
            ->with('success', 'Staff member deleted successfully.');
    }

    /**
     * Display staff performance overview
     */
    public function performance(Request $request)
    {
        $dateFrom = $request->get('date_from', now()->startOfMonth()->format('Y-m-d'));
        $dateTo = $request->get('date_to', now()->endOfMonth()->format('Y-m-d'));

        $staff = User::where('is_active', true)
                    ->with(['shifts' => function ($query) use ($dateFrom, $dateTo) {
                        $query->whereBetween('date', [$dateFrom, $dateTo]);
                    }])
                    ->get()
                    ->map(function ($user) use ($dateFrom, $dateTo) {
                        $shifts = $user->shifts;
                        $timeEntries = TimeEntry::where('user_id', $user->id)
                                               ->whereBetween('date', [$dateFrom, $dateTo])
                                               ->get();

                        return [
                            'id' => $user->id,
                            'name' => $user->name,
                            'department' => $user->department,
                            'position' => $user->position,
                            'total_shifts' => $shifts->count(),
                            'completed_shifts' => $shifts->where('status', 'completed')->count(),
                            'total_hours' => $timeEntries->sum('total_hours'),
                            'late_arrivals' => $timeEntries->where('is_late', true)->count(),
                            'early_departures' => $timeEntries->where('is_early_departure', true)->count(),
                            'attendance_rate' => $this->calculateAttendanceRate($user, $dateFrom, $dateTo),
                            'punctuality_score' => $this->calculatePunctualityScore($user, $dateFrom, $dateTo),
                            'productivity_score' => $this->calculateProductivityScore($user, $dateFrom, $dateTo),
                        ];
                    });

        return Inertia::render('Tenant/Staff/Performance', [
            'staff' => $staff,
            'filters' => compact('dateFrom', 'dateTo'),
            'departmentStats' => $this->getDepartmentStats($dateFrom, $dateTo),
        ]);
    }

    /**
     * Display staff scheduling interface
     */
    public function scheduling(Request $request)
    {
        $weekStart = $request->get('week_start', now()->startOfWeek()->format('Y-m-d'));
        $weekEnd = Carbon::parse($weekStart)->endOfWeek()->format('Y-m-d');

        $staff = User::where('is_active', true)
                    ->orderBy('department')
                    ->orderBy('name')
                    ->get(['id', 'name', 'department', 'position', 'hourly_rate']);

        $shifts = Shift::with('user')
                      ->whereBetween('date', [$weekStart, $weekEnd])
                      ->get()
                      ->groupBy('date');

        $weekDays = [];
        $current = Carbon::parse($weekStart);
        while ($current->lte(Carbon::parse($weekEnd))) {
            $weekDays[] = [
                'date' => $current->format('Y-m-d'),
                'day_name' => $current->format('l'),
                'shifts' => $shifts->get($current->format('Y-m-d'), collect()),
            ];
            $current->addDay();
        }

        return Inertia::render('Tenant/Staff/Scheduling', [
            'staff' => $staff,
            'weekDays' => $weekDays,
            'weekStart' => $weekStart,
            'weekEnd' => $weekEnd,
            'departments' => $this->getDepartments(),
        ]);
    }

    /**
     * Display attendance tracking
     */
    public function attendance(Request $request)
    {
        $date = $request->get('date', today()->format('Y-m-d'));

        $scheduledShifts = Shift::with('user')
                               ->whereDate('date', $date)
                               ->orderBy('start_time')
                               ->get();

        $timeEntries = TimeEntry::with('user')
                                ->whereDate('date', $date)
                                ->get()
                                ->keyBy('user_id');

        $attendanceData = $scheduledShifts->map(function ($shift) use ($timeEntries) {
            $timeEntry = $timeEntries->get($shift->user_id);

            return [
                'shift' => $shift,
                'time_entry' => $timeEntry,
                'status' => $this->getAttendanceStatus($shift, $timeEntry),
                'hours_worked' => $timeEntry?->total_hours ?? 0,
                'is_late' => $timeEntry?->is_late ?? false,
                'is_early_departure' => $timeEntry?->is_early_departure ?? false,
            ];
        });

        $stats = [
            'scheduled' => $scheduledShifts->count(),
            'present' => $timeEntries->count(),
            'absent' => $scheduledShifts->count() - $timeEntries->count(),
            'late' => $timeEntries->where('is_late', true)->count(),
            'early_departures' => $timeEntries->where('is_early_departure', true)->count(),
        ];

        return Inertia::render('Tenant/Staff/Attendance', [
            'attendanceData' => $attendanceData,
            'date' => $date,
            'stats' => $stats,
        ]);
    }

    /**
     * Display payroll information
     */
    public function payroll(Request $request)
    {
        $period = $request->get('period', 'current_month');

        [$dateFrom, $dateTo] = $this->getPayrollPeriod($period);

        $payrollData = User::where('is_active', true)
                          ->get()
                          ->map(function ($user) use ($dateFrom, $dateTo) {
                              $timeEntries = TimeEntry::where('user_id', $user->id)
                                                    ->whereBetween('date', [$dateFrom, $dateTo])
                                                    ->get();

                              $regularHours = $timeEntries->where('total_hours', '<=', 8)->sum('total_hours');
                              $overtimeHours = $timeEntries->where('total_hours', '>', 8)
                                                          ->sum(function ($entry) {
                                                              return max(0, $entry->total_hours - 8);
                                                          });

                              $regularPay = $regularHours * $user->hourly_rate;
                              $overtimePay = $overtimeHours * $user->hourly_rate * 1.5;
                              $totalPay = $regularPay + $overtimePay;

                              return [
                                  'user' => $user,
                                  'regular_hours' => $regularHours,
                                  'overtime_hours' => $overtimeHours,
                                  'total_hours' => $regularHours + $overtimeHours,
                                  'regular_pay' => $regularPay,
                                  'overtime_pay' => $overtimePay,
                                  'total_pay' => $totalPay,
                                  'days_worked' => $timeEntries->groupBy('date')->count(),
                              ];
                          });

        $summary = [
            'total_employees' => $payrollData->count(),
            'total_hours' => $payrollData->sum('total_hours'),
            'total_regular_pay' => $payrollData->sum('regular_pay'),
            'total_overtime_pay' => $payrollData->sum('overtime_pay'),
            'total_payroll' => $payrollData->sum('total_pay'),
        ];

        return Inertia::render('Tenant/Staff/Payroll', [
            'payrollData' => $payrollData,
            'summary' => $summary,
            'period' => $period,
            'dateFrom' => $dateFrom,
            'dateTo' => $dateTo,
        ]);
    }

    /**
     * Bulk clock in/out staff
     */
    public function bulkClockAction(Request $request)
    {
        $request->validate([
            'action' => 'required|in:clock_in,clock_out',
            'user_ids' => 'required|array',
            'user_ids.*' => 'exists:users,id',
            'notes' => 'nullable|string|max:500',
        ]);

        $results = [];

        foreach ($request->user_ids as $userId) {
            $user = User::find($userId);

            if ($request->action === 'clock_in') {
                $result = $this->performClockIn($user, $request->notes);
            } else {
                $result = $this->performClockOut($user, $request->notes);
            }

            $results[] = [
                'user_id' => $userId,
                'user_name' => $user->name,
                'success' => $result['success'],
                'message' => $result['message'],
            ];
        }

        return response()->json([
            'success' => true,
            'results' => $results,
        ]);
    }

    /**
     * Generate staff schedule template
     */
    public function generateScheduleTemplate(Request $request)
    {
        $request->validate([
            'week_start' => 'required|date',
            'template_type' => 'required|in:copy_previous,auto_generate',
            'department' => 'nullable|string',
        ]);

        // This would implement schedule template generation logic
        return response()->json([
            'success' => true,
            'message' => 'Schedule template generated successfully.',
        ]);
    }

    /**
     * Send shift reminders
     */
    public function sendShiftReminders(Request $request)
    {
        $request->validate([
            'reminder_type' => 'required|in:upcoming_shift,shift_change,schedule_published',
            'shift_ids' => 'nullable|array',
            'shift_ids.*' => 'exists:shifts,id',
        ]);

        // This would implement notification sending logic
        return response()->json([
            'success' => true,
            'message' => 'Shift reminders sent successfully.',
        ]);
    }

    /**
     * Calculate attendance rate
     */
    private function calculateAttendanceRate($user, $dateFrom, $dateTo)
    {
        $scheduledShifts = Shift::where('user_id', $user->id)
                               ->whereBetween('date', [$dateFrom, $dateTo])
                               ->count();

        $attendedShifts = TimeEntry::where('user_id', $user->id)
                                  ->whereBetween('date', [$dateFrom, $dateTo])
                                  ->count();

        return $scheduledShifts > 0 ? ($attendedShifts / $scheduledShifts) * 100 : 0;
    }

    /**
     * Calculate punctuality score
     */
    private function calculatePunctualityScore($user, $dateFrom, $dateTo)
    {
        $timeEntries = TimeEntry::where('user_id', $user->id)
                               ->whereBetween('date', [$dateFrom, $dateTo])
                               ->get();

        if ($timeEntries->isEmpty()) {
            return 0;
        }

        $onTimeEntries = $timeEntries->where('is_late', false)->count();
        return ($onTimeEntries / $timeEntries->count()) * 100;
    }

    /**
     * Calculate productivity score
     */
    private function calculateProductivityScore($user, $dateFrom, $dateTo)
    {
        // Mock calculation - replace with actual productivity metrics
        return rand(75, 95);
    }

    /**
     * Get department statistics
     */
    private function getDepartmentStats($dateFrom, $dateTo)
    {
        return User::where('is_active', true)
                  ->selectRaw('department, COUNT(*) as staff_count')
                  ->groupBy('department')
                  ->get()
                  ->map(function ($dept) use ($dateFrom, $dateTo) {
                      $totalHours = TimeEntry::whereHas('user', function ($query) use ($dept) {
                          $query->where('department', $dept->department);
                      })
                      ->whereBetween('date', [$dateFrom, $dateTo])
                      ->sum('total_hours');

                      return [
                          'department' => $dept->department,
                          'staff_count' => $dept->staff_count,
                          'total_hours' => $totalHours,
                          'average_hours_per_staff' => $dept->staff_count > 0 ? $totalHours / $dept->staff_count : 0,
                      ];
                  });
    }

    /**
     * Get attendance status
     */
    private function getAttendanceStatus($shift, $timeEntry)
    {
        if (!$timeEntry) {
            return 'absent';
        }

        if (!$timeEntry->clock_out_time) {
            return 'present';
        }

        if ($timeEntry->is_late) {
            return 'late';
        }

        if ($timeEntry->is_early_departure) {
            return 'early_departure';
        }

        return 'completed';
    }

    /**
     * Get payroll period dates
     */
    private function getPayrollPeriod($period)
    {
        return match ($period) {
            'current_week' => [now()->startOfWeek(), now()->endOfWeek()],
            'last_week' => [now()->subWeek()->startOfWeek(), now()->subWeek()->endOfWeek()],
            'current_month' => [now()->startOfMonth(), now()->endOfMonth()],
            'last_month' => [now()->subMonth()->startOfMonth(), now()->subMonth()->endOfMonth()],
            default => [now()->startOfMonth(), now()->endOfMonth()],
        };
    }

    /**
     * Get departments
     */
    private function getDepartments()
    {
        return Department::orderBy('name')->pluck('name', 'id')->toArray();
    }

    /**
     * Perform clock in
     */
    private function performClockIn($user, $notes = null)
    {
        if (TimeEntry::where('user_id', $user->id)->whereNull('clock_out_time')->exists()) {
            return ['success' => false, 'message' => 'Already clocked in'];
        }

        TimeEntry::create([
            'user_id' => $user->id,
            'clock_in_time' => now(),
            'date' => today(),
            'clock_in_notes' => $notes,
        ]);

        $user->update(['is_on_shift' => true]);

        return ['success' => true, 'message' => 'Clocked in successfully'];
    }

    /**
     * Perform clock out
     */
    private function performClockOut($user, $notes = null)
    {
        $timeEntry = TimeEntry::where('user_id', $user->id)
                              ->whereNull('clock_out_time')
                              ->first();

        if (!$timeEntry) {
            return ['success' => false, 'message' => 'Not currently clocked in'];
        }

        $clockOutTime = now();
        $totalHours = $clockOutTime->diffInHours($timeEntry->clock_in_time);

        $timeEntry->update([
            'clock_out_time' => $clockOutTime,
            'clock_out_notes' => $notes,
            'total_hours' => $totalHours,
        ]);

        $user->update(['is_on_shift' => false]);

        return ['success' => true, 'message' => 'Clocked out successfully'];
    }
}
