<?php

namespace App\Http\Controllers\Tenant;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Inertia\Inertia;
use App\Models\Tenant\Restaurant;
use App\Models\Tenant\Category;
use App\Models\Tenant\MenuItem;
use App\Models\Tenant\Order;
use App\Models\Tenant\OrderItem;
use App\Models\Tenant\Table;
use App\Models\Tenant\Page;
use App\Models\Tenant\Customer;
use App\Models\Tenant\Setting;
use Illuminate\Support\Str;

class GuestController extends Controller
{
    /**
     * Get site settings for guest pages
     */
    private function getSiteSettings()
    {
        return [
            'general' => Setting::getByGroup('general'),
            'contact' => Setting::getByGroup('contact'),
            'appearance' => Setting::getByGroup('appearance'),
            'social' => Setting::getByGroup('social'),
        ];
    }

    /**
     * Show the restaurant homepage
     */
    public function index()
    {
        $restaurant = Restaurant::first();
        $siteSettings = $this->getSiteSettings();
        $featuredItems = MenuItem::with(['primaryMedia', 'mediaItems'])->active()->available()->featured()->take(6)->get();
        $categories = Category::with(['media'])->active()->ordered()->take(8)->get();

        // SEO data
        $seoData = $this->generateSeoData([
            'title' => $siteSettings['general']['site_name'] ?? $restaurant->name ?? 'Restaurant',
            'description' => $siteSettings['general']['site_description'] ?? $restaurant->description ?? 'Delicious food, great atmosphere, unforgettable experience',
            'type' => 'website',
            'url' => request()->url(),
        ]);

        // JSON-LD structured data
        $jsonLd = $this->generateRestaurantSchema($restaurant, $siteSettings);

        return view('tenant.home', compact(
            'restaurant',
            'featuredItems',
            'categories',
            'siteSettings',
            'seoData',
            'jsonLd'
        ));
    }

    /**
     * Show the full menu
     */
    public function menu()
    {
        $restaurant = Restaurant::first();
        $siteSettings = $this->getSiteSettings();
        $categories = Category::active()->ordered()->with(['activeMenuItems' => function ($query) {
            $query->with(['primaryMedia', 'mediaItems', 'variations'])->available()->ordered();
        }])->get();

        // SEO data
        $seoData = $this->generateSeoData([
            'title' => 'Menu - ' . ($siteSettings['general']['site_name'] ?? $restaurant->name ?? 'Restaurant'),
            'description' => 'Discover our delicious selection of dishes. ' . ($siteSettings['general']['site_description'] ?? $restaurant->description ?? ''),
            'type' => 'website',
            'url' => request()->url(),
        ]);

        // JSON-LD structured data for menu
        $jsonLd = $this->generateMenuSchema($categories, $restaurant, $siteSettings);

        return view('tenant.menu', compact(
            'restaurant',
            'categories',
            'siteSettings',
            'seoData',
            'jsonLd'
        ));
    }

    /**
     * Show menu items by category
     */
    public function categoryMenu(Category $category)
    {
        $restaurant = Restaurant::first();
        $siteSettings = $this->getSiteSettings();
        $menuItems = $category->activeMenuItems()
            ->with(['primaryMedia', 'mediaItems'])
            ->available()
            ->ordered()
            ->paginate(12);

        // SEO data
        $seoData = $this->generateSeoData([
            'title' => $category->name . ' - ' . ($siteSettings['general']['site_name'] ?? $restaurant->name ?? 'Restaurant'),
            'description' => $category->description ?? 'Browse our delicious menu items in this category',
            'type' => 'website',
            'url' => request()->url(),
        ]);

        return view('tenant.category-menu', compact(
            'restaurant',
            'category',
            'menuItems',
            'siteSettings',
            'seoData'
        ));
    }

    /**
     * Show a specific menu item
     */
    public function menuItem(MenuItem $menuItem)
    {
        $restaurant = Restaurant::first();
        $siteSettings = $this->getSiteSettings();
        $menuItem->load(['category', 'variations', 'addons', 'mediaItems', 'comboItems.primaryMedia', 'comboItems.mediaItems']);
        $relatedItems = MenuItem::with(['mediaItems'])
            ->active()
            ->available()
            ->where('category_id', $menuItem->category_id)
            ->where('id', '!=', $menuItem->id)
            ->take(4)
            ->get();

        // SEO data
        $seoData = $this->generateSeoData([
            'title' => $menuItem->name . ' - ' . ($siteSettings['general']['site_name'] ?? $restaurant->name ?? 'Restaurant'),
            'description' => $menuItem->description ?? 'Delicious menu item from our restaurant',
            'type' => 'product',
            'url' => request()->url(),
            'image' => $menuItem->primary_image_url,
        ]);

        return view('tenant.menu-item', compact(
            'restaurant',
            'menuItem',
            'relatedItems',
            'siteSettings',
            'seoData'
        ));
    }

    /**
     * Place an order
     */
    public function placeOrder(Request $request)
    {
        $request->validate([
            'items' => 'required|array|min:1',
            'items.*.menu_item_id' => 'required|exists:menu_items,id',
            'items.*.quantity' => 'required|integer|min:1',
            'items.*.special_instructions' => 'nullable|string',
            'order_type' => 'required|in:dine_in,takeaway,delivery',
            'table_id' => 'nullable|exists:tables,id',
            'customer_name' => 'required|string|max:255',
            'customer_phone' => 'required|string|max:20',
            'customer_email' => 'nullable|email',
            'delivery_address' => 'required_if:order_type,delivery|string',
            'special_instructions' => 'nullable|string',
        ]);

        $restaurant = Restaurant::first();
        
        // Calculate order totals
        $subtotal = 0;
        $orderItems = [];
        
        foreach ($request->items as $item) {
            $menuItem = MenuItem::find($item['menu_item_id']);
            $itemTotal = $menuItem->effective_price * $item['quantity'];
            $subtotal += $itemTotal;
            
            $orderItems[] = [
                'menu_item_id' => $menuItem->id,
                'item_name' => $menuItem->name,
                'item_price' => $menuItem->effective_price,
                'quantity' => $item['quantity'],
                'total_price' => $itemTotal,
                'special_instructions' => $item['special_instructions'] ?? null,
            ];
        }

        $taxAmount = $subtotal * ($restaurant->tax_rate / 100);
        $serviceCharge = $request->order_type === 'dine_in' ? $subtotal * ($restaurant->service_charge / 100) : 0;
        $deliveryCharge = $request->order_type === 'delivery' ? $restaurant->delivery_charge : 0;
        $totalAmount = $subtotal + $taxAmount + $serviceCharge + $deliveryCharge;

        // Create or find customer
        $customer = Customer::firstOrCreate(
            ['phone' => $request->customer_phone],
            [
                'restaurant_id' => $restaurant->id,
                'first_name' => explode(' ', $request->customer_name)[0],
                'last_name' => implode(' ', array_slice(explode(' ', $request->customer_name), 1)),
                'email' => $request->customer_email,
            ]
        );

        // Create order
        $order = Order::create([
            'restaurant_id' => $restaurant->id,
            'customer_id' => $customer->id,
            'table_id' => $request->table_id,
            'order_number' => 'ORD-' . strtoupper(Str::random(8)),
            'order_type' => $request->order_type,
            'status' => 'pending',
            'payment_status' => 'pending',
            'subtotal' => $subtotal,
            'tax_amount' => $taxAmount,
            'service_charge' => $serviceCharge,
            'delivery_charge' => $deliveryCharge,
            'total_amount' => $totalAmount,
            'customer_name' => $request->customer_name,
            'customer_phone' => $request->customer_phone,
            'customer_email' => $request->customer_email,
            'delivery_address' => $request->delivery_address,
            'special_instructions' => $request->special_instructions,
        ]);

        // Create order items
        foreach ($orderItems as $item) {
            $order->items()->create($item);
        }

        return response()->json([
            'success' => true,
            'order' => $order,
            'message' => 'Order placed successfully!',
        ]);
    }

    /**
     * Track an order
     */
    public function trackOrder(Order $order)
    {
        $siteSettings = $this->getSiteSettings();
        $order->load(['items.menuItem', 'restaurant']);

        return Inertia::render('Guest/TrackOrder', [
            'order' => $order,
            'siteSettings' => $siteSettings,
        ]);
    }

    /**
     * QR code ordering for tables
     */
    public function qrOrder(Table $table)
    {
        $restaurant = Restaurant::first();
        $siteSettings = $this->getSiteSettings();
        $categories = Category::active()->ordered()->with(['activeMenuItems' => function ($query) {
            $query->available()->ordered();
        }])->get();

        return Inertia::render('Guest/QROrder', [
            'restaurant' => $restaurant,
            'table' => $table,
            'categories' => $categories,
            'siteSettings' => $siteSettings,
        ]);
    }

    /**
     * Show a dynamic page
     */
    public function dynamicPage(Page $page)
    {
        // Only show published pages
        if (!$page->is_published || ($page->published_at && $page->published_at->isFuture())) {
            abort(404);
        }

        $restaurant = Restaurant::first();
        $siteSettings = $this->getSiteSettings();
        $page->load('bannerMedia');

        return Inertia::render('Guest/Page', [
            'restaurant' => $restaurant,
            'page' => $page,
            'siteSettings' => $siteSettings,
        ]);
    }

    /**
     * Generate SEO data for pages
     */
    private function generateSeoData(array $data): array
    {
        return [
            'title' => $data['title'] ?? 'Restaurant',
            'description' => $data['description'] ?? 'Delicious food, great atmosphere',
            'type' => $data['type'] ?? 'website',
            'url' => $data['url'] ?? request()->url(),
            'image' => $data['image'] ?? null,
        ];
    }

    /**
     * Generate Restaurant JSON-LD schema
     */
    private function generateRestaurantSchema($restaurant, $siteSettings): array
    {
        $schema = [
            '@context' => 'https://schema.org',
            '@type' => 'Restaurant',
            'name' => $siteSettings['general']['site_name'] ?? $restaurant->name ?? 'Restaurant',
            'description' => $siteSettings['general']['site_description'] ?? $restaurant->description ?? '',
            'url' => request()->getSchemeAndHttpHost(),
        ];

        // Add address if available
        if (isset($siteSettings['contact']['address'])) {
            $schema['address'] = [
                '@type' => 'PostalAddress',
                'streetAddress' => $siteSettings['contact']['address'],
                'addressLocality' => $siteSettings['contact']['city'] ?? '',
                'addressRegion' => $siteSettings['contact']['state'] ?? '',
                'postalCode' => $siteSettings['contact']['postal_code'] ?? '',
                'addressCountry' => 'BD'
            ];
        }

        // Add contact info
        if (isset($siteSettings['contact']['phone'])) {
            $schema['telephone'] = $siteSettings['contact']['phone'];
        }

        if (isset($siteSettings['contact']['email'])) {
            $schema['email'] = $siteSettings['contact']['email'];
        }

        // Add opening hours
        if (isset($restaurant->opening_time) && isset($restaurant->closing_time)) {
            $schema['openingHours'] = [
                'Mo-Su ' . $restaurant->opening_time . '-' . $restaurant->closing_time
            ];
        }

        // Add cuisine type
        $schema['servesCuisine'] = ['Bangladeshi', 'Asian'];

        // Add price range
        $schema['priceRange'] = '$$';

        return $schema;
    }

    /**
     * Switch language
     */
    public function switchLanguage(Request $request)
    {
        $locale = $request->input('locale');

        if (in_array($locale, ['en', 'bn'])) {
            session(['locale' => $locale]);
            app()->setLocale($locale);
        }

        return redirect()->back();
    }

    /**
     * Show checkout page
     */
    public function checkout()
    {
        $restaurant = Restaurant::first();
        $siteSettings = $this->getSiteSettings();

        // SEO data
        $seoData = $this->generateSeoData([
            'title' => 'Checkout - ' . ($siteSettings['general']['site_name'] ?? $restaurant->name ?? 'Restaurant'),
            'description' => 'Complete your order and enjoy our delicious food',
            'type' => 'website',
            'url' => request()->url(),
        ]);

        return view('tenant.checkout', compact(
            'restaurant',
            'siteSettings',
            'seoData'
        ));
    }

    /**
     * Generate Menu JSON-LD schema
     */
    private function generateMenuSchema($categories, $restaurant, $siteSettings): array
    {
        $menuItems = [];

        foreach ($categories as $category) {
            foreach ($category->activeMenuItems as $item) {
                $menuItems[] = [
                    '@type' => 'MenuItem',
                    'name' => $item->name,
                    'description' => $item->description ?? '',
                    'offers' => [
                        '@type' => 'Offer',
                        'price' => $item->price,
                        'priceCurrency' => 'BDT'
                    ],
                    'menuAddOn' => $category->name
                ];
            }
        }

        return [
            '@context' => 'https://schema.org',
            '@type' => 'Menu',
            'name' => 'Restaurant Menu',
            'description' => 'Our delicious menu items',
            'hasMenuSection' => array_map(function ($category) {
                return [
                    '@type' => 'MenuSection',
                    'name' => $category->name,
                    'description' => $category->description ?? '',
                    'hasMenuItem' => array_map(function ($item) {
                        return [
                            '@type' => 'MenuItem',
                            'name' => $item->name,
                            'description' => $item->description ?? '',
                            'offers' => [
                                '@type' => 'Offer',
                                'price' => $item->price,
                                'priceCurrency' => 'BDT'
                            ]
                        ];
                    }, $category->activeMenuItems->all())
                ];
            }, $categories->all())
        ];
    }
}
