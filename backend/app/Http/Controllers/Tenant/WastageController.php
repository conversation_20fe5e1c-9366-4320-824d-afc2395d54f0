<?php

namespace App\Http\Controllers\Tenant;

use App\Http\Controllers\Controller;
use App\Models\Tenant\WasteRecord;
use App\Models\Tenant\InventoryItem;
use App\Models\Tenant\Branch;
use App\Services\Tenant\MediaService;
use Illuminate\Http\Request;
use Inertia\Inertia;
use Illuminate\Support\Facades\DB;

class WastageController extends Controller
{
    protected MediaService $mediaService;

    public function __construct(MediaService $mediaService)
    {
        $this->mediaService = $mediaService;
        $this->middleware(['role:restaurant_manager|Waiter|Chef']);
    }

    /**
     * Display a listing of waste records
     */
    public function index(Request $request)
    {
        $query = WasteRecord::with(['branch', 'inventoryItem', 'reportedBy', 'approvedBy']);

        // Search functionality
        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->where('waste_number', 'like', "%{$search}%")
                  ->orWhereHas('inventoryItem', function ($iq) use ($search) {
                      $iq->where('name', 'like', "%{$search}%");
                  });
            });
        }

        // Filter by status
        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }

        // Filter by reason
        if ($request->filled('reason')) {
            $query->where('reason', $request->reason);
        }

        // Filter by severity
        if ($request->filled('severity')) {
            $query->where('severity', $request->severity);
        }

        // Filter by branch
        if ($request->filled('branch_id')) {
            $query->where('branch_id', $request->branch_id);
        }

        // Filter by date range
        if ($request->filled('start_date')) {
            $query->whereDate('waste_date', '>=', $request->start_date);
        }

        if ($request->filled('end_date')) {
            $query->whereDate('waste_date', '<=', $request->end_date);
        }

        // Sort
        $sortField = $request->get('sort', 'waste_date');
        $sortDirection = $request->get('direction', 'desc');
        $query->orderBy($sortField, $sortDirection);

        $wasteRecords = $query->paginate(20)->withQueryString();

        // Get filter options
        $branches = Branch::active()->ordered()->get(['id', 'name']);
        $inventoryItems = InventoryItem::active()->orderBy('name')->get(['id', 'name']);

        // Get summary statistics
        $stats = [
            'total_waste_cost' => WasteRecord::where('status', 'approved')->sum('total_cost_impact'),
            'pending_waste_cost' => WasteRecord::where('status', 'pending')->sum('total_cost_impact'),
            'monthly_waste_cost' => WasteRecord::where('status', 'approved')
                                              ->whereMonth('waste_date', now()->month)
                                              ->whereYear('waste_date', now()->year)
                                              ->sum('total_cost_impact'),
            'waste_incidents' => WasteRecord::count(),
            'pending_approvals' => WasteRecord::where('status', 'pending')->where('requires_approval', true)->count(),
        ];

        return Inertia::render('Tenant/Accounting/Wastage/Index', [
            'wasteRecords' => $wasteRecords,
            'filters' => $request->only(['search', 'status', 'reason', 'severity', 'branch_id', 'start_date', 'end_date', 'sort', 'direction']),
            'branches' => $branches,
            'inventoryItems' => $inventoryItems,
            'stats' => $stats,
            'reasons' => [
                'expired' => 'Expired',
                'damaged' => 'Damaged',
                'spillage' => 'Spillage',
                'contaminated' => 'Contaminated',
                'overproduction' => 'Overproduction',
                'other' => 'Other',
            ],
            'severities' => ['low', 'medium', 'high', 'critical'],
            'statuses' => ['pending', 'approved', 'rejected'],
        ]);
    }

    /**
     * Show the form for creating a new waste record
     */
    public function create()
    {
        $branches = Branch::active()->ordered()->get(['id', 'name']);
        $inventoryItems = InventoryItem::active()->with(['category'])->orderBy('name')->get();

        return Inertia::render('Tenant/Accounting/Wastage/Create', [
            'branches' => $branches,
            'inventoryItems' => $inventoryItems,
            'reasons' => [
                'expired' => 'Expired',
                'damaged' => 'Damaged',
                'spillage' => 'Spillage',
                'contaminated' => 'Contaminated',
                'overproduction' => 'Overproduction',
                'other' => 'Other',
            ],
            'severities' => ['low', 'medium', 'high', 'critical'],
        ]);
    }

    /**
     * Store a newly created waste record
     */
    public function store(Request $request)
    {
        $request->validate([
            'branch_id' => 'required|exists:branches,id',
            'inventory_item_id' => 'required|exists:inventory_items,id',
            'quantity_wasted' => 'required|numeric|min:0.01',
            'unit_cost' => 'required|numeric|min:0',
            'reason' => 'required|in:expired,damaged,spillage,contaminated,overproduction,other',
            'detailed_reason' => 'nullable|string|max:1000',
            'waste_date' => 'required|date',
            'severity' => 'required|in:low,medium,high,critical',
            'notes' => 'nullable|string|max:1000',
            'photo_evidence' => 'nullable|string', // Media ID
        ]);

        try {
            return DB::transaction(function () use ($request) {
                // Handle photo evidence
                $photoEvidence = null;
                if ($request->photo_evidence) {
                    $media = $this->mediaService->getMediaById($request->photo_evidence);
                    if ($media) {
                        $photoEvidence = $media->file_path;
                    }
                }

                // Calculate total cost impact
                $totalCostImpact = $request->quantity_wasted * $request->unit_cost;

                // Determine if approval is required (based on cost threshold)
                $requiresApproval = $totalCostImpact > 100.00 || $request->severity === 'critical';

                // Create waste record
                $wasteRecord = WasteRecord::create([
                    'branch_id' => $request->branch_id,
                    'inventory_item_id' => $request->inventory_item_id,
                    'reported_by' => auth()->id(),
                    'waste_number' => $this->generateWasteNumber(),
                    'quantity_wasted' => $request->quantity_wasted,
                    'unit_of_measurement' => $this->getItemUnit($request->inventory_item_id),
                    'unit_cost' => $request->unit_cost,
                    'total_cost_impact' => $totalCostImpact,
                    'reason' => $request->reason,
                    'detailed_reason' => $request->detailed_reason,
                    'waste_date' => $request->waste_date,
                    'severity' => $request->severity,
                    'notes' => $request->notes,
                    'photo_evidence' => $photoEvidence,
                    'requires_approval' => $requiresApproval,
                    'status' => $requiresApproval ? 'pending' : 'approved',
                ]);

                // Auto-approve if no approval required
                if (!$requiresApproval) {
                    $wasteRecord->update([
                        'approved_by' => auth()->id(),
                        'approved_at' => now(),
                    ]);

                    // Update inventory levels
                    $this->updateInventoryLevels($wasteRecord);
                }

                return redirect()->route('wastage.show', $wasteRecord)
                    ->with('success', 'Waste record created successfully.');
            });
        } catch (\Exception $e) {
            return back()->withErrors(['error' => 'Failed to create waste record: ' . $e->getMessage()]);
        }
    }

    /**
     * Display the specified waste record
     */
    public function show(WasteRecord $wasteRecord)
    {
        $wasteRecord->load(['branch', 'inventoryItem.category', 'reportedBy', 'approvedBy']);

        return Inertia::render('Tenant/Accounting/Wastage/Show', [
            'wasteRecord' => $wasteRecord,
        ]);
    }

    /**
     * Approve waste record
     */
    public function approve(Request $request, WasteRecord $wasteRecord)
    {
        if ($wasteRecord->status !== 'pending') {
            return back()->withErrors(['error' => 'Only pending waste records can be approved.']);
        }

        $request->validate([
            'notes' => 'nullable|string|max:1000',
        ]);

        try {
            return DB::transaction(function () use ($request, $wasteRecord) {
                $wasteRecord->update([
                    'status' => 'approved',
                    'approved_by' => auth()->id(),
                    'approved_at' => now(),
                    'notes' => $request->notes ? ($wasteRecord->notes . "\n\nApproval Notes: " . $request->notes) : $wasteRecord->notes,
                ]);

                // Update inventory levels
                $this->updateInventoryLevels($wasteRecord);

                return back()->with('success', 'Waste record approved successfully.');
            });
        } catch (\Exception $e) {
            return back()->withErrors(['error' => 'Failed to approve waste record: ' . $e->getMessage()]);
        }
    }

    /**
     * Reject waste record
     */
    public function reject(Request $request, WasteRecord $wasteRecord)
    {
        if ($wasteRecord->status !== 'pending') {
            return back()->withErrors(['error' => 'Only pending waste records can be rejected.']);
        }

        $request->validate([
            'reason' => 'required|string|max:1000',
        ]);

        $wasteRecord->update([
            'status' => 'rejected',
            'approved_by' => auth()->id(),
            'approved_at' => now(),
            'notes' => $wasteRecord->notes . "\n\nRejection Reason: " . $request->reason,
        ]);

        return back()->with('success', 'Waste record rejected successfully.');
    }

    /**
     * Generate unique waste number
     */
    private function generateWasteNumber(): string
    {
        do {
            $number = 'WST-' . now()->format('Ymd') . '-' . str_pad(random_int(1, 9999), 4, '0', STR_PAD_LEFT);
        } while (WasteRecord::where('waste_number', $number)->exists());

        return $number;
    }

    /**
     * Get inventory item unit
     */
    private function getItemUnit(int $inventoryItemId): string
    {
        $item = InventoryItem::find($inventoryItemId);
        return $item ? $item->unit_of_measurement : 'pcs';
    }

    /**
     * Update inventory levels after waste approval
     */
    private function updateInventoryLevels(WasteRecord $wasteRecord): void
    {
        $inventoryItem = $wasteRecord->inventoryItem;
        if ($inventoryItem) {
            // Reduce current stock
            $inventoryItem->decrement('current_stock', $wasteRecord->quantity_wasted);
            
            // Update last updated timestamp
            $inventoryItem->touch();
        }
    }

    /**
     * Get wastage analytics
     */
    public function analytics(Request $request)
    {
        $period = $request->get('period', 'month'); // week, month, quarter, year

        $startDate = match ($period) {
            'week' => now()->startOfWeek(),
            'month' => now()->startOfMonth(),
            'quarter' => now()->startOfQuarter(),
            'year' => now()->startOfYear(),
            default => now()->startOfMonth(),
        };

        $endDate = match ($period) {
            'week' => now()->endOfWeek(),
            'month' => now()->endOfMonth(),
            'quarter' => now()->endOfQuarter(),
            'year' => now()->endOfYear(),
            default => now()->endOfMonth(),
        };

        // Get wastage by reason
        $wasteByReason = WasteRecord::where('status', 'approved')
            ->whereBetween('waste_date', [$startDate, $endDate])
            ->selectRaw('reason, SUM(total_cost_impact) as total_cost, COUNT(*) as incident_count')
            ->groupBy('reason')
            ->get();

        // Get wastage by item
        $wasteByItem = WasteRecord::where('status', 'approved')
            ->whereBetween('waste_date', [$startDate, $endDate])
            ->with('inventoryItem')
            ->selectRaw('inventory_item_id, SUM(total_cost_impact) as total_cost, SUM(quantity_wasted) as total_quantity')
            ->groupBy('inventory_item_id')
            ->orderByDesc('total_cost')
            ->limit(10)
            ->get();

        // Get daily wastage trend
        $dailyWastage = WasteRecord::where('status', 'approved')
            ->whereBetween('waste_date', [$startDate, $endDate])
            ->selectRaw('DATE(waste_date) as date, SUM(total_cost_impact) as total_cost')
            ->groupBy('date')
            ->orderBy('date')
            ->get();

        return Inertia::render('Tenant/Accounting/Wastage/Analytics', [
            'wasteByReason' => $wasteByReason,
            'wasteByItem' => $wasteByItem,
            'dailyWastage' => $dailyWastage,
            'period' => $period,
            'startDate' => $startDate->format('Y-m-d'),
            'endDate' => $endDate->format('Y-m-d'),
        ]);
    }
}
