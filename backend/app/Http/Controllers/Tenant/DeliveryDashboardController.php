<?php

namespace App\Http\Controllers\Tenant;

use App\Http\Controllers\Controller;
use App\Models\Tenant\DeliveryOrder;
use App\Models\Tenant\DeliveryDriver;
use App\Models\Tenant\Order;
use Illuminate\Http\Request;
use Inertia\Inertia;

class DeliveryDashboardController extends Controller
{
    /**
     * Display the delivery dashboard
     */
    public function index(Request $request)
    {
        $driver = auth()->user();
        $deliveryDriver = DeliveryDriver::where('employee_id', $driver->id)->first();
        
        if (!$deliveryDriver) {
            return redirect()->route('dashboard')
                ->with('error', 'You are not registered as a delivery driver.');
        }
        
        // Get assigned delivery orders
        $assignedDeliveries = DeliveryOrder::where('delivery_driver_id', $deliveryDriver->id)
            ->whereIn('status', ['assigned', 'picked_up', 'on_the_way'])
            ->with(['order.customer', 'order.items.food', 'deliveryZone'])
            ->orderBy('assigned_at')
            ->get();
        
        // Get available delivery orders
        $availableDeliveries = DeliveryOrder::where('status', 'pending')
            ->whereNull('delivery_driver_id')
            ->with(['order.customer', 'order.items.food', 'deliveryZone'])
            ->orderBy('created_at')
            ->get();
        
        // Get completed deliveries for today
        $todayCompletedDeliveries = DeliveryOrder::where('delivery_driver_id', $deliveryDriver->id)
            ->where('status', 'delivered')
            ->whereDate('delivered_at', today())
            ->with(['order.customer'])
            ->get();
        
        // Get driver performance metrics
        $todayMetrics = $this->getTodayDriverMetrics($deliveryDriver);
        
        // Get earnings for today
        $todayEarnings = $this->getTodayEarnings($deliveryDriver);

        return Inertia::render('Delivery/Dashboard', [
            'deliveryDriver' => $deliveryDriver,
            'assignedDeliveries' => $assignedDeliveries,
            'availableDeliveries' => $availableDeliveries,
            'todayCompletedDeliveries' => $todayCompletedDeliveries,
            'todayMetrics' => $todayMetrics,
            'todayEarnings' => $todayEarnings,
            'driver' => $driver,
        ]);
    }

    /**
     * Get today's driver metrics
     */
    private function getTodayDriverMetrics($deliveryDriver): array
    {
        $today = today();
        
        $deliveriesCompleted = DeliveryOrder::where('delivery_driver_id', $deliveryDriver->id)
            ->where('status', 'delivered')
            ->whereDate('delivered_at', $today)
            ->count();
        
        $averageDeliveryTime = DeliveryOrder::where('delivery_driver_id', $deliveryDriver->id)
            ->where('status', 'delivered')
            ->whereDate('delivered_at', $today)
            ->whereNotNull('actual_delivery_time')
            ->avg('actual_delivery_time');
        
        $totalDistance = DeliveryOrder::where('delivery_driver_id', $deliveryDriver->id)
            ->where('status', 'delivered')
            ->whereDate('delivered_at', $today)
            ->sum('distance_km');
        
        $onTimeDeliveries = DeliveryOrder::where('delivery_driver_id', $deliveryDriver->id)
            ->where('status', 'delivered')
            ->whereDate('delivered_at', $today)
            ->whereRaw('actual_delivery_time <= estimated_delivery_time')
            ->count();
        
        $onTimePercentage = $deliveriesCompleted > 0 ? ($onTimeDeliveries / $deliveriesCompleted) * 100 : 0;

        return [
            'deliveries_completed' => $deliveriesCompleted,
            'average_delivery_time' => round($averageDeliveryTime ?? 0, 1),
            'total_distance' => round($totalDistance, 2),
            'on_time_percentage' => round($onTimePercentage, 1),
        ];
    }

    /**
     * Get today's earnings
     */
    private function getTodayEarnings($deliveryDriver): array
    {
        $today = today();
        
        $deliveryCommissions = DeliveryOrder::where('delivery_driver_id', $deliveryDriver->id)
            ->where('status', 'delivered')
            ->whereDate('delivered_at', $today)
            ->sum('driver_commission');
        
        $tips = Order::whereHas('deliveryOrder', function ($query) use ($deliveryDriver, $today) {
            $query->where('delivery_driver_id', $deliveryDriver->id)
                ->where('status', 'delivered')
                ->whereDate('delivered_at', $today);
        })->sum('tip_amount');

        return [
            'delivery_commissions' => $deliveryCommissions,
            'tips' => $tips,
            'total_earnings' => $deliveryCommissions + $tips,
        ];
    }

    /**
     * Accept a delivery order
     */
    public function acceptDelivery(Request $request, DeliveryOrder $deliveryOrder)
    {
        $driver = auth()->user();
        $deliveryDriver = DeliveryDriver::where('employee_id', $driver->id)->first();
        
        if (!$deliveryDriver || !$deliveryDriver->isAvailable()) {
            return response()->json([
                'message' => 'You are not available to accept deliveries'
            ], 422);
        }
        
        if ($deliveryOrder->status !== 'pending') {
            return response()->json([
                'message' => 'This delivery is no longer available'
            ], 422);
        }
        
        $deliveryOrder->assignToDriver($deliveryDriver);

        return response()->json([
            'message' => 'Delivery accepted successfully',
            'deliveryOrder' => $deliveryOrder->fresh(['order.customer', 'deliveryZone'])
        ]);
    }

    /**
     * Mark delivery as picked up
     */
    public function markPickedUp(Request $request, DeliveryOrder $deliveryOrder)
    {
        $driver = auth()->user();
        $deliveryDriver = DeliveryDriver::where('employee_id', $driver->id)->first();
        
        if ($deliveryOrder->delivery_driver_id !== $deliveryDriver->id) {
            return response()->json([
                'message' => 'You are not assigned to this delivery'
            ], 403);
        }
        
        $deliveryOrder->markPickedUp();

        return response()->json([
            'message' => 'Order marked as picked up',
            'deliveryOrder' => $deliveryOrder->fresh(['order.customer'])
        ]);
    }

    /**
     * Mark delivery as completed
     */
    public function markDelivered(Request $request, DeliveryOrder $deliveryOrder)
    {
        $request->validate([
            'delivery_notes' => 'nullable|string|max:500',
            'delivery_photo' => 'nullable|image|max:2048',
        ]);

        $driver = auth()->user();
        $deliveryDriver = DeliveryDriver::where('employee_id', $driver->id)->first();
        
        if ($deliveryOrder->delivery_driver_id !== $deliveryDriver->id) {
            return response()->json([
                'message' => 'You are not assigned to this delivery'
            ], 403);
        }
        
        $photoPath = null;
        if ($request->hasFile('delivery_photo')) {
            $photoPath = $request->file('delivery_photo')->store('delivery-photos', 'public');
        }
        
        $deliveryOrder->markDelivered($request->delivery_notes, $photoPath);

        return response()->json([
            'message' => 'Delivery completed successfully',
            'deliveryOrder' => $deliveryOrder->fresh(['order.customer'])
        ]);
    }

    /**
     * Mark delivery as failed
     */
    public function markFailed(Request $request, DeliveryOrder $deliveryOrder)
    {
        $request->validate([
            'failure_reason' => 'required|string|max:500',
        ]);

        $driver = auth()->user();
        $deliveryDriver = DeliveryDriver::where('employee_id', $driver->id)->first();
        
        if ($deliveryOrder->delivery_driver_id !== $deliveryDriver->id) {
            return response()->json([
                'message' => 'You are not assigned to this delivery'
            ], 403);
        }
        
        $deliveryOrder->markFailed($request->failure_reason);

        return response()->json([
            'message' => 'Delivery marked as failed',
            'deliveryOrder' => $deliveryOrder->fresh(['order.customer'])
        ]);
    }

    /**
     * Update driver location
     */
    public function updateLocation(Request $request)
    {
        $request->validate([
            'latitude' => 'required|numeric|between:-90,90',
            'longitude' => 'required|numeric|between:-180,180',
        ]);

        $driver = auth()->user();
        $deliveryDriver = DeliveryDriver::where('employee_id', $driver->id)->first();
        
        if (!$deliveryDriver) {
            return response()->json([
                'message' => 'Driver profile not found'
            ], 404);
        }
        
        $deliveryDriver->updateLocation($request->latitude, $request->longitude);

        return response()->json([
            'message' => 'Location updated successfully'
        ]);
    }

    /**
     * Get delivery history
     */
    public function deliveryHistory(Request $request)
    {
        $driver = auth()->user();
        $deliveryDriver = DeliveryDriver::where('employee_id', $driver->id)->first();
        
        $query = DeliveryOrder::where('delivery_driver_id', $deliveryDriver->id)
            ->with(['order.customer', 'deliveryZone']);
        
        // Filter by status
        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }
        
        // Filter by date range
        if ($request->filled('date_from')) {
            $query->whereDate('created_at', '>=', $request->date_from);
        }
        
        if ($request->filled('date_to')) {
            $query->whereDate('created_at', '<=', $request->date_to);
        }
        
        $deliveries = $query->orderBy('created_at', 'desc')
            ->paginate(20);

        return Inertia::render('Delivery/History', [
            'deliveries' => $deliveries,
            'filters' => $request->only(['status', 'date_from', 'date_to']),
            'deliveryDriver' => $deliveryDriver,
        ]);
    }

    /**
     * Get earnings report
     */
    public function earnings(Request $request)
    {
        $driver = auth()->user();
        $deliveryDriver = DeliveryDriver::where('employee_id', $driver->id)->first();
        
        $period = $request->get('period', 'week'); // week, month, year
        
        switch ($period) {
            case 'week':
                $startDate = now()->startOfWeek();
                $endDate = now()->endOfWeek();
                break;
            case 'month':
                $startDate = now()->startOfMonth();
                $endDate = now()->endOfMonth();
                break;
            case 'year':
                $startDate = now()->startOfYear();
                $endDate = now()->endOfYear();
                break;
            default:
                $startDate = now()->startOfWeek();
                $endDate = now()->endOfWeek();
        }
        
        $earnings = $this->getEarningsData($deliveryDriver, $startDate, $endDate);

        return Inertia::render('Delivery/Earnings', [
            'earnings' => $earnings,
            'period' => $period,
            'deliveryDriver' => $deliveryDriver,
        ]);
    }

    /**
     * Get earnings data for a period
     */
    private function getEarningsData($deliveryDriver, $startDate, $endDate): array
    {
        $deliveries = DeliveryOrder::where('delivery_driver_id', $deliveryDriver->id)
            ->where('status', 'delivered')
            ->whereBetween('delivered_at', [$startDate, $endDate])
            ->with('order')
            ->get();
        
        $totalCommissions = $deliveries->sum('driver_commission');
        $totalTips = $deliveries->sum('order.tip_amount');
        $totalDeliveries = $deliveries->count();
        $totalDistance = $deliveries->sum('distance_km');
        
        return [
            'total_commissions' => $totalCommissions,
            'total_tips' => $totalTips,
            'total_earnings' => $totalCommissions + $totalTips,
            'total_deliveries' => $totalDeliveries,
            'total_distance' => $totalDistance,
            'average_per_delivery' => $totalDeliveries > 0 ? ($totalCommissions + $totalTips) / $totalDeliveries : 0,
            'deliveries' => $deliveries,
        ];
    }
}
