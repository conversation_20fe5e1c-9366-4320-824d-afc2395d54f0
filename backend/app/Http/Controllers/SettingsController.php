<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Hash;
use Illuminate\Validation\Rules\Password;
use Inertia\Inertia;
use App\Services\NotificationService;

class SettingsController extends Controller
{
    protected $notificationService;

    public function __construct(NotificationService $notificationService)
    {
        $this->notificationService = $notificationService;
    }

    /**
     * Display user settings page
     */
    public function index()
    {
        $user = auth()->user();
        
        return Inertia::render('Settings/Index', [
            'user' => $user->load('roles'),
            'notificationPreferences' => $this->notificationService->getNotificationPreferences($user),
            'availableLanguages' => $this->getAvailableLanguages(),
            'availableThemes' => $this->getAvailableThemes(),
        ]);
    }

    /**
     * Update user profile
     */
    public function updateProfile(Request $request)
    {
        $user = auth()->user();
        
        $request->validate([
            'name' => 'required|string|max:255',
            'email' => 'required|string|email|max:255|unique:users,email,' . $user->id,
            'phone' => 'nullable|string|max:20',
        ]);

        $user->update($request->only(['name', 'email', 'phone']));

        return response()->json([
            'success' => true,
            'message' => __('Profile updated successfully'),
            'user' => $user->fresh()
        ]);
    }

    /**
     * Update user password
     */
    public function updatePassword(Request $request)
    {
        $user = auth()->user();
        
        $request->validate([
            'current_password' => 'required|current_password',
            'password' => ['required', 'confirmed', Password::defaults()],
        ]);

        $user->update([
            'password' => Hash::make($request->password)
        ]);

        return response()->json([
            'success' => true,
            'message' => __('Password updated successfully')
        ]);
    }

    /**
     * Update user preferences
     */
    public function updatePreferences(Request $request)
    {
        $user = auth()->user();
        
        $request->validate([
            'preferred_language' => 'required|string|in:en,bn',
            'theme_preference' => 'required|string|in:light,dark,auto',
            'timezone' => 'nullable|string|max:50',
        ]);

        $user->update($request->only([
            'preferred_language',
            'theme_preference',
            'timezone'
        ]));

        // Update app locale if language changed
        if ($request->preferred_language !== app()->getLocale()) {
            app()->setLocale($request->preferred_language);
            session(['locale' => $request->preferred_language]);
        }

        return response()->json([
            'success' => true,
            'message' => __('Preferences updated successfully'),
            'user' => $user->fresh()
        ]);
    }

    /**
     * Update notification preferences
     */
    public function updateNotificationPreferences(Request $request)
    {
        $user = auth()->user();
        
        $request->validate([
            'email_notifications' => 'boolean',
            'sms_notifications' => 'boolean',
            'push_notifications' => 'boolean',
            'order_notifications' => 'boolean',
            'kitchen_notifications' => 'boolean',
            'delivery_notifications' => 'boolean',
            'system_notifications' => 'boolean',
        ]);

        $this->notificationService->updateNotificationPreferences($user, $request->all());

        return response()->json([
            'success' => true,
            'message' => __('Notification preferences updated successfully')
        ]);
    }

    /**
     * Get user activity log
     */
    public function getActivityLog(Request $request)
    {
        $user = auth()->user();
        $limit = $request->get('limit', 20);
        
        // Mock activity log - implement with actual activity tracking
        $activities = collect([
            [
                'id' => 1,
                'action' => 'login',
                'description' => 'User logged in',
                'ip_address' => $request->ip(),
                'user_agent' => $request->userAgent(),
                'created_at' => now()->subHours(2),
            ],
            [
                'id' => 2,
                'action' => 'profile_update',
                'description' => 'Profile information updated',
                'ip_address' => $request->ip(),
                'user_agent' => $request->userAgent(),
                'created_at' => now()->subDays(1),
            ],
        ])->take($limit);

        return response()->json([
            'activities' => $activities,
            'total' => $activities->count()
        ]);
    }

    /**
     * Export user data
     */
    public function exportData(Request $request)
    {
        $user = auth()->user();
        
        $userData = [
            'profile' => $user->only(['name', 'email', 'phone', 'created_at']),
            'preferences' => [
                'language' => $user->preferred_language,
                'theme' => $user->theme_preference,
                'timezone' => $user->timezone,
            ],
            'roles' => $user->roles->pluck('name'),
            'permissions' => $user->getAllPermissions()->pluck('name'),
            'notification_preferences' => $this->notificationService->getNotificationPreferences($user),
        ];

        return response()->json([
            'success' => true,
            'data' => $userData,
            'exported_at' => now()->toISOString()
        ]);
    }

    /**
     * Delete user account (soft delete)
     */
    public function deleteAccount(Request $request)
    {
        $user = auth()->user();
        
        $request->validate([
            'password' => 'required|current_password',
            'confirmation' => 'required|in:DELETE',
        ]);

        // Soft delete the user
        $user->update(['is_active' => false]);
        
        // Log out the user
        auth()->logout();
        
        return response()->json([
            'success' => true,
            'message' => __('Account deactivated successfully')
        ]);
    }

    /**
     * Update user avatar
     */
    public function updateAvatar(Request $request)
    {
        $user = auth()->user();
        
        $request->validate([
            'avatar' => 'required|image|mimes:jpeg,png,jpg,gif|max:2048',
        ]);

        if ($request->hasFile('avatar')) {
            // Delete old avatar if exists
            if ($user->profile_photo_path) {
                \Storage::disk('public')->delete($user->profile_photo_path);
            }

            // Store new avatar
            $path = $request->file('avatar')->store('avatars', 'public');
            
            $user->update(['profile_photo_path' => $path]);
        }

        return response()->json([
            'success' => true,
            'message' => __('Avatar updated successfully'),
            'avatar_url' => $user->profile_photo_url
        ]);
    }

    /**
     * Get security settings
     */
    public function getSecuritySettings()
    {
        $user = auth()->user();
        
        return response()->json([
            'two_factor_enabled' => !is_null($user->two_factor_secret),
            'recovery_codes_generated' => !is_null($user->two_factor_recovery_codes),
            'last_password_change' => $user->password_changed_at ?? $user->created_at,
            'active_sessions' => $this->getActiveSessions(),
        ]);
    }

    /**
     * Enable/disable two-factor authentication
     */
    public function toggleTwoFactor(Request $request)
    {
        $user = auth()->user();
        
        if ($user->two_factor_secret) {
            // Disable 2FA
            $user->forceFill([
                'two_factor_secret' => null,
                'two_factor_recovery_codes' => null,
            ])->save();
            
            $message = __('Two-factor authentication disabled');
        } else {
            // Enable 2FA
            $user->forceFill([
                'two_factor_secret' => encrypt(\Laravel\Fortify\TwoFactorAuthenticatable::generateSecretKey()),
                'two_factor_recovery_codes' => encrypt(json_encode(\Laravel\Fortify\RecoveryCode::generate())),
            ])->save();
            
            $message = __('Two-factor authentication enabled');
        }

        return response()->json([
            'success' => true,
            'message' => $message,
            'two_factor_enabled' => !is_null($user->fresh()->two_factor_secret)
        ]);
    }

    /**
     * Get available languages
     */
    private function getAvailableLanguages()
    {
        return [
            'en' => [
                'code' => 'en',
                'name' => 'English',
                'native_name' => 'English',
                'flag' => '🇺🇸',
            ],
            'bn' => [
                'code' => 'bn',
                'name' => 'Bengali',
                'native_name' => 'বাংলা',
                'flag' => '🇧🇩',
            ],
        ];
    }

    /**
     * Get available themes
     */
    private function getAvailableThemes()
    {
        return [
            'light' => [
                'name' => __('Light Mode'),
                'description' => __('Clean and bright interface'),
            ],
            'dark' => [
                'name' => __('Dark Mode'),
                'description' => __('Easy on the eyes in low light'),
            ],
            'auto' => [
                'name' => __('Auto'),
                'description' => __('Follows system preference'),
            ],
        ];
    }

    /**
     * Get active sessions (mock implementation)
     */
    private function getActiveSessions()
    {
        return [
            [
                'id' => session()->getId(),
                'ip_address' => request()->ip(),
                'user_agent' => request()->userAgent(),
                'last_activity' => now(),
                'is_current' => true,
            ]
        ];
    }
}
