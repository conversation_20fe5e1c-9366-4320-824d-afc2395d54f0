<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\App;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Session;

class LanguageController extends Controller
{
    /**
     * Switch the application language
     */
    public function switch(Request $request)
    {
        $request->validate([
            'locale' => 'required|string|in:en,bn'
        ]);

        $locale = $request->get('locale');
        
        // Set the application locale
        App::setLocale($locale);
        
        // Store in session
        Session::put('locale', $locale);
        
        // Update user preference if authenticated
        if (Auth::check()) {
            Auth::user()->update(['preferred_language' => $locale]);
        }

        return response()->json([
            'success' => true,
            'locale' => $locale,
            'message' => __('Language changed successfully')
        ]);
    }

    /**
     * Get current locale and available locales
     */
    public function current()
    {
        return response()->json([
            'current_locale' => App::getLocale(),
            'available_locales' => $this->getAvailableLocales(),
            'user_preference' => Auth::check() ? Auth::user()->preferred_language : null,
        ]);
    }

    /**
     * Get available locales with their display names
     */
    private function getAvailableLocales(): array
    {
        return [
            'en' => [
                'code' => 'en',
                'name' => 'English',
                'native_name' => 'English',
                'flag' => '🇺🇸',
            ],
            'bn' => [
                'code' => 'bn',
                'name' => 'Bengali',
                'native_name' => 'বাংলা',
                'flag' => '🇧🇩',
            ],
        ];
    }

    /**
     * Update user language preference
     */
    public function updateLanguage(Request $request)
    {
        $request->validate([
            'language' => 'required|string|in:en,bn'
        ]);

        $locale = $request->get('language');

        // Set the application locale
        App::setLocale($locale);

        // Store in session
        Session::put('locale', $locale);

        // Update user preference if authenticated
        if (Auth::check()) {
            Auth::user()->update(['preferred_language' => $locale]);
        }

        // Return back to the current page with success message
        // This is compatible with Inertia.js and preserves state/scroll
        return back()->with([
            'success' => __('Language updated successfully'),
            'locale' => $locale,
        ]);
    }

    /**
     * Get translations for JavaScript
     */
    public function translations(Request $request)
    {
        $locale = $request->get('locale', App::getLocale());

        // Load the JSON translation file
        $translationFile = resource_path("lang/{$locale}.json");

        if (!file_exists($translationFile)) {
            $translationFile = resource_path("lang/en.json");
        }

        $translations = json_decode(file_get_contents($translationFile), true) ?? [];

        return response()->json([
            'locale' => $locale,
            'translations' => $translations,
        ]);
    }
}
