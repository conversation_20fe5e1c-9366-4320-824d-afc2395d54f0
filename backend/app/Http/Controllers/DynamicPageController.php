<?php

namespace App\Http\Controllers;

use App\Models\DynamicPage;
use Illuminate\Http\Request;
use Illuminate\Support\Str;

class DynamicPageController extends Controller
{
    /**
     * Display the specified dynamic page
     */
    public function show(Request $request, $slug)
    {
        $page = DynamicPage::where('slug', $slug)
            ->where('status', 'active')
            ->with(['bannerImage'])
            ->firstOrFail();

        // SEO data for this specific page
        $seoData = [
            'title' => $page->title . ' - ' . config('app.name'),
            'description' => $page->meta_description ?: Str::limit(strip_tags($page->content), 160),
            'canonical' => url('/page/' . $page->slug),
            'og_type' => 'website',
            'og_image' => $page->banner_image_url,
        ];

        // JSON-LD structured data
        $jsonLd = [
            '@context' => 'https://schema.org',
            '@type' => 'WebPage',
            'name' => $page->title,
            'description' => $page->meta_description ?: Str::limit(strip_tags($page->content), 160),
            'url' => url('/page/' . $page->slug),
            'mainEntity' => [
                '@type' => 'Article',
                'headline' => $page->title,
                'description' => $page->meta_description ?: Str::limit(strip_tags($page->content), 160),
                'datePublished' => $page->created_at?->toISOString(),
                'dateModified' => $page->updated_at?->toISOString(),
            ],
        ];

        // Breadcrumb JSON-LD
        $breadcrumbJsonLd = [
            '@context' => 'https://schema.org',
            '@type' => 'BreadcrumbList',
            'itemListElement' => [
                [
                    '@type' => 'ListItem',
                    'position' => 1,
                    'name' => 'Home',
                    'item' => url('/'),
                ],
                [
                    '@type' => 'ListItem',
                    'position' => 2,
                    'name' => $page->title,
                    'item' => url('/page/' . $page->slug),
                ],
            ],
        ];

        return view('pages.show', compact('page', 'seoData', 'jsonLd', 'breadcrumbJsonLd'));
    }
}
