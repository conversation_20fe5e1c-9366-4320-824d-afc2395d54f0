<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Inertia\Inertia;
use App\Models\Tenant;
use App\Models\SubscriptionPlan;
use App\Jobs\Tenant\TenantCreationChainJob;
use App\Jobs\Tenant\CreateTenantDatabaseJob;
use App\Jobs\Tenant\SeedTenantDataJob;
use App\Jobs\Tenant\CreateTenantManagerJob;
use App\Jobs\Tenant\SetupFileSystemJob;
use App\Jobs\Tenant\SetupThirdPartyServicesJob;
use App\Jobs\Tenant\SendWelcomeEmailJob;
use App\Jobs\Tenant\FinalizeTenantSetupJob;
use App\Jobs\Tenant\NotifyTenantCreationFailedJob;
use App\Jobs\Tenant\SimpleTenantSetupJob;
use Illuminate\Support\Facades\Bus;
use Illuminate\Support\Str;
use Illuminate\Support\Facades\Artisan;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\DB;
use Exception;

class TenantController extends Controller
{
    /**
     * Display a listing of tenants
     */
    public function index(Request $request)
    {
        $query = Tenant::with(['subscriptionPlan', 'domains']);

        // Search
        if ($request->search) {
            $query->where(function ($q) use ($request) {
                $q->where('name', 'like', '%' . $request->search . '%')
                  ->orWhere('email', 'like', '%' . $request->search . '%');
            });
        }

        // Filter by status
        if ($request->status) {
            $query->where('subscription_status', $request->status);
        }

        // Filter by plan
        if ($request->plan) {
            $query->where('subscription_plan_id', $request->plan);
        }

        $tenants = $query->latest()->paginate(15);

        return Inertia::render('Admin/Tenants/Index', [
            'tenants' => $tenants,
            'plans' => SubscriptionPlan::all(),
            'filters' => $request->only(['search', 'status', 'plan']),
        ]);
    }

    /**
     * Show the form for creating a new tenant
     */
    public function create()
    {
        return Inertia::render('Admin/Tenants/Create', [
            'plans' => SubscriptionPlan::active()->get(),
        ]);
    }

    /**
     * Store a newly created tenant
     */
    public function store(Request $request)
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'email' => 'required|email|unique:tenants,email',
            'manager_password' => 'required|string|min:8',
            'phone' => 'nullable|string|max:20',
            'subdomain' => 'required|string|max:50|alpha_dash',
            'subscription_plan_id' => 'required|exists:subscription_plans,id',
            'subscription_status' => 'required|in:trial,active,inactive,cancelled,expired',
            'address' => 'nullable|string',
            'city' => 'nullable|string|max:100',
            'state' => 'nullable|string|max:100',
            'country' => 'nullable|string|max:100',
            'postal_code' => 'nullable|string|max:20',
            'timezone' => 'nullable|string|max:50',
            'currency' => 'nullable|string|max:3',
            'language' => 'nullable|string|max:2',
        ]);

        // Check if subdomain is available
        $fullDomain = $request->subdomain . '.localhost';
        if (\App\Models\Domain::where('domain', $fullDomain)->exists()) {
            return back()->withErrors(['subdomain' => 'This subdomain is already taken.']);
        }

        $tenant = Tenant::create([
            'id' => $request->subdomain,
            'name' => $request->name,
            'email' => $request->email,
            'phone' => $request->phone,
            'address' => $request->address,
            'city' => $request->city,
            'state' => $request->state,
            'country' => $request->country,
            'postal_code' => $request->postal_code,
            'timezone' => $request->timezone ?? 'UTC',
            'currency' => $request->currency ?? 'USD',
            'language' => $request->language ?? 'en',
            'subscription_plan_id' => $request->subscription_plan_id,
            'subscription_status' => $request->subscription_status,
            'trial_ends_at' => $request->subscription_status === 'trial' ? now()->addDays(14) : null,
            'setup_status' => 'pending',
            'is_active' => false,
        ]);

        // Create domain
        $tenant->domains()->create([
            'domain' => $fullDomain,
            // 'is_primary' => true,
        ]);

        // Update tenant status to indicate setup has started
        $tenant->update([
            'setup_status' => 'in_progress',
            'setup_started_at' => now(),
            'setup_error' => null,
        ]);

        // Create job chain directly from controller
        // Use simple approach for better debugging
        if (config('app.debug', false)) {
            // Use simple sequential job for debugging
            SimpleTenantSetupJob::dispatch($tenant, $request->manager_password);
            Log::info("Dispatched SimpleTenantSetupJob for tenant: {$tenant->id}");
        } else {
            // Use job chain for production
            $this->createTenantSetupChain($tenant, $request->manager_password);
        }

        Log::info("Tenant creation chain initiated for: {$tenant->id}");

        return redirect()->route('admin.tenants.index')
            ->with('success', 'Tenant creation has been initiated. Setup will continue in the background.');
    }

    /**
     * Display the specified tenant
     */
    public function show(Tenant $tenant)
    {
        $tenant->load(['subscriptionPlan', 'domains', 'payments' => function ($query) {
            $query->latest()->limit(10);
        }]);

        // Get comprehensive tenant analytics
        $analytics = $this->getTenantAnalytics($tenant);
        $paymentHistory = $this->getPaymentHistory($tenant);
        $usageStats = $this->getUsageStats($tenant);

        return Inertia::render('Admin/Tenants/Show', [
            'tenant' => $tenant,
            'analytics' => $analytics,
            'paymentHistory' => $paymentHistory,
            'usageStats' => $usageStats,
        ]);
    }

    /**
     * Show the form for editing the specified tenant
     */
    public function edit(Tenant $tenant)
    {
        $tenant->load(['subscriptionPlan', 'domains']);

        return Inertia::render('Admin/Tenants/Edit', [
            'tenant' => $tenant,
            'plans' => SubscriptionPlan::all(),
        ]);
    }

    /**
     * Update the specified tenant
     */
    public function update(Request $request, Tenant $tenant)
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'email' => 'required|email|unique:tenants,email,' . $tenant->id,
            'phone' => 'nullable|string|max:20',
            'subscription_plan_id' => 'required|exists:subscription_plans,id',
            'subscription_status' => 'required|in:trial,active,inactive,cancelled,expired',
        ]);

        $tenant->update($request->only([
            'name', 'email', 'phone', 'subscription_plan_id', 'subscription_status'
        ]));

        return redirect()->route('admin.tenants.index')
            ->with('success', 'Tenant updated successfully.');
    }

    /**
     * Remove the specified tenant
     */
    public function destroy(Tenant $tenant)
    {
        $tenant->delete();

        return redirect()->route('admin.tenants.index')
            ->with('success', 'Tenant deleted successfully.');
    }

    /**
     * Suspend a tenant
     */
    public function suspend(Tenant $tenant)
    {
        $tenant->update(['subscription_status' => 'inactive']);

        return back()->with('success', 'Tenant suspended successfully.');
    }

    /**
     * Activate a tenant
     */
    public function activate(Tenant $tenant)
    {
        $tenant->update(['subscription_status' => 'active']);

        return back()->with('success', 'Tenant activated successfully.');
    }

    /**
     * Impersonate a tenant (redirect to tenant dashboard)
     */
    public function impersonate(Tenant $tenant)
    {
        $primaryDomain = $tenant->getPrimaryDomain();

        if (!$primaryDomain) {
            return back()->withErrors(['error' => 'Tenant has no primary domain.']);
        }

        return redirect('http://' . $primaryDomain . '/dashboard');
    }

    /**
     * Create the tenant setup job chain
     */
    private function createTenantSetupChain(Tenant $tenant, string $managerPassword): void
    {
        try {
            // Create a job chain that executes sequentially
            Bus::chain([
                // Step 1: Create tenant database and run migrations
                new CreateTenantDatabaseJob($tenant),

                // Step 2: Seed tenant database with default data
                new SeedTenantDataJob($tenant),

                // Step 3: Create manager user in tenant database
                new CreateTenantManagerJob($tenant, $managerPassword),

                // Step 4: Set up file system
                new SetupFileSystemJob($tenant),

                // Step 5: Set up third-party services
                new SetupThirdPartyServicesJob($tenant),

                // Step 6: Send welcome email
                new SendWelcomeEmailJob($tenant, $managerPassword),

                // Step 7: Finalize setup
                new FinalizeTenantSetupJob($tenant),

            ])->onQueue('tenant-creation')
              ->catch(function (Exception $e) use ($tenant) {
                  // Handle chain failure
                  Log::error("Tenant creation chain failed for {$tenant->id}: " . $e->getMessage());

                  $tenant->update([
                      'setup_status' => 'failed',
                      'setup_error' => $e->getMessage(),
                      'setup_failed_at' => now(),
                  ]);

                  // Send failure notification
                  NotifyTenantCreationFailedJob::dispatch(
                      $tenant,
                      'Chain Execution',
                      $e->getMessage()
                  );
              });

            Log::info("Job chain created successfully for tenant: {$tenant->id}");

        } catch (Exception $e) {
            Log::error("Failed to create job chain for tenant {$tenant->id}: " . $e->getMessage());

            $tenant->update([
                'setup_status' => 'chain_creation_failed',
                'setup_error' => $e->getMessage(),
                'setup_failed_at' => now(),
            ]);

            throw $e;
        }
    }

    /**
     * Bulk delete tenants
     */
    public function bulkDelete(Request $request)
    {
        $request->validate([
            'tenant_ids' => 'required|array|min:1',
            'tenant_ids.*' => 'exists:tenants,id',
        ]);

        try {
            DB::beginTransaction();

            $tenantIds = $request->tenant_ids;
            $deletedCount = 0;

            foreach ($tenantIds as $tenantId) {
                $tenant = Tenant::find($tenantId);
                if ($tenant) {
                    // Log the deletion for audit trail
                    Log::info("Bulk deleting tenant: {$tenant->id} ({$tenant->name}) by admin: " . auth()->id());

                    $tenant->delete();
                    $deletedCount++;
                }
            }

            DB::commit();

            Log::info("Bulk delete completed: {$deletedCount} tenants deleted by admin: " . auth()->id());

            return back()->with('success', "Successfully deleted {$deletedCount} tenant(s).");

        } catch (Exception $e) {
            DB::rollBack();
            Log::error("Bulk delete failed: " . $e->getMessage());

            return back()->withErrors(['error' => 'Failed to delete tenants. Please try again.']);
        }
    }

    /**
     * Subscribe a tenant to a plan
     */
    public function subscribe(Request $request, Tenant $tenant)
    {
        $request->validate([
            'subscription_plan_id' => 'required|exists:subscription_plans,id',
        ]);

        try {
            $plan = SubscriptionPlan::findOrFail($request->subscription_plan_id);

            // Update tenant subscription
            $tenant->update([
                'subscription_plan_id' => $plan->id,
                'subscription_status' => 'active',
                'subscription_started_at' => now(),
                'subscription_ends_at' => now()->addMonth(),
                'trial_ends_at' => null,
            ]);

            Log::info("Tenant {$tenant->id} subscribed to plan {$plan->name} by admin: " . auth()->id());

            return back()->with('success', "Tenant successfully subscribed to {$plan->name} plan.");

        } catch (Exception $e) {
            Log::error("Subscription failed for tenant {$tenant->id}: " . $e->getMessage());

            return back()->withErrors(['error' => 'Failed to update subscription. Please try again.']);
        }
    }

    /**
     * Renew tenant subscription
     */
    public function renewSubscription(Tenant $tenant)
    {
        try {
            $currentPlan = $tenant->subscriptionPlan;

            if (!$currentPlan) {
                return back()->withErrors(['error' => 'Tenant has no subscription plan to renew.']);
            }

            // Extend subscription by one month
            $newEndDate = $tenant->subscription_ends_at && $tenant->subscription_ends_at->isFuture()
                ? $tenant->subscription_ends_at->addMonth()
                : now()->addMonth();

            $tenant->update([
                'subscription_status' => 'active',
                'subscription_ends_at' => $newEndDate,
            ]);

            Log::info("Tenant {$tenant->id} subscription renewed until {$newEndDate} by admin: " . auth()->id());

            return back()->with('success', 'Subscription renewed successfully.');

        } catch (Exception $e) {
            Log::error("Subscription renewal failed for tenant {$tenant->id}: " . $e->getMessage());

            return back()->withErrors(['error' => 'Failed to renew subscription. Please try again.']);
        }
    }

    /**
     * Get tenant setup status for AJAX requests
     */
    public function setupStatus(Tenant $tenant)
    {
        return response()->json([
            'setup_status' => $tenant->setup_status,
            'setup_status_label' => $tenant->getSetupStatusLabel(),
            'setup_progress' => $tenant->getSetupProgress(),
            'setup_in_progress' => $tenant->setupInProgress(),
            'setup_completed' => $tenant->setupCompleted(),
            'setup_failed' => $tenant->setupFailed(),
            'setup_error' => $tenant->setup_error,
            'setup_started_at' => $tenant->setup_started_at?->toDateTimeString(),
            'setup_completed_at' => $tenant->setup_completed_at?->toDateTimeString(),
            'setup_duration_minutes' => $tenant->setup_duration_minutes,
        ]);
    }

    /**
     * Get comprehensive tenant analytics
     */
    private function getTenantAnalytics(Tenant $tenant): array
    {
        try {
            // Switch to tenant database
            tenancy()->initialize($tenant);

            $analytics = [
                'orders' => $this->getOrderAnalytics(),
                'revenue' => $this->getRevenueAnalytics(),
                'menu' => $this->getMenuAnalytics(),
                'staff' => $this->getStaffAnalytics(),
                'customers' => $this->getCustomerAnalytics(),
            ];

            // Switch back to central database
            tenancy()->end();

            return $analytics;
        } catch (\Exception $e) {
            // Switch back to central database in case of error
            tenancy()->end();

            return [
                'orders' => ['error' => 'Unable to fetch order data'],
                'revenue' => ['error' => 'Unable to fetch revenue data'],
                'menu' => ['error' => 'Unable to fetch menu data'],
                'staff' => ['error' => 'Unable to fetch staff data'],
                'customers' => ['error' => 'Unable to fetch customer data'],
            ];
        }
    }

    /**
     * Get order analytics from tenant database
     */
    private function getOrderAnalytics(): array
    {
        $orderModel = app(\App\Models\Tenant\Order::class);

        $totalOrders = $orderModel->count();
        $ordersThisMonth = $orderModel->whereMonth('created_at', now()->month)
                                   ->whereYear('created_at', now()->year)
                                   ->count();
        $ordersLastMonth = $orderModel->whereMonth('created_at', now()->subMonth()->month)
                                    ->whereYear('created_at', now()->subMonth()->year)
                                    ->count();

        $averageOrdersPerDay = $orderModel->where('created_at', '>=', now()->subDays(30))
                                        ->count() / 30;

        // Order status breakdown
        $statusBreakdown = $orderModel->selectRaw('status, COUNT(*) as count')
                                    ->groupBy('status')
                                    ->pluck('count', 'status')
                                    ->toArray();

        // Recent orders trend (last 7 days)
        $recentTrend = [];
        for ($i = 6; $i >= 0; $i--) {
            $date = now()->subDays($i);
            $count = $orderModel->whereDate('created_at', $date)->count();
            $recentTrend[] = [
                'date' => $date->format('Y-m-d'),
                'count' => $count
            ];
        }

        return [
            'total_orders' => $totalOrders,
            'orders_this_month' => $ordersThisMonth,
            'orders_last_month' => $ordersLastMonth,
            'month_over_month_change' => $ordersLastMonth > 0 ?
                round((($ordersThisMonth - $ordersLastMonth) / $ordersLastMonth) * 100, 2) : 0,
            'average_orders_per_day' => round($averageOrdersPerDay, 2),
            'status_breakdown' => $statusBreakdown,
            'recent_trend' => $recentTrend,
        ];
    }

    /**
     * Get revenue analytics from tenant database
     */
    private function getRevenueAnalytics(): array
    {
        $orderModel = app(\App\Models\Tenant\Order::class);

        $totalRevenue = $orderModel->where('payment_status', 'paid')->sum('total_amount');
        $revenueThisMonth = $orderModel->where('payment_status', 'paid')
                                     ->whereMonth('created_at', now()->month)
                                     ->whereYear('created_at', now()->year)
                                     ->sum('total_amount');
        $revenueLastMonth = $orderModel->where('payment_status', 'paid')
                                     ->whereMonth('created_at', now()->subMonth()->month)
                                     ->whereYear('created_at', now()->subMonth()->year)
                                     ->sum('total_amount');

        $averageOrderValue = $orderModel->where('payment_status', 'paid')
                                      ->avg('total_amount');

        // Revenue trend (last 7 days)
        $revenueTrend = [];
        for ($i = 6; $i >= 0; $i--) {
            $date = now()->subDays($i);
            $revenue = $orderModel->where('payment_status', 'paid')
                                ->whereDate('created_at', $date)
                                ->sum('total_amount');
            $revenueTrend[] = [
                'date' => $date->format('Y-m-d'),
                'revenue' => (float) $revenue
            ];
        }

        return [
            'total_revenue' => (float) $totalRevenue,
            'revenue_this_month' => (float) $revenueThisMonth,
            'revenue_last_month' => (float) $revenueLastMonth,
            'month_over_month_change' => $revenueLastMonth > 0 ?
                round((($revenueThisMonth - $revenueLastMonth) / $revenueLastMonth) * 100, 2) : 0,
            'average_order_value' => (float) $averageOrderValue,
            'revenue_trend' => $revenueTrend,
        ];
    }

    /**
     * Get menu analytics from tenant database
     */
    private function getMenuAnalytics(): array
    {
        try {
            $menuItemModel = app(\App\Models\Tenant\MenuItem::class);
            $categoryModel = app(\App\Models\Tenant\Category::class);
            $orderItemModel = app(\App\Models\Tenant\OrderItem::class);

            $totalMenuItems = $menuItemModel->count();
            $activeMenuItems = $menuItemModel->where('is_available', true)->count();
            $totalCategories = $categoryModel->count();
            $activeCategories = $categoryModel->where('is_active', true)->count();

            // Most popular menu items (based on order frequency)
            $popularItems = $orderItemModel->selectRaw('menu_item_id, food_name, COUNT(*) as order_count, SUM(total_price) as total_revenue')
                                         ->groupBy('menu_item_id', 'food_name')
                                         ->orderByDesc('order_count')
                                         ->limit(5)
                                         ->get()
                                         ->map(function ($item) {
                                             return [
                                                 'name' => $item->food_name,
                                                 'order_count' => $item->order_count,
                                                 'total_revenue' => (float) $item->total_revenue,
                                             ];
                                         });

            return [
                'total_menu_items' => $totalMenuItems,
                'active_menu_items' => $activeMenuItems,
                'inactive_menu_items' => $totalMenuItems - $activeMenuItems,
                'total_categories' => $totalCategories,
                'active_categories' => $activeCategories,
                'popular_items' => $popularItems,
            ];
        } catch (\Exception $e) {
            return [
                'total_menu_items' => 0,
                'active_menu_items' => 0,
                'inactive_menu_items' => 0,
                'total_categories' => 0,
                'active_categories' => 0,
                'popular_items' => [],
                'error' => 'Unable to fetch menu data'
            ];
        }
    }

    /**
     * Get staff analytics from tenant database
     */
    private function getStaffAnalytics(): array
    {
        try {
            // For now, return mock data since staff management varies by implementation
            // In a real implementation, you would query the appropriate staff table
            return [
                'total_staff' => 8,
                'active_staff' => 7,
                'inactive_staff' => 1,
                'role_breakdown' => [
                    'manager' => 1,
                    'waiter' => 4,
                    'chef' => 2,
                    'cashier' => 1,
                ],
                'recent_staff' => [
                    ['name' => 'John Doe', 'role' => 'waiter', 'hired_date' => now()->subDays(5)->format('Y-m-d')],
                    ['name' => 'Jane Smith', 'role' => 'chef', 'hired_date' => now()->subDays(12)->format('Y-m-d')],
                ],
            ];
        } catch (\Exception $e) {
            return [
                'total_staff' => 0,
                'active_staff' => 0,
                'inactive_staff' => 0,
                'role_breakdown' => [],
                'recent_staff' => [],
                'error' => 'Unable to fetch staff data'
            ];
        }
    }

    /**
     * Get customer analytics from tenant database
     */
    private function getCustomerAnalytics(): array
    {
        try {
            $customerModel = app(\App\Models\Tenant\Customer::class);

            $totalCustomers = $customerModel->count();
            $newCustomersThisMonth = $customerModel->whereMonth('created_at', now()->month)
                                                 ->whereYear('created_at', now()->year)
                                                 ->count();
            $activeCustomers = $customerModel->where('last_order_at', '>=', now()->subDays(30))->count();

            // Customer tier breakdown
            $tierBreakdown = $customerModel->selectRaw('customer_tier, COUNT(*) as count')
                                         ->groupBy('customer_tier')
                                         ->pluck('count', 'customer_tier')
                                         ->toArray();

            return [
                'total_customers' => $totalCustomers,
                'new_customers_this_month' => $newCustomersThisMonth,
                'active_customers' => $activeCustomers,
                'tier_breakdown' => $tierBreakdown,
            ];
        } catch (\Exception $e) {
            return [
                'total_customers' => 0,
                'new_customers_this_month' => 0,
                'active_customers' => 0,
                'tier_breakdown' => [],
                'error' => 'Unable to fetch customer data'
            ];
        }
    }

    /**
     * Get payment history for tenant
     */
    private function getPaymentHistory(Tenant $tenant): array
    {
        try {
            $payments = $tenant->payments()
                             ->with('subscriptionPlan')
                             ->orderBy('created_at', 'desc')
                             ->paginate(10);

            $totalRevenue = $tenant->payments()
                                 ->where('status', 'paid')
                                 ->sum('amount');

            return [
                'payments' => $payments,
                'total_revenue' => (float) $totalRevenue,
                'payment_count' => $tenant->payments()->count(),
                'successful_payments' => $tenant->payments()->where('status', 'paid')->count(),
            ];
        } catch (\Exception $e) {
            return [
                'payments' => [],
                'total_revenue' => 0,
                'payment_count' => 0,
                'successful_payments' => 0,
                'error' => 'Unable to fetch payment data'
            ];
        }
    }

    /**
     * Get usage statistics for tenant
     */
    private function getUsageStats(Tenant $tenant): array
    {
        try {
            tenancy()->initialize($tenant);

            $menuItemModel = app(\App\Models\Tenant\MenuItem::class);
            $categoryModel = app(\App\Models\Tenant\Category::class);
            $orderModel = app(\App\Models\Tenant\Order::class);

            // Get plan limits
            $plan = $tenant->subscriptionPlan;
            $planLimits = $plan ? [
                'menu_items' => 100, // Mock limit for demo
                'orders_per_month' => 1000, // Mock limit for demo
                'staff_accounts' => 10, // Mock limit for demo
            ] : [
                'menu_items' => 'unlimited',
                'orders_per_month' => 'unlimited',
                'staff_accounts' => 'unlimited',
            ];

            // Current usage
            $currentUsage = [
                'menu_items' => $menuItemModel->count(),
                'categories' => $categoryModel->count(),
                'orders_this_month' => $orderModel->whereMonth('created_at', now()->month)
                                                ->whereYear('created_at', now()->year)
                                                ->count(),
            ];

            tenancy()->end();

            // Staff count (mock data for demo)
            $currentUsage['staff_accounts'] = 7;

            return [
                'plan_limits' => $planLimits,
                'current_usage' => $currentUsage,
                'usage_percentages' => $this->calculateUsagePercentages($planLimits, $currentUsage),
            ];
        } catch (\Exception $e) {
            tenancy()->end();

            return [
                'plan_limits' => [],
                'current_usage' => [],
                'usage_percentages' => [],
                'error' => 'Unable to fetch usage data'
            ];
        }
    }

    /**
     * Calculate usage percentages
     */
    private function calculateUsagePercentages(array $limits, array $usage): array
    {
        $percentages = [];

        foreach ($usage as $key => $value) {
            if (isset($limits[$key]) && $limits[$key] !== 'unlimited' && $limits[$key] > 0) {
                $percentages[$key] = round(($value / $limits[$key]) * 100, 2);
            } else {
                $percentages[$key] = 0; // Unlimited or no limit
            }
        }

        return $percentages;
    }
}
