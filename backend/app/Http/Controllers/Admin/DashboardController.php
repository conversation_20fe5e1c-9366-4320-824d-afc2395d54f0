<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Inertia\Inertia;
use App\Models\Tenant;
use App\Models\Payment;
use App\Models\SubscriptionPlan;
use Carbon\Carbon;

class DashboardController extends Controller
{
    /**
     * Show the admin dashboard
     */
    public function index()
    {
        $stats = $this->getDashboardStats();
        
        return Inertia::render('Admin/Dashboard', [
            'stats' => $stats,
            'recentTenants' => $this->getRecentTenants(),
            'recentPayments' => $this->getRecentPayments(),
            'monthlyRevenue' => $this->getMonthlyRevenue(),
        ]);
    }

    /**
     * Get dashboard statistics
     */
    private function getDashboardStats()
    {
        $totalTenants = Tenant::count();
        $activeTenants = Tenant::where('subscription_status', 'active')->count();
        $trialTenants = Tenant::where('subscription_status', 'trial')->count();
        $totalRevenue = Payment::successful()->sum('amount');
        $monthlyRevenue = Payment::successful()
            ->whereMonth('created_at', now()->month)
            ->whereYear('created_at', now()->year)
            ->sum('amount');

        return [
            'total_tenants' => $totalTenants,
            'active_tenants' => $activeTenants,
            'trial_tenants' => $trialTenants,
            'total_revenue' => $totalRevenue,
            'monthly_revenue' => $monthlyRevenue,
            'growth_rate' => $this->calculateGrowthRate(),
        ];
    }

    /**
     * Get recent tenants
     */
    private function getRecentTenants()
    {
        return Tenant::with('subscriptionPlan')
            ->latest()
            ->take(5)
            ->get()
            ->map(function ($tenant) {
                return [
                    'id' => $tenant->id,
                    'name' => $tenant->name,
                    'email' => $tenant->email,
                    'status' => $tenant->subscription_status,
                    'plan' => $tenant->subscriptionPlan?->name,
                    'created_at' => $tenant->created_at->format('M d, Y'),
                ];
            });
    }

    /**
     * Get recent payments
     */
    private function getRecentPayments()
    {
        return Payment::with(['tenant', 'subscriptionPlan'])
            ->latest()
            ->take(5)
            ->get()
            ->map(function ($payment) {
                return [
                    'id' => $payment->id,
                    'tenant_name' => $payment->tenant->name,
                    'amount' => $payment->amount,
                    'currency' => $payment->currency,
                    'status' => $payment->status,
                    'plan' => $payment->subscriptionPlan->name,
                    'created_at' => $payment->created_at->format('M d, Y'),
                ];
            });
    }

    /**
     * Get monthly revenue for the last 12 months
     */
    private function getMonthlyRevenue()
    {
        $months = [];
        for ($i = 11; $i >= 0; $i--) {
            $date = now()->subMonths($i);
            $revenue = Payment::successful()
                ->whereMonth('created_at', $date->month)
                ->whereYear('created_at', $date->year)
                ->sum('amount');
            
            $months[] = [
                'month' => $date->format('M Y'),
                'revenue' => $revenue,
            ];
        }
        
        return $months;
    }

    /**
     * Calculate growth rate
     */
    private function calculateGrowthRate()
    {
        $currentMonth = Tenant::whereMonth('created_at', now()->month)
            ->whereYear('created_at', now()->year)
            ->count();
            
        $lastMonth = Tenant::whereMonth('created_at', now()->subMonth()->month)
            ->whereYear('created_at', now()->subMonth()->year)
            ->count();

        if ($lastMonth == 0) {
            return $currentMonth > 0 ? 100 : 0;
        }

        return round((($currentMonth - $lastMonth) / $lastMonth) * 100, 2);
    }
}
