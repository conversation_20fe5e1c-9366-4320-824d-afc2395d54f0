<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\SubscriptionPlan;
use Illuminate\Http\Request;
use Inertia\Inertia;
use Illuminate\Support\Str;

class SubscriptionPlanController extends Controller
{
    /**
     * Display a listing of subscription plans
     */
    public function index(Request $request)
    {
        $query = SubscriptionPlan::query();

        // Search functionality
        if ($request->filled('search')) {
            $query->where('name', 'like', '%' . $request->search . '%')
                  ->orWhere('description', 'like', '%' . $request->search . '%');
        }

        // Filter by status
        if ($request->filled('status')) {
            $query->where('is_active', $request->status === 'active');
        }

        // Filter by billing cycle
        if ($request->filled('billing_cycle')) {
            $query->where('billing_cycle', $request->billing_cycle);
        }

        $plans = $query->withCount('tenants')
                      ->orderBy('sort_order')
                      ->paginate(15);

        return Inertia::render('Admin/SubscriptionPlans/Index', [
            'plans' => $plans,
            'filters' => $request->only(['search', 'status', 'billing_cycle']),
            'stats' => [
                'total_plans' => SubscriptionPlan::count(),
                'active_plans' => SubscriptionPlan::where('is_active', true)->count(),
                'total_subscriptions' => SubscriptionPlan::withCount('tenants')->get()->sum('tenants_count'),
            ]
        ]);
    }

    /**
     * Show the form for creating a new subscription plan
     */
    public function create()
    {
        return Inertia::render('Admin/SubscriptionPlans/Create');
    }

    /**
     * Store a newly created subscription plan
     */
    public function store(Request $request)
    {
        $request->validate([
            'name' => 'required|string|max:255|unique:subscription_plans,name',
            'description' => 'required|string|max:1000',
            'price' => 'required|numeric|min:0',
            'billing_cycle' => 'required|in:monthly,yearly',
            'trial_days' => 'required|integer|min:0|max:365',
            'features' => 'required|array|min:1',
            'features.*' => 'string|max:255',
            'max_orders_per_month' => 'nullable|integer|min:1',
            'max_menu_items' => 'nullable|integer|min:1',
            'max_tables' => 'nullable|integer|min:1',
            'max_staff' => 'nullable|integer|min:1',
            'has_delivery' => 'boolean',
            'has_analytics' => 'boolean',
            'has_multi_location' => 'boolean',
            'has_api_access' => 'boolean',
            'is_active' => 'boolean',
        ]);

        $plan = SubscriptionPlan::create([
            'name' => $request->name,
            'slug' => Str::slug($request->name),
            'description' => $request->description,
            'price' => $request->price,
            'billing_cycle' => $request->billing_cycle,
            'trial_days' => $request->trial_days,
            'features' => $request->features,
            'max_orders_per_month' => $request->max_orders_per_month,
            'max_menu_items' => $request->max_menu_items,
            'max_tables' => $request->max_tables,
            'max_staff' => $request->max_staff,
            'has_delivery' => $request->boolean('has_delivery'),
            'has_analytics' => $request->boolean('has_analytics'),
            'has_multi_location' => $request->boolean('has_multi_location'),
            'has_api_access' => $request->boolean('has_api_access'),
            'is_active' => $request->boolean('is_active'),
            'sort_order' => SubscriptionPlan::max('sort_order') + 1,
        ]);

        return redirect()->route('admin.subscription-plans.index')
            ->with('success', 'Subscription plan created successfully.');
    }

    /**
     * Display the specified subscription plan
     */
    public function show(SubscriptionPlan $plan)
    {
        $plan->load(['tenants' => function ($query) {
            $query->latest()->limit(10);
        }]);

        $stats = [
            'total_tenants' => $plan->tenants()->count(),
            'active_tenants' => $plan->tenants()->where('status', 'active')->count(),
            'trial_tenants' => $plan->tenants()->where('subscription_status', 'trial')->count(),
            'monthly_revenue' => $plan->tenants()
                ->where('subscription_status', 'active')
                ->where('billing_cycle', 'monthly')
                ->count() * $plan->price,
        ];

        return Inertia::render('Admin/SubscriptionPlans/Show', [
            'plan' => $plan,
            'stats' => $stats,
        ]);
    }

    /**
     * Show the form for editing the specified subscription plan
     */
    public function edit(SubscriptionPlan $plan)
    {
        return Inertia::render('Admin/SubscriptionPlans/Edit', [
            'plan' => $plan,
        ]);
    }

    /**
     * Update the specified subscription plan
     */
    public function update(Request $request, SubscriptionPlan $plan)
    {
        $request->validate([
            'name' => 'required|string|max:255|unique:subscription_plans,name,' . $plan->id,
            'description' => 'required|string|max:1000',
            'price' => 'required|numeric|min:0',
            'billing_cycle' => 'required|in:monthly,yearly',
            'trial_days' => 'required|integer|min:0|max:365',
            'features' => 'required|array|min:1',
            'features.*' => 'string|max:255',
            'max_orders_per_month' => 'nullable|integer|min:1',
            'max_menu_items' => 'nullable|integer|min:1',
            'max_tables' => 'nullable|integer|min:1',
            'max_staff' => 'nullable|integer|min:1',
            'has_delivery' => 'boolean',
            'has_analytics' => 'boolean',
            'has_multi_location' => 'boolean',
            'has_api_access' => 'boolean',
            'is_active' => 'boolean',
        ]);

        $plan->update([
            'name' => $request->name,
            'slug' => Str::slug($request->name),
            'description' => $request->description,
            'price' => $request->price,
            'billing_cycle' => $request->billing_cycle,
            'trial_days' => $request->trial_days,
            'features' => $request->features,
            'max_orders_per_month' => $request->max_orders_per_month,
            'max_menu_items' => $request->max_menu_items,
            'max_tables' => $request->max_tables,
            'max_staff' => $request->max_staff,
            'has_delivery' => $request->boolean('has_delivery'),
            'has_analytics' => $request->boolean('has_analytics'),
            'has_multi_location' => $request->boolean('has_multi_location'),
            'has_api_access' => $request->boolean('has_api_access'),
            'is_active' => $request->boolean('is_active'),
        ]);

        return redirect()->route('admin.subscription-plans.index')
            ->with('success', 'Subscription plan updated successfully.');
    }

    /**
     * Remove the specified subscription plan
     */
    public function destroy(SubscriptionPlan $plan)
    {
        // Check if plan has active tenants
        if ($plan->tenants()->where('status', 'active')->exists()) {
            return back()->withErrors([
                'plan' => 'Cannot delete plan with active tenants. Please migrate tenants to another plan first.'
            ]);
        }

        $plan->delete();

        return redirect()->route('admin.subscription-plans.index')
            ->with('success', 'Subscription plan deleted successfully.');
    }

    /**
     * Toggle plan status
     */
    public function toggleStatus(SubscriptionPlan $plan)
    {
        $plan->update(['is_active' => !$plan->is_active]);

        $status = $plan->is_active ? 'activated' : 'deactivated';
        
        return response()->json([
            'success' => true,
            'message' => "Plan {$status} successfully.",
            'is_active' => $plan->is_active,
        ]);
    }

    /**
     * Update plan sort order
     */
    public function updateOrder(Request $request)
    {
        $request->validate([
            'plans' => 'required|array',
            'plans.*.id' => 'required|exists:subscription_plans,id',
            'plans.*.sort_order' => 'required|integer|min:0',
        ]);

        foreach ($request->plans as $planData) {
            SubscriptionPlan::where('id', $planData['id'])
                ->update(['sort_order' => $planData['sort_order']]);
        }

        return response()->json([
            'success' => true,
            'message' => 'Plan order updated successfully.',
        ]);
    }

    /**
     * Get plan analytics
     */
    public function analytics(SubscriptionPlan $plan)
    {
        $monthlyData = [];
        for ($i = 11; $i >= 0; $i--) {
            $date = now()->subMonths($i);
            $monthlyData[] = [
                'month' => $date->format('M Y'),
                'new_subscriptions' => $plan->tenants()
                    ->whereYear('created_at', $date->year)
                    ->whereMonth('created_at', $date->month)
                    ->count(),
                'revenue' => $plan->tenants()
                    ->where('subscription_status', 'active')
                    ->whereYear('created_at', $date->year)
                    ->whereMonth('created_at', $date->month)
                    ->count() * $plan->price,
            ];
        }

        return response()->json([
            'monthly_data' => $monthlyData,
            'total_revenue' => $plan->tenants()
                ->where('subscription_status', 'active')
                ->count() * $plan->price,
            'conversion_rate' => $this->calculateConversionRate($plan),
        ]);
    }

    /**
     * Calculate conversion rate from trial to paid
     */
    private function calculateConversionRate(SubscriptionPlan $plan)
    {
        $totalTrials = $plan->tenants()
            ->where('subscription_status', 'trial')
            ->orWhere('subscription_status', 'active')
            ->count();

        $convertedTrials = $plan->tenants()
            ->where('subscription_status', 'active')
            ->count();

        return $totalTrials > 0 ? ($convertedTrials / $totalTrials) * 100 : 0;
    }

    /**
     * Handle bulk actions on subscription plans
     */
    public function bulkAction(Request $request)
    {
        $request->validate([
            'action' => 'required|in:activate,deactivate,delete',
            'plan_ids' => 'required|array|min:1',
            'plan_ids.*' => 'exists:subscription_plans,id',
        ]);

        $planIds = $request->plan_ids;
        $action = $request->action;
        $affectedCount = 0;

        switch ($action) {
            case 'activate':
                $affectedCount = SubscriptionPlan::whereIn('id', $planIds)
                    ->update(['is_active' => true]);
                break;

            case 'deactivate':
                $affectedCount = SubscriptionPlan::whereIn('id', $planIds)
                    ->update(['is_active' => false]);
                break;

            case 'delete':
                // Check if any plans have active tenants
                $plansWithTenants = SubscriptionPlan::whereIn('id', $planIds)
                    ->whereHas('tenants', function ($query) {
                        $query->where('status', 'active');
                    })
                    ->pluck('name');

                if ($plansWithTenants->isNotEmpty()) {
                    return back()->withErrors([
                        'bulk_action' => 'Cannot delete plans with active tenants: ' . $plansWithTenants->implode(', ')
                    ]);
                }

                $affectedCount = SubscriptionPlan::whereIn('id', $planIds)->delete();
                break;
        }

        $actionText = [
            'activate' => 'activated',
            'deactivate' => 'deactivated',
            'delete' => 'deleted',
        ];

        return redirect()->route('admin.subscription-plans.index')
            ->with('success', "{$affectedCount} plans {$actionText[$action]} successfully.");
    }
}
