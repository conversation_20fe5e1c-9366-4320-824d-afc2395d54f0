<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\DynamicPage;
use Illuminate\Http\Request;
use Illuminate\Support\Str;
use Inertia\Inertia;

class DynamicPageController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        $query = DynamicPage::query();

        // Search
        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->where('title', 'like', "%{$search}%")
                  ->orWhere('slug', 'like', "%{$search}%")
                  ->orWhere('content', 'like', "%{$search}%");
            });
        }

        // Filter by status
        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }

        // Sort
        $sortBy = $request->get('sort', 'created_at');
        $sortOrder = 'desc';
        if ($sortBy === 'sort_order') {
            $sortOrder = 'asc';
        }
        $query->orderBy($sortBy, $sortOrder);

        $pages = $query->paginate(15)->withQueryString();

        $stats = [
            'total_pages' => DynamicPage::count(),
            'active_pages' => DynamicPage::where('status', 'active')->count(),
            'inactive_pages' => DynamicPage::where('status', 'inactive')->count(),
        ];

        return Inertia::render('Admin/DynamicPages/Index', [
            'pages' => $pages,
            'stats' => $stats,
            'filters' => $request->only(['search', 'status', 'sort'])
        ]);
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        return Inertia::render('Admin/DynamicPages/Create');
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $request->validate([
            'title_en' => 'required|string|max:255',
            'title_bn' => 'required|string|max:255',
            'slug' => 'nullable|string|max:255|unique:dynamic_pages,slug',
            'content' => 'required|string',
            'meta_description' => 'nullable|string|max:500',
            'status' => 'required|in:active,inactive',
            'sort_order' => 'nullable|integer|min:0',
        ]);

        $data = $request->all();

        // Handle title translations
        $data['title'] = [
            'en' => $request->title_en,
            'bn' => $request->title_bn,
        ];

        // Generate slug if not provided
        if (empty($data['slug'])) {
            $data['slug'] = DynamicPage::generateSlug($request->title_en);
        } else {
            $data['slug'] = Str::slug($data['slug']);
        }

        $page = DynamicPage::create($data);

        return redirect()->route('admin.dynamic-pages.index')
                        ->with('success', __('pages.created_successfully'));
    }

    /**
     * Display the specified resource.
     */
    public function show(DynamicPage $dynamicPage)
    {
        return Inertia::render('Admin/DynamicPages/Show', [
            'page' => $dynamicPage
        ]);
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(DynamicPage $dynamicPage)
    {
        return Inertia::render('Admin/DynamicPages/Edit', [
            'page' => $dynamicPage
        ]);
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, DynamicPage $dynamicPage)
    {
        $request->validate([
            'title_en' => 'required|string|max:255',
            'title_bn' => 'required|string|max:255',
            'slug' => 'nullable|string|max:255|unique:dynamic_pages,slug,' . $dynamicPage->id,
            'content' => 'required|string',
            'meta_description' => 'nullable|string|max:500',
            'status' => 'required|in:active,inactive',
            'sort_order' => 'nullable|integer|min:0',
        ]);

        $data = $request->all();

        // Handle title translations
        $data['title'] = [
            'en' => $request->title_en,
            'bn' => $request->title_bn,
        ];

        // Generate slug if not provided
        if (empty($data['slug'])) {
            $data['slug'] = DynamicPage::generateSlug($request->title_en);
        } else {
            $data['slug'] = Str::slug($data['slug']);
        }

        $dynamicPage->update($data);

        return redirect()->route('admin.dynamic-pages.index')
                        ->with('success', __('pages.updated_successfully'));
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(DynamicPage $dynamicPage)
    {
        $dynamicPage->delete();

        return redirect()->route('admin.dynamic-pages.index')
                        ->with('success', __('pages.deleted_successfully'));
    }

    /**
     * Bulk actions
     */
    public function bulkAction(Request $request)
    {
        $request->validate([
            'action' => 'required|in:activate,deactivate,delete',
            'ids' => 'required|array|min:1',
            'ids.*' => 'exists:dynamic_pages,id',
        ]);

        $pages = DynamicPage::whereIn('id', $request->ids);

        switch ($request->action) {
            case 'activate':
                $pages->update(['status' => 'active']);
                $message = __('pages.bulk_activated');
                break;
            case 'deactivate':
                $pages->update(['status' => 'inactive']);
                $message = __('pages.bulk_deactivated');
                break;
            case 'delete':
                $pages->delete();
                $message = __('pages.bulk_deleted');
                break;
        }

        return redirect()->route('admin.dynamic-pages.index')
                        ->with('success', $message);
    }
}
