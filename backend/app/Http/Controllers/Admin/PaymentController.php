<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Payment;
use Illuminate\Http\Request;
use Inertia\Inertia;

class PaymentController extends Controller
{
    /**
     * Display a listing of payments
     */
    public function index(Request $request)
    {
        $query = Payment::with(['tenant'])
            ->latest();

        // Filter by status
        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }

        // Filter by payment method
        if ($request->filled('payment_method')) {
            $query->where('payment_method', $request->payment_method);
        }

        // Filter by date range
        if ($request->filled('date_from')) {
            $query->whereDate('created_at', '>=', $request->date_from);
        }

        if ($request->filled('date_to')) {
            $query->whereDate('created_at', '<=', $request->date_to);
        }

        $payments = $query->paginate(20);

        return Inertia::render('Admin/Payments/Index', [
            'payments' => $payments,
            'filters' => $request->only(['status', 'payment_method', 'date_from', 'date_to']),
        ]);
    }

    /**
     * Display the specified payment
     */
    public function show(Payment $payment)
    {
        $payment->load(['tenant']);

        return Inertia::render('Admin/Payments/Show', [
            'payment' => $payment,
        ]);
    }

    /**
     * Refund a payment
     */
    public function refund(Payment $payment)
    {
        // TODO: Implement payment refund logic
        
        return response()->json([
            'success' => true,
            'message' => 'Payment refund initiated successfully.',
        ]);
    }

    /**
     * Handle payment gateway return
     */
    public function paymentReturn(Request $request)
    {
        // Log the return for debugging
        \Log::info('Payment return received', $request->all());
        
        // Extract payment information from request
        $transactionId = $request->get('transaction_id') ?? $request->get('tran_id');
        $status = $request->get('status') ?? 'unknown';
        
        if ($transactionId) {
            $payment = Payment::where('transaction_id', $transactionId)->first();
            
            if ($payment) {
                // Update payment status based on gateway response
                switch (strtolower($status)) {
                    case 'success':
                    case 'completed':
                    case 'paid':
                        $payment->update([
                            'status' => 'completed',
                            'paid_at' => now(),
                            'gateway_response' => $request->all(),
                        ]);
                        return redirect()->route('payment.success')->with('success', 'Payment completed successfully!');
                        
                    case 'failed':
                    case 'cancelled':
                    case 'declined':
                        $payment->update([
                            'status' => 'failed',
                            'failed_at' => now(),
                            'gateway_response' => $request->all(),
                        ]);
                        return redirect()->route('payment.fail')->with('error', 'Payment failed. Please try again.');
                        
                    default:
                        return redirect()->route('payment.cancel')->with('warning', 'Payment status unclear. Please contact support.');
                }
            }
        }
        
        // Default redirect if no payment found
        return redirect()->route('landing')->with('error', 'Payment information not found.');
    }

    /**
     * Handle payment webhook
     */
    public function webhook(Request $request)
    {
        // Log webhook for debugging
        \Log::info('Payment webhook received', $request->all());
        
        // TODO: Implement webhook verification and processing
        // This should verify the webhook signature and update payment status
        
        return response()->json(['status' => 'received'], 200);
    }

    /**
     * Payment success page
     */
    public function paymentSuccess(Request $request)
    {
        return view('payment.success', [
            'message' => $request->session()->get('success', 'Payment completed successfully!')
        ]);
    }

    /**
     * Payment cancel page
     */
    public function paymentCancel(Request $request)
    {
        return view('payment.cancel', [
            'message' => $request->session()->get('warning', 'Payment was cancelled.')
        ]);
    }

    /**
     * Payment fail page
     */
    public function paymentFail(Request $request)
    {
        return view('payment.fail', [
            'message' => $request->session()->get('error', 'Payment failed. Please try again.')
        ]);
    }
}
