<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Blog;
use App\Models\Media;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Str;
use Inertia\Inertia;

class BlogController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        $query = Blog::with(['featuredImage', 'author']);

        // Search
        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->where('title', 'like', "%{$search}%")
                  ->orWhere('slug', 'like', "%{$search}%")
                  ->orWhere('content', 'like', "%{$search}%")
                  ->orWhere('excerpt', 'like', "%{$search}%");
            });
        }

        // Filter by status
        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }

        // Filter by author
        if ($request->filled('author_id')) {
            $query->where('author_id', $request->author_id);
        }

        // Sort
        $sortBy = $request->get('sort', 'created_at');
        $sortOrder = 'desc';
        if ($sortBy === 'title') {
            $sortOrder = 'asc';
        }
        $query->orderBy($sortBy, $sortOrder);

        $blogs = $query->paginate(15)->withQueryString();
        $authors = User::all(); // Get all users as potential authors

        $stats = [
            'total_blogs' => Blog::count(),
            'published_blogs' => Blog::where('status', 'published')->count(),
            'draft_blogs' => Blog::where('status', 'draft')->count(),
            'total_authors' => User::whereHas('authoredBlogs')->count(),
        ];

        return Inertia::render('Admin/Blogs/Index', [
            'blogs' => $blogs,
            'authors' => $authors,
            'stats' => $stats,
            'filters' => $request->only(['search', 'status', 'author_id', 'sort'])
        ]);
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        $authors = User::all();
        return Inertia::render('Admin/Blogs/Create', [
            'authors' => $authors
        ]);
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $request->validate([
            'title_en' => 'required|string|max:255',
            'title_bn' => 'required|string|max:255',
            'slug' => 'nullable|string|max:255|unique:blogs,slug',
            'content' => 'required|string',
            'excerpt' => 'nullable|string|max:500',
            'featured_image_id' => 'nullable|exists:media,id',
            'meta_description' => 'nullable|string|max:500',
            'status' => 'required|in:published,draft',
            'published_at' => 'nullable|date',
            'author_id' => 'nullable|exists:users,id',
            'tags' => 'nullable|string',
            'categories' => 'nullable|string',
            'sort_order' => 'nullable|integer|min:0',
        ]);

        $data = $request->all();

        // Handle title translations
        $data['title'] = [
            'en' => $request->title_en,
            'bn' => $request->title_bn,
        ];

        // Generate slug if not provided
        if (empty($data['slug'])) {
            $data['slug'] = Blog::generateSlug($request->title_en);
        } else {
            $data['slug'] = Str::slug($data['slug']);
        }

        // Handle tags and categories
        if ($request->filled('tags')) {
            $data['tags'] = array_map('trim', explode(',', $request->tags));
        }
        if ($request->filled('categories')) {
            $data['categories'] = array_map('trim', explode(',', $request->categories));
        }

        // Set published_at if status is published and no date is set
        if ($data['status'] === 'published' && empty($data['published_at'])) {
            $data['published_at'] = now();
        }

        $blog = Blog::create($data);

        return redirect()->route('admin.blogs.index')
                        ->with('success', __('blogs.created_successfully'));
    }

    /**
     * Display the specified resource.
     */
    public function show(Blog $blog)
    {
        $blog->load(['featuredImage', 'author', 'creator', 'updater']);
        return Inertia::render('Admin/Blogs/Show', [
            'blog' => $blog
        ]);
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(Blog $blog)
    {
        $authors = User::all();
        return Inertia::render('Admin/Blogs/Edit', [
            'blog' => $blog,
            'authors' => $authors
        ]);
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, Blog $blog)
    {
        $request->validate([
            'title_en' => 'required|string|max:255',
            'title_bn' => 'required|string|max:255',
            'slug' => 'nullable|string|max:255|unique:blogs,slug,' . $blog->id,
            'content' => 'required|string',
            'excerpt' => 'nullable|string|max:500',
            'featured_image_id' => 'nullable|exists:media,id',
            'meta_description' => 'nullable|string|max:500',
            'status' => 'required|in:published,draft',
            'published_at' => 'nullable|date',
            'author_id' => 'nullable|exists:users,id',
            'tags' => 'nullable|string',
            'categories' => 'nullable|string',
            'sort_order' => 'nullable|integer|min:0',
        ]);

        $data = $request->all();

        // Handle title translations
        $data['title'] = [
            'en' => $request->title_en,
            'bn' => $request->title_bn,
        ];

        // Generate slug if not provided
        if (empty($data['slug'])) {
            $data['slug'] = Blog::generateSlug($request->title_en);
        } else {
            $data['slug'] = Str::slug($data['slug']);
        }

        // Handle tags and categories
        if ($request->filled('tags')) {
            $data['tags'] = array_map('trim', explode(',', $request->tags));
        } else {
            $data['tags'] = null;
        }

        if ($request->filled('categories')) {
            $data['categories'] = array_map('trim', explode(',', $request->categories));
        } else {
            $data['categories'] = null;
        }

        // Set published_at if status is published and no date is set
        if ($data['status'] === 'published' && empty($data['published_at']) && $blog->status !== 'published') {
            $data['published_at'] = now();
        }

        $blog->update($data);

        return redirect()->route('admin.blogs.index')
                        ->with('success', __('blogs.updated_successfully'));
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Blog $blog)
    {
        $blog->delete();

        return redirect()->route('admin.blogs.index')
                        ->with('success', __('blogs.deleted_successfully'));
    }

    /**
     * Bulk actions
     */
    public function bulkAction(Request $request)
    {
        $request->validate([
            'action' => 'required|in:publish,draft,delete',
            'ids' => 'required|array|min:1',
            'ids.*' => 'exists:blogs,id',
        ]);

        $blogs = Blog::whereIn('id', $request->ids);

        switch ($request->action) {
            case 'publish':
                $blogs->update([
                    'status' => 'published',
                    'published_at' => now()
                ]);
                $message = __('blogs.bulk_published');
                break;
            case 'draft':
                $blogs->update(['status' => 'draft']);
                $message = __('blogs.bulk_drafted');
                break;
            case 'delete':
                $blogs->delete();
                $message = __('blogs.bulk_deleted');
                break;
        }

        return redirect()->route('admin.blogs.index')
                        ->with('success', $message);
    }
}
