<?php

namespace App\Http\Requests\Tenant;

use Illuminate\Foundation\Http\FormRequest;

class UpdateFoodRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        $foodId = $this->route('food')->id;

        return [
            'food_category_id' => 'sometimes|exists:food_categories,id',
            'name' => 'sometimes|string|max:255',
            'slug' => 'sometimes|string|max:255|unique:foods,slug,' . $foodId,
            'description' => 'sometimes|nullable|string|max:1000',
            'ingredients' => 'sometimes|nullable|string|max:1000',
            'images' => 'sometimes|array|max:5',
            'images.*' => 'image|mimes:jpeg,png,jpg,webp|max:2048',
            
            // Pricing
            'base_price' => 'sometimes|numeric|min:0|max:9999.99',
            'sale_price' => 'sometimes|nullable|numeric|min:0|max:9999.99',
            'cost_price' => 'sometimes|nullable|numeric|min:0|max:9999.99',
            
            // Nutritional information
            'calories' => 'sometimes|nullable|integer|min:0|max:9999',
            'protein' => 'sometimes|nullable|numeric|min:0|max:999.99',
            'carbs' => 'sometimes|nullable|numeric|min:0|max:999.99',
            'fat' => 'sometimes|nullable|numeric|min:0|max:999.99',
            'fiber' => 'sometimes|nullable|numeric|min:0|max:999.99',
            'sugar' => 'sometimes|nullable|numeric|min:0|max:999.99',
            'sodium' => 'sometimes|nullable|numeric|min:0|max:9999.99',
            
            // Dietary flags
            'is_vegetarian' => 'sometimes|boolean',
            'is_vegan' => 'sometimes|boolean',
            'is_gluten_free' => 'sometimes|boolean',
            'is_dairy_free' => 'sometimes|boolean',
            'is_nut_free' => 'sometimes|boolean',
            'is_spicy' => 'sometimes|boolean',
            'is_halal' => 'sometimes|boolean',
            'is_kosher' => 'sometimes|boolean',
            
            // Kitchen information
            'preparation_time' => 'sometimes|nullable|integer|min:1|max:300',
            'cooking_time' => 'sometimes|nullable|integer|min:1|max:300',
            'spice_level' => 'sometimes|nullable|in:none,mild,medium,hot,very_hot',
            'kitchen_notes' => 'sometimes|nullable|string|max:500',
            
            // Display settings
            'sort_order' => 'sometimes|nullable|integer|min:0',
            'is_featured' => 'sometimes|boolean',
            'is_popular' => 'sometimes|boolean',
            'is_new' => 'sometimes|boolean',
            'is_available' => 'sometimes|boolean',
            'is_active' => 'sometimes|boolean',
            
            // Availability
            'available_days' => 'sometimes|nullable|array',
            'available_days.*' => 'integer|between:0,6',
            'available_from' => 'sometimes|nullable|date_format:H:i',
            'available_until' => 'sometimes|nullable|date_format:H:i',
            
            // Stock management
            'track_quantity' => 'sometimes|boolean',
            'quantity_available' => 'sometimes|nullable|integer|min:0',
            'minimum_quantity' => 'sometimes|nullable|integer|min:0',
            
            // SEO
            'meta_title' => 'sometimes|nullable|string|max:255',
            'meta_description' => 'sometimes|nullable|string|max:500',
            'meta_keywords' => 'sometimes|nullable|array',
            'meta_keywords.*' => 'string|max:50',
        ];
    }

    /**
     * Configure the validator instance.
     */
    public function withValidator($validator)
    {
        $validator->after(function ($validator) {
            // Validate sale price is less than base price
            if ($this->has('sale_price') && $this->has('base_price')) {
                if ($this->sale_price && $this->sale_price >= $this->base_price) {
                    $validator->errors()->add('sale_price', 'Sale price must be less than base price.');
                }
            }

            // Validate available times
            if ($this->has('available_from') && $this->has('available_until')) {
                if ($this->available_from && $this->available_until) {
                    if ($this->available_from >= $this->available_until) {
                        $validator->errors()->add('available_until', 'Available until time must be after available from time.');
                    }
                }
            }

            // Validate vegan consistency
            if ($this->has('is_vegan') && $this->is_vegan) {
                if ($this->has('is_vegetarian') && !$this->is_vegetarian) {
                    $validator->errors()->add('is_vegetarian', 'Vegan foods must also be marked as vegetarian.');
                }
                if ($this->has('is_dairy_free') && !$this->is_dairy_free) {
                    $validator->errors()->add('is_dairy_free', 'Vegan foods must also be dairy-free.');
                }
            }

            // Validate quantity tracking
            if ($this->has('track_quantity') && $this->track_quantity) {
                if (!$this->has('quantity_available') || $this->quantity_available === null) {
                    $validator->errors()->add('quantity_available', 'Quantity is required when tracking quantity.');
                }
            }
        });
    }
}
