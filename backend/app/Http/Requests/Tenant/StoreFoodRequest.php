<?php

namespace App\Http\Requests\Tenant;

use Illuminate\Foundation\Http\FormRequest;

class StoreFoodRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        return [
            'food_category_id' => 'required|exists:food_categories,id',
            'name' => 'required|string|max:255',
            'slug' => 'nullable|string|max:255|unique:foods,slug',
            'description' => 'nullable|string|max:1000',
            'ingredients' => 'nullable|string|max:1000',
            'images' => 'nullable|array|max:5',
            'images.*' => 'image|mimes:jpeg,png,jpg,webp|max:2048',
            
            // Pricing
            'base_price' => 'required|numeric|min:0|max:9999.99',
            'sale_price' => 'nullable|numeric|min:0|max:9999.99|lt:base_price',
            'cost_price' => 'nullable|numeric|min:0|max:9999.99',
            
            // Nutritional information
            'calories' => 'nullable|integer|min:0|max:9999',
            'protein' => 'nullable|numeric|min:0|max:999.99',
            'carbs' => 'nullable|numeric|min:0|max:999.99',
            'fat' => 'nullable|numeric|min:0|max:999.99',
            'fiber' => 'nullable|numeric|min:0|max:999.99',
            'sugar' => 'nullable|numeric|min:0|max:999.99',
            'sodium' => 'nullable|numeric|min:0|max:9999.99',
            
            // Dietary flags
            'is_vegetarian' => 'boolean',
            'is_vegan' => 'boolean',
            'is_gluten_free' => 'boolean',
            'is_dairy_free' => 'boolean',
            'is_nut_free' => 'boolean',
            'is_spicy' => 'boolean',
            'is_halal' => 'boolean',
            'is_kosher' => 'boolean',
            
            // Kitchen information
            'preparation_time' => 'nullable|integer|min:1|max:300',
            'cooking_time' => 'nullable|integer|min:1|max:300',
            'spice_level' => 'nullable|in:none,mild,medium,hot,very_hot',
            'kitchen_notes' => 'nullable|string|max:500',
            
            // Display settings
            'sort_order' => 'nullable|integer|min:0',
            'is_featured' => 'boolean',
            'is_popular' => 'boolean',
            'is_new' => 'boolean',
            'is_available' => 'boolean',
            'is_active' => 'boolean',
            
            // Availability
            'available_days' => 'nullable|array',
            'available_days.*' => 'integer|between:0,6',
            'available_from' => 'nullable|date_format:H:i',
            'available_until' => 'nullable|date_format:H:i|after:available_from',
            
            // Stock management
            'track_quantity' => 'boolean',
            'quantity_available' => 'nullable|integer|min:0|required_if:track_quantity,true',
            'minimum_quantity' => 'nullable|integer|min:0',
            
            // SEO
            'meta_title' => 'nullable|string|max:255',
            'meta_description' => 'nullable|string|max:500',
            'meta_keywords' => 'nullable|array',
            'meta_keywords.*' => 'string|max:50',
        ];
    }

    /**
     * Get custom messages for validator errors.
     */
    public function messages(): array
    {
        return [
            'food_category_id.required' => 'Please select a food category.',
            'food_category_id.exists' => 'Selected category does not exist.',
            'name.required' => 'Food name is required.',
            'base_price.required' => 'Base price is required.',
            'base_price.min' => 'Price must be greater than 0.',
            'sale_price.lt' => 'Sale price must be less than base price.',
            'images.max' => 'Maximum 5 images allowed.',
            'images.*.image' => 'File must be an image.',
            'images.*.mimes' => 'Image must be jpeg, png, jpg, or webp format.',
            'images.*.max' => 'Image size cannot exceed 2MB.',
            'available_until.after' => 'Available until time must be after available from time.',
            'quantity_available.required_if' => 'Quantity is required when tracking quantity.',
        ];
    }

    /**
     * Configure the validator instance.
     */
    public function withValidator($validator)
    {
        $validator->after(function ($validator) {
            // Validate vegan foods are also vegetarian
            if ($this->is_vegan && !$this->is_vegetarian) {
                $validator->errors()->add('is_vegetarian', 'Vegan foods must also be marked as vegetarian.');
            }

            // Validate dairy-free and vegan consistency
            if ($this->is_vegan && !$this->is_dairy_free) {
                $validator->errors()->add('is_dairy_free', 'Vegan foods must also be dairy-free.');
            }
        });
    }

    /**
     * Prepare the data for validation.
     */
    protected function prepareForValidation()
    {
        // Set restaurant_id from current tenant
        $this->merge([
            'restaurant_id' => tenant('id')
        ]);

        // Set default values
        $this->merge([
            'is_vegetarian' => $this->boolean('is_vegetarian'),
            'is_vegan' => $this->boolean('is_vegan'),
            'is_gluten_free' => $this->boolean('is_gluten_free'),
            'is_dairy_free' => $this->boolean('is_dairy_free'),
            'is_nut_free' => $this->boolean('is_nut_free'),
            'is_spicy' => $this->boolean('is_spicy'),
            'is_halal' => $this->boolean('is_halal'),
            'is_kosher' => $this->boolean('is_kosher'),
            'is_featured' => $this->boolean('is_featured'),
            'is_popular' => $this->boolean('is_popular'),
            'is_new' => $this->boolean('is_new'),
            'is_available' => $this->boolean('is_available', true),
            'is_active' => $this->boolean('is_active', true),
            'track_quantity' => $this->boolean('track_quantity'),
        ]);
    }
}
