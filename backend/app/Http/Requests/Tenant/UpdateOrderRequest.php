<?php

namespace App\Http\Requests\Tenant;

use Illuminate\Foundation\Http\FormRequest;

class UpdateOrderRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        return [
            'customer_name' => 'sometimes|string|max:255',
            'customer_email' => 'sometimes|nullable|email|max:255',
            'customer_phone' => 'sometimes|string|max:20',
            
            // Delivery information
            'delivery_address' => 'sometimes|string|max:500',
            'delivery_instructions' => 'sometimes|nullable|string|max:500',
            
            // Special instructions
            'special_instructions' => 'sometimes|nullable|string|max:1000',
            'kitchen_notes' => 'sometimes|nullable|string|max:500',
            'admin_notes' => 'sometimes|nullable|string|max:500',
            
            // Scheduling
            'scheduled_at' => 'sometimes|nullable|date|after:now',
            
            // Staff assignments
            'assigned_to' => 'sometimes|nullable|exists:employees,id',
            'prepared_by' => 'sometimes|nullable|exists:employees,id',
            'served_by' => 'sometimes|nullable|exists:employees,id',
            
            // Estimated preparation time
            'estimated_preparation_time' => 'sometimes|nullable|integer|min:1|max:300',
        ];
    }

    /**
     * Get custom messages for validator errors.
     */
    public function messages(): array
    {
        return [
            'scheduled_at.after' => 'Scheduled time must be in the future.',
            'estimated_preparation_time.min' => 'Preparation time must be at least 1 minute.',
            'estimated_preparation_time.max' => 'Preparation time cannot exceed 300 minutes.',
        ];
    }
}
