<?php

namespace App\Http\Requests\Tenant;

use Illuminate\Foundation\Http\FormRequest;

class UpdateCustomerRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        $customerId = $this->route('customer')->id;

        return [
            'first_name' => 'sometimes|string|max:255',
            'last_name' => 'sometimes|string|max:255',
            'email' => 'sometimes|email|max:255|unique:customers,email,' . $customerId,
            'phone' => 'sometimes|string|max:20|unique:customers,phone,' . $customerId,
            'password' => 'sometimes|nullable|string|min:8|confirmed',
            
            // Personal information
            'date_of_birth' => 'sometimes|nullable|date|before:today',
            'gender' => 'sometimes|nullable|in:male,female,other,prefer_not_to_say',
            'anniversary_date' => 'sometimes|nullable|date',
            
            // Preferences
            'preferred_language' => 'sometimes|nullable|string|max:10',
            'dietary_preferences' => 'sometimes|nullable|array',
            'dietary_preferences.*' => 'string|max:50',
            'allergies' => 'sometimes|nullable|array',
            'allergies.*' => 'string|max:50',
            'favorite_cuisines' => 'sometimes|nullable|array',
            'favorite_cuisines.*' => 'string|max:50',
            
            // Communication preferences
            'email_notifications' => 'sometimes|boolean',
            'sms_notifications' => 'sometimes|boolean',
            'push_notifications' => 'sometimes|boolean',
            'marketing_emails' => 'sometimes|boolean',
            'birthday_offers' => 'sometimes|boolean',
            
            // Status and tier
            'status' => 'sometimes|in:active,inactive,blocked',
            'customer_tier' => 'sometimes|in:bronze,silver,gold,platinum',
            
            // Notes
            'notes' => 'sometimes|nullable|string|max:1000',
        ];
    }

    /**
     * Get custom messages for validator errors.
     */
    public function messages(): array
    {
        return [
            'email.unique' => 'This email address is already registered.',
            'phone.unique' => 'This phone number is already registered.',
            'password.min' => 'Password must be at least 8 characters.',
            'password.confirmed' => 'Password confirmation does not match.',
            'date_of_birth.before' => 'Date of birth must be in the past.',
        ];
    }
}
