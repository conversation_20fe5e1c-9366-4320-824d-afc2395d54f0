<?php

namespace App\Http\Requests\Tenant;

use Illuminate\Foundation\Http\FormRequest;

class StoreCustomerRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        return [
            'first_name' => 'required|string|max:255',
            'last_name' => 'required|string|max:255',
            'email' => 'required|email|max:255|unique:customers,email',
            'phone' => 'required|string|max:20|unique:customers,phone',
            'password' => 'nullable|string|min:8|confirmed',
            
            // Personal information
            'date_of_birth' => 'nullable|date|before:today',
            'gender' => 'nullable|in:male,female,other,prefer_not_to_say',
            'anniversary_date' => 'nullable|date',
            
            // Preferences
            'preferred_language' => 'nullable|string|max:10',
            'dietary_preferences' => 'nullable|array',
            'dietary_preferences.*' => 'string|max:50',
            'allergies' => 'nullable|array',
            'allergies.*' => 'string|max:50',
            'favorite_cuisines' => 'nullable|array',
            'favorite_cuisines.*' => 'string|max:50',
            
            // Communication preferences
            'email_notifications' => 'boolean',
            'sms_notifications' => 'boolean',
            'push_notifications' => 'boolean',
            'marketing_emails' => 'boolean',
            'birthday_offers' => 'boolean',
            
            // Status and tier
            'status' => 'nullable|in:active,inactive,blocked',
            'customer_tier' => 'nullable|in:bronze,silver,gold,platinum',
            
            // Notes
            'notes' => 'nullable|string|max:1000',
            
            // Address (optional, can be added separately)
            'address' => 'nullable|array',
            'address.type' => 'required_with:address|in:home,work,other',
            'address.label' => 'nullable|string|max:100',
            'address.address_line_1' => 'required_with:address|string|max:255',
            'address.address_line_2' => 'nullable|string|max:255',
            'address.city' => 'required_with:address|string|max:100',
            'address.state' => 'nullable|string|max:100',
            'address.postal_code' => 'required_with:address|string|max:20',
            'address.country' => 'required_with:address|string|max:100',
            'address.latitude' => 'nullable|numeric|between:-90,90',
            'address.longitude' => 'nullable|numeric|between:-180,180',
            'address.delivery_instructions' => 'nullable|string|max:500',
            'address.is_default' => 'boolean',
        ];
    }

    /**
     * Get custom messages for validator errors.
     */
    public function messages(): array
    {
        return [
            'first_name.required' => 'First name is required.',
            'last_name.required' => 'Last name is required.',
            'email.required' => 'Email address is required.',
            'email.unique' => 'This email address is already registered.',
            'phone.required' => 'Phone number is required.',
            'phone.unique' => 'This phone number is already registered.',
            'password.min' => 'Password must be at least 8 characters.',
            'password.confirmed' => 'Password confirmation does not match.',
            'date_of_birth.before' => 'Date of birth must be in the past.',
            'address.address_line_1.required_with' => 'Address line 1 is required when adding an address.',
            'address.city.required_with' => 'City is required when adding an address.',
            'address.postal_code.required_with' => 'Postal code is required when adding an address.',
            'address.country.required_with' => 'Country is required when adding an address.',
        ];
    }

    /**
     * Prepare the data for validation.
     */
    protected function prepareForValidation()
    {
        // Set restaurant_id from current tenant
        $this->merge([
            'restaurant_id' => tenant('id')
        ]);

        // Set default values
        $this->merge([
            'email_notifications' => $this->boolean('email_notifications', true),
            'sms_notifications' => $this->boolean('sms_notifications', true),
            'push_notifications' => $this->boolean('push_notifications', true),
            'marketing_emails' => $this->boolean('marketing_emails', true),
            'birthday_offers' => $this->boolean('birthday_offers', true),
            'status' => $this->get('status', 'active'),
            'customer_tier' => $this->get('customer_tier', 'bronze'),
        ]);

        // Set address defaults if address is provided
        if ($this->has('address')) {
            $address = $this->get('address');
            $address['is_default'] = $this->boolean('address.is_default', true);
            $this->merge(['address' => $address]);
        }
    }
}
