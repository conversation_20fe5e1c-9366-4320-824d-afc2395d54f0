<?php

namespace App\Http\Requests\Tenant;

use Illuminate\Foundation\Http\FormRequest;

class StoreOrderRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        return [
            'customer_id' => 'nullable|exists:customers,id',
            'table_id' => 'nullable|exists:tables,id',
            'customer_address_id' => 'nullable|exists:customer_addresses,id',
            'order_type' => 'required|in:dine_in,takeaway,delivery,online',
            
            // Customer information for guest orders
            'customer_name' => 'required_without:customer_id|string|max:255',
            'customer_email' => 'nullable|email|max:255',
            'customer_phone' => 'required_without:customer_id|string|max:20',
            
            // Delivery information
            'delivery_address' => 'required_if:order_type,delivery|string|max:500',
            'delivery_instructions' => 'nullable|string|max:500',
            'delivery_latitude' => 'nullable|numeric|between:-90,90',
            'delivery_longitude' => 'nullable|numeric|between:-180,180',
            
            // Order items
            'items' => 'required|array|min:1',
            'items.*.food_id' => 'required|exists:foods,id',
            'items.*.food_variant_id' => 'nullable|exists:food_variants,id',
            'items.*.quantity' => 'required|integer|min:1|max:50',
            'items.*.special_instructions' => 'nullable|string|max:500',
            'items.*.addons' => 'nullable|array',
            'items.*.addons.*.food_addon_id' => 'required|exists:food_addons,id',
            'items.*.addons.*.quantity' => 'required|integer|min:1|max:10',
            
            // Pricing (will be calculated server-side but can be provided for validation)
            'subtotal' => 'nullable|numeric|min:0',
            'tax_amount' => 'nullable|numeric|min:0',
            'delivery_fee' => 'nullable|numeric|min:0',
            'service_fee' => 'nullable|numeric|min:0',
            'tip_amount' => 'nullable|numeric|min:0',
            
            // Payment
            'payment_method' => 'nullable|in:cash,card,online,wallet,bank_transfer',
            
            // Special instructions
            'special_instructions' => 'nullable|string|max:1000',
            
            // Scheduling
            'scheduled_at' => 'nullable|date|after:now',
            
            // Coupon
            'coupon_code' => 'nullable|string|exists:coupons,code',
        ];
    }

    /**
     * Get custom messages for validator errors.
     */
    public function messages(): array
    {
        return [
            'customer_name.required_without' => 'Customer name is required for guest orders.',
            'customer_phone.required_without' => 'Customer phone is required for guest orders.',
            'delivery_address.required_if' => 'Delivery address is required for delivery orders.',
            'items.required' => 'At least one item is required.',
            'items.*.food_id.required' => 'Food item is required.',
            'items.*.food_id.exists' => 'Selected food item does not exist.',
            'items.*.quantity.required' => 'Quantity is required for each item.',
            'items.*.quantity.min' => 'Quantity must be at least 1.',
            'items.*.quantity.max' => 'Quantity cannot exceed 50.',
            'scheduled_at.after' => 'Scheduled time must be in the future.',
        ];
    }

    /**
     * Configure the validator instance.
     */
    public function withValidator($validator)
    {
        $validator->after(function ($validator) {
            // Validate delivery requirements
            if ($this->order_type === 'delivery') {
                if (!$this->customer_address_id && !$this->delivery_address) {
                    $validator->errors()->add('delivery_address', 'Delivery address is required for delivery orders.');
                }
            }

            // Validate dine-in requirements
            if ($this->order_type === 'dine_in' && !$this->table_id) {
                $validator->errors()->add('table_id', 'Table selection is required for dine-in orders.');
            }

            // Validate customer information consistency
            if ($this->customer_id && ($this->customer_name || $this->customer_phone)) {
                $validator->errors()->add('customer_id', 'Cannot specify both customer ID and guest customer information.');
            }
        });
    }

    /**
     * Prepare the data for validation.
     */
    protected function prepareForValidation()
    {
        // Set restaurant_id from current tenant
        $this->merge([
            'restaurant_id' => tenant('id')
        ]);
    }
}
