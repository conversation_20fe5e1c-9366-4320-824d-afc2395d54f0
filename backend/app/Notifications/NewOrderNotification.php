<?php

namespace App\Notifications;

use App\Models\Tenant\Order;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;

class NewOrderNotification extends Notification implements ShouldQueue
{
    use Queueable;

    public $order;

    /**
     * Create a new notification instance.
     */
    public function __construct(Order $order)
    {
        $this->order = $order;
    }

    /**
     * Get the notification's delivery channels.
     *
     * @return array<int, string>
     */
    public function via(object $notifiable): array
    {
        return ['database', 'mail'];
    }

    /**
     * Get the mail representation of the notification.
     */
    public function toMail(object $notifiable): MailMessage
    {
        return (new MailMessage)
            ->subject('New Order Received - ' . $this->order->order_number)
            ->greeting('New Order Alert!')
            ->line('A new order has been placed and requires your attention.')
            ->line('Order Number: ' . $this->order->order_number)
            ->line('Customer: ' . $this->order->customer->name)
            ->line('Total Amount: $' . number_format($this->order->total, 2))
            ->line('Payment Method: ' . ucwords(str_replace('_', ' ', $this->order->payment_method)))
            ->action('View Order Details', route('orders.show', $this->order))
            ->line('Please process this order as soon as possible.');
    }

    /**
     * Get the database representation of the notification.
     *
     * @return array<string, mixed>
     */
    public function toDatabase(object $notifiable): array
    {
        return [
            'order_id' => $this->order->id,
            'order_number' => $this->order->order_number,
            'customer_name' => $this->order->customer->name,
            'total' => $this->order->total,
            'type' => 'new_order',
            'message' => "New order #{$this->order->order_number} from {$this->order->customer->name}",
        ];
    }

    /**
     * Get the array representation of the notification.
     *
     * @return array<string, mixed>
     */
    public function toArray(object $notifiable): array
    {
        return $this->toDatabase($notifiable);
    }
}
