<?php

namespace App\Notifications;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;

class OrderStatusNotification extends Notification implements ShouldQueue
{
    use Queueable;

    public $order;
    public $status;
    public $user;

    /**
     * Create a new notification instance.
     */
    public function __construct($order, $status, $user = null)
    {
        $this->order = $order;
        $this->status = $status;
        $this->user = $user;
    }

    /**
     * Get the notification's delivery channels.
     *
     * @return array<int, string>
     */
    public function via(object $notifiable): array
    {
        return ['database', 'broadcast'];
    }

    /**
     * Get the mail representation of the notification.
     */
    public function toMail(object $notifiable): MailMessage
    {
        return (new MailMessage)
            ->subject("Order #{$this->order->order_number} - Status Updated")
            ->line("Order #{$this->order->order_number} status has been updated to: {$this->status}")
            ->action('View Order', url("/orders/{$this->order->id}"))
            ->line('Thank you for using our restaurant management system!');
    }

    /**
     * Get the array representation of the notification.
     *
     * @return array<string, mixed>
     */
    public function toArray(object $notifiable): array
    {
        return [
            'order_id' => $this->order->id,
            'order_number' => $this->order->order_number,
            'status' => $this->status,
            'customer_name' => $this->order->customer_name,
            'table_number' => $this->order->table_number,
            'total_amount' => $this->order->total_amount,
            'updated_by' => $this->user ? $this->user->name : 'System',
            'message' => "Order #{$this->order->order_number} status updated to {$this->status}",
            'type' => 'order_status',
            'priority' => $this->getPriority(),
        ];
    }

    /**
     * Get the broadcastable representation of the notification.
     */
    public function toBroadcast(object $notifiable): array
    {
        return $this->toArray($notifiable);
    }

    /**
     * Get notification priority based on status
     */
    private function getPriority(): string
    {
        return match($this->status) {
            'cancelled', 'failed' => 'high',
            'ready', 'delivered' => 'medium',
            default => 'normal'
        };
    }
}
