<?php

namespace App\Jobs\Tenant;

use App\Models\Tenant;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;
use Exception;

class TestQueueJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    public $tenant;
    public $testMessage;
    public $tries = 1;
    public $timeout = 60;

    /**
     * Create a new job instance.
     */
    public function __construct(Tenant $tenant, string $testMessage = 'Test message')
    {
        $this->tenant = $tenant;
        $this->testMessage = $testMessage;
        $this->onQueue('tenant-creation');
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        Log::info("TestQueueJob executing for tenant: {$this->tenant->id}");
        Log::info("Test message: {$this->testMessage}");
        Log::info("Tenant name: {$this->tenant->name}");
        Log::info("Current time: " . now()->toDateTimeString());
        
        // Simulate some work
        sleep(2);
        
        Log::info("TestQueueJob completed for tenant: {$this->tenant->id}");
    }

    /**
     * Handle a job failure.
     */
    public function failed(Exception $exception): void
    {
        Log::error("TestQueueJob failed for tenant {$this->tenant->id}: " . $exception->getMessage());
    }
}
