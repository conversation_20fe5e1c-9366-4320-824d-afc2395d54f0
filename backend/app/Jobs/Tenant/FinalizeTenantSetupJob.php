<?php

namespace App\Jobs\Tenant;

use App\Models\Tenant;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;
use Exception;

class FinalizeTenantSetupJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    public $tenant;
    public $tries = 3;
    public $timeout = 120; // 2 minutes
    public $backoff = [30, 60, 120];

    /**
     * Create a new job instance.
     */
    public function __construct(Tenant $tenant)
    {
        $this->tenant = $tenant;
        $this->onQueue('tenant-creation');
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        Log::info("Finalizing tenant setup for: {$this->tenant->id}");

        try {
            // Update tenant status
            $this->tenant->update(['setup_status' => 'finalizing']);

            // Perform final setup tasks
            $this->performFinalChecks();
            $this->updateTenantStatus();
            $this->logSetupCompletion();

            // Mark tenant as fully set up
            $this->tenant->update([
                'setup_status' => 'completed',
                'setup_completed_at' => now(),
                'setup_error' => null,
                'is_active' => true,
            ]);

            Log::info("Tenant setup finalized successfully for: {$this->tenant->id}");

        } catch (Exception $e) {
            Log::error("Tenant setup finalization failed for {$this->tenant->id}: " . $e->getMessage());
            
            $this->tenant->update([
                'setup_status' => 'finalization_failed',
                'setup_error' => $e->getMessage()
            ]);

            throw $e;
        }
    }

    /**
     * Perform final checks to ensure everything is set up correctly
     */
    private function performFinalChecks(): void
    {
        try {
            // Switch to tenant context
            tenancy()->initialize($this->tenant);

            // Check if essential data exists
            $this->checkEssentialData();

            // Verify manager user exists
            $this->verifyManagerUser();

            // Check file system setup
            $this->checkFileSystemSetup();

            // Switch back to central context
            tenancy()->end();

            Log::info("Final checks passed for tenant: {$this->tenant->id}");

        } catch (Exception $e) {
            tenancy()->end();
            throw new Exception("Final checks failed: " . $e->getMessage());
        }
    }

    /**
     * Check if essential data exists in tenant database
     */
    private function checkEssentialData(): void
    {
        // Check categories
        $categoryModel = app(\App\Models\Tenant\Category::class);
        if ($categoryModel->count() === 0) {
            throw new Exception("No categories found in tenant database");
        }

        // Check departments
        $departmentModel = app(\App\Models\Tenant\Department::class);
        if ($departmentModel->count() === 0) {
            throw new Exception("No departments found in tenant database");
        }

        // Check branches
        $branchModel = app(\App\Models\Tenant\Branch::class);
        if ($branchModel->count() === 0) {
            throw new Exception("No branches found in tenant database");
        }

        // Check settings
        $settingModel = app(\App\Models\Tenant\Setting::class);
        if ($settingModel->count() === 0) {
            throw new Exception("No settings found in tenant database");
        }

        Log::info("Essential data verification passed for tenant: {$this->tenant->id}");
    }

    /**
     * Verify manager user exists and is properly configured
     */
    private function verifyManagerUser(): void
    {
        $userModel = app(\App\Models\Tenant\User::class);
        
        $manager = $userModel->where('email', $this->tenant->email)
                            ->where('role', 'restaurant_manager')
                            ->first();

        if (!$manager) {
            throw new Exception("Manager user not found in tenant database");
        }

        if (!$manager->is_active) {
            throw new Exception("Manager user is not active");
        }

        if (!$manager->email_verified_at) {
            throw new Exception("Manager email is not verified");
        }

        Log::info("Manager user verification passed for tenant: {$this->tenant->id}");
    }

    /**
     * Check if file system is properly set up
     */
    private function checkFileSystemSetup(): void
    {
        $tenantId = $this->tenant->id;
        
        // Check if tenant directories exist
        $requiredDirectories = [
            "tenants/{$tenantId}/uploads",
            "tenants/{$tenantId}/uploads/menu-items",
            "tenants/{$tenantId}/uploads/categories",
        ];

        foreach ($requiredDirectories as $directory) {
            if (!\Storage::disk('public')->exists($directory)) {
                throw new Exception("Required directory not found: {$directory}");
            }
        }

        Log::info("File system verification passed for tenant: {$this->tenant->id}");
    }

    /**
     * Update tenant status and subscription information
     */
    private function updateTenantStatus(): void
    {
        // Activate subscription if it's in trial
        if ($this->tenant->subscription_status === 'trial') {
            $this->tenant->update([
                'subscription_status' => 'active',
                'subscription_started_at' => now(),
            ]);
        }

        // Update tenant metadata
        $this->tenant->update([
            'last_activity_at' => now(),
            'setup_duration_minutes' => $this->tenant->created_at->diffInMinutes(now()),
        ]);

        Log::info("Tenant status updated for: {$this->tenant->id}");
    }

    /**
     * Log setup completion details
     */
    private function logSetupCompletion(): void
    {
        $setupDuration = $this->tenant->created_at->diffInMinutes(now());
        
        Log::info("Tenant setup completed", [
            'tenant_id' => $this->tenant->id,
            'tenant_name' => $this->tenant->name,
            'tenant_email' => $this->tenant->email,
            'setup_duration_minutes' => $setupDuration,
            'subscription_plan' => $this->tenant->subscriptionPlan?->name,
            'completed_at' => now()->toDateTimeString(),
        ]);

        // Log to a separate setup completion log for analytics
        \Log::channel('tenant-setup')->info("Tenant setup completed: {$this->tenant->id} in {$setupDuration} minutes");
    }

    /**
     * Handle a job failure.
     */
    public function failed(Exception $exception): void
    {
        Log::error("FinalizeTenantSetupJob failed permanently for tenant {$this->tenant->id}: " . $exception->getMessage());

        $this->tenant->update([
            'setup_status' => 'finalization_permanently_failed',
            'setup_error' => $exception->getMessage()
        ]);

        \App\Jobs\Tenant\NotifyTenantCreationFailedJob::dispatch($this->tenant, 'Setup Finalization', $exception->getMessage());
    }
}
