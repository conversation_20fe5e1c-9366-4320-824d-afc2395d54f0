<?php

namespace App\Jobs\Tenant;

use App\Models\Tenant;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;
use Exception;

class SetupThirdPartyServicesJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    public $tenant;
    public $tries = 3;
    public $timeout = 300; // 5 minutes
    public $backoff = [60, 120, 300];

    /**
     * Create a new job instance.
     */
    public function __construct(Tenant $tenant)
    {
        $this->tenant = $tenant;
        $this->onQueue('tenant-creation');
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        Log::info("Starting third-party services setup for tenant: {$this->tenant->id}");

        try {
            // Update tenant status
            $this->tenant->update(['setup_status' => 'setting_up_services']);

            // Setup payment gateways
            $this->setupPaymentGateways();

            // Setup email services
            $this->setupEmailServices();

            // Setup SMS services
            $this->setupSMSServices();

            // Setup analytics
            $this->setupAnalytics();

            // Setup backup services
            $this->setupBackupServices();

            // Update status
            $this->tenant->update(['setup_status' => 'services_setup']);

            Log::info("Third-party services setup completed for tenant: {$this->tenant->id}");

        } catch (Exception $e) {
            Log::error("Third-party services setup failed for tenant {$this->tenant->id}: " . $e->getMessage());
            
            $this->tenant->update([
                'setup_status' => 'services_setup_failed',
                'setup_error' => $e->getMessage()
            ]);

            throw $e;
        }
    }

    /**
     * Setup payment gateways for tenant
     */
    private function setupPaymentGateways(): void
    {
        try {
            // Switch to tenant context
            tenancy()->initialize($this->tenant);

            $settingModel = app(\App\Models\Tenant\Setting::class);

            // Setup default payment gateway settings
            $paymentSettings = [
                'payment_gateway_stripe_enabled' => 'false',
                'payment_gateway_stripe_public_key' => '',
                'payment_gateway_stripe_secret_key' => '',
                'payment_gateway_paypal_enabled' => 'false',
                'payment_gateway_paypal_client_id' => '',
                'payment_gateway_paypal_client_secret' => '',
                'payment_gateway_cash_enabled' => 'true',
                'payment_gateway_card_enabled' => 'true',
                'default_payment_method' => 'cash',
            ];

            foreach ($paymentSettings as $key => $value) {
                $settingModel->updateOrCreate(
                    ['key' => $key],
                    ['value' => $value]
                );
            }

            // Switch back to central context
            tenancy()->end();

            Log::info("Payment gateways configured for tenant: {$this->tenant->id}");

        } catch (Exception $e) {
            tenancy()->end();
            Log::warning("Payment gateway setup failed for tenant {$this->tenant->id}: " . $e->getMessage());
            // Don't throw exception as this is not critical for basic functionality
        }
    }

    /**
     * Setup email services for tenant
     */
    private function setupEmailServices(): void
    {
        try {
            // Switch to tenant context
            tenancy()->initialize($this->tenant);

            $settingModel = app(\App\Models\Tenant\Setting::class);

            // Setup email settings
            $emailSettings = [
                'email_notifications_enabled' => 'true',
                'email_order_confirmation' => 'true',
                'email_order_ready' => 'true',
                'email_reservation_confirmation' => 'true',
                'email_marketing_enabled' => 'false',
                'smtp_host' => '',
                'smtp_port' => '587',
                'smtp_username' => '',
                'smtp_password' => '',
                'smtp_encryption' => 'tls',
                'from_email' => $this->tenant->email,
                'from_name' => $this->tenant->name,
            ];

            foreach ($emailSettings as $key => $value) {
                $settingModel->updateOrCreate(
                    ['key' => $key],
                    ['value' => $value]
                );
            }

            // Switch back to central context
            tenancy()->end();

            Log::info("Email services configured for tenant: {$this->tenant->id}");

        } catch (Exception $e) {
            tenancy()->end();
            Log::warning("Email services setup failed for tenant {$this->tenant->id}: " . $e->getMessage());
        }
    }

    /**
     * Setup SMS services for tenant
     */
    private function setupSMSServices(): void
    {
        try {
            // Switch to tenant context
            tenancy()->initialize($this->tenant);

            $settingModel = app(\App\Models\Tenant\Setting::class);

            // Setup SMS settings
            $smsSettings = [
                'sms_notifications_enabled' => 'false',
                'sms_order_confirmation' => 'false',
                'sms_order_ready' => 'false',
                'sms_reservation_confirmation' => 'false',
                'sms_provider' => 'twilio',
                'twilio_sid' => '',
                'twilio_token' => '',
                'twilio_from_number' => '',
            ];

            foreach ($smsSettings as $key => $value) {
                $settingModel->updateOrCreate(
                    ['key' => $key],
                    ['value' => $value]
                );
            }

            // Switch back to central context
            tenancy()->end();

            Log::info("SMS services configured for tenant: {$this->tenant->id}");

        } catch (Exception $e) {
            tenancy()->end();
            Log::warning("SMS services setup failed for tenant {$this->tenant->id}: " . $e->getMessage());
        }
    }

    /**
     * Setup analytics for tenant
     */
    private function setupAnalytics(): void
    {
        try {
            // Switch to tenant context
            tenancy()->initialize($this->tenant);

            $settingModel = app(\App\Models\Tenant\Setting::class);

            // Setup analytics settings
            $analyticsSettings = [
                'analytics_enabled' => 'true',
                'google_analytics_id' => '',
                'facebook_pixel_id' => '',
                'track_customer_behavior' => 'true',
                'track_order_patterns' => 'true',
                'generate_daily_reports' => 'true',
                'generate_weekly_reports' => 'true',
                'generate_monthly_reports' => 'true',
            ];

            foreach ($analyticsSettings as $key => $value) {
                $settingModel->updateOrCreate(
                    ['key' => $key],
                    ['value' => $value]
                );
            }

            // Switch back to central context
            tenancy()->end();

            Log::info("Analytics configured for tenant: {$this->tenant->id}");

        } catch (Exception $e) {
            tenancy()->end();
            Log::warning("Analytics setup failed for tenant {$this->tenant->id}: " . $e->getMessage());
        }
    }

    /**
     * Setup backup services for tenant
     */
    private function setupBackupServices(): void
    {
        try {
            // Switch to tenant context
            tenancy()->initialize($this->tenant);

            $settingModel = app(\App\Models\Tenant\Setting::class);

            // Setup backup settings
            $backupSettings = [
                'backup_enabled' => 'true',
                'backup_frequency' => 'daily',
                'backup_retention_days' => '30',
                'backup_include_uploads' => 'true',
                'backup_cloud_storage' => 'false',
                'backup_cloud_provider' => 's3',
                'backup_notification_email' => $this->tenant->email,
            ];

            foreach ($backupSettings as $key => $value) {
                $settingModel->updateOrCreate(
                    ['key' => $key],
                    ['value' => $value]
                );
            }

            // Switch back to central context
            tenancy()->end();

            Log::info("Backup services configured for tenant: {$this->tenant->id}");

        } catch (Exception $e) {
            tenancy()->end();
            Log::warning("Backup services setup failed for tenant {$this->tenant->id}: " . $e->getMessage());
        }
    }

    /**
     * Handle a job failure.
     */
    public function failed(Exception $exception): void
    {
        // Ensure we switch back to central context
        tenancy()->end();

        Log::error("SetupThirdPartyServicesJob failed permanently for tenant {$this->tenant->id}: " . $exception->getMessage());

        $this->tenant->update([
            'setup_status' => 'services_setup_permanently_failed',
            'setup_error' => $exception->getMessage()
        ]);

        \App\Jobs\Tenant\NotifyTenantCreationFailedJob::dispatch($this->tenant, 'Third-party Services Setup', $exception->getMessage());
    }
}
