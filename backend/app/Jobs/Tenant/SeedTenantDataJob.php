<?php

namespace App\Jobs\Tenant;

use App\Models\Tenant;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Artisan;
use Illuminate\Support\Facades\Log;
use Exception;

class SeedTenantDataJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    public $tenant;
    public $tries = 3;
    public $timeout = 600; // 10 minutes
    public $backoff = [60, 120, 300]; // Retry after 1m, 2m, 5m

    /**
     * Create a new job instance.
     */
    public function __construct(Tenant $tenant)
    {
        $this->tenant = $tenant;
        $this->onQueue('tenant-creation');
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        Log::info("Starting data seeding for tenant: {$this->tenant->id}");

        try {
            // Update tenant status
            $this->tenant->update(['setup_status' => 'seeding_data']);

            // Switch to tenant context
            tenancy()->initialize($this->tenant);

            // Seed default data
            // $this->seedDefaultCategories();
            // $this->seedDefaultDepartments();
            // $this->seedDefaultBranches();
            // $this->seedDefaultMenuItems();
            $this->seedDefaultSettings();

            // Switch back to central context
            tenancy()->end();

            // Update status
            $this->tenant->update(['setup_status' => 'data_seeded']);

            Log::info("Data seeding completed for tenant: {$this->tenant->id}");

        } catch (Exception $e) {
            // Ensure we switch back to central context
            tenancy()->end();

            Log::error("Data seeding failed for tenant {$this->tenant->id}: " . $e->getMessage());
            
            $this->tenant->update([
                'setup_status' => 'data_seeding_failed',
                'setup_error' => $e->getMessage()
            ]);

            throw $e;
        }
    }

    /**
     * Seed default categories
     */
    private function seedDefaultCategories(): void
    {
        $categoryModel = app(\App\Models\Tenant\Category::class);

        $defaultCategories = [
            ['name' => 'Appetizers', 'description' => 'Starter dishes', 'is_active' => true, 'sort_order' => 1],
            ['name' => 'Main Courses', 'description' => 'Main dishes', 'is_active' => true, 'sort_order' => 2],
            ['name' => 'Desserts', 'description' => 'Sweet treats', 'is_active' => true, 'sort_order' => 3],
            ['name' => 'Beverages', 'description' => 'Drinks and beverages', 'is_active' => true, 'sort_order' => 4],
            ['name' => 'Salads', 'description' => 'Fresh salads', 'is_active' => true, 'sort_order' => 5],
        ];

        foreach ($defaultCategories as $category) {
            $categoryModel->create($category);
        }

        Log::info("Default categories seeded for tenant: {$this->tenant->id}");
    }

    /**
     * Seed default departments
     */
    private function seedDefaultDepartments(): void
    {
        $departmentModel = app(\App\Models\Tenant\Department::class);

        $defaultDepartments = [
            ['name' => 'Kitchen', 'description' => 'Food preparation and cooking', 'is_active' => true],
            ['name' => 'Service', 'description' => 'Customer service and waitstaff', 'is_active' => true],
            ['name' => 'Management', 'description' => 'Restaurant management', 'is_active' => true],
            ['name' => 'Delivery', 'description' => 'Food delivery services', 'is_active' => true],
        ];

        foreach ($defaultDepartments as $department) {
            $departmentModel->create($department);
        }

        Log::info("Default departments seeded for tenant: {$this->tenant->id}");
    }

    /**
     * Seed default branches
     */
    private function seedDefaultBranches(): void
    {
        $branchModel = app(\App\Models\Tenant\Branch::class);

        $defaultBranch = [
            'name' => $this->tenant->name . ' Main Branch',
            'phone' => $this->tenant->phone ?? '(*************',
            'email' => $this->tenant->email,
            'address' => $this->tenant->address ?? '123 Main Street',
            'city' => $this->tenant->city ?? 'City',
            'state' => $this->tenant->state ?? 'State',
            'postal_code' => $this->tenant->postal_code ?? '12345',
            'country' => $this->tenant->country ?? 'Country',
            'is_active' => true,
            'is_main' => true,
            'opening_time' => '09:00:00',
            'closing_time' => '22:00:00',
        ];

        $branchModel->create($defaultBranch);

        Log::info("Default branch seeded for tenant: {$this->tenant->id}");
    }

    /**
     * Seed sample menu items
     */
    private function seedDefaultMenuItems(): void
    {
        $menuItemModel = app(\App\Models\Tenant\MenuItem::class);
        $categoryModel = app(\App\Models\Tenant\Category::class);

        $categories = $categoryModel->all()->keyBy('name');

        $sampleMenuItems = [
            [
                'name' => 'Caesar Salad',
                'description' => 'Fresh romaine lettuce with Caesar dressing',
                'price' => 12.99,
                'category_id' => $categories['Appetizers']->id ?? null,
                'is_available' => true,
                'is_featured' => false,
            ],
            [
                'name' => 'Grilled Chicken',
                'description' => 'Tender grilled chicken breast with herbs',
                'price' => 18.99,
                'category_id' => $categories['Main Courses']->id ?? null,
                'is_available' => true,
                'is_featured' => true,
            ],
            [
                'name' => 'Chocolate Cake',
                'description' => 'Rich chocolate cake with vanilla ice cream',
                'price' => 8.99,
                'category_id' => $categories['Desserts']->id ?? null,
                'is_available' => true,
                'is_featured' => false,
            ],
            [
                'name' => 'Fresh Orange Juice',
                'description' => 'Freshly squeezed orange juice',
                'price' => 4.99,
                'category_id' => $categories['Beverages']->id ?? null,
                'is_available' => true,
                'is_featured' => false,
            ],
        ];

        foreach ($sampleMenuItems as $item) {
            if ($item['category_id']) {
                $menuItemModel->create($item);
            }
        }

        Log::info("Sample menu items seeded for tenant: {$this->tenant->id}");
    }

    /**
     * Seed default settings
     */
    private function seedDefaultSettings(): void
    {
        $settingModel = app(\App\Models\Tenant\Setting::class);

        $defaultSettings = [
            ['key' => 'restaurant_name', 'value' => $this->tenant->name],
            ['key' => 'currency', 'value' => $this->tenant->currency ?? 'USD'],
            ['key' => 'timezone', 'value' => $this->tenant->timezone ?? 'UTC'],
            ['key' => 'tax_rate', 'value' => '10.00'],
            ['key' => 'service_charge', 'value' => '5.00'],
            ['key' => 'allow_online_orders', 'value' => 'true'],
            ['key' => 'allow_reservations', 'value' => 'true'],
        ];

        foreach ($defaultSettings as $setting) {
            $settingModel->updateOrCreate(
                ['key' => $setting['key']],
                ['value' => $setting['value']]
            );
        }

        Log::info("Default settings seeded for tenant: {$this->tenant->id}");
    }

    /**
     * Handle a job failure.
     */
    public function failed(Exception $exception): void
    {
        // Ensure we switch back to central context
        tenancy()->end();

        Log::error("SeedTenantDataJob failed permanently for tenant {$this->tenant->id}: " . $exception->getMessage());

        $this->tenant->update([
            'setup_status' => 'data_seeding_permanently_failed',
            'setup_error' => $exception->getMessage()
        ]);

        \App\Jobs\Tenant\NotifyTenantCreationFailedJob::dispatch($this->tenant, 'Data Seeding', $exception->getMessage());
    }
}
