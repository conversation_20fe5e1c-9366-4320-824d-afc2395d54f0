<?php

namespace App\Jobs\Tenant;

use App\Models\Tenant;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Bus;
use Illuminate\Support\Facades\Log;
use Exception;

class TenantCreationChainJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    public $tenant;
    public $managerPassword;
    public $tries = 1; // Don't retry the chain job itself
    public $timeout = 1800; // 30 minutes total

    /**
     * Create a new job instance.
     */
    public function __construct(Tenant $tenant, string $managerPassword)
    {
        $this->tenant = $tenant;
        $this->managerPassword = $managerPassword;
        $this->onQueue('tenant-creation');
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        Log::info("Starting tenant creation chain for: {$this->tenant->id}");

        try {
            // Update tenant status
            $this->tenant->update([
                'setup_status' => 'in_progress',
                'setup_started_at' => now(),
                'setup_error' => null,
            ]);

            // Create job chain
            $this->createJobChain();

            Log::info("Tenant creation chain initiated for: {$this->tenant->id}");

        } catch (Exception $e) {
            Log::error("Tenant creation chain failed to start for {$this->tenant->id}: " . $e->getMessage());
            
            $this->tenant->update([
                'setup_status' => 'chain_failed',
                'setup_error' => $e->getMessage()
            ]);

            throw $e;
        }
    }

    /**
     * Create the job chain for tenant setup
     */
    private function createJobChain(): void
    {
        // Create a job chain that executes sequentially
        Bus::chain([
            // Step 1: Create tenant database and run migrations
            new CreateTenantDatabaseJob($this->tenant),
            
            // Step 2: Seed tenant database with default data
            // new SeedTenantDataJob($this->tenant),
            
            // Step 3: Create manager user in tenant database
            new CreateTenantManagerJob($this->tenant, $this->managerPassword),
            
            // Step 4: Set up file system (can run in parallel with other steps)
            new SetupFileSystemJob($this->tenant),
            
            // Step 5: Set up third-party services
            new SetupThirdPartyServicesJob($this->tenant),
            
            // Step 6: Send welcome email
            new SendWelcomeEmailJob($this->tenant, $this->managerPassword),
            
            // Step 7: Finalize setup
            new FinalizeTenantSetupJob($this->tenant),
            
        ])->onQueue('tenant-creation')
          ->catch(function (Exception $e) {
              // Handle chain failure
              Log::error("Tenant creation chain failed for {$this->tenant->id}: " . $e->getMessage());
              
              $this->tenant->update([
                  'setup_status' => 'failed',
                  'setup_error' => $e->getMessage(),
                  'setup_failed_at' => now(),
              ]);
              
              // Send failure notification
              NotifyTenantCreationFailedJob::dispatch(
                  $this->tenant, 
                  'Chain Execution', 
                  $e->getMessage()
              );
          });

        Log::info("Job chain created for tenant: {$this->tenant->id}");
    }

    /**
     * Handle a job failure.
     */
    public function failed(Exception $exception): void
    {
        Log::error("TenantCreationChainJob failed for tenant {$this->tenant->id}: " . $exception->getMessage());

        $this->tenant->update([
            'setup_status' => 'chain_permanently_failed',
            'setup_error' => $exception->getMessage(),
            'setup_failed_at' => now(),
        ]);

        // Send failure notification
        NotifyTenantCreationFailedJob::dispatch(
            $this->tenant, 
            'Chain Initialization', 
            $exception->getMessage()
        );
    }
}
