<?php

namespace App\Jobs\Tenant;

use App\Models\Tenant;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Log;
use Exception;

class CreateTenantManagerJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    public $tenant;
    public $managerPassword;
    public $tries = 3;
    public $timeout = 120; // 2 minutes
    public $backoff = [30, 60, 120];

    /**
     * Create a new job instance.
     */
    public function __construct(Tenant $tenant, string $managerPassword)
    {
        $this->tenant = $tenant;
        $this->managerPassword = $managerPassword;
        $this->onQueue('tenant-creation');
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        Log::info("Starting manager creation for tenant: {$this->tenant->id}");

        try {
            // Update tenant status
            $this->tenant->update(['setup_status' => 'creating_manager']);

            // Switch to tenant context
            tenancy()->initialize($this->tenant);

            // Create manager user
            $manager = $this->createManagerUser();

            // Create employee record
            $this->createEmployeeRecord($manager);

            // Switch back to central context
            tenancy()->end();

            // Update status
            $this->tenant->update(['setup_status' => 'manager_created']);

            Log::info("Manager creation completed for tenant: {$this->tenant->id}");

        } catch (Exception $e) {
            // Ensure we switch back to central context
            tenancy()->end();

            Log::error("Manager creation failed for tenant {$this->tenant->id}: " . $e->getMessage());
            
            $this->tenant->update([
                'setup_status' => 'manager_creation_failed',
                'setup_error' => $e->getMessage()
            ]);

            throw $e;
        }
    }

    /**
     * Create manager user in tenant database
     */
    private function createManagerUser()
    {
        $userModel = app(\App\Models\Tenant\User::class);

        $manager = $userModel->create([
            'name' => $this->tenant->name . ' Manager',
            'email' => $this->tenant->email,
            'password' => Hash::make($this->managerPassword),
            'role' => 'restaurant_manager',
            'department' => 'Management',
            'position' => 'Restaurant Manager',
            'phone' => $this->tenant->phone,
            'hire_date' => now(),
            'is_active' => true,
            'email_verified_at' => now(),
        ]);

        Log::info("Manager user created for tenant {$this->tenant->id}: {$manager->email}");

        return $manager;
    }

    /**
     * Create employee record for the manager
     */
    private function createEmployeeRecord($manager): void
    {
        try {
            $employeeModel = app(\App\Models\Tenant\Employee::class);
            $departmentModel = app(\App\Models\Tenant\Department::class);
            $branchModel = app(\App\Models\Tenant\Branch::class);

            // Get management department
            $managementDept = $departmentModel->where('name', 'Management')->first();
            
            // Get main branch
            $mainBranch = $branchModel->where('is_main', true)->first();

            $employee = $employeeModel->create([
                'user_id' => $manager->id,
                'first_name' => explode(' ', $manager->name)[0] ?? 'Manager',
                'last_name' => explode(' ', $manager->name, 2)[1] ?? '',
                'department_id' => $managementDept?->id,
                'primary_branch_id' => $mainBranch?->id,
                'position' => 'Restaurant Manager',
                'employment_type' => 'full_time',
                'employment_status' => 'active',
                'hire_date' => now(),
                'salary' => 50000.00, // Default salary
                'status' => 'active',
                'is_active' => true,
            ]);

            Log::info("Employee record created for manager in tenant {$this->tenant->id}: {$employee->employee_id}");

        } catch (Exception $e) {
            Log::warning("Could not create employee record for manager in tenant {$this->tenant->id}: " . $e->getMessage());
            // Don't throw exception as user creation was successful
        }
    }

    /**
     * Handle a job failure.
     */
    public function failed(Exception $exception): void
    {
        // Ensure we switch back to central context
        tenancy()->end();

        Log::error("CreateTenantManagerJob failed permanently for tenant {$this->tenant->id}: " . $exception->getMessage());

        $this->tenant->update([
            'setup_status' => 'manager_creation_permanently_failed',
            'setup_error' => $exception->getMessage()
        ]);

        \App\Jobs\Tenant\NotifyTenantCreationFailedJob::dispatch($this->tenant, 'Manager Creation', $exception->getMessage());
    }
}
