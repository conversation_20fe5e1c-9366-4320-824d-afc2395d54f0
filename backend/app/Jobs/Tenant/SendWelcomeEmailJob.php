<?php

namespace App\Jobs\Tenant;

use App\Models\Tenant;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\Log;
use Exception;

class SendWelcomeEmailJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    public $tenant;
    public $managerPassword;
    public $tries = 3;
    public $timeout = 60; // 1 minute
    public $backoff = [30, 60, 120];

    /**
     * Create a new job instance.
     */
    public function __construct(Tenant $tenant, string $managerPassword)
    {
        $this->tenant = $tenant;
        $this->managerPassword = $managerPassword;
        $this->onQueue('tenant-emails');
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        Log::info("Sending welcome email for tenant: {$this->tenant->id}");

        try {
            // Update tenant status
            $this->tenant->update(['setup_status' => 'sending_welcome_email']);

            // Send welcome email
            $this->sendWelcomeEmail();

            // Update status
            $this->tenant->update(['setup_status' => 'welcome_email_sent']);

            Log::info("Welcome email sent for tenant: {$this->tenant->id}");

        } catch (Exception $e) {
            Log::error("Welcome email failed for tenant {$this->tenant->id}: " . $e->getMessage());
            
            $this->tenant->update([
                'setup_status' => 'welcome_email_failed',
                'setup_error' => $e->getMessage()
            ]);

            throw $e;
        }
    }

    /**
     * Send welcome email to tenant
     */
    private function sendWelcomeEmail(): void
    {
        $primaryDomain = $this->tenant->domains()->first()?->domain;
        $loginUrl = $primaryDomain ? "http://{$primaryDomain}/login" : '#';

        $emailData = [
            'tenant_name' => $this->tenant->name,
            'tenant_email' => $this->tenant->email,
            'manager_password' => $this->managerPassword,
            'login_url' => $loginUrl,
            'support_email' => config('mail.support_email', '<EMAIL>'),
            'company_name' => config('app.name', 'Restaurant POS'),
        ];

        // Send welcome email
        Mail::send('emails.tenant.welcome', $emailData, function ($message) {
            $message->to($this->tenant->email, $this->tenant->name)
                   ->subject('Welcome to ' . config('app.name') . ' - Your Restaurant is Ready!');
        });

        // Send setup completion notification to admin
        $this->sendAdminNotification();

        Log::info("Welcome email sent to: {$this->tenant->email}");
    }

    /**
     * Send notification to admin about successful tenant creation
     */
    private function sendAdminNotification(): void
    {
        try {
            $adminEmail = config('mail.admin_email', '<EMAIL>');
            
            $emailData = [
                'tenant_name' => $this->tenant->name,
                'tenant_email' => $this->tenant->email,
                'tenant_id' => $this->tenant->id,
                'created_at' => $this->tenant->created_at->format('Y-m-d H:i:s'),
                'subscription_plan' => $this->tenant->subscriptionPlan?->name ?? 'Unknown',
            ];

            Mail::send('emails.admin.tenant-created', $emailData, function ($message) use ($adminEmail) {
                $message->to($adminEmail)
                       ->subject('New Tenant Created: ' . $this->tenant->name);
            });

            Log::info("Admin notification sent for tenant: {$this->tenant->id}");

        } catch (Exception $e) {
            Log::warning("Could not send admin notification for tenant {$this->tenant->id}: " . $e->getMessage());
            // Don't throw exception as this is not critical
        }
    }

    /**
     * Handle a job failure.
     */
    public function failed(Exception $exception): void
    {
        Log::error("SendWelcomeEmailJob failed permanently for tenant {$this->tenant->id}: " . $exception->getMessage());

        $this->tenant->update([
            'setup_status' => 'welcome_email_permanently_failed',
            'setup_error' => $exception->getMessage()
        ]);

        // Don't dispatch another notification job to avoid infinite loops
        Log::error("Welcome email permanently failed for tenant {$this->tenant->id}");
    }
}
