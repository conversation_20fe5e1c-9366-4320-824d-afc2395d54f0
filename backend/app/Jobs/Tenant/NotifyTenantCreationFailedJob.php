<?php

namespace App\Jobs\Tenant;

use App\Models\Tenant;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\Log;
use Exception;

class NotifyTenantCreationFailedJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    public $tenant;
    public $failedStep;
    public $errorMessage;
    public $tries = 3;
    public $timeout = 60;

    /**
     * Create a new job instance.
     */
    public function __construct(Tenant $tenant, string $failedStep, string $errorMessage)
    {
        $this->tenant = $tenant;
        $this->failedStep = $failedStep;
        $this->errorMessage = $errorMessage;
        $this->onQueue('notifications');
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        Log::info("Sending failure notification for tenant: {$this->tenant->id}, step: {$this->failedStep}");

        try {
            // Send notification to admin
            $this->sendAdminNotification();

            // Send notification to tenant (if appropriate)
            $this->sendTenantNotification();

            Log::info("Failure notifications sent for tenant: {$this->tenant->id}");

        } catch (Exception $e) {
            Log::error("Failed to send failure notification for tenant {$this->tenant->id}: " . $e->getMessage());
            throw $e;
        }
    }

    /**
     * Send failure notification to admin
     */
    private function sendAdminNotification(): void
    {
        try {
            $adminEmail = config('mail.admin_email', '<EMAIL>');
            
            $emailData = [
                'tenant_name' => $this->tenant->name,
                'tenant_email' => $this->tenant->email,
                'tenant_id' => $this->tenant->id,
                'failed_step' => $this->failedStep,
                'error_message' => $this->errorMessage,
                'failed_at' => now()->format('Y-m-d H:i:s'),
                'tenant_url' => route('admin.tenants.show', $this->tenant->id),
            ];

            Mail::send('emails.admin.tenant-creation-failed', $emailData, function ($message) use ($adminEmail) {
                $message->to($adminEmail)
                       ->subject('Tenant Creation Failed: ' . $this->tenant->name);
            });

            Log::info("Admin failure notification sent for tenant: {$this->tenant->id}");

        } catch (Exception $e) {
            Log::error("Could not send admin failure notification for tenant {$this->tenant->id}: " . $e->getMessage());
            throw $e;
        }
    }

    /**
     * Send appropriate notification to tenant
     */
    private function sendTenantNotification(): void
    {
        try {
            // Only send tenant notification for certain types of failures
            $notifyTenantSteps = [
                'Database Creation',
                'Data Seeding',
                'Manager Creation',
                'Welcome Email'
            ];

            if (!in_array($this->failedStep, $notifyTenantSteps)) {
                Log::info("Skipping tenant notification for step: {$this->failedStep}");
                return;
            }

            $emailData = [
                'tenant_name' => $this->tenant->name,
                'failed_step' => $this->failedStep,
                'support_email' => config('mail.support_email', '<EMAIL>'),
                'company_name' => config('app.name', 'Restaurant POS'),
            ];

            Mail::send('emails.tenant.creation-failed', $emailData, function ($message) {
                $message->to($this->tenant->email, $this->tenant->name)
                       ->subject('Issue with Your Restaurant Setup - We\'re Working on It');
            });

            Log::info("Tenant failure notification sent to: {$this->tenant->email}");

        } catch (Exception $e) {
            Log::warning("Could not send tenant failure notification for tenant {$this->tenant->id}: " . $e->getMessage());
            // Don't throw exception as admin notification is more important
        }
    }

    /**
     * Handle a job failure.
     */
    public function failed(Exception $exception): void
    {
        Log::error("NotifyTenantCreationFailedJob failed permanently for tenant {$this->tenant->id}: " . $exception->getMessage());
        
        // Log the failure but don't create another notification job to avoid infinite loops
        Log::critical("Critical: Could not send failure notifications for tenant {$this->tenant->id}. Manual intervention required.");
    }
}
