<?php

namespace App\Jobs\Tenant;

use App\Models\Tenant;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;
use Exception;

class SimpleTenantSetupJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    public $tenant;
    public $managerPassword;
    public $tries = 1;
    public $timeout = 1800; // 30 minutes

    /**
     * Create a new job instance.
     */
    public function __construct(Tenant $tenant, string $managerPassword)
    {
        $this->tenant = $tenant;
        $this->managerPassword = $managerPassword;
        $this->onQueue('tenant-creation');
    }

    /**
     * Execute the job - run all setup steps sequentially
     */
    public function handle(): void
    {
        Log::info("Starting simple tenant setup for: {$this->tenant->id}");

        try {
            // Update tenant status
            $this->tenant->update([
                'setup_status' => 'in_progress',
                'setup_started_at' => now(),
                'setup_error' => null,
            ]);

            // Step 1: Create tenant database
            $this->runStep('Database Creation', function() {
                $job = new CreateTenantDatabaseJob($this->tenant);
                $job->handle();
            });

            // Step 2: Seed tenant data
            // $this->runStep('Data Seeding', function() {
            //     $job = new SeedTenantDataJob($this->tenant);
            //     $job->handle();
            // });

            // Step 3: Create manager
            $this->runStep('Manager Creation', function() {
                $job = new CreateTenantManagerJob($this->tenant, $this->managerPassword);
                $job->handle();
            });

            // Step 4: Setup file system
            $this->runStep('File System Setup', function() {
                $job = new SetupFileSystemJob($this->tenant);
                $job->handle();
            });

            // Step 5: Setup services
            $this->runStep('Services Setup', function() {
                $job = new SetupThirdPartyServicesJob($this->tenant);
                $job->handle();
            });

            // Step 6: Send welcome email
            $this->runStep('Welcome Email', function() {
                $job = new SendWelcomeEmailJob($this->tenant, $this->managerPassword);
                $job->handle();
            });

            // Step 7: Finalize setup
            $this->runStep('Setup Finalization', function() {
                $job = new FinalizeTenantSetupJob($this->tenant);
                $job->handle();
            });

            Log::info("Simple tenant setup completed for: {$this->tenant->id}");

        } catch (Exception $e) {
            Log::error("Simple tenant setup failed for {$this->tenant->id}: " . $e->getMessage());
            
            $this->tenant->update([
                'setup_status' => 'failed',
                'setup_error' => $e->getMessage(),
                'setup_failed_at' => now(),
            ]);

            // Send failure notification
            NotifyTenantCreationFailedJob::dispatch(
                $this->tenant, 
                'Setup Process', 
                $e->getMessage()
            );

            throw $e;
        }
    }

    /**
     * Run a setup step with error handling
     */
    private function runStep(string $stepName, callable $stepFunction): void
    {
        Log::info("Running step: {$stepName} for tenant: {$this->tenant->id}");
        
        try {
            $stepFunction();
            Log::info("Completed step: {$stepName} for tenant: {$this->tenant->id}");
        } catch (Exception $e) {
            Log::error("Failed step: {$stepName} for tenant {$this->tenant->id}: " . $e->getMessage());
            throw new Exception("Step '{$stepName}' failed: " . $e->getMessage(), 0, $e);
        }
    }

    /**
     * Handle a job failure.
     */
    public function failed(Exception $exception): void
    {
        Log::error("SimpleTenantSetupJob failed permanently for tenant {$this->tenant->id}: " . $exception->getMessage());

        $this->tenant->update([
            'setup_status' => 'permanently_failed',
            'setup_error' => $exception->getMessage(),
            'setup_failed_at' => now(),
        ]);

        // Send failure notification
        NotifyTenantCreationFailedJob::dispatch(
            $this->tenant, 
            'Complete Setup Process', 
            $exception->getMessage()
        );
    }
}
