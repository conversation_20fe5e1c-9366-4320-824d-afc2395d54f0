<?php

namespace App\Jobs\Tenant;

use App\Models\Tenant;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Artisan;
use Illuminate\Support\Facades\Log;
use Exception;

class CreateTenantDatabaseJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    public $tenant;
    public $tries = 3;
    public $timeout = 300; // 5 minutes
    public $backoff = [30, 60, 120]; // Retry after 30s, 60s, 120s

    /**
     * Create a new job instance.
     */
    public function __construct(Tenant $tenant)
    {
        $this->tenant = $tenant;
        $this->onQueue('tenant-creation');
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        Log::info("Starting database creation for tenant: {$this->tenant->id}");
        Log::info("Tenant details - Name: {$this->tenant->name}, Email: {$this->tenant->email}");

        try {
            // Update tenant status
            $this->tenant->refresh(); // Refresh to get latest data
            $this->tenant->update(['setup_status' => 'creating_database']);
            Log::info("Updated tenant status to 'creating_database' for tenant: {$this->tenant->id}");

            // Create tenant database
            $this->createDatabase();

            // Run migrations
            $this->runMigrations();

            // Update status
            $this->tenant->refresh();
            $this->tenant->update(['setup_status' => 'database_created']);
            Log::info("Updated tenant status to 'database_created' for tenant: {$this->tenant->id}");

            Log::info("Database creation completed for tenant: {$this->tenant->id}");

        } catch (Exception $e) {
            Log::error("Database creation failed for tenant {$this->tenant->id}: " . $e->getMessage());
            Log::error("Exception trace: " . $e->getTraceAsString());

            // Update tenant status to failed
            try {
                $this->tenant->refresh();
                $this->tenant->update([
                    'setup_status' => 'database_creation_failed',
                    'setup_error' => $e->getMessage()
                ]);
            } catch (Exception $updateException) {
                Log::error("Failed to update tenant status after database creation failure: " . $updateException->getMessage());
            }

            throw $e; // Re-throw to trigger retry mechanism
        }
    }

    /**
     * Create the tenant database
     */
    private function createDatabase(): void
    {
        try {
            // Use Stancl/Tenancy's built-in database creation
            Artisan::call('tenants:migrate', [
                '--tenants' => [$this->tenant->id],
                '--force' => true
            ]);

            Log::info("Database created successfully for tenant: {$this->tenant->id}");

        } catch (Exception $e) {
            Log::error("Failed to create database for tenant {$this->tenant->id}: " . $e->getMessage());
            throw new Exception("Database creation failed: " . $e->getMessage());
        }
    }

    /**
     * Run tenant migrations
     */
    private function runMigrations(): void
    {
        try {
            // Run tenant-specific migrations
            Artisan::call('tenants:migrate', [
                '--tenants' => [$this->tenant->id],
                '--path' => 'database/migrations/tenant',
                '--force' => true
            ]);

            Log::info("Migrations completed for tenant: {$this->tenant->id}");

        } catch (Exception $e) {
            Log::error("Migration failed for tenant {$this->tenant->id}: " . $e->getMessage());
            throw new Exception("Migration failed: " . $e->getMessage());
        }
    }

    /**
     * Handle a job failure.
     */
    public function failed(Exception $exception): void
    {
        Log::error("CreateTenantDatabaseJob failed permanently for tenant {$this->tenant->id}: " . $exception->getMessage());

        // Update tenant status to permanently failed
        $this->tenant->update([
            'setup_status' => 'database_creation_permanently_failed',
            'setup_error' => $exception->getMessage()
        ]);

        // Dispatch notification job for admin
        \App\Jobs\Tenant\NotifyTenantCreationFailedJob::dispatch($this->tenant, 'Database Creation', $exception->getMessage());
    }
}
