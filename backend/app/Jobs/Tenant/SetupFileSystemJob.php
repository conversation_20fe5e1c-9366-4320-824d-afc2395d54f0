<?php

namespace App\Jobs\Tenant;

use App\Models\Tenant;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\File;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Log;
use Exception;

class SetupFileSystemJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    public $tenant;
    public $tries = 3;
    public $timeout = 300; // 5 minutes
    public $backoff = [30, 60, 120];

    /**
     * Create a new job instance.
     */
    public function __construct(Tenant $tenant)
    {
        $this->tenant = $tenant;
        $this->onQueue('tenant-creation');
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        Log::info("Starting file system setup for tenant: {$this->tenant->id}");

        try {
            // Update tenant status
            $this->tenant->update(['setup_status' => 'setting_up_filesystem']);

            // Create tenant directories
            $this->createTenantDirectories();

            // Copy default assets
            $this->copyDefaultAssets();

            // Set up storage links
            $this->setupStorageLinks();

            // Update status
            $this->tenant->update(['setup_status' => 'filesystem_setup']);

            Log::info("File system setup completed for tenant: {$this->tenant->id}");

        } catch (Exception $e) {
            Log::error("File system setup failed for tenant {$this->tenant->id}: " . $e->getMessage());
            
            $this->tenant->update([
                'setup_status' => 'filesystem_setup_failed',
                'setup_error' => $e->getMessage()
            ]);

            throw $e;
        }
    }

    /**
     * Create tenant-specific directories
     */
    private function createTenantDirectories(): void
    {
        $tenantId = $this->tenant->id;
        
        $directories = [
            "tenants/{$tenantId}/uploads",
            "tenants/{$tenantId}/uploads/menu-items",
            "tenants/{$tenantId}/uploads/categories",
            "tenants/{$tenantId}/uploads/profiles",
            "tenants/{$tenantId}/uploads/receipts",
            "tenants/{$tenantId}/uploads/documents",
            "tenants/{$tenantId}/backups",
            "tenants/{$tenantId}/exports",
            "tenants/{$tenantId}/temp",
        ];

        foreach ($directories as $directory) {
            if (!Storage::disk('public')->exists($directory)) {
                Storage::disk('public')->makeDirectory($directory);
                Log::info("Created directory: {$directory}");
            }
        }

        // Create private storage directories
        $privateDirectories = [
            "tenants/{$tenantId}/private",
            "tenants/{$tenantId}/private/reports",
            "tenants/{$tenantId}/private/logs",
        ];

        foreach ($privateDirectories as $directory) {
            if (!Storage::disk('local')->exists($directory)) {
                Storage::disk('local')->makeDirectory($directory);
                Log::info("Created private directory: {$directory}");
            }
        }
    }

    /**
     * Copy default assets to tenant directories
     */
    private function copyDefaultAssets(): void
    {
        $tenantId = $this->tenant->id;
        
        // Copy default menu item images
        $this->copyDefaultMenuImages($tenantId);
        
        // Copy default category images
        $this->copyDefaultCategoryImages($tenantId);
        
        // Copy default restaurant assets
        $this->copyDefaultRestaurantAssets($tenantId);

        Log::info("Default assets copied for tenant: {$tenantId}");
    }

    /**
     * Copy default menu item images
     */
    private function copyDefaultMenuImages(string $tenantId): void
    {
        $defaultImagesPath = 'defaults/menu-items';
        $tenantImagesPath = "tenants/{$tenantId}/uploads/menu-items";

        if (Storage::disk('public')->exists($defaultImagesPath)) {
            $defaultImages = Storage::disk('public')->files($defaultImagesPath);
            
            foreach ($defaultImages as $image) {
                $filename = basename($image);
                $destinationPath = "{$tenantImagesPath}/{$filename}";
                
                if (!Storage::disk('public')->exists($destinationPath)) {
                    Storage::disk('public')->copy($image, $destinationPath);
                }
            }
        }
    }

    /**
     * Copy default category images
     */
    private function copyDefaultCategoryImages(string $tenantId): void
    {
        $defaultImagesPath = 'defaults/categories';
        $tenantImagesPath = "tenants/{$tenantId}/uploads/categories";

        if (Storage::disk('public')->exists($defaultImagesPath)) {
            $defaultImages = Storage::disk('public')->files($defaultImagesPath);
            
            foreach ($defaultImages as $image) {
                $filename = basename($image);
                $destinationPath = "{$tenantImagesPath}/{$filename}";
                
                if (!Storage::disk('public')->exists($destinationPath)) {
                    Storage::disk('public')->copy($image, $destinationPath);
                }
            }
        }
    }

    /**
     * Copy default restaurant assets
     */
    private function copyDefaultRestaurantAssets(string $tenantId): void
    {
        $defaultAssetsPath = 'defaults/restaurant';
        $tenantAssetsPath = "tenants/{$tenantId}/uploads";

        if (Storage::disk('public')->exists($defaultAssetsPath)) {
            $defaultAssets = Storage::disk('public')->allFiles($defaultAssetsPath);
            
            foreach ($defaultAssets as $asset) {
                $relativePath = str_replace($defaultAssetsPath . '/', '', $asset);
                $destinationPath = "{$tenantAssetsPath}/{$relativePath}";
                
                // Create directory if it doesn't exist
                $destinationDir = dirname($destinationPath);
                if (!Storage::disk('public')->exists($destinationDir)) {
                    Storage::disk('public')->makeDirectory($destinationDir);
                }
                
                if (!Storage::disk('public')->exists($destinationPath)) {
                    Storage::disk('public')->copy($asset, $destinationPath);
                }
            }
        }
    }

    /**
     * Set up storage links for tenant
     */
    private function setupStorageLinks(): void
    {
        $tenantId = $this->tenant->id;
        
        // Create symbolic links for easy access
        $publicPath = public_path("storage/tenants/{$tenantId}");
        $storagePath = storage_path("app/public/tenants/{$tenantId}");
        
        if (!File::exists($publicPath) && File::exists($storagePath)) {
            try {
                File::link($storagePath, $publicPath);
                Log::info("Storage link created for tenant: {$tenantId}");
            } catch (Exception $e) {
                Log::warning("Could not create storage link for tenant {$tenantId}: " . $e->getMessage());
                // Don't throw exception as this is not critical
            }
        }
    }

    /**
     * Handle a job failure.
     */
    public function failed(Exception $exception): void
    {
        Log::error("SetupFileSystemJob failed permanently for tenant {$this->tenant->id}: " . $exception->getMessage());

        $this->tenant->update([
            'setup_status' => 'filesystem_setup_permanently_failed',
            'setup_error' => $exception->getMessage()
        ]);

        \App\Jobs\Tenant\NotifyTenantCreationFailedJob::dispatch($this->tenant, 'File System Setup', $exception->getMessage());
    }
}
