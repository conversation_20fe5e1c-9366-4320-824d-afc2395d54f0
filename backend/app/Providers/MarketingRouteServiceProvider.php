<?php

namespace App\Providers;

use Illuminate\Support\ServiceProvider;
use Illuminate\Support\Facades\Route;
use App\Http\Controllers\Marketing\HomeController;
use App\Http\Controllers\Marketing\SubscriptionController;

class MarketingRouteServiceProvider extends ServiceProvider
{
    /**
     * Register services.
     */
    public function register(): void
    {
        //
    }

    /**
     * Bootstrap services.
     */
    public function boot(): void
    {
        // Register marketing routes with high priority
        $this->registerMarketingRoutes();
    }

    /**
     * Register marketing routes
     */
    protected function registerMarketingRoutes(): void
    {
        // Register marketing routes with domain filtering middleware
        Route::middleware(['web', 'central.domain'])
             ->group(function () {
                // Marketing Website Routes
                Route::get('/', [HomeController::class, 'index'])->name('marketing.home');
                Route::get('/home', [HomeController::class, 'index'])->name('marketing.home.alt'); // Alternative route
                Route::get('/plans', [HomeController::class, 'plans'])->name('marketing.plans');
                Route::get('/features', [HomeController::class, 'features'])->name('marketing.features');

                // Contact Routes
                Route::get('/contact', [\App\Http\Controllers\Marketing\ContactController::class, 'index'])->name('marketing.contact');
                Route::post('/contact', [\App\Http\Controllers\Marketing\ContactController::class, 'submit'])->name('marketing.contact.submit');

                // Blog Routes
                Route::get('/blogs', [\App\Http\Controllers\Marketing\BlogController::class, 'index'])->name('marketing.blogs.index');
                Route::get('/blog/{slug}', [\App\Http\Controllers\Marketing\BlogController::class, 'show'])->name('marketing.blogs.show');

                // Subscription Routes
                Route::get('/signup', [SubscriptionController::class, 'signup'])->name('marketing.signup');
                Route::post('/signup', [SubscriptionController::class, 'processSignup'])->name('marketing.signup.process');
                Route::get('/check-slug', [SubscriptionController::class, 'checkSlug'])->name('marketing.check-slug');
                Route::get('/generate-slug', [SubscriptionController::class, 'generateSlug'])->name('marketing.generate-slug');

                // Dynamic Pages (must be last to avoid conflicts)
                Route::get('/{slug}', [\App\Http\Controllers\Marketing\PageController::class, 'show'])->name('marketing.pages.show');
            });
    }
}
