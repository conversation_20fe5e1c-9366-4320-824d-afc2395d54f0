<?php

namespace App\Services;

use App\Models\Tenant\Branch;
use Illuminate\Support\Facades\Cache;

class BranchSelectionService
{
    /**
     * Get the correct User model based on context
     */
    private static function getUserModel()
    {
        // In tenant context, use tenant User model
        if (tenant()) {
            return \App\Models\Tenant\User::class;
        }

        // In central context, use central User model
        return \App\Models\User::class;
    }

    /**
     * Get the current branch for POS/Kitchen operations
     * This is the main method that should be used across all POS controllers
     */
    public static function getCurrentBranchForUser($user = null): ?Branch
    {
        $user = $user ?? auth()->user();
        
        if (!$user) {
            return null;
        }

        // Use the trait method to get current branch
        return $user->getCurrentBranch();
    }

    /**
     * Get the current branch ID for POS/Kitchen operations
     */
    public static function getCurrentBranchId($user = null): ?int
    {
        $user = $user ?? auth()->user();
        
        if (!$user) {
            return null;
        }

        return $user->getCurrentBranchId();
    }

    /**
     * Get branch selection data for controllers
     * Returns standardized data structure for POS/Kitchen controllers
     */
    public static function getBranchDataForController($user = null): array
    {
        $user = $user ?? auth()->user();
        
        if (!$user) {
            return [
                'current_branch' => null,
                'current_branch_id' => null,
                'accessible_branches' => collect(),
                'has_access' => false,
                'error' => 'User not authenticated'
            ];
        }

        $branchData = $user->getBranchSelectionData();
        $currentBranch = $branchData['current_branch'];

        return [
            'current_branch' => $currentBranch,
            'current_branch_id' => $branchData['current_branch_id'],
            'accessible_branches' => $branchData['accessible_branches'],
            'has_access' => $currentBranch !== null,
            'error' => $currentBranch === null ? 'No accessible branches found' : null,
            'can_switch_branches' => $branchData['can_switch_branches'],
            'has_multiple_branches' => $branchData['has_multiple_branches'],
        ];
    }

    /**
     * Switch branch for the current user
     */
    public static function switchBranch(int $branchId, $user = null): array
    {
        $user = $user ?? auth()->user();
        
        if (!$user) {
            return [
                'success' => false,
                'message' => 'User not authenticated',
                'error' => 'authentication_required'
            ];
        }

        // Check if user has access to the branch
        if (!$user->hasAccessToBranch($branchId)) {
            return [
                'success' => false,
                'message' => 'You do not have access to this branch',
                'error' => 'access_denied'
            ];
        }

        // Verify branch exists and is active
        $branch = Branch::active()->find($branchId);
        if (!$branch) {
            return [
                'success' => false,
                'message' => 'Branch not found or inactive',
                'error' => 'branch_not_found'
            ];
        }

        // Set the branch in session
        $success = $user->setCurrentBranch($branchId);
        
        if ($success) {
            return [
                'success' => true,
                'message' => "Switched to {$branch->name}",
                'branch' => $branch,
                'branch_id' => $branchId
            ];
        }

        return [
            'success' => false,
            'message' => 'Failed to switch branch',
            'error' => 'switch_failed'
        ];
    }

    /**
     * Ensure user has access to at least one branch
     * Used in middleware or controller constructors
     */
    public static function ensureBranchAccess($user = null): array
    {
        $user = $user ?? auth()->user();
        
        if (!$user) {
            return [
                'has_access' => false,
                'redirect_route' => 'login',
                'message' => 'Authentication required'
            ];
        }

        $accessibleBranches = $user->getAccessibleBranches();
        
        if ($accessibleBranches->isEmpty()) {
            return [
                'has_access' => false,
                'redirect_route' => 'dashboard',
                'message' => 'You do not have access to any branches. Please contact your administrator.'
            ];
        }

        // Ensure a branch is selected
        $currentBranchId = $user->getCurrentBranchId();
        if (!$currentBranchId) {
            // Auto-select the default branch
            $defaultBranchId = $user->getDefaultBranchId();
            if ($defaultBranchId) {
                $user->setCurrentBranch($defaultBranchId);
            }
        }

        return [
            'has_access' => true,
            'current_branch_id' => $user->getCurrentBranchId(),
            'accessible_branches' => $accessibleBranches
        ];
    }

    /**
     * Get branch selection data for frontend components
     */
    public static function getBranchSelectionForFrontend($user = null): array
    {
        $user = $user ?? auth()->user();
        
        if (!$user) {
            return [
                'branches' => [],
                'current_branch_id' => null,
                'can_switch' => false
            ];
        }

        $branchData = $user->getBranchSelectionData();
        
        return [
            'branches' => $branchData['accessible_branches']->map(function ($branch) {
                return [
                    'id' => $branch->id,
                    'name' => $branch->name,
                    'is_main' => $branch->is_main_branch,
                    'is_active' => $branch->is_active,
                ];
            })->toArray(),
            'current_branch_id' => $branchData['current_branch_id'],
            'can_switch' => $branchData['can_switch_branches'],
            'has_multiple' => $branchData['has_multiple_branches'],
        ];
    }

    /**
     * Clear branch cache for user (useful after role/permission changes)
     */
    public static function clearUserBranchCache($user = null): void
    {
        $user = $user ?? auth()->user();

        if ($user) {
            $user->clearBranchCache();
        }
    }

    /**
     * Clear branch cache for specific user by ID
     */
    public static function clearBranchCacheForUser(int $userId): void
    {
        // Create a temporary user instance to access the trait methods
        $userClass = self::getUserModel();
        $user = new $userClass();
        $user->clearBranchCacheForUser($userId);
    }

    /**
     * Clear all branch caches for current tenant
     */
    public static function clearAllBranchCaches(): void
    {
        $userClass = self::getUserModel();
        $user = new $userClass();
        $user->clearAllBranchCaches();
    }

    /**
     * Get cache statistics for debugging
     */
    public static function getCacheStats(): array
    {
        $cacheDriver = Cache::getDefaultDriver();
        $user = auth()->user();

        $stats = [
            'cache_driver' => $cacheDriver,
            'supports_tagging' => in_array($cacheDriver, ['redis', 'memcached', 'dynamodb']),
            'tenant_id' => tenant() ? tenant('id') : null,
            'current_user_id' => $user ? $user->id : null,
        ];

        if ($user) {
            $cacheKey = "user_branches_{$user->id}_" . (tenant() ? tenant('id') : 'central');
            $stats['current_user_cache_key'] = $cacheKey;
            $stats['cache_exists'] = Cache::has($cacheKey);
        }

        return $stats;
    }

    /**
     * Validate branch access for specific operations
     */
    public static function validateBranchAccess(int $branchId, $user = null): array
    {
        $user = $user ?? auth()->user();
        
        if (!$user) {
            return [
                'valid' => false,
                'message' => 'User not authenticated',
                'error' => 'authentication_required'
            ];
        }

        if (!$user->hasAccessToBranch($branchId)) {
            return [
                'valid' => false,
                'message' => 'Access denied to this branch',
                'error' => 'access_denied'
            ];
        }

        $branch = Branch::active()->find($branchId);
        if (!$branch) {
            return [
                'valid' => false,
                'message' => 'Branch not found or inactive',
                'error' => 'branch_not_found'
            ];
        }

        return [
            'valid' => true,
            'branch' => $branch,
            'message' => 'Access granted'
        ];
    }
}
