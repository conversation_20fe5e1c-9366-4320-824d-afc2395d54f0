<?php

namespace App\Services\HRM;

use App\Models\Tenant\Employee;
use App\Models\Tenant\AttendanceRecord;
use App\Models\Tenant\Branch;
use Carbon\Carbon;

class AttendanceService
{
    /**
     * Clock in an employee.
     */
    public function clockIn(Employee $employee, array $options = []): AttendanceRecord
    {
        $date = $options['date'] ?? now()->toDateString();
        $time = $options['time'] ?? now();
        $branchId = $options['branch_id'] ?? $employee->primary_branch_id;
        $location = $options['location'] ?? null;
        $ip = $options['ip'] ?? request()->ip();

        // Check if already clocked in today
        $existingRecord = AttendanceRecord::where('employee_id', $employee->id)
            ->whereDate('date', $date)
            ->first();

        if ($existingRecord && $existingRecord->clock_in_time) {
            throw new \Exception('Employee is already clocked in today.');
        }

        // Create or update attendance record
        $attendance = AttendanceRecord::updateOrCreate(
            [
                'employee_id' => $employee->id,
                'date' => $date,
            ],
            [
                'branch_id' => $branchId,
                'scheduled_start' => $this->getScheduledStartTime($employee, $date),
                'scheduled_end' => $this->getScheduledEndTime($employee, $date),
                'is_holiday' => $this->isHoliday($date),
                'is_weekend' => Carbon::parse($date)->isWeekend(),
            ]
        );

        // Clock in
        $attendance->clockIn(Carbon::parse($time), $location, $ip);

        return $attendance;
    }

    /**
     * Clock out an employee.
     */
    public function clockOut(Employee $employee, array $options = []): AttendanceRecord
    {
        $date = $options['date'] ?? now()->toDateString();
        $time = $options['time'] ?? now();
        $location = $options['location'] ?? null;
        $ip = $options['ip'] ?? request()->ip();

        $attendance = AttendanceRecord::where('employee_id', $employee->id)
            ->whereDate('date', $date)
            ->first();

        if (!$attendance || !$attendance->clock_in_time) {
            throw new \Exception('Employee is not clocked in today.');
        }

        if ($attendance->clock_out_time) {
            throw new \Exception('Employee is already clocked out today.');
        }

        // Clock out
        $attendance->clockOut(Carbon::parse($time), $location, $ip);

        return $attendance;
    }

    /**
     * Get attendance records for an employee within a date range.
     */
    public function getAttendanceRecords(Employee $employee, Carbon $startDate, Carbon $endDate): \Illuminate\Database\Eloquent\Collection
    {
        return AttendanceRecord::where('employee_id', $employee->id)
            ->whereBetween('date', [$startDate, $endDate])
            ->orderBy('date', 'desc')
            ->get();
    }

    /**
     * Get attendance summary for an employee.
     */
    public function getAttendanceSummary(Employee $employee, Carbon $startDate, Carbon $endDate): array
    {
        $records = $this->getAttendanceRecords($employee, $startDate, $endDate);

        $totalDays = $startDate->diffInDays($endDate) + 1;
        $workingDays = $this->getWorkingDays($startDate, $endDate);
        $presentDays = $records->where('status', AttendanceRecord::STATUS_PRESENT)->count();
        $lateDays = $records->where('status', AttendanceRecord::STATUS_LATE)->count();
        $absentDays = $workingDays - $presentDays - $lateDays;

        return [
            'total_days' => $totalDays,
            'working_days' => $workingDays,
            'present_days' => $presentDays,
            'late_days' => $lateDays,
            'absent_days' => $absentDays,
            'total_hours' => $records->sum('total_hours'),
            'regular_hours' => $records->sum('regular_hours'),
            'overtime_hours' => $records->sum('overtime_hours'),
            'average_hours_per_day' => $presentDays > 0 ? $records->sum('total_hours') / $presentDays : 0,
            'attendance_percentage' => $workingDays > 0 ? (($presentDays + $lateDays) / $workingDays) * 100 : 0,
        ];
    }

    /**
     * Get team attendance for a specific date.
     */
    public function getTeamAttendance(Carbon $date, Branch $branch = null): array
    {
        $query = AttendanceRecord::with('employee')
            ->whereDate('date', $date);

        if ($branch) {
            $query->where('branch_id', $branch->id);
        }

        $records = $query->get();

        // Get all employees who should be working today
        $employeeQuery = Employee::active();
        if ($branch) {
            $employeeQuery->where('primary_branch_id', $branch->id);
        }
        $allEmployees = $employeeQuery->get();

        // Create attendance data for all employees
        $attendanceData = [];
        foreach ($allEmployees as $employee) {
            $record = $records->where('employee_id', $employee->id)->first();
            
            $attendanceData[] = [
                'employee_id' => $employee->id,
                'employee_name' => $employee->full_name,
                'employee_position' => $employee->position,
                'scheduled_start' => $this->getScheduledStartTime($employee, $date),
                'scheduled_end' => $this->getScheduledEndTime($employee, $date),
                'clock_in_time' => $record?->clock_in_time,
                'clock_out_time' => $record?->clock_out_time,
                'status' => $record?->status ?? AttendanceRecord::STATUS_ABSENT,
                'total_hours' => $record?->total_hours ?? 0,
                'late_minutes' => $record?->late_minutes ?? 0,
                'is_clocked_in' => $record?->isClockedIn() ?? false,
            ];
        }

        return $attendanceData;
    }

    /**
     * Mark employee as absent for a specific date.
     */
    public function markAbsent(Employee $employee, Carbon $date, string $reason = null): AttendanceRecord
    {
        return AttendanceRecord::updateOrCreate(
            [
                'employee_id' => $employee->id,
                'date' => $date,
            ],
            [
                'branch_id' => $employee->primary_branch_id,
                'status' => AttendanceRecord::STATUS_ABSENT,
                'notes' => $reason,
                'scheduled_start' => $this->getScheduledStartTime($employee, $date),
                'scheduled_end' => $this->getScheduledEndTime($employee, $date),
                'is_holiday' => $this->isHoliday($date),
                'is_weekend' => $date->isWeekend(),
            ]
        );
    }

    /**
     * Mark employee as on leave for a specific date.
     */
    public function markOnLeave(Employee $employee, Carbon $date): AttendanceRecord
    {
        return AttendanceRecord::updateOrCreate(
            [
                'employee_id' => $employee->id,
                'date' => $date,
            ],
            [
                'branch_id' => $employee->primary_branch_id,
                'status' => AttendanceRecord::STATUS_ON_LEAVE,
                'scheduled_start' => $this->getScheduledStartTime($employee, $date),
                'scheduled_end' => $this->getScheduledEndTime($employee, $date),
                'is_holiday' => $this->isHoliday($date),
                'is_weekend' => $date->isWeekend(),
            ]
        );
    }

    /**
     * Get scheduled start time for an employee on a specific date.
     */
    protected function getScheduledStartTime(Employee $employee, $date): ?Carbon
    {
        if ($employee->default_shift_start) {
            return Carbon::parse($date)->setTimeFromTimeString($employee->default_shift_start);
        }

        // Default to 9:00 AM if no schedule is set
        return Carbon::parse($date)->setTime(9, 0);
    }

    /**
     * Get scheduled end time for an employee on a specific date.
     */
    protected function getScheduledEndTime(Employee $employee, $date): ?Carbon
    {
        if ($employee->default_shift_end) {
            return Carbon::parse($date)->setTimeFromTimeString($employee->default_shift_end);
        }

        // Default to 5:00 PM if no schedule is set
        return Carbon::parse($date)->setTime(17, 0);
    }

    /**
     * Check if a date is a holiday.
     */
    protected function isHoliday($date): bool
    {
        // This can be enhanced to check against a holidays table
        // For now, return false
        return false;
    }

    /**
     * Get working days between two dates (excluding weekends).
     */
    protected function getWorkingDays(Carbon $startDate, Carbon $endDate): int
    {
        $workingDays = 0;
        $current = $startDate->copy();

        while ($current->lte($endDate)) {
            if (!$current->isWeekend()) {
                $workingDays++;
            }
            $current->addDay();
        }

        return $workingDays;
    }

    /**
     * Generate attendance report for a period.
     */
    public function generateAttendanceReport(Carbon $startDate, Carbon $endDate, Branch $branch = null): array
    {
        $query = AttendanceRecord::with('employee')
            ->whereBetween('date', [$startDate, $endDate]);

        if ($branch) {
            $query->where('branch_id', $branch->id);
        }

        $records = $query->get();

        $summary = [
            'total_records' => $records->count(),
            'present_count' => $records->where('status', AttendanceRecord::STATUS_PRESENT)->count(),
            'late_count' => $records->where('status', AttendanceRecord::STATUS_LATE)->count(),
            'absent_count' => $records->where('status', AttendanceRecord::STATUS_ABSENT)->count(),
            'on_leave_count' => $records->where('status', AttendanceRecord::STATUS_ON_LEAVE)->count(),
            'total_hours' => $records->sum('total_hours'),
            'total_overtime_hours' => $records->sum('overtime_hours'),
            'average_hours_per_day' => $records->count() > 0 ? $records->sum('total_hours') / $records->count() : 0,
        ];

        $employeeSummary = $records->groupBy('employee_id')->map(function ($employeeRecords) {
            $employee = $employeeRecords->first()->employee;
            return [
                'employee_name' => $employee->full_name,
                'total_days' => $employeeRecords->count(),
                'present_days' => $employeeRecords->where('status', AttendanceRecord::STATUS_PRESENT)->count(),
                'late_days' => $employeeRecords->where('status', AttendanceRecord::STATUS_LATE)->count(),
                'absent_days' => $employeeRecords->where('status', AttendanceRecord::STATUS_ABSENT)->count(),
                'total_hours' => $employeeRecords->sum('total_hours'),
                'overtime_hours' => $employeeRecords->sum('overtime_hours'),
            ];
        })->values();

        return [
            'summary' => $summary,
            'employee_summary' => $employeeSummary,
            'records' => $records,
        ];
    }
}
