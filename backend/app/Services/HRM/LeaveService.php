<?php

namespace App\Services\HRM;

use App\Models\Tenant\Employee;
use App\Models\Tenant\LeaveType;
use App\Models\Tenant\LeaveRequest;
use App\Models\Tenant\EmployeeLeaveEntitlement;
use Carbon\Carbon;

class LeaveService
{
    /**
     * Create leave entitlements for an employee for a specific year.
     */
    public function createLeaveEntitlements(Employee $employee, int $year = null): array
    {
        $year = $year ?? now()->year;
        $leaveTypes = LeaveType::active()->get();
        $entitlements = [];

        foreach ($leaveTypes as $leaveType) {
            $entitlement = EmployeeLeaveEntitlement::updateOrCreate(
                [
                    'employee_id' => $employee->id,
                    'leave_type_id' => $leaveType->id,
                    'year' => $year,
                ],
                [
                    'entitled_days' => $this->calculateEntitledDays($employee, $leaveType, $year),
                    'used_days' => 0,
                    'pending_days' => 0,
                    'carried_forward_days' => $this->calculateCarriedForwardDays($employee, $leaveType, $year),
                    'expires_at' => Carbon::create($year, 12, 31),
                ]
            );

            $entitlements[] = $entitlement;
        }

        return $entitlements;
    }

    /**
     * Submit a leave request.
     */
    public function submitLeaveRequest(Employee $employee, array $data): LeaveRequest
    {
        $leaveType = LeaveType::findOrFail($data['leave_type_id']);
        $startDate = Carbon::parse($data['start_date']);
        $endDate = Carbon::parse($data['end_date']);
        $isHalfDay = $data['is_half_day'] ?? false;

        // Calculate total days
        $totalDays = LeaveRequest::calculateWorkingDays($startDate, $endDate, $isHalfDay);

        // Validate leave request
        $this->validateLeaveRequest($employee, $leaveType, $startDate, $endDate, $totalDays);

        // Create leave request
        $leaveRequest = LeaveRequest::create([
            'employee_id' => $employee->id,
            'leave_type_id' => $leaveType->id,
            'start_date' => $startDate,
            'end_date' => $endDate,
            'total_days' => $totalDays,
            'reason' => $data['reason'],
            'comments' => $data['comments'] ?? null,
            'is_half_day' => $isHalfDay,
            'half_day_period' => $data['half_day_period'] ?? null,
            'emergency_contact_name' => $data['emergency_contact_name'] ?? null,
            'emergency_contact_phone' => $data['emergency_contact_phone'] ?? null,
            'status' => LeaveRequest::STATUS_PENDING,
        ]);

        // Reserve days in entitlement
        $entitlement = $employee->getLeaveEntitlement($leaveType->id, $startDate->year);
        if ($entitlement) {
            $entitlement->reserveDays($totalDays);
        }

        return $leaveRequest;
    }

    /**
     * Validate a leave request.
     */
    protected function validateLeaveRequest(Employee $employee, LeaveType $leaveType, Carbon $startDate, Carbon $endDate, int $totalDays): void
    {
        // Check if employee has sufficient leave balance
        $availableDays = $employee->getAvailableLeaveDays($leaveType->id, $startDate->year);
        if ($availableDays < $totalDays) {
            throw new \Exception("Insufficient leave balance. Available: {$availableDays} days, Requested: {$totalDays} days.");
        }

        // Check maximum consecutive days
        if ($leaveType->exceedsMaxConsecutiveDays($totalDays)) {
            throw new \Exception("Leave request exceeds maximum consecutive days ({$leaveType->max_consecutive_days}).");
        }

        // Check notice period
        if (!$leaveType->hasSufficientNotice(now(), $startDate)) {
            throw new \Exception("Insufficient notice period. Minimum {$leaveType->min_notice_days} days required.");
        }

        // Check for overlapping leave requests
        $overlapping = LeaveRequest::where('employee_id', $employee->id)
            ->where('status', '!=', LeaveRequest::STATUS_REJECTED)
            ->where('status', '!=', LeaveRequest::STATUS_CANCELLED)
            ->where(function ($query) use ($startDate, $endDate) {
                $query->whereBetween('start_date', [$startDate, $endDate])
                      ->orWhereBetween('end_date', [$startDate, $endDate])
                      ->orWhere(function ($q) use ($startDate, $endDate) {
                          $q->where('start_date', '<=', $startDate)
                            ->where('end_date', '>=', $endDate);
                      });
            })
            ->exists();

        if ($overlapping) {
            throw new \Exception("Leave request overlaps with existing leave request.");
        }
    }

    /**
     * Calculate entitled days for an employee for a specific leave type and year.
     */
    protected function calculateEntitledDays(Employee $employee, LeaveType $leaveType, int $year): int
    {
        $entitledDays = $leaveType->default_days_per_year;

        // Adjust based on hire date if employee was hired during the year
        if ($employee->hire_date && $employee->hire_date->year === $year) {
            $monthsWorked = 12 - $employee->hire_date->month + 1;
            $entitledDays = round(($entitledDays / 12) * $monthsWorked);
        }

        return $entitledDays;
    }

    /**
     * Calculate carried forward days from previous year.
     */
    protected function calculateCarriedForwardDays(Employee $employee, LeaveType $leaveType, int $year): int
    {
        if ($year <= now()->year || $leaveType->max_carry_forward_days === 0) {
            return 0;
        }

        $previousYearEntitlement = EmployeeLeaveEntitlement::where('employee_id', $employee->id)
            ->where('leave_type_id', $leaveType->id)
            ->where('year', $year - 1)
            ->first();

        if (!$previousYearEntitlement) {
            return 0;
        }

        $unusedDays = $previousYearEntitlement->entitled_days - $previousYearEntitlement->used_days;
        return min($unusedDays, $leaveType->max_carry_forward_days);
    }

    /**
     * Approve a leave request.
     */
    public function approveLeaveRequest(LeaveRequest $leaveRequest, Employee $approver, string $notes = null): void
    {
        if (!$leaveRequest->isPending()) {
            throw new \Exception("Only pending leave requests can be approved.");
        }

        $leaveRequest->approve($approver, $notes);
    }

    /**
     * Reject a leave request.
     */
    public function rejectLeaveRequest(LeaveRequest $leaveRequest, Employee $rejector, string $reason): void
    {
        if (!$leaveRequest->isPending()) {
            throw new \Exception("Only pending leave requests can be rejected.");
        }

        $leaveRequest->reject($rejector, $reason);
    }

    /**
     * Cancel a leave request.
     */
    public function cancelLeaveRequest(LeaveRequest $leaveRequest): void
    {
        if ($leaveRequest->isApproved() && $leaveRequest->start_date->isPast()) {
            throw new \Exception("Cannot cancel leave request that has already started.");
        }

        $leaveRequest->cancel();
    }

    /**
     * Get leave calendar for an employee or all employees.
     */
    public function getLeaveCalendar(Carbon $startDate, Carbon $endDate, Employee $employee = null): array
    {
        $query = LeaveRequest::with(['employee', 'leaveType'])
            ->where('status', LeaveRequest::STATUS_APPROVED)
            ->where(function ($q) use ($startDate, $endDate) {
                $q->whereBetween('start_date', [$startDate, $endDate])
                  ->orWhereBetween('end_date', [$startDate, $endDate])
                  ->orWhere(function ($query) use ($startDate, $endDate) {
                      $query->where('start_date', '<=', $startDate)
                            ->where('end_date', '>=', $endDate);
                  });
            });

        if ($employee) {
            $query->where('employee_id', $employee->id);
        }

        return $query->get()->map(function ($leave) {
            return [
                'id' => $leave->id,
                'title' => $leave->employee->full_name . ' - ' . $leave->leaveType->name,
                'start' => $leave->start_date->format('Y-m-d'),
                'end' => $leave->end_date->addDay()->format('Y-m-d'), // FullCalendar end is exclusive
                'color' => $leave->leaveType->color_code ?? '#007bff',
                'employee' => $leave->employee->full_name,
                'leave_type' => $leave->leaveType->name,
                'total_days' => $leave->total_days,
                'reason' => $leave->reason,
            ];
        })->toArray();
    }

    /**
     * Get leave statistics for an employee or all employees.
     */
    public function getLeaveStatistics(int $year = null, Employee $employee = null): array
    {
        $year = $year ?? now()->year;

        $query = EmployeeLeaveEntitlement::with(['employee', 'leaveType'])
            ->where('year', $year);

        if ($employee) {
            $query->where('employee_id', $employee->id);
        }

        $entitlements = $query->get();

        return [
            'total_entitled_days' => $entitlements->sum('entitled_days'),
            'total_used_days' => $entitlements->sum('used_days'),
            'total_pending_days' => $entitlements->sum('pending_days'),
            'total_available_days' => $entitlements->sum('available_days'),
            'by_leave_type' => $entitlements->groupBy('leave_type_id')->map(function ($group) {
                $first = $group->first();
                return [
                    'leave_type' => $first->leaveType->name,
                    'entitled_days' => $group->sum('entitled_days'),
                    'used_days' => $group->sum('used_days'),
                    'pending_days' => $group->sum('pending_days'),
                    'available_days' => $group->sum('available_days'),
                ];
            })->values(),
        ];
    }
}
