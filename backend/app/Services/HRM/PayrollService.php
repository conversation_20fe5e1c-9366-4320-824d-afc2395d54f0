<?php

namespace App\Services\HRM;

use App\Models\Tenant\Employee;
use App\Models\Tenant\PayrollRecord;
use App\Models\Tenant\AttendanceRecord;
use App\Models\Tenant\EmployeeLoan;
use Carbon\Carbon;

class PayrollService
{
    /**
     * Generate payroll for an employee for a specific period.
     */
    public function generatePayroll(Employee $employee, Carbon $startDate, Carbon $endDate, array $options = []): PayrollRecord
    {
        $payroll = new PayrollRecord([
            'employee_id' => $employee->id,
            'pay_period_start' => $startDate,
            'pay_period_end' => $endDate,
            'pay_date' => $options['pay_date'] ?? $endDate->copy()->addDays(3), // Pay 3 days after period end
            'pay_frequency' => $employee->pay_frequency ?? PayrollRecord::FREQUENCY_MONTHLY,
            'status' => PayrollRecord::STATUS_DRAFT,
        ]);

        // Set additional fields from options
        $payroll->bonus = $options['bonus'] ?? 0;
        $payroll->commission = $options['commission'] ?? 0;
        $payroll->allowances = $options['allowances'] ?? 0;
        $payroll->insurance_deduction = $options['insurance_deduction'] ?? 0;
        $payroll->advance_deductions = $options['advance_deductions'] ?? 0;
        $payroll->other_deductions = $options['other_deductions'] ?? 0;

        $payroll->save();

        // Calculate payroll automatically
        $payroll->calculatePayroll();
        $payroll->save();

        return $payroll;
    }

    /**
     * Generate payroll for all active employees for a specific period.
     */
    public function generateBulkPayroll(Carbon $startDate, Carbon $endDate, array $options = []): array
    {
        $employees = Employee::active()->get();
        $payrolls = [];

        foreach ($employees as $employee) {
            // Skip if payroll already exists for this period
            $existingPayroll = PayrollRecord::where('employee_id', $employee->id)
                ->where('pay_period_start', $startDate)
                ->where('pay_period_end', $endDate)
                ->first();

            if ($existingPayroll) {
                continue;
            }

            $payrolls[] = $this->generatePayroll($employee, $startDate, $endDate, $options);
        }

        return $payrolls;
    }

    /**
     * Calculate overtime hours for an employee in a period.
     */
    public function calculateOvertimeHours(Employee $employee, Carbon $startDate, Carbon $endDate): float
    {
        return AttendanceRecord::where('employee_id', $employee->id)
            ->whereBetween('date', [$startDate, $endDate])
            ->sum('overtime_hours');
    }

    /**
     * Calculate regular hours for an employee in a period.
     */
    public function calculateRegularHours(Employee $employee, Carbon $startDate, Carbon $endDate): float
    {
        return AttendanceRecord::where('employee_id', $employee->id)
            ->whereBetween('date', [$startDate, $endDate])
            ->sum('regular_hours');
    }

    /**
     * Calculate total loan deductions for an employee.
     */
    public function calculateLoanDeductions(Employee $employee, Carbon $payDate): float
    {
        $activeLoans = EmployeeLoan::where('employee_id', $employee->id)
            ->active()
            ->dueForPayment($payDate)
            ->get();

        return $activeLoans->sum('monthly_installment');
    }

    /**
     * Approve multiple payroll records.
     */
    public function approveBulkPayroll(array $payrollIds, Employee $approver): array
    {
        $payrolls = PayrollRecord::whereIn('id', $payrollIds)
            ->where('status', PayrollRecord::STATUS_DRAFT)
            ->get();

        $approved = [];

        foreach ($payrolls as $payroll) {
            $payroll->approve($approver);
            $approved[] = $payroll;
        }

        return $approved;
    }

    /**
     * Mark multiple payroll records as paid.
     */
    public function markBulkAsPaid(array $payrollIds, Employee $paidBy): array
    {
        $payrolls = PayrollRecord::whereIn('id', $payrollIds)
            ->where('status', PayrollRecord::STATUS_APPROVED)
            ->get();

        $paid = [];

        foreach ($payrolls as $payroll) {
            $payroll->markAsPaid($paidBy);
            $paid[] = $payroll;
        }

        return $paid;
    }

    /**
     * Get payroll summary for a period.
     */
    public function getPayrollSummary(Carbon $startDate, Carbon $endDate): array
    {
        $payrolls = PayrollRecord::whereBetween('pay_period_start', [$startDate, $endDate])->get();

        return [
            'total_employees' => $payrolls->count(),
            'total_gross_pay' => $payrolls->sum('gross_pay'),
            'total_deductions' => $payrolls->sum('total_deductions'),
            'total_net_pay' => $payrolls->sum('net_pay'),
            'total_regular_hours' => $payrolls->sum('regular_hours'),
            'total_overtime_hours' => $payrolls->sum('overtime_hours'),
            'total_loan_deductions' => $payrolls->sum('loan_deductions'),
            'by_status' => [
                'draft' => $payrolls->where('status', PayrollRecord::STATUS_DRAFT)->count(),
                'approved' => $payrolls->where('status', PayrollRecord::STATUS_APPROVED)->count(),
                'paid' => $payrolls->where('status', PayrollRecord::STATUS_PAID)->count(),
            ],
        ];
    }

    /**
     * Get monthly payroll periods for the current year.
     */
    public function getMonthlyPayrollPeriods(int $year = null): array
    {
        $year = $year ?? now()->year;
        $periods = [];

        for ($month = 1; $month <= 12; $month++) {
            $startDate = Carbon::create($year, $month, 1);
            $endDate = $startDate->copy()->endOfMonth();

            $periods[] = [
                'month' => $month,
                'month_name' => $startDate->format('F'),
                'start_date' => $startDate,
                'end_date' => $endDate,
                'pay_date' => $endDate->copy()->addDays(3),
            ];
        }

        return $periods;
    }

    /**
     * Get bi-weekly payroll periods for the current year.
     */
    public function getBiWeeklyPayrollPeriods(int $year = null): array
    {
        $year = $year ?? now()->year;
        $periods = [];
        $startDate = Carbon::create($year, 1, 1);

        // Start from the first Monday of the year
        while ($startDate->dayOfWeek !== Carbon::MONDAY) {
            $startDate->addDay();
        }

        $periodNumber = 1;

        while ($startDate->year === $year) {
            $endDate = $startDate->copy()->addDays(13); // 14 days total

            $periods[] = [
                'period' => $periodNumber,
                'start_date' => $startDate->copy(),
                'end_date' => $endDate->copy(),
                'pay_date' => $endDate->copy()->addDays(3),
            ];

            $startDate->addDays(14);
            $periodNumber++;
        }

        return $periods;
    }
}
