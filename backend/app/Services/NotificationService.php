<?php

namespace App\Services;

use App\Models\User;
use Illuminate\Support\Facades\Notification;
use App\Notifications\OrderStatusNotification;
use App\Notifications\KitchenAlertNotification;
use App\Notifications\DeliveryNotification;
use App\Notifications\TableAssignmentNotification;

class NotificationService
{
    /**
     * Send order status notification to relevant staff
     */
    public function notifyOrderStatus($order, $status, $user = null)
    {
        $notification = new OrderStatusNotification($order, $status, $user);
        
        switch ($status) {
            case 'confirmed':
                // Notify kitchen staff
                $this->notifyRole('kitchen', $notification);
                break;
                
            case 'preparing':
                // Notify assigned waiter
                if ($order->assigned_to) {
                    $waiter = User::find($order->assigned_to);
                    if ($waiter) {
                        $waiter->notify($notification);
                    }
                }
                break;
                
            case 'ready':
                // Notify waiter and delivery if applicable
                if ($order->assigned_to) {
                    $waiter = User::find($order->assigned_to);
                    if ($waiter) {
                        $waiter->notify($notification);
                    }
                }
                
                if ($order->type === 'delivery') {
                    $this->notifyRole('delivery', $notification);
                }
                break;
                
            case 'delivered':
                // Notify manager
                $this->notify<PERSON><PERSON>('restaurant_manager', $notification);
                break;
        }
    }

    /**
     * Send kitchen alert notifications
     */
    public function notifyKitchenAlert($type, $data)
    {
        $notification = new KitchenAlertNotification($type, $data);
        
        // Notify kitchen staff and managers
        $this->notifyRole('kitchen', $notification);
        $this->notifyRole('restaurant_manager', $notification);
    }

    /**
     * Send delivery notifications
     */
    public function notifyDeliveryUpdate($deliveryOrder, $status, $driver = null)
    {
        $notification = new DeliveryNotification($deliveryOrder, $status, $driver);
        
        switch ($status) {
            case 'assigned':
                // Notify the assigned driver
                if ($driver) {
                    $driver->notify($notification);
                }
                break;
                
            case 'picked_up':
                // Notify manager and customer
                $this->notifyRole('restaurant_manager', $notification);
                break;
                
            case 'delivered':
                // Notify manager
                $this->notifyRole('restaurant_manager', $notification);
                break;
                
            case 'failed':
                // Notify manager and available drivers
                $this->notifyRole('restaurant_manager', $notification);
                $this->notifyRole('delivery', $notification);
                break;
        }
    }

    /**
     * Send table assignment notifications
     */
    public function notifyTableAssignment($table, $waiter, $action = 'assigned')
    {
        $notification = new TableAssignmentNotification($table, $waiter, $action);
        
        // Notify the waiter
        $waiter->notify($notification);
        
        // Notify other waiters if table is being reassigned
        if ($action === 'reassigned') {
            $this->notifyRole('waiter', $notification, [$waiter->id]);
        }
    }

    /**
     * Send notification to all users with a specific role
     */
    public function notifyRole($role, $notification, $excludeIds = [])
    {
        $users = User::role($role)
            ->where('is_active', true)
            ->when(!empty($excludeIds), function ($query) use ($excludeIds) {
                return $query->whereNotIn('id', $excludeIds);
            })
            ->get();

        Notification::send($users, $notification);
    }

    /**
     * Send notification to specific users
     */
    public function notifyUsers($userIds, $notification)
    {
        $users = User::whereIn('id', $userIds)
            ->where('is_active', true)
            ->get();

        Notification::send($users, $notification);
    }

    /**
     * Send real-time notification via broadcasting
     */
    public function broadcastToRole($role, $event, $data)
    {
        $users = User::role($role)
            ->where('is_active', true)
            ->get();

        foreach ($users as $user) {
            broadcast(new $event($user, $data))->toOthers();
        }
    }

    /**
     * Get unread notifications for a user
     */
    public function getUnreadNotifications($user, $limit = 10)
    {
        return $user->unreadNotifications()
            ->limit($limit)
            ->get()
            ->map(function ($notification) {
                return [
                    'id' => $notification->id,
                    'type' => $notification->type,
                    'data' => $notification->data,
                    'created_at' => $notification->created_at,
                ];
            });
    }

    /**
     * Mark notification as read
     */
    public function markAsRead($user, $notificationId)
    {
        $notification = $user->unreadNotifications()
            ->where('id', $notificationId)
            ->first();

        if ($notification) {
            $notification->markAsRead();
            return true;
        }

        return false;
    }

    /**
     * Mark all notifications as read for a user
     */
    public function markAllAsRead($user)
    {
        $user->unreadNotifications->markAsRead();
    }

    /**
     * Get notification preferences for a user
     */
    public function getNotificationPreferences($user)
    {
        return [
            'email_notifications' => $user->email_notifications ?? true,
            'sms_notifications' => $user->sms_notifications ?? false,
            'push_notifications' => $user->push_notifications ?? true,
            'order_notifications' => $user->order_notifications ?? true,
            'kitchen_notifications' => $user->kitchen_notifications ?? true,
            'delivery_notifications' => $user->delivery_notifications ?? true,
        ];
    }

    /**
     * Update notification preferences for a user
     */
    public function updateNotificationPreferences($user, $preferences)
    {
        $user->update($preferences);
    }

    /**
     * Send emergency notification to all active staff
     */
    public function sendEmergencyNotification($message, $type = 'emergency')
    {
        $users = User::where('is_active', true)
            ->whereHas('roles')
            ->get();

        $notification = new \App\Notifications\EmergencyNotification($message, $type);
        
        Notification::send($users, $notification);
    }

    /**
     * Send shift reminder notifications
     */
    public function sendShiftReminders()
    {
        // Get users with shifts starting in the next hour
        $upcomingShifts = \App\Models\Tenant\Shift::where('start_time', '>=', now())
            ->where('start_time', '<=', now()->addHour())
            ->where('status', 'scheduled')
            ->with('employee.user')
            ->get();

        foreach ($upcomingShifts as $shift) {
            if ($shift->employee && $shift->employee->user) {
                $shift->employee->user->notify(
                    new \App\Notifications\ShiftReminderNotification($shift)
                );
            }
        }
    }

    /**
     * Send low inventory alerts
     */
    public function sendInventoryAlerts($lowStockItems)
    {
        if (empty($lowStockItems)) {
            return;
        }

        $notification = new \App\Notifications\InventoryAlertNotification($lowStockItems);
        
        // Notify managers and kitchen staff
        $this->notifyRole('restaurant_manager', $notification);
        $this->notifyRole('kitchen', $notification);
    }
}
