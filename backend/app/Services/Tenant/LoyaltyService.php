<?php

namespace App\Services\Tenant;

use App\Models\Tenant\LoyaltySettings;
use App\Models\Tenant\LoyaltyAccount;
use App\Models\Tenant\LoyaltyTransaction;
use App\Models\Tenant\Order;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class LoyaltyService
{
    protected ?LoyaltySettings $settings = null;

    public function __construct()
    {
        // Don't initialize settings in constructor - lazy load when needed
    }

    /**
     * Get loyalty settings instance (lazy loaded)
     */
    protected function getSettings(): LoyaltySettings
    {
        if ($this->settings === null) {
            $this->settings = LoyaltySettings::getInstance();
        }

        return $this->settings;
    }

    /**
     * Check if loyalty program is enabled
     */
    public function isEnabled(): bool
    {
        return $this->getSettings()->enabled;
    }

    /**
     * Get loyalty account by phone number
     */
    public function getAccountByPhone(string $phoneNumber): ?LoyaltyAccount
    {
        if (!$this->isEnabled()) {
            return null;
        }

        $normalizedPhone = LoyaltyAccount::normalizePhoneNumber($phoneNumber);
        return LoyaltyAccount::where('phone_number', $normalizedPhone)->first();
    }

    /**
     * Create or update loyalty account
     */
    public function createOrUpdateAccount(string $phoneNumber, array $customerData): LoyaltyAccount
    {
        $account = LoyaltyAccount::findOrCreateByPhone($phoneNumber, $customerData);
        
        // Update customer data if provided
        if (!empty($customerData)) {
            $updateData = array_filter([
                'customer_name' => $customerData['name'] ?? null,
                'email' => $customerData['email'] ?? null,
                'address' => $customerData['address'] ?? null,
                'date_of_birth' => $customerData['date_of_birth'] ?? null,
            ]);
            
            if (!empty($updateData)) {
                $account->update($updateData);
            }
        }

        return $account;
    }

    /**
     * Process points earning for completed order
     */
    public function processOrderCompletion(Order $order): ?LoyaltyTransaction
    {
        if (!$this->isEnabled() || !$order->customer_phone) {
            return null;
        }

        try {
            return DB::transaction(function () use ($order) {
                // Get or create loyalty account
                $account = $this->createOrUpdateAccount($order->customer_phone, [
                    'name' => $order->customer_name,
                    'email' => $order->customer_email,
                ]);

                // Calculate points to earn
                $pointsToEarn = $this->getSettings()->calculatePointsEarned(
                    $order->total_amount,
                    $account->tier
                );

                if ($pointsToEarn <= 0) {
                    return null;
                }

                // Add points to account
                $transaction = $account->addPoints(
                    $pointsToEarn,
                    'earned',
                    "Points earned from order #{$order->order_number}",
                    $order->id,
                    ['order_amount' => $order->total_amount]
                );

                // Update account order statistics
                $account->increment('total_orders');
                $account->increment('total_spent', $order->total_amount);
                $account->update(['last_order_at' => now()]);

                // Check for birthday bonus
                $this->processBirthdayBonus($account);

                Log::info("Loyalty points earned", [
                    'account_id' => $account->id,
                    'phone' => $account->phone_number,
                    'order_id' => $order->id,
                    'points_earned' => $pointsToEarn,
                ]);

                return $transaction;
            });
        } catch (\Exception $e) {
            Log::error("Failed to process loyalty points for order", [
                'order_id' => $order->id,
                'error' => $e->getMessage(),
            ]);
            return null;
        }
    }

    /**
     * Process points redemption during checkout
     */
    public function processPointsRedemption(string $phoneNumber, int $pointsToRedeem, float $orderAmount): array
    {
        if (!$this->isEnabled()) {
            throw new \Exception('Loyalty program is not enabled');
        }

        $account = $this->getAccountByPhone($phoneNumber);
        if (!$account) {
            throw new \Exception('Loyalty account not found');
        }

        if ($pointsToRedeem > $account->total_points) {
            throw new \Exception('Insufficient points balance');
        }

        // Calculate discount amount
        $discountAmount = $this->getSettings()->calculateDiscountAmount($pointsToRedeem);
        $maxDiscount = $this->getSettings()->getMaxDiscountForOrder($orderAmount);

        if ($discountAmount > $maxDiscount) {
            $discountAmount = $maxDiscount;
            $pointsToRedeem = (int) ($discountAmount * $this->getSettings()->points_for_dollar_discount);
        }

        return [
            'points_to_redeem' => $pointsToRedeem,
            'discount_amount' => $discountAmount,
            'max_discount' => $maxDiscount,
            'remaining_points' => $account->total_points - $pointsToRedeem,
        ];
    }

    /**
     * Apply points redemption to order
     */
    public function applyPointsRedemption(Order $order, int $pointsToRedeem): ?LoyaltyTransaction
    {
        if (!$this->isEnabled() || !$order->customer_phone) {
            return null;
        }

        try {
            return DB::transaction(function () use ($order, $pointsToRedeem) {
                $account = $this->getAccountByPhone($order->customer_phone);
                if (!$account) {
                    throw new \Exception('Loyalty account not found');
                }

                // Redeem points
                $transaction = $account->redeemPoints(
                    $pointsToRedeem,
                    "Points redeemed for order #{$order->order_number}",
                    $order->id
                );

                Log::info("Loyalty points redeemed", [
                    'account_id' => $account->id,
                    'phone' => $account->phone_number,
                    'order_id' => $order->id,
                    'points_redeemed' => $pointsToRedeem,
                ]);

                return $transaction;
            });
        } catch (\Exception $e) {
            Log::error("Failed to redeem loyalty points", [
                'order_id' => $order->id,
                'points' => $pointsToRedeem,
                'error' => $e->getMessage(),
            ]);
            throw $e;
        }
    }

    /**
     * Process order cancellation/refund
     */
    public function processOrderCancellation(Order $order): array
    {
        if (!$this->isEnabled() || !$order->customer_phone) {
            return ['earned_refunded' => null, 'redeemed_refunded' => null];
        }

        try {
            return DB::transaction(function () use ($order) {
                $account = $this->getAccountByPhone($order->customer_phone);
                if (!$account) {
                    return ['earned_refunded' => null, 'redeemed_refunded' => null];
                }

                $earnedRefunded = null;
                $redeemedRefunded = null;

                // Find and refund earned points for this order
                $earnedTransaction = $account->transactions()
                    ->where('order_id', $order->id)
                    ->where('transaction_type', 'earned')
                    ->first();

                if ($earnedTransaction && $earnedTransaction->points > 0) {
                    // Deduct the earned points
                    $earnedRefunded = $account->transactions()->create([
                        'order_id' => $order->id,
                        'transaction_type' => 'refunded',
                        'points' => -$earnedTransaction->points,
                        'description' => "Points refunded due to order #{$order->order_number} cancellation",
                    ]);

                    $account->decrement('total_points', $earnedTransaction->points);
                }

                // Find and restore redeemed points for this order
                $redeemedTransaction = $account->transactions()
                    ->where('order_id', $order->id)
                    ->where('transaction_type', 'redeemed')
                    ->first();

                if ($redeemedTransaction && $redeemedTransaction->points < 0) {
                    // Restore the redeemed points
                    $pointsToRestore = abs($redeemedTransaction->points);
                    $redeemedRefunded = $account->transactions()->create([
                        'order_id' => $order->id,
                        'transaction_type' => 'refunded',
                        'points' => $pointsToRestore,
                        'description' => "Points restored due to order #{$order->order_number} cancellation",
                    ]);

                    $account->increment('total_points', $pointsToRestore);
                }

                $account->update(['last_transaction_at' => now()]);

                return [
                    'earned_refunded' => $earnedRefunded,
                    'redeemed_refunded' => $redeemedRefunded,
                ];
            });
        } catch (\Exception $e) {
            Log::error("Failed to process loyalty refund for cancelled order", [
                'order_id' => $order->id,
                'error' => $e->getMessage(),
            ]);
            return ['earned_refunded' => null, 'redeemed_refunded' => null];
        }
    }

    /**
     * Process birthday bonus
     */
    public function processBirthdayBonus(LoyaltyAccount $account): ?LoyaltyTransaction
    {
        if (!$this->settings->birthday_bonus_enabled || 
            !$this->settings->birthday_bonus_points || 
            !$account->isBirthdayToday()) {
            return null;
        }

        // Check if birthday bonus already given this year
        $existingBonus = $account->transactions()
            ->where('transaction_type', 'birthday_bonus')
            ->whereYear('created_at', now()->year)
            ->exists();

        if ($existingBonus) {
            return null;
        }

        return $account->addPoints(
            $this->settings->birthday_bonus_points,
            'birthday_bonus',
            'Happy Birthday! Bonus points from ' . config('app.name'),
            null,
            ['birthday_year' => now()->year]
        );
    }

    /**
     * Manual points adjustment (for managers)
     */
    public function manualPointsAdjustment(
        string $phoneNumber, 
        int $points, 
        string $reason, 
        int $processedBy
    ): LoyaltyTransaction {
        if (!$this->isEnabled()) {
            throw new \Exception('Loyalty program is not enabled');
        }

        $account = $this->getAccountByPhone($phoneNumber);
        if (!$account) {
            throw new \Exception('Loyalty account not found');
        }

        if ($points < 0 && abs($points) > $account->total_points) {
            throw new \Exception('Cannot deduct more points than available balance');
        }

        return DB::transaction(function () use ($account, $points, $reason, $processedBy) {
            $transaction = $account->transactions()->create([
                'transaction_type' => 'manual_adjustment',
                'points' => $points,
                'description' => $reason,
                'processed_by' => $processedBy,
            ]);

            if ($points > 0) {
                $account->increment('total_points', $points);
                if ($points > 0) {
                    $account->increment('lifetime_points_earned', $points);
                }
            } else {
                $account->decrement('total_points', abs($points));
            }

            $account->update(['last_transaction_at' => now()]);
            $account->updateTier();

            return $transaction;
        });
    }

    /**
     * Get loyalty statistics
     */
    public function getLoyaltyStatistics(): array
    {
        if (!$this->isEnabled()) {
            return [];
        }

        return [
            'total_accounts' => LoyaltyAccount::where('is_active', true)->count(),
            'total_points_issued' => LoyaltyTransaction::where('points', '>', 0)->sum('points'),
            'total_points_redeemed' => abs(LoyaltyTransaction::where('points', '<', 0)->sum('points')),
            'active_accounts_30_days' => LoyaltyAccount::where('last_transaction_at', '>=', now()->subDays(30))->count(),
            'tier_distribution' => LoyaltyAccount::where('is_active', true)
                ->groupBy('tier')
                ->selectRaw('tier, count(*) as count')
                ->pluck('count', 'tier')
                ->toArray(),
        ];
    }

    /**
     * Calculate potential discount for points
     */
    public function calculatePotentialDiscount(int $points, float $orderAmount): array
    {
        if (!$this->isEnabled()) {
            return ['discount_amount' => 0, 'points_needed' => 0, 'max_discount' => 0];
        }

        $discountAmount = $this->getSettings()->calculateDiscountAmount($points);
        $maxDiscount = $this->getSettings()->getMaxDiscountForOrder($orderAmount);

        // Limit discount to maximum allowed
        if ($discountAmount > $maxDiscount) {
            $discountAmount = $maxDiscount;
        }

        return [
            'discount_amount' => $discountAmount,
            'points_needed' => $points,
            'max_discount' => $maxDiscount,
            'max_points_usable' => (int) ($maxDiscount * $this->getSettings()->points_for_dollar_discount),
        ];
    }
}
