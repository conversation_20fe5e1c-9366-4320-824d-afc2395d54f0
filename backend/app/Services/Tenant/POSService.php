<?php

namespace App\Services\Tenant;

use App\Models\Tenant\Order;
use App\Models\Tenant\OrderItem;
use App\Models\Tenant\OrderPayment;
use App\Models\Tenant\OrderDiscount;
use App\Models\Tenant\Table;
use App\Models\Tenant\MenuItem;
use App\Models\Tenant\Branch;
use App\Models\Tenant\User;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Str;

class POSService
{
    /**
     * Create a new POS order
     */
    public function createOrder(array $data, User $user): Order
    {
        return DB::transaction(function () use ($data, $user) {
            $order = Order::create([
                'branch_id' => $data['branch_id'],
                'order_number' => $this->generateOrderNumber(),
                'order_source' => 'pos',
                'table_id' => $data['table_id'] ?? null,
                'customer_id' => $data['customer_id'] ?? null,
                'order_type' => $data['order_type'] ?? 'dine_in',
                'status' => 'pending',
                'kitchen_status' => 'pending',
                'payment_status' => 'pending',
                'waiter_id' => $this->validateWaiterId($data['waiter_id'] ?? null),
                'notes' => $data['special_instructions'] ?? null,
                'phone' => $data['customer_phone'] ?? null,
                'delivery_address' => $data['delivery_address'] ?? null,
                'delivery_location' => $data['delivery_location'] ?? null,
                'subtotal' => 0,
                'grand_total' => 0,
                'total_amount' => 0,
                'discount' => 0,
                'delivery_charge' => $data['delivery_charge'] ?? 0,
                'loyalty_points' => 0,
                'points_applied' => 0,
            ]);

            // Create initial status update
            $order->updates()->create([
                'status' => 'pending',
                'user_id' => $user->id,
                'notes' => 'Order created via POS',
            ]);

            // Mark table as occupied if dine-in
            if ($order->isDineIn() && $order->table_id) {
                Table::find($order->table_id)->update(['status' => 'occupied']);
            }

            return $order;
        });
    }

    /**
     * Add item to order
     */
    public function addItemToOrder(Order $order, array $itemData, User $user): OrderItem
    {
        return DB::transaction(function () use ($order, $itemData, $user) {
            $menuItem = MenuItem::findOrFail($itemData['menu_item_id']);

            // Get branch-specific price if available
            $price = $this->getBranchSpecificPrice($menuItem, $order->branch_id);

            $orderItem = $order->items()->create([
                'menu_item_id' => $menuItem->id,
                'item_name' => $menuItem->name,
                'item_price' => $price,
                'quantity' => $itemData['quantity'],
                'total_price' => $price * $itemData['quantity'],
                'special_instructions' => $itemData['special_instructions'] ?? null,
                'variations' => $itemData['modifiers'] ?? null,
                'addons' => $itemData['addons'] ?? null,
                'status' => 'pending',
            ]);

            // Apply item-level discounts if provided
            if (!empty($itemData['discounts'])) {
                foreach ($itemData['discounts'] as $discountData) {
                    $this->applyItemDiscount($orderItem, $discountData, $user);
                }
            }

            // Recalculate order totals
            $order->recalculateTotal();

            return $orderItem;
        });
    }

    /**
     * Update item quantity
     */
    public function updateItemQuantity(OrderItem $orderItem, int $quantity, User $user): OrderItem
    {
        return DB::transaction(function () use ($orderItem, $quantity, $user) {
            $orderItem->update([
                'quantity' => $quantity,
                'total_price' => $orderItem->item_price * $quantity,
            ]);

            $orderItem->order->recalculateTotal();

            return $orderItem;
        });
    }

    /**
     * Remove item from order
     */
    public function removeItemFromOrder(OrderItem $orderItem, User $user): void
    {
        DB::transaction(function () use ($orderItem, $user) {
            $order = $orderItem->order;
            
            // Remove item-level discounts
            $orderItem->discounts()->delete();
            
            // Delete the item
            $orderItem->delete();

            // Recalculate order totals
            $order->recalculateTotal();
        });
    }

    /**
     * Apply discount to order
     */
    public function applyOrderDiscount(Order $order, array $discountData, User $user): OrderDiscount
    {
        return DB::transaction(function () use ($order, $discountData, $user) {
            $discountData['applied_by'] = $user->id;
            $discountData['applied_to'] = 'order';

            // Calculate discount amount
            if ($discountData['discount_type'] === 'percentage') {
                $discountData['discount_amount'] = ($order->subtotal * $discountData['discount_value']) / 100;
            } else {
                $discountData['discount_amount'] = min($discountData['discount_value'], $order->subtotal);
            }

            $discount = $order->applyDiscount($discountData);

            return $discount;
        });
    }

    /**
     * Apply discount to item
     */
    public function applyItemDiscount(OrderItem $orderItem, array $discountData, User $user): OrderDiscount
    {
        return DB::transaction(function () use ($orderItem, $discountData, $user) {
            $discountData['applied_by'] = $user->id;
            $discountData['applied_to'] = 'item';

            // Calculate discount amount
            if ($discountData['discount_type'] === 'percentage') {
                $discountData['discount_amount'] = ($orderItem->total_price * $discountData['discount_value']) / 100;
            } else {
                $discountData['discount_amount'] = min($discountData['discount_value'], $orderItem->total_price);
            }

            $discount = $orderItem->applyDiscount($discountData);

            return $discount;
        });
    }

    /**
     * Process payment for order
     */
    public function processPayment(Order $order, array $paymentData, User $user): OrderPayment
    {
        return DB::transaction(function () use ($order, $paymentData, $user) {
            $payment = $order->addPayment([
                'branch_id' => $order->branch_id,
                'payment_method' => $paymentData['payment_method'],
                'amount' => $paymentData['amount'],
                'status' => 'completed',
                'processed_at' => now(),
                'transaction_reference' => $paymentData['transaction_reference'] ?? null,
                'payment_details' => $paymentData['payment_details'] ?? null,
                'notes' => $paymentData['notes'] ?? null,
            ]);

            return $payment;
        });
    }

    /**
     * Split bill for order
     */
    public function splitBill(Order $order, array $splitData, User $user): array
    {
        return DB::transaction(function () use ($order, $splitData, $user) {
            $order->update(['is_split_bill' => true]);

            $payments = [];
            foreach ($splitData['payments'] as $paymentData) {
                $payments[] = $this->processPayment($order, $paymentData, $user);
            }

            return $payments;
        });
    }

    /**
     * Send order to kitchen
     */
    public function sendToKitchen(Order $order, User $user): void
    {
        DB::transaction(function () use ($order, $user) {
            $order->update(['status' => 'confirmed']);

            // Create status update record
            $order->updates()->create([
                'status' => 'confirmed',
                'user_id' => $user->id,
                'notes' => 'Order sent to kitchen',
            ]);

            // Update all order items to received status
            $order->items()->update(['kitchen_status' => 'received']);
        });
    }

    /**
     * Close order
     */
    public function closeOrder(Order $order, User $user): void
    {
        DB::transaction(function () use ($order, $user) {
            $order->update(['status' => 'served']);

            // Create status update record
            $order->updates()->create([
                'status' => 'served',
                'user_id' => $user->id,
                'notes' => 'Order completed and closed',
            ]);

            // Free up table if dine-in
            if ($order->isDineIn() && $order->table_id) {
                Table::find($order->table_id)->update(['status' => 'available']);
            }
        });
    }

    /**
     * Cancel order
     */
    public function cancelOrder(Order $order, string $reason, User $user): void
    {
        DB::transaction(function () use ($order, $reason, $user) {
            $order->update([
                'status' => 'cancelled',
                'kitchen_status' => 'cancelled',
                'cancellation_reason' => $reason,
                'order_closed_at' => now(),
                'closed_by' => $user->id,
            ]);

            // Cancel all order items (using status field instead of is_cancelled)
            $order->items()->update([
                'status' => 'cancelled',
                'special_instructions' => $reason,
            ]);

            // Free up table if dine-in
            if ($order->isDineIn() && $order->table_id) {
                Table::find($order->table_id)->update(['status' => 'available']);
            }
        });
    }

    /**
     * Get branch-specific price for menu item
     */
    protected function getBranchSpecificPrice(MenuItem $menuItem, int $branchId): float
    {
        $branchMenuItem = $menuItem->branches()->where('branch_id', $branchId)->first();
        
        return $branchMenuItem && $branchMenuItem->pivot->branch_specific_price 
            ? $branchMenuItem->pivot->branch_specific_price 
            : $menuItem->price;
    }

    /**
     * Generate unique order number
     */
    protected function generateOrderNumber(): string
    {
        do {
            $number = 'ORD-' . strtoupper(Str::random(8));
        } while (Order::where('order_number', $number)->exists());

        return $number;
    }

    /**
     * Validate waiter ID exists in users table
     */
    protected function validateWaiterId($waiterId): ?int
    {
        if (!$waiterId) {
            return null;
        }

        // Check if the waiter exists in the tenant users table
        $waiterExists = User::where('id', $waiterId)->where('is_active', true)->exists();

        if ($waiterExists) {
            return $waiterId;
        }

        // Log the issue for debugging
        \Log::warning("Attempted to assign non-existent waiter ID: {$waiterId}");

        // Return null to allow order creation without waiter assignment
        return null;
    }

    /**
     * Get order summary for receipt
     */
    public function getOrderSummary(Order $order): array
    {
        $order->load(['items.menuItem', 'payments', 'discounts', 'table', 'branch']);

        return [
            'order' => $order,
            'items' => $order->items->where('status', '!=', 'cancelled'),
            'payments' => $order->payments->where('status', 'completed'),
            'discounts' => $order->discounts,
            'totals' => [
                'subtotal' => $order->subtotal,
                'discount_amount' => $order->discount_amount,
                'tax_amount' => $order->tax_amount,
                'service_fee' => $order->service_fee,
                'total_amount' => $order->total_amount,
                'paid_amount' => $order->total_paid_amount,
                'remaining_balance' => $order->remaining_balance,
            ],
        ];
    }
}
