<?php

namespace App\Services\Tenant;

use App\Models\Tenant\Order;
use App\Models\Tenant\OrderItem;
use App\Models\Tenant\OrderItemAddon;
use App\Models\Tenant\Food;
use App\Models\Tenant\FoodVariant;
use App\Models\Tenant\FoodAddon;
use App\Models\Tenant\Customer;
use App\Models\Tenant\CustomerAddress;
use App\Models\Tenant\Coupon;
use App\Models\Tenant\Employee;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class OrderService
{
    /**
     * Create a new order
     */
    public function createOrder(array $data): Order
    {
        return DB::transaction(function () use ($data) {
            // Create the order
            $order = Order::create([
                'restaurant_id' => $data['restaurant_id'],
                'customer_id' => $data['customer_id'] ?? null,
                'table_id' => $data['table_id'] ?? null,
                'customer_address_id' => $data['customer_address_id'] ?? null,
                'order_type' => $data['order_type'],
                'customer_name' => $data['customer_name'] ?? null,
                'customer_email' => $data['customer_email'] ?? null,
                'customer_phone' => $data['customer_phone'] ?? null,
                'delivery_address' => $data['delivery_address'] ?? null,
                'delivery_instructions' => $data['delivery_instructions'] ?? null,
                'delivery_latitude' => $data['delivery_latitude'] ?? null,
                'delivery_longitude' => $data['delivery_longitude'] ?? null,
                'special_instructions' => $data['special_instructions'] ?? null,
                'scheduled_at' => $data['scheduled_at'] ?? null,
                'payment_method' => $data['payment_method'] ?? null,
                'status' => 'pending',
                'payment_status' => 'pending',
            ]);

            // Add order items
            $subtotal = 0;
            foreach ($data['items'] as $itemData) {
                $food = Food::findOrFail($itemData['food_id']);
                $variant = null;
                
                if (isset($itemData['food_variant_id'])) {
                    $variant = FoodVariant::findOrFail($itemData['food_variant_id']);
                }

                // Calculate item price
                $unitPrice = $variant ? $variant->final_price : $food->current_price;
                $totalPrice = $unitPrice * $itemData['quantity'];

                // Create order item
                $orderItem = OrderItem::create([
                    'restaurant_id' => $data['restaurant_id'],
                    'order_id' => $order->id,
                    'food_id' => $food->id,
                    'food_variant_id' => $variant?->id,
                    'food_name' => $food->name,
                    'food_description' => $food->description,
                    'unit_price' => $unitPrice,
                    'quantity' => $itemData['quantity'],
                    'total_price' => $totalPrice,
                    'special_instructions' => $itemData['special_instructions'] ?? null,
                    'customizations' => $itemData['customizations'] ?? null,
                    'status' => 'pending',
                ]);

                $subtotal += $totalPrice;

                // Add addons
                if (isset($itemData['addons'])) {
                    foreach ($itemData['addons'] as $addonData) {
                        $addon = FoodAddon::findOrFail($addonData['food_addon_id']);
                        $addonPrice = $addon->getPriceForFood($food);
                        $addonTotal = $addonPrice * $addonData['quantity'];

                        OrderItemAddon::create([
                            'restaurant_id' => $data['restaurant_id'],
                            'order_item_id' => $orderItem->id,
                            'food_addon_id' => $addon->id,
                            'addon_name' => $addon->name,
                            'unit_price' => $addonPrice,
                            'quantity' => $addonData['quantity'],
                            'total_price' => $addonTotal,
                        ]);

                        $subtotal += $addonTotal;
                    }
                }
            }

            // Calculate fees and taxes
            $taxAmount = $this->calculateTax($subtotal);
            $deliveryFee = $this->calculateDeliveryFee($order, $subtotal);
            $serviceFee = $this->calculateServiceFee($subtotal);
            $discountAmount = 0;

            // Apply coupon if provided
            if (isset($data['coupon_code'])) {
                $coupon = Coupon::where('code', $data['coupon_code'])->first();
                if ($coupon && $coupon->isValidForOrder($order)) {
                    $discountAmount = $coupon->calculateDiscount($order);
                }
            }

            $totalAmount = $subtotal + $taxAmount + $deliveryFee + $serviceFee - $discountAmount + ($data['tip_amount'] ?? 0);

            // Update order totals
            $order->update([
                'subtotal' => $subtotal,
                'tax_amount' => $taxAmount,
                'delivery_fee' => $deliveryFee,
                'service_fee' => $serviceFee,
                'discount_amount' => $discountAmount,
                'tip_amount' => $data['tip_amount'] ?? 0,
                'total_amount' => $totalAmount,
                'estimated_preparation_time' => $this->calculatePreparationTime($order),
            ]);

            // Apply coupon if valid
            if (isset($coupon) && $discountAmount > 0) {
                $coupon->applyToOrder($order);
            }

            // Update customer statistics
            if ($order->customer) {
                $this->updateCustomerStatistics($order->customer, $order);
            }

            // Create delivery order if needed
            if ($order->order_type === 'delivery') {
                $this->createDeliveryOrder($order);
            }

            Log::info('Order created successfully', ['order_id' => $order->id, 'order_number' => $order->order_number]);

            return $order;
        });
    }

    /**
     * Update an existing order
     */
    public function updateOrder(Order $order, array $data): Order
    {
        $order->update(array_filter($data));
        
        Log::info('Order updated', ['order_id' => $order->id]);
        
        return $order->fresh();
    }

    /**
     * Update order status
     */
    public function updateOrderStatus(Order $order, string $status, ?Employee $employee = null, ?string $notes = null): void
    {
        $order->updateStatus($status, $employee, $notes);
        
        Log::info('Order status updated', [
            'order_id' => $order->id,
            'old_status' => $order->getOriginal('status'),
            'new_status' => $status,
            'changed_by' => $employee?->id
        ]);
    }

    /**
     * Cancel an order
     */
    public function cancelOrder(Order $order, string $reason, ?Employee $employee = null): void
    {
        DB::transaction(function () use ($order, $reason, $employee) {
            $order->update([
                'status' => 'cancelled',
                'cancellation_reason' => $reason,
            ]);

            // Note: Status history tracking disabled for simplified schema
            // Could log cancellation to application logs if needed

            // Handle refund if payment was made
            if ($order->payment_status === 'paid') {
                // TODO: Implement refund logic
                $order->update(['payment_status' => 'refunded']);
            }

            // Release delivery driver if assigned
            if ($order->deliveryOrder && $order->deliveryOrder->deliveryDriver) {
                $order->deliveryOrder->deliveryDriver->updateStatus('available');
            }
        });

        Log::info('Order cancelled', ['order_id' => $order->id, 'reason' => $reason]);
    }

    /**
     * Get order statistics
     */
    public function getOrderStatistics(array $filters = []): array
    {
        $query = Order::query();

        // Apply filters
        if (isset($filters['date_from'])) {
            $query->whereDate('created_at', '>=', $filters['date_from']);
        }

        if (isset($filters['date_to'])) {
            $query->whereDate('created_at', '<=', $filters['date_to']);
        }

        if (isset($filters['order_type'])) {
            $query->where('order_type', $filters['order_type']);
        }

        $orders = $query->get();

        return [
            'total_orders' => $orders->count(),
            'total_revenue' => $orders->where('payment_status', 'paid')->sum('total_amount'),
            'average_order_value' => $orders->avg('total_amount'),
            'status_breakdown' => $orders->groupBy('status')->map->count(),
            'order_type_breakdown' => $orders->groupBy('order_type')->map->count(),
            'payment_status_breakdown' => $orders->groupBy('payment_status')->map->count(),
            'hourly_distribution' => $orders->groupBy(function ($order) {
                return $order->created_at->format('H');
            })->map->count(),
        ];
    }

    /**
     * Calculate tax amount
     */
    protected function calculateTax(float $subtotal): float
    {
        // Get tax rate from restaurant settings
        $taxRate = tenant('tax_rate') ?? 0;
        return ($subtotal * $taxRate) / 100;
    }

    /**
     * Calculate delivery fee
     */
    protected function calculateDeliveryFee(Order $order, float $subtotal): float
    {
        if ($order->order_type !== 'delivery') {
            return 0;
        }

        // TODO: Implement delivery zone-based fee calculation
        return tenant('delivery_charge') ?? 0;
    }

    /**
     * Calculate service fee
     */
    protected function calculateServiceFee(float $subtotal): float
    {
        // Get service charge from restaurant settings
        $serviceCharge = tenant('service_charge') ?? 0;
        return ($subtotal * $serviceCharge) / 100;
    }

    /**
     * Calculate estimated preparation time
     */
    protected function calculatePreparationTime(Order $order): int
    {
        $totalTime = 0;
        
        foreach ($order->items as $item) {
            $foodTime = $item->food->preparation_time ?? 15;
            $totalTime = max($totalTime, $foodTime);
        }

        // Add buffer time based on order size
        $itemCount = $order->items->sum('quantity');
        $bufferTime = min($itemCount * 2, 20);

        return $totalTime + $bufferTime;
    }

    /**
     * Update customer statistics
     */
    protected function updateCustomerStatistics(Customer $customer, Order $order): void
    {
        $customer->increment('total_orders');
        $customer->increment('total_spent', $order->total_amount);
        $customer->update(['last_order_at' => now()]);

        // Update customer tier based on spending
        $customer->updateTier();

        // Add loyalty points
        $points = floor($order->total_amount / 10); // 1 point per $10 spent
        $customer->addLoyaltyPoints($points);
    }

    /**
     * Create delivery order
     */
    protected function createDeliveryOrder(Order $order): void
    {
        // TODO: Implement delivery order creation
        // This would involve finding the appropriate delivery zone,
        // calculating distance, and potentially assigning a driver
    }
}
