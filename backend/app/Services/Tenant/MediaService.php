<?php

namespace App\Services\Tenant;

use Illuminate\Http\UploadedFile;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;
use Intervention\Image\Facades\Image;

class MediaService
{
    /**
     * Upload and process media file
     */
    public function uploadMedia(UploadedFile $file, string $collection = 'default'): array
    {
        // Generate unique filename
        $filename = Str::uuid() . '.webp';
        $path = "media/{$collection}/" . date('Y/m/d');
        
        // Convert to WebP and save
        $image = Image::make($file);
        $webpData = $image->encode('webp', 90);
        
        $fullPath = $path . '/' . $filename;
        Storage::disk('public')->put($fullPath, $webpData);
        
        return [
            'filename' => $filename,
            'path' => $fullPath,
            'url' => Storage::disk('public')->url($fullPath),
            'size' => strlen($webpData),
            'mime_type' => 'image/webp',
            'original_name' => $file->getClientOriginalName(),
        ];
    }

    /**
     * Delete media file
     */
    public function deleteMedia(string $path): bool
    {
        return Storage::disk('public')->delete($path);
    }

    /**
     * Get media URL
     */
    public function getMediaUrl(string $path): string
    {
        return Storage::disk('public')->url($path);
    }
}
