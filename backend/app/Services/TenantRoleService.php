<?php

namespace App\Services;

use App\Models\User;
use App\Models\Role;
use App\Models\Tenant;
use Illuminate\Support\Facades\Log;

class TenantRoleService
{
    /**
     * Assign restaurant manager role to tenant owner
     */
    public function assignTenantOwnerRole(User $user, Tenant $tenant): bool
    {
        try {
            // Ensure restaurant_manager role exists
            $managerRole = Role::firstOrCreate(
                ['name' => 'restaurant_manager'],
                ['guard_name' => 'web']
            );

            // Assign role if user doesn't have it
            if (!$user->hasRole('restaurant_manager')) {
                $user->assignRole('restaurant_manager');
                
                Log::info("Assigned restaurant_manager role to user", [
                    'user_id' => $user->id,
                    'user_email' => $user->email,
                    'tenant_id' => $tenant->id,
                ]);
            }

            return true;
        } catch (\Exception $e) {
            Log::error("Failed to assign tenant owner role", [
                'user_id' => $user->id,
                'tenant_id' => $tenant->id,
                'error' => $e->getMessage(),
            ]);

            return false;
        }
    }

    /**
     * Check if user has access to tenant management features
     */
    public function canManageTenant(User $user, Tenant $tenant): bool
    {
        // Check if user has restaurant_manager role
        if ($user->hasRole('restaurant_manager')) {
            return true;
        }

        // Check if user has admin role (super admin access)
        if ($user->hasRole('admin')) {
            return true;
        }

        return false;
    }

    /**
     * Get user's dashboard route based on their role
     */
    public function getUserDashboardRoute(User $user): string
    {
        if ($user->hasRole('admin')) {
            return route('admin.dashboard');
        }

        if ($user->hasRole('restaurant_manager')) {
            return route('manager.dashboard');
        }

        if ($user->hasRole('waiter')) {
            return route('waiter.dashboard');
        }

        if ($user->hasRole('chef') || $user->hasRole('kitchen')) {
            return route('kitchen.dashboard');
        }

        if ($user->hasRole('delivery')) {
            return route('rider.dashboard');
        }

        // Default dashboard
        return route('dashboard');
    }

    /**
     * Ensure all required roles exist in the system
     */
    public function ensureSystemRoles(): void
    {
        $roles = [
            'admin' => 'System Administrator',
            'restaurant_manager' => 'Restaurant Manager',
            'waiter' => 'Restaurant Waiter',
            'chef' => 'Kitchen Chef',
            'kitchen' => 'Kitchen Staff',
            'delivery' => 'Delivery Rider',
        ];

        foreach ($roles as $roleName => $description) {
            Role::firstOrCreate(
                ['name' => $roleName],
                ['guard_name' => 'web']
            );
        }
    }

    /**
     * Get user's role hierarchy level (higher number = more permissions)
     */
    public function getUserRoleLevel(User $user): int
    {
        if ($user->hasRole('admin')) {
            return 100;
        }

        if ($user->hasRole('restaurant_manager')) {
            return 80;
        }

        if ($user->hasRole('chef')) {
            return 60;
        }

        if ($user->hasRole('waiter')) {
            return 40;
        }

        if ($user->hasRole('delivery')) {
            return 20;
        }

        return 0; // No role or unknown role
    }

    /**
     * Check if user can access specific features based on subscription plan
     */
    public function canAccessFeature(User $user, string $feature, Tenant $tenant): bool
    {
        // First check if user has management access
        if (!$this->canManageTenant($user, $tenant)) {
            return false;
        }

        // Get tenant's subscription plan
        $subscription = $tenant->activeSubscription;
        if (!$subscription || !$subscription->plan) {
            return false;
        }

        $plan = $subscription->plan;

        // Check feature access based on plan
        switch ($feature) {
            case 'home_delivery':
                return $plan->has_home_delivery;
            
            case 'email_marketing':
                return $plan->has_email_marketing;
            
            case 'loyalty_program':
                return $plan->has_loyalty_program;
            
            case 'advanced_analytics':
                return $plan->has_advanced_reporting;
            
            case 'api_access':
                return $plan->has_api_access;
            
            case 'multi_location':
                return $plan->has_multi_location;
            
            default:
                return true; // Allow access to basic features
        }
    }

    /**
     * Get user's accessible menu items based on role
     */
    public function getAccessibleMenuItems(User $user): array
    {
        $menuItems = [];

        if ($user->hasRole(['admin', 'restaurant_manager'])) {
            $menuItems = [
                'dashboard' => 'Dashboard',
                'orders' => 'Orders',
                'menu' => 'Menu Management',
                'categories' => 'Categories',
                'tables' => 'Tables',
                'customers' => 'Customers',
                'staff' => 'Staff Management',
                'reports' => 'Reports',
                'settings' => 'Settings',
                'subscription' => 'Subscription',
                'pages' => 'Pages',
            ];
        } elseif ($user->hasRole('waiter')) {
            $menuItems = [
                'dashboard' => 'Dashboard',
                'orders' => 'Orders',
                'tables' => 'Tables',
                'customers' => 'Customers',
                'menu' => 'Menu View',
            ];
        } elseif ($user->hasRole(['chef', 'kitchen'])) {
            $menuItems = [
                'dashboard' => 'Kitchen Dashboard',
                'orders' => 'Kitchen Orders',
                'menu' => 'Menu View',
            ];
        } elseif ($user->hasRole('delivery')) {
            $menuItems = [
                'dashboard' => 'Rider Dashboard',
                'orders' => 'Delivery Orders',
            ];
        }

        return $menuItems;
    }
}
