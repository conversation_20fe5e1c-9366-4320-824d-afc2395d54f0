<?php

namespace App\Models\Tenant;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use App\Traits\HasMediaLibrary;

class Vendor extends Model
{
    use HasFactory, HasMediaLibrary;

    protected $fillable = [
        'restaurant_id',
        'name',
        'company_name',
        'vendor_type',
        'contact_person',
        'email',
        'phone',
        'mobile',
        'website',
        'tax_id',
        'registration_number',
        'address',
        'city',
        'state',
        'country',
        'postal_code',
        'payment_terms',
        'credit_limit',
        'discount_percentage',
        'is_active',
        'notes',
        'rating',
        'total_orders',
        'total_amount_spent',
        'last_order_date',
    ];

    protected $casts = [
        'credit_limit' => 'decimal:2',
        'discount_percentage' => 'decimal:2',
        'is_active' => 'boolean',
        'rating' => 'decimal:2',
        'total_orders' => 'integer',
        'total_amount_spent' => 'decimal:2',
        'last_order_date' => 'date',
    ];

    /**
     * Get the restaurant that owns this vendor
     */
    public function restaurant(): BelongsTo
    {
        return $this->belongsTo(Restaurant::class);
    }

    /**
     * Get all expenses from this vendor
     */
    public function expenses(): HasMany
    {
        return $this->hasMany(Expense::class);
    }

    /**
     * Get all purchase orders from this vendor
     */
    public function purchaseOrders(): HasMany
    {
        return $this->hasMany(PurchaseOrder::class);
    }

    /**
     * Get vendor contacts
     */
    public function contacts(): HasMany
    {
        return $this->hasMany(VendorContact::class);
    }

    /**
     * Get full address
     */
    public function getFullAddressAttribute(): string
    {
        $parts = array_filter([
            $this->address,
            $this->city,
            $this->state,
            $this->postal_code,
            $this->country,
        ]);

        return implode(', ', $parts);
    }

    /**
     * Get total expenses for a period
     */
    public function getTotalExpenses(string $period = 'month', $date = null): float
    {
        $query = $this->expenses()->where('status', 'approved');
        $date = $date ?? now();

        switch ($period) {
            case 'today':
                $query->whereDate('expense_date', $date);
                break;
            case 'week':
                $startOfWeek = $date->copy()->startOfWeek();
                $endOfWeek = $date->copy()->endOfWeek();
                $query->whereBetween('expense_date', [$startOfWeek, $endOfWeek]);
                break;
            case 'month':
                $query->whereYear('expense_date', $date->year)
                      ->whereMonth('expense_date', $date->month);
                break;
            case 'year':
                $query->whereYear('expense_date', $date->year);
                break;
        }

        return $query->sum('amount');
    }

    /**
     * Get vendor performance metrics
     */
    public function getPerformanceMetrics(): array
    {
        $expenses = $this->expenses()->where('status', 'approved');
        
        return [
            'total_transactions' => $expenses->count(),
            'total_amount' => $expenses->sum('amount'),
            'average_transaction' => $expenses->avg('amount') ?? 0,
            'last_transaction' => $expenses->latest('expense_date')->first()?->expense_date,
            'monthly_average' => $this->getMonthlyAverage(),
            'payment_reliability' => $this->getPaymentReliability(),
        ];
    }

    /**
     * Get monthly average spending
     */
    protected function getMonthlyAverage(): float
    {
        $firstExpense = $this->expenses()->oldest('expense_date')->first();
        
        if (!$firstExpense) {
            return 0;
        }

        $monthsSinceFirst = $firstExpense->expense_date->diffInMonths(now()) + 1;
        $totalAmount = $this->expenses()->where('status', 'approved')->sum('amount');

        return $totalAmount / $monthsSinceFirst;
    }

    /**
     * Get payment reliability score (based on on-time payments)
     */
    protected function getPaymentReliability(): float
    {
        $totalExpenses = $this->expenses()->count();
        
        if ($totalExpenses === 0) {
            return 100;
        }

        $onTimePayments = $this->expenses()
            ->whereNotNull('paid_at')
            ->whereRaw('paid_at <= due_date')
            ->count();

        return ($onTimePayments / $totalExpenses) * 100;
    }

    /**
     * Get vendor spending trend
     */
    public function getSpendingTrend(int $months = 6): array
    {
        $trends = [];
        
        for ($i = $months - 1; $i >= 0; $i--) {
            $date = now()->subMonths($i);
            $amount = $this->getTotalExpenses('month', $date);
            
            $trends[] = [
                'month' => $date->format('M Y'),
                'amount' => $amount,
            ];
        }

        return $trends;
    }

    /**
     * Update vendor statistics
     */
    public function updateStatistics(): void
    {
        $approvedExpenses = $this->expenses()->where('status', 'approved');
        
        $this->update([
            'total_orders' => $approvedExpenses->count(),
            'total_amount_spent' => $approvedExpenses->sum('amount'),
            'last_order_date' => $approvedExpenses->latest('expense_date')->first()?->expense_date,
        ]);
    }

    /**
     * Check if vendor is within credit limit
     */
    public function isWithinCreditLimit(float $additionalAmount = 0): bool
    {
        if (!$this->credit_limit || $this->credit_limit <= 0) {
            return true; // No credit limit set
        }

        $pendingAmount = $this->expenses()
            ->whereIn('status', ['pending', 'approved'])
            ->whereNull('paid_at')
            ->sum('amount');

        return ($pendingAmount + $additionalAmount) <= $this->credit_limit;
    }

    /**
     * Get outstanding balance
     */
    public function getOutstandingBalanceAttribute(): float
    {
        return $this->expenses()
            ->whereIn('status', ['approved'])
            ->whereNull('paid_at')
            ->sum('amount');
    }

    /**
     * Get overdue amount
     */
    public function getOverdueAmountAttribute(): float
    {
        return $this->expenses()
            ->where('status', 'approved')
            ->whereNull('paid_at')
            ->where('due_date', '<', now())
            ->sum('amount');
    }

    /**
     * Scope for active vendors
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope by vendor type
     */
    public function scopeByType($query, string $type)
    {
        return $query->where('vendor_type', $type);
    }

    /**
     * Scope for vendors with outstanding balance
     */
    public function scopeWithOutstandingBalance($query)
    {
        return $query->whereHas('expenses', function ($q) {
            $q->where('status', 'approved')
              ->whereNull('paid_at');
        });
    }

    /**
     * Scope for vendors with overdue payments
     */
    public function scopeWithOverduePayments($query)
    {
        return $query->whereHas('expenses', function ($q) {
            $q->where('status', 'approved')
              ->whereNull('paid_at')
              ->where('due_date', '<', now());
        });
    }

    /**
     * Boot the model
     */
    protected static function boot()
    {
        parent::boot();

        static::creating(function ($vendor) {
            if (is_null($vendor->rating)) {
                $vendor->rating = 5.0;
            }
            if (empty($vendor->vendor_type)) {
                $vendor->vendor_type = 'supplier';
            }
        });
    }
}
