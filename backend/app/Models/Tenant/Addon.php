<?php

namespace App\Models\Tenant;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\SoftDeletes;

class Addon extends Model
{
    use HasFactory, SoftDeletes;

    protected $table = 'food_addons'; // Use existing table

    protected $fillable = [
        'restaurant_id',
        'name',
        'description',
        'price',
        'category',
        'is_available',
        'is_active',
        'track_quantity',
        'quantity_available',
        'minimum_quantity',
        'sort_order',
        'image',
    ];

    protected $casts = [
        'price' => 'decimal:2',
        'is_available' => 'boolean',
        'is_active' => 'boolean',
        'track_quantity' => 'boolean',
        'quantity_available' => 'integer',
        'minimum_quantity' => 'integer',
        'sort_order' => 'integer',
    ];

    /**
     * Get the restaurant that owns this addon
     */
    public function restaurant(): BelongsTo
    {
        return $this->belongsTo(Restaurant::class);
    }

    /**
     * Get the menu items that can use this addon
     */
    public function menuItems(): BelongsToMany
    {
        return $this->belongsToMany(MenuItem::class, 'food_addon_pivot', 'food_addon_id', 'menu_item_id')
            ->withPivot(['is_required', 'max_quantity', 'price_override'])
            ->withTimestamps();
    }

    /**
     * Check if addon is available
     */
    public function isAvailable(): bool
    {
        if (!$this->is_available || !$this->is_active) {
            return false;
        }

        // Check quantity if tracking
        if ($this->track_quantity && $this->quantity_available <= 0) {
            return false;
        }

        return true;
    }

    /**
     * Get the price for a specific menu item (with override if set)
     */
    public function getPriceForMenuItem(MenuItem $menuItem): float
    {
        $pivot = $this->menuItems()->where('menu_item_id', $menuItem->id)->first();
        
        if ($pivot && $pivot->pivot->price_override) {
            return $pivot->pivot->price_override;
        }
        
        return $this->price;
    }

    /**
     * Check if addon is required for a specific menu item
     */
    public function isRequiredForMenuItem(MenuItem $menuItem): bool
    {
        $pivot = $this->menuItems()->where('menu_item_id', $menuItem->id)->first();
        
        return $pivot ? $pivot->pivot->is_required : false;
    }

    /**
     * Get max quantity for a specific menu item
     */
    public function getMaxQuantityForMenuItem(MenuItem $menuItem): int
    {
        $pivot = $this->menuItems()->where('menu_item_id', $menuItem->id)->first();
        
        return $pivot ? $pivot->pivot->max_quantity : 1;
    }

    /**
     * Scope for active addons
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope for available addons
     */
    public function scopeAvailable($query)
    {
        return $query->where('is_available', true);
    }

    /**
     * Scope by category
     */
    public function scopeByCategory($query, $category)
    {
        return $query->where('category', $category);
    }

    /**
     * Scope for ordering by sort order
     */
    public function scopeOrdered($query)
    {
        return $query->orderBy('sort_order')->orderBy('name');
    }

    /**
     * Get available categories
     */
    public static function getCategories(): array
    {
        return [
            'extras' => 'Extras',
            'sauces' => 'Sauces',
            'sides' => 'Sides',
            'drinks' => 'Drinks',
            'toppings' => 'Toppings',
            'seasonings' => 'Seasonings',
            'other' => 'Other',
        ];
    }
}
