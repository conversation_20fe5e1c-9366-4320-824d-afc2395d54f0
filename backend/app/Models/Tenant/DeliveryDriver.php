<?php

namespace App\Models\Tenant;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

class DeliveryDriver extends Model
{
    use HasFactory;

    protected $fillable = [
        'restaurant_id',
        'employee_id',
        'license_number',
        'license_expiry',
        'vehicle_type',
        'vehicle_model',
        'vehicle_plate_number',
        'insurance_number',
        'insurance_expiry',
        'status',
        'current_latitude',
        'current_longitude',
        'last_location_update',
        'total_deliveries',
        'average_rating',
        'rating_count',
        'average_delivery_time',
        'completed_orders_today',
        'is_active',
        'working_zones',
    ];

    protected $casts = [
        'license_expiry' => 'date',
        'insurance_expiry' => 'date',
        'current_latitude' => 'decimal:8',
        'current_longitude' => 'decimal:8',
        'last_location_update' => 'datetime',
        'average_rating' => 'decimal:2',
        'average_delivery_time' => 'decimal:2',
        'is_active' => 'boolean',
        'working_zones' => 'array',
    ];

    /**
     * Get the restaurant that owns this driver
     */
    public function restaurant(): BelongsTo
    {
        return $this->belongsTo(Restaurant::class);
    }

    /**
     * Get the employee record
     */
    public function employee(): BelongsTo
    {
        return $this->belongsTo(Employee::class);
    }

    /**
     * Get the delivery orders assigned to this driver
     */
    public function deliveryOrders(): HasMany
    {
        return $this->hasMany(DeliveryOrder::class);
    }

    /**
     * Get current active delivery orders
     */
    public function activeDeliveryOrders(): HasMany
    {
        return $this->deliveryOrders()->whereIn('status', ['assigned', 'picked_up', 'on_the_way']);
    }

    /**
     * Check if driver is available for new deliveries
     */
    public function isAvailable(): bool
    {
        return $this->is_active && 
               $this->status === 'available' && 
               $this->activeDeliveryOrders()->count() === 0;
    }

    /**
     * Update driver location
     */
    public function updateLocation(float $latitude, float $longitude): void
    {
        $this->update([
            'current_latitude' => $latitude,
            'current_longitude' => $longitude,
            'last_location_update' => now(),
        ]);
    }

    /**
     * Update driver status
     */
    public function updateStatus(string $status): void
    {
        $this->update(['status' => $status]);
    }

    /**
     * Calculate distance to a location
     */
    public function distanceTo(float $latitude, float $longitude): float
    {
        if (!$this->current_latitude || !$this->current_longitude) {
            return 0;
        }

        $earthRadius = 6371; // km

        $latDelta = deg2rad($latitude - $this->current_latitude);
        $lonDelta = deg2rad($longitude - $this->current_longitude);

        $a = sin($latDelta / 2) * sin($latDelta / 2) +
             cos(deg2rad($this->current_latitude)) * cos(deg2rad($latitude)) *
             sin($lonDelta / 2) * sin($lonDelta / 2);

        $c = 2 * atan2(sqrt($a), sqrt(1 - $a));

        return $earthRadius * $c;
    }

    /**
     * Update rating after delivery
     */
    public function updateRating(int $newRating): void
    {
        $totalRating = ($this->average_rating * $this->rating_count) + $newRating;
        $newCount = $this->rating_count + 1;
        $newAverage = $totalRating / $newCount;

        $this->update([
            'average_rating' => $newAverage,
            'rating_count' => $newCount,
        ]);
    }

    /**
     * Complete a delivery
     */
    public function completeDelivery(DeliveryOrder $deliveryOrder): void
    {
        $this->increment('total_deliveries');
        $this->increment('completed_orders_today');

        // Update average delivery time
        if ($deliveryOrder->actual_delivery_time) {
            $totalTime = ($this->average_delivery_time * ($this->total_deliveries - 1)) + $deliveryOrder->actual_delivery_time;
            $this->update(['average_delivery_time' => $totalTime / $this->total_deliveries]);
        }
    }

    /**
     * Reset daily counters
     */
    public function resetDailyCounters(): void
    {
        $this->update(['completed_orders_today' => 0]);
    }

    /**
     * Check if driver can work in a zone
     */
    public function canWorkInZone(int $zoneId): bool
    {
        return in_array($zoneId, $this->working_zones ?? []);
    }

    /**
     * Get driver's full name
     */
    public function getFullNameAttribute(): string
    {
        return $this->employee->full_name ?? 'Unknown Driver';
    }

    /**
     * Scope for active drivers
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope for available drivers
     */
    public function scopeAvailable($query)
    {
        return $query->where('status', 'available');
    }

    /**
     * Scope for drivers working in a specific zone
     */
    public function scopeWorkingInZone($query, $zoneId)
    {
        return $query->whereJsonContains('working_zones', $zoneId);
    }

    /**
     * Scope for drivers with recent location updates
     */
    public function scopeWithRecentLocation($query, $minutes = 30)
    {
        return $query->where('last_location_update', '>=', now()->subMinutes($minutes));
    }
}
