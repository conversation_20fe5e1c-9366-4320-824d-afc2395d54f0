<?php

namespace App\Models\Tenant;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class OrderDiscount extends Model
{
    use HasFactory;

    protected $fillable = [
        'order_id',
        'order_item_id',
        'discount_type',
        'discount_name',
        'discount_value',
        'discount_amount',
        'applied_to',
        'coupon_code',
        'applied_by',
        'reason',
        'conditions',
    ];

    protected $casts = [
        'discount_value' => 'decimal:2',
        'discount_amount' => 'decimal:2',
        'conditions' => 'array',
    ];

    /**
     * Get the order that owns this discount
     */
    public function order(): BelongsTo
    {
        return $this->belongsTo(Order::class);
    }

    /**
     * Get the order item that owns this discount (if item-level discount)
     */
    public function orderItem(): BelongsTo
    {
        return $this->belongsTo(OrderItem::class);
    }

    /**
     * Get the user who applied this discount
     */
    public function appliedBy(): BelongsTo
    {
        return $this->belongsTo(\App\Models\User::class, 'applied_by');
    }

    /**
     * Check if discount is applied to order level
     */
    public function isOrderLevel(): bool
    {
        return $this->applied_to === 'order';
    }

    /**
     * Check if discount is applied to item level
     */
    public function isItemLevel(): bool
    {
        return $this->applied_to === 'item';
    }

    /**
     * Check if discount is percentage based
     */
    public function isPercentage(): bool
    {
        return $this->discount_type === 'percentage';
    }

    /**
     * Check if discount is fixed amount
     */
    public function isFixedAmount(): bool
    {
        return $this->discount_type === 'fixed_amount';
    }

    /**
     * Calculate discount amount for given base amount
     */
    public function calculateDiscountAmount(float $baseAmount): float
    {
        if ($this->isPercentage()) {
            return ($baseAmount * $this->discount_value) / 100;
        }
        
        return min($this->discount_value, $baseAmount);
    }

    /**
     * Scope for order-level discounts
     */
    public function scopeOrderLevel($query)
    {
        return $query->where('applied_to', 'order');
    }

    /**
     * Scope for item-level discounts
     */
    public function scopeItemLevel($query)
    {
        return $query->where('applied_to', 'item');
    }

    /**
     * Scope for specific discount type
     */
    public function scopeByType($query, string $type)
    {
        return $query->where('discount_type', $type);
    }

    /**
     * Scope for coupon-based discounts
     */
    public function scopeByCoupon($query, string $couponCode)
    {
        return $query->where('coupon_code', $couponCode);
    }
}
