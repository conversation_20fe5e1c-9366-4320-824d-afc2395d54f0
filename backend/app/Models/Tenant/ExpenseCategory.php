<?php

namespace App\Models\Tenant;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use App\Traits\HasMediaLibrary;

class ExpenseCategory extends Model
{
    use HasFactory, HasMediaLibrary;

    protected $fillable = [
        'restaurant_id',
        'name',
        'description',
        'color_code',
        'icon',
        'is_active',
        'sort_order',
        'budget_limit', // Monthly budget limit
        'parent_id', // For subcategories
    ];

    protected $casts = [
        'is_active' => 'boolean',
        'sort_order' => 'integer',
        'budget_limit' => 'decimal:2',
    ];

    /**
     * Get the restaurant that owns this expense category
     */
    public function restaurant(): BelongsTo
    {
        return $this->belongsTo(Restaurant::class);
    }

    /**
     * Get the parent category
     */
    public function parent(): BelongsTo
    {
        return $this->belongsTo(ExpenseCategory::class, 'parent_id');
    }

    /**
     * Get child categories (subcategories)
     */
    public function children(): HasMany
    {
        return $this->hasMany(ExpenseCategory::class, 'parent_id');
    }

    /**
     * Get all expenses in this category
     */
    public function expenses(): HasMany
    {
        return $this->hasMany(Expense::class);
    }

    /**
     * Get total expenses for a period
     */
    public function getTotalExpenses(string $period = 'month', $date = null): float
    {
        $query = $this->expenses()->where('status', 'approved');
        $date = $date ?? now();

        switch ($period) {
            case 'today':
                $query->whereDate('expense_date', $date);
                break;
            case 'week':
                $startOfWeek = $date->copy()->startOfWeek();
                $endOfWeek = $date->copy()->endOfWeek();
                $query->whereBetween('expense_date', [$startOfWeek, $endOfWeek]);
                break;
            case 'month':
                $query->whereYear('expense_date', $date->year)
                      ->whereMonth('expense_date', $date->month);
                break;
            case 'year':
                $query->whereYear('expense_date', $date->year);
                break;
        }

        return $query->sum('amount');
    }

    /**
     * Get budget utilization percentage
     */
    public function getBudgetUtilizationAttribute(): float
    {
        if (!$this->budget_limit || $this->budget_limit <= 0) {
            return 0;
        }

        $totalExpenses = $this->getTotalExpenses('month');
        return ($totalExpenses / $this->budget_limit) * 100;
    }

    /**
     * Check if budget is exceeded
     */
    public function isBudgetExceeded(): bool
    {
        return $this->budget_utilization > 100;
    }

    /**
     * Get remaining budget
     */
    public function getRemainingBudgetAttribute(): float
    {
        if (!$this->budget_limit) {
            return 0;
        }

        $totalExpenses = $this->getTotalExpenses('month');
        return max(0, $this->budget_limit - $totalExpenses);
    }

    /**
     * Get expense trend (compared to previous period)
     */
    public function getExpenseTrend(string $period = 'month'): array
    {
        $currentPeriodExpenses = $this->getTotalExpenses($period);
        
        $previousDate = match($period) {
            'today' => now()->subDay(),
            'week' => now()->subWeek(),
            'month' => now()->subMonth(),
            'year' => now()->subYear(),
            default => now()->subMonth(),
        };

        $previousPeriodExpenses = $this->getTotalExpenses($period, $previousDate);

        $change = $currentPeriodExpenses - $previousPeriodExpenses;
        $percentageChange = $previousPeriodExpenses > 0 
            ? ($change / $previousPeriodExpenses) * 100 
            : 0;

        return [
            'current' => $currentPeriodExpenses,
            'previous' => $previousPeriodExpenses,
            'change' => $change,
            'percentage_change' => $percentageChange,
            'trend' => $change > 0 ? 'up' : ($change < 0 ? 'down' : 'stable'),
        ];
    }

    /**
     * Get category hierarchy path
     */
    public function getHierarchyPathAttribute(): string
    {
        $path = [$this->name];
        $parent = $this->parent;

        while ($parent) {
            array_unshift($path, $parent->name);
            $parent = $parent->parent;
        }

        return implode(' > ', $path);
    }

    /**
     * Scope for active categories
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope for parent categories (no parent)
     */
    public function scopeParents($query)
    {
        return $query->whereNull('parent_id');
    }

    /**
     * Scope for ordered categories
     */
    public function scopeOrdered($query)
    {
        return $query->orderBy('sort_order')->orderBy('name');
    }

    /**
     * Boot the model
     */
    protected static function boot()
    {
        parent::boot();

        static::creating(function ($category) {
            if (is_null($category->sort_order)) {
                $category->sort_order = self::max('sort_order') + 1;
            }
            if (empty($category->color_code)) {
                $category->color_code = sprintf('#%06X', mt_rand(0, 0xFFFFFF));
            }
        });
    }
}
