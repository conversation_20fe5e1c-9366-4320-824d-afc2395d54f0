<?php

namespace App\Models\Tenant;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use App\Traits\HasMediaLibrary;

class InventoryCategory extends Model
{
    use HasFactory, HasMediaLibrary;

    protected $fillable = [
        'restaurant_id',
        'name',
        'description',
        'color_code',
        'icon',
        'is_active',
        'sort_order',
        'parent_id',
        'storage_requirements', // refrigerated, frozen, dry, etc.
        'shelf_life_days', // default shelf life for items in this category
    ];

    protected $casts = [
        'is_active' => 'boolean',
        'sort_order' => 'integer',
        'shelf_life_days' => 'integer',
    ];

    /**
     * Get the restaurant that owns this inventory category
     */
    public function restaurant(): BelongsTo
    {
        return $this->belongsTo(Restaurant::class);
    }

    /**
     * Get the parent category
     */
    public function parent(): BelongsTo
    {
        return $this->belongsTo(InventoryCategory::class, 'parent_id');
    }

    /**
     * Get child categories (subcategories)
     */
    public function children(): HasMany
    {
        return $this->hasMany(InventoryCategory::class, 'parent_id');
    }

    /**
     * Get all inventory items in this category
     */
    public function inventoryItems(): HasMany
    {
        return $this->hasMany(InventoryItem::class);
    }

    /**
     * Get category hierarchy path
     */
    public function getHierarchyPathAttribute(): string
    {
        $path = [$this->name];
        $parent = $this->parent;

        while ($parent) {
            array_unshift($path, $parent->name);
            $parent = $parent->parent;
        }

        return implode(' > ', $path);
    }

    /**
     * Get total inventory value for this category
     */
    public function getTotalInventoryValue(): float
    {
        return $this->inventoryItems()
            ->where('is_active', true)
            ->get()
            ->sum(function ($item) {
                return $item->current_stock * $item->unit_cost;
            });
    }

    /**
     * Get low stock items count
     */
    public function getLowStockItemsCount(): int
    {
        return $this->inventoryItems()
            ->where('is_active', true)
            ->whereRaw('current_stock <= minimum_stock')
            ->count();
    }

    /**
     * Get expired items count
     */
    public function getExpiredItemsCount(): int
    {
        return $this->inventoryItems()
            ->where('is_active', true)
            ->whereHas('stockBatches', function ($query) {
                $query->where('expiry_date', '<', now())
                      ->where('quantity', '>', 0);
            })
            ->count();
    }

    /**
     * Get items expiring soon count
     */
    public function getExpiringSoonItemsCount(int $days = 7): int
    {
        return $this->inventoryItems()
            ->where('is_active', true)
            ->whereHas('stockBatches', function ($query) use ($days) {
                $query->whereBetween('expiry_date', [now(), now()->addDays($days)])
                      ->where('quantity', '>', 0);
            })
            ->count();
    }

    /**
     * Scope for active categories
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope for parent categories (no parent)
     */
    public function scopeParents($query)
    {
        return $query->whereNull('parent_id');
    }

    /**
     * Scope for ordered categories
     */
    public function scopeOrdered($query)
    {
        return $query->orderBy('sort_order')->orderBy('name');
    }

    /**
     * Boot the model
     */
    protected static function boot()
    {
        parent::boot();

        static::creating(function ($category) {
            if (is_null($category->sort_order)) {
                $category->sort_order = self::max('sort_order') + 1;
            }
            if (empty($category->color_code)) {
                $category->color_code = sprintf('#%06X', mt_rand(0, 0xFFFFFF));
            }
        });
    }
}
