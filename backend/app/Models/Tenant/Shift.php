<?php

namespace App\Models\Tenant;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Carbon\Carbon;

class Shift extends Model
{
    use HasFactory;

    protected $fillable = [
        'restaurant_id',
        'employee_id',
        'department_id',
        'shift_date',
        'start_time',
        'end_time',
        'break_duration', // in minutes
        'position',
        'status',
        'notes',
        'created_by',
        'is_recurring',
        'recurring_pattern', // daily, weekly, monthly
        'recurring_until',
    ];

    protected $casts = [
        'shift_date' => 'date',
        'start_time' => 'datetime',
        'end_time' => 'datetime',
        'break_duration' => 'integer',
        'is_recurring' => 'boolean',
        'recurring_until' => 'date',
    ];

    /**
     * Get the restaurant that owns this shift
     */
    public function restaurant(): BelongsTo
    {
        return $this->belongsTo(Restaurant::class);
    }

    /**
     * Get the employee
     */
    public function employee(): BelongsTo
    {
        return $this->belongsTo(Employee::class);
    }

    /**
     * Get the department
     */
    public function department(): BelongsTo
    {
        return $this->belongsTo(Department::class);
    }

    /**
     * Get the user who created this shift
     */
    public function creator(): BelongsTo
    {
        return $this->belongsTo(\App\Models\User::class, 'created_by');
    }

    /**
     * Get shift duration in hours
     */
    public function getDurationHoursAttribute(): float
    {
        if (!$this->start_time || !$this->end_time) {
            return 0;
        }

        $duration = $this->start_time->diffInMinutes($this->end_time);
        $breakMinutes = $this->break_duration ?? 0;
        
        return ($duration - $breakMinutes) / 60;
    }

    /**
     * Get shift duration in minutes
     */
    public function getDurationMinutesAttribute(): int
    {
        if (!$this->start_time || !$this->end_time) {
            return 0;
        }

        $duration = $this->start_time->diffInMinutes($this->end_time);
        $breakMinutes = $this->break_duration ?? 0;
        
        return $duration - $breakMinutes;
    }

    /**
     * Check if shift is today
     */
    public function isToday(): bool
    {
        return $this->shift_date->isToday();
    }

    /**
     * Check if shift is in the past
     */
    public function isPast(): bool
    {
        return $this->shift_date->isPast();
    }

    /**
     * Check if shift is in the future
     */
    public function isFuture(): bool
    {
        return $this->shift_date->isFuture();
    }

    /**
     * Check if shift is currently active
     */
    public function isActive(): bool
    {
        if (!$this->isToday()) {
            return false;
        }

        $now = now();
        return $now->between($this->start_time, $this->end_time);
    }

    /**
     * Check if shift has started
     */
    public function hasStarted(): bool
    {
        if (!$this->isToday()) {
            return $this->isPast();
        }

        return now()->greaterThanOrEqualTo($this->start_time);
    }

    /**
     * Check if shift has ended
     */
    public function hasEnded(): bool
    {
        if (!$this->isToday()) {
            return $this->isPast();
        }

        return now()->greaterThanOrEqualTo($this->end_time);
    }

    /**
     * Get shift status label
     */
    public function getStatusLabelAttribute(): string
    {
        switch ($this->status) {
            case 'scheduled':
                return 'Scheduled';
            case 'confirmed':
                return 'Confirmed';
            case 'in_progress':
                return 'In Progress';
            case 'completed':
                return 'Completed';
            case 'cancelled':
                return 'Cancelled';
            case 'no_show':
                return 'No Show';
            default:
                return ucfirst($this->status);
        }
    }

    /**
     * Get time range display
     */
    public function getTimeRangeAttribute(): string
    {
        if (!$this->start_time || !$this->end_time) {
            return 'Time not set';
        }

        return $this->start_time->format('H:i') . ' - ' . $this->end_time->format('H:i');
    }

    /**
     * Get formatted shift date and time
     */
    public function getFormattedDateTimeAttribute(): string
    {
        $date = $this->shift_date->format('M j, Y');
        $time = $this->time_range;
        
        return "{$date} ({$time})";
    }

    /**
     * Calculate estimated labor cost for this shift
     */
    public function getEstimatedLaborCostAttribute(): float
    {
        if (!$this->employee) {
            return 0;
        }

        if ($this->employee->employment_type === 'hourly') {
            return $this->duration_hours * $this->employee->hourly_rate;
        }

        // For salary employees, calculate daily rate
        if ($this->employee->employment_type === 'salary') {
            $dailyRate = $this->employee->salary / 365;
            return $dailyRate;
        }

        return 0;
    }

    /**
     * Mark shift as confirmed
     */
    public function confirm(): void
    {
        $this->update(['status' => 'confirmed']);
    }

    /**
     * Mark shift as in progress
     */
    public function start(): void
    {
        $this->update(['status' => 'in_progress']);
    }

    /**
     * Mark shift as completed
     */
    public function complete(): void
    {
        $this->update(['status' => 'completed']);
    }

    /**
     * Cancel shift
     */
    public function cancel(string $reason = null): void
    {
        $this->update([
            'status' => 'cancelled',
            'notes' => $this->notes . ($reason ? "\nCancelled: {$reason}" : "\nCancelled"),
        ]);
    }

    /**
     * Mark as no show
     */
    public function markNoShow(): void
    {
        $this->update(['status' => 'no_show']);
    }

    /**
     * Generate recurring shifts
     */
    public function generateRecurringShifts(): array
    {
        if (!$this->is_recurring || !$this->recurring_pattern || !$this->recurring_until) {
            return [];
        }

        $shifts = [];
        $currentDate = $this->shift_date->copy();
        
        while ($currentDate->lte($this->recurring_until)) {
            switch ($this->recurring_pattern) {
                case 'daily':
                    $currentDate->addDay();
                    break;
                case 'weekly':
                    $currentDate->addWeek();
                    break;
                case 'monthly':
                    $currentDate->addMonth();
                    break;
                default:
                    break 2; // Exit the loop
            }

            if ($currentDate->lte($this->recurring_until)) {
                $newShift = $this->replicate();
                $newShift->shift_date = $currentDate->copy();
                $newShift->start_time = $currentDate->copy()->setTimeFrom($this->start_time);
                $newShift->end_time = $currentDate->copy()->setTimeFrom($this->end_time);
                $newShift->is_recurring = false; // Prevent infinite recursion
                $newShift->save();
                
                $shifts[] = $newShift;
            }
        }

        return $shifts;
    }

    /**
     * Check for shift conflicts
     */
    public function hasConflicts(): bool
    {
        return self::where('employee_id', $this->employee_id)
            ->where('shift_date', $this->shift_date)
            ->where('id', '!=', $this->id ?? 0)
            ->where(function ($query) {
                $query->whereBetween('start_time', [$this->start_time, $this->end_time])
                      ->orWhereBetween('end_time', [$this->start_time, $this->end_time])
                      ->orWhere(function ($q) {
                          $q->where('start_time', '<=', $this->start_time)
                            ->where('end_time', '>=', $this->end_time);
                      });
            })
            ->whereNotIn('status', ['cancelled', 'no_show'])
            ->exists();
    }

    /**
     * Scope for today's shifts
     */
    public function scopeToday($query)
    {
        return $query->whereDate('shift_date', today());
    }

    /**
     * Scope for upcoming shifts
     */
    public function scopeUpcoming($query, int $days = 7)
    {
        return $query->whereBetween('shift_date', [now(), now()->addDays($days)]);
    }

    /**
     * Scope for active shifts
     */
    public function scopeActive($query)
    {
        return $query->whereNotIn('status', ['cancelled', 'no_show']);
    }

    /**
     * Scope by status
     */
    public function scopeByStatus($query, string $status)
    {
        return $query->where('status', $status);
    }

    /**
     * Scope by date range
     */
    public function scopeDateRange($query, $startDate, $endDate)
    {
        return $query->whereBetween('shift_date', [$startDate, $endDate]);
    }

    /**
     * Boot the model
     */
    protected static function boot()
    {
        parent::boot();

        static::creating(function ($shift) {
            if (empty($shift->status)) {
                $shift->status = 'scheduled';
            }
        });

        static::created(function ($shift) {
            // Generate recurring shifts if applicable
            if ($shift->is_recurring) {
                $shift->generateRecurringShifts();
            }
        });
    }
}
