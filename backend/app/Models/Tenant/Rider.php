<?php

namespace App\Models\Tenant;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Support\Facades\Storage;

class Rider extends Model
{
    use HasFactory;

    protected $fillable = [
        'user_id',
        'employee_id',
        'name',
        'phone',
        'email',
        'vehicle_type',
        'license_number',
        'status',
        'hire_date',
        'address',
        'profile_photo',
        'delivery_status',
        'current_delivery_count',
        'max_concurrent_deliveries',
        'current_latitude',
        'current_longitude',
        'last_location_update',
        'total_deliveries',
        'average_rating',
        'rating_count',
        'average_delivery_time',
        'completed_orders_today',
        'total_earnings',
        'commission_rate',
        'shift_start_time',
        'shift_end_time',
        'working_days',
        'emergency_contact_name',
        'emergency_contact_phone',
        'notes',
        'is_verified',
        'last_active_at',
    ];

    protected $casts = [
        'hire_date' => 'date',
        'current_latitude' => 'decimal:8',
        'current_longitude' => 'decimal:8',
        'last_location_update' => 'datetime',
        'average_rating' => 'decimal:2',
        'average_delivery_time' => 'decimal:2',
        'total_earnings' => 'decimal:2',
        'commission_rate' => 'decimal:2',
        'shift_start_time' => 'datetime:H:i',
        'shift_end_time' => 'datetime:H:i',
        'working_days' => 'array',
        'is_verified' => 'boolean',
        'last_active_at' => 'datetime',
    ];

    /**
     * Get the user account for this rider
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(\App\Models\User::class);
    }

    /**
     * Get all orders assigned to this rider
     */
    public function orders(): HasMany
    {
        return $this->hasMany(Order::class, 'delivery_rider_id');
    }

    /**
     * Get order history for this rider
     */
    public function orderHistory(): HasMany
    {
        return $this->hasMany(RiderOrderHistory::class);
    }

    /**
     * Get current active orders
     */
    public function activeOrders(): HasMany
    {
        return $this->hasMany(Order::class, 'delivery_rider_id')
            ->whereIn('delivery_status', ['assigned', 'picked_up', 'en_route']);
    }

    /**
     * Get completed orders for today
     */
    public function todayOrders(): HasMany
    {
        return $this->hasMany(Order::class, 'delivery_rider_id')
            ->whereDate('created_at', today())
            ->where('delivery_status', 'delivered');
    }

    /**
     * Check if rider is available for new deliveries
     */
    public function isAvailable(): bool
    {
        return $this->status === 'active' 
            && $this->delivery_status === 'available' 
            && $this->current_delivery_count < $this->max_concurrent_deliveries;
    }

    /**
     * Check if rider is currently working (within shift hours)
     */
    public function isInShift(): bool
    {
        if (!$this->shift_start_time || !$this->shift_end_time) {
            return true; // No shift restrictions
        }

        $now = now()->format('H:i');
        $start = $this->shift_start_time->format('H:i');
        $end = $this->shift_end_time->format('H:i');

        if ($start <= $end) {
            return $now >= $start && $now <= $end;
        } else {
            // Overnight shift
            return $now >= $start || $now <= $end;
        }
    }

    /**
     * Check if rider works today
     */
    public function worksToday(): bool
    {
        if (!$this->working_days) {
            return true; // No day restrictions
        }

        $today = now()->dayOfWeek; // 0 = Sunday, 6 = Saturday
        return in_array($today, $this->working_days);
    }

    /**
     * Assign order to rider
     */
    public function assignOrder(Order $order): void
    {
        $order->update([
            'delivery_rider_id' => $this->id,
            'delivery_status' => 'assigned',
        ]);

        $this->increment('current_delivery_count');
        $this->update(['last_active_at' => now()]);

        // Log the assignment
        $this->orderHistory()->create([
            'order_id' => $order->id,
            'action' => 'assigned',
            'action_time' => now(),
            'latitude' => $this->current_latitude,
            'longitude' => $this->current_longitude,
        ]);
    }

    /**
     * Complete delivery
     */
    public function completeDelivery(Order $order): void
    {
        $this->decrement('current_delivery_count');
        $this->increment('total_deliveries');
        $this->increment('completed_orders_today');

        // Calculate commission
        $commission = ($order->delivery_fee * $this->commission_rate) / 100;
        $this->increment('total_earnings', $commission);

        // Update order
        $order->update([
            'delivery_status' => 'delivered',
            'delivery_time' => now(),
            'rider_commission' => $commission,
        ]);

        // Update average delivery time
        if ($order->pickup_time) {
            $deliveryTime = $order->pickup_time->diffInMinutes(now());
            $totalTime = ($this->average_delivery_time * ($this->total_deliveries - 1)) + $deliveryTime;
            $this->update(['average_delivery_time' => $totalTime / $this->total_deliveries]);
        }

        // Log the completion
        $this->orderHistory()->create([
            'order_id' => $order->id,
            'action' => 'delivered',
            'action_time' => now(),
            'latitude' => $this->current_latitude,
            'longitude' => $this->current_longitude,
        ]);

        // Update status to available if no more active orders
        if ($this->current_delivery_count <= 0) {
            $this->update(['delivery_status' => 'available']);
        }
    }

    /**
     * Update rider rating
     */
    public function updateRating(int $rating): void
    {
        $totalRating = ($this->average_rating * $this->rating_count) + $rating;
        $newCount = $this->rating_count + 1;
        $newAverage = $totalRating / $newCount;

        $this->update([
            'average_rating' => $newAverage,
            'rating_count' => $newCount,
        ]);
    }

    /**
     * Update location
     */
    public function updateLocation(float $latitude, float $longitude): void
    {
        $this->update([
            'current_latitude' => $latitude,
            'current_longitude' => $longitude,
            'last_location_update' => now(),
            'last_active_at' => now(),
        ]);
    }

    /**
     * Get profile photo URL
     */
    public function getProfilePhotoUrlAttribute(): ?string
    {
        if (!$this->profile_photo) {
            return null;
        }

        return Storage::url($this->profile_photo);
    }

    /**
     * Get status badge color
     */
    public function getStatusColorAttribute(): string
    {
        return match ($this->status) {
            'active' => '#10B981', // green
            'inactive' => '#6B7280', // gray
            'suspended' => '#EF4444', // red
            default => '#6B7280',
        };
    }

    /**
     * Get delivery status badge color
     */
    public function getDeliveryStatusColorAttribute(): string
    {
        return match ($this->delivery_status) {
            'available' => '#10B981', // green
            'busy' => '#F59E0B', // yellow
            'offline' => '#6B7280', // gray
            'on_break' => '#3B82F6', // blue
            default => '#6B7280',
        };
    }

    /**
     * Get earnings for a specific period
     */
    public function getEarningsForPeriod(string $period = 'today'): float
    {
        $query = $this->orders()->where('delivery_status', 'delivered');

        switch ($period) {
            case 'today':
                $query->whereDate('delivery_time', today());
                break;
            case 'week':
                $query->whereBetween('delivery_time', [now()->startOfWeek(), now()->endOfWeek()]);
                break;
            case 'month':
                $query->whereMonth('delivery_time', now()->month)
                      ->whereYear('delivery_time', now()->year);
                break;
        }

        return $query->sum('rider_commission');
    }

    /**
     * Reset daily counters
     */
    public function resetDailyCounters(): void
    {
        $this->update(['completed_orders_today' => 0]);
    }

    /**
     * Generate unique employee ID
     */
    public static function generateEmployeeId(): string
    {
        do {
            $id = 'RDR' . str_pad(random_int(1, 9999), 4, '0', STR_PAD_LEFT);
        } while (static::where('employee_id', $id)->exists());

        return $id;
    }

    /**
     * Scope for active riders
     */
    public function scopeActive($query)
    {
        return $query->where('status', 'active');
    }

    /**
     * Scope for available riders
     */
    public function scopeAvailable($query)
    {
        return $query->where('status', 'active')
                    ->where('delivery_status', 'available')
                    ->whereColumn('current_delivery_count', '<', 'max_concurrent_deliveries');
    }

    /**
     * Scope for riders in shift
     */
    public function scopeInShift($query)
    {
        $now = now()->format('H:i:s');
        $today = now()->dayOfWeek;

        return $query->where(function ($q) use ($now, $today) {
            $q->whereNull('shift_start_time')
              ->orWhere(function ($sq) use ($now) {
                  $sq->where('shift_start_time', '<=', $now)
                     ->where('shift_end_time', '>=', $now);
              });
        })->where(function ($q) use ($today) {
            $q->whereNull('working_days')
              ->orWhereJsonContains('working_days', $today);
        });
    }
}
