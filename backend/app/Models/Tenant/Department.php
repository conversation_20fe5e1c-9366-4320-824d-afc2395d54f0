<?php

namespace App\Models\Tenant;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use App\Traits\HasMediaLibrary;

class Department extends Model
{
    use HasFactory, HasMediaLibrary;

    protected $fillable = [
        'restaurant_id',
        'name',
        'description',
        'color_code',
        'icon',
        'is_active',
        'sort_order',
        'manager_id',
        'budget_limit',
        'max_employees',
    ];

    protected $casts = [
        'is_active' => 'boolean',
        'sort_order' => 'integer',
        'budget_limit' => 'decimal:2',
        'max_employees' => 'integer',
    ];

    /**
     * Get the restaurant that owns this department
     */
    public function restaurant(): BelongsTo
    {
        return $this->belongsTo(Restaurant::class);
    }

    /**
     * Get the department manager
     */
    public function manager(): BelongsTo
    {
        return $this->belongsTo(\App\Models\User::class, 'manager_id');
    }

    /**
     * Get all employees in this department
     */
    public function employees(): HasMany
    {
        return $this->hasMany(Employee::class);
    }

    /**
     * Get active employees in this department
     */
    public function activeEmployees(): HasMany
    {
        return $this->hasMany(Employee::class)->where('status', 'active');
    }

    /**
     * Get total employees count
     */
    public function getTotalEmployeesAttribute(): int
    {
        return $this->employees()->count();
    }

    /**
     * Get active employees count
     */
    public function getActiveEmployeesCountAttribute(): int
    {
        return $this->activeEmployees()->count();
    }

    /**
     * Check if department is at capacity
     */
    public function isAtCapacity(): bool
    {
        if (!$this->max_employees) {
            return false;
        }

        return $this->active_employees_count >= $this->max_employees;
    }

    /**
     * Get available positions
     */
    public function getAvailablePositionsAttribute(): int
    {
        if (!$this->max_employees) {
            return 999; // Unlimited
        }

        return max(0, $this->max_employees - $this->active_employees_count);
    }

    /**
     * Get total salary cost for this department
     */
    public function getTotalSalaryCost(): float
    {
        return $this->activeEmployees()
            ->sum('salary');
    }

    /**
     * Get department utilization percentage
     */
    public function getUtilizationPercentageAttribute(): float
    {
        if (!$this->max_employees || $this->max_employees <= 0) {
            return 0;
        }

        return ($this->active_employees_count / $this->max_employees) * 100;
    }

    /**
     * Get budget utilization percentage
     */
    public function getBudgetUtilizationAttribute(): float
    {
        if (!$this->budget_limit || $this->budget_limit <= 0) {
            return 0;
        }

        $totalSalary = $this->getTotalSalaryCost();
        return ($totalSalary / $this->budget_limit) * 100;
    }

    /**
     * Check if budget is exceeded
     */
    public function isBudgetExceeded(): bool
    {
        return $this->budget_utilization > 100;
    }

    /**
     * Get remaining budget
     */
    public function getRemainingBudgetAttribute(): float
    {
        if (!$this->budget_limit) {
            return 0;
        }

        $totalSalary = $this->getTotalSalaryCost();
        return max(0, $this->budget_limit - $totalSalary);
    }

    /**
     * Scope for active departments
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope for ordered departments
     */
    public function scopeOrdered($query)
    {
        return $query->orderBy('sort_order')->orderBy('name');
    }

    /**
     * Boot the model
     */
    protected static function boot()
    {
        parent::boot();

        static::creating(function ($department) {
            if (is_null($department->sort_order)) {
                $department->sort_order = self::max('sort_order') + 1;
            }
            if (empty($department->color_code)) {
                $department->color_code = sprintf('#%06X', mt_rand(0, 0xFFFFFF));
            }
        });
    }
}
