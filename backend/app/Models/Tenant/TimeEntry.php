<?php

namespace App\Models\Tenant;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Carbon\Carbon;

class TimeEntry extends Model
{
    use HasFactory;

    protected $fillable = [
        'restaurant_id',
        'employee_id',
        'shift_id',
        'date',
        'clock_in_time',
        'clock_out_time',
        'break_start_time',
        'break_end_time',
        'hours_worked',
        'overtime_hours',
        'break_duration', // in minutes
        'status',
        'notes',
        'approved_by',
        'approved_at',
    ];

    protected $casts = [
        'date' => 'date',
        'clock_in_time' => 'datetime',
        'clock_out_time' => 'datetime',
        'break_start_time' => 'datetime',
        'break_end_time' => 'datetime',
        'hours_worked' => 'decimal:2',
        'overtime_hours' => 'decimal:2',
        'break_duration' => 'integer',
        'approved_at' => 'datetime',
    ];

    /**
     * Get the restaurant that owns this time entry
     */
    public function restaurant(): BelongsTo
    {
        return $this->belongsTo(Restaurant::class);
    }

    /**
     * Get the employee
     */
    public function employee(): BelongsTo
    {
        return $this->belongsTo(Employee::class);
    }

    /**
     * Get the shift
     */
    public function shift(): BelongsTo
    {
        return $this->belongsTo(Shift::class);
    }

    /**
     * Get the user who approved this time entry
     */
    public function approver(): BelongsTo
    {
        return $this->belongsTo(\App\Models\User::class, 'approved_by');
    }

    /**
     * Check if employee is currently clocked in
     */
    public function isClockedIn(): bool
    {
        return $this->clock_in_time && !$this->clock_out_time;
    }

    /**
     * Check if employee is on break
     */
    public function isOnBreak(): bool
    {
        return $this->break_start_time && !$this->break_end_time;
    }

    /**
     * Check if time entry is complete
     */
    public function isComplete(): bool
    {
        return $this->clock_in_time && $this->clock_out_time;
    }

    /**
     * Check if time entry is approved
     */
    public function isApproved(): bool
    {
        return $this->status === 'approved';
    }

    /**
     * Get total worked duration in minutes
     */
    public function getTotalWorkedMinutesAttribute(): int
    {
        if (!$this->clock_in_time || !$this->clock_out_time) {
            return 0;
        }

        $totalMinutes = $this->clock_in_time->diffInMinutes($this->clock_out_time);
        $breakMinutes = $this->break_duration ?? 0;
        
        return max(0, $totalMinutes - $breakMinutes);
    }

    /**
     * Get total worked duration in hours
     */
    public function getTotalWorkedHoursAttribute(): float
    {
        return $this->total_worked_minutes / 60;
    }

    /**
     * Get break duration in minutes
     */
    public function getActualBreakDurationAttribute(): int
    {
        if (!$this->break_start_time || !$this->break_end_time) {
            return 0;
        }

        return $this->break_start_time->diffInMinutes($this->break_end_time);
    }

    /**
     * Get status label
     */
    public function getStatusLabelAttribute(): string
    {
        switch ($this->status) {
            case 'pending':
                return 'Pending';
            case 'approved':
                return 'Approved';
            case 'rejected':
                return 'Rejected';
            case 'in_progress':
                return 'In Progress';
            default:
                return ucfirst($this->status);
        }
    }

    /**
     * Clock in employee
     */
    public function clockIn(Carbon $time = null): void
    {
        $this->update([
            'clock_in_time' => $time ?? now(),
            'status' => 'in_progress',
        ]);
    }

    /**
     * Clock out employee
     */
    public function clockOut(Carbon $time = null): void
    {
        $clockOutTime = $time ?? now();
        
        $this->update([
            'clock_out_time' => $clockOutTime,
            'status' => 'pending',
        ]);

        // Calculate hours worked and overtime
        $this->calculateHours();
    }

    /**
     * Start break
     */
    public function startBreak(Carbon $time = null): void
    {
        $this->update([
            'break_start_time' => $time ?? now(),
        ]);
    }

    /**
     * End break
     */
    public function endBreak(Carbon $time = null): void
    {
        $breakEndTime = $time ?? now();
        
        $this->update([
            'break_end_time' => $breakEndTime,
            'break_duration' => $this->actual_break_duration,
        ]);
    }

    /**
     * Calculate hours worked and overtime
     */
    public function calculateHours(): void
    {
        if (!$this->clock_in_time || !$this->clock_out_time) {
            return;
        }

        $totalHours = $this->total_worked_hours;
        $regularHours = min($totalHours, 8); // Regular hours cap at 8
        $overtimeHours = max(0, $totalHours - 8);

        $this->update([
            'hours_worked' => $regularHours,
            'overtime_hours' => $overtimeHours,
        ]);
    }

    /**
     * Approve time entry
     */
    public function approve(\App\Models\User $approver): void
    {
        $this->update([
            'status' => 'approved',
            'approved_by' => $approver->id,
            'approved_at' => now(),
        ]);
    }

    /**
     * Reject time entry
     */
    public function reject(\App\Models\User $rejector, string $reason = null): void
    {
        $this->update([
            'status' => 'rejected',
            'notes' => $this->notes . ($reason ? "\nRejected: {$reason}" : "\nRejected"),
        ]);
    }

    /**
     * Calculate gross pay for this time entry
     */
    public function calculateGrossPay(): float
    {
        if (!$this->employee) {
            return 0;
        }

        if ($this->employee->employment_type === 'hourly') {
            $regularPay = $this->hours_worked * $this->employee->hourly_rate;
            $overtimePay = $this->overtime_hours * ($this->employee->hourly_rate * 1.5);
            return $regularPay + $overtimePay;
        }

        // For salary employees, return daily rate
        if ($this->employee->employment_type === 'salary') {
            return $this->employee->salary / 365;
        }

        return 0;
    }

    /**
     * Check if time entry has discrepancies with scheduled shift
     */
    public function hasDiscrepancies(): bool
    {
        if (!$this->shift) {
            return false;
        }

        $scheduledHours = $this->shift->duration_hours;
        $actualHours = $this->total_worked_hours;
        
        // Consider discrepancy if difference is more than 15 minutes (0.25 hours)
        return abs($scheduledHours - $actualHours) > 0.25;
    }

    /**
     * Get discrepancy details
     */
    public function getDiscrepancyDetails(): array
    {
        if (!$this->shift) {
            return [];
        }

        $scheduledHours = $this->shift->duration_hours;
        $actualHours = $this->total_worked_hours;
        $difference = $actualHours - $scheduledHours;

        return [
            'scheduled_hours' => $scheduledHours,
            'actual_hours' => $actualHours,
            'difference' => $difference,
            'type' => $difference > 0 ? 'overtime' : 'undertime',
            'has_discrepancy' => abs($difference) > 0.25,
        ];
    }

    /**
     * Scope for pending time entries
     */
    public function scopePending($query)
    {
        return $query->where('status', 'pending');
    }

    /**
     * Scope for approved time entries
     */
    public function scopeApproved($query)
    {
        return $query->where('status', 'approved');
    }

    /**
     * Scope for in progress time entries
     */
    public function scopeInProgress($query)
    {
        return $query->where('status', 'in_progress');
    }

    /**
     * Scope for today's time entries
     */
    public function scopeToday($query)
    {
        return $query->whereDate('date', today());
    }

    /**
     * Scope by date range
     */
    public function scopeDateRange($query, $startDate, $endDate)
    {
        return $query->whereBetween('date', [$startDate, $endDate]);
    }

    /**
     * Boot the model
     */
    protected static function boot()
    {
        parent::boot();

        static::creating(function ($timeEntry) {
            if (is_null($timeEntry->date)) {
                $timeEntry->date = today();
            }
            if (empty($timeEntry->status)) {
                $timeEntry->status = 'pending';
            }
        });
    }
}
