<?php

namespace App\Models\Tenant;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class ComboComponent extends Model
{
    use HasFactory;

    protected $fillable = [
        'combo_menu_id',
        'component_type',
        'menu_item_id',
        'is_required',
        'max_selections',
        'sort_order',
    ];

    protected $casts = [
        'is_required' => 'boolean',
        'max_selections' => 'integer',
        'sort_order' => 'integer',
    ];

    /**
     * Get the combo menu this component belongs to.
     */
    public function comboMenu(): BelongsTo
    {
        return $this->belongsTo(ComboMenu::class);
    }

    /**
     * Get the menu item for this component.
     */
    public function menuItem(): BelongsTo
    {
        return $this->belongsTo(MenuItem::class);
    }

    /**
     * Scope by component type.
     */
    public function scopeByType($query, $type)
    {
        return $query->where('component_type', $type);
    }

    /**
     * Scope for required components.
     */
    public function scopeRequired($query)
    {
        return $query->where('is_required', true);
    }

    /**
     * Scope to order by sort order.
     */
    public function scopeOrdered($query)
    {
        return $query->orderBy('sort_order');
    }

    /**
     * Get available component types.
     */
    public static function getComponentTypes(): array
    {
        return [
            'main' => 'Main Item',
            'side' => 'Side Item',
            'drink' => 'Drink',
            'dessert' => 'Dessert',
        ];
    }
}
