<?php

namespace App\Models\Tenant;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasOne;
use Illuminate\Support\Str;

class Order extends Model
{
    use HasFactory;

    protected $fillable = [
        'branch_id',
        'order_number',
        'order_source',
        'subtotal',
        'grand_total',
        'total_amount',
        'table_id',
        'customer_id',
        'loyalty_points',
        'discount',
        'points_applied',
        'status',
        'kitchen_status',
        'waiter_id',
        'chef_id',
        'notes',
        'delivery_charge',
        'payment_status',
        'delivery_address',
        'delivery_location',
        'phone',
        'order_type',
        'order_closed_at',
        'feedback',
        'feedback_at',
        // POS specific fields
        'pos_session_id',
        'order_source',
        'tax_rate',
        'service_charge_rate',
        'applied_taxes',
        'is_split_bill',
        'guest_count',
        'pos_notes',
        'order_opened_at',
        'order_closed_at',
        'opened_by',
        'closed_by',
        'kitchen_notified_at',
        'estimated_prep_time',
        'kitchen_instructions',
        'kitchen_status',
        'payment_method_id',
        'payment_details',
    ];

    protected $casts = [
        'subtotal' => 'decimal:2',
        'grand_total' => 'decimal:2',
        'total_amount' => 'decimal:2',
        'loyalty_points' => 'integer',
        'discount' => 'decimal:2',
        'points_applied' => 'integer',
        'delivery_charge' => 'decimal:2',
        'order_closed_at' => 'datetime',
        'payment_details' => 'array',
    ];

    /**
     * Boot the model
     */
    protected static function boot()
    {
        parent::boot();

        static::creating(function ($order) {
            // Generate order number if not set
            if (empty($order->order_number)) {
                $order->order_number = static::generateOrderNumber();
            }

            // Auto-calculate totals if not set
            if (empty($order->grand_total)) {
                $order->grand_total = ($order->subtotal ?? 0) + ($order->delivery_charge ?? 0) - ($order->discount ?? 0);
            }
            if (empty($order->total_amount)) {
                $order->total_amount = $order->grand_total;
            }
        });
    }

    /**
     * Generate unique order number
     */
    public static function generateOrderNumber(): string
    {
        do {
            $number = 'ORD-' . strtoupper(Str::random(8));
        } while (static::where('order_number', $number)->exists());

        return $number;
    }

    /**
     * Get the restaurant that owns this order
     */
    public function restaurant(): BelongsTo
    {
        return $this->belongsTo(Restaurant::class);
    }

    /**
     * Get the customer who placed this order
     */
    public function customer(): BelongsTo
    {
        return $this->belongsTo(Customer::class);
    }

    /**
     * Get the table for dine-in orders
     */
    public function table(): BelongsTo
    {
        return $this->belongsTo(Table::class);
    }

    /**
     * Get the waiter assigned to this order
     */
    public function waiter(): BelongsTo
    {
        return $this->belongsTo(User::class, 'waiter_id');
    }

    /**
     * Get the chef assigned to this order
     */
    public function chef(): BelongsTo
    {
        return $this->belongsTo(User::class, 'chef_id');
    }

    /**
     * Get the branch where this order was placed
     */
    public function branch(): BelongsTo
    {
        return $this->belongsTo(Branch::class);
    }

    /**
     * Get the customer address for delivery orders
     */
    public function customerAddress(): BelongsTo
    {
        return $this->belongsTo(CustomerAddress::class);
    }

    /**
     * Get the order items
     */
    public function items(): HasMany
    {
        return $this->hasMany(OrderItem::class);
    }

    /**
     * Get the order updates/status changes
     */
    public function updates(): HasMany
    {
        return $this->hasMany(OrderUpdate::class);
    }

    /**
     * Get the order status history (disabled for simplified schema)
     */
    // public function statusHistory(): HasMany
    // {
    //     return $this->hasMany(OrderStatusHistory::class);
    // }

    /**
     * Get the delivery order (for delivery orders)
     */
    public function deliveryOrder(): HasOne
    {
        return $this->hasOne(DeliveryOrder::class);
    }

    /**
     * Get the coupon usage for this order
     */
    public function couponUsage(): HasOne
    {
        return $this->hasOne(CouponUsage::class);
    }

    /**
     * Get the assigned employee
     */
    public function assignedEmployee(): BelongsTo
    {
        return $this->belongsTo(Employee::class, 'assigned_to');
    }

    /**
     * Get the employee who prepared the order
     */
    public function preparedByEmployee(): BelongsTo
    {
        return $this->belongsTo(Employee::class, 'prepared_by');
    }

    /**
     * Get the employee who served the order
     */
    public function servedByEmployee(): BelongsTo
    {
        return $this->belongsTo(Employee::class, 'served_by');
    }

    /**
     * Get the user who opened this order
     */
    public function openedBy(): BelongsTo
    {
        return $this->belongsTo(\App\Models\User::class, 'opened_by');
    }

    /**
     * Get the user who closed this order
     */
    public function closedBy(): BelongsTo
    {
        return $this->belongsTo(\App\Models\User::class, 'closed_by');
    }

    /**
     * Get the payment method for this order
     */
    public function paymentMethod(): BelongsTo
    {
        return $this->belongsTo(PaymentMethod::class);
    }

    /**
     * Get all payments for this order
     */
    public function payments(): HasMany
    {
        return $this->hasMany(OrderPayment::class);
    }

    /**
     * Get all discounts applied to this order
     */
    public function discounts(): HasMany
    {
        return $this->hasMany(OrderDiscount::class);
    }

    /**
     * Get order-level discounts only
     */
    public function orderDiscounts(): HasMany
    {
        return $this->discounts()->where('applied_to', 'order');
    }

    /**
     * Update order status (simplified - no history tracking)
     */
    public function updateStatus(string $newStatus, ?Employee $changedBy = null, ?string $notes = null): void
    {
        $oldStatus = $this->status;

        $this->update(['status' => $newStatus]);

        // Note: Status history tracking disabled for simplified schema
        // If needed, could log status changes to application logs instead

        // Update timestamps based on status
        $this->updateStatusTimestamps($newStatus);
    }

    /**
     * Update timestamps based on status
     */
    protected function updateStatusTimestamps(string $status): void
    {
        $updates = [];

        switch ($status) {
            case 'confirmed':
                $updates['confirmed_at'] = now();
                break;
            case 'preparing':
                $updates['preparing_at'] = now();
                break;
            case 'ready':
                $updates['ready_at'] = now();
                break;
            case 'delivered':
                $updates['delivered_at'] = now();
                break;
            case 'completed':
                $updates['completed_at'] = now();
                break;
        }

        if (!empty($updates)) {
            $this->update($updates);
        }
    }

    /**
     * Calculate estimated delivery time
     */
    public function getEstimatedDeliveryTimeAttribute(): ?int
    {
        if ($this->order_type !== 'delivery') {
            return null;
        }

        $preparationTime = $this->estimated_preparation_time ?? 30;
        $deliveryTime = 20; // Default delivery time

        if ($this->deliveryOrder && $this->deliveryOrder->deliveryZone) {
            $deliveryTime = $this->deliveryOrder->deliveryZone->estimated_delivery_time;
        }

        return $preparationTime + $deliveryTime;
    }

    /**
     * Get order total items count
     */
    public function getTotalItemsAttribute(): int
    {
        return $this->items->sum('quantity');
    }

    /**
     * Check if order can be cancelled
     */
    public function canBeCancelled(): bool
    {
        return in_array($this->status, ['pending', 'confirmed']);
    }

    /**
     * Check if order is active (not completed/cancelled)
     */
    public function isActive(): bool
    {
        return !in_array($this->status, ['completed', 'cancelled', 'refunded']);
    }

    /**
     * Scope for orders by status
     */
    public function scopeByStatus($query, $status)
    {
        return $query->where('status', $status);
    }

    /**
     * Scope for orders by type
     */
    public function scopeByType($query, $type)
    {
        return $query->where('order_type', $type);
    }

    /**
     * Scope for active orders
     */
    public function scopeActive($query)
    {
        return $query->whereNotIn('status', ['completed', 'cancelled', 'refunded']);
    }

    /**
     * Scope for today's orders
     */
    public function scopeToday($query)
    {
        return $query->whereDate('created_at', today());
    }

    /**
     * Scope for orders within date range
     */
    public function scopeDateRange($query, $startDate, $endDate)
    {
        return $query->whereBetween('created_at', [$startDate, $endDate]);
    }

    // POS-specific methods

    /**
     * Check if order is from POS
     */
    public function isPosOrder(): bool
    {
        return $this->order_source === 'pos';
    }

    /**
     * Check if order is dine-in
     */
    public function isDineIn(): bool
    {
        return $this->order_type === 'dine_in';
    }

    /**
     * Check if order has split bill
     */
    public function hasSplitBill(): bool
    {
        return $this->is_split_bill;
    }

    /**
     * Get total paid amount
     */
    public function getTotalPaidAmountAttribute(): float
    {
        return $this->payments()->completed()->sum('amount');
    }

    /**
     * Get remaining balance
     */
    public function getRemainingBalanceAttribute(): float
    {
        return $this->total_amount - $this->total_paid_amount;
    }

    /**
     * Check if order is fully paid
     */
    public function isFullyPaid(): bool
    {
        return $this->remaining_balance <= 0;
    }

    /**
     * Check if order is partially paid
     */
    public function isPartiallyPaid(): bool
    {
        return $this->total_paid_amount > 0 && $this->remaining_balance > 0;
    }

    /**
     * Apply discount to order
     */
    public function applyDiscount(array $discountData): OrderDiscount
    {
        $discount = $this->discounts()->create($discountData);
        $this->recalculateTotal();
        return $discount;
    }

    /**
     * Remove discount from order
     */
    public function removeDiscount(OrderDiscount $discount): void
    {
        $discount->delete();
        $this->recalculateTotal();
    }

    /**
     * Recalculate order totals
     */
    public function recalculateTotal(): void
    {
        // Calculate subtotal from items (all items since simplified schema doesn't have is_cancelled)
        $this->subtotal = $this->items()->sum('total_price');

        // Calculate grand total (simplified - no discounts or delivery charges in basic schema)
        $this->grand_total = $this->subtotal;

        // Set total_amount to grand_total for consistency
        $this->total_amount = $this->grand_total;

        $this->save();
    }

    /**
     * Add payment to order
     */
    public function addPayment(array $paymentData): OrderPayment
    {
        $payment = $this->payments()->create($paymentData);

        // Update payment status if fully paid
        if ($this->isFullyPaid()) {
            $this->update(['payment_status' => 'paid', 'paid_at' => now()]);
        } elseif ($this->isPartiallyPaid()) {
            $this->update(['payment_status' => 'partially_paid']);
        }

        return $payment;
    }

    /**
     * Update kitchen status
     */
    public function updateKitchenStatus(string $status, ?\App\Models\User $user = null): void
    {
        $updates = ['kitchen_status' => $status];

        if ($status === 'received' && !$this->kitchen_notified_at) {
            $updates['kitchen_notified_at'] = now();
        }

        $this->update($updates);
    }

    /**
     * Scope for POS orders
     */
    public function scopePosOrders($query)
    {
        return $query->where('order_source', 'pos');
    }

    /**
     * Scope for dine-in orders
     */
    public function scopeDineIn($query)
    {
        return $query->where('order_type', 'dine_in');
    }

    /**
     * Scope for orders by branch
     */
    public function scopeByBranch($query, $branchId)
    {
        return $query->where('branch_id', $branchId);
    }

    /**
     * Scope for orders by kitchen status
     */
    public function scopeByKitchenStatus($query, $status)
    {
        return $query->where('kitchen_status', $status);
    }

    /**
     * Scope for open orders (not closed)
     */
    public function scopeOpen($query)
    {
        return $query->whereNull('order_closed_at');
    }

    /**
     * Scope for orders by table
     */
    public function scopeByTable($query, $tableId)
    {
        return $query->where('table_id', $tableId);
    }
}
