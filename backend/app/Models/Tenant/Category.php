<?php

namespace App\Models\Tenant;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use App\Traits\HasMediaLibrary;

class Category extends Model
{
    use HasFactory, HasMediaLibrary;

    protected $fillable = [
        'name',
        'slug',
        'description',
        'image',
        'parent_id',
        'sort_order',
        'is_active',
    ];

    protected $casts = [
        'sort_order' => 'integer',
        'is_active' => 'boolean',
    ];

    /**
     * Get the parent category
     */
    public function parent(): BelongsTo
    {
        return $this->belongsTo(Category::class, 'parent_id');
    }

    /**
     * Get child categories (subcategories)
     */
    public function children(): HasMany
    {
        return $this->hasMany(Category::class, 'parent_id');
    }

    /**
     * Get active child categories
     */
    public function activeChildren(): Has<PERSON>any
    {
        return $this->hasMany(Category::class, 'parent_id')->where('is_active', true)->ordered();
    }

    /**
     * Get all subcategories in this category (legacy - for backward compatibility)
     */
    public function subcategories(): HasMany
    {
        return $this->hasMany(Subcategory::class);
    }

    /**
     * Get active subcategories in this category
     */
    public function activeSubcategories(): HasMany
    {
        return $this->hasMany(Subcategory::class)->where('is_active', true)->ordered();
    }

    /**
     * Get all menu items in this category (many-to-many)
     */
    public function menuItems(): BelongsToMany
    {
        return $this->belongsToMany(MenuItem::class, 'menu_item_categories')
            ->withTimestamps();
    }

    /**
     * Get active menu items in this category
     */
    public function activeMenuItems(): BelongsToMany
    {
        return $this->belongsToMany(MenuItem::class, 'menu_item_categories')
            ->where('menu_items.is_active', true)
            ->withTimestamps();
    }

    /**
     * Get available menu items in this category
     */
    public function availableMenuItems(): BelongsToMany
    {
        return $this->belongsToMany(MenuItem::class, 'menu_item_categories')
            ->where('menu_items.is_available', true)
            ->where('menu_items.is_active', true)
            ->withTimestamps();
    }

    /**
     * Scope for active categories
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope for ordered categories
     */
    public function scopeOrdered($query)
    {
        return $query->orderBy('sort_order');
    }
}
