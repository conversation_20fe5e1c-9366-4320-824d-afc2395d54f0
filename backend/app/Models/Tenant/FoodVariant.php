<?php

namespace App\Models\Tenant;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class FoodVariant extends Model
{
    use HasFactory;

    protected $fillable = [
        'restaurant_id',
        'food_id',
        'name',
        'type',
        'description',
        'price_adjustment',
        'price_type',
        'is_available',
        'is_default',
        'track_quantity',
        'quantity_available',
        'minimum_quantity',
        'sort_order',
        'image',
    ];

    protected $casts = [
        'price_adjustment' => 'decimal:2',
        'is_available' => 'boolean',
        'is_default' => 'boolean',
        'track_quantity' => 'boolean',
    ];

    /**
     * Get the restaurant that owns this variant
     */
    public function restaurant(): BelongsTo
    {
        return $this->belongsTo(Restaurant::class);
    }

    /**
     * Get the food this variant belongs to
     */
    public function food(): BelongsTo
    {
        return $this->belongsTo(Food::class);
    }

    /**
     * Calculate the final price for this variant
     */
    public function getFinalPriceAttribute(): float
    {
        $basePrice = $this->food->current_price;
        
        if ($this->price_type === 'percentage') {
            return $basePrice + ($basePrice * ($this->price_adjustment / 100));
        }
        
        return $basePrice + $this->price_adjustment;
    }

    /**
     * Check if variant is available
     */
    public function isAvailable(): bool
    {
        if (!$this->is_available) {
            return false;
        }

        // Check quantity if tracking
        if ($this->track_quantity && $this->quantity_available <= 0) {
            return false;
        }

        return true;
    }

    /**
     * Scope for available variants
     */
    public function scopeAvailable($query)
    {
        return $query->where('is_available', true);
    }

    /**
     * Scope for default variants
     */
    public function scopeDefault($query)
    {
        return $query->where('is_default', true);
    }

    /**
     * Scope for ordering by sort order
     */
    public function scopeOrdered($query)
    {
        return $query->orderBy('sort_order')->orderBy('name');
    }
}
