<?php

namespace App\Models\Tenant;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use App\Traits\HasMediaLibrary;

class PurchaseOrder extends Model
{
    use HasFactory, HasMediaLibrary;

    protected $fillable = [
        'restaurant_id',
        'vendor_id',
        'created_by',
        'approved_by',
        'po_number',
        'status',
        'order_date',
        'expected_delivery_date',
        'actual_delivery_date',
        'subtotal',
        'tax_amount',
        'shipping_cost',
        'discount_amount',
        'total_amount',
        'payment_terms',
        'delivery_address',
        'notes',
        'internal_notes',
    ];

    protected $casts = [
        'order_date' => 'date',
        'expected_delivery_date' => 'date',
        'actual_delivery_date' => 'date',
        'subtotal' => 'decimal:2',
        'tax_amount' => 'decimal:2',
        'shipping_cost' => 'decimal:2',
        'discount_amount' => 'decimal:2',
        'total_amount' => 'decimal:2',
    ];

    /**
     * Get the restaurant that owns this purchase order
     */
    public function restaurant(): BelongsTo
    {
        return $this->belongsTo(Restaurant::class);
    }

    /**
     * Get the vendor
     */
    public function vendor(): BelongsTo
    {
        return $this->belongsTo(Vendor::class);
    }

    /**
     * Get the user who created this purchase order
     */
    public function creator(): BelongsTo
    {
        return $this->belongsTo(\App\Models\User::class, 'created_by');
    }

    /**
     * Get the user who approved this purchase order
     */
    public function approver(): BelongsTo
    {
        return $this->belongsTo(\App\Models\User::class, 'approved_by');
    }

    /**
     * Get all purchase order items
     */
    public function items(): HasMany
    {
        return $this->hasMany(PurchaseOrderItem::class);
    }

    /**
     * Get purchase order receipts
     */
    public function receipts(): HasMany
    {
        return $this->hasMany(PurchaseOrderReceipt::class);
    }

    /**
     * Check if purchase order is pending
     */
    public function isPending(): bool
    {
        return $this->status === 'pending';
    }

    /**
     * Check if purchase order is approved
     */
    public function isApproved(): bool
    {
        return in_array($this->status, ['approved', 'sent', 'partially_received', 'received', 'completed']);
    }

    /**
     * Check if purchase order is completed
     */
    public function isCompleted(): bool
    {
        return $this->status === 'completed';
    }

    /**
     * Check if purchase order is overdue
     */
    public function isOverdue(): bool
    {
        return $this->expected_delivery_date &&
               $this->expected_delivery_date->isPast() &&
               !in_array($this->status, ['received', 'completed', 'cancelled']);
    }

    /**
     * Get status label
     */
    public function getStatusLabelAttribute(): string
    {
        switch ($this->status) {
            case 'draft':
                return 'Draft';
            case 'pending':
                return 'Pending Approval';
            case 'approved':
                return 'Approved';
            case 'sent':
                return 'Sent to Vendor';
            case 'partially_received':
                return 'Partially Received';
            case 'received':
                return 'Received';
            case 'completed':
                return 'Completed';
            case 'cancelled':
                return 'Cancelled';
            default:
                return ucfirst($this->status);
        }
    }

    /**
     * Get days until delivery
     */
    public function getDaysUntilDeliveryAttribute(): ?int
    {
        if (!$this->expected_delivery_date || $this->isCompleted()) {
            return null;
        }

        return now()->diffInDays($this->expected_delivery_date, false);
    }

    /**
     * Calculate totals
     */
    public function calculateTotals(): void
    {
        $this->subtotal = $this->items->sum(function ($item) {
            return $item->quantity * $item->unit_cost;
        });

        $this->total_amount = $this->subtotal + $this->tax_amount + $this->shipping_cost - $this->discount_amount;
        $this->save();
    }

    /**
     * Approve purchase order
     */
    public function approve(\App\Models\User $approver): void
    {
        $this->update([
            'status' => 'approved',
            'approved_by' => $approver->id,
        ]);
    }

    /**
     * Send to vendor
     */
    public function sendToVendor(): void
    {
        $this->update(['status' => 'sent']);

        // Here you could integrate with email service to send PO to vendor
        // Mail::to($this->vendor->email)->send(new PurchaseOrderMail($this));
    }

    /**
     * Mark as received
     */
    public function markAsReceived(): void
    {
        $this->update([
            'status' => 'received',
            'actual_delivery_date' => now(),
        ]);

        // Update inventory for all items
        foreach ($this->items as $item) {
            $item->inventoryItem->addStock(
                $item->quantity,
                $item->unit_cost,
                $item->batch_number,
                $item->expiry_date,
                'purchase',
                $this
            );
        }
    }

    /**
     * Partially receive items
     */
    public function partiallyReceive(array $receivedItems): void
    {
        foreach ($receivedItems as $itemData) {
            $item = $this->items()->find($itemData['id']);
            if ($item && $itemData['received_quantity'] > 0) {
                $item->inventoryItem->addStock(
                    $itemData['received_quantity'],
                    $item->unit_cost,
                    $itemData['batch_number'] ?? null,
                    $itemData['expiry_date'] ?? null,
                    'purchase',
                    $this
                );

                $item->update(['received_quantity' => $itemData['received_quantity']]);
            }
        }

        // Check if all items are fully received
        $allReceived = $this->items->every(function ($item) {
            return $item->received_quantity >= $item->quantity;
        });

        $this->update([
            'status' => $allReceived ? 'received' : 'partially_received',
            'actual_delivery_date' => $allReceived ? now() : $this->actual_delivery_date,
        ]);
    }

    /**
     * Complete purchase order
     */
    public function complete(): void
    {
        $this->update(['status' => 'completed']);
    }

    /**
     * Cancel purchase order
     */
    public function cancel(?string $reason = null): void
    {
        $this->update([
            'status' => 'cancelled',
            'internal_notes' => $this->internal_notes . "\nCancelled: " . ($reason ?? 'No reason provided'),
        ]);
    }

    /**
     * Generate automatic purchase orders for low stock items
     */
    public static function generateAutomaticOrders(): array
    {
        $lowStockItems = InventoryItem::needsReorder()
            ->with(['vendor', 'category'])
            ->get()
            ->groupBy('vendor_id');

        $createdOrders = [];

        foreach ($lowStockItems as $vendorId => $items) {
            if (!$vendorId) continue; // Skip items without vendor

            $vendor = Vendor::find($vendorId);
            if (!$vendor || !$vendor->is_active) continue;

            $po = self::create([
                'restaurant_id' => $items->first()->restaurant_id,
                'vendor_id' => $vendorId,
                'created_by' => auth()->id() ?? 1, // System user
                'po_number' => self::generatePoNumber(),
                'status' => 'draft',
                'order_date' => now(),
                'expected_delivery_date' => now()->addDays(3), // Default 3 days
                'payment_terms' => $vendor->payment_terms ?? 'Net 30',
                'notes' => 'Automatically generated for low stock items',
            ]);

            foreach ($items as $item) {
                $po->items()->create([
                    'inventory_item_id' => $item->id,
                    'quantity' => $item->reorder_quantity,
                    'unit_cost' => $item->unit_cost,
                    'total_cost' => $item->reorder_quantity * $item->unit_cost,
                ]);
            }

            $po->calculateTotals();
            $createdOrders[] = $po;
        }

        return $createdOrders;
    }

    /**
     * Generate unique PO number
     */
    public static function generatePoNumber(): string
    {
        $prefix = 'PO';
        $date = now()->format('Ymd');
        $count = self::whereDate('created_at', today())->count() + 1;

        return "{$prefix}-{$date}-" . str_pad($count, 4, '0', STR_PAD_LEFT);
    }

    /**
     * Scope for pending orders
     */
    public function scopePending($query)
    {
        return $query->where('status', 'pending');
    }

    /**
     * Scope for approved orders
     */
    public function scopeApproved($query)
    {
        return $query->whereIn('status', ['approved', 'sent', 'partially_received', 'received']);
    }

    /**
     * Scope for overdue orders
     */
    public function scopeOverdue($query)
    {
        return $query->where('expected_delivery_date', '<', now())
                    ->whereNotIn('status', ['received', 'completed', 'cancelled']);
    }

    /**
     * Boot the model
     */
    protected static function boot()
    {
        parent::boot();

        static::creating(function ($po) {
            if (empty($po->po_number)) {
                $po->po_number = self::generatePoNumber();
            }
            if (empty($po->status)) {
                $po->status = 'draft';
            }
            if (is_null($po->order_date)) {
                $po->order_date = now();
            }
        });
    }
}
