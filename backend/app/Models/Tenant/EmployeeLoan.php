<?php

namespace App\Models\Tenant;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Carbon\Carbon;

class EmployeeLoan extends Model
{
    use HasFactory;

    protected $fillable = [
        'employee_id',
        'loan_number',
        'principal_amount',
        'interest_rate',
        'term_months',
        'monthly_installment',
        'total_amount',
        'paid_amount',
        'outstanding_balance',
        'start_date',
        'end_date',
        'next_payment_date',
        'status',
        'purpose',
        'terms_conditions',
        'notes',
        'approved_by',
        'approved_at',
        'approval_notes',
        'guarantor_name',
        'guarantor_phone',
        'guarantor_address',
    ];

    protected $casts = [
        'principal_amount' => 'decimal:2',
        'interest_rate' => 'decimal:2',
        'term_months' => 'integer',
        'monthly_installment' => 'decimal:2',
        'total_amount' => 'decimal:2',
        'paid_amount' => 'decimal:2',
        'outstanding_balance' => 'decimal:2',
        'start_date' => 'date',
        'end_date' => 'date',
        'next_payment_date' => 'date',
        'approved_at' => 'datetime',
    ];

    const STATUS_ACTIVE = 'active';
    const STATUS_COMPLETED = 'completed';
    const STATUS_DEFAULTED = 'defaulted';
    const STATUS_CANCELLED = 'cancelled';

    protected static function boot()
    {
        parent::boot();

        static::creating(function ($loan) {
            if (!$loan->loan_number) {
                $loan->loan_number = static::generateLoanNumber();
            }
        });
    }

    /**
     * Get the employee that owns this loan.
     */
    public function employee(): BelongsTo
    {
        return $this->belongsTo(Employee::class);
    }

    /**
     * Get the employee who approved this loan.
     */
    public function approver(): BelongsTo
    {
        return $this->belongsTo(Employee::class, 'approved_by');
    }

    /**
     * Get the loan payments for this loan.
     */
    public function payments(): HasMany
    {
        return $this->hasMany(LoanPayment::class);
    }

    /**
     * Generate a unique loan number.
     */
    public static function generateLoanNumber(): string
    {
        $prefix = 'LOAN';
        $year = now()->year;
        $month = now()->format('m');
        
        $lastLoan = static::whereYear('created_at', $year)
                          ->whereMonth('created_at', $month)
                          ->orderBy('id', 'desc')
                          ->first();
        
        $sequence = $lastLoan ? (int) substr($lastLoan->loan_number, -4) + 1 : 1;
        
        return sprintf('%s%s%s%04d', $prefix, $year, $month, $sequence);
    }

    /**
     * Calculate loan details based on principal, rate, and term.
     */
    public function calculateLoanDetails(): void
    {
        if ($this->interest_rate > 0) {
            // Calculate monthly payment using loan formula
            $monthlyRate = $this->interest_rate / 100 / 12;
            $numPayments = $this->term_months;
            
            $monthlyPayment = $this->principal_amount * 
                ($monthlyRate * pow(1 + $monthlyRate, $numPayments)) / 
                (pow(1 + $monthlyRate, $numPayments) - 1);
            
            $this->monthly_installment = round($monthlyPayment, 2);
            $this->total_amount = round($monthlyPayment * $numPayments, 2);
        } else {
            // No interest loan
            $this->monthly_installment = round($this->principal_amount / $this->term_months, 2);
            $this->total_amount = $this->principal_amount;
        }

        $this->outstanding_balance = $this->total_amount;
        $this->end_date = $this->start_date->copy()->addMonths($this->term_months);
        $this->next_payment_date = $this->start_date->copy()->addMonth();
    }

    /**
     * Make a payment on this loan.
     */
    public function makePayment(float $amount, string $method = 'salary_deduction', array $options = []): LoanPayment
    {
        $payment = $this->payments()->create([
            'payment_amount' => $amount,
            'principal_amount' => $amount, // Simplified - could be split between principal and interest
            'interest_amount' => 0,
            'payment_date' => $options['payment_date'] ?? now(),
            'due_date' => $this->next_payment_date,
            'payment_method' => $method,
            'status' => 'paid',
            'notes' => $options['notes'] ?? null,
            'reference_number' => $options['reference'] ?? null,
        ]);

        $this->updateBalanceAfterPayment($amount);

        return $payment;
    }

    /**
     * Update loan balance after payment.
     */
    public function updateBalanceAfterPayment(float $amount): void
    {
        $this->paid_amount += $amount;
        $this->outstanding_balance -= $amount;

        // Update next payment date
        if ($this->outstanding_balance > 0) {
            $this->next_payment_date = $this->next_payment_date->addMonth();
        } else {
            $this->status = self::STATUS_COMPLETED;
            $this->next_payment_date = null;
        }

        $this->save();
    }

    /**
     * Check if loan is overdue.
     */
    public function isOverdue(): bool
    {
        return $this->status === self::STATUS_ACTIVE && 
               $this->next_payment_date && 
               $this->next_payment_date->isPast();
    }

    /**
     * Get days overdue.
     */
    public function getDaysOverdue(): int
    {
        if (!$this->isOverdue()) {
            return 0;
        }

        return $this->next_payment_date->diffInDays(now());
    }

    /**
     * Get remaining payments count.
     */
    public function getRemainingPaymentsAttribute(): int
    {
        if ($this->status !== self::STATUS_ACTIVE) {
            return 0;
        }

        $paidPayments = $this->payments()->where('status', 'paid')->count();
        return max(0, $this->term_months - $paidPayments);
    }

    /**
     * Get completion percentage.
     */
    public function getCompletionPercentageAttribute(): float
    {
        if ($this->total_amount == 0) {
            return 0;
        }

        return round(($this->paid_amount / $this->total_amount) * 100, 2);
    }

    /**
     * Approve the loan.
     */
    public function approve(Employee $approver, string $notes = null): void
    {
        $this->update([
            'status' => self::STATUS_ACTIVE,
            'approved_by' => $approver->id,
            'approved_at' => now(),
            'approval_notes' => $notes,
        ]);

        $this->calculateLoanDetails();
        $this->save();
    }

    /**
     * Scope to get active loans.
     */
    public function scopeActive($query)
    {
        return $query->where('status', self::STATUS_ACTIVE);
    }

    /**
     * Scope to get overdue loans.
     */
    public function scopeOverdue($query)
    {
        return $query->where('status', self::STATUS_ACTIVE)
                    ->where('next_payment_date', '<', now());
    }

    /**
     * Scope to get loans due for payment.
     */
    public function scopeDueForPayment($query, Carbon $date = null)
    {
        $date = $date ?? now();
        return $query->where('status', self::STATUS_ACTIVE)
                    ->whereDate('next_payment_date', '<=', $date);
    }
}
