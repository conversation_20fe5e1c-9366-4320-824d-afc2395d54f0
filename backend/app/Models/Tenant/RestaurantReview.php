<?php

namespace App\Models\Tenant;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class RestaurantReview extends Model
{
    use HasFactory;

    protected $fillable = [
        'restaurant_id',
        'customer_id',
        'order_id',
        'reservation_id',
        'overall_rating',
        'title',
        'review',
        'images',
        'food_rating',
        'service_rating',
        'ambiance_rating',
        'value_rating',
        'cleanliness_rating',
        'visit_type',
        'visit_date',
        'party_size',
        'occasion',
        'reviewer_name',
        'is_verified_visit',
        'is_anonymous',
        'status',
        'moderated_by',
        'moderated_at',
        'moderation_notes',
        'helpful_count',
        'not_helpful_count',
        'featured',
        'restaurant_response',
        'responded_at',
        'responded_by',
        'source',
        'external_id',
    ];

    protected $casts = [
        'images' => 'array',
        'visit_date' => 'date',
        'is_verified_visit' => 'boolean',
        'is_anonymous' => 'boolean',
        'moderated_at' => 'datetime',
        'featured' => 'boolean',
        'responded_at' => 'datetime',
    ];

    /**
     * Get the restaurant being reviewed
     */
    public function restaurant(): BelongsTo
    {
        return $this->belongsTo(Restaurant::class);
    }

    /**
     * Get the customer who wrote the review
     */
    public function customer(): BelongsTo
    {
        return $this->belongsTo(Customer::class);
    }

    /**
     * Get the order this review is for
     */
    public function order(): BelongsTo
    {
        return $this->belongsTo(Order::class);
    }

    /**
     * Get the reservation this review is for
     */
    public function reservation(): BelongsTo
    {
        return $this->belongsTo(TableReservation::class, 'reservation_id');
    }

    /**
     * Get the employee who moderated this review
     */
    public function moderatedBy(): BelongsTo
    {
        return $this->belongsTo(Employee::class, 'moderated_by');
    }

    /**
     * Get the employee who responded to this review
     */
    public function respondedBy(): BelongsTo
    {
        return $this->belongsTo(Employee::class, 'responded_by');
    }

    /**
     * Get the reviewer's display name
     */
    public function getReviewerDisplayNameAttribute(): string
    {
        if ($this->is_anonymous) {
            return 'Anonymous';
        }

        return $this->reviewer_name ?? $this->customer?->full_name ?? 'Guest';
    }

    /**
     * Get the average of detailed ratings
     */
    public function getDetailedRatingAverageAttribute(): float
    {
        $ratings = array_filter([
            $this->food_rating,
            $this->service_rating,
            $this->ambiance_rating,
            $this->value_rating,
            $this->cleanliness_rating,
        ]);

        return count($ratings) > 0 ? array_sum($ratings) / count($ratings) : $this->overall_rating;
    }

    /**
     * Get helpfulness ratio
     */
    public function getHelpfulnessRatioAttribute(): float
    {
        $total = $this->helpful_count + $this->not_helpful_count;
        
        if ($total === 0) {
            return 0;
        }

        return ($this->helpful_count / $total) * 100;
    }

    /**
     * Get visit type label
     */
    public function getVisitTypeLabelAttribute(): string
    {
        return match ($this->visit_type) {
            'dine_in' => 'Dine In',
            'takeaway' => 'Takeaway',
            'delivery' => 'Delivery',
            default => ucfirst($this->visit_type ?? 'Unknown'),
        };
    }

    /**
     * Get party size label
     */
    public function getPartySizeLabelAttribute(): string
    {
        return match ($this->party_size) {
            '1' => 'Solo',
            '2' => 'Couple',
            '3-4' => 'Small Group',
            '5-6' => 'Medium Group',
            '7+' => 'Large Group',
            default => $this->party_size ?? 'Unknown',
        };
    }

    /**
     * Approve the review
     */
    public function approve(?Employee $moderator = null): void
    {
        $this->update([
            'status' => 'approved',
            'moderated_by' => $moderator?->id,
            'moderated_at' => now(),
        ]);

        // Update restaurant rating
        $this->restaurant->updateRating();
    }

    /**
     * Reject the review
     */
    public function reject(?Employee $moderator = null, ?string $notes = null): void
    {
        $this->update([
            'status' => 'rejected',
            'moderated_by' => $moderator?->id,
            'moderated_at' => now(),
            'moderation_notes' => $notes,
        ]);
    }

    /**
     * Hide the review
     */
    public function hide(?Employee $moderator = null, ?string $notes = null): void
    {
        $this->update([
            'status' => 'hidden',
            'moderated_by' => $moderator?->id,
            'moderated_at' => now(),
            'moderation_notes' => $notes,
        ]);
    }

    /**
     * Add restaurant response
     */
    public function addResponse(string $response, Employee $employee): void
    {
        $this->update([
            'restaurant_response' => $response,
            'responded_at' => now(),
            'responded_by' => $employee->id,
        ]);
    }

    /**
     * Mark as helpful
     */
    public function markHelpful(): void
    {
        $this->increment('helpful_count');
    }

    /**
     * Mark as not helpful
     */
    public function markNotHelpful(): void
    {
        $this->increment('not_helpful_count');
    }

    /**
     * Feature the review
     */
    public function feature(): void
    {
        $this->update(['featured' => true]);
    }

    /**
     * Unfeature the review
     */
    public function unfeature(): void
    {
        $this->update(['featured' => false]);
    }

    /**
     * Scope for approved reviews
     */
    public function scopeApproved($query)
    {
        return $query->where('status', 'approved');
    }

    /**
     * Scope for pending reviews
     */
    public function scopePending($query)
    {
        return $query->where('status', 'pending');
    }

    /**
     * Scope for featured reviews
     */
    public function scopeFeatured($query)
    {
        return $query->where('featured', true);
    }

    /**
     * Scope for verified visit reviews
     */
    public function scopeVerified($query)
    {
        return $query->where('is_verified_visit', true);
    }

    /**
     * Scope for reviews with images
     */
    public function scopeWithImages($query)
    {
        return $query->whereNotNull('images');
    }

    /**
     * Scope for reviews by rating
     */
    public function scopeByRating($query, $rating)
    {
        return $query->where('overall_rating', $rating);
    }

    /**
     * Scope for reviews by visit type
     */
    public function scopeByVisitType($query, $visitType)
    {
        return $query->where('visit_type', $visitType);
    }

    /**
     * Scope for recent reviews
     */
    public function scopeRecent($query, $days = 30)
    {
        return $query->where('created_at', '>=', now()->subDays($days));
    }

    /**
     * Scope for external reviews
     */
    public function scopeExternal($query)
    {
        return $query->whereNotNull('source');
    }
}
