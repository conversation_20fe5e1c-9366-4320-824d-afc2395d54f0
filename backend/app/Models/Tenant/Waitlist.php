<?php

namespace App\Models\Tenant;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class Waitlist extends Model
{
    use HasFactory;

    protected $fillable = [
        'restaurant_id',
        'customer_id',
        'waitlist_number',
        'customer_name',
        'customer_phone',
        'party_size',
        'preferred_table_type',
        'estimated_wait_time',
        'status',
        'special_requests',
        'notes',
        'joined_at',
        'notified_at',
        'seated_at',
        'left_at',
        'created_by',
    ];

    protected $casts = [
        'party_size' => 'integer',
        'estimated_wait_time' => 'integer', // in minutes
        'joined_at' => 'datetime',
        'notified_at' => 'datetime',
        'seated_at' => 'datetime',
        'left_at' => 'datetime',
    ];

    /**
     * Get the restaurant that owns this waitlist entry
     */
    public function restaurant(): BelongsTo
    {
        return $this->belongsTo(Restaurant::class);
    }

    /**
     * Get the customer for this waitlist entry
     */
    public function customer(): BelongsTo
    {
        return $this->belongsTo(Customer::class);
    }

    /**
     * Get the staff member who created this waitlist entry
     */
    public function creator(): BelongsTo
    {
        return $this->belongsTo(\App\Models\User::class, 'created_by');
    }

    /**
     * Check if customer is waiting
     */
    public function isWaiting(): bool
    {
        return $this->status === 'waiting';
    }

    /**
     * Check if customer has been notified
     */
    public function isNotified(): bool
    {
        return $this->status === 'notified';
    }

    /**
     * Check if customer has been seated
     */
    public function isSeated(): bool
    {
        return $this->status === 'seated';
    }

    /**
     * Check if customer has left
     */
    public function hasLeft(): bool
    {
        return $this->status === 'left';
    }

    /**
     * Check if customer is no-show
     */
    public function isNoShow(): bool
    {
        return $this->status === 'no_show';
    }

    /**
     * Get current wait time in minutes
     */
    public function getCurrentWaitTimeAttribute(): int
    {
        if (!$this->joined_at) {
            return 0;
        }

        return $this->joined_at->diffInMinutes(now());
    }

    /**
     * Get position in queue
     */
    public function getQueuePositionAttribute(): int
    {
        return self::where('restaurant_id', $this->restaurant_id)
            ->where('status', 'waiting')
            ->where('joined_at', '<', $this->joined_at)
            ->count() + 1;
    }

    /**
     * Notify customer that table is ready
     */
    public function notifyCustomer(): void
    {
        $this->update([
            'status' => 'notified',
            'notified_at' => now(),
        ]);

        // TODO: Send SMS/notification to customer
    }

    /**
     * Mark customer as seated
     */
    public function markSeated(): void
    {
        $this->update([
            'status' => 'seated',
            'seated_at' => now(),
        ]);
    }

    /**
     * Mark customer as left
     */
    public function markLeft(): void
    {
        $this->update([
            'status' => 'left',
            'left_at' => now(),
        ]);
    }

    /**
     * Mark customer as no-show
     */
    public function markNoShow(): void
    {
        $this->update([
            'status' => 'no_show',
        ]);
    }

    /**
     * Update estimated wait time
     */
    public function updateEstimatedWaitTime(int $minutes): void
    {
        $this->update(['estimated_wait_time' => $minutes]);
    }

    /**
     * Scope for active waitlist (waiting customers)
     */
    public function scopeActive($query)
    {
        return $query->where('status', 'waiting');
    }

    /**
     * Scope for today's waitlist
     */
    public function scopeToday($query)
    {
        return $query->whereDate('joined_at', today());
    }

    /**
     * Scope by party size
     */
    public function scopeByPartySize($query, int $size)
    {
        return $query->where('party_size', $size);
    }

    /**
     * Scope by status
     */
    public function scopeByStatus($query, string $status)
    {
        return $query->where('status', $status);
    }

    /**
     * Generate unique waitlist number
     */
    public static function generateWaitlistNumber(): string
    {
        $prefix = 'W';
        $date = now()->format('md');
        $count = self::whereDate('created_at', today())->count() + 1;
        
        return "{$prefix}{$date}" . str_pad($count, 3, '0', STR_PAD_LEFT);
    }

    /**
     * Calculate average wait time for similar party sizes
     */
    public static function getAverageWaitTime(int $partySize): int
    {
        $recentWaitTimes = self::where('party_size', $partySize)
            ->where('status', 'seated')
            ->whereDate('created_at', '>=', now()->subDays(7))
            ->get()
            ->map(function ($entry) {
                return $entry->joined_at->diffInMinutes($entry->seated_at);
            });

        if ($recentWaitTimes->isEmpty()) {
            // Default estimates based on party size
            return match($partySize) {
                1, 2 => 15,
                3, 4 => 25,
                5, 6 => 35,
                default => 45
            };
        }

        return (int) $recentWaitTimes->average();
    }

    /**
     * Boot the model
     */
    protected static function boot()
    {
        parent::boot();

        static::creating(function ($waitlist) {
            if (empty($waitlist->waitlist_number)) {
                $waitlist->waitlist_number = self::generateWaitlistNumber();
            }
            if (empty($waitlist->joined_at)) {
                $waitlist->joined_at = now();
            }
            if (empty($waitlist->estimated_wait_time)) {
                $waitlist->estimated_wait_time = self::getAverageWaitTime($waitlist->party_size);
            }
            if (empty($waitlist->status)) {
                $waitlist->status = 'waiting';
            }
        });
    }
}
