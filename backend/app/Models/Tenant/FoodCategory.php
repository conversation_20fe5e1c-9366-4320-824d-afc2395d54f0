<?php

namespace App\Models\Tenant;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Support\Str;

class FoodCategory extends Model
{
    use HasFactory;

    protected $fillable = [
        'restaurant_id',
        'parent_id',
        'name',
        'slug',
        'description',
        'image',
        'icon',
        'color_code',
        'sort_order',
        'is_featured',
        'is_active',
        'show_in_menu',
        'available_days',
        'available_from',
        'available_until',
        'meta_title',
        'meta_description',
        'meta_keywords',
    ];

    protected $casts = [
        'available_days' => 'array',
        'available_from' => 'datetime:H:i',
        'available_until' => 'datetime:H:i',
        'meta_keywords' => 'array',
        'is_featured' => 'boolean',
        'is_active' => 'boolean',
        'show_in_menu' => 'boolean',
    ];

    /**
     * Boot the model
     */
    protected static function boot()
    {
        parent::boot();

        static::creating(function ($category) {
            if (empty($category->slug)) {
                $category->slug = Str::slug($category->name);
            }
        });

        static::updating(function ($category) {
            if ($category->isDirty('name') && empty($category->slug)) {
                $category->slug = Str::slug($category->name);
            }
        });
    }

    /**
     * Get the restaurant that owns this category
     */
    public function restaurant(): BelongsTo
    {
        return $this->belongsTo(Restaurant::class);
    }

    /**
     * Get the parent category
     */
    public function parent(): BelongsTo
    {
        return $this->belongsTo(FoodCategory::class, 'parent_id');
    }

    /**
     * Get the child categories
     */
    public function children(): HasMany
    {
        return $this->hasMany(FoodCategory::class, 'parent_id');
    }

    /**
     * Get the foods in this category
     */
    public function foods(): HasMany
    {
        return $this->hasMany(Food::class);
    }

    /**
     * Get active foods in this category
     */
    public function activeFoods(): HasMany
    {
        return $this->foods()->where('is_active', true)->where('is_available', true);
    }

    /**
     * Check if category is available at current time
     */
    public function isAvailableNow(): bool
    {
        if (!$this->is_active) {
            return false;
        }

        // Check day availability
        if ($this->available_days && !in_array(now()->dayOfWeek, $this->available_days)) {
            return false;
        }

        // Check time availability
        if ($this->available_from && $this->available_until) {
            $now = now()->format('H:i');
            return $now >= $this->available_from && $now <= $this->available_until;
        }

        return true;
    }

    /**
     * Get the category hierarchy as breadcrumb
     */
    public function getBreadcrumbAttribute(): array
    {
        $breadcrumb = [];
        $category = $this;

        while ($category) {
            array_unshift($breadcrumb, [
                'id' => $category->id,
                'name' => $category->name,
                'slug' => $category->slug,
            ]);
            $category = $category->parent;
        }

        return $breadcrumb;
    }

    /**
     * Scope for active categories
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope for featured categories
     */
    public function scopeFeatured($query)
    {
        return $query->where('is_featured', true);
    }

    /**
     * Scope for parent categories (no parent)
     */
    public function scopeParent($query)
    {
        return $query->whereNull('parent_id');
    }

    /**
     * Scope for categories shown in menu
     */
    public function scopeShowInMenu($query)
    {
        return $query->where('show_in_menu', true);
    }

    /**
     * Scope for ordering by sort order
     */
    public function scopeOrdered($query)
    {
        return $query->orderBy('sort_order')->orderBy('name');
    }
}
