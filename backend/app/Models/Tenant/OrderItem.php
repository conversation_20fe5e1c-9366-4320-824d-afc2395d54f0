<?php

namespace App\Models\Tenant;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

class OrderItem extends Model
{
    use HasFactory;

    protected $fillable = [
        'order_id',
        'menu_item_id',
        'item_name',
        'item_price',
        'quantity',
        'total_price',
        'special_instructions',
        'variations',
        'addons',
        'status',
        'preparation_started_at',
        'ready_at',
        'served_at',
    ];

    protected $casts = [
        'item_price' => 'decimal:2',
        'total_price' => 'decimal:2',
        'variations' => 'array',
        'addons' => 'array',
        'preparation_started_at' => 'datetime',
        'ready_at' => 'datetime',
        'served_at' => 'datetime',
    ];

    /**
     * Get the restaurant that owns this order item
     */
    public function restaurant(): BelongsTo
    {
        return $this->belongsTo(Restaurant::class);
    }

    /**
     * Get the order this item belongs to
     */
    public function order(): BelongsTo
    {
        return $this->belongsTo(Order::class);
    }

    /**
     * Get the food item
     */
    public function food(): BelongsTo
    {
        return $this->belongsTo(Food::class);
    }

    /**
     * Get the menu item variation (if selected)
     */
    public function menuItemVariation(): BelongsTo
    {
        return $this->belongsTo(MenuItemVariation::class, 'food_variant_id');
    }

    /**
     * Get the addons for this order item
     */
    public function addons(): HasMany
    {
        return $this->hasMany(OrderItemAddon::class);
    }

    /**
     * Get the employee who prepared this item
     */
    public function preparedByEmployee(): BelongsTo
    {
        return $this->belongsTo(Employee::class, 'prepared_by');
    }

    /**
     * Get the employee who served this item
     */
    public function servedByEmployee(): BelongsTo
    {
        return $this->belongsTo(Employee::class, 'served_by');
    }

    /**
     * Get the menu item this order item is based on
     */
    public function menuItem(): BelongsTo
    {
        return $this->belongsTo(MenuItem::class);
    }

    /**
     * Get the user who cancelled this item
     */
    public function cancelledBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'cancelled_by');
    }

    /**
     * Get the user who took this order item
     */
    public function takenBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'taken_by');
    }

    /**
     * Get item-level discounts
     */
    public function discounts(): HasMany
    {
        return $this->hasMany(OrderDiscount::class);
    }

    /**
     * Calculate total price including addons
     */
    public function getTotalPriceWithAddonsAttribute(): float
    {
        $addonTotal = $this->addons->sum('total_price');
        return $this->total_price + $addonTotal;
    }

    /**
     * Get preparation time in minutes
     */
    public function getPreparationTimeAttribute(): ?int
    {
        if (!$this->started_preparing_at || !$this->ready_at) {
            return null;
        }

        return $this->started_preparing_at->diffInMinutes($this->ready_at);
    }

    /**
     * Update item status
     */
    public function updateStatus(string $newStatus, ?Employee $employee = null): void
    {
        $updates = ['status' => $newStatus];

        switch ($newStatus) {
            case 'preparing':
                $updates['started_preparing_at'] = now();
                if ($employee) {
                    $updates['prepared_by'] = $employee->id;
                }
                break;
            case 'ready':
                $updates['ready_at'] = now();
                break;
            case 'served':
                $updates['served_at'] = now();
                if ($employee) {
                    $updates['served_by'] = $employee->id;
                }
                break;
        }

        $this->update($updates);
    }

    /**
     * Check if item can be cancelled
     */
    public function canBeCancelled(): bool
    {
        return in_array($this->status, ['pending', 'preparing']);
    }

    /**
     * Scope for items by status
     */
    public function scopeByStatus($query, $status)
    {
        return $query->where('status', $status);
    }

    /**
     * Scope for pending items
     */
    public function scopePending($query)
    {
        return $query->where('status', 'pending');
    }

    /**
     * Scope for preparing items
     */
    public function scopePreparing($query)
    {
        return $query->where('status', 'preparing');
    }

    /**
     * Scope for ready items
     */
    public function scopeReady($query)
    {
        return $query->where('status', 'ready');
    }

    // POS-specific methods

    /**
     * Check if item is complimentary
     */
    public function isComplimentary(): bool
    {
        // Simplified schema doesn't have is_complimentary field
        // Could check if item_price is 0 or use another logic
        return $this->item_price == 0;
    }

    /**
     * Check if item is cancelled
     */
    public function isCancelled(): bool
    {
        // Simplified schema doesn't have is_cancelled field
        // Use status field instead
        return $this->status === 'cancelled';
    }

    /**
     * Get net price after discount
     */
    public function getNetPriceAttribute(): float
    {
        return $this->item_price;
    }

    /**
     * Recalculate item price
     */
    public function recalculatePrice(): void
    {
        $this->total_price = $this->item_price * $this->quantity;
        $this->save();
    }
}
