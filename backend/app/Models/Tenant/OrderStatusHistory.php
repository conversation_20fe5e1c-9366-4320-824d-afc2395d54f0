<?php

namespace App\Models\Tenant;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class OrderStatusHistory extends Model
{
    use HasFactory;

    protected $fillable = [
        'restaurant_id',
        'order_id',
        'changed_by',
        'from_status',
        'to_status',
        'notes',
        'changed_at',
    ];

    protected $casts = [
        'changed_at' => 'datetime',
    ];

    /**
     * Get the restaurant that owns this history record
     */
    public function restaurant(): BelongsTo
    {
        return $this->belongsTo(Restaurant::class);
    }

    /**
     * Get the order this history belongs to
     */
    public function order(): BelongsTo
    {
        return $this->belongsTo(Order::class);
    }

    /**
     * Get the employee who changed the status
     */
    public function changedBy(): BelongsTo
    {
        return $this->belongsTo(Employee::class, 'changed_by');
    }

    /**
     * Get formatted status change
     */
    public function getStatusChangeAttribute(): string
    {
        if ($this->from_status) {
            return ucfirst($this->from_status) . ' → ' . ucfirst($this->to_status);
        }
        
        return 'Created as ' . ucfirst($this->to_status);
    }

    /**
     * Scope for recent changes
     */
    public function scopeRecent($query, $hours = 24)
    {
        return $query->where('changed_at', '>=', now()->subHours($hours));
    }
}
