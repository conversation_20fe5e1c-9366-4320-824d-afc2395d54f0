<?php

namespace App\Models\Tenant;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class EmployeeDocument extends Model
{
    use HasFactory;

    protected $fillable = [
        'employee_id',
        'media_id',
        'document_type',
        'title',
        'description',
        'sort_order',
        'is_required',
        'expiry_date',
    ];

    protected $casts = [
        'is_required' => 'boolean',
        'expiry_date' => 'date',
    ];

    /**
     * Document types available.
     */
    public const DOCUMENT_TYPES = [
        'resume' => 'Resume/CV',
        'id_card' => 'ID Card',
        'certificate' => 'Certificate',
        'contract' => 'Employment Contract',
        'photo' => 'Photo',
        'other' => 'Other',
    ];

    /**
     * Get the employee that owns this document.
     */
    public function employee(): BelongsTo
    {
        return $this->belongsTo(Employee::class);
    }

    /**
     * Get the media file for this document.
     */
    public function media(): BelongsTo
    {
        return $this->belongsTo(Media::class);
    }

    /**
     * Scope for ordered documents.
     */
    public function scopeOrdered($query)
    {
        return $query->orderBy('sort_order')->orderBy('title');
    }

    /**
     * Scope for required documents.
     */
    public function scopeRequired($query)
    {
        return $query->where('is_required', true);
    }

    /**
     * Scope for documents by type.
     */
    public function scopeOfType($query, $type)
    {
        return $query->where('document_type', $type);
    }

    /**
     * Check if document is expired.
     */
    public function getIsExpiredAttribute(): bool
    {
        return $this->expiry_date && $this->expiry_date->isPast();
    }

    /**
     * Check if document is expiring soon (within 30 days).
     */
    public function getIsExpiringSoonAttribute(): bool
    {
        return $this->expiry_date && $this->expiry_date->isBefore(now()->addDays(30));
    }

    /**
     * Get document type label.
     */
    public function getDocumentTypeLabelAttribute(): string
    {
        return self::DOCUMENT_TYPES[$this->document_type] ?? ucfirst($this->document_type);
    }

    /**
     * Get document URL.
     */
    public function getUrlAttribute(): ?string
    {
        return $this->media?->url;
    }

    /**
     * Get document thumbnail URL.
     */
    public function getThumbnailUrlAttribute(): ?string
    {
        return $this->media?->thumbnail_url;
    }
}
