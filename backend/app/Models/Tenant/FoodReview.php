<?php

namespace App\Models\Tenant;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use App\Models\MenuItem;

class FoodReview extends Model
{
    use HasFactory;

    protected $fillable = [
        'restaurant_id',
        'menu_item_id',
        'customer_id',
        'order_id',
        'rating',
        'title',
        'review',
        'images',
        'taste_rating',
        'presentation_rating',
        'value_rating',
        'portion_rating',
        'reviewer_name',
        'is_verified_purchase',
        'is_anonymous',
        'status',
        'moderated_by',
        'moderated_at',
        'moderation_notes',
        'helpful_count',
        'not_helpful_count',
        'featured',
        'restaurant_response',
        'responded_at',
        'responded_by',
    ];

    protected $casts = [
        'images' => 'array',
        'is_verified_purchase' => 'boolean',
        'is_anonymous' => 'boolean',
        'moderated_at' => 'datetime',
        'featured' => 'boolean',
        'responded_at' => 'datetime',
    ];

    /**
     * Get the restaurant that owns this review
     */
    public function restaurant(): BelongsTo
    {
        return $this->belongsTo(Restaurant::class);
    }

    /**
     * Get the menu item being reviewed
     */
    public function menuItem(): BelongsTo
    {
        return $this->belongsTo(MenuItem::class);
    }

    /**
     * Get the food being reviewed (alias for menuItem)
     */
    public function food(): BelongsTo
    {
        return $this->menuItem();
    }

    /**
     * Get the customer who wrote the review
     */
    public function customer(): BelongsTo
    {
        return $this->belongsTo(Customer::class);
    }

    /**
     * Get the order this review is for
     */
    public function order(): BelongsTo
    {
        return $this->belongsTo(Order::class);
    }

    /**
     * Get the employee who moderated this review
     */
    public function moderatedBy(): BelongsTo
    {
        return $this->belongsTo(Employee::class, 'moderated_by');
    }

    /**
     * Get the employee who responded to this review
     */
    public function respondedBy(): BelongsTo
    {
        return $this->belongsTo(Employee::class, 'responded_by');
    }

    /**
     * Get the reviewer's display name
     */
    public function getReviewerDisplayNameAttribute(): string
    {
        if ($this->is_anonymous) {
            return 'Anonymous';
        }

        return $this->reviewer_name ?? $this->customer?->full_name ?? 'Guest';
    }

    /**
     * Get the overall rating (average of detailed ratings if available)
     */
    public function getOverallRatingAttribute(): float
    {
        if ($this->taste_rating || $this->presentation_rating || $this->value_rating || $this->portion_rating) {
            $ratings = array_filter([
                $this->taste_rating,
                $this->presentation_rating,
                $this->value_rating,
                $this->portion_rating,
            ]);

            return count($ratings) > 0 ? array_sum($ratings) / count($ratings) : $this->rating;
        }

        return $this->rating;
    }

    /**
     * Get helpfulness ratio
     */
    public function getHelpfulnessRatioAttribute(): float
    {
        $total = $this->helpful_count + $this->not_helpful_count;

        if ($total === 0) {
            return 0;
        }

        return ($this->helpful_count / $total) * 100;
    }

    /**
     * Approve the review
     */
    public function approve(?Employee $moderator = null): void
    {
        $this->update([
            'status' => 'approved',
            'moderated_by' => $moderator?->id,
            'moderated_at' => now(),
        ]);

        // Update menu item rating if method exists
        if (method_exists($this->menuItem, 'updateRating')) {
            $this->menuItem->updateRating();
        }
    }

    /**
     * Reject the review
     */
    public function reject(?Employee $moderator = null, ?string $notes = null): void
    {
        $this->update([
            'status' => 'rejected',
            'moderated_by' => $moderator?->id,
            'moderated_at' => now(),
            'moderation_notes' => $notes,
        ]);
    }

    /**
     * Hide the review
     */
    public function hide(?Employee $moderator = null, ?string $notes = null): void
    {
        $this->update([
            'status' => 'hidden',
            'moderated_by' => $moderator?->id,
            'moderated_at' => now(),
            'moderation_notes' => $notes,
        ]);
    }

    /**
     * Add restaurant response
     */
    public function addResponse(string $response, Employee $employee): void
    {
        $this->update([
            'restaurant_response' => $response,
            'responded_at' => now(),
            'responded_by' => $employee->id,
        ]);
    }

    /**
     * Mark as helpful
     */
    public function markHelpful(): void
    {
        $this->increment('helpful_count');
    }

    /**
     * Mark as not helpful
     */
    public function markNotHelpful(): void
    {
        $this->increment('not_helpful_count');
    }

    /**
     * Feature the review
     */
    public function feature(): void
    {
        $this->update(['featured' => true]);
    }

    /**
     * Unfeature the review
     */
    public function unfeature(): void
    {
        $this->update(['featured' => false]);
    }

    /**
     * Scope for approved reviews
     */
    public function scopeApproved($query)
    {
        return $query->where('status', 'approved');
    }

    /**
     * Scope for pending reviews
     */
    public function scopePending($query)
    {
        return $query->where('status', 'pending');
    }

    /**
     * Scope for featured reviews
     */
    public function scopeFeatured($query)
    {
        return $query->where('featured', true);
    }

    /**
     * Scope for verified purchase reviews
     */
    public function scopeVerified($query)
    {
        return $query->where('is_verified_purchase', true);
    }

    /**
     * Scope for reviews with images
     */
    public function scopeWithImages($query)
    {
        return $query->whereNotNull('images');
    }

    /**
     * Scope for reviews by rating
     */
    public function scopeByRating($query, $rating)
    {
        return $query->where('rating', $rating);
    }

    /**
     * Scope for recent reviews
     */
    public function scopeRecent($query, $days = 30)
    {
        return $query->where('created_at', '>=', now()->subDays($days));
    }
}
