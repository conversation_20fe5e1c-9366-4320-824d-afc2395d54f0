<?php

namespace App\Models\Tenant;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class DeliveryOrder extends Model
{
    use HasFactory;

    protected $fillable = [
        'restaurant_id',
        'order_id',
        'delivery_driver_id',
        'delivery_zone_id',
        'pickup_address',
        'delivery_address',
        'pickup_latitude',
        'pickup_longitude',
        'delivery_latitude',
        'delivery_longitude',
        'assigned_at',
        'picked_up_at',
        'delivered_at',
        'estimated_delivery_time',
        'actual_delivery_time',
        'status',
        'distance_km',
        'delivery_fee',
        'driver_commission',
        'delivery_instructions',
        'delivery_notes',
        'delivery_photo',
        'customer_rating',
        'customer_feedback',
        'failure_reason',
        'delivery_attempts',
        'next_attempt_at',
    ];

    protected $casts = [
        'pickup_latitude' => 'decimal:8',
        'pickup_longitude' => 'decimal:8',
        'delivery_latitude' => 'decimal:8',
        'delivery_longitude' => 'decimal:8',
        'assigned_at' => 'datetime',
        'picked_up_at' => 'datetime',
        'delivered_at' => 'datetime',
        'distance_km' => 'decimal:2',
        'delivery_fee' => 'decimal:2',
        'driver_commission' => 'decimal:2',
        'next_attempt_at' => 'datetime',
    ];

    /**
     * Get the restaurant that owns this delivery order
     */
    public function restaurant(): BelongsTo
    {
        return $this->belongsTo(Restaurant::class);
    }

    /**
     * Get the order being delivered
     */
    public function order(): BelongsTo
    {
        return $this->belongsTo(Order::class);
    }

    /**
     * Get the delivery driver
     */
    public function deliveryDriver(): BelongsTo
    {
        return $this->belongsTo(DeliveryDriver::class);
    }

    /**
     * Get the delivery zone
     */
    public function deliveryZone(): BelongsTo
    {
        return $this->belongsTo(DeliveryZone::class);
    }

    /**
     * Assign to a driver
     */
    public function assignToDriver(DeliveryDriver $driver): void
    {
        $this->update([
            'delivery_driver_id' => $driver->id,
            'status' => 'assigned',
            'assigned_at' => now(),
        ]);

        $driver->updateStatus('busy');
    }

    /**
     * Mark as picked up
     */
    public function markPickedUp(): void
    {
        $this->update([
            'status' => 'picked_up',
            'picked_up_at' => now(),
        ]);

        $this->order->updateStatus('out_for_delivery');
    }

    /**
     * Mark as delivered
     */
    public function markDelivered(?string $notes = null, ?string $photo = null): void
    {
        $deliveredAt = now();
        $actualTime = $this->assigned_at ? $this->assigned_at->diffInMinutes($deliveredAt) : null;

        $this->update([
            'status' => 'delivered',
            'delivered_at' => $deliveredAt,
            'actual_delivery_time' => $actualTime,
            'delivery_notes' => $notes,
            'delivery_photo' => $photo,
        ]);

        $this->order->updateStatus('delivered');
        
        if ($this->deliveryDriver) {
            $this->deliveryDriver->completeDelivery($this);
            $this->deliveryDriver->updateStatus('available');
        }
    }

    /**
     * Mark as failed
     */
    public function markFailed(string $reason): void
    {
        $this->update([
            'status' => 'failed',
            'failure_reason' => $reason,
        ]);

        $this->increment('delivery_attempts');

        if ($this->deliveryDriver) {
            $this->deliveryDriver->updateStatus('available');
        }
    }

    /**
     * Calculate distance between pickup and delivery
     */
    public function calculateDistance(): float
    {
        if (!$this->pickup_latitude || !$this->pickup_longitude || 
            !$this->delivery_latitude || !$this->delivery_longitude) {
            return 0;
        }

        $earthRadius = 6371; // km

        $latDelta = deg2rad($this->delivery_latitude - $this->pickup_latitude);
        $lonDelta = deg2rad($this->delivery_longitude - $this->pickup_longitude);

        $a = sin($latDelta / 2) * sin($latDelta / 2) +
             cos(deg2rad($this->pickup_latitude)) * cos(deg2rad($this->delivery_latitude)) *
             sin($lonDelta / 2) * sin($lonDelta / 2);

        $c = 2 * atan2(sqrt($a), sqrt(1 - $a));

        return $earthRadius * $c;
    }

    /**
     * Check if delivery is on time
     */
    public function isOnTime(): bool
    {
        if (!$this->delivered_at || !$this->estimated_delivery_time) {
            return false;
        }

        return $this->actual_delivery_time <= $this->estimated_delivery_time;
    }

    /**
     * Get delivery status label
     */
    public function getStatusLabelAttribute(): string
    {
        return match ($this->status) {
            'pending' => 'Pending Assignment',
            'assigned' => 'Assigned to Driver',
            'picked_up' => 'Picked Up',
            'on_the_way' => 'On the Way',
            'delivered' => 'Delivered',
            'failed' => 'Delivery Failed',
            'cancelled' => 'Cancelled',
            'returned' => 'Returned',
            default => ucfirst($this->status),
        };
    }

    /**
     * Scope for active deliveries
     */
    public function scopeActive($query)
    {
        return $query->whereIn('status', ['pending', 'assigned', 'picked_up', 'on_the_way']);
    }

    /**
     * Scope for deliveries by status
     */
    public function scopeByStatus($query, $status)
    {
        return $query->where('status', $status);
    }

    /**
     * Scope for deliveries by driver
     */
    public function scopeByDriver($query, $driverId)
    {
        return $query->where('delivery_driver_id', $driverId);
    }

    /**
     * Scope for today's deliveries
     */
    public function scopeToday($query)
    {
        return $query->whereDate('created_at', today());
    }
}
