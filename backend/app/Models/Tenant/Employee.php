<?php

namespace App\Models\Tenant;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use App\Traits\HasMediaLibrary;
use Carbon\Carbon;
use Spatie\Permission\Traits\HasRoles;
use Illuminate\Notifications\Notifiable;

class Employee extends Authenticatable
{
    use HasFactory, HasMediaLibrary, HasRoles, Notifiable;

    /**
     * The guard name for <PERSON><PERSON> Permission
     */
    protected $guard_name = 'web';

    protected $fillable = [
        'department_id',
        'primary_branch_id',
        'user_id', // Foreign key to users table
        'employee_id',
        'first_name',
        'last_name',
        // Removed: name, email, password, username, phone, role, address, emergency_contact_name, emergency_contact_phone
        // These fields now exist only in users table
        'mobile',
        'date_of_birth',
        'gender',
        'city',
        'state',
        'country',
        'postal_code',
        'emergency_contact_relationship',
        'position',
        'employment_type',
        'employment_status',
        'hire_date',
        'termination_date',
        'salary',
        'hourly_rate',
        'status',
        'notes',
        'social_security_number',
        'tax_id',
        'bank_account_number',
        'bank_routing_number',
        'bank_name',
        'is_active',
    ];

    protected $casts = [
        'date_of_birth' => 'date',
        'hire_date' => 'date',
        'termination_date' => 'date',
        'salary' => 'decimal:2',
        'hourly_rate' => 'decimal:2',
        'is_active' => 'boolean',
        // Removed: password cast (now in users table)
    ];

    protected $hidden = [
        // Removed: password (now in users table)
        'social_security_number',
        'tax_id',
        'bank_account_number',
        'bank_routing_number',
    ];

    /**
     * Get the primary branch
     */
    public function primaryBranch(): BelongsTo
    {
        return $this->belongsTo(Branch::class, 'primary_branch_id');
    }

    /**
     * Get the department
     */
    public function department(): BelongsTo
    {
        return $this->belongsTo(Department::class);
    }

    /**
     * Get the associated user account (tenant-specific)
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }



    /**
     * Get all documents for this employee
     */
    public function documents(): HasMany
    {
        return $this->hasMany(EmployeeDocument::class);
    }

    /**
     * Get all shifts for this employee
     */
    public function shifts(): HasMany
    {
        return $this->hasMany(Shift::class);
    }

    /**
     * Get all employee shifts for this employee
     */
    public function employeeShifts(): HasMany
    {
        return $this->hasMany(EmployeeShift::class);
    }

    /**
     * Get all time entries for this employee
     */
    public function timeEntries(): HasMany
    {
        return $this->hasMany(TimeEntry::class);
    }

    /**
     * Get all payroll records for this employee
     */
    public function payrollRecords(): HasMany
    {
        return $this->hasMany(PayrollRecord::class);
    }

    /**
     * Get all leave entitlements for this employee
     */
    public function leaveEntitlements(): HasMany
    {
        return $this->hasMany(EmployeeLeaveEntitlement::class);
    }

    /**
     * Get all leave requests for this employee
     */
    public function leaveRequests(): HasMany
    {
        return $this->hasMany(LeaveRequest::class);
    }

    /**
     * Get all attendance records for this employee
     */
    public function attendanceRecords(): HasMany
    {
        return $this->hasMany(AttendanceRecord::class);
    }

    /**
     * Get all loans for this employee
     */
    public function loans(): HasMany
    {
        return $this->hasMany(EmployeeLoan::class);
    }

    /**
     * Get active loans for this employee
     */
    public function activeLoans(): HasMany
    {
        return $this->hasMany(EmployeeLoan::class)->where('status', 'active');
    }

    /**
     * Get employees supervised by this employee
     */
    public function subordinates(): HasMany
    {
        return $this->hasMany(Employee::class, 'supervisor_id');
    }

    /**
     * Get the supervisor of this employee
     */
    public function supervisor(): BelongsTo
    {
        return $this->belongsTo(Employee::class, 'supervisor_id');
    }

    /**
     * Get all orders served by this employee
     */
    public function servedOrders(): HasMany
    {
        return $this->hasMany(Order::class, 'served_by');
    }

    /**
     * Get all orders handled by this employee (using staff_id)
     */
    public function handledOrders(): HasMany
    {
        return $this->hasMany(Order::class, 'staff_id');
    }

    /**
     * Get employee's full name (from user or first_name + last_name)
     */
    public function getFullNameAttribute(): string
    {
        if ($this->user && $this->user->name) {
            return $this->user->name;
        }
        return trim($this->first_name . ' ' . $this->last_name);
    }

    /**
     * Get employee's email (from user)
     */
    public function getEmailAttribute(): ?string
    {
        return $this->user?->email;
    }

    /**
     * Get employee's phone (from user)
     */
    public function getPhoneAttribute(): ?string
    {
        return $this->user?->phone;
    }

    /**
     * Get employee's role (from user)
     */
    public function getRoleAttribute(): ?string
    {
        return $this->user?->role;
    }

    /**
     * Get employee's name (from user)
     */
    public function getNameAttribute(): ?string
    {
        return $this->user?->name;
    }

    /**
     * Get employee's age
     */
    public function getAgeAttribute(): ?int
    {
        return $this->date_of_birth ? $this->date_of_birth->age : null;
    }

    /**
     * Get years of service
     */
    public function getYearsOfServiceAttribute(): float
    {
        if (!$this->hire_date) {
            return 0;
        }

        $endDate = $this->termination_date ?? now();
        return $this->hire_date->diffInYears($endDate, true);
    }

    /**
     * Get months of service
     */
    public function getMonthsOfServiceAttribute(): int
    {
        if (!$this->hire_date) {
            return 0;
        }

        $endDate = $this->termination_date ?? now();
        return $this->hire_date->diffInMonths($endDate);
    }

    /**
     * Check if employee is active
     */
    public function isActive(): bool
    {
        return $this->status === 'active';
    }

    /**
     * Check if employee is terminated
     */
    public function isTerminated(): bool
    {
        return $this->status === 'terminated';
    }

    /**
     * Check if employee is on leave
     */
    public function isOnLeave(): bool
    {
        return $this->status === 'on_leave';
    }

    /**
     * Get current monthly salary
     */
    public function getMonthlySalaryAttribute(): float
    {
        if ($this->employment_type === 'salary') {
            return $this->salary / 12;
        } elseif ($this->employment_type === 'hourly' && $this->hourly_rate) {
            // Estimate based on 40 hours/week * 4.33 weeks/month
            return $this->hourly_rate * 40 * 4.33;
        }

        return 0;
    }

    /**
     * Get total hours worked this month
     */
    public function getHoursWorkedThisMonth(): float
    {
        return $this->timeEntries()
            ->whereYear('date', now()->year)
            ->whereMonth('date', now()->month)
            ->sum('hours_worked');
    }

    /**
     * Get total hours worked this week
     */
    public function getHoursWorkedThisWeek(): float
    {
        $startOfWeek = now()->startOfWeek();
        $endOfWeek = now()->endOfWeek();

        return $this->timeEntries()
            ->whereBetween('date', [$startOfWeek, $endOfWeek])
            ->sum('hours_worked');
    }

    /**
     * Get average hours per week
     */
    public function getAverageHoursPerWeek(int $weeks = 4): float
    {
        $startDate = now()->subWeeks($weeks);

        $totalHours = $this->timeEntries()
            ->where('date', '>=', $startDate)
            ->sum('hours_worked');

        return $weeks > 0 ? $totalHours / $weeks : 0;
    }

    /**
     * Get upcoming shifts
     */
    public function getUpcomingShifts(int $days = 7)
    {
        return $this->shifts()
            ->whereBetween('shift_date', [now(), now()->addDays($days)])
            ->orderBy('shift_date')
            ->orderBy('start_time')
            ->get();
    }

    /**
     * Check if employee is scheduled for a specific date
     */
    public function isScheduledOn(Carbon $date): bool
    {
        return $this->shifts()
            ->whereDate('shift_date', $date)
            ->exists();
    }

    /**
     * Get total overtime hours this month
     */
    public function getOvertimeHoursThisMonth(): float
    {
        return $this->timeEntries()
            ->whereYear('date', now()->year)
            ->whereMonth('date', now()->month)
            ->sum('overtime_hours');
    }

    /**
     * Calculate gross pay for a period
     */
    public function calculateGrossPay(Carbon $startDate, Carbon $endDate): float
    {
        $timeEntries = $this->timeEntries()
            ->whereBetween('date', [$startDate, $endDate])
            ->get();

        $totalPay = 0;

        foreach ($timeEntries as $entry) {
            if ($this->employment_type === 'hourly') {
                $regularPay = $entry->hours_worked * $this->hourly_rate;
                $overtimePay = $entry->overtime_hours * ($this->hourly_rate * 1.5); // 1.5x for overtime
                $totalPay += $regularPay + $overtimePay;
            }
        }

        // For salary employees, calculate based on days worked
        if ($this->employment_type === 'salary') {
            $daysInPeriod = $startDate->diffInDays($endDate) + 1;
            $dailySalary = $this->salary / 365; // Annual salary / 365 days
            $totalPay = $dailySalary * $daysInPeriod;
        }

        return $totalPay;
    }

    /**
     * Get employee's display name (name or first_name + last_name)
     */
    public function getDisplayNameAttribute(): string
    {
        if ($this->name) {
            return $this->name;
        }
        return trim($this->first_name . ' ' . $this->last_name);
    }

    /**
     * Get leave entitlement for a specific leave type and year
     */
    public function getLeaveEntitlement(int $leaveTypeId, int $year = null): ?EmployeeLeaveEntitlement
    {
        $year = $year ?? now()->year;

        return $this->leaveEntitlements()
            ->where('leave_type_id', $leaveTypeId)
            ->where('year', $year)
            ->first();
    }

    /**
     * Get available leave days for a specific leave type
     */
    public function getAvailableLeaveDays(int $leaveTypeId, int $year = null): int
    {
        $entitlement = $this->getLeaveEntitlement($leaveTypeId, $year);
        return $entitlement ? $entitlement->available_days : 0;
    }

    /**
     * Check if employee has pending leave requests
     */
    public function hasPendingLeaveRequests(): bool
    {
        return $this->leaveRequests()->where('status', 'pending')->exists();
    }

    /**
     * Get current attendance record for today
     */
    public function getTodayAttendance(): ?AttendanceRecord
    {
        return $this->attendanceRecords()
            ->whereDate('date', now())
            ->first();
    }

    /**
     * Check if employee is currently clocked in
     */
    public function isClockedIn(): bool
    {
        $todayAttendance = $this->getTodayAttendance();
        return $todayAttendance && $todayAttendance->isClockedIn();
    }

    /**
     * Get total outstanding loan balance
     */
    public function getTotalLoanBalance(): float
    {
        return $this->activeLoans()->sum('outstanding_balance');
    }

    /**
     * Get monthly loan deductions
     */
    public function getMonthlyLoanDeductions(): float
    {
        return $this->activeLoans()->sum('monthly_installment');
    }

    /**
     * Check if employee is on probation
     */
    public function isOnProbation(): bool
    {
        return $this->probation_end_date && $this->probation_end_date->isFuture();
    }

    /**
     * Get days remaining in probation
     */
    public function getProbationDaysRemaining(): int
    {
        if (!$this->isOnProbation()) {
            return 0;
        }

        return now()->diffInDays($this->probation_end_date);
    }

    /**
     * Get role label
     */
    public function getRoleLabelAttribute(): string
    {
        // Handle case where role column might not exist yet
        $role = $this->attributes['role'] ?? 'waiter';

        return match($role) {
            'waiter' => 'Waiter',
            'chef' => 'Chef',
            'rider' => 'Rider',
            'manager' => 'Manager',
            default => 'Waiter'
        };
    }

    /**
     * Get employment status label
     */
    public function getEmploymentStatusLabelAttribute(): string
    {
        return match($this->employment_status) {
            'active' => 'Active',
            'inactive' => 'Inactive',
            'terminated' => 'Terminated',
            'resigned' => 'Resigned',
            default => 'Unknown'
        };
    }

    /**
     * Sync documents for this employee
     */
    public function syncDocuments(array $documents): void
    {
        // Remove existing documents not in the new list
        $newMediaIds = collect($documents)->pluck('media_id')->filter();
        $this->documents()->whereNotIn('media_id', $newMediaIds)->delete();

        // Add or update documents
        foreach ($documents as $index => $document) {
            if (isset($document['media_id'])) {
                $this->documents()->updateOrCreate(
                    ['media_id' => $document['media_id']],
                    [
                        'document_type' => $document['document_type'] ?? 'other',
                        'title' => $document['title'] ?? 'Document',
                        'description' => $document['description'] ?? null,
                        'sort_order' => $index,
                        'is_required' => $document['is_required'] ?? false,
                        'expiry_date' => $document['expiry_date'] ?? null,
                    ]
                );
            }
        }
    }

    /**
     * Terminate employee
     */
    public function terminate(Carbon $terminationDate, ?string $reason = null): void
    {
        $this->update([
            'status' => 'terminated',
            'termination_date' => $terminationDate,
            'notes' => $this->notes . "\nTerminated on {$terminationDate->format('Y-m-d')}" .
                      ($reason ? ": {$reason}" : ''),
        ]);
    }

    /**
     * Reactivate employee
     */
    public function reactivate(): void
    {
        $this->update([
            'status' => 'active',
            'termination_date' => null,
        ]);
    }

    /**
     * Scope for active employees
     */
    public function scopeActive($query)
    {
        return $query->where('status', 'active');
    }

    /**
     * Scope for terminated employees
     */
    public function scopeTerminated($query)
    {
        return $query->where('status', 'terminated');
    }

    /**
     * Scope by department
     */
    public function scopeByDepartment($query, $departmentId)
    {
        return $query->where('department_id', $departmentId);
    }

    /**
     * Scope by employment type
     */
    public function scopeByEmploymentType($query, string $type)
    {
        return $query->where('employment_type', $type);
    }

    /**
     * Scope for employees with specific role
     */
    public function scopeWithRole($query, string $role)
    {
        return $query->whereHas('roles', function ($q) use ($role) {
            $q->where('name', $role);
        });
    }

    /**
     * Scope for managers
     */
    public function scopeManagers($query)
    {
        return $query->withRole('Manager');
    }

    /**
     * Scope for ordered employees
     */
    public function scopeOrdered($query)
    {
        return $query->orderBy('name')->orderBy('first_name')->orderBy('last_name');
    }

    /**
     * Check if employee is a manager
     */
    public function isManager(): bool
    {
        return $this->hasRole('Manager');
    }

    /**
     * Check if employee is a waiter
     */
    public function isWaiter(): bool
    {
        return $this->hasRole('Waiter');
    }

    /**
     * Check if employee is a chef
     */
    public function isChef(): bool
    {
        return $this->hasRole('Chef') || $this->hasRole('Kitchen');
    }

    /**
     * Get employee's primary role
     */
    public function getPrimaryRoleAttribute(): ?string
    {
        $role = $this->roles()->first();
        return $role ? $role->name : null;
    }

    /**
     * Boot the model
     */
    protected static function boot()
    {
        parent::boot();

        static::creating(function ($employee) {
            if (empty($employee->employee_id)) {
                $employee->employee_id = self::generateEmployeeId();
            }
            if (empty($employee->status)) {
                $employee->status = 'active';
            }
            // Removed username generation - now handled in users table
        });
    }

    /**
     * Generate unique employee ID
     */
    public static function generateEmployeeId(): string
    {
        $prefix = 'EMP';
        $year = now()->format('Y');
        $count = self::whereYear('created_at', now()->year)->count() + 1;

        return "{$prefix}-{$year}-" . str_pad($count, 4, '0', STR_PAD_LEFT);
    }

}
