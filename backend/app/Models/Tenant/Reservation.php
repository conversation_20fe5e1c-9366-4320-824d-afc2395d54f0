<?php

namespace App\Models\Tenant;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Carbon\Carbon;

class Reservation extends Model
{
    use HasFactory;

    protected $fillable = [
        'restaurant_id',
        'table_id',
        'customer_id',
        'reservation_number',
        'customer_name',
        'customer_phone',
        'customer_email',
        'party_size',
        'reservation_date',
        'reservation_time',
        'duration_minutes',
        'status',
        'special_requests',
        'notes',
        'confirmed_at',
        'checked_in_at',
        'completed_at',
        'cancelled_at',
        'cancellation_reason',
        'reminder_sent_at',
        'created_by',
    ];

    protected $casts = [
        'reservation_date' => 'date',
        'reservation_time' => 'datetime:H:i',
        'party_size' => 'integer',
        'duration_minutes' => 'integer',
        'confirmed_at' => 'datetime',
        'checked_in_at' => 'datetime',
        'completed_at' => 'datetime',
        'cancelled_at' => 'datetime',
        'reminder_sent_at' => 'datetime',
    ];

    /**
     * Get the restaurant that owns this reservation
     */
    public function restaurant(): BelongsTo
    {
        return $this->belongsTo(Restaurant::class);
    }

    /**
     * Get the table for this reservation
     */
    public function table(): BelongsTo
    {
        return $this->belongsTo(Table::class);
    }

    /**
     * Get the customer for this reservation
     */
    public function customer(): BelongsTo
    {
        return $this->belongsTo(Customer::class);
    }

    /**
     * Get the staff member who created this reservation
     */
    public function creator(): BelongsTo
    {
        return $this->belongsTo(\App\Models\User::class, 'created_by');
    }

    /**
     * Get the full reservation datetime
     */
    public function getReservationDatetimeAttribute(): Carbon
    {
        return Carbon::parse($this->reservation_date->format('Y-m-d') . ' ' . $this->reservation_time->format('H:i:s'));
    }

    /**
     * Get the end time of the reservation
     */
    public function getEndTimeAttribute(): Carbon
    {
        return $this->reservation_datetime->addMinutes($this->duration_minutes);
    }

    /**
     * Check if reservation is pending
     */
    public function isPending(): bool
    {
        return $this->status === 'pending';
    }

    /**
     * Check if reservation is confirmed
     */
    public function isConfirmed(): bool
    {
        return $this->status === 'confirmed';
    }

    /**
     * Check if customer has checked in
     */
    public function isCheckedIn(): bool
    {
        return $this->status === 'checked_in';
    }

    /**
     * Check if reservation is completed
     */
    public function isCompleted(): bool
    {
        return $this->status === 'completed';
    }

    /**
     * Check if reservation is cancelled
     */
    public function isCancelled(): bool
    {
        return $this->status === 'cancelled';
    }

    /**
     * Check if reservation is no-show
     */
    public function isNoShow(): bool
    {
        return $this->status === 'no_show';
    }

    /**
     * Check if reservation is overdue (past reservation time + grace period)
     */
    public function isOverdue(): bool
    {
        $gracePeriod = 15; // 15 minutes grace period
        return now()->isAfter($this->reservation_datetime->addMinutes($gracePeriod)) && 
               in_array($this->status, ['pending', 'confirmed']);
    }

    /**
     * Check if reservation needs reminder
     */
    public function needsReminder(): bool
    {
        $reminderTime = $this->reservation_datetime->subHours(2); // 2 hours before
        return now()->isAfter($reminderTime) && 
               !$this->reminder_sent_at && 
               in_array($this->status, ['pending', 'confirmed']);
    }

    /**
     * Confirm the reservation
     */
    public function confirm(): void
    {
        $this->update([
            'status' => 'confirmed',
            'confirmed_at' => now(),
        ]);
    }

    /**
     * Check in the customer
     */
    public function checkIn(): void
    {
        $this->update([
            'status' => 'checked_in',
            'checked_in_at' => now(),
        ]);

        // Update table status
        if ($this->table) {
            $this->table->update(['status' => 'occupied']);
        }
    }

    /**
     * Complete the reservation
     */
    public function complete(): void
    {
        $this->update([
            'status' => 'completed',
            'completed_at' => now(),
        ]);

        // Free up the table
        if ($this->table) {
            $this->table->update(['status' => 'available']);
        }
    }

    /**
     * Cancel the reservation
     */
    public function cancel(string $reason = null): void
    {
        $this->update([
            'status' => 'cancelled',
            'cancelled_at' => now(),
            'cancellation_reason' => $reason,
        ]);

        // Free up the table if it was reserved
        if ($this->table && $this->table->status === 'reserved') {
            $this->table->update(['status' => 'available']);
        }
    }

    /**
     * Mark as no-show
     */
    public function markNoShow(): void
    {
        $this->update([
            'status' => 'no_show',
        ]);

        // Free up the table
        if ($this->table) {
            $this->table->update(['status' => 'available']);
        }
    }

    /**
     * Mark reminder as sent
     */
    public function markReminderSent(): void
    {
        $this->update(['reminder_sent_at' => now()]);
    }

    /**
     * Scope for today's reservations
     */
    public function scopeToday($query)
    {
        return $query->whereDate('reservation_date', today());
    }

    /**
     * Scope for upcoming reservations
     */
    public function scopeUpcoming($query)
    {
        return $query->where('reservation_date', '>=', today())
                    ->whereIn('status', ['pending', 'confirmed']);
    }

    /**
     * Scope for active reservations
     */
    public function scopeActive($query)
    {
        return $query->whereIn('status', ['pending', 'confirmed', 'checked_in']);
    }

    /**
     * Scope by status
     */
    public function scopeByStatus($query, string $status)
    {
        return $query->where('status', $status);
    }

    /**
     * Scope for date range
     */
    public function scopeDateRange($query, $startDate, $endDate)
    {
        return $query->whereBetween('reservation_date', [$startDate, $endDate]);
    }

    /**
     * Generate unique reservation number
     */
    public static function generateReservationNumber(): string
    {
        $prefix = 'RES';
        $date = now()->format('Ymd');
        $random = strtoupper(\Str::random(4));
        
        return "{$prefix}-{$date}-{$random}";
    }

    /**
     * Boot the model
     */
    protected static function boot()
    {
        parent::boot();

        static::creating(function ($reservation) {
            if (empty($reservation->reservation_number)) {
                $reservation->reservation_number = self::generateReservationNumber();
            }
            if (empty($reservation->duration_minutes)) {
                $reservation->duration_minutes = 120; // Default 2 hours
            }
        });
    }
}
