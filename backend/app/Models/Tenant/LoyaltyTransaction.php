<?php

namespace App\Models\Tenant;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class LoyaltyTransaction extends Model
{
    use HasFactory;

    protected $fillable = [
        'loyalty_account_id',
        'order_id',
        'transaction_type',
        'points',
        'order_amount',
        'description',
        'processed_by',
        'notes',
        'metadata',
        'expires_at',
    ];

    protected $casts = [
        'points' => 'integer',
        'order_amount' => 'decimal:2',
        'metadata' => 'array',
        'expires_at' => 'datetime',
    ];

    /**
     * Get the loyalty account that owns this transaction
     */
    public function loyaltyAccount(): BelongsTo
    {
        return $this->belongsTo(LoyaltyAccount::class);
    }

    /**
     * Get the order associated with this transaction
     */
    public function order(): BelongsTo
    {
        return $this->belongsTo(Order::class);
    }

    /**
     * Get the user who processed this transaction
     */
    public function processedBy(): BelongsTo
    {
        return $this->belongsTo(\App\Models\User::class, 'processed_by');
    }

    /**
     * Scope for earned points
     */
    public function scopeEarned($query)
    {
        return $query->where('transaction_type', 'earned');
    }

    /**
     * Scope for redeemed points
     */
    public function scopeRedeemed($query)
    {
        return $query->where('transaction_type', 'redeemed');
    }

    /**
     * Scope for refunded points
     */
    public function scopeRefunded($query)
    {
        return $query->where('transaction_type', 'refunded');
    }

    /**
     * Scope for manual adjustments
     */
    public function scopeManualAdjustment($query)
    {
        return $query->where('transaction_type', 'manual_adjustment');
    }

    /**
     * Check if transaction is a credit (adds points)
     */
    public function isCredit(): bool
    {
        return in_array($this->transaction_type, ['earned', 'refunded', 'manual_adjustment', 'birthday_bonus', 'referral_bonus']) && $this->points > 0;
    }

    /**
     * Check if transaction is a debit (removes points)
     */
    public function isDebit(): bool
    {
        return in_array($this->transaction_type, ['redeemed', 'manual_adjustment', 'expired']) && $this->points < 0;
    }

    /**
     * Get formatted transaction type
     */
    public function getFormattedTypeAttribute(): string
    {
        return match ($this->transaction_type) {
            'earned' => 'Points Earned',
            'redeemed' => 'Points Redeemed',
            'refunded' => 'Points Refunded',
            'manual_adjustment' => 'Manual Adjustment',
            'birthday_bonus' => 'Birthday Bonus',
            'referral_bonus' => 'Referral Bonus',
            'expired' => 'Points Expired',
            default => ucfirst(str_replace('_', ' ', $this->transaction_type)),
        };
    }

    /**
     * Get transaction icon
     */
    public function getIconAttribute(): string
    {
        return match ($this->transaction_type) {
            'earned' => 'fas fa-plus-circle text-green-500',
            'redeemed' => 'fas fa-minus-circle text-red-500',
            'refunded' => 'fas fa-undo text-blue-500',
            'manual_adjustment' => 'fas fa-edit text-yellow-500',
            'birthday_bonus' => 'fas fa-birthday-cake text-pink-500',
            'referral_bonus' => 'fas fa-users text-purple-500',
            'expired' => 'fas fa-clock text-gray-500',
            default => 'fas fa-circle text-gray-400',
        };
    }
}
