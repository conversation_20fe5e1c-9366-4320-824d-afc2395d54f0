<?php

namespace App\Models\Tenant;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class LoanPayment extends Model
{
    use HasFactory;

    protected $fillable = [
        'employee_loan_id',
        'payroll_record_id',
        'payment_amount',
        'principal_amount',
        'interest_amount',
        'payment_date',
        'due_date',
        'payment_method',
        'status',
        'notes',
        'is_early_payment',
        'is_late_payment',
        'days_late',
        'late_fee',
        'reference_number',
    ];

    protected $casts = [
        'payment_amount' => 'decimal:2',
        'principal_amount' => 'decimal:2',
        'interest_amount' => 'decimal:2',
        'payment_date' => 'date',
        'due_date' => 'date',
        'is_early_payment' => 'boolean',
        'is_late_payment' => 'boolean',
        'days_late' => 'integer',
        'late_fee' => 'decimal:2',
    ];

    const STATUS_SCHEDULED = 'scheduled';
    const STATUS_PAID = 'paid';
    const STATUS_OVERDUE = 'overdue';
    const STATUS_PARTIAL = 'partial';

    const METHOD_SALARY_DEDUCTION = 'salary_deduction';
    const METHOD_CASH = 'cash';
    const METHOD_BANK_TRANSFER = 'bank_transfer';
    const METHOD_OTHER = 'other';

    /**
     * Get the employee loan that owns this payment.
     */
    public function employeeLoan(): BelongsTo
    {
        return $this->belongsTo(EmployeeLoan::class);
    }

    /**
     * Get the payroll record associated with this payment.
     */
    public function payrollRecord(): BelongsTo
    {
        return $this->belongsTo(PayrollRecord::class);
    }

    /**
     * Check if payment is overdue.
     */
    public function isOverdue(): bool
    {
        return $this->status === self::STATUS_SCHEDULED && 
               $this->due_date->isPast();
    }

    /**
     * Check if payment was made early.
     */
    public function isEarly(): bool
    {
        return $this->is_early_payment || 
               ($this->payment_date && $this->payment_date->lt($this->due_date));
    }

    /**
     * Check if payment was made late.
     */
    public function isLate(): bool
    {
        return $this->is_late_payment || 
               ($this->payment_date && $this->payment_date->gt($this->due_date));
    }

    /**
     * Mark payment as paid.
     */
    public function markAsPaid(array $options = []): void
    {
        $paymentDate = $options['payment_date'] ?? now();
        $isLate = $paymentDate->gt($this->due_date);
        $daysLate = $isLate ? $this->due_date->diffInDays($paymentDate) : 0;

        $this->update([
            'status' => self::STATUS_PAID,
            'payment_date' => $paymentDate,
            'is_late_payment' => $isLate,
            'days_late' => $daysLate,
            'late_fee' => $options['late_fee'] ?? 0,
            'reference_number' => $options['reference'] ?? null,
            'notes' => $options['notes'] ?? $this->notes,
        ]);
    }

    /**
     * Calculate late fee based on days late.
     */
    public function calculateLateFee(float $dailyRate = 0.01): float
    {
        if (!$this->isLate()) {
            return 0;
        }

        return $this->payment_amount * ($dailyRate / 100) * $this->days_late;
    }

    /**
     * Get the employee through the loan relationship.
     */
    public function getEmployeeAttribute()
    {
        return $this->employeeLoan->employee;
    }

    /**
     * Scope to get payments by status.
     */
    public function scopeByStatus($query, string $status)
    {
        return $query->where('status', $status);
    }

    /**
     * Scope to get overdue payments.
     */
    public function scopeOverdue($query)
    {
        return $query->where('status', self::STATUS_SCHEDULED)
                    ->where('due_date', '<', now());
    }

    /**
     * Scope to get payments due today.
     */
    public function scopeDueToday($query)
    {
        return $query->where('status', self::STATUS_SCHEDULED)
                    ->whereDate('due_date', now());
    }

    /**
     * Scope to get payments by method.
     */
    public function scopeByMethod($query, string $method)
    {
        return $query->where('payment_method', $method);
    }

    /**
     * Scope to get salary deduction payments.
     */
    public function scopeSalaryDeductions($query)
    {
        return $query->where('payment_method', self::METHOD_SALARY_DEDUCTION);
    }
}
