<?php

namespace App\Models\Tenant;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class CouponUsage extends Model
{
    use HasFactory;

    protected $fillable = [
        'restaurant_id',
        'coupon_id',
        'order_id',
        'customer_id',
        'discount_amount',
        'order_amount_before_discount',
        'order_amount_after_discount',
    ];

    protected $casts = [
        'discount_amount' => 'decimal:2',
        'order_amount_before_discount' => 'decimal:2',
        'order_amount_after_discount' => 'decimal:2',
    ];

    /**
     * Get the restaurant that owns this usage record
     */
    public function restaurant(): BelongsTo
    {
        return $this->belongsTo(Restaurant::class);
    }

    /**
     * Get the coupon that was used
     */
    public function coupon(): BelongsTo
    {
        return $this->belongsTo(Coupon::class);
    }

    /**
     * Get the order where the coupon was used
     */
    public function order(): BelongsTo
    {
        return $this->belongsTo(Order::class);
    }

    /**
     * Get the customer who used the coupon
     */
    public function customer(): BelongsTo
    {
        return $this->belongsTo(Customer::class);
    }

    /**
     * Get the savings percentage
     */
    public function getSavingsPercentageAttribute(): float
    {
        if ($this->order_amount_before_discount <= 0) {
            return 0;
        }

        return ($this->discount_amount / $this->order_amount_before_discount) * 100;
    }

    /**
     * Scope for usage within date range
     */
    public function scopeDateRange($query, $startDate, $endDate)
    {
        return $query->whereBetween('created_at', [$startDate, $endDate]);
    }

    /**
     * Scope for usage by customer
     */
    public function scopeByCustomer($query, $customerId)
    {
        return $query->where('customer_id', $customerId);
    }

    /**
     * Scope for usage by coupon
     */
    public function scopeByCoupon($query, $couponId)
    {
        return $query->where('coupon_id', $couponId);
    }

    /**
     * Scope for recent usage
     */
    public function scopeRecent($query, $days = 30)
    {
        return $query->where('created_at', '>=', now()->subDays($days));
    }
}
