<?php

namespace App\Models\Tenant;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Support\Str;

class TableReservation extends Model
{
    use HasFactory;

    protected $fillable = [
        'restaurant_id',
        'customer_id',
        'table_id',
        'reservation_number',
        'reservation_date',
        'reservation_time',
        'party_size',
        'duration_minutes',
        'customer_name',
        'customer_email',
        'customer_phone',
        'status',
        'confirmed_at',
        'seated_at',
        'completed_at',
        'special_requests',
        'dietary_requirements',
        'occasion',
        'seating_preference',
        'high_chair_needed',
        'wheelchair_accessible',
        'assigned_to',
        'served_by',
        'deposit_amount',
        'deposit_status',
        'deposit_reference',
        'confirmation_sent',
        'reminder_sent',
        'reminder_sent_at',
        'admin_notes',
        'cancellation_reason',
        'rating',
        'feedback',
    ];

    protected $casts = [
        'reservation_date' => 'date',
        'reservation_time' => 'datetime:H:i',
        'dietary_requirements' => 'array',
        'confirmed_at' => 'datetime',
        'seated_at' => 'datetime',
        'completed_at' => 'datetime',
        'deposit_amount' => 'decimal:2',
        'high_chair_needed' => 'boolean',
        'wheelchair_accessible' => 'boolean',
        'confirmation_sent' => 'boolean',
        'reminder_sent' => 'boolean',
        'reminder_sent_at' => 'datetime',
    ];

    /**
     * Boot the model
     */
    protected static function boot()
    {
        parent::boot();

        static::creating(function ($reservation) {
            if (empty($reservation->reservation_number)) {
                $reservation->reservation_number = static::generateReservationNumber();
            }
        });
    }

    /**
     * Generate unique reservation number
     */
    public static function generateReservationNumber(): string
    {
        do {
            $number = 'RES-' . strtoupper(Str::random(8));
        } while (static::where('reservation_number', $number)->exists());

        return $number;
    }

    /**
     * Get the restaurant that owns this reservation
     */
    public function restaurant(): BelongsTo
    {
        return $this->belongsTo(Restaurant::class);
    }

    /**
     * Get the customer who made this reservation
     */
    public function customer(): BelongsTo
    {
        return $this->belongsTo(Customer::class);
    }

    /**
     * Get the reserved table
     */
    public function table(): BelongsTo
    {
        return $this->belongsTo(Table::class);
    }

    /**
     * Get the assigned employee
     */
    public function assignedEmployee(): BelongsTo
    {
        return $this->belongsTo(Employee::class, 'assigned_to');
    }

    /**
     * Get the serving employee
     */
    public function servingEmployee(): BelongsTo
    {
        return $this->belongsTo(Employee::class, 'served_by');
    }

    /**
     * Get the full reservation datetime
     */
    public function getReservationDatetimeAttribute(): \Carbon\Carbon
    {
        return $this->reservation_date->setTimeFromTimeString($this->reservation_time);
    }

    /**
     * Get the end time of the reservation
     */
    public function getReservationEndTimeAttribute(): \Carbon\Carbon
    {
        return $this->reservation_datetime->addMinutes($this->duration_minutes);
    }

    /**
     * Check if reservation is today
     */
    public function isTodayAttribute(): bool
    {
        return $this->reservation_date->isToday();
    }

    /**
     * Check if reservation is in the past
     */
    public function isPastAttribute(): bool
    {
        return $this->reservation_datetime->isPast();
    }

    /**
     * Check if reservation can be cancelled
     */
    public function canBeCancelled(): bool
    {
        return in_array($this->status, ['pending', 'confirmed']) && 
               $this->reservation_datetime->isFuture();
    }

    /**
     * Check if reservation can be modified
     */
    public function canBeModified(): bool
    {
        return in_array($this->status, ['pending', 'confirmed']) && 
               $this->reservation_datetime->subHours(2)->isFuture();
    }

    /**
     * Confirm the reservation
     */
    public function confirm(): void
    {
        $this->update([
            'status' => 'confirmed',
            'confirmed_at' => now(),
        ]);
    }

    /**
     * Mark as seated
     */
    public function markSeated(?Employee $employee = null): void
    {
        $updates = [
            'status' => 'seated',
            'seated_at' => now(),
        ];

        if ($employee) {
            $updates['served_by'] = $employee->id;
        }

        $this->update($updates);
    }

    /**
     * Complete the reservation
     */
    public function complete(): void
    {
        $this->update([
            'status' => 'completed',
            'completed_at' => now(),
        ]);
    }

    /**
     * Cancel the reservation
     */
    public function cancel(string $reason): void
    {
        $this->update([
            'status' => 'cancelled',
            'cancellation_reason' => $reason,
        ]);
    }

    /**
     * Mark as no-show
     */
    public function markNoShow(): void
    {
        $this->update(['status' => 'no_show']);
    }

    /**
     * Send confirmation
     */
    public function sendConfirmation(): void
    {
        $this->update(['confirmation_sent' => true]);
        // TODO: Implement email/SMS sending logic
    }

    /**
     * Send reminder
     */
    public function sendReminder(): void
    {
        $this->update([
            'reminder_sent' => true,
            'reminder_sent_at' => now(),
        ]);
        // TODO: Implement email/SMS sending logic
    }

    /**
     * Scope for reservations by status
     */
    public function scopeByStatus($query, $status)
    {
        return $query->where('status', $status);
    }

    /**
     * Scope for today's reservations
     */
    public function scopeToday($query)
    {
        return $query->whereDate('reservation_date', today());
    }

    /**
     * Scope for upcoming reservations
     */
    public function scopeUpcoming($query)
    {
        return $query->where('reservation_date', '>=', today());
    }

    /**
     * Scope for reservations on a specific date
     */
    public function scopeOnDate($query, $date)
    {
        return $query->whereDate('reservation_date', $date);
    }

    /**
     * Scope for reservations needing reminders
     */
    public function scopeNeedingReminder($query)
    {
        return $query->where('status', 'confirmed')
            ->where('reminder_sent', false)
            ->where('reservation_date', today()->addDay())
            ->whereTime('reservation_time', '>=', now()->addHours(2)->format('H:i'));
    }
}
