<?php

namespace App\Models\Tenant;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use App\Traits\HasMediaLibrary;

class MenuItem extends Model
{
    use HasFactory, HasMediaLibrary;

    protected $fillable = [
        'category_id',
        'subcategory_id',
        'media_id',
        'name',
        'slug',
        'description',
        'image',
        'price',
        'preparation_time', // in minutes
        'calories',
        'ingredients',
        'allergens',
        'nutritional_info',
        'is_vegetarian',
        'is_vegan',
        'is_gluten_free',
        'is_spicy',
        'spice_level', // 1-5
        'is_available',
        'is_featured',
        'is_combo',
        'sort_order',
    ];

    protected $casts = [
        'price' => 'decimal:2',
        'preparation_time' => 'integer',
        'calories' => 'integer',
        'ingredients' => 'array',
        'allergens' => 'array',
        'nutritional_info' => 'array',
        'is_vegetarian' => 'boolean',
        'is_vegan' => 'boolean',
        'is_gluten_free' => 'boolean',
        'is_spicy' => 'boolean',
        'spice_level' => 'integer',
        'is_available' => 'boolean',
        'is_featured' => 'boolean',
        'is_combo' => 'boolean',
        'sort_order' => 'integer',
    ];

    protected $appends = [
        'primary_image_url',
        'image_url',
        'thumbnail_url',
        'effective_price',
    ];

    /**
     * Get the route key for the model.
     */
    public function getRouteKeyName(): string
    {
        return 'slug';
    }



    /**
     * Get the categories this menu item belongs to (many-to-many)
     */
    public function categories(): BelongsToMany
    {
        return $this->belongsToMany(Category::class, 'menu_item_categories')
            ->withTimestamps();
    }

    /**
     * Get the primary category (for backward compatibility)
     * Returns the first category or null
     */
    public function category(): BelongsTo
    {
        return $this->belongsTo(Category::class);
    }

    /**
     * Get the primary category using the many-to-many relationship
     */
    public function primaryCategory()
    {
        return $this->categories()->first();
    }

    /**
     * Get the subcategory this menu item belongs to
     */
    public function subcategory(): BelongsTo
    {
        return $this->belongsTo(Subcategory::class);
    }

    // /**
    //  * Get the primary media associated with this menu item (backward compatibility)
    //  */
    // public function media(): BelongsTo
    // {
    //     return $this->belongsTo(Media::class);
    // }

    /**
     * Get all media associated with this menu item (multiple images)
     */
    public function mediaItems(): BelongsToMany
    {
        return $this->belongsToMany(Media::class, 'menu_item_media')
                    ->withPivot(['sort_order', 'is_primary'])
                    ->withTimestamps()
                    ->orderBy('menu_item_media.sort_order');
    }

    /**
     * Get the primary media item
     */
    public function primaryMedia(): BelongsTo
    {
        return $this->belongsTo(Media::class, 'media_id');
    }

    /**
     * Get all order items for this menu item
     */
    public function orderItems(): HasMany
    {
        return $this->hasMany(OrderItem::class);
    }

    /**
     * Get all variations for this menu item
     */
    public function variations(): HasMany
    {
        return $this->hasMany(MenuItemVariation::class);
    }

    /**
     * Get all branches where this menu item is available
     */
    public function branches(): BelongsToMany
    {
        return $this->belongsToMany(Branch::class, 'menu_item_branches')
            ->withPivot(['is_available', 'branch_specific_price'])
            ->withTimestamps();
    }

    /**
     * Get all addons for this menu item
     */
    public function addons(): BelongsToMany
    {
        return $this->belongsToMany(MenuItemAddon::class, 'menu_item_addon_pivot', 'menu_item_id', 'addon_id')
            ->withTimestamps();
    }

    /**
     * Get all size variations for this menu item
     */
    public function sizes(): HasMany
    {
        return $this->hasMany(MenuItemSize::class);
    }

    /**
     * Get available size variations for this menu item
     */
    public function availableSizes(): HasMany
    {
        return $this->sizes()->available()->ordered();
    }

    /**
     * Get available addons for this menu item
     */
    public function availableAddons(): BelongsToMany
    {
        return $this->addons()->available()->ordered();
    }

    /**
     * Get available variations for this menu item
     */
    public function availableVariations(): HasMany
    {
        return $this->variations()->available()->ordered();
    }

    /**
     * Get required addons for this menu item
     */
    public function requiredAddons(): BelongsToMany
    {
        return $this->addons()->wherePivot('is_required', true);
    }

    /**
     * Get optional addons for this menu item
     */
    public function optionalAddons(): BelongsToMany
    {
        return $this->addons()->wherePivot('is_required', false);
    }

    /**
     * Get combo items that make up this combo (if this item is a combo)
     */
    public function comboItems(): BelongsToMany
    {
        return $this->belongsToMany(MenuItem::class, 'menu_item_combos', 'parent_item_id', 'menu_item_id')
            ->withPivot(['component_type', 'is_required', 'sort_order'])
            ->withTimestamps()
            ->orderBy('menu_item_combos.sort_order');
    }

    /**
     * Get parent combos that include this item (if this item is part of combos)
     */
    public function parentCombos(): BelongsToMany
    {
        return $this->belongsToMany(MenuItem::class, 'menu_item_combos', 'menu_item_id', 'parent_item_id')
            ->withPivot(['component_type', 'is_required', 'sort_order'])
            ->withTimestamps();
    }

    /**
     * Get the image URL (from media or fallback to image column)
     */
    public function getImageUrlAttribute(): ?string
    {
        // Use the primary image URL
        return $this->primary_image_url;
    }

    /**
     * Get the thumbnail URL
     */
    public function getThumbnailUrlAttribute(): ?string
    {
        // Try to get from primaryMedia relationship
        if ($this->relationLoaded('primaryMedia') && $this->primaryMedia) {
            return $this->primaryMedia->thumbnail_url;
        }

        // If relationships not loaded, try to load primaryMedia
        if ($this->media_id) {
            $media = \App\Models\Tenant\Media::find($this->media_id);
            if ($media) {
                return $media->thumbnail_url;
            }
        }

        return $this->primary_image_url;
    }

    /**
     * Get the effective price (considering discount)
     */
    public function getEffectivePriceAttribute(): float
    {
        return $this->discount_price ?? $this->price;
    }

    /**
     * Calculate price with selected variations and addons
     */
    public function calculateTotalPrice(array $selectedVariations = [], array $selectedAddons = []): float
    {
        $totalPrice = $this->effective_price;

        // Add variation price modifiers
        foreach ($selectedVariations as $variationId) {
            $variation = $this->variations()->find($variationId);
            if ($variation) {
                $totalPrice += $variation->price_modifier;
            }
        }

        // Add addon prices
        foreach ($selectedAddons as $addonId => $quantity) {
            $addon = MenuItemAddon::find($addonId);
            if ($addon) {
                $totalPrice += ($addon->price * $quantity);
            }
        }

        return $totalPrice;
    }

    /**
     * Calculate price with new size variations and addons
     */
    public function calculateTotalPriceNew($sizeId = null, array $selectedAddons = []): float
    {
        $totalPrice = $this->effective_price;

        // Add size variation price modifier
        if ($sizeId) {
            $size = $this->sizes()->find($sizeId);
            if ($size) {
                $totalPrice += $size->price_modifier;
            }
        }

        // Add new addon prices
        foreach ($selectedAddons as $addonId => $quantity) {
            $addon = MenuItemAddon::find($addonId);
            if ($addon) {
                $totalPrice += ($addon->price * $quantity);
            }
        }

        return $totalPrice;
    }

    /**
     * Get minimum possible price (base price + cheapest variations)
     */
    public function getMinimumPriceAttribute(): float
    {
        $minPrice = $this->effective_price;

        // Add cheapest variation price modifier (if any negative modifiers exist)
        $cheapestVariation = $this->variations()->available()->orderBy('price_modifier')->first();
        if ($cheapestVariation && $cheapestVariation->price_modifier < 0) {
            $minPrice += $cheapestVariation->price_modifier;
        }

        return max($minPrice, 0); // Ensure price doesn't go below 0
    }

    /**
     * Check if menu item has variations
     */
    public function hasVariations(): bool
    {
        return $this->variations()->available()->exists();
    }

    /**
     * Check if menu item has addons
     */
    public function hasAddons(): bool
    {
        return $this->addons()->available()->exists();
    }

    /**
     * Check if item is on discount
     */
    public function isOnDiscount(): bool
    {
        return $this->discount_price !== null && $this->discount_price < $this->price;
    }

    /**
     * Get discount percentage
     */
    public function getDiscountPercentageAttribute(): ?int
    {
        if (!$this->isOnDiscount()) {
            return null;
        }

        return round((($this->price - $this->discount_price) / $this->price) * 100);
    }

    /**
     * Scope for active items
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope for available items
     */
    public function scopeAvailable($query)
    {
        return $query->where('is_available', true);
    }

    /**
     * Scope for featured items
     */
    public function scopeFeatured($query)
    {
        return $query->where('is_featured', true);
    }

    /**
     * Scope for vegetarian items
     */
    public function scopeVegetarian($query)
    {
        return $query->where('is_vegetarian', true);
    }

    /**
     * Scope for vegan items
     */
    public function scopeVegan($query)
    {
        return $query->where('is_vegan', true);
    }

    /**
     * Scope for ordered items
     */
    public function scopeOrdered($query)
    {
        return $query->orderBy('sort_order');
    }

    /**
     * Scope for combo items
     */
    public function scopeCombo($query)
    {
        return $query->where('is_combo', true);
    }

    /**
     * Scope for non-combo items
     */
    public function scopeRegular($query)
    {
        return $query->where('is_combo', false);
    }

    /**
     * Scope for items available in a specific branch
     */
    public function scopeAvailableInBranch($query, $branchId)
    {
        return $query->whereHas('branches', function ($q) use ($branchId) {
            $q->where('branch_id', $branchId)
              ->where('menu_item_branches.is_available', true);
        });
    }

    /**
     * Check if menu item is available in a specific branch
     */
    public function isAvailableInBranch($branchId): bool
    {
        return $this->branches()
            ->where('branch_id', $branchId)
            ->where('menu_item_branches.is_available', true)
            ->exists();
    }

    /**
     * Get price for a specific branch (branch-specific or default)
     */
    public function getPriceForBranch($branchId): float
    {
        $branchPivot = $this->branches()
            ->where('branch_id', $branchId)
            ->first();

        if ($branchPivot && $branchPivot->pivot->branch_specific_price) {
            return $branchPivot->pivot->branch_specific_price;
        }

        return $this->effective_price;
    }

    /**
     * Sync menu item with branches
     */
    public function syncBranches(array $branchIds, array $branchData = [])
    {
        $syncData = [];
        foreach ($branchIds as $branchId) {
            $syncData[$branchId] = [
                'is_available' => $branchData[$branchId]['is_available'] ?? true,
                'branch_specific_price' => $branchData[$branchId]['branch_specific_price'] ?? null,
            ];
        }

        $this->branches()->sync($syncData);
    }

    /**
     * Add media to menu item
     */
    public function addMedia($mediaId, $isPrimary = false, $sortOrder = null)
    {
        // If this is primary, unset other primary media
        if ($isPrimary) {
            $this->mediaItems()->updateExistingPivot($this->mediaItems()->pluck('media.id'), ['is_primary' => false]);
        }

        // Get next sort order if not provided
        if ($sortOrder === null) {
            $sortOrder = $this->mediaItems()->max('menu_item_media.sort_order') + 1;
        }

        $this->mediaItems()->attach($mediaId, [
            'is_primary' => $isPrimary,
            'sort_order' => $sortOrder,
        ]);
    }

    /**
     * Sync media items (replace all)
     */
    public function syncMedia(array $mediaIds)
    {
        $syncData = [];
        foreach ($mediaIds as $index => $mediaId) {
            $syncData[$mediaId] = [
                'sort_order' => $index,
                'is_primary' => $index === 0, // First image is primary
            ];
        }

        $this->mediaItems()->sync($syncData);
    }

    /**
     * Get all image URLs
     */
    public function getImageUrlsAttribute(): array
    {
        return $this->mediaItems->map(function ($media) {
            return $media->url;
        })->toArray();
    }

    /**
     * Get primary image URL (enhanced)
     */
    public function getPrimaryImageUrlAttribute(): ?string
    {
        // Try to get primary from media items (loaded relationship)
        if ($this->relationLoaded('mediaItems')) {
            $primaryMedia = $this->mediaItems->where('pivot.is_primary', true)->first();
            if ($primaryMedia) {
                return $primaryMedia->url;
            }

            // Fallback to first media item
            $firstMedia = $this->mediaItems->first();
            if ($firstMedia) {
                return $firstMedia->url;
            }
        }

        // Try to get from primaryMedia relationship
        if ($this->relationLoaded('primaryMedia') && $this->primaryMedia) {
            return $this->primaryMedia->url;
        }

        // Fallback to legacy media relationship
        if ($this->relationLoaded('media') && $this->media) {
            return $this->media->url;
        }

        // If relationships not loaded, try to load primaryMedia
        if ($this->media_id) {
            $media = \App\Models\Tenant\Media::find($this->media_id);
            if ($media) {
                return $media->url;
            }
        }

        return null;
    }

    /**
     * Get the total price of individual items in this combo
     */
    public function getIndividualTotalAttribute(): float
    {
        if (!$this->is_combo) {
            return $this->price;
        }

        return $this->comboItems->sum('price');
    }

    /**
     * Get the savings amount when buying as combo
     */
    public function getSavingsAttribute(): float
    {
        if (!$this->is_combo) {
            return 0;
        }

        return max(0, $this->individual_total - $this->price);
    }

    /**
     * Check if this item is a combo
     */
    public function isCombo(): bool
    {
        return $this->is_combo;
    }

    /**
     * Check if this item is part of any combos
     */
    public function isPartOfCombos(): bool
    {
        return $this->parentCombos()->exists();
    }

    /**
     * Add an item to this combo
     */
    public function addComboItem($menuItemId, $componentType, $isRequired = false, $sortOrder = null)
    {
        if (!$this->is_combo) {
            throw new \Exception('This menu item is not a combo');
        }

        // Get next sort order if not provided
        if ($sortOrder === null) {
            $sortOrder = $this->comboItems()->max('menu_item_combos.sort_order') + 1;
        }

        $this->comboItems()->attach($menuItemId, [
            'component_type' => $componentType,
            'is_required' => $isRequired,
            'sort_order' => $sortOrder,
        ]);
    }

    /**
     * Sync combo items (replace all)
     */
    public function syncComboItems(array $items)
    {
        if (!$this->is_combo) {
            throw new \Exception('This menu item is not a combo');
        }

        $syncData = [];
        foreach ($items as $index => $item) {
            $syncData[$item['menu_item_id']] = [
                'component_type' => $item['component_type'],
                'is_required' => $item['is_required'] ?? false,
                'sort_order' => $item['sort_order'] ?? $index,
            ];
        }

        $this->comboItems()->sync($syncData);
    }

    /**
     * Get combo items grouped by component type
     */
    public function getComboItemsByTypeAttribute(): array
    {
        if (!$this->is_combo) {
            return [];
        }

        return $this->comboItems->groupBy('pivot.component_type')->toArray();
    }
}
