<?php

namespace App\Models\Tenant;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\MorphTo;

class StockMovement extends Model
{
    use HasFactory;

    protected $fillable = [
        'restaurant_id',
        'inventory_item_id',
        'movement_type',
        'quantity',
        'unit_cost',
        'total_cost',
        'reason',
        'reference_type',
        'reference_id',
        'batch_number',
        'performed_by',
        'notes',
        'movement_date',
    ];

    protected $casts = [
        'quantity' => 'decimal:2',
        'unit_cost' => 'decimal:4',
        'total_cost' => 'decimal:2',
        'movement_date' => 'datetime',
    ];

    /**
     * Get the restaurant that owns this stock movement
     */
    public function restaurant(): BelongsTo
    {
        return $this->belongsTo(Restaurant::class);
    }

    /**
     * Get the inventory item
     */
    public function inventoryItem(): BelongsTo
    {
        return $this->belongsTo(InventoryItem::class);
    }

    /**
     * Get the user who performed this movement
     */
    public function performedBy(): BelongsTo
    {
        return $this->belongsTo(\App\Models\User::class, 'performed_by');
    }

    /**
     * Get the reference model (polymorphic)
     */
    public function reference(): MorphTo
    {
        return $this->morphTo();
    }

    /**
     * Get movement type label
     */
    public function getMovementTypeLabelAttribute(): string
    {
        switch ($this->movement_type) {
            case 'in':
                return 'Stock In';
            case 'out':
                return 'Stock Out';
            case 'adjustment':
                return 'Adjustment';
            case 'transfer':
                return 'Transfer';
            case 'waste':
                return 'Waste';
            default:
                return ucfirst($this->movement_type);
        }
    }

    /**
     * Get reason label
     */
    public function getReasonLabelAttribute(): string
    {
        switch ($this->reason) {
            case 'purchase':
                return 'Purchase';
            case 'usage':
                return 'Usage';
            case 'sale':
                return 'Sale';
            case 'waste':
                return 'Waste';
            case 'expired':
                return 'Expired';
            case 'damaged':
                return 'Damaged';
            case 'theft':
                return 'Theft';
            case 'adjustment':
                return 'Stock Adjustment';
            case 'transfer_in':
                return 'Transfer In';
            case 'transfer_out':
                return 'Transfer Out';
            case 'return':
                return 'Return';
            case 'sample':
                return 'Sample';
            case 'promotion':
                return 'Promotion';
            default:
                return ucfirst($this->reason);
        }
    }

    /**
     * Get impact on stock (positive or negative)
     */
    public function getStockImpactAttribute(): float
    {
        return $this->movement_type === 'in' ? $this->quantity : -$this->quantity;
    }

    /**
     * Scope for stock in movements
     */
    public function scopeStockIn($query)
    {
        return $query->where('movement_type', 'in');
    }

    /**
     * Scope for stock out movements
     */
    public function scopeStockOut($query)
    {
        return $query->where('movement_type', 'out');
    }

    /**
     * Scope for adjustments
     */
    public function scopeAdjustments($query)
    {
        return $query->where('movement_type', 'adjustment');
    }

    /**
     * Scope for waste movements
     */
    public function scopeWaste($query)
    {
        return $query->where('reason', 'waste');
    }

    /**
     * Scope for today's movements
     */
    public function scopeToday($query)
    {
        return $query->whereDate('created_at', today());
    }

    /**
     * Scope by date range
     */
    public function scopeDateRange($query, $startDate, $endDate)
    {
        return $query->whereBetween('movement_date', [$startDate, $endDate]);
    }

    /**
     * Boot the model
     */
    protected static function boot()
    {
        parent::boot();

        static::creating(function ($movement) {
            if (is_null($movement->movement_date)) {
                $movement->movement_date = now();
            }
        });
    }
}
