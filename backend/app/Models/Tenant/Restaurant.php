<?php

namespace App\Models\Tenant;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;
use App\Traits\HasMediaLibrary;

class Restaurant extends Model
{
    use HasFactory, HasMediaLibrary;

    protected $fillable = [
        'name',
        'slug',
        'description',
        'email',
        'phone',
        'address',
        'city',
        'state',
        'country',
        'postal_code',
        'latitude',
        'longitude',
        'logo',
        'cover_image',
        'theme_color',
        'currency',
        'timezone',
        'language',
        'tax_rate',
        'tax_enabled',
        'tax_name',
        'vat_enabled',
        'vat_rate',
        'vat_number',
        'service_charge',
        'service_charge_enabled',
        'service_charge_name',
        'delivery_charge',
        'minimum_order_amount',
        'delivery_radius',
        'opening_time',
        'closing_time',
        'is_open',
        'is_delivery_enabled',
        'is_takeaway_enabled',
        'is_dine_in_enabled',
        'social_facebook',
        'social_instagram',
        'social_twitter',
        'social_youtube',
        'meta_title',
        'meta_description',
        'meta_keywords',
    ];

    protected $casts = [
        'latitude' => 'decimal:8',
        'longitude' => 'decimal:8',
        'tax_rate' => 'decimal:2',
        'tax_enabled' => 'boolean',
        'vat_enabled' => 'boolean',
        'vat_rate' => 'decimal:2',
        'service_charge' => 'decimal:2',
        'service_charge_enabled' => 'boolean',
        'delivery_charge' => 'decimal:2',
        'minimum_order_amount' => 'decimal:2',
        'delivery_radius' => 'decimal:2',
        'opening_time' => 'datetime:H:i',
        'closing_time' => 'datetime:H:i',
        'is_open' => 'boolean',
        'is_delivery_enabled' => 'boolean',
        'is_takeaway_enabled' => 'boolean',
        'is_dine_in_enabled' => 'boolean',
    ];

    /**
     * Get all categories for this restaurant
     */
    public function categories(): HasMany
    {
        return $this->hasMany(Category::class);
    }

    /**
     * Get all menu items for this restaurant
     */
    public function menuItems(): HasMany
    {
        return $this->hasMany(MenuItem::class);
    }

    /**
     * Get all tables for this restaurant
     */
    public function tables(): HasMany
    {
        return $this->hasMany(Table::class);
    }

    /**
     * Get all orders for this restaurant
     */
    public function orders(): HasMany
    {
        return $this->hasMany(Order::class);
    }

    /**
     * Get all staff for this restaurant
     */
    public function staff(): HasMany
    {
        return $this->hasMany(Staff::class);
    }

    /**
     * Get all restaurant reviews
     */
    public function restaurantReviews(): HasMany
    {
        return $this->hasMany(RestaurantReview::class);
    }

    /**
     * Get all food reviews for this restaurant
     */
    public function foodReviews(): HasMany
    {
        return $this->hasMany(FoodReview::class);
    }

    /**
     * Check if restaurant is currently open
     */
    public function isCurrentlyOpen(): bool
    {
        if (!$this->is_open) {
            return false;
        }

        $now = now()->format('H:i');
        $opening = $this->opening_time->format('H:i');
        $closing = $this->closing_time->format('H:i');

        return $now >= $opening && $now <= $closing;
    }

    /**
     * Get formatted address
     */
    public function getFullAddressAttribute(): string
    {
        $parts = array_filter([
            $this->address,
            $this->city,
            $this->state,
            $this->postal_code,
            $this->country,
        ]);

        return implode(', ', $parts);
    }

    /**
     * Update restaurant rating based on reviews
     */
    public function updateRating(): void
    {
        $avgRating = $this->restaurantReviews()->where('status', 'approved')->avg('overall_rating');
        $reviewCount = $this->restaurantReviews()->where('status', 'approved')->count();

        // You might want to add average_rating and review_count columns to restaurants table
        // For now, this method exists to prevent errors in the review models
    }

    /**
     * Calculate tax amount for a given subtotal
     */
    public function calculateTax(float $subtotal): float
    {
        if (!$this->tax_enabled) {
            return 0;
        }

        return round($subtotal * ($this->tax_rate / 100), 2);
    }

    /**
     * Calculate VAT amount for a given subtotal
     */
    public function calculateVat(float $subtotal): float
    {
        if (!$this->vat_enabled) {
            return 0;
        }

        return round($subtotal * ($this->vat_rate / 100), 2);
    }

    /**
     * Calculate service charge amount for a given subtotal
     */
    public function calculateServiceCharge(float $subtotal): float
    {
        if (!$this->service_charge_enabled) {
            return 0;
        }

        return round($subtotal * ($this->service_charge / 100), 2);
    }

    /**
     * Calculate total amount including all taxes and charges
     */
    public function calculateTotal(float $subtotal): array
    {
        $tax = $this->calculateTax($subtotal);
        $vat = $this->calculateVat($subtotal);
        $serviceCharge = $this->calculateServiceCharge($subtotal);
        $total = $subtotal + $tax + $vat + $serviceCharge;

        return [
            'subtotal' => round($subtotal, 2),
            'tax' => $tax,
            'tax_name' => $this->tax_name,
            'vat' => $vat,
            'vat_rate' => $this->vat_rate,
            'service_charge' => $serviceCharge,
            'service_charge_name' => $this->service_charge_name,
            'total' => round($total, 2),
        ];
    }
}
