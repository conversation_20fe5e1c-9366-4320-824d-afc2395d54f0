<?php

namespace App\Models\Tenant;

use Illuminate\Contracts\Auth\MustVerifyEmail;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use <PERSON><PERSON>\Fortify\TwoFactorAuthenticatable;
use <PERSON><PERSON>\Jetstream\HasProfilePhoto;
use Lara<PERSON>\Sanctum\HasApiTokens;
use App\Traits\HasBranchAccess;

class User extends Authenticatable implements MustVerifyEmail
{
    use HasApiTokens;
    use HasFactory;
    use HasProfilePhoto;
    use Notifiable;
    use TwoFactorAuthenticatable;
    use HasBranchAccess;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'name',
        'email',
        'email_verified_at',
        'password',
        'phone',
        'role',
        'department',
        'position',
        'hire_date',
        'salary',
        'hourly_rate',
        'address',
        'emergency_contact_name',
        'emergency_contact_phone',
        'notes',
        'preferred_language',
        'theme_preference',
        'is_active',
        'is_on_shift',
        'last_login_at',
    ];

    /**
     * The attributes that should be hidden for serialization.
     *
     * @var array<int, string>
     */
    protected $hidden = [
        'password',
        'remember_token',
        'two_factor_recovery_codes',
        'two_factor_secret',
    ];

    /**
     * The accessors to append to the model's array form.
     *
     * @var array<int, string>
     */
    protected $appends = [
        'profile_photo_url',
    ];

    /**
     * Get the attributes that should be cast.
     *
     * @return array<string, string>
     */
    protected function casts(): array
    {
        return [
            'email_verified_at' => 'datetime',
            'password' => 'hashed',
            'hire_date' => 'date',
            'salary' => 'decimal:2',
            'hourly_rate' => 'decimal:2',
            'is_active' => 'boolean',
            'is_on_shift' => 'boolean',
            'last_login_at' => 'datetime',
        ];
    }

    /**
     * Get the user's primary role
     */
    public function getPrimaryRoleAttribute(): ?string
    {
        return $this->role;
    }

    /**
     * Check if user has manager role
     */
    public function isManager(): bool
    {
        return $this->role === 'manager';
    }

    /**
     * Check if user has waiter role
     */
    public function isWaiter(): bool
    {
        return $this->role === 'waiter';
    }

    /**
     * Check if user has chef role
     */
    public function isChef(): bool
    {
        return $this->role === 'chef';
    }

    /**
     * Check if user has rider role
     */
    public function isRider(): bool
    {
        return $this->role === 'rider';
    }

    /**
     * Check if user has any of the specified roles
     */
    public function hasAnyRole(array $roles): bool
    {
        return in_array($this->role, $roles);
    }

    /**
     * Check if user has specific role
     */
    public function hasRole( $role): bool
    {
        return $this->role === $role;
    }

    /**
     * Update last login timestamp
     */
    public function updateLastLogin(): void
    {
        $this->update(['last_login_at' => now()]);
    }

    /**
     * Get user's dashboard route based on their primary role
     */
    public function getDashboardRoute(): string
    {
        switch ($this->role) {
            case 'manager':
            case 'restaurant_manager':
                return route('manager.dashboard');
            case 'waiter':
                return route('waiter.dashboard');
            case 'chef':
            case 'kitchen':
                return route('kitchen.dashboard');
            case 'rider':
            case 'delivery':
                return route('delivery.dashboard');
            default:
                return route('dashboard');
        }
    }

    /**
     * Get the user's employee record in the current tenant
     */
    public function employee()
    {
        return $this->hasOne(Employee::class);
    }

    /**
     * Get branches accessible to this user (via HasBranchAccess trait)
     */
    public function branches()
    {
        return $this->getAccessibleBranches();
    }

    /**
     * Get branches where this user is a manager
     */
    public function managedBranches()
    {
        $employee = $this->employee;
        if (!$employee) {
            return new \Illuminate\Database\Eloquent\Collection();
        }

        return Branch::where('manager_id', $employee->id)->get();
    }
}
