<?php

namespace App\Models\Tenant;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Spatie\MediaLibrary\HasMedia;
use Spatie\MediaLibrary\InteractsWithMedia;

class PaymentHistory extends Model implements HasMedia
{
    use HasFactory, InteractsWithMedia;

    protected $table = 'payment_history';

    protected $fillable = [
        'branch_id',
        'purchase_order_id',
        'expense_id',
        'supplier_id',
        'created_by',
        'payment_number',
        'amount',
        'payment_method',
        'payment_date',
        'reference_number',
        'status',
        'notes',
        'receipt_image',
        'metadata',
    ];

    protected $casts = [
        'amount' => 'decimal:2',
        'payment_date' => 'date',
        'metadata' => 'array',
    ];

    /**
     * Get the branch that owns this payment
     */
    public function branch(): BelongsTo
    {
        return $this->belongsTo(Branch::class);
    }

    /**
     * Get the purchase order this payment is for
     */
    public function purchaseOrder(): BelongsTo
    {
        return $this->belongsTo(PurchaseOrder::class);
    }

    /**
     * Get the expense this payment is for
     */
    public function expense(): BelongsTo
    {
        return $this->belongsTo(Expense::class);
    }

    /**
     * Get the supplier this payment is for
     */
    public function supplier(): BelongsTo
    {
        return $this->belongsTo(Vendor::class, 'supplier_id');
    }

    /**
     * Get the user who created this payment
     */
    public function creator(): BelongsTo
    {
        return $this->belongsTo(\App\Models\User::class, 'created_by');
    }

    /**
     * Get formatted payment method
     */
    public function getFormattedPaymentMethodAttribute(): string
    {
        return match ($this->payment_method) {
            'cash' => 'Cash',
            'bank_transfer' => 'Bank Transfer',
            'check' => 'Check',
            'credit_card' => 'Credit Card',
            'debit_card' => 'Debit Card',
            'mobile_payment' => 'Mobile Payment',
            'other' => 'Other',
            default => ucfirst(str_replace('_', ' ', $this->payment_method)),
        };
    }

    /**
     * Get status badge color
     */
    public function getStatusColorAttribute(): string
    {
        return match ($this->status) {
            'pending' => '#F59E0B', // yellow
            'completed' => '#10B981', // green
            'failed' => '#EF4444', // red
            'cancelled' => '#6B7280', // gray
            default => '#6B7280',
        };
    }

    /**
     * Get payment type (PO or Expense)
     */
    public function getPaymentTypeAttribute(): string
    {
        if ($this->purchase_order_id) {
            return 'Purchase Order';
        } elseif ($this->expense_id) {
            return 'Expense';
        }
        return 'General';
    }

    /**
     * Get related document number
     */
    public function getDocumentNumberAttribute(): ?string
    {
        if ($this->purchase_order_id && $this->purchaseOrder) {
            return $this->purchaseOrder->po_number;
        } elseif ($this->expense_id && $this->expense) {
            return $this->expense->expense_number;
        }
        return null;
    }

    /**
     * Generate unique payment number
     */
    public static function generatePaymentNumber(): string
    {
        do {
            $number = 'PAY-' . now()->format('Ymd') . '-' . str_pad(random_int(1, 9999), 4, '0', STR_PAD_LEFT);
        } while (static::where('payment_number', $number)->exists());

        return $number;
    }

    /**
     * Scope for completed payments
     */
    public function scopeCompleted($query)
    {
        return $query->where('status', 'completed');
    }

    /**
     * Scope for pending payments
     */
    public function scopePending($query)
    {
        return $query->where('status', 'pending');
    }

    /**
     * Scope for failed payments
     */
    public function scopeFailed($query)
    {
        return $query->where('status', 'failed');
    }

    /**
     * Scope for payments by date range
     */
    public function scopeDateRange($query, $startDate, $endDate)
    {
        return $query->whereBetween('payment_date', [$startDate, $endDate]);
    }

    /**
     * Scope for payments by supplier
     */
    public function scopeBySupplier($query, $supplierId)
    {
        return $query->where('supplier_id', $supplierId);
    }

    /**
     * Scope for purchase order payments
     */
    public function scopePurchaseOrderPayments($query)
    {
        return $query->whereNotNull('purchase_order_id');
    }

    /**
     * Scope for expense payments
     */
    public function scopeExpensePayments($query)
    {
        return $query->whereNotNull('expense_id');
    }

    /**
     * Boot the model
     */
    protected static function boot()
    {
        parent::boot();

        static::creating(function ($payment) {
            if (empty($payment->payment_number)) {
                $payment->payment_number = static::generatePaymentNumber();
            }
        });
    }
}
