<?php

namespace App\Models\Tenant;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class Promotion extends Model
{
    use HasFactory;

    protected $fillable = [
        'restaurant_id',
        'name',
        'description',
        'banner_image',
        'thumbnail_image',
        'type',
        'discount_type',
        'discount_value',
        'maximum_discount',
        'starts_at',
        'ends_at',
        'applicable_days',
        'applicable_from',
        'applicable_until',
        'applicable_food_ids',
        'applicable_category_ids',
        'combo_items',
        'minimum_order_amount',
        'minimum_items',
        'maximum_uses',
        'maximum_uses_per_customer',
        'current_uses',
        'target_audience',
        'eligible_customer_tiers',
        'is_featured',
        'show_on_homepage',
        'show_in_menu',
        'sort_order',
        'is_active',
        'auto_activate',
        'auto_deactivate',
        'view_count',
        'click_count',
        'conversion_count',
        'total_discount_given',
        'total_revenue_generated',
    ];

    protected $casts = [
        'discount_value' => 'decimal:2',
        'maximum_discount' => 'decimal:2',
        'starts_at' => 'datetime',
        'ends_at' => 'datetime',
        'applicable_days' => 'array',
        'applicable_from' => 'datetime:H:i',
        'applicable_until' => 'datetime:H:i',
        'applicable_food_ids' => 'array',
        'applicable_category_ids' => 'array',
        'combo_items' => 'array',
        'minimum_order_amount' => 'decimal:2',
        'eligible_customer_tiers' => 'array',
        'is_featured' => 'boolean',
        'show_on_homepage' => 'boolean',
        'show_in_menu' => 'boolean',
        'is_active' => 'boolean',
        'auto_activate' => 'boolean',
        'auto_deactivate' => 'boolean',
        'total_discount_given' => 'decimal:2',
        'total_revenue_generated' => 'decimal:2',
    ];

    /**
     * Get the restaurant that owns this promotion
     */
    public function restaurant(): BelongsTo
    {
        return $this->belongsTo(Restaurant::class);
    }

    /**
     * Check if promotion is currently active
     */
    public function isCurrentlyActive(): bool
    {
        if (!$this->is_active) {
            return false;
        }

        $now = now();

        // Check date range
        if ($now < $this->starts_at || $now > $this->ends_at) {
            return false;
        }

        // Check day availability
        if ($this->applicable_days && !in_array($now->dayOfWeek, $this->applicable_days)) {
            return false;
        }

        // Check time availability
        if ($this->applicable_from && $this->applicable_until) {
            $currentTime = $now->format('H:i');
            if ($currentTime < $this->applicable_from || $currentTime > $this->applicable_until) {
                return false;
            }
        }

        // Check usage limits
        if ($this->maximum_uses && $this->current_uses >= $this->maximum_uses) {
            return false;
        }

        return true;
    }

    /**
     * Check if promotion is valid for a customer
     */
    public function isValidForCustomer(?Customer $customer): bool
    {
        if (!$this->isCurrentlyActive()) {
            return false;
        }

        // Check target audience
        switch ($this->target_audience) {
            case 'new_customers':
                if (!$customer || $customer->total_orders > 0) {
                    return false;
                }
                break;
            case 'loyal_customers':
                if (!$customer || $customer->total_orders < 5) {
                    return false;
                }
                break;
            case 'birthday_customers':
                if (!$customer || !$customer->date_of_birth || 
                    !$customer->date_of_birth->isBirthday()) {
                    return false;
                }
                break;
        }

        // Check customer tier eligibility
        if ($customer && $this->eligible_customer_tiers && 
            !in_array($customer->customer_tier, $this->eligible_customer_tiers)) {
            return false;
        }

        return true;
    }

    /**
     * Calculate discount for an order
     */
    public function calculateDiscount(Order $order): float
    {
        if (!$this->isValidForCustomer($order->customer)) {
            return 0;
        }

        // Check minimum order amount
        if ($order->subtotal < $this->minimum_order_amount) {
            return 0;
        }

        // Check minimum items
        if ($order->total_items < $this->minimum_items) {
            return 0;
        }

        switch ($this->discount_type) {
            case 'percentage':
                $discount = ($order->subtotal * $this->discount_value) / 100;
                return $this->maximum_discount ? min($discount, $this->maximum_discount) : $discount;

            case 'fixed_amount':
                return min($this->discount_value, $order->subtotal);

            case 'buy_x_get_y':
                return $this->calculateBuyXGetYDiscount($order);

            case 'free_item':
                return $this->calculateFreeItemDiscount($order);

            default:
                return 0;
        }
    }

    /**
     * Calculate buy X get Y discount
     */
    protected function calculateBuyXGetYDiscount(Order $order): float
    {
        // Implementation depends on specific combo_items structure
        // This is a simplified version
        return 0;
    }

    /**
     * Calculate free item discount
     */
    protected function calculateFreeItemDiscount(Order $order): float
    {
        // Implementation depends on specific applicable_food_ids
        // This is a simplified version
        return 0;
    }

    /**
     * Increment view count
     */
    public function incrementViewCount(): void
    {
        $this->increment('view_count');
    }

    /**
     * Increment click count
     */
    public function incrementClickCount(): void
    {
        $this->increment('click_count');
    }

    /**
     * Increment conversion count
     */
    public function incrementConversionCount(float $discountAmount, float $orderAmount): void
    {
        $this->increment('conversion_count');
        $this->increment('current_uses');
        $this->increment('total_discount_given', $discountAmount);
        $this->increment('total_revenue_generated', $orderAmount);
    }

    /**
     * Get conversion rate
     */
    public function getConversionRateAttribute(): float
    {
        if ($this->click_count === 0) {
            return 0;
        }

        return ($this->conversion_count / $this->click_count) * 100;
    }

    /**
     * Get type label
     */
    public function getTypeLabelAttribute(): string
    {
        return match ($this->type) {
            'happy_hour' => 'Happy Hour',
            'daily_special' => 'Daily Special',
            'combo_deal' => 'Combo Deal',
            'loyalty_reward' => 'Loyalty Reward',
            'seasonal_offer' => 'Seasonal Offer',
            'flash_sale' => 'Flash Sale',
            'group_discount' => 'Group Discount',
            'birthday_special' => 'Birthday Special',
            default => ucfirst($this->type),
        };
    }

    /**
     * Get status label
     */
    public function getStatusLabelAttribute(): string
    {
        if (!$this->is_active) {
            return 'Inactive';
        }

        $now = now();

        if ($now < $this->starts_at) {
            return 'Scheduled';
        }

        if ($now > $this->ends_at) {
            return 'Expired';
        }

        if ($this->maximum_uses && $this->current_uses >= $this->maximum_uses) {
            return 'Limit Reached';
        }

        return 'Active';
    }

    /**
     * Scope for active promotions
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope for current promotions (within date range)
     */
    public function scopeCurrent($query)
    {
        $now = now();
        return $query->where('starts_at', '<=', $now)
            ->where('ends_at', '>=', $now);
    }

    /**
     * Scope for featured promotions
     */
    public function scopeFeatured($query)
    {
        return $query->where('is_featured', true);
    }

    /**
     * Scope for homepage promotions
     */
    public function scopeHomepage($query)
    {
        return $query->where('show_on_homepage', true);
    }

    /**
     * Scope for menu promotions
     */
    public function scopeMenu($query)
    {
        return $query->where('show_in_menu', true);
    }

    /**
     * Scope for promotions by type
     */
    public function scopeByType($query, $type)
    {
        return $query->where('type', $type);
    }

    /**
     * Scope for ordering by sort order
     */
    public function scopeOrdered($query)
    {
        return $query->orderBy('sort_order')->orderBy('name');
    }
}
