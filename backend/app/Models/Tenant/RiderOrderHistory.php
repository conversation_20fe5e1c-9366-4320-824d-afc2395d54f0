<?php

namespace App\Models\Tenant;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class RiderOrderHistory extends Model
{
    use HasFactory;

    protected $table = 'rider_order_history';

    protected $fillable = [
        'rider_id',
        'order_id',
        'action',
        'action_time',
        'latitude',
        'longitude',
        'notes',
        'metadata',
    ];

    protected $casts = [
        'action_time' => 'datetime',
        'latitude' => 'decimal:8',
        'longitude' => 'decimal:8',
        'metadata' => 'array',
    ];

    /**
     * Get the rider that performed this action
     */
    public function rider(): BelongsTo
    {
        return $this->belongsTo(Rider::class);
    }

    /**
     * Get the order this action was performed on
     */
    public function order(): BelongsTo
    {
        return $this->belongsTo(Order::class);
    }

    /**
     * Get formatted action name
     */
    public function getFormattedActionAttribute(): string
    {
        return match ($this->action) {
            'assigned' => 'Order Assigned',
            'accepted' => 'Order Accepted',
            'picked_up' => 'Order Picked Up',
            'en_route' => 'En Route to Customer',
            'delivered' => 'Order Delivered',
            'cancelled' => 'Order Cancelled',
            'failed' => 'Delivery Failed',
            default => ucfirst(str_replace('_', ' ', $this->action)),
        };
    }

    /**
     * Get action icon
     */
    public function getActionIconAttribute(): string
    {
        return match ($this->action) {
            'assigned' => 'fas fa-user-plus text-blue-500',
            'accepted' => 'fas fa-check text-green-500',
            'picked_up' => 'fas fa-box text-orange-500',
            'en_route' => 'fas fa-truck text-purple-500',
            'delivered' => 'fas fa-check-circle text-green-600',
            'cancelled' => 'fas fa-times-circle text-red-500',
            'failed' => 'fas fa-exclamation-triangle text-red-600',
            default => 'fas fa-circle text-gray-400',
        };
    }

    /**
     * Scope for specific action
     */
    public function scopeAction($query, string $action)
    {
        return $query->where('action', $action);
    }

    /**
     * Scope for date range
     */
    public function scopeDateRange($query, $startDate, $endDate)
    {
        return $query->whereBetween('action_time', [$startDate, $endDate]);
    }

    /**
     * Scope for today's actions
     */
    public function scopeToday($query)
    {
        return $query->whereDate('action_time', today());
    }
}
