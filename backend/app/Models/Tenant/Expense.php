<?php

namespace App\Models\Tenant;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use App\Traits\HasMediaLibrary;

class Expense extends Model
{
    use HasFactory, HasMediaLibrary;

    protected $fillable = [
        'restaurant_id',
        'expense_category_id',
        'vendor_id',
        'created_by',
        'approved_by',
        'expense_number',
        'title',
        'description',
        'amount',
        'tax_amount',
        'total_amount',
        'expense_date',
        'due_date',
        'paid_at',
        'payment_method',
        'payment_reference',
        'status',
        'priority',
        'is_recurring',
        'recurring_frequency',
        'recurring_until',
        'invoice_number',
        'receipt_number',
        'notes',
        'tags',
    ];

    protected $casts = [
        'amount' => 'decimal:2',
        'tax_amount' => 'decimal:2',
        'total_amount' => 'decimal:2',
        'expense_date' => 'date',
        'due_date' => 'date',
        'paid_at' => 'datetime',
        'is_recurring' => 'boolean',
        'recurring_until' => 'date',
        'tags' => 'array',
    ];

    /**
     * Get the restaurant that owns this expense
     */
    public function restaurant(): BelongsTo
    {
        return $this->belongsTo(Restaurant::class);
    }

    /**
     * Get the expense category
     */
    public function category(): BelongsTo
    {
        return $this->belongsTo(ExpenseCategory::class, 'expense_category_id');
    }

    /**
     * Get the vendor
     */
    public function vendor(): BelongsTo
    {
        return $this->belongsTo(Vendor::class);
    }

    /**
     * Get the user who created this expense
     */
    public function creator(): BelongsTo
    {
        return $this->belongsTo(\App\Models\User::class, 'created_by');
    }

    /**
     * Get the user who approved this expense
     */
    public function approver(): BelongsTo
    {
        return $this->belongsTo(\App\Models\User::class, 'approved_by');
    }

    /**
     * Get expense line items
     */
    public function lineItems(): HasMany
    {
        return $this->hasMany(ExpenseLineItem::class);
    }

    /**
     * Get expense approvals/workflow
     */
    public function approvals(): HasMany
    {
        return $this->hasMany(ExpenseApproval::class);
    }

    /**
     * Check if expense is pending
     */
    public function isPending(): bool
    {
        return $this->status === 'pending';
    }

    /**
     * Check if expense is approved
     */
    public function isApproved(): bool
    {
        return $this->status === 'approved';
    }

    /**
     * Check if expense is paid
     */
    public function isPaid(): bool
    {
        return $this->status === 'paid' || !is_null($this->paid_at);
    }

    /**
     * Check if expense is overdue
     */
    public function isOverdue(): bool
    {
        return $this->status === 'approved' && 
               !$this->isPaid() && 
               $this->due_date && 
               $this->due_date->isPast();
    }

    /**
     * Get days until due
     */
    public function getDaysUntilDueAttribute(): ?int
    {
        if (!$this->due_date || $this->isPaid()) {
            return null;
        }

        return now()->diffInDays($this->due_date, false);
    }

    /**
     * Get days overdue
     */
    public function getDaysOverdueAttribute(): int
    {
        if (!$this->isOverdue()) {
            return 0;
        }

        return $this->due_date->diffInDays(now());
    }

    /**
     * Approve the expense
     */
    public function approve(\App\Models\User $approver, string $notes = null): void
    {
        $this->update([
            'status' => 'approved',
            'approved_by' => $approver->id,
        ]);

        // Create approval record
        $this->approvals()->create([
            'user_id' => $approver->id,
            'action' => 'approved',
            'notes' => $notes,
            'approved_at' => now(),
        ]);
    }

    /**
     * Reject the expense
     */
    public function reject(\App\Models\User $rejector, string $reason): void
    {
        $this->update(['status' => 'rejected']);

        // Create approval record
        $this->approvals()->create([
            'user_id' => $rejector->id,
            'action' => 'rejected',
            'notes' => $reason,
            'approved_at' => now(),
        ]);
    }

    /**
     * Mark as paid
     */
    public function markAsPaid(string $paymentMethod = null, string $paymentReference = null): void
    {
        $this->update([
            'status' => 'paid',
            'paid_at' => now(),
            'payment_method' => $paymentMethod,
            'payment_reference' => $paymentReference,
        ]);

        // Update vendor statistics
        if ($this->vendor) {
            $this->vendor->updateStatistics();
        }
    }

    /**
     * Generate next recurring expense
     */
    public function generateRecurringExpense(): ?self
    {
        if (!$this->is_recurring || !$this->recurring_frequency) {
            return null;
        }

        $nextDate = $this->getNextRecurringDate();
        
        if (!$nextDate || ($this->recurring_until && $nextDate->isAfter($this->recurring_until))) {
            return null;
        }

        $newExpense = $this->replicate();
        $newExpense->expense_date = $nextDate;
        $newExpense->due_date = $nextDate->copy()->addDays(30); // Default 30 days payment term
        $newExpense->status = 'pending';
        $newExpense->paid_at = null;
        $newExpense->payment_method = null;
        $newExpense->payment_reference = null;
        $newExpense->approved_by = null;
        $newExpense->expense_number = $this->generateExpenseNumber();
        $newExpense->save();

        return $newExpense;
    }

    /**
     * Get next recurring date
     */
    protected function getNextRecurringDate(): ?\Carbon\Carbon
    {
        if (!$this->recurring_frequency) {
            return null;
        }

        $lastDate = $this->expense_date;

        return match($this->recurring_frequency) {
            'daily' => $lastDate->copy()->addDay(),
            'weekly' => $lastDate->copy()->addWeek(),
            'monthly' => $lastDate->copy()->addMonth(),
            'quarterly' => $lastDate->copy()->addQuarter(),
            'yearly' => $lastDate->copy()->addYear(),
            default => null,
        };
    }

    /**
     * Calculate tax amount
     */
    public function calculateTax(float $taxRate): void
    {
        $this->tax_amount = $this->amount * ($taxRate / 100);
        $this->total_amount = $this->amount + $this->tax_amount;
        $this->save();
    }

    /**
     * Scope for pending expenses
     */
    public function scopePending($query)
    {
        return $query->where('status', 'pending');
    }

    /**
     * Scope for approved expenses
     */
    public function scopeApproved($query)
    {
        return $query->where('status', 'approved');
    }

    /**
     * Scope for paid expenses
     */
    public function scopePaid($query)
    {
        return $query->where('status', 'paid');
    }

    /**
     * Scope for overdue expenses
     */
    public function scopeOverdue($query)
    {
        return $query->where('status', 'approved')
                    ->whereNull('paid_at')
                    ->where('due_date', '<', now());
    }

    /**
     * Scope for recurring expenses
     */
    public function scopeRecurring($query)
    {
        return $query->where('is_recurring', true);
    }

    /**
     * Scope by date range
     */
    public function scopeDateRange($query, $startDate, $endDate)
    {
        return $query->whereBetween('expense_date', [$startDate, $endDate]);
    }

    /**
     * Scope by category
     */
    public function scopeByCategory($query, $categoryId)
    {
        return $query->where('expense_category_id', $categoryId);
    }

    /**
     * Scope by vendor
     */
    public function scopeByVendor($query, $vendorId)
    {
        return $query->where('vendor_id', $vendorId);
    }

    /**
     * Generate unique expense number
     */
    public static function generateExpenseNumber(): string
    {
        $prefix = 'EXP';
        $date = now()->format('Ymd');
        $count = self::whereDate('created_at', today())->count() + 1;
        
        return "{$prefix}-{$date}-" . str_pad($count, 4, '0', STR_PAD_LEFT);
    }

    /**
     * Boot the model
     */
    protected static function boot()
    {
        parent::boot();

        static::creating(function ($expense) {
            if (empty($expense->expense_number)) {
                $expense->expense_number = self::generateExpenseNumber();
            }
            if (empty($expense->status)) {
                $expense->status = 'pending';
            }
            if (is_null($expense->total_amount)) {
                $expense->total_amount = $expense->amount + ($expense->tax_amount ?? 0);
            }
        });
    }
}
