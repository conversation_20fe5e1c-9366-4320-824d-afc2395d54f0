<?php

namespace App\Models\Tenant;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class EmployeeLeaveEntitlement extends Model
{
    use HasFactory;

    protected $fillable = [
        'employee_id',
        'leave_type_id',
        'year',
        'entitled_days',
        'used_days',
        'pending_days',
        'carried_forward_days',
        'expires_at',
    ];

    protected $casts = [
        'year' => 'integer',
        'entitled_days' => 'integer',
        'used_days' => 'integer',
        'pending_days' => 'integer',
        'carried_forward_days' => 'integer',
        'expires_at' => 'date',
    ];

    /**
     * Get the employee that owns this entitlement.
     */
    public function employee(): BelongsTo
    {
        return $this->belongsTo(Employee::class);
    }

    /**
     * Get the leave type for this entitlement.
     */
    public function leaveType(): BelongsTo
    {
        return $this->belongsTo(LeaveType::class);
    }

    /**
     * Get the available days (entitled + carried forward - used - pending).
     */
    public function getAvailableDaysAttribute(): int
    {
        return ($this->entitled_days + $this->carried_forward_days) - ($this->used_days + $this->pending_days);
    }

    /**
     * Get the total entitled days (entitled + carried forward).
     */
    public function getTotalEntitledDaysAttribute(): int
    {
        return $this->entitled_days + $this->carried_forward_days;
    }

    /**
     * Check if there are enough available days for a request.
     */
    public function hasAvailableDays(int $requestedDays): bool
    {
        return $this->available_days >= $requestedDays;
    }

    /**
     * Reserve days for a pending leave request.
     */
    public function reserveDays(int $days): void
    {
        $this->increment('pending_days', $days);
    }

    /**
     * Release reserved days (when request is rejected or cancelled).
     */
    public function releaseDays(int $days): void
    {
        $this->decrement('pending_days', $days);
    }

    /**
     * Consume days (when leave is approved and taken).
     */
    public function consumeDays(int $days): void
    {
        $this->decrement('pending_days', $days);
        $this->increment('used_days', $days);
    }

    /**
     * Scope to get entitlements for a specific year.
     */
    public function scopeForYear($query, int $year)
    {
        return $query->where('year', $year);
    }

    /**
     * Scope to get entitlements for current year.
     */
    public function scopeCurrentYear($query)
    {
        return $query->where('year', now()->year);
    }
}
