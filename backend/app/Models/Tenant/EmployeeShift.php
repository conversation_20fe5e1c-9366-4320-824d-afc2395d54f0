<?php

namespace App\Models\Tenant;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Carbon\Carbon;

class EmployeeShift extends Model
{
    use HasFactory;

    protected $fillable = [
        'branch_id',
        'employee_id',
        'shift_date',
        'start_time',
        'end_time',
        'shift_type',
        'status',
        'actual_start_time',
        'actual_end_time',
        'notes',
        'break_duration',
        'overtime_hours',
        'is_holiday',
        'hourly_rate',
        'created_by',
    ];

    protected $casts = [
        'shift_date' => 'date',
        'is_holiday' => 'boolean',
        'break_duration' => 'decimal:2',
        'overtime_hours' => 'decimal:2',
        'hourly_rate' => 'decimal:2',
    ];

    /**
     * Shift types available.
     */
    public const SHIFT_TYPES = [
        'morning' => 'Morning (6:00 AM - 2:00 PM)',
        'afternoon' => 'Afternoon (2:00 PM - 10:00 PM)',
        'evening' => 'Evening (6:00 PM - 2:00 AM)',
        'night' => 'Night (10:00 PM - 6:00 AM)',
        'full_day' => 'Full Day (9:00 AM - 9:00 PM)',
        'custom' => 'Custom',
    ];

    /**
     * Shift statuses available.
     */
    public const STATUSES = [
        'scheduled' => 'Scheduled',
        'confirmed' => 'Confirmed',
        'started' => 'Started',
        'completed' => 'Completed',
        'cancelled' => 'Cancelled',
        'no_show' => 'No Show',
    ];

    /**
     * Get the branch for this shift.
     */
    public function branch(): BelongsTo
    {
        return $this->belongsTo(Branch::class);
    }

    /**
     * Get the employee for this shift.
     */
    public function employee(): BelongsTo
    {
        return $this->belongsTo(Employee::class);
    }

    /**
     * Get the user who created this shift.
     */
    public function creator(): BelongsTo
    {
        return $this->belongsTo(Employee::class, 'created_by');
    }

    /**
     * Scope for shifts on a specific date.
     */
    public function scopeOnDate($query, $date)
    {
        return $query->whereDate('shift_date', $date);
    }

    /**
     * Scope for shifts in a date range.
     */
    public function scopeBetweenDates($query, $startDate, $endDate)
    {
        return $query->whereBetween('shift_date', [$startDate, $endDate]);
    }

    /**
     * Scope for shifts by status.
     */
    public function scopeWithStatus($query, $status)
    {
        return $query->where('status', $status);
    }

    /**
     * Scope for active shifts (not cancelled).
     */
    public function scopeActive($query)
    {
        return $query->whereNotIn('status', ['cancelled']);
    }

    /**
     * Get shift duration in hours.
     */
    public function getDurationHoursAttribute(): float
    {
        $start = Carbon::createFromFormat('H:i:s', $this->start_time);
        $end = Carbon::createFromFormat('H:i:s', $this->end_time);
        
        // Handle overnight shifts
        if ($end->lessThan($start)) {
            $end->addDay();
        }
        
        return $start->diffInHours($end, true);
    }

    /**
     * Get actual duration in hours.
     */
    public function getActualDurationHoursAttribute(): ?float
    {
        if (!$this->actual_start_time || !$this->actual_end_time) {
            return null;
        }

        $start = Carbon::createFromFormat('H:i:s', $this->actual_start_time);
        $end = Carbon::createFromFormat('H:i:s', $this->actual_end_time);
        
        // Handle overnight shifts
        if ($end->lessThan($start)) {
            $end->addDay();
        }
        
        return $start->diffInHours($end, true);
    }

    /**
     * Get working hours (duration minus breaks).
     */
    public function getWorkingHoursAttribute(): float
    {
        return max(0, $this->duration_hours - $this->break_duration);
    }

    /**
     * Get shift type label.
     */
    public function getShiftTypeLabelAttribute(): string
    {
        return self::SHIFT_TYPES[$this->shift_type] ?? ucfirst($this->shift_type);
    }

    /**
     * Get status label.
     */
    public function getStatusLabelAttribute(): string
    {
        return self::STATUSES[$this->status] ?? ucfirst($this->status);
    }

    /**
     * Check if shift is in progress.
     */
    public function getIsInProgressAttribute(): bool
    {
        return $this->status === 'started';
    }

    /**
     * Check if shift is completed.
     */
    public function getIsCompletedAttribute(): bool
    {
        return $this->status === 'completed';
    }

    /**
     * Calculate total pay for this shift.
     */
    public function getTotalPayAttribute(): ?float
    {
        if (!$this->hourly_rate) {
            return null;
        }

        $regularHours = $this->working_hours;
        $overtimeRate = $this->hourly_rate * 1.5; // 1.5x for overtime
        
        return ($regularHours * $this->hourly_rate) + ($this->overtime_hours * $overtimeRate);
    }
}
