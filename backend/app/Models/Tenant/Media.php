<?php

namespace App\Models\Tenant;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\MorphTo;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Str;

class Media extends Model
{
    use HasFactory;

    protected $fillable = [
        'name',
        'file_name',
        'mime_type',
        'disk',
        'collection_name',
        'size',
        'manipulations',
        'custom_properties',
        'generated_conversions',
        'responsive_images',
        'order_column',
        'model_type',
        'model_id',
        'uuid',
        'conversions_disk',
        'alt_text',
        'caption',
        'tags',
        'width',
        'height',
        'folder',
        'is_featured',
        'uploaded_at',
        'uploaded_by',
    ];

    protected $casts = [
        'manipulations' => 'array',
        'custom_properties' => 'array',
        'generated_conversions' => 'array',
        'responsive_images' => 'array',
        'tags' => 'array',
        'width' => 'integer',
        'height' => 'integer',
        'size' => 'integer',
        'is_featured' => 'boolean',
        'uploaded_at' => 'datetime',
    ];

    protected $appends = [
        'url',
        'thumbnail_url',
        'human_readable_size',
        'extension'
    ];

    /**
     * Get the owning model.
     */
    public function model(): MorphTo
    {
        return $this->morphTo();
    }

    /**
     * Get the user who uploaded this media.
     */
    public function uploader(): BelongsTo
    {
        return $this->belongsTo(\App\Models\User::class, 'uploaded_by');
    }

    /**
     * Get the full URL of the media file.
     */
    public function getUrlAttribute(): string
    {
        // For tenant context, use tenant asset URL
        if (tenancy()->initialized) {
            return route('tenant.asset', [
                'path' => $this->file_name
            ]);
        }

        // Fallback to regular storage URL
        return Storage::disk($this->disk)->url($this->file_name);
    }

    /**
     * Get the thumbnail URL
     */
    public function getThumbnailUrlAttribute(): string
    {
        // Check if thumbnail path exists in custom_properties
        if (isset($this->custom_properties['thumbnail_path'])) {
            // For tenant context, use tenant asset URL
            if (tenancy()->initialized) {
                return route('tenant.asset', [
                    'path' => $this->custom_properties['thumbnail_path']
                ]);
            }
            return Storage::disk($this->disk)->url($this->custom_properties['thumbnail_path']);
        }

        return $this->url;
    }

    /**
     * Get formatted file size
     */
    public function getSizeFormattedAttribute(): string
    {
        return $this->human_readable_size;
    }

    /**
     * Get the full path of the media file.
     */
    public function getFullPathAttribute(): string
    {
        return Storage::disk($this->disk)->path($this->file_name);
    }

    /**
     * Get the human readable file size.
     */
    public function getHumanReadableSizeAttribute(): string
    {
        $bytes = $this->size;
        $units = ['B', 'KB', 'MB', 'GB', 'TB'];

        for ($i = 0; $bytes > 1024; $i++) {
            $bytes /= 1024;
        }

        return round($bytes, 2) . ' ' . $units[$i];
    }

    /**
     * Get the file extension.
     */
    public function getExtensionAttribute(): string
    {
        return pathinfo($this->file_name, PATHINFO_EXTENSION);
    }

    /**
     * Check if the media is an image.
     */
    public function isImage(): bool
    {
        return str_starts_with($this->mime_type, 'image/');
    }

    /**
     * Check if the media is a video.
     */
    public function isVideo(): bool
    {
        return str_starts_with($this->mime_type, 'video/');
    }

    /**
     * Check if the media is a document.
     */
    public function isDocument(): bool
    {
        return in_array($this->mime_type, [
            'application/pdf',
            'application/msword',
            'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
            'application/vnd.ms-excel',
            'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
        ]);
    }

    /**
     * Get conversion URL.
     */
    public function getConversionUrl(string $conversion): ?string
    {
        if (!isset($this->generated_conversions[$conversion])) {
            return null;
        }

        $conversionPath = str_replace($this->name, $conversion . '/' . $this->name, $this->file_name);
        return Storage::disk($this->conversions_disk ?? $this->disk)->url($conversionPath);
    }

    /**
     * Get thumbnail URL.
     */
    public function getThumbnailAttribute(): ?string
    {
        return $this->getConversionUrl('thumbnail') ?? $this->url;
    }

    /**
     * Get medium size URL.
     */
    public function getMediumAttribute(): ?string
    {
        return $this->getConversionUrl('medium') ?? $this->url;
    }

    /**
     * Get large size URL.
     */
    public function getLargeAttribute(): ?string
    {
        return $this->getConversionUrl('large') ?? $this->url;
    }

    /**
     * Scope for images only.
     */
    public function scopeImages($query)
    {
        return $query->where('mime_type', 'like', 'image/%');
    }

    /**
     * Scope for specific collection.
     */
    public function scopeInCollection($query, string $collection)
    {
        return $query->where('collection_name', $collection);
    }

    /**
     * Scope for specific folder.
     */
    public function scopeInFolder($query, string $folder)
    {
        return $query->where('folder', $folder);
    }

    /**
     * Scope for search.
     */
    public function scopeSearch($query, string $search)
    {
        return $query->where(function ($q) use ($search) {
            $q->where('name', 'like', '%' . $search . '%')
              ->orWhere('alt_text', 'like', '%' . $search . '%')
              ->orWhere('caption', 'like', '%' . $search . '%')
              ->orWhereJsonContains('tags', $search);
        });
    }

    /**
     * Create media from uploaded file
     */
    public static function createFromUpload($file, $options = [])
    {
        $disk = $options['disk'] ?? 'tenant';
        $collection = $options['collection'] ?? 'default';

        // Generate unique filename
        $filename = uniqid() . '.' . $file->getClientOriginalExtension();

        // Store the file
        $storedPath = $file->storeAs($collection, $filename, $disk);

        // Create media record
        $media = static::create([
            'name' => pathinfo($file->getClientOriginalName(), PATHINFO_FILENAME),
            'file_name' => $storedPath,
            'original_name' => $file->getClientOriginalName(),
            'mime_type' => $file->getMimeType(),
            'size' => $file->getSize(),
            'disk' => $disk,
            'collection_name' => $collection,
            'alt_text' => $options['alt_text'] ?? null,
            'caption' => $options['caption'] ?? null,
            'uploaded_by' => Auth::id(),
        ]);

        return $media;
    }

    /**
     * Boot the model.
     */
    protected static function boot()
    {
        parent::boot();

        static::creating(function ($media) {
            if (empty($media->uuid)) {
                $media->uuid = (string) Str::uuid();
            }
            if (empty($media->uploaded_at)) {
                $media->uploaded_at = now();
            }
        });

        static::deleting(function ($media) {
            // Delete the WebP file
            if (Storage::disk($media->disk)->exists($media->file_name)) {
                Storage::disk($media->disk)->delete($media->file_name);
            }

            // Delete thumbnail
            if (isset($media->custom_properties['thumbnail_path'])) {
                $thumbnailPath = $media->custom_properties['thumbnail_path'];
                if (Storage::disk($media->disk)->exists($thumbnailPath)) {
                    Storage::disk($media->disk)->delete($thumbnailPath);
                }
            }

            // Delete conversions (if any exist)
            if ($media->generated_conversions) {
                foreach (array_keys($media->generated_conversions) as $conversion) {
                    $conversionPath = str_replace($media->name, $conversion . '/' . $media->name, $media->file_name);
                    if (Storage::disk($media->conversions_disk ?? $media->disk)->exists($conversionPath)) {
                        Storage::disk($media->conversions_disk ?? $media->disk)->delete($conversionPath);
                    }
                }
            }
        });
    }
}
