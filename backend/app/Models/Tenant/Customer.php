<?php

namespace App\Models\Tenant;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use <PERSON><PERSON>\Sanctum\HasApiTokens;

class Customer extends Authenticatable
{
    use HasApiTokens, HasFactory, Notifiable;

    protected $fillable = [
        'restaurant_id',
        'name',
        'email',
        'phone',
        'date_of_birth',
        'gender',
        'password',
        'dietary_preferences',
        'allergies',
        'preferred_language',
        'marketing_emails',
        'order_notifications',
        'total_orders',
        'total_spent',
        'average_order_value',
        'last_order_at',
        'loyalty_points',
        'customer_tier',
        'status',
        'notes',
    ];

    protected $hidden = [
        'password',
        'remember_token',
    ];

    protected $casts = [
        'email_verified_at' => 'datetime',
        'date_of_birth' => 'date',
        'dietary_preferences' => 'array',
        'allergies' => 'array',
        'marketing_emails' => 'boolean',
        'order_notifications' => 'boolean',
        'total_spent' => 'decimal:2',
        'average_order_value' => 'decimal:2',
        'last_order_at' => 'datetime',
        'password' => 'hashed',
    ];

    /**
     * Get the restaurant that owns this customer
     */
    public function restaurant(): BelongsTo
    {
        return $this->belongsTo(Restaurant::class);
    }

    /**
     * Get the customer's addresses
     */
    public function addresses(): HasMany
    {
        return $this->hasMany(CustomerAddress::class);
    }

    /**
     * Get the customer's default address
     */
    public function defaultAddress()
    {
        return $this->addresses()->where('is_default', true)->first();
    }

    /**
     * Get the customer's orders
     */
    public function orders(): HasMany
    {
        return $this->hasMany(Order::class);
    }

    /**
     * Get the customer's reservations
     */
    public function reservations(): HasMany
    {
        return $this->hasMany(TableReservation::class);
    }

    /**
     * Get the customer's food reviews
     */
    public function foodReviews(): HasMany
    {
        return $this->hasMany(FoodReview::class);
    }

    /**
     * Get the customer's restaurant reviews
     */
    public function restaurantReviews(): HasMany
    {
        return $this->hasMany(RestaurantReview::class);
    }

    /**
     * Get the customer's full name
     */
    public function getFullNameAttribute(): string
    {
        return $this->first_name . ' ' . $this->last_name;
    }

    /**
     * Get the customer's tier badge color
     */
    public function getTierColorAttribute(): string
    {
        return match ($this->customer_tier) {
            'bronze' => '#CD7F32',
            'silver' => '#C0C0C0',
            'gold' => '#FFD700',
            'platinum' => '#E5E4E2',
            default => '#CD7F32',
        };
    }

    /**
     * Check if customer is a VIP
     */
    public function isVip(): bool
    {
        return in_array($this->customer_tier, ['gold', 'platinum']);
    }

    /**
     * Update customer statistics after order
     */
    public function updateOrderStats(Order $order): void
    {
        $this->increment('total_orders');
        $this->increment('total_spent', $order->total_amount);
        $this->update([
            'average_order_value' => $this->total_spent / $this->total_orders,
            'last_order_at' => now(),
        ]);

        // Update customer tier based on total spent
        $this->updateCustomerTier();
    }

    /**
     * Update customer tier based on spending
     */
    protected function updateCustomerTier(): void
    {
        $tier = match (true) {
            $this->total_spent >= 5000 => 'platinum',
            $this->total_spent >= 2000 => 'gold',
            $this->total_spent >= 500 => 'silver',
            default => 'bronze',
        };

        if ($tier !== $this->customer_tier) {
            $this->update(['customer_tier' => $tier]);
        }
    }

    /**
     * Add loyalty points
     */
    public function addLoyaltyPoints(int $points): void
    {
        $this->increment('loyalty_points', $points);
    }

    /**
     * Redeem loyalty points
     */
    public function redeemLoyaltyPoints(int $points): bool
    {
        if ($this->loyalty_points >= $points) {
            $this->decrement('loyalty_points', $points);
            return true;
        }
        return false;
    }

    /**
     * Scope for active customers
     */
    public function scopeActive($query)
    {
        return $query->where(function($q) {
            $q->where('status', 'active')
              ->orWhereNull('status')
              ->orWhere('status', '');
        });
    }

    /**
     * Scope for VIP customers
     */
    public function scopeVip($query)
    {
        return $query->whereIn('customer_tier', ['gold', 'platinum']);
    }

    /**
     * Scope for customers with recent orders
     */
    public function scopeRecentlyActive($query, $days = 30)
    {
        return $query->where('last_order_at', '>=', now()->subDays($days));
    }
}
