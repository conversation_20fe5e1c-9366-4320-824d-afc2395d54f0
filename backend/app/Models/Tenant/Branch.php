<?php

namespace App\Models\Tenant;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Support\Str;

class Branch extends Model
{
    use HasFactory;

    protected $fillable = [
        'name',
        'slug',
        'description',
        'address',
        'city',
        'state',
        'postal_code',
        'country',
        'phone',
        'email',
        'manager_id',
        'latitude',
        'longitude',
        'formatted_address',
        'opening_time',
        'closing_time',
        'is_active',
        'is_main_branch',
        'sort_order',
        'operating_days',
        'contact_info',
    ];

    protected $casts = [
        'is_active' => 'boolean',
        'is_main_branch' => 'boolean',
        'operating_days' => 'array',
        'contact_info' => 'array',
        'latitude' => 'decimal:8',
        'longitude' => 'decimal:8',
    ];

    /**
     * Boot the model.
     */
    protected static function boot()
    {
        parent::boot();

        static::creating(function ($branch) {
            if (empty($branch->slug)) {
                $branch->slug = Str::slug($branch->name);
            }
        });

        static::updating(function ($branch) {
            if ($branch->isDirty('name') && empty($branch->slug)) {
                $branch->slug = Str::slug($branch->name);
            }
        });
    }

    /**
     * Get the manager of this branch.
     */
    public function manager(): BelongsTo
    {
        return $this->belongsTo(Employee::class, 'manager_id');
    }

    /**
     * Get all employees assigned to this branch.
     */
    public function employees(): HasMany
    {
        return $this->hasMany(Employee::class, 'primary_branch_id');
    }

    /**
     * Get all shifts for this branch.
     */
    public function shifts(): HasMany
    {
        return $this->hasMany(EmployeeShift::class);
    }

    /**
     * Get all floors for this branch.
     */
    public function floors(): HasMany
    {
        return $this->hasMany(\App\Models\Tenant\Floor::class);
    }

    /**
     * Get active floors for this branch.
     */
    public function activeFloors(): HasMany
    {
        return $this->floors()->active()->ordered();
    }

    /**
     * Get all tables for this branch.
     */
    public function tables(): HasMany
    {
        return $this->hasMany(Table::class);
    }

    /**
     * Get all menu items available in this branch.
     */
    public function menuItems(): BelongsToMany
    {
        return $this->belongsToMany(MenuItem::class, 'menu_item_branches')
            ->withPivot(['is_available', 'branch_specific_price'])
            ->withTimestamps();
    }

    /**
     * Get only available menu items in this branch.
     */
    public function availableMenuItems(): BelongsToMany
    {
        return $this->menuItems()->wherePivot('is_available', true);
    }

    /**
     * Scope for active branches.
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope for ordered branches.
     */
    public function scopeOrdered($query)
    {
        return $query->orderBy('sort_order')->orderBy('name');
    }

    /**
     * Scope for main branch.
     */
    public function scopeMainBranch($query)
    {
        return $query->where('is_main_branch', true);
    }

    /**
     * Get full address.
     */
    public function getFullAddressAttribute(): string
    {
        $parts = array_filter([
            $this->address,
            $this->city,
            $this->state,
            $this->postal_code,
            $this->country,
        ]);

        return implode(', ', $parts);
    }

    /**
     * Check if branch is open at given time.
     */
    public function isOpenAt($time = null): bool
    {
        if (!$this->is_active) {
            return false;
        }

        $time = $time ?: now()->format('H:i');
        return $time >= $this->opening_time && $time <= $this->closing_time;
    }

    /**
     * Get operating days as array.
     */
    public function getOperatingDaysListAttribute(): array
    {
        return $this->operating_days ?: ['monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday', 'sunday'];
    }

    /**
     * Check if branch operates on given day.
     */
    public function operatesOnDay($day): bool
    {
        return in_array(strtolower($day), $this->operating_days_list);
    }
}
