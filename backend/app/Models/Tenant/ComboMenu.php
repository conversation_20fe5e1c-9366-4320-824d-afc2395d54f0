<?php

namespace App\Models\Tenant;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use App\Traits\HasMediaLibrary;

class ComboMenu extends Model
{
    use HasFactory, HasMediaLibrary;

    protected $fillable = [
        'name',
        'description',
        'price',
        'is_available',
        'sort_order',
        'image_url', // Deprecated - use media relationships instead
        'media_id', // Primary media ID for backward compatibility
        'customization_rules',
    ];

    protected $casts = [
        'price' => 'decimal:2',
        'is_available' => 'boolean',
        'sort_order' => 'integer',
        'customization_rules' => 'array',
    ];

    protected $appends = [
        'primary_image_url',
        'thumbnail_url',
    ];

    /**
     * Get the components for this combo.
     */
    public function components(): HasMany
    {
        return $this->hasMany(ComboComponent::class);
    }

    /**
     * Get all media associated with this combo menu (multiple images)
     */
    public function mediaItems(): BelongsToMany
    {
        return $this->belongsToMany(Media::class, 'combo_menu_media')
                    ->withPivot(['sort_order', 'is_primary'])
                    ->withTimestamps()
                    ->orderBy('combo_menu_media.sort_order');
    }

    /**
     * Get the primary media item
     */
    public function primaryMedia(): BelongsTo
    {
        return $this->belongsTo(Media::class, 'media_id');
    }

    /**
     * Get main components.
     */
    public function mainComponents(): HasMany
    {
        return $this->hasMany(ComboComponent::class)->where('component_type', 'main');
    }

    /**
     * Get side components.
     */
    public function sideComponents(): HasMany
    {
        return $this->hasMany(ComboComponent::class)->where('component_type', 'side');
    }

    /**
     * Get drink components.
     */
    public function drinkComponents(): HasMany
    {
        return $this->hasMany(ComboComponent::class)->where('component_type', 'drink');
    }

    /**
     * Get dessert components.
     */
    public function dessertComponents(): HasMany
    {
        return $this->hasMany(ComboComponent::class)->where('component_type', 'dessert');
    }

    /**
     * Scope to get only available combos.
     */
    public function scopeAvailable($query)
    {
        return $query->where('is_available', true);
    }

    /**
     * Scope to order by sort order.
     */
    public function scopeOrdered($query)
    {
        return $query->orderBy('sort_order')->orderBy('name');
    }



    /**
     * Calculate the total individual price of all components.
     */
    public function getIndividualTotalAttribute(): float
    {
        return $this->components->sum(function ($component) {
            return $component->menuItem->price;
        });
    }

    /**
     * Calculate the savings from buying as combo.
     */
    public function getSavingsAttribute(): float
    {
        return max(0, $this->individual_total - $this->price);
    }

    /**
     * Get the primary image URL
     */
    public function getPrimaryImageUrlAttribute(): ?string
    {
        // First try to get from media relationships
        if ($this->primaryMedia) {
            return $this->primaryMedia->url;
        }

        // Try to get from mediaItems (first primary or first item)
        $primaryMediaItem = $this->mediaItems->where('pivot.is_primary', true)->first();
        if ($primaryMediaItem) {
            return $primaryMediaItem->url;
        }

        $firstMediaItem = $this->mediaItems->first();
        if ($firstMediaItem) {
            return $firstMediaItem->url;
        }

        // Fallback to deprecated image_url field
        return $this->image_url;
    }

    /**
     * Get the thumbnail URL
     */
    public function getThumbnailUrlAttribute(): ?string
    {
        // First try to get from media relationships
        if ($this->primaryMedia) {
            return $this->primaryMedia->thumbnail_url;
        }

        // Try to get from mediaItems (first primary or first item)
        $primaryMediaItem = $this->mediaItems->where('pivot.is_primary', true)->first();
        if ($primaryMediaItem) {
            return $primaryMediaItem->thumbnail_url;
        }

        $firstMediaItem = $this->mediaItems->first();
        if ($firstMediaItem) {
            return $firstMediaItem->thumbnail_url;
        }

        // Fallback to deprecated image_url field
        return $this->image_url;
    }

    /**
     * Add media to combo menu
     */
    public function addMedia($mediaId, $isPrimary = false, $sortOrder = null)
    {
        // If this is primary, unset other primary media
        if ($isPrimary) {
            $this->mediaItems()->updateExistingPivot($this->mediaItems()->pluck('media.id'), ['is_primary' => false]);
        }

        // Get next sort order if not provided
        if ($sortOrder === null) {
            $sortOrder = $this->mediaItems()->max('combo_menu_media.sort_order') + 1;
        }

        $this->mediaItems()->attach($mediaId, [
            'is_primary' => $isPrimary,
            'sort_order' => $sortOrder,
        ]);
    }

    /**
     * Sync media items (replace all)
     */
    public function syncMedia(array $mediaIds)
    {
        $syncData = [];
        foreach ($mediaIds as $index => $mediaId) {
            $syncData[$mediaId] = [
                'sort_order' => $index,
                'is_primary' => $index === 0, // First image is primary
            ];
        }

        $this->mediaItems()->sync($syncData);
    }

    /**
     * Get all image URLs
     */
    public function getImageUrlsAttribute(): array
    {
        return $this->mediaItems->map(function ($media) {
            return $media->url;
        })->toArray();
    }
}
