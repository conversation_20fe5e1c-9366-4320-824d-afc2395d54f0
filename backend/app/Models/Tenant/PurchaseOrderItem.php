<?php

namespace App\Models\Tenant;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class PurchaseOrderItem extends Model
{
    use HasFactory;

    protected $fillable = [
        'purchase_order_id',
        'inventory_item_id',
        'quantity',
        'unit_cost',
        'total_cost',
        'received_quantity',
        'batch_number',
        'expiry_date',
        'notes',
    ];

    protected $casts = [
        'quantity' => 'decimal:2',
        'unit_cost' => 'decimal:4',
        'total_cost' => 'decimal:2',
        'received_quantity' => 'decimal:2',
        'expiry_date' => 'date',
    ];

    /**
     * Get the purchase order that owns this item
     */
    public function purchaseOrder(): BelongsTo
    {
        return $this->belongsTo(PurchaseOrder::class);
    }

    /**
     * Get the inventory item
     */
    public function inventoryItem(): BelongsTo
    {
        return $this->belongsTo(InventoryItem::class);
    }

    /**
     * Check if item is fully received
     */
    public function isFullyReceived(): bool
    {
        return $this->received_quantity >= $this->quantity;
    }

    /**
     * Check if item is partially received
     */
    public function isPartiallyReceived(): bool
    {
        return $this->received_quantity > 0 && $this->received_quantity < $this->quantity;
    }

    /**
     * Get remaining quantity to receive
     */
    public function getRemainingQuantityAttribute(): float
    {
        return max(0, $this->quantity - $this->received_quantity);
    }

    /**
     * Get received percentage
     */
    public function getReceivedPercentageAttribute(): float
    {
        if ($this->quantity <= 0) {
            return 0;
        }

        return ($this->received_quantity / $this->quantity) * 100;
    }

    /**
     * Get receive status
     */
    public function getReceiveStatusAttribute(): string
    {
        if ($this->received_quantity <= 0) {
            return 'pending';
        } elseif ($this->isFullyReceived()) {
            return 'received';
        } else {
            return 'partial';
        }
    }

    /**
     * Boot the model
     */
    protected static function boot()
    {
        parent::boot();

        static::creating(function ($item) {
            if (is_null($item->total_cost)) {
                $item->total_cost = $item->quantity * $item->unit_cost;
            }
        });

        static::updating(function ($item) {
            if ($item->isDirty(['quantity', 'unit_cost'])) {
                $item->total_cost = $item->quantity * $item->unit_cost;
            }
        });
    }
}
