<?php

namespace App\Models\Tenant;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class LoyaltySettings extends Model
{
    use HasFactory;

    protected $fillable = [
        'enabled',
        'points_per_dollar',
        'points_for_dollar_discount',
        'max_discount_percentage',
        'minimum_order_amount',
        'points_expiry_days',
        'birthday_bonus_enabled',
        'birthday_bonus_points',
        'referral_program_enabled',
        'referral_bonus_points',
        'tier_settings',
    ];

    protected $casts = [
        'enabled' => 'boolean',
        'points_per_dollar' => 'decimal:2',
        'points_for_dollar_discount' => 'integer',
        'max_discount_percentage' => 'decimal:2',
        'minimum_order_amount' => 'decimal:2',
        'points_expiry_days' => 'integer',
        'birthday_bonus_enabled' => 'boolean',
        'birthday_bonus_points' => 'integer',
        'referral_program_enabled' => 'boolean',
        'referral_bonus_points' => 'integer',
        'tier_settings' => 'array',
    ];

    /**
     * Get the singleton instance of loyalty settings
     */
    public static function getInstance(): self
    {
        return static::firstOrCreate([], [
            'enabled' => false,
            'points_per_dollar' => 1.00,
            'points_for_dollar_discount' => 100,
            'max_discount_percentage' => 20.00,
            'minimum_order_amount' => 0.00,
            'tier_settings' => [
                'regular' => ['min_points' => 0, 'multiplier' => 1.0],
                'bronze' => ['min_points' => 500, 'multiplier' => 1.1],
                'silver' => ['min_points' => 1500, 'multiplier' => 1.2],
                'gold' => ['min_points' => 3000, 'multiplier' => 1.3],
                'platinum' => ['min_points' => 5000, 'multiplier' => 1.5],
            ],
        ]);
    }

    /**
     * Calculate points earned for a given amount
     */
    public function calculatePointsEarned(float $amount, string $tier = 'regular'): int
    {
        if (!$this->enabled || $amount < $this->minimum_order_amount) {
            return 0;
        }

        $basePoints = $amount * $this->points_per_dollar;
        $tierMultiplier = $this->tier_settings[$tier]['multiplier'] ?? 1.0;
        
        return (int) floor($basePoints * $tierMultiplier);
    }

    /**
     * Calculate discount amount for given points
     */
    public function calculateDiscountAmount(int $points): float
    {
        if (!$this->enabled || $points < $this->points_for_dollar_discount) {
            return 0.00;
        }

        return floor($points / $this->points_for_dollar_discount);
    }

    /**
     * Calculate maximum discount for order amount
     */
    public function getMaxDiscountForOrder(float $orderAmount): float
    {
        return $orderAmount * ($this->max_discount_percentage / 100);
    }

    /**
     * Get tier for given lifetime points
     */
    public function getTierForPoints(int $lifetimePoints): string
    {
        $tiers = collect($this->tier_settings)->sortByDesc('min_points');
        
        foreach ($tiers as $tier => $settings) {
            if ($lifetimePoints >= $settings['min_points']) {
                return $tier;
            }
        }
        
        return 'regular';
    }
}
