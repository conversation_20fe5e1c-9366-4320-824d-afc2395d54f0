<?php

namespace App\Models\Tenant;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\SoftDeletes;

class MenuItemVariation extends Model
{
    use HasFactory;

    protected $table = 'menu_item_variations';

    protected $fillable = [
        'menu_item_id',
        'name',
        'name_bn',
        'price_modifier',
        'is_available',
        'sort_order',
        'description',
        'description_bn',
    ];

    protected $casts = [
        'price_modifier' => 'decimal:2',
        'is_available' => 'boolean',
        'sort_order' => 'integer',
    ];

    /**
     * Get the menu item this variation belongs to
     */
    public function menuItem(): BelongsTo
    {
        return $this->belongsTo(MenuItem::class);
    }

    /**
     * Calculate the final price for this variation with base price
     */
    public function calculatePrice(float $basePrice): float
    {
        return $basePrice + $this->price_modifier;
    }

    /**
     * Check if variation is available
     */
    public function isAvailable(): bool
    {
        return $this->is_available;
    }

    /**
     * Scope for available variations
     */
    public function scopeAvailable($query)
    {
        return $query->where('is_available', true);
    }

    /**
     * Scope for ordering by sort order
     */
    public function scopeOrdered($query)
    {
        return $query->orderBy('sort_order')->orderBy('name');
    }

    /**
     * Get the localized name based on current locale.
     */
    public function getLocalizedNameAttribute(): string
    {
        $locale = app()->getLocale();
        return $locale === 'bn' && $this->name_bn ? $this->name_bn : $this->name;
    }

    /**
     * Get the localized description based on current locale.
     */
    public function getLocalizedDescriptionAttribute(): ?string
    {
        $locale = app()->getLocale();
        return $locale === 'bn' && $this->description_bn ? $this->description_bn : $this->description;
    }
}
