<?php

namespace App\Models\Tenant;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use App\Traits\HasMediaLibrary;

class WasteRecord extends Model
{
    use HasFactory, HasMediaLibrary;

    protected $fillable = [
        'restaurant_id',
        'inventory_item_id',
        'batch_number',
        'quantity',
        'unit_cost',
        'total_cost',
        'reason',
        'waste_date',
        'recorded_by',
        'notes',
    ];

    protected $casts = [
        'quantity' => 'decimal:2',
        'unit_cost' => 'decimal:4',
        'total_cost' => 'decimal:2',
        'waste_date' => 'date',
    ];

    /**
     * Get the restaurant that owns this waste record
     */
    public function restaurant(): BelongsTo
    {
        return $this->belongsTo(Restaurant::class);
    }

    /**
     * Get the inventory item
     */
    public function inventoryItem(): BelongsTo
    {
        return $this->belongsTo(InventoryItem::class);
    }

    /**
     * Get the user who recorded this waste
     */
    public function recordedBy(): BelongsTo
    {
        return $this->belongsTo(\App\Models\User::class, 'recorded_by');
    }

    /**
     * Get reason label
     */
    public function getReasonLabelAttribute(): string
    {
        switch ($this->reason) {
            case 'expired':
                return 'Expired';
            case 'damaged':
                return 'Damaged';
            case 'spoiled':
                return 'Spoiled';
            case 'contaminated':
                return 'Contaminated';
            case 'overproduction':
                return 'Overproduction';
            case 'preparation_error':
                return 'Preparation Error';
            case 'customer_return':
                return 'Customer Return';
            case 'quality_control':
                return 'Quality Control';
            case 'theft':
                return 'Theft';
            case 'other':
                return 'Other';
            default:
                return ucfirst($this->reason);
        }
    }

    /**
     * Get waste percentage of total inventory value
     */
    public function getWastePercentageAttribute(): float
    {
        $totalInventoryValue = $this->inventoryItem->current_value;

        if ($totalInventoryValue <= 0) {
            return 0;
        }

        return ($this->total_cost / $totalInventoryValue) * 100;
    }

    /**
     * Scope for today's waste
     */
    public function scopeToday($query)
    {
        return $query->whereDate('waste_date', today());
    }

    /**
     * Scope for this week's waste
     */
    public function scopeThisWeek($query)
    {
        return $query->whereBetween('waste_date', [now()->startOfWeek(), now()->endOfWeek()]);
    }

    /**
     * Scope for this month's waste
     */
    public function scopeThisMonth($query)
    {
        return $query->whereMonth('waste_date', now()->month)
                    ->whereYear('waste_date', now()->year);
    }

    /**
     * Scope by waste reason
     */
    public function scopeByReason($query, string $reason)
    {
        return $query->where('reason', $reason);
    }

    /**
     * Scope by date range
     */
    public function scopeDateRange($query, $startDate, $endDate)
    {
        return $query->whereBetween('waste_date', [$startDate, $endDate]);
    }

    /**
     * Get waste statistics for a period
     */
    public static function getWasteStats(string $period = 'month', $date = null): array
    {
        $query = self::query();
        $date = $date ?? now();

        switch ($period) {
            case 'today':
                $query->whereDate('waste_date', $date);
                break;
            case 'week':
                $startOfWeek = $date->copy()->startOfWeek();
                $endOfWeek = $date->copy()->endOfWeek();
                $query->whereBetween('waste_date', [$startOfWeek, $endOfWeek]);
                break;
            case 'month':
                $query->whereYear('waste_date', $date->year)
                      ->whereMonth('waste_date', $date->month);
                break;
            case 'year':
                $query->whereYear('waste_date', $date->year);
                break;
        }

        $records = $query->get();

        return [
            'total_records' => $records->count(),
            'total_cost' => $records->sum('total_cost'),
            'total_quantity' => $records->sum('quantity'),
            'by_reason' => $records->groupBy('reason')->map(function ($group) {
                return [
                    'count' => $group->count(),
                    'total_cost' => $group->sum('total_cost'),
                    'total_quantity' => $group->sum('quantity'),
                ];
            }),
            'by_category' => $records->groupBy('inventoryItem.category.name')->map(function ($group) {
                return [
                    'count' => $group->count(),
                    'total_cost' => $group->sum('total_cost'),
                    'total_quantity' => $group->sum('quantity'),
                ];
            }),
        ];
    }

    /**
     * Get waste trend over time
     */
    public static function getWasteTrend(int $days = 30): array
    {
        $records = self::where('waste_date', '>=', now()->subDays($days))
            ->selectRaw('DATE(waste_date) as date, SUM(total_cost) as daily_cost, SUM(quantity) as daily_quantity')
            ->groupBy('date')
            ->orderBy('date')
            ->get();

        return $records->map(function ($record) {
            return [
                'date' => $record->date,
                'cost' => $record->daily_cost,
                'quantity' => $record->daily_quantity,
            ];
        })->toArray();
    }

    /**
     * Boot the model
     */
    protected static function boot()
    {
        parent::boot();

        static::creating(function ($waste) {
            if (is_null($waste->waste_date)) {
                $waste->waste_date = now();
            }
        });
    }
}
