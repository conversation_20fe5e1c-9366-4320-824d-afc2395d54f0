<?php

namespace App\Models\Tenant;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;

class DeliveryPersonnel extends Model
{
    use HasFactory;

    protected $fillable = [
        'restaurant_id',
        'user_id',
        'employee_id',
        'first_name',
        'last_name',
        'phone',
        'email',
        'vehicle_type',
        'vehicle_number',
        'license_number',
        'current_latitude',
        'current_longitude',
        'last_location_update',
        'status',
        'is_active',
        'hourly_rate',
        'commission_rate',
        'max_concurrent_orders',
        'delivery_radius', // in kilometers
        'rating',
        'total_deliveries',
        'on_time_deliveries',
        'shift_start_time',
        'shift_end_time',
        'emergency_contact_name',
        'emergency_contact_phone',
    ];

    protected $casts = [
        'current_latitude' => 'decimal:8',
        'current_longitude' => 'decimal:8',
        'last_location_update' => 'datetime',
        'is_active' => 'boolean',
        'hourly_rate' => 'decimal:2',
        'commission_rate' => 'decimal:2',
        'max_concurrent_orders' => 'integer',
        'delivery_radius' => 'decimal:2',
        'rating' => 'decimal:2',
        'total_deliveries' => 'integer',
        'on_time_deliveries' => 'integer',
        'shift_start_time' => 'datetime:H:i',
        'shift_end_time' => 'datetime:H:i',
    ];

    /**
     * Get the restaurant this delivery person belongs to
     */
    public function restaurant(): BelongsTo
    {
        return $this->belongsTo(Restaurant::class);
    }

    /**
     * Get the user account for this delivery person
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(\App\Models\User::class);
    }

    /**
     * Get all orders assigned to this delivery person
     */
    public function orders(): HasMany
    {
        return $this->hasMany(Order::class, 'delivery_person_id');
    }

    /**
     * Get delivery zones this person can work in
     */
    public function deliveryZones(): BelongsToMany
    {
        return $this->belongsToMany(DeliveryZone::class, 'delivery_personnel_zones');
    }

    /**
     * Get current active orders
     */
    public function activeOrders(): HasMany
    {
        return $this->hasMany(Order::class, 'delivery_person_id')
            ->whereIn('status', ['confirmed', 'preparing', 'ready', 'out_for_delivery']);
    }

    /**
     * Get full name attribute
     */
    public function getFullNameAttribute(): string
    {
        return $this->first_name . ' ' . $this->last_name;
    }

    /**
     * Get current location as array
     */
    public function getCurrentLocationAttribute(): ?array
    {
        if (!$this->current_latitude || !$this->current_longitude) {
            return null;
        }

        return [
            'lat' => (float) $this->current_latitude,
            'lng' => (float) $this->current_longitude,
            'updated_at' => $this->last_location_update,
        ];
    }

    /**
     * Get on-time delivery percentage
     */
    public function getOnTimePercentageAttribute(): float
    {
        if ($this->total_deliveries === 0) {
            return 100;
        }

        return ($this->on_time_deliveries / $this->total_deliveries) * 100;
    }

    /**
     * Check if delivery person is available for new orders
     */
    public function isAvailable(): bool
    {
        if (!$this->is_active || $this->status !== 'available') {
            return false;
        }

        // Check if within shift hours
        if (!$this->isWithinShiftHours()) {
            return false;
        }

        // Check if under max concurrent orders
        return $this->activeOrders()->count() < $this->max_concurrent_orders;
    }

    /**
     * Check if currently within shift hours
     */
    public function isWithinShiftHours(): bool
    {
        if (!$this->shift_start_time || !$this->shift_end_time) {
            return true; // No shift restrictions
        }

        $now = now()->format('H:i:s');
        $start = $this->shift_start_time->format('H:i:s');
        $end = $this->shift_end_time->format('H:i:s');

        if ($start <= $end) {
            // Same day shift
            return $now >= $start && $now <= $end;
        } else {
            // Overnight shift
            return $now >= $start || $now <= $end;
        }
    }

    /**
     * Update current location
     */
    public function updateLocation(float $latitude, float $longitude): void
    {
        $this->update([
            'current_latitude' => $latitude,
            'current_longitude' => $longitude,
            'last_location_update' => now(),
        ]);
    }

    /**
     * Calculate distance to a point in kilometers
     */
    public function distanceTo(float $latitude, float $longitude): float
    {
        if (!$this->current_latitude || !$this->current_longitude) {
            return 999; // Return large number if location unknown
        }

        return $this->calculateDistance(
            $this->current_latitude,
            $this->current_longitude,
            $latitude,
            $longitude
        );
    }

    /**
     * Calculate distance between two points using Haversine formula
     */
    protected function calculateDistance(float $lat1, float $lon1, float $lat2, float $lon2): float
    {
        $earthRadius = 6371; // Earth's radius in kilometers

        $dLat = deg2rad($lat2 - $lat1);
        $dLon = deg2rad($lon2 - $lon1);

        $a = sin($dLat / 2) * sin($dLat / 2) +
             cos(deg2rad($lat1)) * cos(deg2rad($lat2)) *
             sin($dLon / 2) * sin($dLon / 2);

        $c = 2 * atan2(sqrt($a), sqrt(1 - $a));

        return $earthRadius * $c;
    }

    /**
     * Get delivery statistics for a period
     */
    public function getDeliveryStats(string $period = 'today'): array
    {
        $query = $this->orders()->where('order_type', 'delivery');

        switch ($period) {
            case 'today':
                $query->whereDate('created_at', today());
                break;
            case 'week':
                $query->whereBetween('created_at', [now()->startOfWeek(), now()->endOfWeek()]);
                break;
            case 'month':
                $query->whereMonth('created_at', now()->month);
                break;
        }

        $orders = $query->get();
        $deliveredOrders = $orders->where('status', 'delivered');

        return [
            'total_orders' => $orders->count(),
            'delivered_orders' => $deliveredOrders->count(),
            'total_earnings' => $this->calculateEarnings($orders),
            'average_delivery_time' => $this->getAverageDeliveryTime($deliveredOrders),
            'distance_traveled' => $this->calculateDistanceTraveled($deliveredOrders),
        ];
    }

    /**
     * Calculate earnings for given orders
     */
    protected function calculateEarnings($orders): float
    {
        $deliveredOrders = $orders->where('status', 'delivered');
        $totalEarnings = 0;

        foreach ($deliveredOrders as $order) {
            // Base delivery fee
            $totalEarnings += $order->delivery_charge;
            
            // Commission on order value
            if ($this->commission_rate > 0) {
                $totalEarnings += ($order->subtotal * $this->commission_rate / 100);
            }
        }

        return $totalEarnings;
    }

    /**
     * Get average delivery time
     */
    protected function getAverageDeliveryTime($orders): int
    {
        if ($orders->isEmpty()) {
            return 0;
        }

        $totalTime = $orders->sum(function ($order) {
            if (!$order->delivered_at) {
                return 0;
            }
            return $order->created_at->diffInMinutes($order->delivered_at);
        });

        return (int) ($totalTime / $orders->count());
    }

    /**
     * Calculate total distance traveled (approximate)
     */
    protected function calculateDistanceTraveled($orders): float
    {
        // This is a simplified calculation
        // In a real system, you'd track GPS points throughout delivery
        $totalDistance = 0;
        $restaurant = $this->restaurant;

        foreach ($orders as $order) {
            if ($order->delivery_latitude && $order->delivery_longitude) {
                // Distance from restaurant to delivery location
                $distance = $this->calculateDistance(
                    $restaurant->latitude ?? 0,
                    $restaurant->longitude ?? 0,
                    $order->delivery_latitude,
                    $order->delivery_longitude
                );
                
                // Round trip
                $totalDistance += ($distance * 2);
            }
        }

        return $totalDistance;
    }

    /**
     * Find best delivery person for an order
     */
    public static function findBestForOrder(Order $order): ?self
    {
        if (!$order->delivery_latitude || !$order->delivery_longitude) {
            return null;
        }

        $availablePersonnel = self::where('is_active', true)
            ->where('status', 'available')
            ->whereHas('deliveryZones', function ($query) use ($order) {
                $query->where('delivery_zones.id', $order->delivery_zone_id);
            })
            ->get()
            ->filter(function ($person) {
                return $person->isAvailable();
            });

        if ($availablePersonnel->isEmpty()) {
            return null;
        }

        // Sort by distance and rating
        return $availablePersonnel->sortBy(function ($person) use ($order) {
            $distance = $person->distanceTo($order->delivery_latitude, $order->delivery_longitude);
            $ratingScore = (5 - $person->rating) * 2; // Lower rating = higher penalty
            
            return $distance + $ratingScore;
        })->first();
    }

    /**
     * Scope for available personnel
     */
    public function scopeAvailable($query)
    {
        return $query->where('is_active', true)->where('status', 'available');
    }

    /**
     * Scope for active personnel
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Boot the model
     */
    protected static function boot()
    {
        parent::boot();

        static::creating(function ($personnel) {
            if (is_null($personnel->max_concurrent_orders)) {
                $personnel->max_concurrent_orders = 3;
            }
            if (is_null($personnel->delivery_radius)) {
                $personnel->delivery_radius = 10; // 10km default
            }
            if (is_null($personnel->rating)) {
                $personnel->rating = 5.0;
            }
            if (is_null($personnel->status)) {
                $personnel->status = 'available';
            }
        });
    }
}
