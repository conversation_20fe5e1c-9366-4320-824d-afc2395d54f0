<?php

namespace App\Models\Tenant;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Carbon\Carbon;

class Budget extends Model
{
    use HasFactory;

    protected $fillable = [
        'restaurant_id',
        'expense_category_id',
        'name',
        'description',
        'budget_type',
        'period_type',
        'start_date',
        'end_date',
        'allocated_amount',
        'spent_amount',
        'remaining_amount',
        'is_active',
        'alert_threshold', // Percentage threshold for alerts
        'created_by',
    ];

    protected $casts = [
        'start_date' => 'date',
        'end_date' => 'date',
        'allocated_amount' => 'decimal:2',
        'spent_amount' => 'decimal:2',
        'remaining_amount' => 'decimal:2',
        'is_active' => 'boolean',
        'alert_threshold' => 'decimal:2',
    ];

    /**
     * Get the restaurant that owns this budget
     */
    public function restaurant(): BelongsTo
    {
        return $this->belongsTo(Restaurant::class);
    }

    /**
     * Get the expense category
     */
    public function category(): BelongsTo
    {
        return $this->belongsTo(ExpenseCategory::class, 'expense_category_id');
    }

    /**
     * Get the user who created this budget
     */
    public function creator(): BelongsTo
    {
        return $this->belongsTo(\App\Models\User::class, 'created_by');
    }

    /**
     * Get budget line items
     */
    public function lineItems(): HasMany
    {
        return $this->hasMany(BudgetLineItem::class);
    }

    /**
     * Get budget alerts
     */
    public function alerts(): HasMany
    {
        return $this->hasMany(BudgetAlert::class);
    }

    /**
     * Calculate utilization percentage
     */
    public function getUtilizationPercentageAttribute(): float
    {
        if ($this->allocated_amount <= 0) {
            return 0;
        }

        return ($this->spent_amount / $this->allocated_amount) * 100;
    }

    /**
     * Check if budget is exceeded
     */
    public function isExceeded(): bool
    {
        return $this->spent_amount > $this->allocated_amount;
    }

    /**
     * Check if alert threshold is reached
     */
    public function isAlertThresholdReached(): bool
    {
        return $this->utilization_percentage >= $this->alert_threshold;
    }

    /**
     * Get budget status
     */
    public function getStatusAttribute(): string
    {
        if ($this->isExceeded()) {
            return 'exceeded';
        } elseif ($this->isAlertThresholdReached()) {
            return 'warning';
        } elseif ($this->utilization_percentage >= 75) {
            return 'caution';
        } else {
            return 'good';
        }
    }

    /**
     * Update spent amount from expenses
     */
    public function updateSpentAmount(): void
    {
        $query = Expense::where('status', 'approved')
            ->whereBetween('expense_date', [$this->start_date, $this->end_date]);

        if ($this->expense_category_id) {
            $query->where('expense_category_id', $this->expense_category_id);
        }

        $spentAmount = $query->sum('total_amount');
        
        $this->update([
            'spent_amount' => $spentAmount,
            'remaining_amount' => $this->allocated_amount - $spentAmount,
        ]);

        // Check for alerts
        $this->checkAlerts();
    }

    /**
     * Check and create alerts if necessary
     */
    protected function checkAlerts(): void
    {
        if (!$this->isAlertThresholdReached()) {
            return;
        }

        // Check if alert already exists for current threshold
        $existingAlert = $this->alerts()
            ->where('alert_type', 'threshold_reached')
            ->where('created_at', '>=', now()->subDay())
            ->exists();

        if (!$existingAlert) {
            $this->alerts()->create([
                'alert_type' => 'threshold_reached',
                'message' => "Budget '{$this->name}' has reached {$this->utilization_percentage}% utilization",
                'threshold_percentage' => $this->utilization_percentage,
                'is_read' => false,
            ]);
        }

        // Check for exceeded budget
        if ($this->isExceeded()) {
            $exceededAlert = $this->alerts()
                ->where('alert_type', 'budget_exceeded')
                ->where('created_at', '>=', now()->subDay())
                ->exists();

            if (!$exceededAlert) {
                $this->alerts()->create([
                    'alert_type' => 'budget_exceeded',
                    'message' => "Budget '{$this->name}' has been exceeded by " . number_format($this->spent_amount - $this->allocated_amount, 2),
                    'threshold_percentage' => $this->utilization_percentage,
                    'is_read' => false,
                ]);
            }
        }
    }

    /**
     * Get budget performance compared to previous period
     */
    public function getPerformanceComparison(): array
    {
        $periodLength = $this->start_date->diffInDays($this->end_date);
        $previousStart = $this->start_date->copy()->subDays($periodLength + 1);
        $previousEnd = $this->start_date->copy()->subDay();

        $query = Expense::where('status', 'approved');
        
        if ($this->expense_category_id) {
            $query->where('expense_category_id', $this->expense_category_id);
        }

        $previousSpent = $query->clone()
            ->whereBetween('expense_date', [$previousStart, $previousEnd])
            ->sum('total_amount');

        $change = $this->spent_amount - $previousSpent;
        $percentageChange = $previousSpent > 0 ? ($change / $previousSpent) * 100 : 0;

        return [
            'current_spent' => $this->spent_amount,
            'previous_spent' => $previousSpent,
            'change' => $change,
            'percentage_change' => $percentageChange,
            'trend' => $change > 0 ? 'up' : ($change < 0 ? 'down' : 'stable'),
        ];
    }

    /**
     * Get daily spending rate
     */
    public function getDailySpendingRateAttribute(): float
    {
        $daysElapsed = $this->start_date->diffInDays(min(now(), $this->end_date)) + 1;
        
        if ($daysElapsed <= 0) {
            return 0;
        }

        return $this->spent_amount / $daysElapsed;
    }

    /**
     * Get projected end amount based on current spending rate
     */
    public function getProjectedEndAmountAttribute(): float
    {
        $totalDays = $this->start_date->diffInDays($this->end_date) + 1;
        return $this->daily_spending_rate * $totalDays;
    }

    /**
     * Check if budget is on track
     */
    public function isOnTrack(): bool
    {
        return $this->projected_end_amount <= $this->allocated_amount;
    }

    /**
     * Get variance from planned spending
     */
    public function getVarianceAttribute(): float
    {
        $daysElapsed = $this->start_date->diffInDays(min(now(), $this->end_date)) + 1;
        $totalDays = $this->start_date->diffInDays($this->end_date) + 1;
        
        $plannedSpending = ($this->allocated_amount / $totalDays) * $daysElapsed;
        
        return $this->spent_amount - $plannedSpending;
    }

    /**
     * Create budget for next period
     */
    public function createNextPeriodBudget(float $adjustmentPercentage = 0): self
    {
        $periodLength = $this->start_date->diffInDays($this->end_date);
        
        $newBudget = $this->replicate();
        $newBudget->start_date = $this->end_date->copy()->addDay();
        $newBudget->end_date = $newBudget->start_date->copy()->addDays($periodLength);
        $newBudget->allocated_amount = $this->allocated_amount * (1 + $adjustmentPercentage / 100);
        $newBudget->spent_amount = 0;
        $newBudget->remaining_amount = $newBudget->allocated_amount;
        $newBudget->name = $this->name . ' - ' . $newBudget->start_date->format('M Y');
        $newBudget->save();

        return $newBudget;
    }

    /**
     * Scope for active budgets
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope for current period budgets
     */
    public function scopeCurrent($query)
    {
        return $query->where('start_date', '<=', now())
                    ->where('end_date', '>=', now());
    }

    /**
     * Scope for exceeded budgets
     */
    public function scopeExceeded($query)
    {
        return $query->whereRaw('spent_amount > allocated_amount');
    }

    /**
     * Scope by budget type
     */
    public function scopeByType($query, string $type)
    {
        return $query->where('budget_type', $type);
    }

    /**
     * Boot the model
     */
    protected static function boot()
    {
        parent::boot();

        static::creating(function ($budget) {
            if (is_null($budget->remaining_amount)) {
                $budget->remaining_amount = $budget->allocated_amount;
            }
            if (is_null($budget->alert_threshold)) {
                $budget->alert_threshold = 80; // Default 80%
            }
        });
    }
}
