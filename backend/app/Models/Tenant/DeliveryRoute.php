<?php

namespace App\Models\Tenant;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

class DeliveryRoute extends Model
{
    use HasFactory;

    protected $fillable = [
        'restaurant_id',
        'delivery_person_id',
        'route_name',
        'status',
        'start_time',
        'end_time',
        'estimated_duration', // in minutes
        'actual_duration', // in minutes
        'total_distance', // in kilometers
        'total_orders',
        'route_coordinates', // JSON array of waypoints
        'optimization_algorithm',
        'fuel_cost_estimate',
        'notes',
    ];

    protected $casts = [
        'start_time' => 'datetime',
        'end_time' => 'datetime',
        'estimated_duration' => 'integer',
        'actual_duration' => 'integer',
        'total_distance' => 'decimal:2',
        'total_orders' => 'integer',
        'route_coordinates' => 'array',
        'fuel_cost_estimate' => 'decimal:2',
    ];

    /**
     * Get the restaurant this route belongs to
     */
    public function restaurant(): BelongsTo
    {
        return $this->belongsTo(Restaurant::class);
    }

    /**
     * Get the delivery person assigned to this route
     */
    public function deliveryPerson(): BelongsTo
    {
        return $this->belongsTo(DeliveryPersonnel::class);
    }

    /**
     * Get all orders in this route
     */
    public function orders(): HasMany
    {
        return $this->hasMany(Order::class, 'delivery_route_id');
    }

    /**
     * Get route stops (delivery locations)
     */
    public function stops(): HasMany
    {
        return $this->hasMany(DeliveryStop::class);
    }

    /**
     * Start the delivery route
     */
    public function start(): void
    {
        $this->update([
            'status' => 'in_progress',
            'start_time' => now(),
        ]);

        // Update delivery person status
        $this->deliveryPerson->update(['status' => 'on_delivery']);

        // Update all orders in route to "out for delivery"
        $this->orders()->update(['status' => 'out_for_delivery']);
    }

    /**
     * Complete the delivery route
     */
    public function complete(): void
    {
        $actualDuration = $this->start_time ? 
            $this->start_time->diffInMinutes(now()) : 0;

        $this->update([
            'status' => 'completed',
            'end_time' => now(),
            'actual_duration' => $actualDuration,
        ]);

        // Update delivery person status
        $this->deliveryPerson->update(['status' => 'available']);
    }

    /**
     * Optimize route using nearest neighbor algorithm
     */
    public function optimizeRoute(): array
    {
        $orders = $this->orders()->with(['customer'])->get();
        
        if ($orders->count() <= 1) {
            return $this->route_coordinates ?? [];
        }

        $restaurant = $this->restaurant;
        $startPoint = [
            'lat' => $restaurant->latitude ?? 0,
            'lng' => $restaurant->longitude ?? 0,
            'type' => 'restaurant',
            'name' => $restaurant->name,
        ];

        $unvisited = $orders->map(function ($order) {
            return [
                'lat' => $order->delivery_latitude,
                'lng' => $order->delivery_longitude,
                'type' => 'delivery',
                'order_id' => $order->id,
                'customer_name' => $order->customer_name,
                'address' => $order->delivery_address,
            ];
        })->toArray();

        $route = [$startPoint];
        $currentPoint = $startPoint;
        $totalDistance = 0;

        // Nearest neighbor algorithm
        while (!empty($unvisited)) {
            $nearestIndex = 0;
            $nearestDistance = $this->calculateDistance(
                $currentPoint['lat'],
                $currentPoint['lng'],
                $unvisited[0]['lat'],
                $unvisited[0]['lng']
            );

            for ($i = 1; $i < count($unvisited); $i++) {
                $distance = $this->calculateDistance(
                    $currentPoint['lat'],
                    $currentPoint['lng'],
                    $unvisited[$i]['lat'],
                    $unvisited[$i]['lng']
                );

                if ($distance < $nearestDistance) {
                    $nearestDistance = $distance;
                    $nearestIndex = $i;
                }
            }

            $nextPoint = $unvisited[$nearestIndex];
            $route[] = $nextPoint;
            $totalDistance += $nearestDistance;
            $currentPoint = $nextPoint;

            array_splice($unvisited, $nearestIndex, 1);
        }

        // Return to restaurant
        $returnDistance = $this->calculateDistance(
            $currentPoint['lat'],
            $currentPoint['lng'],
            $startPoint['lat'],
            $startPoint['lng']
        );
        $route[] = $startPoint;
        $totalDistance += $returnDistance;

        // Update route data
        $this->update([
            'route_coordinates' => $route,
            'total_distance' => $totalDistance,
            'estimated_duration' => $this->estimateRouteDuration($totalDistance, count($orders)),
            'optimization_algorithm' => 'nearest_neighbor',
        ]);

        return $route;
    }

    /**
     * Estimate route duration based on distance and stops
     */
    protected function estimateRouteDuration(float $distance, int $stops): int
    {
        // Base calculation: 30 km/h average speed + 5 minutes per stop
        $drivingTime = ($distance / 30) * 60; // Convert to minutes
        $stopTime = $stops * 5; // 5 minutes per delivery
        
        return (int) ($drivingTime + $stopTime);
    }

    /**
     * Calculate distance between two points using Haversine formula
     */
    protected function calculateDistance(float $lat1, float $lon1, float $lat2, float $lon2): float
    {
        $earthRadius = 6371; // Earth's radius in kilometers

        $dLat = deg2rad($lat2 - $lat1);
        $dLon = deg2rad($lon2 - $lon1);

        $a = sin($dLat / 2) * sin($dLat / 2) +
             cos(deg2rad($lat1)) * cos(deg2rad($lat2)) *
             sin($dLon / 2) * sin($dLon / 2);

        $c = 2 * atan2(sqrt($a), sqrt(1 - $a));

        return $earthRadius * $c;
    }

    /**
     * Get route efficiency metrics
     */
    public function getEfficiencyMetrics(): array
    {
        $directDistance = 0;
        $orders = $this->orders;
        $restaurant = $this->restaurant;

        // Calculate total direct distance (restaurant to each delivery)
        foreach ($orders as $order) {
            $directDistance += $this->calculateDistance(
                $restaurant->latitude ?? 0,
                $restaurant->longitude ?? 0,
                $order->delivery_latitude,
                $order->delivery_longitude
            ) * 2; // Round trip
        }

        $efficiency = $directDistance > 0 ? ($directDistance / $this->total_distance) * 100 : 0;

        return [
            'total_distance' => $this->total_distance,
            'direct_distance' => $directDistance,
            'efficiency_percentage' => min(100, $efficiency),
            'time_saved' => max(0, $this->estimated_duration - $this->actual_duration),
            'fuel_savings' => max(0, ($directDistance - $this->total_distance) * 0.1), // Estimate
        ];
    }

    /**
     * Create optimized routes for multiple orders
     */
    public static function createOptimizedRoutes(array $orderIds, int $maxOrdersPerRoute = 5): array
    {
        $orders = Order::whereIn('id', $orderIds)
            ->where('order_type', 'delivery')
            ->whereIn('status', ['confirmed', 'preparing', 'ready'])
            ->get();

        if ($orders->isEmpty()) {
            return [];
        }

        $routes = [];
        $orderChunks = $orders->chunk($maxOrdersPerRoute);

        foreach ($orderChunks as $chunk) {
            // Find best delivery person for this chunk
            $firstOrder = $chunk->first();
            $deliveryPerson = DeliveryPersonnel::findBestForOrder($firstOrder);

            if (!$deliveryPerson) {
                continue; // Skip if no available delivery person
            }

            // Create route
            $route = self::create([
                'restaurant_id' => $firstOrder->restaurant_id,
                'delivery_person_id' => $deliveryPerson->id,
                'route_name' => 'Route ' . now()->format('Y-m-d H:i'),
                'status' => 'planned',
                'total_orders' => $chunk->count(),
            ]);

            // Assign orders to route
            $chunk->each(function ($order) use ($route) {
                $order->update(['delivery_route_id' => $route->id]);
            });

            // Optimize the route
            $route->optimizeRoute();
            $routes[] = $route;
        }

        return $routes;
    }

    /**
     * Scope for active routes
     */
    public function scopeActive($query)
    {
        return $query->whereIn('status', ['planned', 'in_progress']);
    }

    /**
     * Scope for today's routes
     */
    public function scopeToday($query)
    {
        return $query->whereDate('created_at', today());
    }

    /**
     * Boot the model
     */
    protected static function boot()
    {
        parent::boot();

        static::creating(function ($route) {
            if (empty($route->route_name)) {
                $route->route_name = 'Route ' . now()->format('Y-m-d H:i:s');
            }
            if (empty($route->status)) {
                $route->status = 'planned';
            }
        });
    }
}
