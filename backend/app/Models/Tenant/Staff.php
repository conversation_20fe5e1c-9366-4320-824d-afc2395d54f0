<?php

namespace App\Models\Tenant;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use App\Models\User;

class Staff extends Model
{
    use HasFactory;

    protected $fillable = [
        'restaurant_id',
        'user_id',
        'employee_id',
        'first_name',
        'last_name',
        'phone',
        'email',
        'address',
        'date_of_birth',
        'gender',
        'position',
        'department',
        'salary',
        'salary_type',
        'hire_date',
        'termination_date',
        'employment_status',
        'shift_start',
        'shift_end',
        'working_days',
        'emergency_contact_name',
        'emergency_contact_phone',
        'notes',
        'avatar',
        'is_active',
    ];

    protected $casts = [
        'date_of_birth' => 'date',
        'salary' => 'decimal:2',
        'hire_date' => 'date',
        'termination_date' => 'date',
        'shift_start' => 'datetime:H:i',
        'shift_end' => 'datetime:H:i',
        'working_days' => 'array',
        'is_active' => 'boolean',
    ];

    /**
     * Get the restaurant that owns this staff member
     */
    public function restaurant(): BelongsTo
    {
        return $this->belongsTo(Restaurant::class);
    }

    /**
     * Get the user account for this staff member
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get all orders handled by this staff member
     */
    public function orders(): HasMany
    {
        return $this->hasMany(Order::class);
    }

    /**
     * Get all deliveries handled by this staff member
     */
    public function deliveries(): HasMany
    {
        return $this->hasMany(Order::class, 'delivery_person_id');
    }

    /**
     * Get staff member's full name
     */
    public function getFullNameAttribute(): string
    {
        return $this->first_name . ' ' . $this->last_name;
    }

    /**
     * Check if staff member is currently working
     */
    public function isCurrentlyWorking(): bool
    {
        if (!$this->is_active || $this->employment_status !== 'active') {
            return false;
        }

        $now = now();
        $currentDay = $now->dayOfWeek; // 0 = Sunday, 1 = Monday, etc.
        
        // Check if today is a working day
        if (!in_array($currentDay, $this->working_days ?? [])) {
            return false;
        }

        $currentTime = $now->format('H:i');
        $shiftStart = $this->shift_start?->format('H:i');
        $shiftEnd = $this->shift_end?->format('H:i');

        if (!$shiftStart || !$shiftEnd) {
            return true; // No shift restrictions
        }

        return $currentTime >= $shiftStart && $currentTime <= $shiftEnd;
    }

    /**
     * Check if staff member can take orders
     */
    public function canTakeOrders(): bool
    {
        return in_array($this->position, ['Manager', 'Waiter', 'Cashier']) && $this->isCurrentlyWorking();
    }

    /**
     * Check if staff member can prepare food
     */
    public function canPrepareFood(): bool
    {
        return in_array($this->position, ['Chef', 'Cook', 'Kitchen Staff']) && $this->isCurrentlyWorking();
    }

    /**
     * Check if staff member can deliver orders
     */
    public function canDeliver(): bool
    {
        return in_array($this->position, ['Delivery', 'Driver']) && $this->isCurrentlyWorking();
    }

    /**
     * Get monthly salary
     */
    public function getMonthlySalary(): float
    {
        switch ($this->salary_type) {
            case 'hourly':
                // Assume 8 hours/day, 22 working days/month
                return $this->salary * 8 * 22;
            case 'daily':
                // Assume 22 working days/month
                return $this->salary * 22;
            case 'monthly':
            default:
                return $this->salary;
        }
    }

    /**
     * Get working days as text
     */
    public function getWorkingDaysTextAttribute(): string
    {
        $days = ['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday'];
        $workingDays = [];
        
        foreach ($this->working_days ?? [] as $day) {
            $workingDays[] = $days[$day] ?? '';
        }
        
        return implode(', ', $workingDays);
    }

    /**
     * Scope for active staff
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true)->where('employment_status', 'active');
    }

    /**
     * Scope by position
     */
    public function scopeByPosition($query, $position)
    {
        return $query->where('position', $position);
    }

    /**
     * Scope for currently working staff
     */
    public function scopeCurrentlyWorking($query)
    {
        return $query->where('is_active', true)
            ->where('employment_status', 'active');
            // Note: Additional time-based filtering would need to be done in PHP
    }
}
