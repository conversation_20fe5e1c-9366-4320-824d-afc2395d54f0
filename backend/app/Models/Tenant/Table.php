<?php

namespace App\Models\Tenant;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use App\Traits\HasMediaLibrary;

class Table extends Model
{
    use HasFactory, HasMediaLibrary;

    protected $fillable = [
        'restaurant_id',
        'branch_id',
        'floor_id',
        'name',
        'table_number',
        'qr_code',
        'capacity',
        'min_capacity',
        'location',
        'shape',
        'description',
        'status',
        'assigned_waiter_id',
        'current_party_size',
        'occupied_at',
        'table_type',
        'position_x',
        'position_y',
        'width',
        'height',
        'rotation',
        'has_power_outlet',
        'has_window_view',
        'is_wheelchair_accessible',
        'is_high_top',
        'is_booth',
        'is_outdoor',
        'is_active',
        'is_reservable',
        'sort_order',
    ];

    protected $casts = [
        'capacity' => 'integer',
        'min_capacity' => 'integer',
        'current_party_size' => 'integer',
        'occupied_at' => 'datetime',
        'position_x' => 'decimal:2',
        'position_y' => 'decimal:2',
        'width' => 'decimal:2',
        'height' => 'decimal:2',
        'rotation' => 'integer',
        'has_power_outlet' => 'boolean',
        'has_window_view' => 'boolean',
        'is_wheelchair_accessible' => 'boolean',
        'is_high_top' => 'boolean',
        'is_booth' => 'boolean',
        'is_outdoor' => 'boolean',
        'is_active' => 'boolean',
        'is_reservable' => 'boolean',
        'sort_order' => 'integer',
    ];

    /**
     * Get the restaurant that owns this table
     */
    public function restaurant(): BelongsTo
    {
        return $this->belongsTo(Restaurant::class)->withDefault();
    }

    /**
     * Get the branch that owns this table
     */
    public function branch(): BelongsTo
    {
        return $this->belongsTo(Branch::class);
    }

    /**
     * Get the floor that owns this table
     */
    public function floor(): BelongsTo
    {
        return $this->belongsTo(Floor::class);
    }

    /**
     * Get all orders for this table
     */
    public function orders(): HasMany
    {
        return $this->hasMany(Order::class);
    }

    /**
     * Get all reservations for this table
     */
    public function reservations(): HasMany
    {
        return $this->hasMany(Reservation::class);
    }

    /**
     * Get active reservations for this table
     */
    public function activeReservations(): HasMany
    {
        return $this->hasMany(Reservation::class)->active();
    }

    /**
     * Get current reservation for this table (today's active reservation)
     */
    public function reservation()
    {
        return $this->hasOne(TableReservation::class)
            ->whereDate('reservation_date', today())
            ->whereIn('status', ['confirmed', 'seated']);
    }

    /**
     * Get the assigned waiter for this table
     */
    public function assignedWaiter()
    {
        return $this->belongsTo(User::class, 'assigned_waiter_id');
    }

    /**
     * Get current order for this table (as relationship)
     */
    public function currentOrder()
    {
        return $this->hasOne(Order::class)
            ->whereIn('status', ['pending', 'confirmed', 'preparing', 'ready'])
            ->latest();
    }

    /**
     * Get current order for this table (as method)
     */
    public function getCurrentOrder()
    {
        return $this->orders()
            ->whereIn('status', ['pending', 'confirmed', 'preparing', 'ready'])
            ->latest()
            ->first();
    }

    /**
     * Check if table is available
     */
    public function isAvailable(): bool
    {
        return $this->status === 'available' && $this->is_active;
    }

    /**
     * Check if table is occupied
     */
    public function isOccupied(): bool
    {
        return $this->status === 'occupied';
    }

    /**
     * Generate QR code for table
     */
    public function generateQrCode(): string
    {
        if (!$this->qr_code) {
            $this->qr_code = 'QR-' . strtoupper(uniqid());
            $this->save();
        }

        return $this->qr_code;
    }

    /**
     * Get QR code URL
     */
    public function getQrUrlAttribute(): string
    {
        return route('guest.table.qr', $this);
    }

    /**
     * Scope for active tables
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope for available tables
     */
    public function scopeAvailable($query)
    {
        return $query->where('status', 'available');
    }

    /**
     * Scope for ordered tables
     */
    public function scopeOrdered($query)
    {
        return $query->orderBy('sort_order')->orderBy('name');
    }

    /**
     * Check if table can accommodate party size
     */
    public function canAccommodate(int $partySize): bool
    {
        return $partySize >= $this->min_capacity && $partySize <= $this->capacity;
    }

    /**
     * Get table features as array
     */
    public function getFeaturesAttribute(): array
    {
        $features = [];

        if ($this->has_power_outlet) $features[] = 'Power Outlet';
        if ($this->has_window_view) $features[] = 'Window View';
        if ($this->is_wheelchair_accessible) $features[] = 'Wheelchair Accessible';
        if ($this->is_high_top) $features[] = 'High Top';
        if ($this->is_booth) $features[] = 'Booth';
        if ($this->is_outdoor) $features[] = 'Outdoor';

        return $features;
    }

    /**
     * Get table type color class for UI
     */
    public function getTypeColorClass(): string
    {
        return match($this->table_type ?? 'regular') {
            'regular' => 'bg-blue-500',
            'vip' => 'bg-purple-500',
            'outdoor' => 'bg-green-500',
            'private' => 'bg-red-500',
            'bar' => 'bg-yellow-500',
            default => 'bg-gray-500',
        };
    }

    /**
     * Get table type icon for UI
     */
    public function getTypeIcon(): string
    {
        return match($this->table_type ?? 'regular') {
            'regular' => 'fa-chair',
            'vip' => 'fa-crown',
            'outdoor' => 'fa-tree',
            'private' => 'fa-lock',
            'bar' => 'fa-wine-glass',
            default => 'fa-chair',
        };
    }
}
