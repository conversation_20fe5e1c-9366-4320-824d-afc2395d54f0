<?php

namespace App\Models\Tenant;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Carbon\Carbon;

class LeaveRequest extends Model
{
    use HasFactory;

    protected $fillable = [
        'employee_id',
        'leave_type_id',
        'start_date',
        'end_date',
        'total_days',
        'reason',
        'comments',
        'status',
        'rejection_reason',
        'admin_notes',
        'is_half_day',
        'half_day_period',
        'attachments',
        'approved_by',
        'approved_at',
        'rejected_by',
        'rejected_at',
        'emergency_contact_name',
        'emergency_contact_phone',
    ];

    protected $casts = [
        'start_date' => 'date',
        'end_date' => 'date',
        'total_days' => 'integer',
        'is_half_day' => 'boolean',
        'attachments' => 'array',
        'approved_at' => 'datetime',
        'rejected_at' => 'datetime',
    ];

    const STATUS_PENDING = 'pending';
    const STATUS_APPROVED = 'approved';
    const STATUS_REJECTED = 'rejected';
    const STATUS_CANCELLED = 'cancelled';

    const HALF_DAY_MORNING = 'morning';
    const HALF_DAY_AFTERNOON = 'afternoon';

    /**
     * Get the employee that owns this leave request.
     */
    public function employee(): BelongsTo
    {
        return $this->belongsTo(Employee::class);
    }

    /**
     * Get the leave type for this request.
     */
    public function leaveType(): BelongsTo
    {
        return $this->belongsTo(LeaveType::class);
    }

    /**
     * Get the employee who approved this request.
     */
    public function approver(): BelongsTo
    {
        return $this->belongsTo(Employee::class, 'approved_by');
    }

    /**
     * Get the employee who rejected this request.
     */
    public function rejector(): BelongsTo
    {
        return $this->belongsTo(Employee::class, 'rejected_by');
    }

    /**
     * Check if the request is pending.
     */
    public function isPending(): bool
    {
        return $this->status === self::STATUS_PENDING;
    }

    /**
     * Check if the request is approved.
     */
    public function isApproved(): bool
    {
        return $this->status === self::STATUS_APPROVED;
    }

    /**
     * Check if the request is rejected.
     */
    public function isRejected(): bool
    {
        return $this->status === self::STATUS_REJECTED;
    }

    /**
     * Check if the request is cancelled.
     */
    public function isCancelled(): bool
    {
        return $this->status === self::STATUS_CANCELLED;
    }

    /**
     * Approve the leave request.
     */
    public function approve(Employee $approver, string $notes = null): void
    {
        $this->update([
            'status' => self::STATUS_APPROVED,
            'approved_by' => $approver->id,
            'approved_at' => now(),
            'admin_notes' => $notes,
        ]);

        // Convert pending days to used days in entitlement
        $entitlement = EmployeeLeaveEntitlement::where([
            'employee_id' => $this->employee_id,
            'leave_type_id' => $this->leave_type_id,
            'year' => $this->start_date->year,
        ])->first();

        if ($entitlement) {
            $entitlement->consumeDays($this->total_days);
        }
    }

    /**
     * Reject the leave request.
     */
    public function reject(Employee $rejector, string $reason): void
    {
        $this->update([
            'status' => self::STATUS_REJECTED,
            'rejected_by' => $rejector->id,
            'rejected_at' => now(),
            'rejection_reason' => $reason,
        ]);

        // Release reserved days in entitlement
        $entitlement = EmployeeLeaveEntitlement::where([
            'employee_id' => $this->employee_id,
            'leave_type_id' => $this->leave_type_id,
            'year' => $this->start_date->year,
        ])->first();

        if ($entitlement) {
            $entitlement->releaseDays($this->total_days);
        }
    }

    /**
     * Cancel the leave request.
     */
    public function cancel(): void
    {
        if ($this->isPending()) {
            // Release reserved days
            $entitlement = EmployeeLeaveEntitlement::where([
                'employee_id' => $this->employee_id,
                'leave_type_id' => $this->leave_type_id,
                'year' => $this->start_date->year,
            ])->first();

            if ($entitlement) {
                $entitlement->releaseDays($this->total_days);
            }
        }

        $this->update(['status' => self::STATUS_CANCELLED]);
    }

    /**
     * Calculate working days between start and end date.
     */
    public static function calculateWorkingDays(Carbon $startDate, Carbon $endDate, bool $isHalfDay = false): int
    {
        $days = 0;
        $current = $startDate->copy();

        while ($current->lte($endDate)) {
            // Skip weekends (Saturday = 6, Sunday = 0)
            if (!$current->isWeekend()) {
                $days++;
            }
            $current->addDay();
        }

        return $isHalfDay ? 0.5 : $days;
    }

    /**
     * Scope to get requests by status.
     */
    public function scopeByStatus($query, string $status)
    {
        return $query->where('status', $status);
    }

    /**
     * Scope to get pending requests.
     */
    public function scopePending($query)
    {
        return $query->where('status', self::STATUS_PENDING);
    }

    /**
     * Scope to get approved requests.
     */
    public function scopeApproved($query)
    {
        return $query->where('status', self::STATUS_APPROVED);
    }
}
