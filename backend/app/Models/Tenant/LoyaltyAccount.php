<?php

namespace App\Models\Tenant;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Support\Facades\DB;

class LoyaltyAccount extends Model
{
    use HasFactory;

    protected $fillable = [
        'phone_number',
        'customer_name',
        'address',
        'email',
        'date_of_birth',
        'total_points',
        'lifetime_points_earned',
        'lifetime_points_redeemed',
        'tier',
        'total_spent',
        'total_orders',
        'last_transaction_at',
        'last_order_at',
        'is_active',
        'notes',
    ];

    protected $casts = [
        'date_of_birth' => 'date',
        'total_points' => 'integer',
        'lifetime_points_earned' => 'integer',
        'lifetime_points_redeemed' => 'integer',
        'total_spent' => 'decimal:2',
        'total_orders' => 'integer',
        'last_transaction_at' => 'datetime',
        'last_order_at' => 'datetime',
        'is_active' => 'boolean',
    ];

    /**
     * Get loyalty transactions for this account
     */
    public function transactions(): HasMany
    {
        return $this->hasMany(LoyaltyTransaction::class);
    }

    /**
     * Get orders associated with this loyalty account
     */
    public function orders(): HasMany
    {
        return $this->hasMany(Order::class, 'customer_phone', 'phone_number');
    }

    /**
     * Find or create loyalty account by phone number
     */
    public static function findOrCreateByPhone(string $phoneNumber, array $customerData = []): self
    {
        $phoneNumber = static::normalizePhoneNumber($phoneNumber);
        
        return static::firstOrCreate(
            ['phone_number' => $phoneNumber],
            array_merge([
                'customer_name' => $customerData['name'] ?? 'Guest Customer',
                'email' => $customerData['email'] ?? null,
                'address' => $customerData['address'] ?? null,
                'is_active' => true,
            ], $customerData)
        );
    }

    /**
     * Normalize phone number format
     */
    public static function normalizePhoneNumber(string $phoneNumber): string
    {
        // Remove all non-numeric characters
        $cleaned = preg_replace('/[^0-9]/', '', $phoneNumber);
        
        // Add country code if missing (assuming Bangladesh +880)
        if (strlen($cleaned) === 11 && substr($cleaned, 0, 2) === '01') {
            $cleaned = '880' . $cleaned;
        } elseif (strlen($cleaned) === 10) {
            $cleaned = '8801' . $cleaned;
        }
        
        return $cleaned;
    }

    /**
     * Add points to account
     */
    public function addPoints(int $points, string $type, string $description, ?int $orderId = null, array $metadata = []): LoyaltyTransaction
    {
        return DB::transaction(function () use ($points, $type, $description, $orderId, $metadata) {
            // Create transaction record
            $transaction = $this->transactions()->create([
                'order_id' => $orderId,
                'transaction_type' => $type,
                'points' => $points,
                'description' => $description,
                'metadata' => $metadata,
                'expires_at' => $this->calculateExpiryDate(),
            ]);

            // Update account totals
            $this->increment('total_points', $points);
            $this->increment('lifetime_points_earned', $points);
            $this->update(['last_transaction_at' => now()]);

            // Update tier if needed
            $this->updateTier();

            return $transaction;
        });
    }

    /**
     * Redeem points from account
     */
    public function redeemPoints(int $points, string $description, ?int $orderId = null): LoyaltyTransaction
    {
        if ($points > $this->total_points) {
            throw new \Exception('Insufficient points balance');
        }

        return DB::transaction(function () use ($points, $description, $orderId) {
            // Create transaction record
            $transaction = $this->transactions()->create([
                'order_id' => $orderId,
                'transaction_type' => 'redeemed',
                'points' => -$points, // Negative for redemption
                'description' => $description,
            ]);

            // Update account totals
            $this->decrement('total_points', $points);
            $this->increment('lifetime_points_redeemed', $points);
            $this->update(['last_transaction_at' => now()]);

            return $transaction;
        });
    }

    /**
     * Refund points to account
     */
    public function refundPoints(int $points, string $description, ?int $orderId = null): LoyaltyTransaction
    {
        return DB::transaction(function () use ($points, $description, $orderId) {
            $transaction = $this->transactions()->create([
                'order_id' => $orderId,
                'transaction_type' => 'refunded',
                'points' => $points,
                'description' => $description,
            ]);

            $this->increment('total_points', $points);
            $this->update(['last_transaction_at' => now()]);

            return $transaction;
        });
    }

    /**
     * Update customer tier based on lifetime points
     */
    public function updateTier(): void
    {
        $settings = LoyaltySettings::getInstance();
        $newTier = $settings->getTierForPoints($this->lifetime_points_earned);
        
        if ($newTier !== $this->tier) {
            $this->update(['tier' => $newTier]);
        }
    }

    /**
     * Calculate expiry date for points
     */
    protected function calculateExpiryDate(): ?string
    {
        $settings = LoyaltySettings::getInstance();
        
        if ($settings->points_expiry_days) {
            return now()->addDays($settings->points_expiry_days)->toDateTimeString();
        }
        
        return null;
    }

    /**
     * Check if today is customer's birthday
     */
    public function isBirthdayToday(): bool
    {
        if (!$this->date_of_birth) {
            return false;
        }

        return $this->date_of_birth->format('m-d') === now()->format('m-d');
    }

    /**
     * Get available points (excluding expired)
     */
    public function getAvailablePointsAttribute(): int
    {
        // For now, return total_points
        // In future, can implement expiry logic
        return $this->total_points;
    }

    /**
     * Get tier badge color
     */
    public function getTierColorAttribute(): string
    {
        return match ($this->tier) {
            'bronze' => '#CD7F32',
            'silver' => '#C0C0C0',
            'gold' => '#FFD700',
            'platinum' => '#E5E4E2',
            default => '#6B7280', // Gray for regular
        };
    }
}
