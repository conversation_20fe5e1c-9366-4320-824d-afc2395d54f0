<?php

namespace App\Models\Tenant;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

class Coupon extends Model
{
    use HasFactory;

    protected $fillable = [
        'restaurant_id',
        'code',
        'name',
        'description',
        'type',
        'value',
        'maximum_discount',
        'minimum_order_amount',
        'maximum_order_amount',
        'usage_limit',
        'usage_limit_per_customer',
        'used_count',
        'valid_from',
        'valid_until',
        'applicable_order_types',
        'applicable_days',
        'applicable_from',
        'applicable_until',
        'customer_eligibility',
        'eligible_customer_ids',
        'eligible_customer_tiers',
        'applicable_food_ids',
        'applicable_category_ids',
        'excluded_food_ids',
        'excluded_category_ids',
        'buy_quantity',
        'get_quantity',
        'buy_food_id',
        'get_food_id',
        'is_active',
        'is_stackable',
        'auto_apply',
        'total_discount_given',
        'total_revenue_generated',
    ];

    protected $casts = [
        'value' => 'decimal:2',
        'maximum_discount' => 'decimal:2',
        'minimum_order_amount' => 'decimal:2',
        'maximum_order_amount' => 'decimal:2',
        'valid_from' => 'datetime',
        'valid_until' => 'datetime',
        'applicable_order_types' => 'array',
        'applicable_days' => 'array',
        'applicable_from' => 'datetime:H:i',
        'applicable_until' => 'datetime:H:i',
        'eligible_customer_ids' => 'array',
        'eligible_customer_tiers' => 'array',
        'applicable_food_ids' => 'array',
        'applicable_category_ids' => 'array',
        'excluded_food_ids' => 'array',
        'excluded_category_ids' => 'array',
        'is_active' => 'boolean',
        'is_stackable' => 'boolean',
        'auto_apply' => 'boolean',
        'total_discount_given' => 'decimal:2',
        'total_revenue_generated' => 'decimal:2',
    ];

    /**
     * Get the restaurant that owns this coupon
     */
    public function restaurant(): BelongsTo
    {
        return $this->belongsTo(Restaurant::class);
    }

    /**
     * Get the buy food item (for buy_x_get_y coupons)
     */
    public function buyFood(): BelongsTo
    {
        return $this->belongsTo(Food::class, 'buy_food_id');
    }

    /**
     * Get the get food item (for buy_x_get_y coupons)
     */
    public function getFood(): BelongsTo
    {
        return $this->belongsTo(Food::class, 'get_food_id');
    }

    /**
     * Get the coupon usage records
     */
    public function usageRecords(): HasMany
    {
        return $this->hasMany(CouponUsage::class);
    }

    /**
     * Check if coupon is valid for use
     */
    public function isValid(): bool
    {
        if (!$this->is_active) {
            return false;
        }

        // Check date validity
        $now = now();
        if ($now < $this->valid_from || $now > $this->valid_until) {
            return false;
        }

        // Check usage limit
        if ($this->usage_limit && $this->used_count >= $this->usage_limit) {
            return false;
        }

        // Check day availability
        if ($this->applicable_days && !in_array($now->dayOfWeek, $this->applicable_days)) {
            return false;
        }

        // Check time availability
        if ($this->applicable_from && $this->applicable_until) {
            $currentTime = $now->format('H:i');
            if ($currentTime < $this->applicable_from || $currentTime > $this->applicable_until) {
                return false;
            }
        }

        return true;
    }

    /**
     * Check if coupon is valid for a specific customer
     */
    public function isValidForCustomer(?Customer $customer): bool
    {
        if (!$this->isValid()) {
            return false;
        }

        // Check customer eligibility
        switch ($this->customer_eligibility) {
            case 'new_customers':
                if (!$customer || $customer->total_orders > 0) {
                    return false;
                }
                break;
            case 'existing_customers':
                if (!$customer || $customer->total_orders === 0) {
                    return false;
                }
                break;
            case 'specific_customers':
                if (!$customer || !in_array($customer->id, $this->eligible_customer_ids ?? [])) {
                    return false;
                }
                break;
        }

        // Check customer tier eligibility
        if ($customer && $this->eligible_customer_tiers && 
            !in_array($customer->customer_tier, $this->eligible_customer_tiers)) {
            return false;
        }

        // Check per-customer usage limit
        if ($customer && $this->usage_limit_per_customer) {
            $customerUsage = $this->usageRecords()->where('customer_id', $customer->id)->count();
            if ($customerUsage >= $this->usage_limit_per_customer) {
                return false;
            }
        }

        return true;
    }

    /**
     * Check if coupon is valid for an order
     */
    public function isValidForOrder(Order $order): bool
    {
        if (!$this->isValidForCustomer($order->customer)) {
            return false;
        }

        // Check order type
        if ($this->applicable_order_types && 
            !in_array($order->order_type, $this->applicable_order_types)) {
            return false;
        }

        // Check minimum order amount
        if ($order->subtotal < $this->minimum_order_amount) {
            return false;
        }

        // Check maximum order amount
        if ($this->maximum_order_amount && $order->subtotal > $this->maximum_order_amount) {
            return false;
        }

        return true;
    }

    /**
     * Calculate discount for an order
     */
    public function calculateDiscount(Order $order): float
    {
        if (!$this->isValidForOrder($order)) {
            return 0;
        }

        switch ($this->type) {
            case 'percentage':
                $discount = ($order->subtotal * $this->value) / 100;
                return $this->maximum_discount ? min($discount, $this->maximum_discount) : $discount;

            case 'fixed_amount':
                return min($this->value, $order->subtotal);

            case 'free_delivery':
                return $order->delivery_fee ?? 0;

            case 'buy_x_get_y':
                return $this->calculateBuyXGetYDiscount($order);

            default:
                return 0;
        }
    }

    /**
     * Calculate buy X get Y discount
     */
    protected function calculateBuyXGetYDiscount(Order $order): float
    {
        if (!$this->buy_food_id || !$this->get_food_id) {
            return 0;
        }

        $buyItem = $order->items()->where('food_id', $this->buy_food_id)->first();
        $getItem = $order->items()->where('food_id', $this->get_food_id)->first();

        if (!$buyItem || $buyItem->quantity < $this->buy_quantity) {
            return 0;
        }

        $freeQuantity = min(
            floor($buyItem->quantity / $this->buy_quantity) * $this->get_quantity,
            $getItem?->quantity ?? 0
        );

        return $freeQuantity * ($getItem?->unit_price ?? $this->getFood->current_price);
    }

    /**
     * Apply coupon to an order
     */
    public function applyToOrder(Order $order): bool
    {
        $discount = $this->calculateDiscount($order);

        if ($discount <= 0) {
            return false;
        }

        // Create usage record
        $this->usageRecords()->create([
            'restaurant_id' => $this->restaurant_id,
            'order_id' => $order->id,
            'customer_id' => $order->customer_id,
            'discount_amount' => $discount,
            'order_amount_before_discount' => $order->subtotal,
            'order_amount_after_discount' => $order->subtotal - $discount,
        ]);

        // Update coupon statistics
        $this->increment('used_count');
        $this->increment('total_discount_given', $discount);
        $this->increment('total_revenue_generated', $order->total_amount);

        return true;
    }

    /**
     * Get usage percentage
     */
    public function getUsagePercentageAttribute(): float
    {
        if (!$this->usage_limit) {
            return 0;
        }

        return ($this->used_count / $this->usage_limit) * 100;
    }

    /**
     * Get type label
     */
    public function getTypeLabelAttribute(): string
    {
        return match ($this->type) {
            'percentage' => 'Percentage Discount',
            'fixed_amount' => 'Fixed Amount',
            'free_delivery' => 'Free Delivery',
            'buy_x_get_y' => 'Buy X Get Y',
            default => ucfirst($this->type),
        };
    }

    /**
     * Scope for active coupons
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope for valid coupons (within date range)
     */
    public function scopeValid($query)
    {
        $now = now();
        return $query->where('valid_from', '<=', $now)
            ->where('valid_until', '>=', $now);
    }

    /**
     * Scope for auto-apply coupons
     */
    public function scopeAutoApply($query)
    {
        return $query->where('auto_apply', true);
    }

    /**
     * Scope for coupons by type
     */
    public function scopeByType($query, $type)
    {
        return $query->where('type', $type);
    }
}
