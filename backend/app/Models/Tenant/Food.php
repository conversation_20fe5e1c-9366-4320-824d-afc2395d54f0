<?php

namespace App\Models\Tenant;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Support\Str;

class Food extends Model
{
    use HasFactory;
    protected $table = 'foods';

    protected $fillable = [
        'restaurant_id',
        'food_category_id',
        'name',
        'slug',
        'description',
        'ingredients',
        'images',
        'base_price',
        'sale_price',
        'cost_price',
        'calories',
        'protein',
        'carbs',
        'fat',
        'fiber',
        'sugar',
        'sodium',
        'is_vegetarian',
        'is_vegan',
        'is_gluten_free',
        'is_dairy_free',
        'is_nut_free',
        'is_spicy',
        'is_halal',
        'is_kosher',
        'preparation_time',
        'cooking_time',
        'spice_level',
        'kitchen_notes',
        'sort_order',
        'is_featured',
        'is_popular',
        'is_new',
        'is_available',
        'is_active',
        'available_days',
        'available_from',
        'available_until',
        'track_quantity',
        'quantity_available',
        'minimum_quantity',
        'meta_title',
        'meta_description',
        'meta_keywords',
        'view_count',
        'order_count',
        'average_rating',
        'review_count',
    ];

    protected $casts = [
        'images' => 'array',
        'base_price' => 'decimal:2',
        'sale_price' => 'decimal:2',
        'cost_price' => 'decimal:2',
        'protein' => 'decimal:2',
        'carbs' => 'decimal:2',
        'fat' => 'decimal:2',
        'fiber' => 'decimal:2',
        'sugar' => 'decimal:2',
        'sodium' => 'decimal:2',
        'is_vegetarian' => 'boolean',
        'is_vegan' => 'boolean',
        'is_gluten_free' => 'boolean',
        'is_dairy_free' => 'boolean',
        'is_nut_free' => 'boolean',
        'is_spicy' => 'boolean',
        'is_halal' => 'boolean',
        'is_kosher' => 'boolean',
        'is_featured' => 'boolean',
        'is_popular' => 'boolean',
        'is_new' => 'boolean',
        'is_available' => 'boolean',
        'is_active' => 'boolean',
        'available_days' => 'array',
        'available_from' => 'datetime:H:i',
        'available_until' => 'datetime:H:i',
        'track_quantity' => 'boolean',
        'meta_keywords' => 'array',
        'average_rating' => 'decimal:2',
    ];

    /**
     * Boot the model
     */
    protected static function boot()
    {
        parent::boot();

        static::creating(function ($food) {
            if (empty($food->slug)) {
                $food->slug = Str::slug($food->name);
            }
        });

        static::updating(function ($food) {
            if ($food->isDirty('name') && empty($food->slug)) {
                $food->slug = Str::slug($food->name);
            }
        });
    }

    /**
     * Get the restaurant that owns this food
     */
    public function restaurant(): BelongsTo
    {
        return $this->belongsTo(Restaurant::class);
    }

    /**
     * Get the category this food belongs to
     */
    public function category(): BelongsTo
    {
        return $this->belongsTo(FoodCategory::class, 'food_category_id');
    }

    /**
     * Get the food variants (legacy - use MenuItem variations instead)
     */
    public function variants(): HasMany
    {
        return $this->hasMany(MenuItemVariation::class, 'menu_item_id');
    }

    /**
     * Get the available addons for this food
     */
    public function addons(): BelongsToMany
    {
        return $this->belongsToMany(FoodAddon::class, 'food_addon_pivot')
            ->withPivot(['is_required', 'max_quantity', 'price_override'])
            ->withTimestamps();
    }

    /**
     * Get the food reviews
     */
    public function reviews(): HasMany
    {
        return $this->hasMany(FoodReview::class);
    }

    /**
     * Get the order items for this food
     */
    public function orderItems(): HasMany
    {
        return $this->hasMany(OrderItem::class);
    }

    /**
     * Get the current price (sale price if available, otherwise base price)
     */
    public function getCurrentPriceAttribute(): float
    {
        return $this->sale_price ?? $this->base_price;
    }

    /**
     * Get the discount percentage if on sale
     */
    public function getDiscountPercentageAttribute(): ?float
    {
        if ($this->sale_price && $this->sale_price < $this->base_price) {
            return round((($this->base_price - $this->sale_price) / $this->base_price) * 100, 2);
        }
        return null;
    }

    /**
     * Get the primary image
     */
    public function getPrimaryImageAttribute(): ?string
    {
        return $this->images[0] ?? null;
    }

    /**
     * Get dietary flags as array
     */
    public function getDietaryFlagsAttribute(): array
    {
        $flags = [];
        
        if ($this->is_vegetarian) $flags[] = 'Vegetarian';
        if ($this->is_vegan) $flags[] = 'Vegan';
        if ($this->is_gluten_free) $flags[] = 'Gluten Free';
        if ($this->is_dairy_free) $flags[] = 'Dairy Free';
        if ($this->is_nut_free) $flags[] = 'Nut Free';
        if ($this->is_halal) $flags[] = 'Halal';
        if ($this->is_kosher) $flags[] = 'Kosher';
        
        return $flags;
    }

    /**
     * Check if food is available now
     */
    public function isAvailableNow(): bool
    {
        if (!$this->is_active || !$this->is_available) {
            return false;
        }

        // Check quantity if tracking
        if ($this->track_quantity && $this->quantity_available <= 0) {
            return false;
        }

        // Check day availability
        if ($this->available_days && !in_array(now()->dayOfWeek, $this->available_days)) {
            return false;
        }

        // Check time availability
        if ($this->available_from && $this->available_until) {
            $now = now()->format('H:i');
            return $now >= $this->available_from && $now <= $this->available_until;
        }

        return true;
    }

    /**
     * Increment view count
     */
    public function incrementViewCount(): void
    {
        $this->increment('view_count');
    }

    /**
     * Update rating after review
     */
    public function updateRating(): void
    {
        $avgRating = $this->reviews()->where('status', 'approved')->avg('rating');
        $reviewCount = $this->reviews()->where('status', 'approved')->count();
        
        $this->update([
            'average_rating' => $avgRating ?? 0,
            'review_count' => $reviewCount,
        ]);
    }

    /**
     * Scope for active foods
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope for available foods
     */
    public function scopeAvailable($query)
    {
        return $query->where('is_available', true);
    }

    /**
     * Scope for featured foods
     */
    public function scopeFeatured($query)
    {
        return $query->where('is_featured', true);
    }

    /**
     * Scope for popular foods
     */
    public function scopePopular($query)
    {
        return $query->where('is_popular', true);
    }

    /**
     * Scope for new foods
     */
    public function scopeNew($query)
    {
        return $query->where('is_new', true);
    }

    /**
     * Scope for vegetarian foods
     */
    public function scopeVegetarian($query)
    {
        return $query->where('is_vegetarian', true);
    }

    /**
     * Scope for vegan foods
     */
    public function scopeVegan($query)
    {
        return $query->where('is_vegan', true);
    }

    /**
     * Scope for ordering by sort order
     */
    public function scopeOrdered($query)
    {
        return $query->orderBy('sort_order')->orderBy('name');
    }
}
