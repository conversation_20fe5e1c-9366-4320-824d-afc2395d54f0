<?php

namespace App\Models\Tenant;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class PaymentMethodField extends Model
{
    use HasFactory;

    protected $fillable = [
        'payment_method_id',
        'field_name',
        'field_type',
        'field_label',
        'is_required',
        'default_value',
        'validation_rules',
        'select_options',
        'display_order',
        'placeholder',
        'help_text',
    ];

    protected $casts = [
        'is_required' => 'boolean',
        'validation_rules' => 'array',
        'select_options' => 'array',
        'display_order' => 'integer',
    ];

    /**
     * Field types
     */
    const FIELD_TYPES = [
        'text' => 'Text',
        'number' => 'Number',
        'email' => 'Email',
        'tel' => 'Phone',
        'date' => 'Date',
        'textarea' => 'Textarea',
        'select' => 'Select',
        'checkbox' => 'Checkbox',
    ];

    /**
     * Get the payment method that owns this field
     */
    public function paymentMethod(): BelongsTo
    {
        return $this->belongsTo(PaymentMethod::class);
    }

    /**
     * Get the field type label
     */
    public function getFieldTypeLabelAttribute()
    {
        return self::FIELD_TYPES[$this->field_type] ?? $this->field_type;
    }

    /**
     * Get the validation rules as an array
     */
    public function getValidationRulesArrayAttribute()
    {
        return is_array($this->validation_rules) ? $this->validation_rules : [];
    }

    /**
     * Get the select options as an array
     */
    public function getSelectOptionsArrayAttribute()
    {
        return is_array($this->select_options) ? $this->select_options : [];
    }

    /**
     * Scope to order by display order
     */
    public function scopeOrdered($query)
    {
        return $query->orderBy('display_order')->orderBy('field_label');
    }

    /**
     * Generate HTML input attributes
     */
    public function getInputAttributesAttribute()
    {
        $attributes = [
            'type' => $this->getHtmlInputType(),
            'name' => $this->field_name,
            'id' => $this->field_name,
            'placeholder' => $this->placeholder,
            'required' => $this->is_required,
        ];

        if ($this->default_value) {
            $attributes['value'] = $this->default_value;
        }

        // Add validation attributes
        $rules = $this->validation_rules_array;
        
        if (isset($rules['min_length'])) {
            $attributes['minlength'] = $rules['min_length'];
        }
        
        if (isset($rules['max_length'])) {
            $attributes['maxlength'] = $rules['max_length'];
        }
        
        if (isset($rules['min']) && $this->field_type === 'number') {
            $attributes['min'] = $rules['min'];
        }
        
        if (isset($rules['max']) && $this->field_type === 'number') {
            $attributes['max'] = $rules['max'];
        }
        
        if (isset($rules['step']) && $this->field_type === 'number') {
            $attributes['step'] = $rules['step'];
        }
        
        if (isset($rules['pattern'])) {
            $attributes['pattern'] = $rules['pattern'];
        }

        return $attributes;
    }

    /**
     * Get the HTML input type
     */
    private function getHtmlInputType()
    {
        $typeMap = [
            'text' => 'text',
            'number' => 'number',
            'email' => 'email',
            'tel' => 'tel',
            'date' => 'date',
            'textarea' => 'textarea',
            'select' => 'select',
            'checkbox' => 'checkbox',
        ];

        return $typeMap[$this->field_type] ?? 'text';
    }

    /**
     * Validate a value against this field's rules
     */
    public function validateValue($value)
    {
        $errors = [];

        // Check required
        if ($this->is_required && empty($value)) {
            $errors[] = "The {$this->field_label} field is required.";
            return $errors;
        }

        // Skip further validation if empty and not required
        if (empty($value)) {
            return $errors;
        }

        // Type-specific validation
        switch ($this->field_type) {
            case 'email':
                if (!filter_var($value, FILTER_VALIDATE_EMAIL)) {
                    $errors[] = "The {$this->field_label} must be a valid email address.";
                }
                break;

            case 'number':
                if (!is_numeric($value)) {
                    $errors[] = "The {$this->field_label} must be a number.";
                }
                break;

            case 'tel':
                if (!preg_match('/^[\+]?[0-9\s\-\(\)]+$/', $value)) {
                    $errors[] = "The {$this->field_label} must be a valid phone number.";
                }
                break;

            case 'date':
                if (!strtotime($value)) {
                    $errors[] = "The {$this->field_label} must be a valid date.";
                }
                break;

            case 'select':
                $options = $this->select_options_array;
                $validValues = array_column($options, 'value');
                if (!in_array($value, $validValues)) {
                    $errors[] = "The selected {$this->field_label} is invalid.";
                }
                break;
        }

        // Custom validation rules
        $rules = $this->validation_rules_array;

        if (isset($rules['min_length']) && strlen($value) < $rules['min_length']) {
            $errors[] = "The {$this->field_label} must be at least {$rules['min_length']} characters.";
        }

        if (isset($rules['max_length']) && strlen($value) > $rules['max_length']) {
            $errors[] = "The {$this->field_label} must not exceed {$rules['max_length']} characters.";
        }

        if (isset($rules['pattern']) && !preg_match($rules['pattern'], $value)) {
            $errors[] = "The {$this->field_label} format is invalid.";
        }

        if ($this->field_type === 'number') {
            $numValue = floatval($value);
            
            if (isset($rules['min']) && $numValue < $rules['min']) {
                $errors[] = "The {$this->field_label} must be at least {$rules['min']}.";
            }
            
            if (isset($rules['max']) && $numValue > $rules['max']) {
                $errors[] = "The {$this->field_label} must not exceed {$rules['max']}.";
            }
        }

        return $errors;
    }

    /**
     * Get the default value for this field
     */
    public function getDefaultValueForType()
    {
        if ($this->default_value) {
            return $this->default_value;
        }

        switch ($this->field_type) {
            case 'checkbox':
                return false;
            case 'number':
                return 0;
            case 'select':
                $options = $this->select_options_array;
                return $options[0]['value'] ?? '';
            default:
                return '';
        }
    }
}
