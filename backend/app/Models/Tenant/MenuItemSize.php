<?php

namespace App\Models\Tenant;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class MenuItemSize extends Model
{
    use HasFactory;

    protected $table = 'menu_item_variations';

    protected $fillable = [
        'menu_item_id',
        'name',
        'name_bn',
        'price_modifier',
        'is_available',
        'sort_order',
        'description',
        'description_bn',
    ];

    protected $casts = [
        'price_modifier' => 'decimal:2',
        'is_available' => 'boolean',
        'sort_order' => 'integer',
    ];

    /**
     * Get the menu item that owns this variation.
     */
    public function menuItem(): BelongsTo
    {
        return $this->belongsTo(MenuItem::class);
    }

    /**
     * Scope to get only available variations.
     */
    public function scopeAvailable($query)
    {
        return $query->where('is_available', true);
    }

    /**
     * Scope to order by sort order.
     */
    public function scopeOrdered($query)
    {
        return $query->orderBy('sort_order')->orderBy('name');
    }

    /**
     * Get the localized name based on current locale.
     */
    public function getLocalizedNameAttribute(): string
    {
        $locale = app()->getLocale();
        return $locale === 'bn' && $this->name_bn ? $this->name_bn : $this->name;
    }

    /**
     * Get the localized description based on current locale.
     */
    public function getLocalizedDescriptionAttribute(): ?string
    {
        $locale = app()->getLocale();
        return $locale === 'bn' && $this->description_bn ? $this->description_bn : $this->description;
    }

    /**
     * Calculate the final price for this variation.
     */
    public function getFinalPriceAttribute(): float
    {
        return $this->menuItem->price + $this->price_modifier;
    }
}
