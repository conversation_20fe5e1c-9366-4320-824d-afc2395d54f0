<?php

namespace App\Models\Tenant;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class SupplierPaymentSummary extends Model
{
    use HasFactory;

    protected $fillable = [
        'branch_id',
        'supplier_id',
        'total_outstanding',
        'total_paid',
        'total_overdue',
        'overdue_days',
        'last_payment_date',
        'last_payment_amount',
    ];

    protected $casts = [
        'total_outstanding' => 'decimal:2',
        'total_paid' => 'decimal:2',
        'total_overdue' => 'decimal:2',
        'last_payment_date' => 'date',
        'last_payment_amount' => 'decimal:2',
    ];

    /**
     * Get the branch that owns this summary
     */
    public function branch(): BelongsTo
    {
        return $this->belongsTo(Branch::class);
    }

    /**
     * Get the supplier this summary is for
     */
    public function supplier(): BelongsTo
    {
        return $this->belongsTo(Vendor::class, 'supplier_id');
    }

    /**
     * Get payment status
     */
    public function getPaymentStatusAttribute(): string
    {
        if ($this->total_overdue > 0) {
            return 'overdue';
        } elseif ($this->total_outstanding > 0) {
            return 'outstanding';
        } else {
            return 'current';
        }
    }

    /**
     * Get status color
     */
    public function getStatusColorAttribute(): string
    {
        return match ($this->payment_status) {
            'overdue' => '#EF4444', // red
            'outstanding' => '#F59E0B', // yellow
            'current' => '#10B981', // green
            default => '#6B7280',
        };
    }

    /**
     * Calculate payment percentage
     */
    public function getPaymentPercentageAttribute(): float
    {
        $total = $this->total_paid + $this->total_outstanding;
        if ($total <= 0) {
            return 100;
        }
        return ($this->total_paid / $total) * 100;
    }

    /**
     * Update summary from payments and orders
     */
    public function updateSummary(): void
    {
        $supplier = $this->supplier;
        $branch = $this->branch;

        // Calculate total outstanding from unpaid purchase orders and expenses
        $outstandingPOs = PurchaseOrder::where('branch_id', $branch->id)
            ->where('vendor_id', $supplier->id)
            ->whereIn('status', ['confirmed', 'delivered', 'invoiced'])
            ->sum('total_amount');

        $outstandingExpenses = Expense::where('branch_id', $branch->id)
            ->where('supplier_id', $supplier->id)
            ->where('status', 'approved')
            ->whereNull('paid_at')
            ->sum('total_amount');

        $totalOutstanding = $outstandingPOs + $outstandingExpenses;

        // Calculate total paid
        $totalPaid = PaymentHistory::where('branch_id', $branch->id)
            ->where('supplier_id', $supplier->id)
            ->where('status', 'completed')
            ->sum('amount');

        // Calculate overdue amounts
        $overduePOs = PurchaseOrder::where('branch_id', $branch->id)
            ->where('vendor_id', $supplier->id)
            ->whereIn('status', ['confirmed', 'delivered', 'invoiced'])
            ->where('expected_delivery_date', '<', now()->subDays(30))
            ->sum('total_amount');

        $overdueExpenses = Expense::where('branch_id', $branch->id)
            ->where('supplier_id', $supplier->id)
            ->where('status', 'approved')
            ->whereNull('paid_at')
            ->where('due_date', '<', now())
            ->sum('total_amount');

        $totalOverdue = $overduePOs + $overdueExpenses;

        // Get last payment info
        $lastPayment = PaymentHistory::where('branch_id', $branch->id)
            ->where('supplier_id', $supplier->id)
            ->where('status', 'completed')
            ->orderBy('payment_date', 'desc')
            ->first();

        // Calculate overdue days
        $overdueDays = 0;
        if ($totalOverdue > 0) {
            $oldestOverdue = Expense::where('branch_id', $branch->id)
                ->where('supplier_id', $supplier->id)
                ->where('status', 'approved')
                ->whereNull('paid_at')
                ->where('due_date', '<', now())
                ->orderBy('due_date')
                ->first();

            if ($oldestOverdue) {
                $overdueDays = now()->diffInDays($oldestOverdue->due_date);
            }
        }

        // Update the summary
        $this->update([
            'total_outstanding' => $totalOutstanding,
            'total_paid' => $totalPaid,
            'total_overdue' => $totalOverdue,
            'overdue_days' => $overdueDays,
            'last_payment_date' => $lastPayment?->payment_date,
            'last_payment_amount' => $lastPayment?->amount,
        ]);
    }

    /**
     * Scope for overdue suppliers
     */
    public function scopeOverdue($query)
    {
        return $query->where('total_overdue', '>', 0);
    }

    /**
     * Scope for suppliers with outstanding balances
     */
    public function scopeOutstanding($query)
    {
        return $query->where('total_outstanding', '>', 0);
    }

    /**
     * Scope for current suppliers (no overdue)
     */
    public function scopeCurrent($query)
    {
        return $query->where('total_overdue', '<=', 0);
    }

    /**
     * Get or create summary for supplier and branch
     */
    public static function getOrCreateForSupplier(int $branchId, int $supplierId): self
    {
        return static::firstOrCreate(
            ['branch_id' => $branchId, 'supplier_id' => $supplierId],
            [
                'total_outstanding' => 0,
                'total_paid' => 0,
                'total_overdue' => 0,
                'overdue_days' => 0,
            ]
        );
    }
}
