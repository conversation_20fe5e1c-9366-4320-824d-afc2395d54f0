<?php

namespace App\Models\Tenant;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Database\Eloquent\Relations\HasMany;

class PaymentMethod extends Model
{
    use HasFactory, SoftDeletes;

    protected $fillable = [
        'name',
        'type',
        'instructions',
        'is_active',
        'display_order',
        'icon',
        'image',
    ];

    protected $casts = [
        'is_active' => 'boolean',
        'display_order' => 'integer',
    ];

    /**
     * Payment method types
     */
    const TYPES = [
        'cash' => 'Cash',
        'card' => 'Credit/Debit Card',
        'bank_transfer' => 'Bank Transfer',
        'digital_wallet' => 'Digital Wallet',
        'other' => 'Other',
    ];

    /**
     * Get the payment method fields
     */
    public function fields(): HasMany
    {
        return $this->hasMany(PaymentMethodField::class)->orderBy('display_order');
    }

    /**
     * Get the orders using this payment method
     */
    public function orders(): HasMany
    {
        return $this->hasMany(Order::class);
    }

    /**
     * Scope to get only active payment methods
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope to order by display order
     */
    public function scopeOrdered($query)
    {
        return $query->orderBy('display_order')->orderBy('name');
    }

    /**
     * Get the type label
     */
    public function getTypeLabelAttribute()
    {
        return self::TYPES[$this->type] ?? $this->type;
    }

    /**
     * Get the icon URL
     */
    public function getIconUrlAttribute()
    {
        if ($this->icon) {
            return asset('storage/' . $this->icon);
        }
        
        // Return default icons based on type
        $defaultIcons = [
            'cash' => '/images/payment-icons/cash.svg',
            'card' => '/images/payment-icons/card.svg',
            'bank_transfer' => '/images/payment-icons/bank.svg',
            'digital_wallet' => '/images/payment-icons/wallet.svg',
            'other' => '/images/payment-icons/other.svg',
        ];
        
        return $defaultIcons[$this->type] ?? $defaultIcons['other'];
    }

    /**
     * Get the image URL
     */
    public function getImageUrlAttribute()
    {
        if ($this->image) {
            return asset('storage/' . $this->image);
        }
        
        return null;
    }

    /**
     * Validate payment details against the payment method fields
     */
    public function validatePaymentDetails(array $paymentDetails): array
    {
        $errors = [];
        
        foreach ($this->fields as $field) {
            $value = $paymentDetails[$field->field_name] ?? null;
            
            // Check required fields
            if ($field->is_required && empty($value)) {
                $errors[$field->field_name] = "The {$field->field_label} field is required.";
                continue;
            }
            
            // Skip validation if field is empty and not required
            if (empty($value)) {
                continue;
            }
            
            // Validate based on field type
            switch ($field->field_type) {
                case 'email':
                    if (!filter_var($value, FILTER_VALIDATE_EMAIL)) {
                        $errors[$field->field_name] = "The {$field->field_label} must be a valid email address.";
                    }
                    break;
                    
                case 'number':
                    if (!is_numeric($value)) {
                        $errors[$field->field_name] = "The {$field->field_label} must be a number.";
                    }
                    break;
                    
                case 'tel':
                    // Basic phone validation
                    if (!preg_match('/^[\+]?[0-9\s\-\(\)]+$/', $value)) {
                        $errors[$field->field_name] = "The {$field->field_label} must be a valid phone number.";
                    }
                    break;
                    
                case 'date':
                    if (!strtotime($value)) {
                        $errors[$field->field_name] = "The {$field->field_label} must be a valid date.";
                    }
                    break;
            }
            
            // Apply custom validation rules if any
            if ($field->validation_rules) {
                $rules = json_decode($field->validation_rules, true);
                
                if (isset($rules['min_length']) && strlen($value) < $rules['min_length']) {
                    $errors[$field->field_name] = "The {$field->field_label} must be at least {$rules['min_length']} characters.";
                }
                
                if (isset($rules['max_length']) && strlen($value) > $rules['max_length']) {
                    $errors[$field->field_name] = "The {$field->field_label} must not exceed {$rules['max_length']} characters.";
                }
                
                if (isset($rules['pattern']) && !preg_match($rules['pattern'], $value)) {
                    $errors[$field->field_name] = "The {$field->field_label} format is invalid.";
                }
            }
        }
        
        return $errors;
    }

    /**
     * Get formatted payment details for display
     */
    public function formatPaymentDetails(array $paymentDetails): array
    {
        $formatted = [];
        
        foreach ($this->fields as $field) {
            $value = $paymentDetails[$field->field_name] ?? null;
            
            if ($value !== null) {
                $formatted[] = [
                    'label' => $field->field_label,
                    'value' => $value,
                    'type' => $field->field_type,
                ];
            }
        }
        
        return $formatted;
    }

    /**
     * Create default payment methods
     */
    public static function createDefaults()
    {
        $defaults = [
            [
                'name' => 'Cash',
                'type' => 'cash',
                'instructions' => 'Pay with cash at the counter or to the delivery person.',
                'is_active' => true,
                'display_order' => 1,
            ],
            [
                'name' => 'Credit/Debit Card',
                'type' => 'card',
                'instructions' => 'Pay securely with your credit or debit card.',
                'is_active' => true,
                'display_order' => 2,
            ],
            [
                'name' => 'Bank Transfer',
                'type' => 'bank_transfer',
                'instructions' => 'Transfer payment directly to our bank account.',
                'is_active' => true,
                'display_order' => 3,
            ],
            [
                'name' => 'Digital Wallet',
                'type' => 'digital_wallet',
                'instructions' => 'Pay using your preferred digital wallet.',
                'is_active' => true,
                'display_order' => 4,
            ],
        ];

        foreach ($defaults as $default) {
            if (!self::where('name', $default['name'])->exists()) {
                self::create($default);
            }
        }
    }
}
