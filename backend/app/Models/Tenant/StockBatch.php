<?php

namespace App\Models\Tenant;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Carbon\Carbon;

class StockBatch extends Model
{
    use HasFactory;

    protected $fillable = [
        'restaurant_id',
        'inventory_item_id',
        'batch_number',
        'quantity',
        'original_quantity',
        'unit_cost',
        'expiry_date',
        'received_date',
        'supplier_batch_number',
        'notes',
    ];

    protected $casts = [
        'quantity' => 'decimal:2',
        'original_quantity' => 'decimal:2',
        'unit_cost' => 'decimal:4',
        'expiry_date' => 'date',
        'received_date' => 'date',
    ];

    /**
     * Get the restaurant that owns this stock batch
     */
    public function restaurant(): BelongsTo
    {
        return $this->belongsTo(Restaurant::class);
    }

    /**
     * Get the inventory item
     */
    public function inventoryItem(): BelongsTo
    {
        return $this->belongsTo(InventoryItem::class);
    }

    /**
     * Check if batch is expired
     */
    public function isExpired(): bool
    {
        return $this->expiry_date && $this->expiry_date->isPast();
    }

    /**
     * Check if batch is expiring soon
     */
    public function isExpiringSoon(int $days = 7): bool
    {
        if (!$this->expiry_date) {
            return false;
        }

        return $this->expiry_date->isBetween(now(), now()->addDays($days));
    }

    /**
     * Get days until expiry
     */
    public function getDaysUntilExpiryAttribute(): ?int
    {
        if (!$this->expiry_date) {
            return null;
        }

        return now()->diffInDays($this->expiry_date, false);
    }

    /**
     * Get batch status
     */
    public function getStatusAttribute(): string
    {
        if ($this->quantity <= 0) {
            return 'depleted';
        } elseif ($this->isExpired()) {
            return 'expired';
        } elseif ($this->isExpiringSoon()) {
            return 'expiring_soon';
        } else {
            return 'active';
        }
    }

    /**
     * Get batch value
     */
    public function getBatchValueAttribute(): float
    {
        return $this->quantity * $this->unit_cost;
    }

    /**
     * Get usage percentage
     */
    public function getUsagePercentageAttribute(): float
    {
        if ($this->original_quantity <= 0) {
            return 0;
        }

        $used = $this->original_quantity - $this->quantity;
        return ($used / $this->original_quantity) * 100;
    }

    /**
     * Get shelf life remaining percentage
     */
    public function getShelfLifeRemainingPercentageAttribute(): ?float
    {
        if (!$this->expiry_date || !$this->received_date) {
            return null;
        }

        $totalShelfLife = $this->received_date->diffInDays($this->expiry_date);
        $remainingShelfLife = now()->diffInDays($this->expiry_date, false);

        if ($totalShelfLife <= 0) {
            return 0;
        }

        return max(0, ($remainingShelfLife / $totalShelfLife) * 100);
    }

    /**
     * Mark batch as waste
     */
    public function markAsWaste(string $reason = 'expired', float $quantity = null): void
    {
        $wasteQuantity = $quantity ?? $this->quantity;
        
        if ($wasteQuantity > $this->quantity) {
            $wasteQuantity = $this->quantity;
        }

        // Create waste record
        WasteRecord::create([
            'restaurant_id' => $this->restaurant_id,
            'inventory_item_id' => $this->inventory_item_id,
            'batch_number' => $this->batch_number,
            'quantity' => $wasteQuantity,
            'unit_cost' => $this->unit_cost,
            'total_cost' => $wasteQuantity * $this->unit_cost,
            'reason' => $reason,
            'waste_date' => now(),
            'recorded_by' => auth()->id(),
        ]);

        // Update batch quantity
        $this->decrement('quantity', $wasteQuantity);

        // Update inventory item stock
        $this->inventoryItem->decrement('current_stock', $wasteQuantity);

        // Create stock movement
        $this->inventoryItem->stockMovements()->create([
            'restaurant_id' => $this->restaurant_id,
            'movement_type' => 'out',
            'quantity' => $wasteQuantity,
            'unit_cost' => $this->unit_cost,
            'total_cost' => $wasteQuantity * $this->unit_cost,
            'reason' => 'waste',
            'batch_number' => $this->batch_number,
            'performed_by' => auth()->id(),
            'notes' => "Waste recorded: {$reason}",
        ]);
    }

    /**
     * Scope for active batches (quantity > 0)
     */
    public function scopeActive($query)
    {
        return $query->where('quantity', '>', 0);
    }

    /**
     * Scope for expired batches
     */
    public function scopeExpired($query)
    {
        return $query->where('expiry_date', '<', now());
    }

    /**
     * Scope for batches expiring soon
     */
    public function scopeExpiringSoon($query, int $days = 7)
    {
        return $query->whereBetween('expiry_date', [now(), now()->addDays($days)]);
    }

    /**
     * Scope for batches by expiry date range
     */
    public function scopeExpiryDateRange($query, $startDate, $endDate)
    {
        return $query->whereBetween('expiry_date', [$startDate, $endDate]);
    }

    /**
     * Boot the model
     */
    protected static function boot()
    {
        parent::boot();

        static::creating(function ($batch) {
            if (is_null($batch->original_quantity)) {
                $batch->original_quantity = $batch->quantity;
            }
            if (is_null($batch->received_date)) {
                $batch->received_date = now();
            }
        });
    }
}
