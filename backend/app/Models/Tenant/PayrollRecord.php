<?php

namespace App\Models\Tenant;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Carbon\Carbon;

class PayrollRecord extends Model
{
    use HasFactory;

    protected $fillable = [
        'employee_id',
        'payroll_number',
        'pay_period_start',
        'pay_period_end',
        'pay_date',
        'pay_frequency',
        'base_salary',
        'hourly_rate',
        'regular_hours',
        'overtime_hours',
        'overtime_rate_multiplier',
        'regular_pay',
        'overtime_pay',
        'bonus',
        'commission',
        'allowances',
        'gross_pay',
        'tax_deduction',
        'insurance_deduction',
        'loan_deductions',
        'advance_deductions',
        'other_deductions',
        'total_deductions',
        'net_pay',
        'status',
        'notes',
        'earnings_breakdown',
        'deductions_breakdown',
        'approved_by',
        'approved_at',
        'paid_by',
        'paid_at',
    ];

    protected $casts = [
        'pay_period_start' => 'date',
        'pay_period_end' => 'date',
        'pay_date' => 'date',
        'base_salary' => 'decimal:2',
        'hourly_rate' => 'decimal:2',
        'regular_hours' => 'decimal:2',
        'overtime_hours' => 'decimal:2',
        'overtime_rate_multiplier' => 'decimal:2',
        'regular_pay' => 'decimal:2',
        'overtime_pay' => 'decimal:2',
        'bonus' => 'decimal:2',
        'commission' => 'decimal:2',
        'allowances' => 'decimal:2',
        'gross_pay' => 'decimal:2',
        'tax_deduction' => 'decimal:2',
        'insurance_deduction' => 'decimal:2',
        'loan_deductions' => 'decimal:2',
        'advance_deductions' => 'decimal:2',
        'other_deductions' => 'decimal:2',
        'total_deductions' => 'decimal:2',
        'net_pay' => 'decimal:2',
        'earnings_breakdown' => 'array',
        'deductions_breakdown' => 'array',
        'approved_at' => 'datetime',
        'paid_at' => 'datetime',
    ];

    const STATUS_DRAFT = 'draft';
    const STATUS_APPROVED = 'approved';
    const STATUS_PAID = 'paid';
    const STATUS_CANCELLED = 'cancelled';

    const FREQUENCY_WEEKLY = 'weekly';
    const FREQUENCY_BI_WEEKLY = 'bi_weekly';
    const FREQUENCY_MONTHLY = 'monthly';

    protected static function boot()
    {
        parent::boot();

        static::creating(function ($payroll) {
            if (!$payroll->payroll_number) {
                $payroll->payroll_number = static::generatePayrollNumber();
            }
        });
    }

    /**
     * Get the employee that owns this payroll record.
     */
    public function employee(): BelongsTo
    {
        return $this->belongsTo(Employee::class);
    }

    /**
     * Get the employee who approved this payroll.
     */
    public function approver(): BelongsTo
    {
        return $this->belongsTo(Employee::class, 'approved_by');
    }

    /**
     * Get the employee who marked this as paid.
     */
    public function paidBy(): BelongsTo
    {
        return $this->belongsTo(Employee::class, 'paid_by');
    }

    /**
     * Get the loan payments associated with this payroll.
     */
    public function loanPayments(): HasMany
    {
        return $this->hasMany(LoanPayment::class);
    }

    /**
     * Generate a unique payroll number.
     */
    public static function generatePayrollNumber(): string
    {
        $prefix = 'PAY';
        $year = now()->year;
        $month = now()->format('m');

        $lastPayroll = static::whereYear('created_at', $year)
                            ->whereMonth('created_at', $month)
                            ->orderBy('id', 'desc')
                            ->first();

        $sequence = $lastPayroll ? (int) substr($lastPayroll->payroll_number, -4) + 1 : 1;

        return sprintf('%s%s%s%04d', $prefix, $year, $month, $sequence);
    }

    /**
     * Calculate payroll based on employee data and attendance.
     */
    public function calculatePayroll(): void
    {
        $employee = $this->employee;

        // Get attendance records for the pay period
        $attendanceRecords = AttendanceRecord::where('employee_id', $employee->id)
            ->whereBetween('date', [$this->pay_period_start, $this->pay_period_end])
            ->get();

        $totalRegularHours = $attendanceRecords->sum('regular_hours');
        $totalOvertimeHours = $attendanceRecords->sum('overtime_hours');

        // Calculate earnings
        if ($employee->employment_type === 'hourly') {
            $this->hourly_rate = $employee->hourly_rate;
            $this->regular_hours = $totalRegularHours;
            $this->overtime_hours = $totalOvertimeHours;
            $this->overtime_rate_multiplier = $employee->overtime_rate_multiplier ?? 1.5;

            $this->regular_pay = $this->regular_hours * $this->hourly_rate;
            $this->overtime_pay = $this->overtime_hours * $this->hourly_rate * $this->overtime_rate_multiplier;
        } else {
            // Salaried employee
            $this->base_salary = $employee->salary;
            $this->regular_pay = $this->base_salary;

            // Calculate overtime pay for salaried employees if applicable
            if ($totalOvertimeHours > 0) {
                $hourlyEquivalent = $this->base_salary / (30 * 8); // Assuming 30 days, 8 hours per day
                $this->overtime_pay = $totalOvertimeHours * $hourlyEquivalent * ($employee->overtime_rate_multiplier ?? 1.5);
            }
        }

        // Calculate gross pay
        $this->gross_pay = $this->regular_pay + $this->overtime_pay + $this->bonus + $this->commission + $this->allowances;

        // Calculate deductions
        $this->calculateDeductions();

        // Calculate net pay
        $this->net_pay = $this->gross_pay - $this->total_deductions;
    }

    /**
     * Calculate deductions.
     */
    public function calculateDeductions(): void
    {
        // Calculate tax (simplified - 10% of gross pay over 50,000 BDT annually)
        $annualGross = $this->gross_pay * 12;
        $this->tax_deduction = $annualGross > 50000 ? ($this->gross_pay * 0.10) : 0;

        // Calculate loan deductions
        $this->loan_deductions = $this->calculateLoanDeductions();

        // Total deductions
        $this->total_deductions = $this->tax_deduction +
                                 $this->insurance_deduction +
                                 $this->loan_deductions +
                                 $this->advance_deductions +
                                 $this->other_deductions;
    }

    /**
     * Calculate loan deductions for this pay period.
     */
    public function calculateLoanDeductions(): float
    {
        $activeLoans = EmployeeLoan::where('employee_id', $this->employee_id)
            ->active()
            ->dueForPayment($this->pay_date)
            ->get();

        $totalDeduction = 0;

        foreach ($activeLoans as $loan) {
            $deductionAmount = min($loan->monthly_installment, $loan->outstanding_balance);
            $totalDeduction += $deductionAmount;

            // Create loan payment record
            $loan->payments()->create([
                'payroll_record_id' => $this->id,
                'payment_amount' => $deductionAmount,
                'principal_amount' => $deductionAmount,
                'interest_amount' => 0,
                'payment_date' => $this->pay_date,
                'due_date' => $loan->next_payment_date,
                'payment_method' => LoanPayment::METHOD_SALARY_DEDUCTION,
                'status' => LoanPayment::STATUS_PAID,
            ]);

            // Update loan balance
            $loan->updateBalanceAfterPayment($deductionAmount);
        }

        return $totalDeduction;
    }

    /**
     * Approve the payroll record.
     */
    public function approve(Employee $approver, string $notes = null): void
    {
        $this->update([
            'status' => self::STATUS_APPROVED,
            'approved_by' => $approver->id,
            'approved_at' => now(),
            'notes' => $notes,
        ]);
    }

    /**
     * Mark payroll as paid.
     */
    public function markAsPaid(Employee $paidBy, string $notes = null): void
    {
        $this->update([
            'status' => self::STATUS_PAID,
            'paid_by' => $paidBy->id,
            'paid_at' => now(),
            'notes' => $notes,
        ]);
    }

    /**
     * Check if payroll is editable.
     */
    public function isEditable(): bool
    {
        return $this->status === self::STATUS_DRAFT;
    }

    /**
     * Get formatted pay period.
     */
    public function getPayPeriodAttribute(): string
    {
        return $this->pay_period_start->format('M d') . ' - ' . $this->pay_period_end->format('M d, Y');
    }

    /**
     * Scope to get payroll records by status.
     */
    public function scopeByStatus($query, string $status)
    {
        return $query->where('status', $status);
    }

    /**
     * Scope to get draft payroll records.
     */
    public function scopeDraft($query)
    {
        return $query->where('status', self::STATUS_DRAFT);
    }

    /**
     * Scope to get approved payroll records.
     */
    public function scopeApproved($query)
    {
        return $query->where('status', self::STATUS_APPROVED);
    }

    /**
     * Scope to get paid payroll records.
     */
    public function scopePaid($query)
    {
        return $query->where('status', self::STATUS_PAID);
    }
}
