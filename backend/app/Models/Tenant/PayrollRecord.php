<?php

namespace App\Models\Tenant;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Carbon\Carbon;

class PayrollRecord extends Model
{
    use HasFactory;

    protected $fillable = [
        'restaurant_id',
        'employee_id',
        'pay_period_start',
        'pay_period_end',
        'regular_hours',
        'overtime_hours',
        'regular_pay',
        'overtime_pay',
        'gross_pay',
        'tax_deductions',
        'other_deductions',
        'net_pay',
        'status',
        'processed_by',
        'processed_at',
        'notes',
    ];

    protected $casts = [
        'pay_period_start' => 'date',
        'pay_period_end' => 'date',
        'regular_hours' => 'decimal:2',
        'overtime_hours' => 'decimal:2',
        'regular_pay' => 'decimal:2',
        'overtime_pay' => 'decimal:2',
        'gross_pay' => 'decimal:2',
        'tax_deductions' => 'decimal:2',
        'other_deductions' => 'decimal:2',
        'net_pay' => 'decimal:2',
        'processed_at' => 'datetime',
    ];

    /**
     * Get the restaurant that owns this payroll record
     */
    public function restaurant(): BelongsTo
    {
        return $this->belongsTo(Restaurant::class);
    }

    /**
     * Get the employee
     */
    public function employee(): BelongsTo
    {
        return $this->belongsTo(Employee::class);
    }

    /**
     * Get the user who processed this payroll
     */
    public function processor(): BelongsTo
    {
        return $this->belongsTo(\App\Models\User::class, 'processed_by');
    }

    /**
     * Calculate total deductions
     */
    public function getTotalDeductionsAttribute(): float
    {
        return $this->tax_deductions + $this->other_deductions;
    }

    /**
     * Get status label
     */
    public function getStatusLabelAttribute(): string
    {
        switch ($this->status) {
            case 'draft':
                return 'Draft';
            case 'processed':
                return 'Processed';
            case 'paid':
                return 'Paid';
            case 'cancelled':
                return 'Cancelled';
            default:
                return ucfirst($this->status);
        }
    }

    /**
     * Scope for processed payroll records
     */
    public function scopeProcessed($query)
    {
        return $query->where('status', 'processed');
    }

    /**
     * Scope for paid payroll records
     */
    public function scopePaid($query)
    {
        return $query->where('status', 'paid');
    }

    /**
     * Boot the model
     */
    protected static function boot()
    {
        parent::boot();

        static::creating(function ($payroll) {
            if (empty($payroll->status)) {
                $payroll->status = 'draft';
            }
        });
    }
}
