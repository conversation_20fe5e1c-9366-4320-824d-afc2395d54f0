<?php

namespace App\Models\Tenant;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;

class LeaveType extends Model
{
    use HasFactory;

    protected $fillable = [
        'name',
        'description',
        'default_days_per_year',
        'is_paid',
        'requires_approval',
        'requires_medical_certificate',
        'max_consecutive_days',
        'min_notice_days',
        'max_carry_forward_days',
        'is_active',
        'color_code',
        'sort_order',
    ];

    protected $casts = [
        'is_paid' => 'boolean',
        'requires_approval' => 'boolean',
        'requires_medical_certificate' => 'boolean',
        'is_active' => 'boolean',
        'default_days_per_year' => 'integer',
        'max_consecutive_days' => 'integer',
        'min_notice_days' => 'integer',
        'max_carry_forward_days' => 'integer',
        'sort_order' => 'integer',
    ];

    /**
     * Get the leave entitlements for this leave type.
     */
    public function entitlements(): HasMany
    {
        return $this->hasMany(EmployeeLeaveEntitlement::class);
    }

    /**
     * Get the leave requests for this leave type.
     */
    public function leaveRequests(): HasMany
    {
        return $this->hasMany(LeaveRequest::class);
    }

    /**
     * Scope to get only active leave types.
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope to order by sort order.
     */
    public function scopeOrdered($query)
    {
        return $query->orderBy('sort_order')->orderBy('name');
    }

    /**
     * Check if this leave type requires medical certificate for given days.
     */
    public function requiresMedicalCertificate(int $days = 1): bool
    {
        return $this->requires_medical_certificate && $days >= 3; // Usually 3+ days require certificate
    }

    /**
     * Check if the requested days exceed maximum consecutive days.
     */
    public function exceedsMaxConsecutiveDays(int $days): bool
    {
        return $this->max_consecutive_days && $days > $this->max_consecutive_days;
    }

    /**
     * Check if the notice period is sufficient.
     */
    public function hasSufficientNotice(\Carbon\Carbon $requestDate, \Carbon\Carbon $startDate): bool
    {
        $noticeDays = $requestDate->diffInDays($startDate);
        return $noticeDays >= $this->min_notice_days;
    }
}
