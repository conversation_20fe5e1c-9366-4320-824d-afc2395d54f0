<?php

namespace App\Models\Tenant;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;

class MenuItemAddon extends Model
{
    use HasFactory;

    protected $fillable = [
        'name',
        'name_bn',
        'price',
        'category',
        'category_bn',
        'max_quantity',
        'is_available',
        'sort_order',
        'description',
        'description_bn',
    ];

    protected $casts = [
        'price' => 'decimal:2',
        'max_quantity' => 'integer',
        'is_available' => 'boolean',
        'sort_order' => 'integer',
    ];

    /**
     * Get the menu items that can use this addon.
     */
    public function menuItems(): BelongsToMany
    {
        return $this->belongsToMany(MenuItem::class, 'menu_item_addon_pivot', 'addon_id', 'menu_item_id');
    }

    /**
     * Scope to get only available addons.
     */
    public function scopeAvailable($query)
    {
        return $query->where('is_available', true);
    }

    /**
     * Scope to order by category and sort order.
     */
    public function scopeOrdered($query)
    {
        return $query->orderBy('category')->orderBy('sort_order')->orderBy('name');
    }

    /**
     * Scope by category.
     */
    public function scopeByCategory($query, $category)
    {
        return $query->where('category', $category);
    }

    /**
     * Get the localized name based on current locale.
     */
    public function getLocalizedNameAttribute(): string
    {
        $locale = app()->getLocale();
        return $locale === 'bn' && $this->name_bn ? $this->name_bn : $this->name;
    }

    /**
     * Get the localized category based on current locale.
     */
    public function getLocalizedCategoryAttribute(): string
    {
        $locale = app()->getLocale();
        return $locale === 'bn' && $this->category_bn ? $this->category_bn : $this->category;
    }

    /**
     * Get the localized description based on current locale.
     */
    public function getLocalizedDescriptionAttribute(): ?string
    {
        $locale = app()->getLocale();
        return $locale === 'bn' && $this->description_bn ? $this->description_bn : $this->description;
    }

    /**
     * Get all unique categories.
     */
    public static function getCategories(): array
    {
        return self::select('category', 'category_bn')
            ->distinct()
            ->orderBy('category')
            ->get()
            ->map(function ($addon) {
                return [
                    'value' => $addon->category,
                    'label' => $addon->localized_category,
                ];
            })
            ->toArray();
    }
}
