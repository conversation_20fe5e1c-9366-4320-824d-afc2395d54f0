<?php

namespace App\Models\Tenant;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

class DeliveryZone extends Model
{
    use HasFactory;

    protected $fillable = [
        'restaurant_id',
        'name',
        'description',
        'polygon_coordinates', // JSON array of lat/lng points
        'delivery_fee',
        'minimum_order_amount',
        'estimated_delivery_time', // in minutes
        'is_active',
        'priority_order',
        'color_code', // for map visualization
    ];

    protected $casts = [
        'polygon_coordinates' => 'array',
        'delivery_fee' => 'decimal:2',
        'minimum_order_amount' => 'decimal:2',
        'estimated_delivery_time' => 'integer',
        'is_active' => 'boolean',
        'priority_order' => 'integer',
    ];

    /**
     * Get the restaurant that owns this delivery zone
     */
    public function restaurant(): BelongsTo
    {
        return $this->belongsTo(Restaurant::class);
    }

    /**
     * Get all orders in this delivery zone
     */
    public function orders(): HasMany
    {
        return $this->hasMany(Order::class);
    }

    /**
     * Get all delivery personnel assigned to this zone
     */
    public function deliveryPersonnel(): HasMany
    {
        return $this->hasMany(DeliveryPersonnel::class);
    }

    /**
     * Check if a point (lat, lng) is within this delivery zone
     */
    public function containsPoint(float $latitude, float $longitude): bool
    {
        if (empty($this->polygon_coordinates)) {
            return false;
        }

        $polygon = $this->polygon_coordinates;
        $x = $longitude;
        $y = $latitude;
        $inside = false;

        $j = count($polygon) - 1;
        for ($i = 0; $i < count($polygon); $i++) {
            $xi = $polygon[$i]['lng'];
            $yi = $polygon[$i]['lat'];
            $xj = $polygon[$j]['lng'];
            $yj = $polygon[$j]['lat'];

            if ((($yi > $y) !== ($yj > $y)) && ($x < ($xj - $xi) * ($y - $yi) / ($yj - $yi) + $xi)) {
                $inside = !$inside;
            }
            $j = $i;
        }

        return $inside;
    }

    /**
     * Get the center point of the delivery zone
     */
    public function getCenterPointAttribute(): array
    {
        if (empty($this->polygon_coordinates)) {
            return ['lat' => 0, 'lng' => 0];
        }

        $latSum = 0;
        $lngSum = 0;
        $count = count($this->polygon_coordinates);

        foreach ($this->polygon_coordinates as $point) {
            $latSum += $point['lat'];
            $lngSum += $point['lng'];
        }

        return [
            'lat' => $latSum / $count,
            'lng' => $lngSum / $count,
        ];
    }

    /**
     * Calculate area of the delivery zone (approximate)
     */
    public function getAreaAttribute(): float
    {
        if (empty($this->polygon_coordinates) || count($this->polygon_coordinates) < 3) {
            return 0;
        }

        $area = 0;
        $coordinates = $this->polygon_coordinates;
        $j = count($coordinates) - 1;

        for ($i = 0; $i < count($coordinates); $i++) {
            $area += ($coordinates[$j]['lng'] + $coordinates[$i]['lng']) * 
                     ($coordinates[$j]['lat'] - $coordinates[$i]['lat']);
            $j = $i;
        }

        return abs($area / 2);
    }

    /**
     * Get delivery statistics for this zone
     */
    public function getDeliveryStats(string $period = 'today'): array
    {
        $query = $this->orders()->where('order_type', 'delivery');

        switch ($period) {
            case 'today':
                $query->whereDate('created_at', today());
                break;
            case 'week':
                $query->whereBetween('created_at', [now()->startOfWeek(), now()->endOfWeek()]);
                break;
            case 'month':
                $query->whereMonth('created_at', now()->month);
                break;
        }

        $orders = $query->get();

        return [
            'total_orders' => $orders->count(),
            'total_revenue' => $orders->where('payment_status', 'paid')->sum('total_amount'),
            'average_delivery_time' => $this->getAverageDeliveryTime($orders),
            'on_time_percentage' => $this->getOnTimePercentage($orders),
        ];
    }

    /**
     * Calculate average delivery time for orders
     */
    protected function getAverageDeliveryTime($orders): int
    {
        $deliveredOrders = $orders->where('status', 'delivered')->whereNotNull('delivered_at');
        
        if ($deliveredOrders->isEmpty()) {
            return $this->estimated_delivery_time;
        }

        $totalTime = $deliveredOrders->sum(function ($order) {
            return $order->created_at->diffInMinutes($order->delivered_at);
        });

        return (int) ($totalTime / $deliveredOrders->count());
    }

    /**
     * Calculate on-time delivery percentage
     */
    protected function getOnTimePercentage($orders): float
    {
        $deliveredOrders = $orders->where('status', 'delivered')->whereNotNull('delivered_at');
        
        if ($deliveredOrders->isEmpty()) {
            return 100;
        }

        $onTimeOrders = $deliveredOrders->filter(function ($order) {
            $actualTime = $order->created_at->diffInMinutes($order->delivered_at);
            return $actualTime <= $this->estimated_delivery_time;
        });

        return ($onTimeOrders->count() / $deliveredOrders->count()) * 100;
    }

    /**
     * Find delivery zone for given coordinates
     */
    public static function findZoneForLocation(float $latitude, float $longitude): ?self
    {
        $zones = self::where('is_active', true)
            ->orderBy('priority_order')
            ->get();

        foreach ($zones as $zone) {
            if ($zone->containsPoint($latitude, $longitude)) {
                return $zone;
            }
        }

        return null;
    }

    /**
     * Scope for active zones
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope for ordered zones
     */
    public function scopeOrdered($query)
    {
        return $query->orderBy('priority_order')->orderBy('name');
    }

    /**
     * Boot the model
     */
    protected static function boot()
    {
        parent::boot();

        static::creating(function ($zone) {
            if (is_null($zone->priority_order)) {
                $zone->priority_order = self::max('priority_order') + 1;
            }
            if (empty($zone->color_code)) {
                $zone->color_code = sprintf('#%06X', mt_rand(0, 0xFFFFFF));
            }
        });
    }
}
