<?php

namespace App\Models\Tenant;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class MenuItemCombo extends Model
{
    use HasFactory;

    protected $fillable = [
        'parent_item_id',
        'menu_item_id',
        'component_type',
        'is_required',
        'sort_order',
    ];

    protected $casts = [
        'is_required' => 'boolean',
        'sort_order' => 'integer',
    ];

    /**
     * Get the parent combo item
     */
    public function parentItem(): BelongsTo
    {
        return $this->belongsTo(MenuItem::class, 'parent_item_id');
    }

    /**
     * Get the menu item that is part of the combo
     */
    public function menuItem(): BelongsTo
    {
        return $this->belongsTo(MenuItem::class, 'menu_item_id');
    }

    /**
     * Get available component types
     */
    public static function getComponentTypes(): array
    {
        return [
            'main' => 'Main Course',
            'side' => 'Side Dish',
            'drink' => 'Beverage',
            'dessert' => 'Dessert',
        ];
    }

    /**
     * Scope for ordered components
     */
    public function scopeOrdered($query)
    {
        return $query->orderBy('sort_order');
    }

    /**
     * Scope for required components
     */
    public function scopeRequired($query)
    {
        return $query->where('is_required', true);
    }

    /**
     * Scope for optional components
     */
    public function scopeOptional($query)
    {
        return $query->where('is_required', false);
    }

    /**
     * Scope by component type
     */
    public function scopeByType($query, $type)
    {
        return $query->where('component_type', $type);
    }
}
