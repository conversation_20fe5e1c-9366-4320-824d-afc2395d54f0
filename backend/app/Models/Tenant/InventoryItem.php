<?php

namespace App\Models\Tenant;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use App\Traits\HasMediaLibrary;

class InventoryItem extends Model
{
    use HasFactory, HasMediaLibrary;

    protected $fillable = [
        'restaurant_id',
        'inventory_category_id',
        'vendor_id',
        'name',
        'description',
        'sku',
        'barcode',
        'unit_of_measurement',
        'unit_cost',
        'selling_price',
        'current_stock',
        'minimum_stock',
        'maximum_stock',
        'reorder_point',
        'reorder_quantity',
        'shelf_life_days',
        'storage_requirements',
        'is_perishable',
        'is_active',
        'track_expiry',
        'track_batches',
        'last_restocked_at',
        'last_counted_at',
        'notes',
    ];

    protected $casts = [
        'unit_cost' => 'decimal:4',
        'selling_price' => 'decimal:4',
        'current_stock' => 'decimal:2',
        'minimum_stock' => 'decimal:2',
        'maximum_stock' => 'decimal:2',
        'reorder_point' => 'decimal:2',
        'reorder_quantity' => 'decimal:2',
        'shelf_life_days' => 'integer',
        'is_perishable' => 'boolean',
        'is_active' => 'boolean',
        'track_expiry' => 'boolean',
        'track_batches' => 'boolean',
        'last_restocked_at' => 'datetime',
        'last_counted_at' => 'datetime',
    ];

    /**
     * Get the restaurant that owns this inventory item
     */
    public function restaurant(): BelongsTo
    {
        return $this->belongsTo(Restaurant::class);
    }

    /**
     * Get the inventory category
     */
    public function category(): BelongsTo
    {
        return $this->belongsTo(InventoryCategory::class, 'inventory_category_id');
    }

    /**
     * Get the vendor
     */
    public function vendor(): BelongsTo
    {
        return $this->belongsTo(Vendor::class);
    }

    /**
     * Get all stock movements for this item
     */
    public function stockMovements(): HasMany
    {
        return $this->hasMany(StockMovement::class);
    }

    /**
     * Get all stock batches for this item
     */
    public function stockBatches(): HasMany
    {
        return $this->hasMany(StockBatch::class);
    }

    /**
     * Get all purchase order items for this inventory item
     */
    public function purchaseOrderItems(): HasMany
    {
        return $this->hasMany(PurchaseOrderItem::class);
    }

    /**
     * Get menu items that use this inventory item
     */
    public function menuItems(): BelongsToMany
    {
        return $this->belongsToMany(MenuItem::class, 'recipe_ingredients')
            ->withPivot(['quantity_required', 'unit', 'cost_per_unit'])
            ->withTimestamps();
    }

    /**
     * Get current inventory value
     */
    public function getCurrentValueAttribute(): float
    {
        return $this->current_stock * $this->unit_cost;
    }

    /**
     * Check if item is low stock
     */
    public function isLowStock(): bool
    {
        return $this->current_stock <= $this->minimum_stock;
    }

    /**
     * Check if item is out of stock
     */
    public function isOutOfStock(): bool
    {
        return $this->current_stock <= 0;
    }

    /**
     * Check if item needs reordering
     */
    public function needsReordering(): bool
    {
        return $this->current_stock <= $this->reorder_point;
    }

    /**
     * Get stock status
     */
    public function getStockStatusAttribute(): string
    {
        if ($this->isOutOfStock()) {
            return 'out_of_stock';
        } elseif ($this->isLowStock()) {
            return 'low_stock';
        } elseif ($this->needsReordering()) {
            return 'reorder_needed';
        } else {
            return 'in_stock';
        }
    }

    /**
     * Get expired batches
     */
    public function getExpiredBatches()
    {
        return $this->stockBatches()
            ->where('expiry_date', '<', now())
            ->where('quantity', '>', 0)
            ->get();
    }

    /**
     * Get batches expiring soon
     */
    public function getBatchesExpiringSoon(int $days = 7)
    {
        return $this->stockBatches()
            ->whereBetween('expiry_date', [now(), now()->addDays($days)])
            ->where('quantity', '>', 0)
            ->orderBy('expiry_date')
            ->get();
    }

    /**
     * Get total expired quantity
     */
    public function getExpiredQuantityAttribute(): float
    {
        return $this->stockBatches()
            ->where('expiry_date', '<', now())
            ->sum('quantity');
    }

    /**
     * Get quantity expiring soon
     */
    public function getQuantityExpiringSoonAttribute(): float
    {
        return $this->stockBatches()
            ->whereBetween('expiry_date', [now(), now()->addDays(7)])
            ->sum('quantity');
    }

    /**
     * Add stock
     */
    public function addStock(float $quantity, ?float $unitCost = null, ?string $batchNumber = null, $expiryDate = null, string $reason = 'purchase', $referenceId = null): void
    {
        $this->increment('current_stock', $quantity);
        $this->update(['last_restocked_at' => now()]);

        // Create stock movement
        $this->stockMovements()->create([
            'restaurant_id' => $this->restaurant_id,
            'movement_type' => 'in',
            'quantity' => $quantity,
            'unit_cost' => $unitCost ?? $this->unit_cost,
            'total_cost' => $quantity * ($unitCost ?? $this->unit_cost),
            'reason' => $reason,
            'reference_type' => $referenceId ? get_class($referenceId) : null,
            'reference_id' => is_object($referenceId) ? $referenceId->id : $referenceId,
            'batch_number' => $batchNumber,
            'performed_by' => auth()->id() ?? 1,
            'notes' => "Stock added: {$quantity} {$this->unit_of_measurement}",
        ]);

        // Create stock batch if tracking batches or expiry
        if ($this->track_batches || $this->track_expiry) {
            $this->stockBatches()->create([
                'restaurant_id' => $this->restaurant_id,
                'batch_number' => $batchNumber ?? $this->generateBatchNumber(),
                'quantity' => $quantity,
                'unit_cost' => $unitCost ?? $this->unit_cost,
                'expiry_date' => $expiryDate,
                'received_date' => now(),
            ]);
        }

        // Update unit cost if provided
        if ($unitCost && $unitCost !== $this->unit_cost) {
            $this->update(['unit_cost' => $unitCost]);
        }
    }

    /**
     * Remove stock
     */
    public function removeStock(float $quantity, string $reason = 'usage', $referenceId = null, ?string $batchNumber = null): bool
    {
        if ($this->current_stock < $quantity) {
            return false; // Insufficient stock
        }

        $this->decrement('current_stock', $quantity);

        // Create stock movement
        $this->stockMovements()->create([
            'restaurant_id' => $this->restaurant_id,
            'movement_type' => 'out',
            'quantity' => $quantity,
            'unit_cost' => $this->unit_cost,
            'total_cost' => $quantity * $this->unit_cost,
            'reason' => $reason,
            'reference_type' => $referenceId ? get_class($referenceId) : null,
            'reference_id' => is_object($referenceId) ? $referenceId->id : $referenceId,
            'batch_number' => $batchNumber,
            'performed_by' => auth()->id() ?? 1,
            'notes' => "Stock removed: {$quantity} {$this->unit_of_measurement}",
        ]);

        // Update batch quantities if tracking batches (FIFO)
        if ($this->track_batches) {
            $this->updateBatchQuantities($quantity);
        }

        return true;
    }

    /**
     * Update batch quantities using FIFO method
     */
    protected function updateBatchQuantities(float $quantityToRemove): void
    {
        $batches = $this->stockBatches()
            ->where('quantity', '>', 0)
            ->orderBy('received_date')
            ->get();

        $remainingQuantity = $quantityToRemove;

        foreach ($batches as $batch) {
            if ($remainingQuantity <= 0) {
                break;
            }

            $quantityFromBatch = min($batch->quantity, $remainingQuantity);
            $batch->decrement('quantity', $quantityFromBatch);
            $remainingQuantity -= $quantityFromBatch;
        }
    }

    /**
     * Adjust stock to specific quantity
     */
    public function adjustStock(float $newQuantity, string $reason = 'adjustment'): void
    {
        $difference = $newQuantity - $this->current_stock;

        if ($difference > 0) {
            $this->addStock($difference, null, null, null, $reason);
        } elseif ($difference < 0) {
            $this->removeStock(abs($difference), $reason);
        }

        $this->update(['last_counted_at' => now()]);
    }

    /**
     * Generate batch number
     */
    protected function generateBatchNumber(): string
    {
        $prefix = strtoupper(substr($this->sku ?? $this->name, 0, 3));
        $date = now()->format('Ymd');
        $sequence = $this->stockBatches()->whereDate('created_at', today())->count() + 1;

        return "{$prefix}-{$date}-" . str_pad($sequence, 3, '0', STR_PAD_LEFT);
    }

    /**
     * Get turnover rate (times per month)
     */
    public function getTurnoverRate(): float
    {
        $thirtyDaysAgo = now()->subDays(30);

        $totalUsed = $this->stockMovements()
            ->where('movement_type', 'out')
            ->where('created_at', '>=', $thirtyDaysAgo)
            ->sum('quantity');

        $averageStock = ($this->current_stock + $this->minimum_stock) / 2;

        return $averageStock > 0 ? $totalUsed / $averageStock : 0;
    }

    /**
     * Get usage trend
     */
    public function getUsageTrend(int $days = 30): array
    {
        $movements = $this->stockMovements()
            ->where('movement_type', 'out')
            ->where('created_at', '>=', now()->subDays($days))
            ->selectRaw('DATE(created_at) as date, SUM(quantity) as total_used')
            ->groupBy('date')
            ->orderBy('date')
            ->get();

        return $movements->map(function ($movement) {
            return [
                'date' => $movement->date,
                'quantity' => $movement->total_used,
            ];
        })->toArray();
    }

    /**
     * Scope for active items
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope for low stock items
     */
    public function scopeLowStock($query)
    {
        return $query->whereRaw('current_stock <= minimum_stock');
    }

    /**
     * Scope for out of stock items
     */
    public function scopeOutOfStock($query)
    {
        return $query->where('current_stock', '<=', 0);
    }

    /**
     * Scope for items needing reorder
     */
    public function scopeNeedsReorder($query)
    {
        return $query->whereRaw('current_stock <= reorder_point');
    }

    /**
     * Scope for perishable items
     */
    public function scopePerishable($query)
    {
        return $query->where('is_perishable', true);
    }

    /**
     * Boot the model
     */
    protected static function boot()
    {
        parent::boot();

        static::creating(function ($item) {
            if (empty($item->sku)) {
                $item->sku = $item->generateSku();
            }
        });
    }

    /**
     * Generate SKU
     */
    protected function generateSku(): string
    {
        $prefix = strtoupper(substr($this->name, 0, 3));
        $categoryPrefix = $this->category ? strtoupper(substr($this->category->name, 0, 2)) : 'GN';
        $sequence = self::count() + 1;

        return "{$categoryPrefix}-{$prefix}-" . str_pad($sequence, 4, '0', STR_PAD_LEFT);
    }
}
