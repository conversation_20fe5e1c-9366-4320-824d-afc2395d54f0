<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Support\Facades\Storage;

class FoodCategory extends Model
{
    use HasFactory;

    protected $fillable = [
        'name',
        'slug',
        'description',
        'parent_id',
        'image',
        'icon',
        'color',
        'is_active',
        'is_featured',
        'sort_order',
    ];

    protected $casts = [
        'is_active' => 'boolean',
        'is_featured' => 'boolean',
        'sort_order' => 'integer',
    ];

    protected $appends = [
        'image_url',
    ];

    /**
     * Get the parent category
     */
    public function parent(): BelongsTo
    {
        return $this->belongsTo(FoodCategory::class, 'parent_id');
    }

    /**
     * Get the subcategories
     */
    public function subcategories(): HasMany
    {
        return $this->hasMany(FoodCategory::class, 'parent_id');
    }

    /**
     * Get the menu items in this category
     */
    public function menuItems(): HasMany
    {
        return $this->hasMany(MenuItem::class, 'category_id');
    }

    /**
     * Scope for active categories
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope for featured categories
     */
    public function scopeFeatured($query)
    {
        return $query->where('is_featured', true);
    }

    /**
     * Scope for parent categories (no parent)
     */
    public function scopeParent($query)
    {
        return $query->whereNull('parent_id');
    }

    /**
     * Scope for subcategories (has parent)
     */
    public function scopeSubcategory($query)
    {
        return $query->whereNotNull('parent_id');
    }

    /**
     * Scope for ordered categories
     */
    public function scopeOrdered($query)
    {
        return $query->orderBy('sort_order')->orderBy('name');
    }

    /**
     * Get the image URL
     */
    public function getImageUrlAttribute(): ?string
    {
        if ($this->image) {
            return Storage::url($this->image);
        }
        
        return null;
    }

    /**
     * Get the full category path (for breadcrumbs)
     */
    public function getPathAttribute(): array
    {
        $path = [];
        $category = $this;
        
        while ($category) {
            array_unshift($path, [
                'id' => $category->id,
                'name' => $category->name,
                'slug' => $category->slug,
            ]);
            $category = $category->parent;
        }
        
        return $path;
    }

    /**
     * Get the depth level of the category
     */
    public function getDepthAttribute(): int
    {
        $depth = 0;
        $category = $this->parent;
        
        while ($category) {
            $depth++;
            $category = $category->parent;
        }
        
        return $depth;
    }

    /**
     * Check if category has subcategories
     */
    public function hasSubcategories(): bool
    {
        return $this->subcategories()->exists();
    }

    /**
     * Check if category has menu items
     */
    public function hasMenuItems(): bool
    {
        return $this->menuItems()->exists();
    }

    /**
     * Get all descendant categories
     */
    public function descendants(): HasMany
    {
        return $this->subcategories()->with('descendants');
    }

    /**
     * Get all ancestor categories
     */
    public function ancestors()
    {
        $ancestors = collect();
        $category = $this->parent;
        
        while ($category) {
            $ancestors->push($category);
            $category = $category->parent;
        }
        
        return $ancestors->reverse();
    }

    /**
     * Get category tree structure
     */
    public static function getTree($parentId = null)
    {
        return static::where('parent_id', $parentId)
                    ->where('is_active', true)
                    ->with(['subcategories' => function ($query) {
                        $query->where('is_active', true)->orderBy('sort_order');
                    }])
                    ->orderBy('sort_order')
                    ->get();
    }

    /**
     * Get category statistics
     */
    public function getStatsAttribute(): array
    {
        return [
            'menu_items_count' => $this->menuItems()->count(),
            'active_menu_items_count' => $this->menuItems()->where('is_active', true)->count(),
            'subcategories_count' => $this->subcategories()->count(),
            'active_subcategories_count' => $this->subcategories()->where('is_active', true)->count(),
        ];
    }

    /**
     * Get the category's menu items with their variants
     */
    public function menuItemsWithVariants()
    {
        return $this->menuItems()
                   ->with(['variants' => function ($query) {
                       $query->where('is_active', true)->orderBy('sort_order');
                   }])
                   ->where('is_active', true)
                   ->orderBy('sort_order');
    }

    /**
     * Search categories by name or description
     */
    public static function search($query)
    {
        return static::where('name', 'like', "%{$query}%")
                    ->orWhere('description', 'like', "%{$query}%")
                    ->where('is_active', true)
                    ->orderBy('name');
    }

    /**
     * Get popular categories based on menu item orders
     */
    public static function popular($limit = 10)
    {
        return static::withCount(['menuItems as orders_count' => function ($query) {
                        // This would count orders for menu items in this category
                        // $query->join('order_items', 'menu_items.id', '=', 'order_items.menu_item_id');
                    }])
                    ->where('is_active', true)
                    ->orderBy('orders_count', 'desc')
                    ->limit($limit);
    }

    /**
     * Auto-delete image when category is deleted
     */
    protected static function boot()
    {
        parent::boot();

        static::deleting(function ($category) {
            if ($category->image) {
                Storage::delete($category->image);
            }
        });
    }
}
