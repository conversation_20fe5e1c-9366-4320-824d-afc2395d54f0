<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;

class SubscriptionPlan extends Model
{
    use HasFactory;

    protected $fillable = [
        'name',
        'slug',
        'description',
        'price',
        'billing_cycle', // monthly, yearly
        'trial_days',
        'features',
        'max_orders_per_month',
        'max_menu_items',
        'max_tables',
        'max_staff',
        'has_delivery',
        'has_analytics',
        'has_multi_location',
        'has_api_access',
        'is_active',
        'sort_order',
    ];

    protected $casts = [
        'price' => 'decimal:2',
        'trial_days' => 'integer',
        'features' => 'array',
        'max_orders_per_month' => 'integer',
        'max_menu_items' => 'integer',
        'max_tables' => 'integer',
        'max_staff' => 'integer',
        'has_delivery' => 'boolean',
        'has_analytics' => 'boolean',
        'has_multi_location' => 'boolean',
        'has_api_access' => 'boolean',
        'is_active' => 'boolean',
        'sort_order' => 'integer',
    ];

    /**
     * Get all tenants using this plan
     */
    public function tenants(): HasMany
    {
        return $this->hasMany(Tenant::class);
    }

    /**
     * Check if plan is free
     */
    public function isFree(): bool
    {
        return $this->price == 0;
    }

    /**
     * Get formatted price
     */
    public function getFormattedPriceAttribute(): string
    {
        if ($this->isFree()) {
            return 'Free';
        }
        
        return '$' . number_format($this->price, 2) . '/' . $this->billing_cycle;
    }

    /**
     * Scope for active plans
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope for ordered plans
     */
    public function scopeOrdered($query)
    {
        return $query->orderBy('sort_order');
    }
}
