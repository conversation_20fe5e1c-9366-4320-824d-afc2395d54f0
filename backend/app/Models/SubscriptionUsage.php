<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Stancl\Tenancy\Database\Concerns\CentralConnection;

class SubscriptionUsage extends Model
{
    use HasFactory, CentralConnection;

    protected $table = 'subscription_usage';

    protected $fillable = [
        'tenant_id',
        'subscription_plan_id',
        'period_start',
        'period_end',
        'menu_items_used',
        'orders_used',
        'pages_used',
        'branches_used',
        'staff_used',
        'total_menu_items',
        'total_pages',
        'total_branches',
        'revenue_generated',
        'customers_served',
    ];

    protected $casts = [
        'period_start' => 'date',
        'period_end' => 'date',
        'menu_items_used' => 'integer',
        'orders_used' => 'integer',
        'pages_used' => 'integer',
        'branches_used' => 'integer',
        'staff_used' => 'integer',
        'total_menu_items' => 'integer',
        'total_pages' => 'integer',
        'total_branches' => 'integer',
        'revenue_generated' => 'decimal:2',
        'customers_served' => 'integer',
    ];

    /**
     * Get the tenant that owns this usage record
     */
    public function tenant(): BelongsTo
    {
        return $this->belongsTo(Tenant::class);
    }

    /**
     * Get the subscription plan
     */
    public function plan(): BelongsTo
    {
        return $this->belongsTo(SubscriptionPlan::class, 'subscription_plan_id');
    }

    /**
     * Check if usage is within limits for a specific metric
     */
    public function isWithinLimit(string $metric, ?int $limit = null): bool
    {
        if ($limit === null) {
            return true; // Unlimited
        }

        $used = $this->{$metric . '_used'} ?? 0;
        return $used < $limit;
    }

    /**
     * Get usage percentage for a specific metric
     */
    public function getUsagePercentage(string $metric, ?int $limit = null): float
    {
        if ($limit === null) {
            return 0; // Unlimited
        }

        $used = $this->{$metric . '_used'} ?? 0;
        return min(100, ($used / $limit) * 100);
    }

    /**
     * Increment usage for a specific metric
     */
    public function incrementUsage(string $metric, int $amount = 1): void
    {
        $field = $metric . '_used';
        if (in_array($field, $this->fillable)) {
            $this->increment($field, $amount);
        }
    }

    /**
     * Get current period usage for tenant
     */
    public static function getCurrentPeriodUsage(string $tenantId): ?self
    {
        $now = now();
        $periodStart = $now->startOfMonth()->toDateString();
        $periodEnd = $now->endOfMonth()->toDateString();

        return static::where('tenant_id', $tenantId)
                    ->where('period_start', $periodStart)
                    ->where('period_end', $periodEnd)
                    ->first();
    }

    /**
     * Create or get current period usage
     */
    public static function getOrCreateCurrentPeriod(string $tenantId, int $planId): self
    {
        $usage = static::getCurrentPeriodUsage($tenantId);

        if (!$usage) {
            $now = now();
            $usage = static::create([
                'tenant_id' => $tenantId,
                'subscription_plan_id' => $planId,
                'period_start' => $now->startOfMonth()->toDateString(),
                'period_end' => $now->endOfMonth()->toDateString(),
            ]);
        }

        return $usage;
    }
}
