<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Carbon\Carbon;

class TableReservation extends Model
{
    use HasFactory;

    protected $fillable = [
        'table_id',
        'customer_id',
        'guest_name',
        'guest_phone',
        'guest_email',
        'reservation_date',
        'reservation_time',
        'party_size',
        'duration_minutes',
        'special_requests',
        'occasion',
        'status',
        'confirmation_code',
        'cancellation_reason',
        'cancelled_at',
        'cancelled_by',
        'seated_at',
        'completed_at',
        'notes',
    ];

    protected $casts = [
        'reservation_date' => 'date',
        'reservation_time' => 'datetime:H:i:s',
        'party_size' => 'integer',
        'duration_minutes' => 'integer',
        'cancelled_at' => 'datetime',
        'seated_at' => 'datetime',
        'completed_at' => 'datetime',
    ];

    /**
     * Get the table for the reservation
     */
    public function table(): BelongsTo
    {
        return $this->belongsTo(Table::class);
    }

    /**
     * Get the customer for the reservation
     */
    public function customer(): BelongsTo
    {
        return $this->belongsTo(Customer::class);
    }

    /**
     * Get the user who cancelled the reservation
     */
    public function cancelledBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'cancelled_by');
    }

    /**
     * Scope for confirmed reservations
     */
    public function scopeConfirmed($query)
    {
        return $query->where('status', 'confirmed');
    }

    /**
     * Scope for cancelled reservations
     */
    public function scopeCancelled($query)
    {
        return $query->where('status', 'cancelled');
    }

    /**
     * Scope for completed reservations
     */
    public function scopeCompleted($query)
    {
        return $query->where('status', 'completed');
    }

    /**
     * Scope for no-show reservations
     */
    public function scopeNoShow($query)
    {
        return $query->where('status', 'no_show');
    }

    /**
     * Scope for today's reservations
     */
    public function scopeToday($query)
    {
        return $query->whereDate('reservation_date', today());
    }

    /**
     * Scope for upcoming reservations
     */
    public function scopeUpcoming($query)
    {
        return $query->where('reservation_date', '>=', today())
                    ->where('status', 'confirmed');
    }

    /**
     * Scope for past reservations
     */
    public function scopePast($query)
    {
        return $query->where('reservation_date', '<', today());
    }

    /**
     * Get the customer name (guest or registered customer)
     */
    public function getCustomerNameAttribute(): string
    {
        return $this->customer ? $this->customer->name : $this->guest_name;
    }

    /**
     * Get the customer phone (guest or registered customer)
     */
    public function getCustomerPhoneAttribute(): string
    {
        return $this->customer ? $this->customer->phone : $this->guest_phone;
    }

    /**
     * Get the customer email (guest or registered customer)
     */
    public function getCustomerEmailAttribute(): ?string
    {
        return $this->customer ? $this->customer->email : $this->guest_email;
    }

    /**
     * Get the full reservation datetime
     */
    public function getReservationDatetimeAttribute(): Carbon
    {
        return Carbon::parse($this->reservation_date->format('Y-m-d') . ' ' . $this->reservation_time->format('H:i:s'));
    }

    /**
     * Get the end time of the reservation
     */
    public function getEndTimeAttribute(): Carbon
    {
        return $this->reservation_datetime->addMinutes($this->duration_minutes);
    }

    /**
     * Get formatted reservation time
     */
    public function getFormattedTimeAttribute(): string
    {
        return $this->reservation_time->format('g:i A');
    }

    /**
     * Get formatted reservation date
     */
    public function getFormattedDateAttribute(): string
    {
        return $this->reservation_date->format('M j, Y');
    }

    /**
     * Get formatted duration
     */
    public function getFormattedDurationAttribute(): string
    {
        $hours = floor($this->duration_minutes / 60);
        $minutes = $this->duration_minutes % 60;

        if ($hours > 0 && $minutes > 0) {
            return "{$hours}h {$minutes}m";
        } elseif ($hours > 0) {
            return "{$hours}h";
        } else {
            return "{$minutes}m";
        }
    }

    /**
     * Get status label
     */
    public function getStatusLabelAttribute(): string
    {
        return match ($this->status) {
            'confirmed' => 'Confirmed',
            'seated' => 'Seated',
            'completed' => 'Completed',
            'cancelled' => 'Cancelled',
            'no_show' => 'No Show',
            default => ucfirst($this->status),
        };
    }

    /**
     * Get status color
     */
    public function getStatusColorAttribute(): string
    {
        return match ($this->status) {
            'confirmed' => 'blue',
            'seated' => 'green',
            'completed' => 'gray',
            'cancelled' => 'red',
            'no_show' => 'orange',
            default => 'gray',
        };
    }

    /**
     * Check if reservation is confirmed
     */
    public function isConfirmed(): bool
    {
        return $this->status === 'confirmed';
    }

    /**
     * Check if reservation is cancelled
     */
    public function isCancelled(): bool
    {
        return $this->status === 'cancelled';
    }

    /**
     * Check if reservation is completed
     */
    public function isCompleted(): bool
    {
        return $this->status === 'completed';
    }

    /**
     * Check if reservation is a no-show
     */
    public function isNoShow(): bool
    {
        return $this->status === 'no_show';
    }

    /**
     * Check if reservation is in the past
     */
    public function isPast(): bool
    {
        return $this->reservation_datetime->isPast();
    }

    /**
     * Check if reservation is upcoming
     */
    public function isUpcoming(): bool
    {
        return $this->reservation_datetime->isFuture();
    }

    /**
     * Check if reservation is today
     */
    public function isToday(): bool
    {
        return $this->reservation_date->isToday();
    }

    /**
     * Check if reservation can be cancelled
     */
    public function canBeCancelled(): bool
    {
        if ($this->isCancelled() || $this->isCompleted() || $this->isNoShow()) {
            return false;
        }

        // Can cancel up to 1 hour before the reservation
        return now()->addHour()->lt($this->reservation_datetime);
    }

    /**
     * Check if reservation can be modified
     */
    public function canBeModified(): bool
    {
        if ($this->isCancelled() || $this->isCompleted() || $this->isNoShow()) {
            return false;
        }

        // Can modify up to 2 hours before the reservation
        return now()->addHours(2)->lt($this->reservation_datetime);
    }

    /**
     * Check if reservation is late (15+ minutes past reservation time)
     */
    public function isLate(): bool
    {
        if (!$this->isConfirmed()) {
            return false;
        }

        return now()->gt($this->reservation_datetime->addMinutes(15));
    }

    /**
     * Get time until reservation
     */
    public function getTimeUntilReservationAttribute(): ?string
    {
        if ($this->isPast()) {
            return null;
        }

        return $this->reservation_datetime->diffForHumans();
    }

    /**
     * Get reservation summary for notifications
     */
    public function getSummaryAttribute(): string
    {
        return "Table {$this->table->number} for {$this->party_size} on {$this->formatted_date} at {$this->formatted_time}";
    }

    /**
     * Generate a unique confirmation code
     */
    public static function generateConfirmationCode(): string
    {
        do {
            $code = 'RES-' . strtoupper(substr(md5(uniqid()), 0, 8));
        } while (static::where('confirmation_code', $code)->exists());

        return $code;
    }

    /**
     * Find reservation by confirmation code
     */
    public static function findByConfirmationCode(string $code): ?self
    {
        return static::where('confirmation_code', $code)->first();
    }

    /**
     * Get reservations for a specific table and date
     */
    public static function getForTableAndDate($tableId, $date)
    {
        return static::where('table_id', $tableId)
                    ->whereDate('reservation_date', $date)
                    ->where('status', '!=', 'cancelled')
                    ->orderBy('reservation_time')
                    ->get();
    }

    /**
     * Check if table is available at specific time
     */
    public static function isTableAvailable($tableId, $datetime, $duration, $excludeReservationId = null)
    {
        $endTime = $datetime->copy()->addMinutes($duration);

        $query = static::where('table_id', $tableId)
                      ->where('status', '!=', 'cancelled')
                      ->whereDate('reservation_date', $datetime->toDateString())
                      ->where(function ($q) use ($datetime, $endTime) {
                          $q->where(function ($subQ) use ($datetime, $endTime) {
                              $subQ->whereRaw("TIME(reservation_time) < ?", [$endTime->format('H:i:s')])
                                   ->whereRaw("TIME(DATE_ADD(CONCAT(reservation_date, ' ', reservation_time), INTERVAL duration_minutes MINUTE)) > ?", [$datetime->format('H:i:s')]);
                          });
                      });

        if ($excludeReservationId) {
            $query->where('id', '!=', $excludeReservationId);
        }

        return !$query->exists();
    }

    /**
     * Auto-generate confirmation code when creating
     */
    protected static function boot()
    {
        parent::boot();

        static::creating(function ($reservation) {
            if (!$reservation->confirmation_code) {
                $reservation->confirmation_code = static::generateConfirmationCode();
            }
        });
    }
}
