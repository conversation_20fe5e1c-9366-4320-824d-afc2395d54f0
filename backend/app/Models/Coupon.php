<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Carbon\Carbon;

class Coupon extends Model
{
    use HasFactory;

    protected $fillable = [
        'name',
        'code',
        'description',
        'type',
        'value',
        'minimum_order_amount',
        'maximum_discount_amount',
        'usage_limit',
        'usage_limit_per_customer',
        'used_count',
        'total_discount_amount',
        'valid_from',
        'valid_until',
        'is_active',
        'applicable_to',
        'applicable_items',
        'applicable_categories',
    ];

    protected $casts = [
        'value' => 'decimal:2',
        'minimum_order_amount' => 'decimal:2',
        'maximum_discount_amount' => 'decimal:2',
        'total_discount_amount' => 'decimal:2',
        'usage_limit' => 'integer',
        'usage_limit_per_customer' => 'integer',
        'used_count' => 'integer',
        'valid_from' => 'date',
        'valid_until' => 'date',
        'is_active' => 'boolean',
        'applicable_items' => 'array',
        'applicable_categories' => 'array',
    ];

    /**
     * Scope for active coupons
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope for expired coupons
     */
    public function scopeExpired($query)
    {
        return $query->where('valid_until', '<', now());
    }

    /**
     * Scope for valid coupons (not expired and active)
     */
    public function scopeValid($query)
    {
        return $query->where('is_active', true)
                    ->where('valid_from', '<=', now())
                    ->where('valid_until', '>=', now());
    }

    /**
     * Get type label
     */
    public function getTypeLabelAttribute(): string
    {
        return match ($this->type) {
            'fixed' => 'Fixed Amount',
            'percentage' => 'Percentage',
            default => ucfirst($this->type),
        };
    }

    /**
     * Get formatted value
     */
    public function getFormattedValueAttribute(): string
    {
        if ($this->type === 'percentage') {
            return $this->value . '%';
        }
        
        return '$' . number_format($this->value, 2);
    }

    /**
     * Get status label
     */
    public function getStatusLabelAttribute(): string
    {
        if (!$this->is_active) {
            return 'Inactive';
        }
        
        if ($this->valid_until->isPast()) {
            return 'Expired';
        }
        
        if ($this->valid_from->isFuture()) {
            return 'Scheduled';
        }
        
        return 'Active';
    }

    /**
     * Get status color
     */
    public function getStatusColorAttribute(): string
    {
        return match ($this->status_label) {
            'Active' => 'green',
            'Scheduled' => 'blue',
            'Expired' => 'red',
            'Inactive' => 'gray',
            default => 'gray',
        };
    }

    /**
     * Check if coupon is expired
     */
    public function isExpired(): bool
    {
        return $this->valid_until->isPast();
    }

    /**
     * Check if coupon is active
     */
    public function isActive(): bool
    {
        return $this->is_active && 
               $this->valid_from->isPast() && 
               $this->valid_until->isFuture();
    }

    /**
     * Check if coupon usage limit is reached
     */
    public function isUsageLimitReached(): bool
    {
        return $this->usage_limit && $this->used_count >= $this->usage_limit;
    }

    /**
     * Calculate discount amount for given order amount
     */
    public function calculateDiscount(float $orderAmount): float
    {
        if ($this->type === 'fixed') {
            $discount = $this->value;
        } else {
            $discount = ($orderAmount * $this->value) / 100;
        }

        // Apply maximum discount limit if set
        if ($this->maximum_discount_amount && $discount > $this->maximum_discount_amount) {
            $discount = $this->maximum_discount_amount;
        }

        // Discount cannot exceed order amount
        return min($discount, $orderAmount);
    }

    /**
     * Validate coupon for an order
     */
    public function validateForOrder(float $orderAmount, ?int $customerId = null, array $items = []): array
    {
        // Check if coupon is active
        if (!$this->is_active) {
            return ['valid' => false, 'message' => 'This coupon is not active.'];
        }

        // Check if coupon is expired
        if ($this->isExpired()) {
            return ['valid' => false, 'message' => 'This coupon has expired.'];
        }

        // Check if coupon is not yet valid
        if ($this->valid_from->isFuture()) {
            return ['valid' => false, 'message' => 'This coupon is not yet valid.'];
        }

        // Check usage limit
        if ($this->isUsageLimitReached()) {
            return ['valid' => false, 'message' => 'This coupon has reached its usage limit.'];
        }

        // Check minimum order amount
        if ($this->minimum_order_amount && $orderAmount < $this->minimum_order_amount) {
            return [
                'valid' => false, 
                'message' => "Minimum order amount of $" . number_format($this->minimum_order_amount, 2) . " required."
            ];
        }

        // Check per-customer usage limit
        if ($customerId && $this->usage_limit_per_customer) {
            // This would require a coupon_usage table to track individual customer usage
            // For now, we'll skip this validation
        }

        // Check item/category applicability
        if ($this->applicable_to === 'specific_items' && !empty($this->applicable_items)) {
            $hasApplicableItems = !empty(array_intersect($items, $this->applicable_items));
            if (!$hasApplicableItems) {
                return ['valid' => false, 'message' => 'This coupon is not applicable to the items in your order.'];
            }
        }

        if ($this->applicable_to === 'specific_categories' && !empty($this->applicable_categories)) {
            // This would require checking if any items belong to the specified categories
            // For now, we'll assume it's valid
        }

        return ['valid' => true, 'message' => 'Coupon is valid.'];
    }

    /**
     * Apply coupon to an order
     */
    public function applyToOrder(float $orderAmount): array
    {
        $validation = $this->validateForOrder($orderAmount);
        
        if (!$validation['valid']) {
            return $validation;
        }

        $discountAmount = $this->calculateDiscount($orderAmount);
        $finalAmount = $orderAmount - $discountAmount;

        // Increment usage count
        $this->increment('used_count');
        $this->increment('total_discount_amount', $discountAmount);

        return [
            'valid' => true,
            'discount_amount' => $discountAmount,
            'final_amount' => $finalAmount,
            'message' => 'Coupon applied successfully.',
        ];
    }

    /**
     * Get remaining uses
     */
    public function getRemainingUsesAttribute(): ?int
    {
        if (!$this->usage_limit) {
            return null;
        }

        return max(0, $this->usage_limit - $this->used_count);
    }

    /**
     * Get usage percentage
     */
    public function getUsagePercentageAttribute(): float
    {
        if (!$this->usage_limit) {
            return 0;
        }

        return ($this->used_count / $this->usage_limit) * 100;
    }

    /**
     * Generate unique coupon code
     */
    public static function generateUniqueCode(int $length = 8): string
    {
        do {
            $code = strtoupper(substr(str_shuffle('ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789'), 0, $length));
        } while (static::where('code', $code)->exists());

        return $code;
    }

    /**
     * Get coupons applicable to specific items
     */
    public static function getApplicableCoupons(array $itemIds = [], float $orderAmount = 0): \Illuminate\Database\Eloquent\Collection
    {
        return static::valid()
                    ->where(function ($query) use ($itemIds) {
                        $query->where('applicable_to', 'all')
                              ->orWhere(function ($subQuery) use ($itemIds) {
                                  $subQuery->where('applicable_to', 'specific_items')
                                           ->where(function ($itemQuery) use ($itemIds) {
                                               foreach ($itemIds as $itemId) {
                                                   $itemQuery->orWhereJsonContains('applicable_items', $itemId);
                                               }
                                           });
                              });
                    })
                    ->where(function ($query) use ($orderAmount) {
                        $query->whereNull('minimum_order_amount')
                              ->orWhere('minimum_order_amount', '<=', $orderAmount);
                    })
                    ->where(function ($query) {
                        $query->whereNull('usage_limit')
                              ->orWhereRaw('used_count < usage_limit');
                    })
                    ->orderBy('value', 'desc')
                    ->get();
    }
}
