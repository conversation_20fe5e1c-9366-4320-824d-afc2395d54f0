<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;

class Media extends Model
{
    use HasFactory;

    protected $fillable = [
        'name',
        'file_name',
        'mime_type',
        'disk',
        'collection_name',
        'size',
        'manipulations',
        'custom_properties',
        'generated_conversions',
        'responsive_images',
        'order_column',
        'model_type',
        'model_id',
        'uuid',
        'conversions_disk',
        'alt_text',
        'caption',
        'tags',
        'width',
        'height',
        'folder',
        'is_featured',
        'uploaded_at',
        'uploaded_by',
    ];

    protected $casts = [
        'manipulations' => 'array',
        'custom_properties' => 'array',
        'generated_conversions' => 'array',
        'responsive_images' => 'array',
        'tags' => 'array',
        'width' => 'integer',
        'height' => 'integer',
        'size' => 'integer',
        'is_featured' => 'boolean',
        'uploaded_at' => 'datetime',
    ];

    protected $appends = [
        'url',
        'thumbnail_url',
        'human_readable_size',
        'extension'
    ];

    /**
     * Get the owning model.
     */
    public function model()
    {
        return $this->morphTo();
    }

    /**
     * Get the user who uploaded this media.
     */
    public function uploader()
    {
        return $this->belongsTo(User::class, 'uploaded_by');
    }

    /**
     * Get the full URL of the media file.
     */
    public function getUrlAttribute(): string
    {
        return Storage::disk($this->disk)->url($this->file_name);
    }

    /**
     * Get the thumbnail URL.
     */
    public function getThumbnailUrlAttribute(): string
    {
        $conversions = $this->generated_conversions ?? [];
        
        if (isset($conversions['thumbnail'])) {
            return Storage::disk($this->conversions_disk ?? $this->disk)->url($conversions['thumbnail']);
        }

        return $this->url;
    }

    /**
     * Get human readable file size.
     */
    public function getHumanReadableSizeAttribute(): string
    {
        $bytes = $this->size;
        $units = ['B', 'KB', 'MB', 'GB', 'TB'];
        
        for ($i = 0; $bytes > 1024 && $i < count($units) - 1; $i++) {
            $bytes /= 1024;
        }
        
        return round($bytes, 2) . ' ' . $units[$i];
    }

    /**
     * Get file extension.
     */
    public function getExtensionAttribute(): string
    {
        return pathinfo($this->file_name, PATHINFO_EXTENSION);
    }

    /**
     * Check if the media is an image.
     */
    public function isImage(): bool
    {
        return str_starts_with($this->mime_type, 'image/');
    }

    /**
     * Check if the media is a video.
     */
    public function isVideo(): bool
    {
        return str_starts_with($this->mime_type, 'video/');
    }

    /**
     * Check if the media is a document.
     */
    public function isDocument(): bool
    {
        return in_array($this->mime_type, [
            'application/pdf',
            'application/msword',
            'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
            'application/vnd.ms-excel',
            'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
        ]);
    }

    /**
     * Scope for images only.
     */
    public function scopeImages($query)
    {
        return $query->where('mime_type', 'like', 'image/%');
    }

    /**
     * Scope for specific collection.
     */
    public function scopeInCollection($query, $collection)
    {
        return $query->where('collection_name', $collection);
    }

    /**
     * Boot the model.
     */
    protected static function boot()
    {
        parent::boot();

        static::creating(function ($media) {
            if (empty($media->uuid)) {
                $media->uuid = (string) Str::uuid();
            }
            if (empty($media->uploaded_at)) {
                $media->uploaded_at = now();
            }
            if (empty($media->uploaded_by)) {
                $media->uploaded_by = auth()->id();
            }
        });

        static::deleting(function ($media) {
            // Delete the actual file when the media record is deleted
            if (Storage::disk($media->disk)->exists($media->file_name)) {
                Storage::disk($media->disk)->delete($media->file_name);
            }

            // Delete conversions
            $conversions = $media->generated_conversions ?? [];
            foreach ($conversions as $conversion) {
                if (Storage::disk($media->conversions_disk ?? $media->disk)->exists($conversion)) {
                    Storage::disk($media->conversions_disk ?? $media->disk)->delete($conversion);
                }
            }
        });
    }
}
