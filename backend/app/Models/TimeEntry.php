<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Carbon\Carbon;

class TimeEntry extends Model
{
    use HasFactory;

    protected $fillable = [
        'user_id',
        'shift_id',
        'clock_in_time',
        'clock_out_time',
        'date',
        'total_hours',
        'break_duration',
        'clock_in_notes',
        'clock_out_notes',
        'clock_in_location',
        'clock_out_location',
        'admin_notes',
        'is_late',
        'is_early_departure',
        'is_manual_entry',
        'created_by',
    ];

    protected $casts = [
        'clock_in_time' => 'datetime',
        'clock_out_time' => 'datetime',
        'date' => 'date',
        'total_hours' => 'decimal:2',
        'break_duration' => 'integer',
        'is_late' => 'boolean',
        'is_early_departure' => 'boolean',
        'is_manual_entry' => 'boolean',
    ];

    /**
     * Get the user that owns the time entry
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the shift associated with the time entry
     */
    public function shift(): BelongsTo
    {
        return $this->belongsTo(Shift::class);
    }

    /**
     * Get the user who created the time entry (for manual entries)
     */
    public function createdBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    /**
     * Scope for active time entries (not clocked out)
     */
    public function scopeActive($query)
    {
        return $query->whereNull('clock_out_time');
    }

    /**
     * Scope for completed time entries
     */
    public function scopeCompleted($query)
    {
        return $query->whereNotNull('clock_out_time');
    }

    /**
     * Scope for today's time entries
     */
    public function scopeToday($query)
    {
        return $query->whereDate('date', today());
    }

    /**
     * Scope for this week's time entries
     */
    public function scopeThisWeek($query)
    {
        return $query->whereBetween('date', [
            now()->startOfWeek(),
            now()->endOfWeek()
        ]);
    }

    /**
     * Scope for this month's time entries
     */
    public function scopeThisMonth($query)
    {
        return $query->whereMonth('date', now()->month)
                    ->whereYear('date', now()->year);
    }

    /**
     * Scope for late arrivals
     */
    public function scopeLate($query)
    {
        return $query->where('is_late', true);
    }

    /**
     * Scope for early departures
     */
    public function scopeEarlyDeparture($query)
    {
        return $query->where('is_early_departure', true);
    }

    /**
     * Scope for manual entries
     */
    public function scopeManual($query)
    {
        return $query->where('is_manual_entry', true);
    }

    /**
     * Check if the time entry is currently active (clocked in but not out)
     */
    public function isActive(): bool
    {
        return $this->clock_in_time && !$this->clock_out_time;
    }

    /**
     * Check if the time entry is completed
     */
    public function isCompleted(): bool
    {
        return $this->clock_in_time && $this->clock_out_time;
    }

    /**
     * Get the duration of the time entry in hours
     */
    public function getDurationAttribute(): float
    {
        if ($this->total_hours) {
            return $this->total_hours;
        }

        if ($this->clock_in_time && $this->clock_out_time) {
            $duration = $this->clock_out_time->diffInMinutes($this->clock_in_time);
            $breakMinutes = $this->break_duration ?? 0;
            return ($duration - $breakMinutes) / 60;
        }

        return 0;
    }

    /**
     * Get formatted clock in time
     */
    public function getFormattedClockInAttribute(): string
    {
        return $this->clock_in_time ? $this->clock_in_time->format('H:i') : '';
    }

    /**
     * Get formatted clock out time
     */
    public function getFormattedClockOutAttribute(): string
    {
        return $this->clock_out_time ? $this->clock_out_time->format('H:i') : '';
    }

    /**
     * Get formatted duration
     */
    public function getFormattedDurationAttribute(): string
    {
        $hours = floor($this->duration);
        $minutes = ($this->duration - $hours) * 60;
        
        return sprintf('%02d:%02d', $hours, $minutes);
    }

    /**
     * Get status of the time entry
     */
    public function getStatusAttribute(): string
    {
        if ($this->isActive()) {
            return 'active';
        }
        
        if ($this->isCompleted()) {
            if ($this->is_late && $this->is_early_departure) {
                return 'late_and_early';
            } elseif ($this->is_late) {
                return 'late';
            } elseif ($this->is_early_departure) {
                return 'early';
            }
            return 'completed';
        }
        
        return 'pending';
    }

    /**
     * Get status color
     */
    public function getStatusColorAttribute(): string
    {
        return match ($this->status) {
            'active' => 'green',
            'completed' => 'blue',
            'late' => 'orange',
            'early' => 'yellow',
            'late_and_early' => 'red',
            default => 'gray',
        };
    }

    /**
     * Calculate overtime hours
     */
    public function getOvertimeHoursAttribute(): float
    {
        $regularHours = 8; // Standard work day
        $totalHours = $this->duration;
        
        return max(0, $totalHours - $regularHours);
    }

    /**
     * Calculate regular hours
     */
    public function getRegularHoursAttribute(): float
    {
        $regularHours = 8; // Standard work day
        $totalHours = $this->duration;
        
        return min($totalHours, $regularHours);
    }

    /**
     * Get the pay for this time entry
     */
    public function getPayAttribute(): float
    {
        if (!$this->user || !$this->user->hourly_rate) {
            return 0;
        }

        $regularPay = $this->regular_hours * $this->user->hourly_rate;
        $overtimePay = $this->overtime_hours * $this->user->hourly_rate * 1.5; // 1.5x for overtime
        
        return $regularPay + $overtimePay;
    }

    /**
     * Auto-calculate total hours when clock out time is set
     */
    protected static function boot()
    {
        parent::boot();

        static::saving(function ($timeEntry) {
            if ($timeEntry->clock_in_time && $timeEntry->clock_out_time && !$timeEntry->total_hours) {
                $duration = $timeEntry->clock_out_time->diffInMinutes($timeEntry->clock_in_time);
                $breakMinutes = $timeEntry->break_duration ?? 0;
                $timeEntry->total_hours = ($duration - $breakMinutes) / 60;
            }
        });
    }
}
