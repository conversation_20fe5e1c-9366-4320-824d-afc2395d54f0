<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class RestaurantReview extends Model
{
    use HasFactory;

    protected $fillable = [
        'customer_id',
        'order_id',
        'rating',
        'review',
        'food_quality',
        'service_quality',
        'delivery_quality',
        'value_for_money',
        'status',
        'admin_response',
    ];

    protected $casts = [
        'rating' => 'integer',
        'food_quality' => 'integer',
        'service_quality' => 'integer',
        'delivery_quality' => 'integer',
        'value_for_money' => 'integer',
    ];

    /**
     * Get the customer who wrote the review
     */
    public function customer(): BelongsTo
    {
        return $this->belongsTo(Customer::class);
    }

    /**
     * Get the order associated with the review
     */
    public function order(): BelongsTo
    {
        return $this->belongsTo(Order::class);
    }

    /**
     * Scope for approved reviews
     */
    public function scopeApproved($query)
    {
        return $query->where('status', 'approved');
    }

    /**
     * Scope for pending reviews
     */
    public function scopePending($query)
    {
        return $query->where('status', 'pending');
    }

    /**
     * Scope for rejected reviews
     */
    public function scopeRejected($query)
    {
        return $query->where('status', 'rejected');
    }

    /**
     * Scope for high rated reviews
     */
    public function scopeHighRated($query, $minRating = 4)
    {
        return $query->where('rating', '>=', $minRating);
    }

    /**
     * Get status label
     */
    public function getStatusLabelAttribute(): string
    {
        return match ($this->status) {
            'approved' => 'Approved',
            'pending' => 'Pending',
            'rejected' => 'Rejected',
            default => ucfirst($this->status),
        };
    }

    /**
     * Get status color
     */
    public function getStatusColorAttribute(): string
    {
        return match ($this->status) {
            'approved' => 'green',
            'pending' => 'yellow',
            'rejected' => 'red',
            default => 'gray',
        };
    }

    /**
     * Get rating stars as array
     */
    public function getRatingStarsAttribute(): array
    {
        $stars = [];
        for ($i = 1; $i <= 5; $i++) {
            $stars[] = $i <= $this->rating;
        }
        return $stars;
    }

    /**
     * Get average quality rating
     */
    public function getAverageQualityRatingAttribute(): float
    {
        $ratings = array_filter([
            $this->food_quality,
            $this->service_quality,
            $this->delivery_quality,
            $this->value_for_money,
        ]);

        return count($ratings) > 0 ? array_sum($ratings) / count($ratings) : 0;
    }

    /**
     * Check if review is approved
     */
    public function isApproved(): bool
    {
        return $this->status === 'approved';
    }

    /**
     * Check if review is pending
     */
    public function isPending(): bool
    {
        return $this->status === 'pending';
    }

    /**
     * Check if review is rejected
     */
    public function isRejected(): bool
    {
        return $this->status === 'rejected';
    }

    /**
     * Get formatted review date
     */
    public function getFormattedDateAttribute(): string
    {
        return $this->created_at->format('M j, Y');
    }

    /**
     * Get customer name
     */
    public function getCustomerNameAttribute(): string
    {
        return $this->customer ? $this->customer->name : 'Anonymous';
    }

    /**
     * Get truncated review text
     */
    public function getTruncatedReviewAttribute(): string
    {
        return strlen($this->review) > 100 ? substr($this->review, 0, 100) . '...' : $this->review;
    }
}
