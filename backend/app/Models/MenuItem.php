<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Support\Facades\Storage;

class MenuItem extends Model
{
    use HasFactory;

    protected $fillable = [
        'category_id',
        'name',
        'slug',
        'description',
        'price',
        'image',
        'ingredients',
        'allergens',
        'nutritional_info',
        'preparation_time',
        'calories',
        'is_vegetarian',
        'is_vegan',
        'is_gluten_free',
        'is_spicy',
        'spice_level',
        'is_available',
        'is_featured',
        'sort_order',
    ];

    protected $casts = [
        'price' => 'decimal:2',
        'preparation_time' => 'integer',
        'calories' => 'integer',
        'spice_level' => 'integer',
        'is_vegetarian' => 'boolean',
        'is_vegan' => 'boolean',
        'is_gluten_free' => 'boolean',
        'is_spicy' => 'boolean',
        'is_available' => 'boolean',
        'is_featured' => 'boolean',
        'sort_order' => 'integer',
        'ingredients' => 'array',
        'allergens' => 'array',
        'nutritional_info' => 'array',
    ];

    protected $appends = [
        'image_url',
    ];

    /**
     * Get the category that owns the menu item
     */
    public function category(): BelongsTo
    {
        return $this->belongsTo(FoodCategory::class, 'category_id');
    }

    /**
     * Get the variants for the menu item
     */
    public function variants(): HasMany
    {
        return $this->hasMany(FoodVariant::class);
    }

    /**
     * Scope for available items
     */
    public function scopeAvailable($query)
    {
        return $query->where('is_available', true);
    }

    /**
     * Scope for featured items
     */
    public function scopeFeatured($query)
    {
        return $query->where('is_featured', true);
    }

    /**
     * Scope for vegetarian items
     */
    public function scopeVegetarian($query)
    {
        return $query->where('is_vegetarian', true);
    }

    /**
     * Scope for vegan items
     */
    public function scopeVegan($query)
    {
        return $query->where('is_vegan', true);
    }

    /**
     * Scope for gluten-free items
     */
    public function scopeGlutenFree($query)
    {
        return $query->where('is_gluten_free', true);
    }

    /**
     * Scope for ordered items
     */
    public function scopeOrdered($query)
    {
        return $query->orderBy('sort_order')->orderBy('name');
    }

    /**
     * Get the image URL
     */
    public function getImageUrlAttribute(): ?string
    {
        if ($this->image) {
            return Storage::url($this->image);
        }
        
        return null;
    }

    /**
     * Get formatted price
     */
    public function getFormattedPriceAttribute(): string
    {
        return '$' . number_format($this->price, 2);
    }

    /**
     * Get spice level label
     */
    public function getSpiceLevelLabelAttribute(): string
    {
        return match ($this->spice_level) {
            1 => 'Mild',
            2 => 'Medium',
            3 => 'Hot',
            4 => 'Very Hot',
            5 => 'Extremely Hot',
            default => 'Not Spicy',
        };
    }

    /**
     * Get dietary labels
     */
    public function getDietaryLabelsAttribute(): array
    {
        $labels = [];
        
        if ($this->is_vegetarian) $labels[] = 'Vegetarian';
        if ($this->is_vegan) $labels[] = 'Vegan';
        if ($this->is_gluten_free) $labels[] = 'Gluten-Free';
        if ($this->is_spicy) $labels[] = 'Spicy';
        
        return $labels;
    }

    /**
     * Check if item has variants
     */
    public function hasVariants(): bool
    {
        return $this->variants()->exists();
    }

    /**
     * Get active variants
     */
    public function activeVariants()
    {
        return $this->variants()->where('is_active', true)->orderBy('sort_order');
    }

    /**
     * Get base price (without variants)
     */
    public function getBasePriceAttribute(): float
    {
        return $this->price;
    }

    /**
     * Calculate price with variants
     */
    public function calculatePriceWithVariants(array $selectedVariants = []): float
    {
        $totalPrice = $this->price;
        
        foreach ($selectedVariants as $variantId => $optionIds) {
            $variant = $this->variants()->find($variantId);
            if (!$variant) continue;
            
            if (is_array($optionIds)) {
                foreach ($optionIds as $optionId) {
                    $option = $variant->options()->find($optionId);
                    if ($option) {
                        $totalPrice += $option->price;
                    }
                }
            } else {
                $option = $variant->options()->find($optionIds);
                if ($option) {
                    $totalPrice += $option->price;
                }
            }
        }
        
        return $totalPrice;
    }

    /**
     * Search menu items
     */
    public static function search($query)
    {
        return static::where('name', 'like', "%{$query}%")
                    ->orWhere('description', 'like', "%{$query}%")
                    ->where('is_available', true)
                    ->orderBy('name');
    }

    /**
     * Get popular items
     */
    public static function popular($limit = 10)
    {
        return static::where('is_available', true)
                    ->where('is_featured', true)
                    ->orderBy('sort_order')
                    ->limit($limit);
    }

    /**
     * Auto-delete image when menu item is deleted
     */
    protected static function boot()
    {
        parent::boot();

        static::deleting(function ($menuItem) {
            if ($menuItem->image) {
                Storage::delete($menuItem->image);
            }
        });
    }
}
