<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class CustomerAddress extends Model
{
    use HasFactory;

    protected $fillable = [
        'customer_id',
        'type',
        'label',
        'address_line_1',
        'address_line_2',
        'city',
        'state',
        'postal_code',
        'country',
        'latitude',
        'longitude',
        'delivery_instructions',
        'is_default',
        'is_active',
    ];

    protected $casts = [
        'latitude' => 'decimal:8',
        'longitude' => 'decimal:8',
        'is_default' => 'boolean',
        'is_active' => 'boolean',
    ];

    /**
     * Get the customer that owns the address
     */
    public function customer(): BelongsTo
    {
        return $this->belongsTo(Customer::class);
    }

    /**
     * Scope for active addresses
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope for default addresses
     */
    public function scopeDefault($query)
    {
        return $query->where('is_default', true);
    }

    /**
     * Scope for addresses by type
     */
    public function scopeOfType($query, $type)
    {
        return $query->where('type', $type);
    }

    /**
     * Get the full address as a single string
     */
    public function getFullAddressAttribute(): string
    {
        $parts = array_filter([
            $this->address_line_1,
            $this->address_line_2,
            $this->city,
            $this->state,
            $this->postal_code,
            $this->country,
        ]);

        return implode(', ', $parts);
    }

    /**
     * Get the short address (first line + city)
     */
    public function getShortAddressAttribute(): string
    {
        $parts = array_filter([
            $this->address_line_1,
            $this->city,
        ]);

        return implode(', ', $parts);
    }

    /**
     * Get the display label for the address
     */
    public function getDisplayLabelAttribute(): string
    {
        if ($this->label) {
            return $this->label;
        }

        return ucfirst($this->type);
    }

    /**
     * Get the type label
     */
    public function getTypeLabelAttribute(): string
    {
        return match ($this->type) {
            'home' => 'Home',
            'work' => 'Work',
            'other' => 'Other',
            default => ucfirst($this->type),
        };
    }

    /**
     * Check if address has coordinates
     */
    public function hasCoordinates(): bool
    {
        return !is_null($this->latitude) && !is_null($this->longitude);
    }

    /**
     * Check if address is default
     */
    public function isDefault(): bool
    {
        return $this->is_default;
    }

    /**
     * Get formatted coordinates
     */
    public function getCoordinatesAttribute(): ?array
    {
        if ($this->hasCoordinates()) {
            return [
                'lat' => (float) $this->latitude,
                'lng' => (float) $this->longitude,
            ];
        }

        return null;
    }

    /**
     * Calculate distance from restaurant
     */
    public function getDistanceFromRestaurant(): ?float
    {
        if (!$this->hasCoordinates()) {
            return null;
        }

        $tenant = tenant();
        $restaurantLat = $tenant->latitude ?? null;
        $restaurantLng = $tenant->longitude ?? null;

        if (!$restaurantLat || !$restaurantLng) {
            return null;
        }

        return $this->calculateDistance(
            $this->latitude,
            $this->longitude,
            $restaurantLat,
            $restaurantLng
        );
    }

    /**
     * Check if address is within delivery area
     */
    public function isWithinDeliveryArea(): bool
    {
        $distance = $this->getDistanceFromRestaurant();
        
        if (is_null($distance)) {
            return true; // If we can't calculate, assume it's valid
        }

        $tenant = tenant();
        $deliveryRadius = $tenant->delivery_radius ?? 10; // Default 10km

        return $distance <= $deliveryRadius;
    }

    /**
     * Calculate delivery fee for this address
     */
    public function getDeliveryFee(): float
    {
        $tenant = tenant();
        $baseDeliveryFee = $tenant->delivery_fee ?? 0;

        if (!$this->hasCoordinates()) {
            return $baseDeliveryFee;
        }

        $distance = $this->getDistanceFromRestaurant();
        
        if (is_null($distance)) {
            return $baseDeliveryFee;
        }

        // Add extra fee for distance beyond base radius
        $baseRadius = 5; // km
        if ($distance > $baseRadius) {
            $extraDistance = $distance - $baseRadius;
            $extraFee = $extraDistance * 0.5; // $0.5 per km
            return $baseDeliveryFee + $extraFee;
        }

        return $baseDeliveryFee;
    }

    /**
     * Estimate delivery time for this address
     */
    public function getEstimatedDeliveryTime(): int
    {
        if (!$this->hasCoordinates()) {
            return 30; // Default 30 minutes
        }

        $distance = $this->getDistanceFromRestaurant();
        
        if (is_null($distance)) {
            return 30;
        }

        // Base time + travel time (assuming 30 km/h average speed)
        $baseTime = 20; // minutes
        $travelTime = ($distance / 30) * 60; // minutes
        
        return (int) round($baseTime + $travelTime);
    }

    /**
     * Calculate distance between two coordinates using Haversine formula
     */
    private function calculateDistance($lat1, $lon1, $lat2, $lon2): float
    {
        $earthRadius = 6371; // km

        $dLat = deg2rad($lat2 - $lat1);
        $dLon = deg2rad($lon2 - $lon1);

        $a = sin($dLat/2) * sin($dLat/2) + cos(deg2rad($lat1)) * cos(deg2rad($lat2)) * sin($dLon/2) * sin($dLon/2);
        $c = 2 * atan2(sqrt($a), sqrt(1-$a));

        return $earthRadius * $c;
    }

    /**
     * Get all address types
     */
    public static function getTypes(): array
    {
        return [
            'home' => 'Home',
            'work' => 'Work',
            'other' => 'Other',
        ];
    }

    /**
     * Ensure only one default address per customer
     */
    protected static function boot()
    {
        parent::boot();

        static::saving(function ($address) {
            if ($address->is_default) {
                // Unset other default addresses for this customer
                static::where('customer_id', $address->customer_id)
                     ->where('id', '!=', $address->id)
                     ->where('is_default', true)
                     ->update(['is_default' => false]);
            }
        });
    }
}
