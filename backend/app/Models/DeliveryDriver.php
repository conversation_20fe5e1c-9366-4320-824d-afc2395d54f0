<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasOne;

class DeliveryDriver extends Model
{
    use HasFactory;

    protected $fillable = [
        'user_id',
        'vehicle_type',
        'vehicle_number',
        'license_number',
        'phone',
        'emergency_contact_name',
        'emergency_contact_phone',
        'current_latitude',
        'current_longitude',
        'last_location_update',
        'is_available',
        'status',
        'current_delivery_id',
    ];

    protected $casts = [
        'current_latitude' => 'decimal:8',
        'current_longitude' => 'decimal:8',
        'last_location_update' => 'datetime',
        'is_available' => 'boolean',
    ];

    /**
     * Get the user associated with the driver
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the current delivery
     */
    public function currentDelivery(): BelongsTo
    {
        return $this->belongsTo(DeliveryOrder::class, 'current_delivery_id');
    }

    /**
     * Get all deliveries for this driver
     */
    public function deliveries(): HasMany
    {
        return $this->hasMany(DeliveryOrder::class, 'driver_id');
    }

    /**
     * Scope for available drivers
     */
    public function scopeAvailable($query)
    {
        return $query->where('is_available', true);
    }

    /**
     * Scope for active drivers
     */
    public function scopeActive($query)
    {
        return $query->where('status', 'active');
    }

    /**
     * Scope for drivers with current location
     */
    public function scopeWithLocation($query)
    {
        return $query->whereNotNull('current_latitude')
                    ->whereNotNull('current_longitude');
    }

    /**
     * Get vehicle type label
     */
    public function getVehicleTypeLabelAttribute(): string
    {
        return match ($this->vehicle_type) {
            'bike' => 'Bike',
            'motorcycle' => 'Motorcycle',
            'car' => 'Car',
            'bicycle' => 'Bicycle',
            default => ucfirst($this->vehicle_type),
        };
    }

    /**
     * Get status label
     */
    public function getStatusLabelAttribute(): string
    {
        return match ($this->status) {
            'active' => 'Active',
            'inactive' => 'Inactive',
            'suspended' => 'Suspended',
            default => ucfirst($this->status),
        };
    }

    /**
     * Get status color
     */
    public function getStatusColorAttribute(): string
    {
        return match ($this->status) {
            'active' => 'green',
            'inactive' => 'gray',
            'suspended' => 'red',
            default => 'gray',
        };
    }

    /**
     * Check if driver has active deliveries
     */
    public function hasActiveDeliveries(): bool
    {
        return $this->deliveries()
                   ->whereIn('status', ['assigned', 'picked_up', 'in_transit'])
                   ->exists();
    }

    /**
     * Check if driver is currently delivering
     */
    public function isDelivering(): bool
    {
        return !is_null($this->current_delivery_id);
    }

    /**
     * Get current location
     */
    public function getCurrentLocationAttribute(): ?array
    {
        if ($this->current_latitude && $this->current_longitude) {
            return [
                'latitude' => (float) $this->current_latitude,
                'longitude' => (float) $this->current_longitude,
                'updated_at' => $this->last_location_update,
            ];
        }

        return null;
    }

    /**
     * Get driver statistics
     */
    public function getStatsAttribute(): array
    {
        return [
            'total_deliveries' => $this->deliveries()->count(),
            'completed_deliveries' => $this->deliveries()->where('status', 'delivered')->count(),
            'cancelled_deliveries' => $this->deliveries()->where('status', 'cancelled')->count(),
            'average_rating' => $this->deliveries()->avg('rating') ?? 0,
            'total_earnings' => $this->deliveries()->where('status', 'delivered')->sum('delivery_fee'),
            'completion_rate' => $this->getCompletionRate(),
        ];
    }

    /**
     * Get completion rate percentage
     */
    public function getCompletionRate(): float
    {
        $totalDeliveries = $this->deliveries()->count();
        
        if ($totalDeliveries === 0) {
            return 0;
        }

        $completedDeliveries = $this->deliveries()->where('status', 'delivered')->count();
        
        return ($completedDeliveries / $totalDeliveries) * 100;
    }

    /**
     * Get deliveries for today
     */
    public function getTodayDeliveries()
    {
        return $this->deliveries()->whereDate('created_at', today());
    }

    /**
     * Get deliveries for this week
     */
    public function getWeekDeliveries()
    {
        return $this->deliveries()->whereBetween('created_at', [
            now()->startOfWeek(),
            now()->endOfWeek()
        ]);
    }

    /**
     * Get deliveries for this month
     */
    public function getMonthDeliveries()
    {
        return $this->deliveries()->whereMonth('created_at', now()->month);
    }

    /**
     * Calculate distance from a location
     */
    public function getDistanceFrom($latitude, $longitude): ?float
    {
        if (!$this->current_latitude || !$this->current_longitude) {
            return null;
        }

        $earthRadius = 6371; // km

        $dLat = deg2rad($latitude - $this->current_latitude);
        $dLon = deg2rad($longitude - $this->current_longitude);

        $a = sin($dLat/2) * sin($dLat/2) + cos(deg2rad($this->current_latitude)) * cos(deg2rad($latitude)) * sin($dLon/2) * sin($dLon/2);
        $c = 2 * atan2(sqrt($a), sqrt(1-$a));

        return $earthRadius * $c;
    }

    /**
     * Check if driver is online (location updated recently)
     */
    public function isOnline(): bool
    {
        if (!$this->last_location_update) {
            return false;
        }

        return $this->last_location_update->gt(now()->subMinutes(10));
    }

    /**
     * Get driver display name
     */
    public function getDisplayNameAttribute(): string
    {
        return $this->user ? $this->user->name : 'Unknown Driver';
    }
}
