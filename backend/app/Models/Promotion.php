<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Storage;
use Carbon\Carbon;

class Promotion extends Model
{
    use HasFactory;

    protected $fillable = [
        'title',
        'description',
        'type',
        'discount_type',
        'discount_value',
        'minimum_order_amount',
        'maximum_discount_amount',
        'applicable_to',
        'applicable_items',
        'applicable_categories',
        'start_date',
        'end_date',
        'start_time',
        'end_time',
        'days_of_week',
        'usage_limit',
        'usage_limit_per_customer',
        'used_count',
        'image',
        'is_active',
        'is_featured',
        'priority',
    ];

    protected $casts = [
        'discount_value' => 'decimal:2',
        'minimum_order_amount' => 'decimal:2',
        'maximum_discount_amount' => 'decimal:2',
        'start_date' => 'date',
        'end_date' => 'date',
        'applicable_items' => 'array',
        'applicable_categories' => 'array',
        'days_of_week' => 'array',
        'usage_limit' => 'integer',
        'usage_limit_per_customer' => 'integer',
        'used_count' => 'integer',
        'is_active' => 'boolean',
        'is_featured' => 'boolean',
        'priority' => 'integer',
    ];

    protected $appends = [
        'image_url',
    ];

    /**
     * Scope for active promotions
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true)
                    ->where('start_date', '<=', now())
                    ->where('end_date', '>=', now());
    }

    /**
     * Scope for expired promotions
     */
    public function scopeExpired($query)
    {
        return $query->where('end_date', '<', now());
    }

    /**
     * Scope for scheduled promotions
     */
    public function scopeScheduled($query)
    {
        return $query->where('start_date', '>', now());
    }

    /**
     * Scope for featured promotions
     */
    public function scopeFeatured($query)
    {
        return $query->where('is_featured', true);
    }

    /**
     * Get the image URL
     */
    public function getImageUrlAttribute(): ?string
    {
        if ($this->image) {
            return Storage::url($this->image);
        }

        return null;
    }

    /**
     * Get type label
     */
    public function getTypeLabelAttribute(): string
    {
        return match ($this->type) {
            'discount' => 'Discount',
            'bogo' => 'Buy One Get One',
            'free_delivery' => 'Free Delivery',
            'combo_deal' => 'Combo Deal',
            'happy_hour' => 'Happy Hour',
            default => ucfirst($this->type),
        };
    }

    /**
     * Get status label
     */
    public function getStatusLabelAttribute(): string
    {
        if (!$this->is_active) {
            return 'Inactive';
        }

        if ($this->end_date->isPast()) {
            return 'Expired';
        }

        if ($this->start_date->isFuture()) {
            return 'Scheduled';
        }

        return 'Active';
    }

    /**
     * Get status color
     */
    public function getStatusColorAttribute(): string
    {
        return match ($this->status_label) {
            'Active' => 'green',
            'Scheduled' => 'blue',
            'Expired' => 'red',
            'Inactive' => 'gray',
            default => 'gray'
        };
    }

    /**
     * Get formatted discount value
     */
    public function getFormattedDiscountAttribute(): string
    {
        if ($this->discount_type === 'percentage') {
            return $this->discount_value . '%';
        }

        return '$' . number_format($this->discount_value, 2);
    }

    /**
     * Check if promotion is currently active
     */
    public function isCurrentlyActive(): bool
    {
        if (!$this->is_active) {
            return false;
        }

        $now = now();

        // Check date range
        if ($now->lt($this->start_date) || $now->gt($this->end_date)) {
            return false;
        }

        // Check time range if specified
        if ($this->start_time && $this->end_time) {
            $currentTime = $now->format('H:i');
            if ($currentTime < $this->start_time || $currentTime > $this->end_time) {
                return false;
            }
        }

        // Check days of week if specified
        if (!empty($this->days_of_week)) {
            $currentDay = strtolower($now->format('l'));
            if (!in_array($currentDay, $this->days_of_week)) {
                return false;
            }
        }

        return true;
    }

    /**
     * Check if promotion usage limit is reached
     */
    public function isUsageLimitReached(): bool
    {
        return $this->usage_limit && $this->used_count >= $this->usage_limit;
    }

    /**
     * Calculate discount amount for given order amount
     */
    public function calculateDiscount(float $orderAmount): float
    {
        if ($this->type === 'free_delivery') {
            // For free delivery, return the delivery fee amount
            // This would need to be calculated based on the actual delivery fee
            return 0; // Placeholder
        }

        if ($this->discount_type === 'fixed') {
            $discount = $this->discount_value;
        } else {
            $discount = ($orderAmount * $this->discount_value) / 100;
        }

        // Apply maximum discount limit if set
        if ($this->maximum_discount_amount && $discount > $this->maximum_discount_amount) {
            $discount = $this->maximum_discount_amount;
        }

        // Discount cannot exceed order amount
        return min($discount, $orderAmount);
    }

    /**
     * Check promotion applicability
     */
    public function checkApplicability(float $orderAmount, array $items = [], ?int $customerId = null): array
    {
        // Check if promotion is currently active
        if (!$this->isCurrentlyActive()) {
            return ['applicable' => false, 'message' => 'This promotion is not currently active.'];
        }

        // Check usage limit
        if ($this->isUsageLimitReached()) {
            return ['applicable' => false, 'message' => 'This promotion has reached its usage limit.'];
        }

        // Check minimum order amount
        if ($this->minimum_order_amount && $orderAmount < $this->minimum_order_amount) {
            return [
                'applicable' => false,
                'message' => "Minimum order amount of $" . number_format($this->minimum_order_amount, 2) . " required."
            ];
        }

        // Check item/category applicability
        if ($this->applicable_to === 'specific_items' && !empty($this->applicable_items)) {
            $hasApplicableItems = !empty(array_intersect($items, $this->applicable_items));
            if (!$hasApplicableItems) {
                return ['applicable' => false, 'message' => 'This promotion is not applicable to the items in your order.'];
            }
        }

        if ($this->applicable_to === 'specific_categories' && !empty($this->applicable_categories)) {
            // This would require checking if any items belong to the specified categories
            // For now, we'll assume it's applicable
        }

        // Check per-customer usage limit
        if ($customerId && $this->usage_limit_per_customer) {
            // This would require a promotion_usage table to track individual customer usage
            // For now, we'll skip this validation
        }

        $discountAmount = $this->calculateDiscount($orderAmount);

        return [
            'applicable' => true,
            'message' => 'Promotion is applicable.',
            'discount_amount' => $discountAmount,
            'final_amount' => $orderAmount - $discountAmount,
        ];
    }

    /**
     * Apply promotion to an order
     */
    public function applyToOrder(float $orderAmount, array $items = []): array
    {
        $applicability = $this->checkApplicability($orderAmount, $items);

        if (!$applicability['applicable']) {
            return $applicability;
        }

        // Increment usage count
        $this->increment('used_count');

        return $applicability;
    }

    /**
     * Get remaining uses
     */
    public function getRemainingUsesAttribute(): ?int
    {
        if (!$this->usage_limit) {
            return null;
        }

        return max(0, $this->usage_limit - $this->used_count);
    }

    /**
     * Get usage percentage
     */
    public function getUsagePercentageAttribute(): float
    {
        if (!$this->usage_limit) {
            return 0;
        }

        return ($this->used_count / $this->usage_limit) * 100;
    }

    /**
     * Get days of week labels
     */
    public function getDaysOfWeekLabelsAttribute(): array
    {
        if (empty($this->days_of_week)) {
            return ['All days'];
        }

        $dayLabels = [
            'monday' => 'Monday',
            'tuesday' => 'Tuesday',
            'wednesday' => 'Wednesday',
            'thursday' => 'Thursday',
            'friday' => 'Friday',
            'saturday' => 'Saturday',
            'sunday' => 'Sunday',
        ];

        return array_map(fn($day) => $dayLabels[$day] ?? ucfirst($day), $this->days_of_week);
    }

    /**
     * Get time range label
     */
    public function getTimeRangeLabelAttribute(): string
    {
        if ($this->start_time && $this->end_time) {
            return $this->start_time . ' - ' . $this->end_time;
        }

        return 'All day';
    }

    /**
     * Auto-delete image when promotion is deleted
     */
    protected static function boot()
    {
        parent::boot();

        static::deleting(function ($promotion) {
            if ($promotion->image) {
                Storage::delete($promotion->image);
            }
        });
    }
}
