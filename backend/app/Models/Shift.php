<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

class Shift extends Model
{
    use HasFactory;

    protected $fillable = [
        'user_id',
        'date',
        'start_time',
        'end_time',
        'break_duration',
        'hours_worked',
        'hourly_rate',
        'total_pay',
        'actual_start_time',
        'actual_end_time',
        'actual_hours_worked',
        'actual_total_pay',
        'status',
        'notes',
        'created_by',
    ];

    protected $casts = [
        'date' => 'date',
        'start_time' => 'datetime:H:i:s',
        'end_time' => 'datetime:H:i:s',
        'actual_start_time' => 'datetime:H:i:s',
        'actual_end_time' => 'datetime:H:i:s',
        'break_duration' => 'integer',
        'hours_worked' => 'decimal:2',
        'hourly_rate' => 'decimal:2',
        'total_pay' => 'decimal:2',
        'actual_hours_worked' => 'decimal:2',
        'actual_total_pay' => 'decimal:2',
    ];

    /**
     * Get the user that owns the shift
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the user who created the shift
     */
    public function createdBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    /**
     * Get the time entries for the shift
     */
    public function timeEntries(): HasMany
    {
        return $this->hasMany(TimeEntry::class);
    }

    /**
     * Scope for active shifts
     */
    public function scopeActive($query)
    {
        return $query->where('status', 'active');
    }

    /**
     * Scope for completed shifts
     */
    public function scopeCompleted($query)
    {
        return $query->where('status', 'completed');
    }

    /**
     * Scope for scheduled shifts
     */
    public function scopeScheduled($query)
    {
        return $query->where('status', 'scheduled');
    }

    /**
     * Scope for today's shifts
     */
    public function scopeToday($query)
    {
        return $query->whereDate('date', today());
    }

    /**
     * Scope for this week's shifts
     */
    public function scopeThisWeek($query)
    {
        return $query->whereBetween('date', [
            now()->startOfWeek(),
            now()->endOfWeek()
        ]);
    }

    /**
     * Check if shift is currently active
     */
    public function isActive(): bool
    {
        return $this->status === 'active';
    }

    /**
     * Check if shift is completed
     */
    public function isCompleted(): bool
    {
        return $this->status === 'completed';
    }

    /**
     * Check if shift is scheduled
     */
    public function isScheduled(): bool
    {
        return $this->status === 'scheduled';
    }

    /**
     * Get the duration of the shift in hours
     */
    public function getDurationAttribute(): float
    {
        if ($this->actual_hours_worked) {
            return $this->actual_hours_worked;
        }
        
        return $this->hours_worked ?? 0;
    }

    /**
     * Get the total pay for the shift
     */
    public function getTotalPayAttribute(): float
    {
        if ($this->actual_total_pay) {
            return $this->actual_total_pay;
        }
        
        return $this->total_pay ?? 0;
    }

    /**
     * Check if shift is late (started more than 15 minutes after scheduled time)
     */
    public function isLate(): bool
    {
        if (!$this->actual_start_time || !$this->start_time) {
            return false;
        }

        $scheduledStart = $this->start_time;
        $actualStart = $this->actual_start_time;
        
        return $actualStart->gt($scheduledStart->addMinutes(15));
    }

    /**
     * Check if shift ended early (more than 15 minutes before scheduled end)
     */
    public function isEarly(): bool
    {
        if (!$this->actual_end_time || !$this->end_time) {
            return false;
        }

        $scheduledEnd = $this->end_time;
        $actualEnd = $this->actual_end_time;
        
        return $actualEnd->lt($scheduledEnd->subMinutes(15));
    }

    /**
     * Get formatted shift time
     */
    public function getFormattedTimeAttribute(): string
    {
        return $this->start_time->format('H:i') . ' - ' . $this->end_time->format('H:i');
    }

    /**
     * Get shift status color
     */
    public function getStatusColorAttribute(): string
    {
        return match ($this->status) {
            'scheduled' => 'blue',
            'active' => 'green',
            'completed' => 'gray',
            'cancelled' => 'red',
            default => 'gray',
        };
    }
}
