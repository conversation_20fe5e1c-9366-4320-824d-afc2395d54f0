<?php

namespace App\Models;

// use Illuminate\Contracts\Auth\MustVerifyEmail;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use <PERSON><PERSON>\Fortify\TwoFactorAuthenticatable;
use <PERSON><PERSON>\Jetstream\HasProfilePhoto;
use <PERSON><PERSON>\Jetstream\HasTeams;
use Laravel\Sanctum\HasApiTokens;
use Spatie\Permission\Traits\HasRoles;
use Stancl\Tenancy\Database\Concerns\CentralConnection;
use App\Traits\HasBranchAccess;

class User extends Authenticatable
{
    use HasApiTokens;

    /** @use HasFactory<\Database\Factories\UserFactory> */
    use HasFactory;
    use HasProfilePhoto;
    use HasTeams;
    use Notifiable;
    use TwoFactorAuthenticatable;
    use HasRoles;
    use CentralConnection; // Always use central database for users
    use HasBranchAccess; // Branch access functionality

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'name',
        'email',
        'password',
        'phone',
        'role',
        'department',
        'position',
        'hire_date',
        'salary',
        'hourly_rate',
        'address',
        'emergency_contact_name',
        'emergency_contact_phone',
        'notes',
        'preferred_language',
        'theme_preference',
        'is_active',
        'is_on_shift',
        'last_login_at',
    ];

    /**
     * The attributes that should be hidden for serialization.
     *
     * @var array<int, string>
     */
    protected $hidden = [
        'password',
        'remember_token',
        'two_factor_recovery_codes',
        'two_factor_secret',
    ];

    /**
     * The accessors to append to the model's array form.
     *
     * @var array<int, string>
     */
    protected $appends = [
        'profile_photo_url',
    ];

    /**
     * Get the attributes that should be cast.
     *
     * @return array<string, string>
     */
    protected function casts(): array
    {
        return [
            'email_verified_at' => 'datetime',
            'password' => 'hashed',
            'hire_date' => 'date',
            'salary' => 'decimal:2',
            'hourly_rate' => 'decimal:2',
            'is_active' => 'boolean',
            'is_on_shift' => 'boolean',
            'last_login_at' => 'datetime',
        ];
    }

    /**
     * Get the user's primary role
     */
    public function getPrimaryRoleAttribute(): ?string
    {
        return $this->roles->first()?->name;
    }

    /**
     * Check if user has admin role
     */
    public function isAdmin(): bool
    {
        return $this->hasRole('admin');
    }

    /**
     * Check if user has restaurant manager role
     */
    public function isRestaurantManager(): bool
    {
        return $this->hasRole('restaurant_manager');
    }

    /**
     * Check if user has waiter role
     */
    public function isWaiter(): bool
    {
        return $this->hasRole('waiter');
    }

    /**
     * Check if user has kitchen role
     */
    public function isKitchen(): bool
    {
        return $this->hasRole('kitchen');
    }

    /**
     * Check if user has delivery role
     */
    public function isDelivery(): bool
    {
        return $this->hasRole('delivery');
    }

    /**
     * Get dashboard route based on user role
     */
    public function getDashboardRoute(): string
    {
        if ($this->isAdmin()) {
            return route('admin.dashboard');
        }

        if ($this->isRestaurantManager()) {
            return route('manager.dashboard');
        }

        if ($this->isWaiter()) {
            return route('waiter.dashboard');
        }

        if ($this->isKitchen()) {
            return route('kitchen.dashboard');
        }

        if ($this->isDelivery()) {
            return route('delivery.dashboard');
        }

        return route('dashboard');
    }

    /**
     * Get the user's shifts
     */
    public function shifts()
    {
        return $this->hasMany(Shift::class);
    }

    /**
     * Get the user's time entries
     */
    public function timeEntries()
    {
        return $this->hasMany(TimeEntry::class);
    }

    /**
     * Get the user's employee record in the current tenant
     */
    public function employee()
    {
        return $this->hasOne(\App\Models\Tenant\Employee::class);
    }

    /**
     * Get branches accessible to this user (via HasBranchAccess trait)
     * This method provides the branches() relationship that was missing
     */
    public function branches()
    {
        return $this->getAccessibleBranches();
    }

    /**
     * Get branches where this user is a manager
     */
    public function managedBranches()
    {
        $employee = $this->employee;
        if (!$employee) {
            return new \Illuminate\Database\Eloquent\Collection();
        }

        return \App\Models\Tenant\Branch::where('manager_id', $employee->id)->get();
    }

    /**
     * Get the user's primary branch (via employee record)
     */
    public function primaryBranch()
    {
        $employee = $this->employee;
        if (!$employee || !$employee->primary_branch_id) {
            return null;
        }

        return \App\Models\Tenant\Branch::find($employee->primary_branch_id);
    }

    /**
     * Get the user's current time entry (if clocked in)
     */
    public function currentTimeEntry()
    {
        return $this->hasOne(TimeEntry::class)->whereNull('clock_out_time');
    }

    /**
     * Get the user's active shift
     */
    public function activeShift()
    {
        return $this->hasOne(Shift::class)->where('status', 'active');
    }

    /**
     * Get blogs authored by this user
     */
    public function authoredBlogs()
    {
        return $this->hasMany(\App\Models\Blog::class, 'author_id');
    }

    /**
     * Get blogs created by this user
     */
    public function createdBlogs()
    {
        return $this->hasMany(\App\Models\Blog::class, 'created_by');
    }

    /**
     * Update last login timestamp
     */
    public function updateLastLogin(): void
    {
        $this->update(['last_login_at' => now()]);
    }
}
