<?php

namespace App\Models;

use Stancl\Tenancy\Database\Models\Tenant as BaseTenant;
use Stancl\Tenancy\Contracts\TenantWithDatabase;
use Stancl\Tenancy\Database\Concerns\HasDatabase;
use Stancl\Tenancy\Database\Concerns\HasDomains;
use Illuminate\Database\Eloquent\Relations\HasOne;
use Illuminate\Database\Eloquent\Relations\HasMany;

class Tenant extends BaseTenant implements TenantWithDatabase
{
    use HasDatabase, HasDomains;

    protected $fillable = [
        'id',
        'name',
        'email',
        'phone',
        'address',
        'city',
        'state',
        'country',
        'postal_code',
        'timezone',
        'currency',
        'language',
        'logo',
        'theme_color',
        'subscription_plan_id',
        'subscription_status',
        'trial_ends_at',
        'subscription_started_at',
        'subscription_ends_at',
        'setup_status',
        'setup_started_at',
        'setup_completed_at',
        'setup_failed_at',
        'setup_error',
        'setup_duration_minutes',
        'last_activity_at',
        'is_active',
        'created_at',
        'updated_at',
    ];

    protected $casts = [
        'trial_ends_at' => 'datetime',
        'subscription_started_at' => 'datetime',
        'subscription_ends_at' => 'datetime',
        'setup_started_at' => 'datetime',
        'setup_completed_at' => 'datetime',
        'setup_failed_at' => 'datetime',
        'last_activity_at' => 'datetime',
        'is_active' => 'boolean',
    ];

    public static function getCustomColumns(): array
    {
        return [
            'id',
            'name',
            'email',
            'phone',
            'address',
            'city',
            'state',
            'country',
            'postal_code',
            'timezone',
            'currency',
            'language',
            'logo',
            'theme_color',
            'subscription_plan_id',
            'subscription_status',
            'trial_ends_at',
            'subscription_ends_at',
        ];
    }

    /**
     * Get the subscription plan for this tenant
     */
    public function subscriptionPlan(): HasOne
    {
        return $this->hasOne(SubscriptionPlan::class, 'id', 'subscription_plan_id');
    }

    /**
     * Get all payments for this tenant
     */
    public function payments(): HasMany
    {
        return $this->hasMany(Payment::class);
    }

    /**
     * Check if tenant is on trial
     */
    public function onTrial(): bool
    {
        return $this->trial_ends_at && $this->trial_ends_at->isFuture();
    }

    /**
     * Check if tenant subscription is active
     */
    public function subscriptionActive(): bool
    {
        return $this->subscription_status === 'active' && 
               ($this->subscription_ends_at === null || $this->subscription_ends_at->isFuture());
    }

    /**
     * Check if tenant can access the system
     */
    public function canAccess(): bool
    {
        return $this->onTrial() || $this->subscriptionActive();
    }

    /**
     * Get the primary domain for this tenant
     */
    public function getPrimaryDomain(): ?string
    {
        return $this->domains()->where('is_primary', true)->first()?->domain;
    }

    /**
     * Get the restaurant subdomain
     */
    public function getSubdomain(): ?string
    {
        $domain = $this->getPrimaryDomain();
        if ($domain) {
            return explode('.', $domain)[0];
        }
        return null;
    }

    /**
     * Check if tenant setup is in progress
     */
    public function setupInProgress(): bool
    {
        return in_array($this->setup_status, [
            'pending',
            'in_progress',
            'creating_database',
            'seeding_data',
            'creating_manager',
            'setting_up_filesystem',
            'setting_up_services',
            'sending_welcome_email',
            'finalizing'
        ]);
    }

    /**
     * Check if tenant setup is completed
     */
    public function setupCompleted(): bool
    {
        return $this->setup_status === 'completed';
    }

    /**
     * Check if tenant setup has failed
     */
    public function setupFailed(): bool
    {
        return str_contains($this->setup_status ?? '', 'failed');
    }

    /**
     * Get setup progress percentage
     */
    public function getSetupProgress(): int
    {
        $statusProgress = [
            'pending' => 0,
            'in_progress' => 10,
            'creating_database' => 20,
            'database_created' => 25,
            'seeding_data' => 35,
            'data_seeded' => 45,
            'creating_manager' => 55,
            'manager_created' => 65,
            'setting_up_filesystem' => 70,
            'filesystem_setup' => 75,
            'setting_up_services' => 80,
            'services_setup' => 85,
            'sending_welcome_email' => 90,
            'welcome_email_sent' => 95,
            'finalizing' => 98,
            'completed' => 100,
        ];

        return $statusProgress[$this->setup_status] ?? 0;
    }

    /**
     * Get human-readable setup status
     */
    public function getSetupStatusLabel(): string
    {
        $statusLabels = [
            'pending' => 'Pending Setup',
            'in_progress' => 'Setup In Progress',
            'creating_database' => 'Creating Database',
            'database_created' => 'Database Created',
            'seeding_data' => 'Setting Up Default Data',
            'data_seeded' => 'Default Data Ready',
            'creating_manager' => 'Creating Manager Account',
            'manager_created' => 'Manager Account Created',
            'setting_up_filesystem' => 'Setting Up File Storage',
            'filesystem_setup' => 'File Storage Ready',
            'setting_up_services' => 'Configuring Services',
            'services_setup' => 'Services Configured',
            'sending_welcome_email' => 'Sending Welcome Email',
            'welcome_email_sent' => 'Welcome Email Sent',
            'finalizing' => 'Finalizing Setup',
            'completed' => 'Setup Complete',
        ];

        if ($this->setupFailed()) {
            return 'Setup Failed';
        }

        return $statusLabels[$this->setup_status] ?? 'Unknown Status';
    }
}
