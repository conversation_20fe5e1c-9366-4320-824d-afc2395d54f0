<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

class Table extends Model
{
    use HasFactory;

    protected $fillable = [
        'number',
        'name',
        'capacity',
        'location',
        'branch_id',
        'floor_id',
        'is_active',
        'is_available',
        'sort_order',
    ];

    protected $casts = [
        'capacity' => 'integer',
        'is_active' => 'boolean',
        'is_available' => 'boolean',
        'sort_order' => 'integer',
    ];

    /**
     * Get the branch that owns the table.
     */
    public function branch(): BelongsTo
    {
        return $this->belongsTo(\App\Models\Tenant\Branch::class);
    }

    /**
     * Get the floor that owns the table.
     */
    public function floor(): BelongsTo
    {
        return $this->belongsTo(Floor::class);
    }

    /**
     * Get the table reservations
     */
    public function reservations(): HasMany
    {
        return $this->hasMany(TableReservation::class);
    }

    /**
     * Scope for active tables
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope for available tables
     */
    public function scopeAvailable($query)
    {
        return $query->where('is_available', true);
    }

    /**
     * Scope for ordered tables
     */
    public function scopeOrdered($query)
    {
        return $query->orderBy('sort_order')->orderBy('number');
    }

    /**
     * Get current reservation
     */
    public function currentReservation()
    {
        return $this->hasOne(TableReservation::class)
                   ->where('status', 'seated')
                   ->whereDate('reservation_date', today());
    }

    /**
     * Check if table is currently occupied
     */
    public function isOccupied(): bool
    {
        return $this->currentReservation()->exists();
    }

    /**
     * Get table display name
     */
    public function getDisplayNameAttribute(): string
    {
        return $this->name ?: "Table {$this->number}";
    }

    /**
     * Get table status
     */
    public function getStatusAttribute(): string
    {
        if (!$this->is_active) return 'inactive';
        if (!$this->is_available) return 'unavailable';
        if ($this->isOccupied()) return 'occupied';
        
        return 'available';
    }

    /**
     * Get status color
     */
    public function getStatusColorAttribute(): string
    {
        return match ($this->status) {
            'available' => 'green',
            'occupied' => 'red',
            'unavailable' => 'yellow',
            'inactive' => 'gray',
            default => 'gray',
        };
    }
}
