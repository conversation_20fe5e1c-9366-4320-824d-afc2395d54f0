<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Str;

class Blog extends Model
{
    use HasFactory;

    protected $fillable = [
        'title',
        'slug',
        'content',
        'excerpt',
        'featured_image_id',
        'meta_description',
        'status',
        'published_at',
        'author_id',
        'tags',
        'categories',
        'sort_order',
        'created_by',
        'updated_by',
        // Legacy columns for backward compatibility
        'description',
        'short_description',
        'banner',
        'meta_title',
        'meta_img',
        'meta_keywords',
        'category_id',
    ];

    protected $casts = [
        'title' => 'array',
        'tags' => 'array',
        'categories' => 'array',
        'published_at' => 'datetime',
        'sort_order' => 'integer',
    ];

    protected $attributes = [
        'status' => 0, // 0 = draft, 1 = published (for legacy compatibility)
        'sort_order' => 0,
    ];

    /**
     * Get the status attribute (convert int to string)
     */
    public function getStatusAttribute($value)
    {
        if (is_numeric($value)) {
            return $value == 1 ? 'published' : 'draft';
        }
        return $value;
    }

    /**
     * Set the status attribute (convert string to int for legacy compatibility)
     */
    public function setStatusAttribute($value)
    {
        if (is_string($value)) {
            $this->attributes['status'] = $value === 'published' ? 1 : 0;
        } else {
            $this->attributes['status'] = $value;
        }
    }

    /**
     * Get content (fallback to description if content doesn't exist)
     */
    public function getContentAttribute($value)
    {
        return $value ?: $this->attributes['description'] ?? '';
    }

    /**
     * Get excerpt (fallback to short_description if excerpt doesn't exist)
     */
    public function getExcerptAttribute($value)
    {
        if ($value) {
            return $value;
        }

        // Fallback to short_description
        if (isset($this->attributes['short_description']) && $this->attributes['short_description']) {
            return $this->attributes['short_description'];
        }

        // Fallback to truncated content
        $content = $this->content ?: $this->attributes['description'] ?? '';
        return Str::limit(strip_tags($content), 150);
    }

    /**
     * Get featured_image_id (fallback to banner if featured_image_id doesn't exist)
     */
    public function getFeaturedImageIdAttribute($value)
    {
        return $value ?: $this->attributes['banner'] ?? null;
    }

    /**
     * Get the title in the current locale
     */
    public function getTitleAttribute($value)
    {
        // If it's already JSON, decode it
        if (is_string($value) && json_decode($value)) {
            $titles = json_decode($value, true);
            $locale = app()->getLocale();
            return $titles[$locale] ?? $titles['en'] ?? '';
        }

        // If it's a plain string, return it
        return $value;
    }

    /**
     * Set the title for multiple locales
     */
    public function setTitleAttribute($value)
    {
        if (is_string($value)) {
            $this->attributes['title'] = json_encode([
                'en' => $value,
                'bn' => $value
            ]);
        } else {
            $this->attributes['title'] = json_encode($value);
        }
    }

    /**
     * Get the raw title array
     */
    public function getTitleTranslationsAttribute()
    {
        return json_decode($this->attributes['title'], true) ?? [];
    }

    /**
     * Generate slug from title
     */
    public static function generateSlug($title)
    {
        $baseSlug = Str::slug($title);
        $slug = $baseSlug;
        $counter = 1;

        while (static::where('slug', $slug)->exists()) {
            $slug = $baseSlug . '-' . $counter;
            $counter++;
        }

        return $slug;
    }

    // /**
    //  * Scope for published blogs
    //  */
    // public function scopePublished($query)
    // {
    //     return $query->where('status', 'published')
    //                 ->whereNotNull('published_at')
    //                 ->where('published_at', '<=', now());
    // }

    /**
     * Scope for draft blogs
     */
    public function scopeDraft($query)
    {
        return $query->where('status', 'draft');
    }


    /**
     * Get the featured image
     */
    public function featuredImage()
    {
        return $this->belongsTo(Media::class, 'featured_image_id');
    }

    /**
     * Get the author
     */
    public function author()
    {
        return $this->belongsTo(User::class, 'author_id');
    }

    /**
     * Get the user who created this blog
     */
    public function creator()
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    /**
     * Get the user who last updated this blog
     */
    public function updater()
    {
        return $this->belongsTo(User::class, 'updated_by');
    }

    /**
     * Get the featured image URL
     */
    public function getFeaturedImageUrlAttribute()
    {
        return $this->featuredImage ? $this->featuredImage->url : null;
    }



    /**
     * Boot the model
     */
    protected static function boot()
    {
        parent::boot();

        static::creating(function ($blog) {
            if (empty($blog->slug)) {
                $title = is_array($blog->title) ? ($blog->title['en'] ?? '') : $blog->title;
                $blog->slug = static::generateSlug($title);
            }
            $blog->created_by = auth()->id();
            if (!$blog->author_id) {
                $blog->author_id = auth()->id();
            }
        });

        static::updating(function ($blog) {
            $blog->updated_by = auth()->id();
            
            // Auto-set published_at when status changes to published
            if ($blog->status === 'published' && !$blog->published_at) {
                $blog->published_at = now();
            }
        });
    }

    /**
     * Scope to get only published blogs
     */
    public function scopePublished($query)
    {
        return $query->where('status', 'published')
                    ->whereNotNull('published_at')
                    ->where('published_at', '<=', now());
    }

    /**
     * Scope to order blogs
     */
    public function scopeOrdered($query)
    {
        return $query->orderBy('sort_order', 'asc')
                    ->orderBy('published_at', 'desc')
                    ->orderBy('created_at', 'desc');
    }

    /**
     * Get the route key for the model.
     */
    public function getRouteKeyName(): string
    {
        return 'slug';
    }
}
