<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Support\Facades\Storage;

class Customer extends Model
{
    use HasFactory;

    protected $fillable = [
        'name',
        'email',
        'phone',
        'date_of_birth',
        'gender',
        'avatar',
        'loyalty_points',
        'tier',
        'total_orders',
        'total_spent',
        'last_order_at',
        'is_blocked',
        'notes',
    ];

    protected $casts = [
        'date_of_birth' => 'date',
        'loyalty_points' => 'integer',
        'total_orders' => 'integer',
        'total_spent' => 'decimal:2',
        'last_order_at' => 'datetime',
        'is_blocked' => 'boolean',
    ];

    protected $appends = [
        'avatar_url',
    ];

    /**
     * Get the customer's addresses
     */
    public function addresses(): HasMany
    {
        return $this->hasMany(CustomerAddress::class);
    }

    /**
     * Get the customer's orders
     */
    public function orders(): Has<PERSON><PERSON>
    {
        return $this->hasMany(Order::class);
    }

    /**
     * Get the customer's table reservations
     */
    public function tableReservations(): HasMany
    {
        return $this->hasMany(TableReservation::class);
    }

    /**
     * Get the default address
     */
    public function defaultAddress()
    {
        return $this->hasOne(CustomerAddress::class)->where('is_default', true);
    }

    /**
     * Get active addresses
     */
    public function activeAddresses()
    {
        return $this->addresses()->where('is_active', true);
    }

    /**
     * Scope for active customers
     */
    public function scopeActive($query)
    {
        return $query->where('is_blocked', false);
    }

    /**
     * Scope for blocked customers
     */
    public function scopeBlocked($query)
    {
        return $query->where('is_blocked', true);
    }

    /**
     * Scope for VIP customers
     */
    public function scopeVip($query)
    {
        return $query->where('tier', 'vip');
    }

    /**
     * Get the avatar URL
     */
    public function getAvatarUrlAttribute(): ?string
    {
        if ($this->avatar) {
            return Storage::url($this->avatar);
        }
        
        return null;
    }

    /**
     * Get tier label
     */
    public function getTierLabelAttribute(): string
    {
        return match ($this->tier) {
            'bronze' => 'Bronze',
            'silver' => 'Silver',
            'gold' => 'Gold',
            'platinum' => 'Platinum',
            'vip' => 'VIP',
            default => 'Regular',
        };
    }

    /**
     * Get tier color
     */
    public function getTierColorAttribute(): string
    {
        return match ($this->tier) {
            'bronze' => '#CD7F32',
            'silver' => '#C0C0C0',
            'gold' => '#FFD700',
            'platinum' => '#E5E4E2',
            'vip' => '#800080',
            default => '#6B7280',
        };
    }

    /**
     * Add loyalty points
     */
    public function addLoyaltyPoints(int $points): void
    {
        $this->increment('loyalty_points', $points);
        $this->updateTier();
    }

    /**
     * Redeem loyalty points
     */
    public function redeemLoyaltyPoints(int $points): bool
    {
        if ($this->loyalty_points >= $points) {
            $this->decrement('loyalty_points', $points);
            return true;
        }
        
        return false;
    }

    /**
     * Update customer tier based on total spent
     */
    public function updateTier(): void
    {
        $tier = match (true) {
            $this->total_spent >= 5000 => 'vip',
            $this->total_spent >= 2000 => 'platinum',
            $this->total_spent >= 1000 => 'gold',
            $this->total_spent >= 500 => 'silver',
            $this->total_spent >= 100 => 'bronze',
            default => 'regular',
        };

        $this->update(['tier' => $tier]);
    }

    /**
     * Update order statistics
     */
    public function updateOrderStats(float $orderAmount): void
    {
        $this->increment('total_orders');
        $this->increment('total_spent', $orderAmount);
        $this->update(['last_order_at' => now()]);
        $this->updateTier();
    }

    /**
     * Check if customer is VIP
     */
    public function isVip(): bool
    {
        return $this->tier === 'vip';
    }

    /**
     * Check if customer is blocked
     */
    public function isBlocked(): bool
    {
        return $this->is_blocked;
    }

    /**
     * Get customer statistics
     */
    public function getStatsAttribute(): array
    {
        return [
            'total_orders' => $this->total_orders,
            'total_spent' => $this->total_spent,
            'average_order_value' => $this->total_orders > 0 ? $this->total_spent / $this->total_orders : 0,
            'loyalty_points' => $this->loyalty_points,
            'tier' => $this->tier_label,
            'addresses_count' => $this->addresses()->count(),
            'last_order_days_ago' => $this->last_order_at ? $this->last_order_at->diffInDays() : null,
        ];
    }

    /**
     * Search customers
     */
    public static function search($query)
    {
        return static::where('name', 'like', "%{$query}%")
                    ->orWhere('email', 'like', "%{$query}%")
                    ->orWhere('phone', 'like', "%{$query}%");
    }

    /**
     * Get customers by tier
     */
    public static function byTier($tier)
    {
        return static::where('tier', $tier)->where('is_blocked', false);
    }

    /**
     * Auto-delete avatar when customer is deleted
     */
    protected static function boot()
    {
        parent::boot();

        static::deleting(function ($customer) {
            if ($customer->avatar) {
                Storage::delete($customer->avatar);
            }
        });
    }
}
