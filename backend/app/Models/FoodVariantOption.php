<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class FoodVariantOption extends Model
{
    use HasFactory;

    protected $fillable = [
        'food_variant_id',
        'name',
        'price',
        'is_default',
        'is_active',
        'sort_order',
    ];

    protected $casts = [
        'price' => 'decimal:2',
        'is_default' => 'boolean',
        'is_active' => 'boolean',
        'sort_order' => 'integer',
    ];

    /**
     * Get the food variant that owns the option
     */
    public function foodVariant(): BelongsTo
    {
        return $this->belongsTo(FoodVariant::class);
    }

    /**
     * Scope for active options
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope for default options
     */
    public function scopeDefault($query)
    {
        return $query->where('is_default', true);
    }

    /**
     * Scope for ordered options
     */
    public function scopeOrdered($query)
    {
        return $query->orderBy('sort_order')->orderBy('name');
    }

    /**
     * Get formatted price
     */
    public function getFormattedPriceAttribute(): string
    {
        if ($this->price == 0) {
            return 'Free';
        }

        return '+$' . number_format($this->price, 2);
    }

    /**
     * Check if option is free
     */
    public function isFree(): bool
    {
        return $this->price == 0;
    }

    /**
     * Check if option is default
     */
    public function isDefault(): bool
    {
        return $this->is_default;
    }

    /**
     * Get option display name with price
     */
    public function getDisplayNameAttribute(): string
    {
        $name = $this->name;
        
        if ($this->price > 0) {
            $name .= ' (' . $this->formatted_price . ')';
        }

        return $name;
    }
}
