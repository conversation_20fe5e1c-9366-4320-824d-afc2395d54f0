<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Str;

class DynamicPage extends Model
{
    use HasFactory, SoftDeletes;

    protected $fillable = [
        'title',
        'slug',
        'content',
        'meta_description',
        'status',
        'sort_order',
        'created_by',
        'updated_by',
    ];

    protected $casts = [
        'title' => 'array',
        'sort_order' => 'integer',
    ];

    protected $attributes = [
        'status' => 'active',
        'sort_order' => 0,
    ];

    /**
     * Get the title in the current locale
     */
    public function getTitleAttribute($value)
    {
        $titles = json_decode($value, true);
        $locale = app()->getLocale();
        
        return $titles[$locale] ?? $titles['en'] ?? '';
    }

    /**
     * Set the title for multiple locales
     */
    public function setTitleAttribute($value)
    {
        if (is_string($value)) {
            $this->attributes['title'] = json_encode([
                'en' => $value,
                'bn' => $value
            ]);
        } else {
            $this->attributes['title'] = json_encode($value);
        }
    }

    /**
     * Get the raw title array
     */
    public function getTitleTranslationsAttribute()
    {
        return json_decode($this->attributes['title'], true) ?? [];
    }

    /**
     * Generate slug from title
     */
    public static function generateSlug($title)
    {
        $baseSlug = Str::slug($title);
        $slug = $baseSlug;
        $counter = 1;

        while (static::where('slug', $slug)->exists()) {
            $slug = $baseSlug . '-' . $counter;
            $counter++;
        }

        return $slug;
    }

    /**
     * Scope for active pages
     */
    public function scopeActive($query)
    {
        return $query->where('status', 'active');
    }

    /**
     * Scope for ordered pages
     */
    public function scopeOrdered($query)
    {
        return $query->orderBy('sort_order')->orderBy('created_at');
    }

    /**
     * Get the user who created this page
     */
    public function creator()
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    /**
     * Get the user who last updated this page
     */
    public function updater()
    {
        return $this->belongsTo(User::class, 'updated_by');
    }

    /**
     * Boot the model
     */
    protected static function boot()
    {
        parent::boot();

        static::creating(function ($page) {
            if (empty($page->slug)) {
                $title = is_array($page->title) ? ($page->title['en'] ?? '') : $page->title;
                $page->slug = static::generateSlug($title);
            }
            $page->created_by = auth()->id();
        });

        static::updating(function ($page) {
            $page->updated_by = auth()->id();
        });
    }
}
