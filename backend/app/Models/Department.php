<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;

class Department extends Model
{
    use HasFactory;

    protected $fillable = [
        'name',
        'description',
        'manager_id',
        'is_active',
        'sort_order',
    ];

    protected $casts = [
        'is_active' => 'boolean',
        'sort_order' => 'integer',
    ];

    /**
     * Get the department manager
     */
    public function manager()
    {
        return $this->belongsTo(User::class, 'manager_id');
    }

    /**
     * Get the employees in this department
     */
    public function employees(): HasMany
    {
        return $this->hasMany(User::class, 'department', 'name');
    }

    /**
     * Scope for active departments
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope for ordered departments
     */
    public function scopeOrdered($query)
    {
        return $query->orderBy('sort_order')->orderBy('name');
    }

    /**
     * Get active employees
     */
    public function activeEmployees()
    {
        return $this->employees()->where('is_active', true);
    }

    /**
     * Get department statistics
     */
    public function getStatsAttribute(): array
    {
        return [
            'total_employees' => $this->employees()->count(),
            'active_employees' => $this->activeEmployees()->count(),
            'on_shift_employees' => $this->activeEmployees()->where('is_on_shift', true)->count(),
        ];
    }

    /**
     * Check if department has employees
     */
    public function hasEmployees(): bool
    {
        return $this->employees()->exists();
    }

    /**
     * Get all department names
     */
    public static function getDepartmentNames(): array
    {
        return static::active()->pluck('name', 'name')->toArray();
    }
}
