<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class DeliveryOrder extends Model
{
    use HasFactory;

    protected $fillable = [
        'order_id',
        'customer_id',
        'driver_id',
        'tracking_number',
        'delivery_address',
        'delivery_latitude',
        'delivery_longitude',
        'delivery_phone',
        'delivery_instructions',
        'delivery_fee',
        'estimated_delivery_time',
        'status',
        'assigned_at',
        'picked_up_at',
        'in_transit_at',
        'delivered_at',
        'cancelled_at',
        'notes',
        'rating',
        'review',
    ];

    protected $casts = [
        'delivery_latitude' => 'decimal:8',
        'delivery_longitude' => 'decimal:8',
        'delivery_fee' => 'decimal:2',
        'estimated_delivery_time' => 'integer',
        'assigned_at' => 'datetime',
        'picked_up_at' => 'datetime',
        'in_transit_at' => 'datetime',
        'delivered_at' => 'datetime',
        'cancelled_at' => 'datetime',
        'rating' => 'integer',
    ];

    /**
     * Get the order associated with the delivery
     */
    public function order(): BelongsTo
    {
        return $this->belongsTo(Order::class);
    }

    /**
     * Get the customer associated with the delivery
     */
    public function customer(): BelongsTo
    {
        return $this->belongsTo(Customer::class);
    }

    /**
     * Get the driver assigned to the delivery
     */
    public function driver(): BelongsTo
    {
        return $this->belongsTo(DeliveryDriver::class, 'driver_id');
    }

    /**
     * Scope for pending deliveries
     */
    public function scopePending($query)
    {
        return $query->where('status', 'pending');
    }

    /**
     * Scope for assigned deliveries
     */
    public function scopeAssigned($query)
    {
        return $query->where('status', 'assigned');
    }

    /**
     * Scope for in transit deliveries
     */
    public function scopeInTransit($query)
    {
        return $query->where('status', 'in_transit');
    }

    /**
     * Scope for delivered orders
     */
    public function scopeDelivered($query)
    {
        return $query->where('status', 'delivered');
    }

    /**
     * Scope for cancelled deliveries
     */
    public function scopeCancelled($query)
    {
        return $query->where('status', 'cancelled');
    }

    /**
     * Scope for active deliveries (not delivered or cancelled)
     */
    public function scopeActive($query)
    {
        return $query->whereNotIn('status', ['delivered', 'cancelled']);
    }

    /**
     * Get status label
     */
    public function getStatusLabelAttribute(): string
    {
        return match ($this->status) {
            'pending' => 'Pending Assignment',
            'assigned' => 'Driver Assigned',
            'picked_up' => 'Picked Up',
            'in_transit' => 'Out for Delivery',
            'delivered' => 'Delivered',
            'cancelled' => 'Cancelled',
            default => ucfirst($this->status),
        };
    }

    /**
     * Get status color
     */
    public function getStatusColorAttribute(): string
    {
        return match ($this->status) {
            'pending' => 'yellow',
            'assigned' => 'blue',
            'picked_up' => 'indigo',
            'in_transit' => 'purple',
            'delivered' => 'green',
            'cancelled' => 'red',
            default => 'gray',
        };
    }

    /**
     * Get delivery location
     */
    public function getDeliveryLocationAttribute(): ?array
    {
        if ($this->delivery_latitude && $this->delivery_longitude) {
            return [
                'latitude' => (float) $this->delivery_latitude,
                'longitude' => (float) $this->delivery_longitude,
            ];
        }

        return null;
    }

    /**
     * Get estimated delivery time in minutes
     */
    public function getEstimatedDeliveryMinutesAttribute(): int
    {
        return $this->estimated_delivery_time;
    }

    /**
     * Get formatted delivery fee
     */
    public function getFormattedDeliveryFeeAttribute(): string
    {
        return '$' . number_format($this->delivery_fee, 2);
    }

    /**
     * Check if delivery is active
     */
    public function isActive(): bool
    {
        return !in_array($this->status, ['delivered', 'cancelled']);
    }

    /**
     * Check if delivery is completed
     */
    public function isCompleted(): bool
    {
        return $this->status === 'delivered';
    }

    /**
     * Check if delivery is cancelled
     */
    public function isCancelled(): bool
    {
        return $this->status === 'cancelled';
    }

    /**
     * Get actual delivery time in minutes
     */
    public function getActualDeliveryTimeAttribute(): ?int
    {
        if ($this->created_at && $this->delivered_at) {
            return $this->created_at->diffInMinutes($this->delivered_at);
        }

        return null;
    }

    /**
     * Check if delivery is late
     */
    public function isLate(): bool
    {
        if (!$this->delivered_at) {
            // Check if current time exceeds estimated delivery time
            $estimatedDeliveryTime = $this->created_at->addMinutes($this->estimated_delivery_time);
            return now()->gt($estimatedDeliveryTime);
        }

        // Check if actual delivery time exceeds estimated time
        return $this->actual_delivery_time > $this->estimated_delivery_time;
    }

    /**
     * Get delivery progress percentage
     */
    public function getProgressPercentageAttribute(): int
    {
        return match ($this->status) {
            'pending' => 10,
            'assigned' => 25,
            'picked_up' => 50,
            'in_transit' => 75,
            'delivered' => 100,
            'cancelled' => 0,
            default => 0,
        };
    }

    /**
     * Generate tracking number
     */
    public static function generateTrackingNumber(): string
    {
        do {
            $trackingNumber = 'TRK-' . strtoupper(substr(md5(uniqid()), 0, 8));
        } while (static::where('tracking_number', $trackingNumber)->exists());

        return $trackingNumber;
    }

    /**
     * Auto-generate tracking number when creating
     */
    protected static function boot()
    {
        parent::boot();

        static::creating(function ($deliveryOrder) {
            if (!$deliveryOrder->tracking_number) {
                $deliveryOrder->tracking_number = static::generateTrackingNumber();
            }
        });
    }
}
