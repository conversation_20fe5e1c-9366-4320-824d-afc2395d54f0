<?php

namespace App\Auth;

use Illuminate\Auth\SessionGuard;
use Illuminate\Contracts\Auth\Authenticatable;
use Illuminate\Support\Facades\Log;

class TenantSessionGuard extends SessionGuard
{
    /**
     * Attempt to authenticate a user using the given credentials.
     *
     * @param  array  $credentials
     * @param  bool  $remember
     * @return bool
     */
    public function attempt(array $credentials = [], $remember = false)
    {
        // Ensure we're in tenant context
        if (!tenant()) {
            Log::error('Authentication attempt outside tenant context');
            return false;
        }

        Log::info('Tenant authentication attempt', [
            'tenant_id' => tenant('id'),
            'email' => $credentials['email'] ?? 'unknown',
        ]);

        return parent::attempt($credentials, $remember);
    }

    /**
     * Log a user into the application.
     *
     * @param  \Illuminate\Contracts\Auth\Authenticatable  $user
     * @param  bool  $remember
     * @return void
     */
    public function login(Authenticatable $user, $remember = false)
    {
        // Ensure we're in tenant context
        if (!tenant()) {
            Log::error('Login attempt outside tenant context', [
                'user_id' => $user->getAuthIdentifier(),
            ]);
            return;
        }

        Log::info('Tenant user login', [
            'tenant_id' => tenant('id'),
            'user_id' => $user->getAuthIdentifier(),
            'email' => $user->email ?? 'unknown',
        ]);

        parent::login($user, $remember);
    }

    /**
     * Log the user out of the application.
     *
     * @return void
     */
    public function logout()
    {
        $user = $this->user();
        
        if ($user && tenant()) {
            Log::info('Tenant user logout', [
                'tenant_id' => tenant('id'),
                'user_id' => $user->getAuthIdentifier(),
                'email' => $user->email ?? 'unknown',
            ]);
        }

        parent::logout();
    }

    /**
     * Get the currently authenticated user.
     *
     * @return \Illuminate\Contracts\Auth\Authenticatable|null
     */
    public function user()
    {
        if ($this->loggedOut) {
            return null;
        }

        // If we already have a user instance, return it
        if (!is_null($this->user)) {
            return $this->user;
        }

        $id = $this->session->get($this->getName());

        if (!is_null($id) && $this->user = $this->provider->retrieveById($id)) {
            // Fire authenticated event for the user
            $this->fireAuthenticatedEvent($this->user);

            // Log successful user retrieval for debugging
            if (tenant()) {
                Log::debug('Tenant user retrieved from session', [
                    'tenant_id' => tenant('id'),
                    'user_id' => $this->user->getAuthIdentifier(),
                    'email' => $this->user->email ?? 'unknown',
                ]);
            }
        }

        return $this->user;
    }
}
