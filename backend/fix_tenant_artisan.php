<?php

// Laravel Artisan command to fix tenant domain
echo "=== Fixing Tenant Domain via Artisan ===\n";

// Run Laravel commands to fix the tenant
$commands = [
    'php artisan tinker --execute="
        use App\Models\Tenant;
        use App\Models\SubscriptionPlan;
        
        echo \'Creating demo tenant...\' . PHP_EOL;
        
        // Get subscription plan
        \$plan = SubscriptionPlan::first();
        
        // Create or update tenant
        \$tenant = Tenant::updateOrCreate(
            [\'id\' => \'demo-restaurant\'],
            [
                \'name\' => \'Demo Restaurant\',
                \'email\' => \'<EMAIL>\',
                \'phone\' => \'******-0123\',
                \'address\' => \'123 Main Street\',
                \'city\' => \'New York\',
                \'state\' => \'NY\',
                \'country\' => \'USA\',
                \'postal_code\' => \'10001\',
                \'timezone\' => \'America/New_York\',
                \'currency\' => \'USD\',
                \'language\' => \'en\',
                \'subscription_plan_id\' => \$plan?->id,
                \'subscription_status\' => \'active\',
                \'trial_ends_at\' => now()->addDays(30),
            ]
        );
        
        echo \'Tenant: \' . \$tenant->name . PHP_EOL;
        
        // Create domain
        \$domain = \$tenant->domains()->updateOrCreate(
            [\'domain\' => \'demo-restaurant.localhost\'],
            [\'domain\' => \'demo-restaurant.localhost\']
        );
        
        echo \'Domain: \' . \$domain->domain . PHP_EOL;
        echo \'✅ Fix completed!\' . PHP_EOL;
    "'
];

foreach ($commands as $command) {
    echo "Running: " . substr($command, 0, 50) . "...\n";
    
    // Execute the command
    $output = [];
    $returnCode = 0;
    exec($command, $output, $returnCode);
    
    if ($returnCode === 0) {
        echo "✓ Command executed successfully\n";
        foreach ($output as $line) {
            echo "  $line\n";
        }
    } else {
        echo "✗ Command failed with code: $returnCode\n";
        foreach ($output as $line) {
            echo "  $line\n";
        }
    }
    echo "\n";
}

echo "=== Manual Alternative ===\n";
echo "If the above doesn't work, run this manually:\n";
echo "php artisan tinker\n";
echo "Then paste this code:\n\n";
echo '$tenant = App\Models\Tenant::updateOrCreate([\'id\' => \'demo-restaurant\'], [\'name\' => \'Demo Restaurant\', \'email\' => \'<EMAIL>\', \'subscription_status\' => \'active\']);' . "\n";
echo '$tenant->domains()->updateOrCreate([\'domain\' => \'demo-restaurant.localhost\']);' . "\n";
echo 'echo "Fixed!";' . "\n";
echo "\n";
echo "Then try accessing: http://demo-restaurant.localhost:8000\n";
