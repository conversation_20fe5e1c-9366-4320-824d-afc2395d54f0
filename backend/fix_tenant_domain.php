<?php

// Fix tenant domain script
require_once 'vendor/autoload.php';

$app = require_once 'bootstrap/app.php';
$kernel = $app->make('Illuminate\Contracts\Console\Kernel');
$kernel->bootstrap();

use App\Models\Tenant;
use App\Models\SubscriptionPlan;
use Illuminate\Support\Facades\DB;

echo "=== Fixing Tenant Domain Issue ===\n\n";

try {
    // Check if tenant exists
    $tenant = Tenant::find('demo-restaurant');
    
    if (!$tenant) {
        echo "Creating demo tenant...\n";
        
        // Get a subscription plan
        $plan = SubscriptionPlan::first();
        
        $tenant = Tenant::create([
            'id' => 'demo-restaurant',
            'name' => 'Demo Restaurant',
            'email' => '<EMAIL>',
            'phone' => '******-0123',
            'address' => '123 Main Street',
            'city' => 'New York',
            'state' => 'NY',
            'country' => 'USA',
            'postal_code' => '10001',
            'timezone' => 'America/New_York',
            'currency' => 'USD',
            'language' => 'en',
            'subscription_plan_id' => $plan?->id,
            'subscription_status' => 'active',
            'trial_ends_at' => now()->addDays(30),
        ]);
        echo "✓ Demo tenant created: {$tenant->name}\n";
    } else {
        echo "✓ Demo tenant exists: {$tenant->name}\n";
    }

    // Check domains
    $domains = $tenant->domains;
    echo "Current domains: " . $domains->count() . "\n";
    
    foreach ($domains as $domain) {
        echo "  - {$domain->domain}\n";
    }

    // Ensure the correct domain exists
    $targetDomain = 'demo-restaurant.localhost';
    $domainExists = $tenant->domains()->where('domain', $targetDomain)->exists();
    
    if (!$domainExists) {
        echo "\nCreating domain: {$targetDomain}\n";
        $tenant->domains()->create([
            'domain' => $targetDomain
        ]);
        echo "✓ Domain created successfully\n";
    } else {
        echo "✓ Domain already exists: {$targetDomain}\n";
    }

    // Verify the setup
    echo "\n=== Verification ===\n";
    $tenant = Tenant::find('demo-restaurant');
    $domains = $tenant->domains;
    
    echo "Tenant ID: {$tenant->id}\n";
    echo "Tenant Name: {$tenant->name}\n";
    echo "Domains:\n";
    foreach ($domains as $domain) {
        echo "  - {$domain->domain}\n";
    }

    // Test domain lookup
    echo "\n=== Testing Domain Lookup ===\n";
    $foundTenant = null;
    foreach ($domains as $domain) {
        if ($domain->domain === 'demo-restaurant.localhost') {
            $foundTenant = $domain->tenant;
            break;
        }
    }

    if ($foundTenant) {
        echo "✓ Domain lookup successful: {$foundTenant->name}\n";
    } else {
        echo "✗ Domain lookup failed\n";
    }

    echo "\n=== Fix Completed ===\n";
    echo "You can now access: http://demo-restaurant.localhost:8000\n";

} catch (Exception $e) {
    echo "Error: " . $e->getMessage() . "\n";
    echo "File: " . $e->getFile() . ":" . $e->getLine() . "\n";
}
