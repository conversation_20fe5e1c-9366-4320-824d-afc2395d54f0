#!/bin/bash

# Restaurant Management System - Database Setup Script
# This script sets up the complete database with all migrations and seeders

echo "🍽️  Restaurant Management System - Database Setup"
echo "=================================================="

# Check if .env file exists
if [ ! -f .env ]; then
    echo "❌ Error: .env file not found. Please copy .env.example to .env and configure your database settings."
    exit 1
fi

echo "📋 Step 1: Clearing application cache..."
php artisan config:clear
php artisan cache:clear
php artisan route:clear
php artisan view:clear

echo "🗄️  Step 2: Setting up main database..."
echo "Dropping existing database tables..."
php artisan migrate:fresh --force

echo "📦 Step 3: Running main database seeders..."
php artisan db:seed --force

echo "🏢 Step 4: Setting up tenant databases..."
echo "Running tenant migrations..."
php artisan tenants:migrate --force

echo "🌱 Step 5: Seeding tenant databases..."
php artisan tenants:seed --force

echo "🔗 Step 6: Creating storage links..."
php artisan storage:link

echo "🎉 Database setup completed successfully!"
echo ""
echo "📝 Default Login Credentials:"
echo "================================"
echo "🔐 Super Admin:"
echo "   Email: <EMAIL>"
echo "   Password: Restaurant@2024"
echo ""
echo "👨‍💼 Restaurant Manager:"
echo "   Email: <EMAIL>"
echo "   Password: Manager@2024"
echo ""
echo "🧪 Test User:"
echo "   Email: <EMAIL>"
echo "   Password: Test@2024"
echo ""
echo "🌐 Demo Tenant:"
echo "   Domain: demo-restaurant.localhost"
echo "   (Add this to your hosts file for local development)"
echo ""
echo "✅ Setup complete! You can now start the development server:"
echo "   php artisan serve"
echo ""
echo "📚 Access the application:"
echo "   Main App: http://localhost:8000"
echo "   Demo Restaurant: http://demo-restaurant.localhost:8000"
