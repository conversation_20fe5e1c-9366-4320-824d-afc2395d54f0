[90m= [39m[34;4mApp\Models\Tenant[39;24m {#7256
    [34mid[39m: "[32mdemo-restaurant[39m",
    [34mname[39m: "[32mDemo Restaurant[39m",
    [34memail[39m: "[<EMAIL>[39m",
    [34mphone[39m: "[32m******-0123[39m",
    [34maddress[39m: "[32m123 Main Street[39m",
    [34mcity[39m: "[32mNew York[39m",
    [34mstate[39m: "[32mNY[39m",
    [34mcountry[39m: "[32mUSA[39m",
    [34mpostal_code[39m: "[32m10001[39m",
    [34mtimezone[39m: "[32mAmerica/New_York[39m",
    [34mcurrency[39m: "[32mUSD[39m",
    [34mlanguage[39m: "[32men[39m",
    [34mlogo[39m: [36mnull[39m,
    [34mtheme_color[39m: "[32m#3B82F6[39m",
    [34msubscription_plan_id[39m: [35m5[39m,
    [34msubscription_status[39m: "[32mactive[39m",
    [34mtrial_ends_at[39m: "[32m2025-06-16 15:01:39[39m",
    [34msubscription_ends_at[39m: "[32m2025-07-02 15:01:39[39m",
    [34mcreated_at[39m: "[32m2025-05-29 09:01:58[39m",
    [34mupdated_at[39m: "[32m2025-05-29 09:01:58[39m",
    [34mdata[39m: [36mnull[39m,
    [34mtenancy_db_name[39m: "[32mtenant_demo-restaurant[39m",
  }

