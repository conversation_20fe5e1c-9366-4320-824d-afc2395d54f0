<?php

// Force fix menu_items table structure
require_once 'vendor/autoload.php';

$app = require_once 'bootstrap/app.php';
$kernel = $app->make('Illuminate\Contracts\Console\Kernel');
$kernel->bootstrap();

use App\Models\Tenant;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

echo "=== Force Fixing Menu Items Table Structure ===\n\n";

try {
    $tenant = Tenant::find('demo-restaurant');
    
    if (!$tenant) {
        echo "❌ Tenant 'demo-restaurant' not found!\n";
        exit(1);
    }

    echo "✓ Tenant found: {$tenant->name}\n";

    $tenant->run(function () {
        echo "\n🔧 Analyzing menu_items table...\n";
        
        if (Schema::hasTable('menu_items')) {
            // Get current structure
            $columns = Schema::getColumnListing('menu_items');
            echo "Current columns: " . implode(', ', $columns) . "\n";
            
            // Check if we have the problematic columns
            $missingColumns = [];
            $requiredColumns = ['is_active', 'is_available', 'is_featured', 'sort_order'];
            
            foreach ($requiredColumns as $col) {
                if (!in_array($col, $columns)) {
                    $missingColumns[] = $col;
                }
            }
            
            if (!empty($missingColumns)) {
                echo "Missing columns: " . implode(', ', $missingColumns) . "\n";
                
                // Backup existing data
                echo "\n📦 Backing up existing menu_items data...\n";
                $existingData = DB::table('menu_items')->get()->toArray();
                echo "Backed up " . count($existingData) . " records\n";
                
                // Drop and recreate table with correct structure
                echo "\n🔄 Recreating menu_items table with correct structure...\n";
                
                DB::statement("DROP TABLE IF EXISTS `menu_items`");
                echo "✓ Dropped old table\n";
                
                DB::statement("
                    CREATE TABLE `menu_items` (
                        `id` bigint unsigned NOT NULL AUTO_INCREMENT,
                        `name` varchar(255) NOT NULL,
                        `slug` varchar(255) NOT NULL,
                        `description` text,
                        `short_description` varchar(500) DEFAULT NULL,
                        `price` decimal(10,2) NOT NULL,
                        `cost_price` decimal(10,2) DEFAULT NULL,
                        `category_id` bigint unsigned DEFAULT NULL,
                        `image` varchar(255) DEFAULT NULL,
                        `ingredients` text,
                        `preparation_time` int DEFAULT NULL,
                        `calories` int DEFAULT NULL,
                        `is_vegetarian` tinyint(1) NOT NULL DEFAULT '0',
                        `is_vegan` tinyint(1) NOT NULL DEFAULT '0',
                        `is_gluten_free` tinyint(1) NOT NULL DEFAULT '0',
                        `is_spicy` tinyint(1) NOT NULL DEFAULT '0',
                        `is_active` tinyint(1) NOT NULL DEFAULT '1',
                        `is_available` tinyint(1) NOT NULL DEFAULT '1',
                        `is_featured` tinyint(1) NOT NULL DEFAULT '0',
                        `sort_order` int NOT NULL DEFAULT '0',
                        `created_at` timestamp NULL DEFAULT NULL,
                        `updated_at` timestamp NULL DEFAULT NULL,
                        PRIMARY KEY (`id`),
                        UNIQUE KEY `menu_items_slug_unique` (`slug`),
                        KEY `menu_items_category_id_index` (`category_id`),
                        KEY `menu_items_is_active_index` (`is_active`),
                        KEY `menu_items_is_available_index` (`is_available`),
                        KEY `menu_items_is_featured_index` (`is_featured`)
                    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
                ");
                echo "✓ Created new table with complete structure\n";
                
                // Restore data with default values for new columns
                echo "\n📥 Restoring data with new column defaults...\n";
                
                if (!empty($existingData)) {
                    foreach ($existingData as $item) {
                        $insertData = [
                            'name' => $item->name ?? 'Unknown Item',
                            'slug' => $item->slug ?? 'unknown-' . uniqid(),
                            'description' => $item->description ?? '',
                            'price' => $item->price ?? 0.00,
                            'category_id' => $item->category_id ?? null,
                            'is_active' => 1,
                            'is_available' => 1,
                            'is_featured' => 0,
                            'sort_order' => 0,
                            'is_vegetarian' => 0,
                            'is_vegan' => 0,
                            'is_gluten_free' => 0,
                            'is_spicy' => 0,
                            'created_at' => $item->created_at ?? now(),
                            'updated_at' => $item->updated_at ?? now(),
                        ];
                        
                        // Add optional fields if they exist
                        if (isset($item->image)) $insertData['image'] = $item->image;
                        if (isset($item->short_description)) $insertData['short_description'] = $item->short_description;
                        if (isset($item->cost_price)) $insertData['cost_price'] = $item->cost_price;
                        if (isset($item->ingredients)) $insertData['ingredients'] = $item->ingredients;
                        if (isset($item->preparation_time)) $insertData['preparation_time'] = $item->preparation_time;
                        if (isset($item->calories)) $insertData['calories'] = $item->calories;
                        
                        DB::table('menu_items')->insert($insertData);
                    }
                    echo "✓ Restored " . count($existingData) . " records\n";
                } else {
                    echo "No existing data to restore\n";
                }
                
            } else {
                echo "✓ All required columns already exist\n";
            }
            
        } else {
            echo "Creating menu_items table from scratch...\n";
            
            DB::statement("
                CREATE TABLE `menu_items` (
                    `id` bigint unsigned NOT NULL AUTO_INCREMENT,
                    `name` varchar(255) NOT NULL,
                    `slug` varchar(255) NOT NULL,
                    `description` text,
                    `short_description` varchar(500) DEFAULT NULL,
                    `price` decimal(10,2) NOT NULL,
                    `cost_price` decimal(10,2) DEFAULT NULL,
                    `category_id` bigint unsigned DEFAULT NULL,
                    `image` varchar(255) DEFAULT NULL,
                    `ingredients` text,
                    `preparation_time` int DEFAULT NULL,
                    `calories` int DEFAULT NULL,
                    `is_vegetarian` tinyint(1) NOT NULL DEFAULT '0',
                    `is_vegan` tinyint(1) NOT NULL DEFAULT '0',
                    `is_gluten_free` tinyint(1) NOT NULL DEFAULT '0',
                    `is_spicy` tinyint(1) NOT NULL DEFAULT '0',
                    `is_active` tinyint(1) NOT NULL DEFAULT '1',
                    `is_available` tinyint(1) NOT NULL DEFAULT '1',
                    `is_featured` tinyint(1) NOT NULL DEFAULT '0',
                    `sort_order` int NOT NULL DEFAULT '0',
                    `created_at` timestamp NULL DEFAULT NULL,
                    `updated_at` timestamp NULL DEFAULT NULL,
                    PRIMARY KEY (`id`),
                    UNIQUE KEY `menu_items_slug_unique` (`slug`),
                    KEY `menu_items_category_id_index` (`category_id`),
                    KEY `menu_items_is_active_index` (`is_active`),
                    KEY `menu_items_is_available_index` (`is_available`),
                    KEY `menu_items_is_featured_index` (`is_featured`)
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
            ");
            echo "✓ Created menu_items table\n";
        }
        
        // Add demo data if table is empty
        if (DB::table('menu_items')->count() == 0) {
            echo "\n🌱 Adding demo menu items...\n";
            
            $demoItems = [
                [
                    'name' => 'Caesar Salad',
                    'slug' => 'caesar-salad-menu',
                    'description' => 'Fresh romaine lettuce with caesar dressing, croutons, and parmesan cheese',
                    'short_description' => 'Classic caesar salad',
                    'price' => 12.99,
                    'category_id' => 1,
                    'is_active' => 1,
                    'is_available' => 1,
                    'is_featured' => 1,
                    'sort_order' => 1,
                    'is_vegetarian' => 1,
                    'preparation_time' => 10,
                    'calories' => 350,
                    'created_at' => now(),
                    'updated_at' => now(),
                ],
                [
                    'name' => 'Grilled Chicken Breast',
                    'slug' => 'grilled-chicken-breast',
                    'description' => 'Tender grilled chicken breast with herbs and spices, served with vegetables',
                    'short_description' => 'Healthy grilled chicken',
                    'price' => 18.99,
                    'category_id' => 2,
                    'is_active' => 1,
                    'is_available' => 1,
                    'is_featured' => 1,
                    'sort_order' => 1,
                    'is_vegetarian' => 0,
                    'preparation_time' => 25,
                    'calories' => 450,
                    'created_at' => now(),
                    'updated_at' => now(),
                ],
                [
                    'name' => 'Chocolate Lava Cake',
                    'slug' => 'chocolate-lava-cake',
                    'description' => 'Rich chocolate cake with molten chocolate center, served with vanilla ice cream',
                    'short_description' => 'Decadent chocolate dessert',
                    'price' => 8.99,
                    'category_id' => 3,
                    'is_active' => 1,
                    'is_available' => 1,
                    'is_featured' => 1,
                    'sort_order' => 1,
                    'is_vegetarian' => 1,
                    'preparation_time' => 15,
                    'calories' => 520,
                    'created_at' => now(),
                    'updated_at' => now(),
                ],
                [
                    'name' => 'Fresh Orange Juice',
                    'slug' => 'fresh-orange-juice',
                    'description' => 'Freshly squeezed orange juice, no added sugar',
                    'short_description' => 'Pure orange juice',
                    'price' => 4.99,
                    'category_id' => 4,
                    'is_active' => 1,
                    'is_available' => 1,
                    'is_featured' => 0,
                    'sort_order' => 1,
                    'is_vegetarian' => 1,
                    'is_vegan' => 1,
                    'preparation_time' => 5,
                    'calories' => 110,
                    'created_at' => now(),
                    'updated_at' => now(),
                ],
            ];
            
            foreach ($demoItems as $item) {
                DB::table('menu_items')->insert($item);
            }
            
            echo "✓ Added " . count($demoItems) . " demo menu items\n";
        }
        
        // Final verification
        echo "\n🔍 Final verification...\n";
        
        $finalColumns = Schema::getColumnListing('menu_items');
        echo "Final columns: " . implode(', ', $finalColumns) . "\n";
        
        // Test the problematic queries
        try {
            $activeCount = DB::table('menu_items')->where('is_active', 1)->count();
            echo "✅ Active menu items query: {$activeCount} items\n";
            
            $availableCount = DB::table('menu_items')->where('is_active', 1)->where('is_available', 1)->count();
            echo "✅ Active & available query: {$availableCount} items\n";
            
            $featuredCount = DB::table('menu_items')->where('is_featured', 1)->count();
            echo "✅ Featured items query: {$featuredCount} items\n";
            
        } catch (Exception $e) {
            echo "❌ Query test failed: " . $e->getMessage() . "\n";
        }
    });

    echo "\n🎉 Menu items table fix completed!\n";
    echo "\n📋 Summary:\n";
    echo "✅ Menu items table recreated with complete schema\n";
    echo "✅ All required columns added (is_active, is_available, etc.)\n";
    echo "✅ Existing data preserved and migrated\n";
    echo "✅ Demo data added if table was empty\n";
    echo "✅ Indexes created for performance\n";
    echo "\n🧪 Test the fix:\n";
    echo "Run the verification command again:\n";
    echo "php artisan tinker --execute=\"\$tenant = App\\Models\\Tenant::find('demo-restaurant'); \$tenant->run(function () { \$count = DB::table('menu_items')->where('is_active', 1)->count(); echo 'Active menu items: ' . \$count; });\"\n";
    echo "\n✅ The 'Unknown column is_active' error should be completely resolved!\n";

} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
    echo "File: " . $e->getFile() . ":" . $e->getLine() . "\n";
}
