<?php

// Manual seeding script for multi-tenant setup
require_once 'vendor/autoload.php';

$app = require_once 'bootstrap/app.php';
$kernel = $app->make('Illuminate\Contracts\Console\Kernel');
$kernel->bootstrap();

use App\Models\User;
use App\Models\Tenant;
use App\Models\SubscriptionPlan;
use Spatie\Permission\Models\Role;
use Spatie\Permission\Models\Permission;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\DB;

echo "=== Manual Database Seeding ===\n\n";

try {
    // 1. Create Subscription Plans
    echo "1. Creating subscription plans...\n";
    $plans = [
        [
            'name' => 'Basic Plan',
            'slug' => 'basic',
            'description' => 'Perfect for small restaurants',
            'price' => 29.99,
            'billing_cycle' => 'monthly',
            'features' => json_encode(['Basic POS', 'Menu Management', 'Order Tracking']),
            'max_locations' => 1,
            'max_users' => 5,
            'is_active' => true,
        ],
        [
            'name' => 'Professional Plan',
            'slug' => 'professional',
            'description' => 'Great for growing restaurants',
            'price' => 59.99,
            'billing_cycle' => 'monthly',
            'features' => json_encode(['Advanced POS', 'Inventory Management', 'Analytics', 'Multi-location']),
            'max_locations' => 3,
            'max_users' => 15,
            'is_active' => true,
        ],
        [
            'name' => 'Enterprise Plan',
            'slug' => 'enterprise',
            'description' => 'For large restaurant chains',
            'price' => 99.99,
            'billing_cycle' => 'monthly',
            'features' => json_encode(['Full POS Suite', 'Advanced Analytics', 'Custom Integrations', 'Unlimited Locations']),
            'max_locations' => -1,
            'max_users' => -1,
            'is_active' => true,
        ]
    ];

    foreach ($plans as $plan) {
        SubscriptionPlan::firstOrCreate(['slug' => $plan['slug']], $plan);
        echo "   ✓ {$plan['name']}\n";
    }

    // 2. Create Permissions
    echo "\n2. Creating permissions...\n";
    $permissions = [
        // Central Admin Permissions
        'manage_tenants', 'manage_subscriptions', 'manage_payments', 'view_admin_dashboard', 'manage_system_settings',
        
        // Restaurant Management
        'manage_restaurant', 'view_restaurant_dashboard', 'manage_restaurant_settings',
        
        // Menu Management
        'manage_menu', 'view_menu', 'create_menu_items', 'edit_menu_items', 'delete_menu_items',
        
        // Order Management
        'manage_orders', 'view_orders', 'create_orders', 'edit_orders', 'cancel_orders',
        
        // Customer Management
        'manage_customers', 'view_customers', 'create_customers', 'edit_customers',
        
        // Staff Management
        'manage_staff', 'view_staff', 'create_staff', 'edit_staff', 'delete_staff',
        
        // Inventory Management
        'manage_inventory', 'view_inventory', 'update_stock', 'manage_suppliers',
        
        // Financial Management
        'manage_expenses', 'view_expenses', 'create_expenses', 'approve_expenses',
        'view_reports', 'view_sales_reports', 'view_financial_reports',
        
        // Table & Reservation Management
        'manage_tables', 'view_tables', 'manage_reservations', 'view_reservations',
        
        // Delivery Management
        'manage_delivery', 'view_delivery', 'assign_delivery', 'track_delivery',
        
        // Review Management
        'manage_reviews', 'view_reviews', 'respond_to_reviews'
    ];

    foreach ($permissions as $permission) {
        Permission::firstOrCreate(['name' => $permission]);
    }
    echo "   ✓ Created " . count($permissions) . " permissions\n";

    // 3. Create Roles
    echo "\n3. Creating roles...\n";
    
    // Admin role (has all permissions)
    $adminRole = Role::firstOrCreate(['name' => 'admin']);
    $adminRole->syncPermissions(Permission::all());
    echo "   ✓ Admin role (all permissions)\n";

    // Restaurant Manager role
    $managerRole = Role::firstOrCreate(['name' => 'restaurant_manager']);
    $managerPermissions = Permission::whereIn('name', [
        'manage_restaurant', 'view_restaurant_dashboard', 'manage_restaurant_settings',
        'manage_menu', 'view_menu', 'create_menu_items', 'edit_menu_items', 'delete_menu_items',
        'manage_orders', 'view_orders', 'create_orders', 'edit_orders', 'cancel_orders',
        'manage_customers', 'view_customers', 'create_customers', 'edit_customers',
        'manage_staff', 'view_staff', 'create_staff', 'edit_staff', 'delete_staff',
        'manage_inventory', 'view_inventory', 'update_stock', 'manage_suppliers',
        'manage_expenses', 'view_expenses', 'create_expenses', 'approve_expenses',
        'view_reports', 'view_sales_reports', 'view_financial_reports',
        'manage_tables', 'view_tables', 'manage_reservations', 'view_reservations',
        'manage_delivery', 'view_delivery', 'assign_delivery', 'track_delivery',
        'manage_reviews', 'view_reviews', 'respond_to_reviews'
    ])->get();
    $managerRole->syncPermissions($managerPermissions);
    echo "   ✓ Restaurant Manager role\n";

    // Waiter role
    $waiterRole = Role::firstOrCreate(['name' => 'waiter']);
    $waiterPermissions = Permission::whereIn('name', [
        'view_menu', 'manage_orders', 'view_orders', 'create_orders', 'edit_orders',
        'view_customers', 'create_customers', 'edit_customers',
        'manage_tables', 'view_tables', 'manage_reservations', 'view_reservations'
    ])->get();
    $waiterRole->syncPermissions($waiterPermissions);
    echo "   ✓ Waiter role\n";

    // Kitchen role
    $kitchenRole = Role::firstOrCreate(['name' => 'kitchen']);
    $kitchenPermissions = Permission::whereIn('name', [
        'view_menu', 'view_orders', 'edit_orders',
        'view_inventory', 'update_stock'
    ])->get();
    $kitchenRole->syncPermissions($kitchenPermissions);
    echo "   ✓ Kitchen role\n";

    // Delivery role
    $deliveryRole = Role::firstOrCreate(['name' => 'delivery']);
    $deliveryPermissions = Permission::whereIn('name', [
        'view_orders', 'view_delivery', 'track_delivery',
        'view_customers'
    ])->get();
    $deliveryRole->syncPermissions($deliveryPermissions);
    echo "   ✓ Delivery role\n";

    // 4. Create Users
    echo "\n4. Creating users...\n";
    
    // Super Admin
    $admin = User::firstOrCreate(
        ['email' => '<EMAIL>'],
        [
            'name' => 'Super Admin',
            'password' => Hash::make('Restaurant@2024'),
            'email_verified_at' => now(),
            'role' => 'admin',
            'preferred_language' => 'en',
            'theme_preference' => 'light',
            'is_active' => true,
        ]
    );
    if (!$admin->hasRole('admin')) {
        $admin->assignRole('admin');
    }
    echo "   ✓ Super Admin created\n";

    // Restaurant Manager
    $manager = User::firstOrCreate(
        ['email' => '<EMAIL>'],
        [
            'name' => 'Restaurant Manager',
            'password' => Hash::make('Manager@2024'),
            'email_verified_at' => now(),
            'role' => 'restaurant_manager',
            'preferred_language' => 'en',
            'theme_preference' => 'light',
            'is_active' => true,
        ]
    );
    if (!$manager->hasRole('restaurant_manager')) {
        $manager->assignRole('restaurant_manager');
    }
    echo "   ✓ Restaurant Manager created\n";

    // Additional staff
    $staff = [
        ['name' => 'John Smith', 'email' => '<EMAIL>', 'role' => 'waiter', 'password' => 'Waiter@2024'],
        ['name' => 'Chef Maria', 'email' => '<EMAIL>', 'role' => 'kitchen', 'password' => 'Kitchen@2024'],
        ['name' => 'Ahmed Rahman', 'email' => '<EMAIL>', 'role' => 'delivery', 'password' => 'Delivery@2024'],
    ];

    foreach ($staff as $member) {
        $user = User::firstOrCreate(
            ['email' => $member['email']],
            [
                'name' => $member['name'],
                'password' => Hash::make($member['password']),
                'email_verified_at' => now(),
                'role' => $member['role'],
                'preferred_language' => 'en',
                'theme_preference' => 'light',
                'is_active' => true,
            ]
        );
        if (!$user->hasRole($member['role'])) {
            $user->assignRole($member['role']);
        }
        echo "   ✓ {$member['name']} ({$member['role']}) created\n";
    }

    // 5. Create Demo Tenant
    echo "\n5. Creating demo tenant...\n";
    $plan = SubscriptionPlan::where('slug', 'professional')->first();
    
    $tenant = Tenant::firstOrCreate(
        ['id' => 'demo-restaurant'],
        [
            'name' => 'Demo Restaurant',
            'email' => '<EMAIL>',
            'phone' => '******-0123',
            'address' => '123 Main Street',
            'city' => 'New York',
            'state' => 'NY',
            'country' => 'USA',
            'postal_code' => '10001',
            'timezone' => 'America/New_York',
            'currency' => 'USD',
            'language' => 'en',
            'subscription_plan_id' => $plan?->id,
            'subscription_status' => 'active',
            'trial_ends_at' => now()->addDays(30),
        ]
    );

    // Create domain
    $tenant->domains()->firstOrCreate([
        'domain' => 'demo-restaurant.localhost',
    ]);
    
    echo "   ✓ Demo tenant created with domain: demo-restaurant.localhost\n";

    echo "\n=== Seeding Completed Successfully! ===\n";
    echo "\nLogin Credentials:\n";
    echo "Super Admin: <EMAIL> / Restaurant@2024\n";
    echo "Manager: <EMAIL> / Manager@2024\n";
    echo "Waiter: <EMAIL> / Waiter@2024\n";
    echo "Kitchen: <EMAIL> / Kitchen@2024\n";
    echo "Delivery: <EMAIL> / Delivery@2024\n";

} catch (Exception $e) {
    echo "Error: " . $e->getMessage() . "\n";
    echo "File: " . $e->getFile() . ":" . $e->getLine() . "\n";
}
