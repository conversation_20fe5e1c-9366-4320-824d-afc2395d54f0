<!DOCTYPE html>
<html>
<head>
    <title>Google Maps Test</title>
    <style>
        #map {
            height: 400px;
            width: 100%;
        }
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
        }
        .success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
    </style>
</head>
<body>
    <h1>Google Maps API Test</h1>
    <div id="status" class="status">Loading...</div>
    <div id="map"></div>

    <script>
        function initMap() {
            try {
                const map = new google.maps.Map(document.getElementById('map'), {
                    zoom: 15,
                    center: { lat: 23.8103, lng: 90.4125 } // Dhaka, Bangladesh
                });

                const marker = new google.maps.Marker({
                    position: { lat: 23.8103, lng: 90.4125 },
                    map: map,
                    title: 'Test Location'
                });

                document.getElementById('status').className = 'status success';
                document.getElementById('status').textContent = 'Google Maps loaded successfully!';
            } catch (error) {
                document.getElementById('status').className = 'status error';
                document.getElementById('status').textContent = 'Error loading Google Maps: ' + error.message;
            }
        }

        function gm_authFailure() {
            document.getElementById('status').className = 'status error';
            document.getElementById('status').textContent = 'Google Maps authentication failed. Please check your API key.';
        }

        // Handle script load errors
        window.addEventListener('error', function(e) {
            if (e.target.src && e.target.src.includes('maps.googleapis.com')) {
                document.getElementById('status').className = 'status error';
                document.getElementById('status').textContent = 'Failed to load Google Maps script.';
            }
        });
    </script>

    <script async defer 
            src="https://maps.googleapis.com/maps/api/js?key=AIzaSyBOti4mM-6x9WDnZIjIeyb21_d_aRjQbR4&callback=initMap"
            onerror="document.getElementById('status').className='status error'; document.getElementById('status').textContent='Failed to load Google Maps script.';">
    </script>
</body>
</html>
