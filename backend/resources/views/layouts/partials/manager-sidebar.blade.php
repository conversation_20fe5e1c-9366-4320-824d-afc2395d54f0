{{-- Dashboard --}}
<a href="{{ route('manager.dashboard') }}" 
   class="group flex items-center px-2 py-2 text-sm font-medium rounded-md {{ request()->routeIs('manager.dashboard') ? 'bg-primary-100 text-primary-700 dark:bg-primary-900 dark:text-primary-200' : 'text-gray-600 hover:bg-gray-50 hover:text-gray-900 dark:text-gray-300 dark:hover:bg-gray-700 dark:hover:text-white' }}">
    <i class="fas fa-tachometer-alt mr-3 text-lg"></i>
    {{ __('Dashboard') }}
</a>

{{-- Analytics --}}
<a href="{{ route('manager.analytics') }}" 
   class="group flex items-center px-2 py-2 text-sm font-medium rounded-md {{ request()->routeIs('manager.analytics*') ? 'bg-primary-100 text-primary-700 dark:bg-primary-900 dark:text-primary-200' : 'text-gray-600 hover:bg-gray-50 hover:text-gray-900 dark:text-gray-300 dark:hover:bg-gray-700 dark:hover:text-white' }}">
    <i class="fas fa-chart-bar mr-3 text-lg"></i>
    {{ __('Analytics') }}
</a>

{{-- Orders --}}
<div class="space-y-1">
    <button onclick="toggleSubmenu('orders')" 
            class="group w-full flex items-center px-2 py-2 text-sm font-medium rounded-md text-gray-600 hover:bg-gray-50 hover:text-gray-900 dark:text-gray-300 dark:hover:bg-gray-700 dark:hover:text-white">
        <i class="fas fa-shopping-cart mr-3 text-lg"></i>
        {{ __('Orders') }}
        <i class="fas fa-chevron-down ml-auto transform transition-transform" id="orders-chevron"></i>
    </button>
    <div id="orders-submenu" class="ml-6 space-y-1 hidden">
        <a href="{{ route('manager.orders.index') }}" 
           class="group flex items-center px-2 py-2 text-sm rounded-md {{ request()->routeIs('manager.orders.index') ? 'bg-primary-100 text-primary-700 dark:bg-primary-900 dark:text-primary-200' : 'text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200' }}">
            <i class="fas fa-list mr-2"></i>
            {{ __('All Orders') }}
        </a>
        <a href="{{ route('manager.kitchen.index') }}" 
           class="group flex items-center px-2 py-2 text-sm rounded-md {{ request()->routeIs('manager.kitchen*') ? 'bg-primary-100 text-primary-700 dark:bg-primary-900 dark:text-primary-200' : 'text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200' }}">
            <i class="fas fa-utensils mr-2"></i>
            {{ __('Kitchen Orders') }}
        </a>
        <a href="{{ route('manager.orders.create') }}" 
           class="group flex items-center px-2 py-2 text-sm rounded-md {{ request()->routeIs('manager.orders.create') ? 'bg-primary-100 text-primary-700 dark:bg-primary-900 dark:text-primary-200' : 'text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200' }}">
            <i class="fas fa-plus mr-2"></i>
            {{ __('Create Order') }}
        </a>
    </div>
</div>

{{-- Menu Management --}}
<div class="space-y-1">
    <button onclick="toggleSubmenu('menu')" 
            class="group w-full flex items-center px-2 py-2 text-sm font-medium rounded-md text-gray-600 hover:bg-gray-50 hover:text-gray-900 dark:text-gray-300 dark:hover:bg-gray-700 dark:hover:text-white">
        <i class="fas fa-utensils mr-3 text-lg"></i>
        {{ __('Menu Management') }}
        <i class="fas fa-chevron-down ml-auto transform transition-transform" id="menu-chevron"></i>
    </button>
    <div id="menu-submenu" class="ml-6 space-y-1 hidden">
        <a href="{{ route('manager.menu-items.index') }}" 
           class="group flex items-center px-2 py-2 text-sm rounded-md {{ request()->routeIs('manager.menu-items*') ? 'bg-primary-100 text-primary-700 dark:bg-primary-900 dark:text-primary-200' : 'text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200' }}">
            <i class="fas fa-hamburger mr-2"></i>
            {{ __('Menu Items') }}
        </a>
        <a href="{{ route('manager.categories.index') }}" 
           class="group flex items-center px-2 py-2 text-sm rounded-md {{ request()->routeIs('manager.categories*') ? 'bg-primary-100 text-primary-700 dark:bg-primary-900 dark:text-primary-200' : 'text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200' }}">
            <i class="fas fa-tags mr-2"></i>
            {{ __('Categories') }}
        </a>
    </div>
</div>

{{-- Space Management --}}
<div class="space-y-1">
    <button onclick="toggleSubmenu('space')"
            class="group w-full flex items-center px-2 py-2 text-sm font-medium rounded-md {{ request()->routeIs('manager.branches*', 'manager.floors*', 'manager.tables*') ? 'bg-primary-100 text-primary-700 dark:bg-primary-900 dark:text-primary-200' : 'text-gray-600 hover:bg-gray-50 hover:text-gray-900 dark:text-gray-300 dark:hover:bg-gray-700 dark:hover:text-white' }}">
        <i class="fas fa-building mr-3 text-lg"></i>
        {{ __('Space Management') }}
        <i class="fas fa-chevron-down ml-auto transform transition-transform" id="space-chevron"></i>
    </button>
    <div id="space-submenu" class="ml-6 space-y-1 {{ request()->routeIs('manager.branches*', 'manager.floors*', 'manager.tables*') ? '' : 'hidden' }}">
        <!-- Branches -->
        <a href="{{ route('manager.branches.index') }}"
           class="group flex items-center px-2 py-2 text-sm rounded-md {{ request()->routeIs('manager.branches*') ? 'bg-primary-100 text-primary-700 dark:bg-primary-900 dark:text-primary-200' : 'text-gray-500 hover:text-gray-700 hover:bg-gray-50 dark:text-gray-400 dark:hover:text-gray-200 dark:hover:bg-gray-700' }}">
            <i class="fas fa-store mr-2 text-blue-500"></i>
            {{ __('Branches') }}
            <span class="ml-auto text-xs bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200 px-2 py-0.5 rounded-full">
                {{ \App\Models\Tenant\Branch::count() }}
            </span>
        </a>

        <!-- Floors -->
        <a href="{{ route('manager.floors.index') }}"
           class="group flex items-center px-2 py-2 text-sm rounded-md {{ request()->routeIs('manager.floors*') ? 'bg-primary-100 text-primary-700 dark:bg-primary-900 dark:text-primary-200' : 'text-gray-500 hover:text-gray-700 hover:bg-gray-50 dark:text-gray-400 dark:hover:text-gray-200 dark:hover:bg-gray-700' }}">
            <i class="fas fa-layer-group mr-2 text-green-500"></i>
            {{ __('Floors') }}
            <span class="ml-auto text-xs bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200 px-2 py-0.5 rounded-full">
                {{ \App\Models\Tenant\Floor::count() }}
            </span>
        </a>

        <!-- Tables -->
        <a href="{{ route('manager.tables.index') }}"
           class="group flex items-center px-2 py-2 text-sm rounded-md {{ request()->routeIs('manager.tables*') ? 'bg-primary-100 text-primary-700 dark:bg-primary-900 dark:text-primary-200' : 'text-gray-500 hover:text-gray-700 hover:bg-gray-50 dark:text-gray-400 dark:hover:text-gray-200 dark:hover:bg-gray-700' }}">
            <i class="fas fa-chair mr-2 text-yellow-500"></i>
            {{ __('Tables') }}
            <span class="ml-auto text-xs bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200 px-2 py-0.5 rounded-full">
                {{ \App\Models\Tenant\Table::count() }}
            </span>
        </a>

        <!-- Divider -->
        <div class="border-t border-gray-200 dark:border-gray-700 my-2"></div>

        <!-- Quick Actions -->
        <div class="px-2 py-1">
            <p class="text-xs font-medium text-gray-400 dark:text-gray-500 uppercase tracking-wider">
                {{ __('Quick Actions') }}
            </p>
        </div>

        <a href="{{ route('manager.branches.create') }}"
           class="group flex items-center px-2 py-1.5 text-xs rounded-md text-gray-500 hover:text-blue-600 hover:bg-blue-50 dark:text-gray-400 dark:hover:text-blue-400 dark:hover:bg-blue-900">
            <i class="fas fa-plus mr-2"></i>
            {{ __('Add Branch') }}
        </a>

        <a href="{{ route('manager.floors.create') }}"
           class="group flex items-center px-2 py-1.5 text-xs rounded-md text-gray-500 hover:text-green-600 hover:bg-green-50 dark:text-gray-400 dark:hover:text-green-400 dark:hover:bg-green-900">
            <i class="fas fa-plus mr-2"></i>
            {{ __('Add Floor') }}
        </a>

        <a href="{{ route('manager.tables.create') }}"
           class="group flex items-center px-2 py-1.5 text-xs rounded-md text-gray-500 hover:text-yellow-600 hover:bg-yellow-50 dark:text-gray-400 dark:hover:text-yellow-400 dark:hover:bg-yellow-900">
            <i class="fas fa-plus mr-2"></i>
            {{ __('Add Table') }}
        </a>
    </div>
</div>

{{-- Staff Management --}}
<div class="space-y-1">
    <button onclick="toggleSubmenu('staff')" 
            class="group w-full flex items-center px-2 py-2 text-sm font-medium rounded-md text-gray-600 hover:bg-gray-50 hover:text-gray-900 dark:text-gray-300 dark:hover:bg-gray-700 dark:hover:text-white">
        <i class="fas fa-users mr-3 text-lg"></i>
        {{ __('Staff Management') }}
        <i class="fas fa-chevron-down ml-auto transform transition-transform" id="staff-chevron"></i>
    </button>
    <div id="staff-submenu" class="ml-6 space-y-1 hidden">
        <a href="{{ route('manager.staff.index') }}" 
           class="group flex items-center px-2 py-2 text-sm rounded-md {{ request()->routeIs('manager.staff*') ? 'bg-primary-100 text-primary-700 dark:bg-primary-900 dark:text-primary-200' : 'text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200' }}">
            <i class="fas fa-user-tie mr-2"></i>
            {{ __('Staff') }}
        </a>
        <a href="{{ route('manager.departments.index') }}" 
           class="group flex items-center px-2 py-2 text-sm rounded-md {{ request()->routeIs('manager.departments*') ? 'bg-primary-100 text-primary-700 dark:bg-primary-900 dark:text-primary-200' : 'text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200' }}">
            <i class="fas fa-sitemap mr-2"></i>
            {{ __('Departments') }}
        </a>
    </div>
</div>

{{-- Customers --}}
<a href="{{ route('manager.customers.index') }}" 
   class="group flex items-center px-2 py-2 text-sm font-medium rounded-md {{ request()->routeIs('manager.customers*') ? 'bg-primary-100 text-primary-700 dark:bg-primary-900 dark:text-primary-200' : 'text-gray-600 hover:bg-gray-50 hover:text-gray-900 dark:text-gray-300 dark:hover:bg-gray-700 dark:hover:text-white' }}">
    <i class="fas fa-user-friends mr-3 text-lg"></i>
    {{ __('Customers') }}
</a>

{{-- Reports --}}
<a href="{{ route('manager.reports.index') }}" 
   class="group flex items-center px-2 py-2 text-sm font-medium rounded-md {{ request()->routeIs('manager.reports*') ? 'bg-primary-100 text-primary-700 dark:bg-primary-900 dark:text-primary-200' : 'text-gray-600 hover:bg-gray-50 hover:text-gray-900 dark:text-gray-300 dark:hover:bg-gray-700 dark:hover:text-white' }}">
    <i class="fas fa-file-alt mr-3 text-lg"></i>
    {{ __('Reports') }}
</a>

{{-- Settings --}}
<a href="{{ route('manager.settings.index') }}" 
   class="group flex items-center px-2 py-2 text-sm font-medium rounded-md {{ request()->routeIs('manager.settings*') ? 'bg-primary-100 text-primary-700 dark:bg-primary-900 dark:text-primary-200' : 'text-gray-600 hover:bg-gray-50 hover:text-gray-900 dark:text-gray-300 dark:hover:bg-gray-700 dark:hover:text-white' }}">
    <i class="fas fa-cog mr-3 text-lg"></i>
    {{ __('Settings') }}
</a>

<script>
function toggleSubmenu(menuId) {
    const submenu = document.getElementById(menuId + '-submenu');
    const chevron = document.getElementById(menuId + '-chevron');
    
    submenu.classList.toggle('hidden');
    chevron.classList.toggle('rotate-180');
}

// Auto-expand active submenus
document.addEventListener('DOMContentLoaded', function() {
    // Check if we're on a space management page
    const currentPath = window.location.pathname;
    const spaceManagementPaths = ['/manager/branches', '/manager/floors', '/manager/tables'];
    const isSpaceManagementPage = spaceManagementPaths.some(path => currentPath.includes(path));

    if (isSpaceManagementPage) {
        const submenu = document.getElementById('space-submenu');
        const chevron = document.getElementById('space-chevron');

        if (submenu && chevron) {
            submenu.classList.remove('hidden');
            chevron.classList.add('rotate-180');
        }
    }

    // Auto-expand other active submenus
    const activeSubmenus = ['orders', 'menu', 'staff']; // Add other submenus that should be auto-expanded based on current route

    activeSubmenus.forEach(menuId => {
        const submenu = document.getElementById(menuId + '-submenu');
        const chevron = document.getElementById(menuId + '-chevron');

        if (submenu && !submenu.classList.contains('hidden')) {
            if (chevron) chevron.classList.add('rotate-180');
        }
    });
});
</script>
