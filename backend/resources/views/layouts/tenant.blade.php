<!DOCTYPE html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}" class="{{ request()->cookie('theme', 'light') }}">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="csrf-token" content="{{ csrf_token() }}">

    <!-- SEO Meta Tags -->
    <title>@yield('title', $siteSettings['general']['site_name'] ?? $restaurant->name ?? 'Restaurant')</title>
    <meta name="description" content="@yield('description', $siteSettings['general']['site_description'] ?? $restaurant->description ?? 'Delicious food, great atmosphere, unforgettable experience')">
    <meta name="keywords" content="@yield('keywords', 'restaurant, food, dining, ' . ($restaurant->name ?? 'restaurant'))">
    <meta name="author" content="{{ $siteSettings['general']['site_name'] ?? $restaurant->name ?? 'Restaurant' }}">
    <link rel="canonical" href="@yield('canonical', request()->url())"
    
    <!-- Language and Locale -->
    <meta name="language" content="{{ app()->getLocale() }}">
    <meta property="og:locale" content="{{ app()->getLocale() === 'bn' ? 'bn_BD' : 'en_US' }}">
    
    <!-- Open Graph Meta Tags -->
    <meta property="og:title" content="@yield('og_title', $siteSettings['general']['site_name'] ?? $restaurant->name ?? 'Restaurant')">
    <meta property="og:description" content="@yield('og_description', $siteSettings['general']['site_description'] ?? $restaurant->description ?? 'Delicious food, great atmosphere')">
    <meta property="og:type" content="@yield('og_type', 'website')">
    <meta property="og:url" content="@yield('og_url', request()->url())">
    <meta property="og:site_name" content="{{ $siteSettings['general']['site_name'] ?? $restaurant->name ?? 'Restaurant' }}">
    @if(isset($ogImage))
        <meta property="og:image" content="{{ $ogImage }}">
        <meta property="og:image:width" content="1200">
        <meta property="og:image:height" content="630">
    @endif
    
    <!-- Twitter Card Meta Tags -->
    <meta name="twitter:card" content="summary_large_image">
    <meta name="twitter:title" content="@yield('twitter_title', $siteSettings['general']['site_name'] ?? $restaurant->name ?? 'Restaurant')">
    <meta name="twitter:description" content="@yield('twitter_description', $siteSettings['general']['site_description'] ?? $restaurant->description ?? 'Delicious food, great atmosphere')">
    @if(isset($ogImage))
        <meta name="twitter:image" content="{{ $ogImage }}">
    @endif
    
    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="{{ asset('favicon.ico') }}">
    <link rel="apple-touch-icon" href="{{ asset('apple-touch-icon.png') }}">
    
    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.bunny.net">
    <link href="https://fonts.bunny.net/css?family=figtree:400,500,600&display=swap" rel="stylesheet" />
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <!-- Styles -->
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            darkMode: 'class',
            theme: {
                extend: {
                    colors: {
                        primary: {
                            50: '#fff7ed',
                            100: '#ffedd5',
                            200: '#fed7aa',
                            300: '#fdba74',
                            400: '#fb923c',
                            500: '#f97316',
                            600: '#ea580c',
                            700: '#c2410c',
                            800: '#9a3412',
                            900: '#7c2d12',
                        }
                    }
                }
            }
        }
    </script>
    
    <!-- Custom Styles -->
    <style>
        .dropdown-enter-active, .dropdown-leave-active {
            transition: all 0.2s ease;
        }
        .dropdown-enter-from, .dropdown-leave-to {
            opacity: 0;
            transform: translateY(-10px);
        }
        .theme-transition {
            transition: background-color 0.3s ease, color 0.3s ease;
        }
        .cart-badge {
            animation: bounce 0.5s ease-in-out;
        }
        @keyframes bounce {
            0%, 20%, 50%, 80%, 100% { transform: translateY(0); }
            40% { transform: translateY(-10px); }
            60% { transform: translateY(-5px); }
        }
    </style>
    
    @stack('head')
</head>
<body class="min-h-screen bg-white dark:bg-gray-900 theme-transition">
    <!-- Navigation -->
    <nav class="bg-white dark:bg-gray-800 shadow-lg sticky top-0 z-50 theme-transition">
        <div class="container mx-auto px-4">
            <div class="flex justify-between items-center py-4">
                <!-- Logo -->
                <a href="{{ route('guest.home') }}" class="flex items-center space-x-2">
                    <div class="w-10 h-10 bg-primary-600 rounded-lg flex items-center justify-center">
                        <i class="fas fa-utensils text-white"></i>
                    </div>
                    <span class="text-xl font-bold text-gray-800 dark:text-white">
                        {{ $siteSettings['general']['site_name'] ?? $restaurant->name ?? 'Restaurant' }}
                    </span>
                </a>

                <!-- Desktop Navigation -->
                <div class="hidden md:flex items-center space-x-6">
                    <a href="{{ route('guest.home') }}"
                       class="text-gray-800 dark:text-gray-200 hover:text-primary-600 dark:hover:text-primary-400 transition-colors {{ request()->routeIs('guest.home') ? 'text-primary-600 dark:text-primary-400 font-semibold' : '' }}">
                        {{ __('Home') }}
                    </a>
                    <a href="{{ route('guest.menu') }}"
                       class="text-gray-800 dark:text-gray-200 hover:text-primary-600 dark:hover:text-primary-400 transition-colors {{ request()->routeIs('guest.menu*') ? 'text-primary-600 dark:text-primary-400 font-semibold' : '' }}">
                        {{ __('Menu') }}
                    </a>
                    
                    <!-- Cart Button -->
                    <button onclick="toggleCart()" 
                            class="relative bg-primary-600 text-white px-4 py-2 rounded-lg hover:bg-primary-700 transition-colors">
                        <i class="fas fa-shopping-cart"></i>
                        <span id="cart-count" class="cart-count hidden absolute -top-2 -right-2 bg-red-500 text-white text-xs rounded-full h-5 w-5 flex items-center justify-center cart-badge">
                            0
                        </span>
                    </button>

                    <!-- Three-Dot Dropdown Menu -->
                    <div class="relative">
                        <button onclick="toggleDropdown()"
                                class="p-2 text-gray-800 dark:text-gray-200 hover:text-primary-600 dark:hover:text-primary-400 transition-colors"
                                aria-label="More options">
                            <i class="fas fa-ellipsis-v"></i>
                        </button>
                        
                        <!-- Dropdown Menu -->
                        <div id="dropdown-menu" 
                             class="hidden absolute right-0 mt-2 w-48 bg-white dark:bg-gray-800 rounded-md shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none z-50">
                            <div class="py-1">
                                <!-- Language Switcher -->
                                <div class="px-4 py-2 text-xs font-semibold text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                                    {{ __('Language') }}
                                </div>
                                <button onclick="switchLanguage('en')"
                                        class="flex items-center w-full px-4 py-2 text-sm text-gray-800 dark:text-gray-200 hover:bg-gray-100 dark:hover:bg-gray-700 {{ app()->getLocale() === 'en' ? 'bg-gray-100 dark:bg-gray-700' : '' }}">
                                    <i class="fas fa-globe mr-3"></i>
                                    English
                                </button>
                                <button onclick="switchLanguage('bn')"
                                        class="flex items-center w-full px-4 py-2 text-sm text-gray-800 dark:text-gray-200 hover:bg-gray-100 dark:hover:bg-gray-700 {{ app()->getLocale() === 'bn' ? 'bg-gray-100 dark:bg-gray-700' : '' }}">
                                    <i class="fas fa-globe mr-3"></i>
                                    বাংলা
                                </button>
                                
                                <hr class="my-1 border-gray-200 dark:border-gray-600">
                                
                                <!-- Theme Toggle -->
                                <div class="px-4 py-2 text-xs font-semibold text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                                    {{ __('Theme') }}
                                </div>
                                <button onclick="setTheme('light')" 
                                        class="flex items-center w-full px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700">
                                    <i class="fas fa-sun mr-3"></i>
                                    {{ __('Light') }}
                                </button>
                                <button onclick="setTheme('dark')" 
                                        class="flex items-center w-full px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700">
                                    <i class="fas fa-moon mr-3"></i>
                                    {{ __('Dark') }}
                                </button>
                                <button onclick="setTheme('system')" 
                                        class="flex items-center w-full px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700">
                                    <i class="fas fa-desktop mr-3"></i>
                                    {{ __('System') }}
                                </button>
                                
                                <hr class="my-1 border-gray-200 dark:border-gray-600">
                                
                                <!-- Contact -->
                                @if(isset($siteSettings['contact']['phone']))
                                <a href="tel:{{ $siteSettings['contact']['phone'] }}" 
                                   class="flex items-center w-full px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700">
                                    <i class="fas fa-phone mr-3"></i>
                                    {{ __('Call Now') }}
                                </a>
                                @endif
                                
                                <!-- Staff Login -->
                                <a href="{{ route('login') }}" 
                                   class="flex items-center w-full px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700">
                                    <i class="fas fa-user mr-3"></i>
                                    {{ __('Staff Login') }}
                                </a>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Mobile Menu Button -->
                <button onclick="toggleMobileMenu()"
                        class="md:hidden text-gray-800 dark:text-gray-200 hover:text-primary-600 dark:hover:text-primary-400">
                    <i class="fas fa-bars text-xl"></i>
                </button>
            </div>

            <!-- Mobile Navigation -->
            <div id="mobile-menu" class="hidden md:hidden py-4 border-t border-gray-200 dark:border-gray-700">
                <div class="flex flex-col space-y-4">
                    <a href="{{ route('guest.home') }}"
                       class="text-gray-800 dark:text-gray-200 hover:text-primary-600 dark:hover:text-primary-400 transition-colors {{ request()->routeIs('guest.home') ? 'text-primary-600 dark:text-primary-400 font-semibold' : '' }}">
                        {{ __('Home') }}
                    </a>
                    <a href="{{ route('guest.menu') }}"
                       class="text-gray-800 dark:text-gray-200 hover:text-primary-600 dark:hover:text-primary-400 transition-colors {{ request()->routeIs('guest.menu*') ? 'text-primary-600 dark:text-primary-400 font-semibold' : '' }}">
                        {{ __('Menu') }}
                    </a>
                    
                    <!-- Mobile Cart Button -->
                    <button onclick="toggleCart()" 
                            class="relative bg-primary-600 text-white px-6 py-2 rounded-lg hover:bg-primary-700 transition-colors text-center">
                        <i class="fas fa-shopping-cart mr-2"></i>
                        {{ __('Cart') }}
                        <span id="mobile-cart-count" class="cart-count hidden ml-2 bg-red-500 text-white text-xs rounded-full px-2 py-1">
                            0
                        </span>
                    </button>

                    @if(isset($siteSettings['contact']['phone']))
                    <a href="tel:{{ $siteSettings['contact']['phone'] }}" 
                       class="bg-primary-600 text-white px-6 py-2 rounded-lg hover:bg-primary-700 transition-colors text-center">
                        {{ __('Call Now') }}
                    </a>
                    @endif
                </div>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <main>
        @yield('content')
    </main>

    @include('tenant.partials.footer')
    @include('tenant.partials.cart-sidebar')
    @include('tenant.partials.scripts')
    
    @stack('scripts')
</body>
</html>
