@extends('layouts.tenant')

@section('title', $seoData['title'])
@section('description', $seoData['description'])
@section('canonical', $seoData['url'])

@push('head')
<!-- Google Maps API -->
@if(config('services.google_maps.api_key'))
<script>
    // Global callback function for Google Maps
    function initGoogleMaps() {
        if (typeof initMap === 'function') {
            initMap();
        }
    }

    // Error handler for Google Maps
    function gm_authFailure() {
        console.error('Google Maps authentication failed');
        showMapError('Authentication failed. Please check the API key.');
    }

    // Load Google Maps script
    function loadGoogleMaps() {
        const script = document.createElement('script');
        script.src = 'https://maps.googleapis.com/maps/api/js?key={{ config('services.google_maps.api_key') }}&libraries=places&callback=initGoogleMaps';
        script.async = true;
        script.defer = true;
        script.onerror = function() {
            console.error('Failed to load Google Maps script');
            showMapError('Failed to load Google Maps. Please check your internet connection.');
        };
        document.head.appendChild(script);
    }
</script>
@else
<script>
    console.warn('Google Maps API key not configured');
    function showMapError(message) {
        const mapContainer = document.getElementById('map-container');
        if (mapContainer) {
            mapContainer.innerHTML = `
                <div class="map-loading">
                    <div class="text-center">
                        <i class="fas fa-exclamation-triangle text-2xl text-red-400 mb-2"></i>
                        <p class="text-red-600">${message}</p>
                    </div>
                </div>
            `;
        }
    }
</script>
@endif
<style>
    #map {
        height: 300px;
        width: 100%;
        border-radius: 0.5rem;
        border: 2px solid #e5e7eb;
    }

    .location-toggle-section {
        background: linear-gradient(135deg, #f3f4f6 0%, #e5e7eb 100%);
        border: 2px dashed #d1d5db;
        transition: all 0.3s ease;
    }

    .location-toggle-section.enabled {
        background: linear-gradient(135deg, #fef3c7 0%, #fde68a 100%);
        border-color: #f59e0b;
    }

    .location-permission-message {
        background: #eff6ff;
        border-left: 4px solid #3b82f6;
    }

    .location-error-message {
        background: #fef2f2;
        border-left: 4px solid #ef4444;
    }

    .location-success-message {
        background: #f0fdf4;
        border-left: 4px solid #22c55e;
    }

    .map-loading {
        display: flex;
        align-items: center;
        justify-content: center;
        height: 300px;
        background: #f9fafb;
        border-radius: 0.5rem;
        border: 2px dashed #d1d5db;
    }
</style>
@endpush

@section('content')
    <!-- Header -->
    <section class="bg-gray-50 dark:bg-gray-800 py-8">
        <div class="container mx-auto px-4">
            <h1 class="text-3xl font-bold text-center mb-4 text-gray-900 dark:text-white">{{ __('Checkout') }}</h1>
            <p class="text-lg text-gray-600 dark:text-gray-400 text-center">
                {{ __('Complete your order') }}
            </p>
        </div>
    </section>

    <!-- Checkout Content -->
    <section class="py-12">
        <div class="container mx-auto px-4">
            <div class="max-w-4xl mx-auto">
                <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
                    <!-- Order Summary -->
                    <div class="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6">
                        <h2 class="text-xl font-semibold text-gray-900 dark:text-white mb-4">{{ __('Order Summary') }}</h2>
                        
                        <!-- Cart Items -->
                        <div id="checkout-cart-items" class="space-y-4 mb-6">
                            <!-- Items will be populated by JavaScript -->
                        </div>

                        <!-- Order Totals -->
                        <div class="border-t border-gray-200 dark:border-gray-700 pt-4">
                            <div class="space-y-2">
                                <div class="flex justify-between text-sm">
                                    <span class="text-gray-600 dark:text-gray-400">{{ __('Subtotal') }}</span>
                                    <span id="checkout-subtotal" class="font-medium text-gray-900 dark:text-white">৳0.00</span>
                                </div>
                                <div class="flex justify-between text-sm">
                                    <span class="text-gray-600 dark:text-gray-400">{{ __('Tax') }}</span>
                                    <span id="checkout-tax" class="font-medium text-gray-900 dark:text-white">৳0.00</span>
                                </div>
                                <div class="flex justify-between text-lg font-semibold border-t border-gray-200 dark:border-gray-700 pt-2">
                                    <span class="text-gray-900 dark:text-white">{{ __('Total') }}</span>
                                    <span id="checkout-total" class="text-primary-600 dark:text-primary-400">৳0.00</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Customer Information -->
                    <div class="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6">
                        <h2 class="text-xl font-semibold text-gray-900 dark:text-white mb-4">{{ __('Customer Information') }}</h2>
                        
                        <form id="checkout-form" class="space-y-4">
                            <div>
                                <label for="customer_name" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                                    {{ __('Full Name') }} <span class="text-red-500">*</span>
                                </label>
                                <input type="text" 
                                       id="customer_name" 
                                       name="customer_name" 
                                       required
                                       class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:text-white">
                            </div>

                            <div>
                                <label for="customer_phone" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                                    {{ __('Phone Number') }} <span class="text-red-500">*</span>
                                </label>
                                <input type="tel" 
                                       id="customer_phone" 
                                       name="customer_phone" 
                                       required
                                       class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:text-white">
                            </div>

                            <div>
                                <label for="customer_email" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                                    {{ __('Email Address') }}
                                </label>
                                <input type="email" 
                                       id="customer_email" 
                                       name="customer_email"
                                       class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:text-white">
                            </div>

                            <!-- Location Toggle Section -->
                            <div class="location-toggle-section p-4 rounded-lg mb-4" id="location-toggle-section">
                                <div class="flex items-center justify-between mb-3">
                                    <div class="flex items-center space-x-3">
                                        <i class="fas fa-map-marker-alt text-primary-600 text-lg"></i>
                                        <div>
                                            <h3 class="font-medium text-gray-900 dark:text-white">{{ __('Enable Delivery Location') }}</h3>
                                            <p class="text-sm text-gray-600 dark:text-gray-400">{{ __('Help us find you for faster delivery') }}</p>
                                        </div>
                                    </div>
                                    <label class="relative inline-flex items-center cursor-pointer">
                                        <input type="checkbox" id="location_enabled" name="location_enabled" class="sr-only peer" onchange="toggleLocationFeatures()">
                                        <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-primary-300 dark:peer-focus:ring-primary-800 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-primary-600"></div>
                                    </label>
                                </div>

                                <!-- Location Permission Message -->
                                <div id="location-permission-message" class="hidden location-permission-message p-3 rounded-lg mb-3">
                                    <div class="flex items-start space-x-2">
                                        <i class="fas fa-info-circle text-blue-500 mt-0.5"></i>
                                        <div class="text-sm">
                                            <p class="font-medium text-blue-800">{{ __('Location Access Requested') }}</p>
                                            <p class="text-blue-700">{{ __('We need your location to provide accurate delivery service. Please allow location access when prompted.') }}</p>
                                        </div>
                                    </div>
                                </div>

                                <!-- Location Error Message -->
                                <div id="location-error-message" class="hidden location-error-message p-3 rounded-lg mb-3">
                                    <div class="flex items-start space-x-2">
                                        <i class="fas fa-exclamation-triangle text-red-500 mt-0.5"></i>
                                        <div class="text-sm">
                                            <p class="font-medium text-red-800">{{ __('Location Access Denied') }}</p>
                                            <p class="text-red-700">{{ __('You can still place an order by entering your address manually below.') }}</p>
                                        </div>
                                    </div>
                                </div>

                                <!-- Location Success Message -->
                                <div id="location-success-message" class="hidden location-success-message p-3 rounded-lg mb-3">
                                    <div class="flex items-start space-x-2">
                                        <i class="fas fa-check-circle text-green-500 mt-0.5"></i>
                                        <div class="text-sm">
                                            <p class="font-medium text-green-800">{{ __('Location Found') }}</p>
                                            <p class="text-green-700">{{ __('You can adjust the delivery location on the map below.') }}</p>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Map Section -->
                            <div id="map-section" class="hidden mb-4">
                                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                                    {{ __('Select Delivery Location') }}
                                </label>

                                <!-- Address Search Box -->
                                <div class="mb-3">
                                    <input type="text"
                                           id="address-search"
                                           placeholder="{{ __('Search for an address...') }}"
                                           class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:text-white">
                                </div>

                                <!-- Map Container -->
                                <div id="map-container">
                                    <div id="map-loading" class="map-loading">
                                        <div class="text-center">
                                            <i class="fas fa-spinner fa-spin text-2xl text-gray-400 mb-2"></i>
                                            <p class="text-gray-600">{{ __('Loading map...') }}</p>
                                        </div>
                                    </div>
                                    <div id="map" style="display: none;"></div>
                                </div>

                                <!-- Selected Address Display -->
                                <div id="selected-address" class="hidden mt-3 p-3 bg-green-50 dark:bg-green-900 border border-green-200 dark:border-green-700 rounded-lg">
                                    <div class="flex items-start space-x-2">
                                        <i class="fas fa-map-marker-alt text-green-600 mt-1"></i>
                                        <div>
                                            <p class="font-medium text-green-800 dark:text-green-200">{{ __('Selected Location') }}</p>
                                            <p id="selected-address-text" class="text-sm text-green-700 dark:text-green-300"></p>
                                        </div>
                                    </div>
                                </div>

                                <!-- Fallback Location Input (when maps fail) -->
                                <div id="location-fallback" class="hidden mt-3 p-3 bg-blue-50 dark:bg-blue-900 border border-blue-200 dark:border-blue-700 rounded-lg">
                                    <div class="flex items-start space-x-2">
                                        <i class="fas fa-info-circle text-blue-600 mt-1"></i>
                                        <div class="flex-1">
                                            <p class="font-medium text-blue-800 dark:text-blue-200">{{ __('Manual Location Entry') }}</p>
                                            <p class="text-sm text-blue-700 dark:text-blue-300 mb-3">{{ __('Enter your coordinates manually if you know them:') }}</p>
                                            <div class="grid grid-cols-2 gap-3">
                                                <div>
                                                    <label class="block text-xs font-medium text-blue-800 dark:text-blue-200 mb-1">{{ __('Latitude') }}</label>
                                                    <input type="number"
                                                           id="manual_latitude"
                                                           step="any"
                                                           placeholder="23.8103"
                                                           class="w-full px-2 py-1 text-sm border border-blue-300 dark:border-blue-600 rounded focus:outline-none focus:ring-1 focus:ring-blue-500 dark:bg-blue-800 dark:text-white">
                                                </div>
                                                <div>
                                                    <label class="block text-xs font-medium text-blue-800 dark:text-blue-200 mb-1">{{ __('Longitude') }}</label>
                                                    <input type="number"
                                                           id="manual_longitude"
                                                           step="any"
                                                           placeholder="90.4125"
                                                           class="w-full px-2 py-1 text-sm border border-blue-300 dark:border-blue-600 rounded focus:outline-none focus:ring-1 focus:ring-blue-500 dark:bg-blue-800 dark:text-white">
                                                </div>
                                            </div>
                                            <button type="button"
                                                    onclick="setManualLocation()"
                                                    class="mt-2 px-3 py-1 bg-blue-600 text-white text-sm rounded hover:bg-blue-700 transition-colors">
                                                {{ __('Set Location') }}
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div>
                                <label for="delivery_address" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                                    {{ __('Delivery Address') }} <span class="text-red-500">*</span>
                                </label>
                                <textarea id="delivery_address"
                                          name="delivery_address"
                                          rows="3"
                                          required
                                          placeholder="{{ __('Enter your complete delivery address...') }}"
                                          class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:text-white"></textarea>
                                <p class="text-xs text-gray-500 dark:text-gray-400 mt-1">
                                    {{ __('Include apartment/floor number, landmarks, and any special delivery instructions') }}
                                </p>
                            </div>

                            <div>
                                <label for="special_instructions" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                                    {{ __('Special Instructions') }}
                                </label>
                                <textarea id="special_instructions" 
                                          name="special_instructions" 
                                          rows="2"
                                          placeholder="{{ __('Any special requests or notes...') }}"
                                          class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:text-white"></textarea>
                            </div>

                            <!-- Payment Method -->
                            <div>
                                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                                    {{ __('Payment Method') }} <span class="text-red-500">*</span>
                                </label>
                                <div class="space-y-2">
                                    <label class="flex items-center">
                                        <input type="radio" name="payment_method" value="cash_on_delivery" checked
                                               class="text-primary-600 focus:ring-primary-500 border-gray-300 dark:border-gray-600">
                                        <span class="ml-2 text-sm text-gray-700 dark:text-gray-300">{{ __('Cash on Delivery') }}</span>
                                    </label>
                                    <label class="flex items-center">
                                        <input type="radio" name="payment_method" value="bkash"
                                               class="text-primary-600 focus:ring-primary-500 border-gray-300 dark:border-gray-600">
                                        <span class="ml-2 text-sm text-gray-700 dark:text-gray-300">{{ __('bKash') }}</span>
                                    </label>
                                    <label class="flex items-center">
                                        <input type="radio" name="payment_method" value="nagad"
                                               class="text-primary-600 focus:ring-primary-500 border-gray-300 dark:border-gray-600">
                                        <span class="ml-2 text-sm text-gray-700 dark:text-gray-300">{{ __('Nagad') }}</span>
                                    </label>
                                </div>
                            </div>

                            <!-- Hidden Location Fields -->
                            <input type="hidden" id="delivery_latitude" name="delivery_latitude">
                            <input type="hidden" id="delivery_longitude" name="delivery_longitude">
                            <input type="hidden" id="delivery_formatted_address" name="delivery_formatted_address">

                            <!-- Place Order Button -->
                            <button type="submit"
                                    id="place-order-btn"
                                    class="w-full bg-primary-600 text-white py-3 px-4 rounded-lg hover:bg-primary-700 transition-colors font-medium text-lg">
                                {{ __('Place Order') }}
                            </button>
                        </form>
                    </div>
                </div>

                <!-- Empty Cart Message -->
                <div id="empty-checkout-cart" class="hidden text-center py-12">
                    <div class="text-gray-400 dark:text-gray-600 text-6xl mb-4">
                        <i class="fas fa-shopping-cart"></i>
                    </div>
                    <h3 class="text-2xl font-semibold text-gray-600 dark:text-gray-400 mb-2">{{ __('Your cart is empty') }}</h3>
                    <p class="text-gray-500 dark:text-gray-500 mb-6">{{ __('Add some items to your cart before checkout') }}</p>
                    <a href="{{ route('guest.menu') }}" 
                       class="inline-block bg-primary-600 text-white px-8 py-3 rounded-lg hover:bg-primary-700 transition-colors">
                        {{ __('Browse Menu') }}
                    </a>
                </div>
            </div>
        </div>
    </section>
@endsection

@push('scripts')
<script>
    // Global variables for Google Maps
    let map;
    let marker;
    let geocoder;
    let autocomplete;
    let userLocation = null;
    let selectedLocation = null;
    let isLocationEnabled = false;

    // Restaurant default location (you can set this from backend)
    const restaurantLocation = {
        lat: 23.8103, // Dhaka, Bangladesh
        lng: 90.4125
    };

    // Initialize checkout page
    document.addEventListener('DOMContentLoaded', function() {
        updateCheckoutDisplay();

        // Handle form submission
        document.getElementById('checkout-form').addEventListener('submit', function(e) {
            e.preventDefault();
            placeOrder();
        });

        // Load Google Maps if API key is available
        @if(config('services.google_maps.api_key'))
            loadGoogleMaps();
        @else
            // Show error message if no API key
            setTimeout(() => {
                showMapError('Google Maps API key not configured. Location features are disabled.');
            }, 100);
        @endif
    });

    // Show map error message
    function showMapError(message) {
        const mapContainer = document.getElementById('map-container');
        const fallbackContainer = document.getElementById('location-fallback');

        if (mapContainer) {
            mapContainer.innerHTML = `
                <div class="map-loading">
                    <div class="text-center">
                        <i class="fas fa-exclamation-triangle text-2xl text-red-400 mb-2"></i>
                        <p class="text-red-600">${message}</p>
                        <p class="text-sm text-gray-500 mt-2">{{ __('You can still place an order by entering your address manually.') }}</p>
                        <button type="button" onclick="showLocationFallback()" class="mt-2 px-3 py-1 bg-blue-600 text-white text-sm rounded hover:bg-blue-700 transition-colors">
                            {{ __('Enter Coordinates Manually') }}
                        </button>
                    </div>
                </div>
            `;
        }
    }

    // Show location fallback form
    function showLocationFallback() {
        document.getElementById('location-fallback').classList.remove('hidden');
    }

    // Google Maps initialization callback
    function initMap() {
        try {
            // Check if Google Maps is available
            if (typeof google === 'undefined' || !google.maps) {
                throw new Error('Google Maps not loaded');
            }

            geocoder = new google.maps.Geocoder();

            // Initialize map (hidden initially)
            map = new google.maps.Map(document.getElementById('map'), {
                zoom: 15,
                center: restaurantLocation,
                mapTypeControl: false,
                streetViewControl: false,
                fullscreenControl: false
            });

            // Create marker
            marker = new google.maps.Marker({
                position: restaurantLocation,
                map: map,
                draggable: true,
                title: '{{ __("Delivery Location") }}'
            });

            // Add click listener to map
            map.addListener('click', function(event) {
                updateMarkerPosition(event.latLng);
            });

            // Add drag listener to marker
            marker.addListener('dragend', function(event) {
                updateMarkerPosition(event.latLng);
            });

            // Initialize autocomplete
            const searchInput = document.getElementById('address-search');
            if (google.maps.places) {
                autocomplete = new google.maps.places.Autocomplete(searchInput, {
                    componentRestrictions: { country: 'bd' }, // Restrict to Bangladesh
                    fields: ['place_id', 'geometry', 'name', 'formatted_address']
                });

                autocomplete.addListener('place_changed', function() {
                    const place = autocomplete.getPlace();
                    if (place.geometry) {
                        updateMarkerPosition(place.geometry.location);
                        map.setCenter(place.geometry.location);
                    }
                });
            } else {
                // Disable autocomplete if Places API not available
                searchInput.placeholder = '{{ __("Address search not available") }}';
                searchInput.disabled = true;
            }

            // Hide loading indicator and show map
            const mapLoading = document.getElementById('map-loading');
            const mapElement = document.getElementById('map');
            if (mapLoading) mapLoading.style.display = 'none';
            if (mapElement) mapElement.style.display = 'block';

        } catch (error) {
            console.error('Error initializing Google Maps:', error);
            showMapError('{{ __("Failed to initialize map. Please try refreshing the page.") }}');
        }
    }

    // Toggle location features
    function toggleLocationFeatures() {
        const checkbox = document.getElementById('location_enabled');
        const toggleSection = document.getElementById('location-toggle-section');
        const mapSection = document.getElementById('map-section');

        isLocationEnabled = checkbox.checked;

        if (isLocationEnabled) {
            // Check if Google Maps is available
            if (typeof google === 'undefined' || !google.maps) {
                // Show error and disable the toggle
                checkbox.checked = false;
                isLocationEnabled = false;
                showLocationMessage('error');
                return;
            }

            toggleSection.classList.add('enabled');
            mapSection.classList.remove('hidden');
            requestUserLocation();
        } else {
            toggleSection.classList.remove('enabled');
            mapSection.classList.add('hidden');
            hideAllLocationMessages();
            clearLocationData();
        }
    }

    // Request user's current location
    function requestUserLocation() {
        showLocationMessage('permission');

        if (navigator.geolocation) {
            navigator.geolocation.getCurrentPosition(
                function(position) {
                    userLocation = {
                        lat: position.coords.latitude,
                        lng: position.coords.longitude
                    };

                    // Center map on user location
                    map.setCenter(userLocation);
                    updateMarkerPosition(new google.maps.LatLng(userLocation.lat, userLocation.lng));

                    showLocationMessage('success');
                },
                function(error) {
                    console.log('Geolocation error:', error);
                    // Center map on restaurant location as fallback
                    map.setCenter(restaurantLocation);
                    updateMarkerPosition(new google.maps.LatLng(restaurantLocation.lat, restaurantLocation.lng));

                    showLocationMessage('error');
                },
                {
                    enableHighAccuracy: true,
                    timeout: 10000,
                    maximumAge: 300000 // 5 minutes
                }
            );
        } else {
            // Geolocation not supported
            map.setCenter(restaurantLocation);
            updateMarkerPosition(new google.maps.LatLng(restaurantLocation.lat, restaurantLocation.lng));
            showLocationMessage('error');
        }
    }

    // Update marker position and get address
    function updateMarkerPosition(latLng) {
        try {
            if (!marker || !geocoder) {
                console.error('Google Maps components not initialized');
                return;
            }

            marker.setPosition(latLng);
            selectedLocation = {
                lat: latLng.lat(),
                lng: latLng.lng()
            };

            // Reverse geocode to get formatted address
            geocoder.geocode({ location: latLng }, function(results, status) {
                if (status === 'OK' && results[0]) {
                    const formattedAddress = results[0].formatted_address;

                    // Update UI
                    document.getElementById('selected-address').classList.remove('hidden');
                    document.getElementById('selected-address-text').textContent = formattedAddress;

                    // Update hidden form fields
                    document.getElementById('delivery_latitude').value = selectedLocation.lat;
                    document.getElementById('delivery_longitude').value = selectedLocation.lng;
                    document.getElementById('delivery_formatted_address').value = formattedAddress;

                    // Auto-fill delivery address if empty
                    const deliveryAddressField = document.getElementById('delivery_address');
                    if (!deliveryAddressField.value.trim()) {
                        deliveryAddressField.value = formattedAddress;
                    }
                } else {
                    console.error('Geocoding failed:', status);
                    // Still update coordinates even if geocoding fails
                    document.getElementById('delivery_latitude').value = selectedLocation.lat;
                    document.getElementById('delivery_longitude').value = selectedLocation.lng;
                }
            });
        } catch (error) {
            console.error('Error updating marker position:', error);
        }
    }

    // Show location messages
    function showLocationMessage(type) {
        hideAllLocationMessages();

        const messageId = `location-${type}-message`;
        const messageElement = document.getElementById(messageId);
        if (messageElement) {
            messageElement.classList.remove('hidden');
        }
    }

    // Hide all location messages
    function hideAllLocationMessages() {
        ['permission', 'error', 'success'].forEach(type => {
            const messageElement = document.getElementById(`location-${type}-message`);
            if (messageElement) {
                messageElement.classList.add('hidden');
            }
        });
    }

    // Clear location data
    function clearLocationData() {
        selectedLocation = null;
        document.getElementById('delivery_latitude').value = '';
        document.getElementById('delivery_longitude').value = '';
        document.getElementById('delivery_formatted_address').value = '';
        document.getElementById('selected-address').classList.add('hidden');
        document.getElementById('location-fallback').classList.add('hidden');
    }

    // Set manual location coordinates
    function setManualLocation() {
        const lat = parseFloat(document.getElementById('manual_latitude').value);
        const lng = parseFloat(document.getElementById('manual_longitude').value);

        if (isNaN(lat) || isNaN(lng)) {
            alert('{{ __("Please enter valid latitude and longitude values") }}');
            return;
        }

        if (lat < -90 || lat > 90) {
            alert('{{ __("Latitude must be between -90 and 90") }}');
            return;
        }

        if (lng < -180 || lng > 180) {
            alert('{{ __("Longitude must be between -180 and 180") }}');
            return;
        }

        // Set the location data
        selectedLocation = { lat: lat, lng: lng };
        document.getElementById('delivery_latitude').value = lat;
        document.getElementById('delivery_longitude').value = lng;
        document.getElementById('delivery_formatted_address').value = `${lat}, ${lng}`;

        // Show confirmation
        document.getElementById('selected-address').classList.remove('hidden');
        document.getElementById('selected-address-text').textContent = `{{ __('Manual coordinates:') }} ${lat}, ${lng}`;

        // Hide fallback form
        document.getElementById('location-fallback').classList.add('hidden');

        alert('{{ __("Location set successfully!") }}');
    }

    function updateCheckoutDisplay() {
        const cart = JSON.parse(localStorage.getItem('restaurant_cart') || '[]');
        const checkoutItems = document.getElementById('checkout-cart-items');
        const emptyCart = document.getElementById('empty-checkout-cart');
        const checkoutForm = document.querySelector('.max-w-4xl');

        if (cart.length === 0) {
            emptyCart.classList.remove('hidden');
            checkoutForm.classList.add('hidden');
            return;
        }

        emptyCart.classList.add('hidden');
        checkoutForm.classList.remove('hidden');

        // Render cart items
        checkoutItems.innerHTML = '';
        cart.forEach(item => {
            const itemElement = document.createElement('div');
            itemElement.className = 'flex items-center space-x-3 p-3 bg-gray-50 dark:bg-gray-700 rounded-lg';
            itemElement.innerHTML = `
                <img src="${item.image || '/images/placeholder-food.jpg'}"
                     alt="${item.name}"
                     class="w-12 h-12 object-cover rounded-lg">
                <div class="flex-1">
                    <h4 class="font-medium text-gray-900 dark:text-white">${item.name}</h4>
                    <p class="text-sm text-gray-600 dark:text-gray-400">৳${(item.basePrice || item.price)} × ${item.quantity}</p>
                    ${item.variations && Object.keys(item.variations).length > 0 ?
                        `<p class="text-xs text-gray-500">{{ __('With variations') }}</p>` : ''}
                    ${item.addons && item.addons.length > 0 ?
                        `<p class="text-xs text-gray-500">{{ __('With add-ons') }}</p>` : ''}
                </div>
                <span class="font-medium text-gray-900 dark:text-white">৳${(item.totalPrice || item.price * item.quantity).toFixed(2)}</span>
            `;
            checkoutItems.appendChild(itemElement);
        });

        // Update totals
        const subtotal = cart.reduce((total, item) => total + (item.totalPrice || item.price * item.quantity), 0);
        const tax = subtotal * ({{ $restaurant->tax_rate ?? 10 }} / 100);
        const total = subtotal + tax;

        document.getElementById('checkout-subtotal').textContent = `৳${subtotal.toFixed(2)}`;
        document.getElementById('checkout-tax').textContent = `৳${tax.toFixed(2)}`;
        document.getElementById('checkout-total').textContent = `৳${total.toFixed(2)}`;
    }

    function placeOrder() {
        const cart = JSON.parse(localStorage.getItem('restaurant_cart') || '[]');

        if (cart.length === 0) {
            alert('{{ __("Your cart is empty") }}');
            return;
        }

        const formData = new FormData(document.getElementById('checkout-form'));
        const orderData = {
            customer_name: formData.get('customer_name'),
            customer_phone: formData.get('customer_phone'),
            customer_email: formData.get('customer_email'),
            delivery_address: formData.get('delivery_address'),
            special_instructions: formData.get('special_instructions'),
            payment_method: formData.get('payment_method'),
            location_enabled: formData.get('location_enabled') === 'on',
            delivery_latitude: formData.get('delivery_latitude'),
            delivery_longitude: formData.get('delivery_longitude'),
            delivery_formatted_address: formData.get('delivery_formatted_address'),
            items: cart
        };

        // Disable submit button
        const submitBtn = document.getElementById('place-order-btn');
        submitBtn.disabled = true;
        submitBtn.textContent = '{{ __("Placing Order...") }}';

        // Submit order via AJAX
        fetch('{{ route("guest.orders.store") }}', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
            },
            body: JSON.stringify(orderData)
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // Clear cart
                localStorage.removeItem('restaurant_cart');
                if (typeof updateCartUI === 'function') {
                    updateCartUI();
                }

                // Show success message
                alert('{{ __("Order placed successfully! We will contact you soon.") }}');

                // Redirect to home
                window.location.href = '{{ route("guest.home") }}';
            } else {
                throw new Error(data.message || 'Order placement failed');
            }
        })
        .catch(error => {
            console.error('Order placement error:', error);
            alert('{{ __("Failed to place order. Please try again.") }}');

            // Re-enable submit button
            submitBtn.disabled = false;
            submitBtn.textContent = '{{ __("Place Order") }}';
        });
    }
</script>
@endpush
