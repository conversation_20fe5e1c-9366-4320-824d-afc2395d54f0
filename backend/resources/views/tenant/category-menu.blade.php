@extends('layouts.tenant')

@section('title', ($category->name ?? 'Category') . ' - ' . ($siteSettings['general']['site_name'] ?? $restaurant->name ?? 'Restaurant'))
@section('description', $category->description ?? 'Browse our delicious menu items in this category')
@section('og_title', $category->name ?? 'Category')
@section('og_description', $category->description ?? 'Browse our delicious menu items in this category')
@section('og_type', 'website')
@section('canonical', request()->url())

@push('head')
    <!-- JSON-LD Structured Data for Category -->
    <script type="application/ld+json">
    {
        "@context": "https://schema.org",
        "@type": "MenuSection",
        "name": "{{ $category->name ?? 'Category' }}",
        "description": "{{ $category->description ?? '' }}",
        "hasMenuItem": [
            @foreach($menuItems as $index => $item)
            {
                "@type": "MenuItem",
                "name": "{{ $item->name }}",
                "description": "{{ $item->description ?? '' }}",
                "image": "{{ $item->primary_image_url }}",
                "offers": {
                    "@type": "Offer",
                    "price": "{{ $item->price }}",
                    "priceCurrency": "BDT",
                    "availability": "{{ $item->is_available ? 'InStock' : 'OutOfStock' }}"
                }
            }@if(!$loop->last),@endif
            @endforeach
        ]
    }
    </script>
    
    <!-- Custom Styles -->
    <style>
        .line-clamp-2 {
            display: -webkit-box;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
            overflow: hidden;
        }
    </style>
@endpush

@section('content')
    <!-- Breadcrumb -->
    <section class="bg-gray-50 dark:bg-gray-800 py-4">
        <div class="container mx-auto px-4">
            <nav class="flex items-center space-x-2 text-sm">
                <a href="{{ route('guest.home') }}" class="text-gray-600 dark:text-gray-400 hover:text-primary-600 dark:hover:text-primary-400">
                    {{ __('Home') }}
                </a>
                <span class="text-gray-400">/</span>
                <a href="{{ route('guest.menu') }}" class="text-gray-600 dark:text-gray-400 hover:text-primary-600 dark:hover:text-primary-400">
                    {{ __('Menu') }}
                </a>
                <span class="text-gray-400">/</span>
                <span class="text-gray-800 dark:text-gray-200 font-semibold">{{ $category->name ?? __('Category') }}</span>
            </nav>
        </div>
    </section>

    <!-- Category Header -->
    <section class="bg-gray-50 dark:bg-gray-800 py-12">
        <div class="container mx-auto px-4">
            <div class="text-center">
                <h1 class="text-4xl font-bold text-gray-800 dark:text-white mb-4">{{ $category->name ?? __('Category') }}</h1>
                @if($category && $category->description)
                    <p class="text-xl text-gray-600 dark:text-gray-400 mb-6">
                        {{ $category->description }}
                    </p>
                @endif
                <div class="flex items-center justify-center space-x-4 text-sm text-gray-500 dark:text-gray-400">
                    @if($menuItems->count() > 0)
                        <span>
                            {{ $menuItems->count() }} {{ $menuItems->count() === 1 ? __('item') : __('items') }} {{ __('available') }}
                        </span>
                    @endif
                    @if($menuItems instanceof \Illuminate\Pagination\LengthAwarePaginator && $menuItems->total() > $menuItems->count())
                        <span>
                            {{ $menuItems->total() }} {{ __('total items') }}
                        </span>
                    @endif
                </div>
            </div>
        </div>
    </section>

    <!-- Menu Items -->
    <section class="py-12">
        <div class="container mx-auto px-4">
            @if(!$category)
                <!-- Category Not Found -->
                <div class="text-center py-16">
                    <div class="text-gray-400 text-6xl mb-6">
                        <i class="fas fa-search"></i>
                    </div>
                    <h3 class="text-2xl font-semibold text-gray-600 dark:text-gray-400 mb-2">{{ __('Category Not Found') }}</h3>
                    <p class="text-gray-500 dark:text-gray-500 mb-8">
                        {{ __('The category you\'re looking for doesn\'t exist or has been removed.') }}
                    </p>
                    <a href="{{ route('guest.menu') }}" 
                       class="inline-block bg-primary-600 text-white px-6 py-3 rounded-lg font-semibold hover:bg-primary-700 transition-colors">
                        {{ __('Browse All Categories') }}
                    </a>
                </div>
            @elseif($menuItems->count() === 0)
                <!-- No Items Message -->
                <div class="text-center py-16">
                    <div class="text-gray-400 text-6xl mb-6">
                        <i class="fas fa-utensils"></i>
                    </div>
                    <h3 class="text-2xl font-semibold text-gray-600 dark:text-gray-400 mb-2">{{ __('No Items Available') }}</h3>
                    <p class="text-gray-500 dark:text-gray-500 mb-8">
                        {{ __('There are currently no items available in this category.') }}
                    </p>
                    <a href="{{ route('guest.menu') }}" 
                       class="inline-block bg-primary-600 text-white px-6 py-3 rounded-lg font-semibold hover:bg-primary-700 transition-colors">
                        {{ __('Browse All Categories') }}
                    </a>
                </div>
            @else
                <!-- Menu Items Grid -->
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-8">
                    @foreach($menuItems as $item)
                        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md overflow-hidden hover:shadow-lg transition-shadow">
                            <!-- Item Image -->
                            <div class="h-48 bg-gray-200 dark:bg-gray-700 relative">
                                <a href="{{ route('guest.menu.item', $item->slug) }}" class="block w-full h-full">
                                    @if($item->primary_image_url)
                                        <img src="{{ $item->primary_image_url }}" 
                                             alt="{{ $item->name }}" 
                                             class="w-full h-full object-cover hover:scale-105 transition-transform duration-200"
                                             onerror="this.src='data:image/svg+xml;base64,{{ base64_encode('<svg xmlns=\"http://www.w3.org/2000/svg\" width=\"200\" height=\"200\" viewBox=\"0 0 200 200\"><rect width=\"200\" height=\"200\" fill=\"#f3f4f6\"/><circle cx=\"80\" cy=\"70\" r=\"12\" fill=\"#d1d5db\"/><polygon points=\"40,140 80,100 120,120 160,80 180,140\" fill=\"#d1d5db\"/><text x=\"100\" y=\"170\" text-anchor=\"middle\" fill=\"#9ca3af\" font-size=\"16\">Menu Item</text></svg>') }}'">
                                    @else
                                        <div class="flex items-center justify-center h-full text-gray-400 hover:bg-gray-300 dark:hover:bg-gray-600 transition-colors">
                                            <i class="fas fa-utensils text-4xl"></i>
                                        </div>
                                    @endif
                                </a>
                                
                                <!-- Price Badge -->
                                <div class="absolute top-2 right-2">
                                    <span class="bg-primary-600 text-white px-3 py-1 rounded-full text-sm font-semibold">
                                        ৳{{ number_format($item->price, 2) }}
                                    </span>
                                </div>

                                <!-- Availability Badge -->
                                @if(!$item->is_available)
                                    <div class="absolute inset-0 bg-black bg-opacity-50 flex items-center justify-center">
                                        <span class="bg-red-600 text-white px-3 py-1 rounded text-sm font-semibold">
                                            {{ __('Not Available') }}
                                        </span>
                                    </div>
                                @endif
                            </div>

                            <!-- Item Details -->
                            <div class="p-6">
                                <a href="{{ route('guest.menu.item', $item->slug) }}" class="block hover:text-primary-600 transition-colors">
                                    <h3 class="text-xl font-semibold mb-2 text-gray-900 dark:text-white">{{ $item->name }}</h3>
                                </a>
                                @if($item->description)
                                    <p class="text-gray-600 dark:text-gray-400 mb-4 line-clamp-2">{{ $item->description }}</p>
                                @endif
                                
                                <!-- Item Tags -->
                                <div class="flex items-center flex-wrap gap-2 mb-4">
                                    @if($item->is_vegetarian)
                                        <span class="bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200 text-xs px-2 py-1 rounded-full">
                                            <i class="fas fa-leaf mr-1"></i>{{ __('Vegetarian') }}
                                        </span>
                                    @endif
                                    @if($item->is_vegan)
                                        <span class="bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200 text-xs px-2 py-1 rounded-full">
                                            <i class="fas fa-seedling mr-1"></i>{{ __('Vegan') }}
                                        </span>
                                    @endif
                                    @if($item->is_spicy)
                                        <span class="bg-red-100 dark:bg-red-900 text-red-800 dark:text-red-200 text-xs px-2 py-1 rounded-full">
                                            <i class="fas fa-pepper-hot mr-1"></i>{{ __('Spicy') }}
                                        </span>
                                    @endif
                                    @if($item->is_gluten_free)
                                        <span class="bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200 text-xs px-2 py-1 rounded-full">
                                            {{ __('Gluten Free') }}
                                        </span>
                                    @endif
                                </div>

                                <!-- Additional Info -->
                                <div class="flex items-center justify-between text-sm text-gray-500 dark:text-gray-400 mb-4">
                                    @if($item->preparation_time)
                                        <div class="flex items-center">
                                            <i class="fas fa-clock mr-1"></i>
                                            {{ $item->preparation_time }} {{ __('mins') }}
                                        </div>
                                    @endif
                                    @if($item->calories)
                                        <div class="flex items-center">
                                            <i class="fas fa-fire mr-1"></i>
                                            {{ $item->calories }} {{ __('cal') }}
                                        </div>
                                    @endif
                                </div>

                                <!-- Action Button -->
                                <div class="flex items-center justify-between">
                                    <div class="text-lg font-bold text-primary-600">
                                        ৳{{ number_format($item->price, 2) }}
                                    </div>
                                    <a href="{{ route('guest.menu.item', $item->slug) }}" 
                                       class="px-4 py-2 rounded-lg font-semibold transition-colors {{ $item->is_available ? 'bg-primary-600 text-white hover:bg-primary-700' : 'bg-gray-300 dark:bg-gray-600 text-gray-500 dark:text-gray-400 cursor-not-allowed' }}">
                                        {{ $item->is_available ? __('View Details') : __('Not Available') }}
                                    </a>
                                </div>
                            </div>
                        </div>
                    @endforeach
                </div>

                <!-- Pagination -->
                @if($menuItems instanceof \Illuminate\Pagination\LengthAwarePaginator && $menuItems->hasPages())
                    <div class="mt-12">
                        {{ $menuItems->links() }}
                    </div>
                @endif
            @endif
        </div>
    </section>

    <!-- Call to Action -->
    <section class="py-16 bg-primary-600 text-white">
        <div class="container mx-auto px-4 text-center">
            <h2 class="text-3xl font-bold mb-4">{{ __('Explore More Categories') }}</h2>
            <p class="text-xl mb-8 opacity-90">
                {{ __('Discover our full menu with all available categories') }}
            </p>
            <div class="space-x-4">
                <a href="{{ route('guest.menu') }}" 
                   class="inline-block bg-white text-primary-600 px-8 py-3 rounded-lg font-semibold hover:bg-gray-100 transition-colors">
                    <i class="fas fa-utensils mr-2"></i>
                    {{ __('View Full Menu') }}
                </a>
                <a href="{{ route('guest.home') }}" 
                   class="inline-block border-2 border-white text-white px-8 py-3 rounded-lg font-semibold hover:bg-white hover:text-primary-600 transition-colors">
                    {{ __('Back to Home') }}
                </a>
            </div>
        </div>
    </section>
@endsection
