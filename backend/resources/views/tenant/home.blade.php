@extends('layouts.tenant')

@section('title', $seoData['title'])
@section('description', $seoData['description'])
@section('og_title', $seoData['title'])
@section('og_description', $seoData['description'])
@section('og_type', 'website')
@section('canonical', $seoData['url'])

@push('head')
    <!-- JSON-LD Structured Data -->
    <script type="application/ld+json">
        {!! json_encode($jsonLd, JSON_UNESCAPED_SLASHES | JSON_UNESCAPED_UNICODE) !!}
    </script>
@endpush

@section('content')
    <!-- Hero Section -->
    <section class="relative bg-gradient-to-r from-primary-500 to-red-600 text-white py-20">
        <div class="container mx-auto px-4 text-center">
            <h1 class="text-4xl md:text-6xl font-bold mb-4">
                {{ $restaurant->name ?? __('Welcome to Our Restaurant') }}
            </h1>
            <p class="text-xl md:text-2xl mb-8 opacity-90">
                {{ $restaurant->description ?? __('Delicious food, great atmosphere, unforgettable experience') }}
            </p>
            <div class="space-x-4">
                <a href="{{ route('guest.menu') }}" 
                   class="inline-block bg-white text-primary-600 px-8 py-3 rounded-lg font-semibold hover:bg-gray-100 transition-colors">
                    {{ __('View Menu') }}
                </a>
                @if(isset($siteSettings['contact']['phone']))
                <a href="tel:{{ $siteSettings['contact']['phone'] }}" 
                   class="inline-block border-2 border-white text-white px-8 py-3 rounded-lg font-semibold hover:bg-white hover:text-primary-600 transition-colors">
                    {{ __('Call Now') }}
                </a>
                @endif
            </div>
        </div>
    </section>

    <!-- Featured Items Section -->
    @if($featuredItems->count() > 0)
    <section class="py-16 bg-white dark:bg-gray-900">
        <div class="container mx-auto px-4">
            <div class="text-center mb-12">
                <h2 class="text-3xl md:text-4xl font-bold text-gray-900 dark:text-white mb-4">
                    {{ __('Featured Dishes') }}
                </h2>
                <p class="text-xl text-gray-600 dark:text-gray-400">
                    {{ __('Try our most popular and delicious items') }}
                </p>
            </div>

            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                @foreach($featuredItems as $item)
                <div class="bg-white dark:bg-gray-800 rounded-lg shadow-lg overflow-hidden hover:shadow-xl transition-shadow">
                    <div class="relative h-48 bg-gray-200 dark:bg-gray-700">
                        @if($item->primary_image_url)
                            <img src="{{ $item->primary_image_url }}" 
                                 alt="{{ $item->name }}" 
                                 class="w-full h-full object-cover">
                        @else
                            <div class="w-full h-full flex items-center justify-center">
                                <i class="fas fa-utensils text-4xl text-gray-400"></i>
                            </div>
                        @endif
                        <div class="absolute top-4 right-4">
                            <span class="bg-primary-600 text-white px-3 py-1 rounded-full text-sm font-semibold">
                                ৳{{ number_format($item->price, 2) }}
                            </span>
                        </div>
                    </div>
                    <div class="p-6">
                        <h3 class="text-xl font-semibold text-gray-900 dark:text-white mb-2">
                            {{ $item->name }}
                        </h3>
                        @if($item->description)
                        <p class="text-gray-600 dark:text-gray-400 mb-4">
                            {{ Str::limit($item->description, 100) }}
                        </p>
                        @endif
                        <button onclick="addMenuItemToCart({{ $item->id }}, '{{ addslashes($item->name) }}', {{ $item->price }}, '{{ $item->primary_image_url }}')" 
                                class="w-full bg-primary-600 text-white py-2 px-4 rounded-lg hover:bg-primary-700 transition-colors">
                            <i class="fas fa-plus mr-2"></i>
                            {{ __('Add to Cart') }}
                        </button>
                    </div>
                </div>
                @endforeach
            </div>

            <div class="text-center mt-12">
                <a href="{{ route('guest.menu') }}" 
                   class="inline-block bg-primary-600 text-white px-8 py-3 rounded-lg font-semibold hover:bg-primary-700 transition-colors">
                    {{ __('View Full Menu') }}
                </a>
            </div>
        </div>
    </section>
    @endif

    <!-- Categories Section -->
    @if($categories->count() > 0)
    <section class="py-16 bg-gray-50 dark:bg-gray-800">
        <div class="container mx-auto px-4">
            <div class="text-center mb-12">
                <h2 class="text-3xl md:text-4xl font-bold text-gray-900 dark:text-white mb-4">
                    {{ __('Menu Categories') }}
                </h2>
                <p class="text-xl text-gray-600 dark:text-gray-400">
                    {{ __('Explore our diverse range of cuisines') }}
                </p>
            </div>

            <div class="grid grid-cols-2 md:grid-cols-4 gap-6">
                @foreach($categories as $category)
                <a href="{{ route('guest.menu') }}#category-{{ $category->id }}"
                   class="group bg-white dark:bg-gray-700 rounded-lg p-6 text-center hover:shadow-lg transition-all hover:scale-105">
                    <div class="w-16 h-16 bg-primary-100 dark:bg-primary-900 rounded-full flex items-center justify-center mx-auto mb-4 group-hover:bg-primary-200 dark:group-hover:bg-primary-800 transition-colors overflow-hidden">
                        @if($category->media && $category->media->count() > 0)
                            <img src="{{ $category->media->first()->url }}"
                                 alt="{{ $category->name }}"
                                 class="w-full h-full object-cover rounded-full">
                        @else
                            <i class="fas fa-utensils text-2xl text-primary-600 dark:text-primary-400"></i>
                        @endif
                    </div>
                    <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-2">
                        {{ $category->name }}
                    </h3>
                    @if($category->description)
                    <p class="text-sm text-gray-600 dark:text-gray-400">
                        {{ Str::limit($category->description, 50) }}
                    </p>
                    @endif
                </a>
                @endforeach
            </div>
        </div>
    </section>
    @endif

    <!-- About Section -->
    <section class="py-16 bg-white dark:bg-gray-900">
        <div class="container mx-auto px-4">
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
                <div>
                    <h2 class="text-3xl md:text-4xl font-bold text-gray-900 dark:text-white mb-6">
                        {{ __('About Our Restaurant') }}
                    </h2>
                    <p class="text-lg text-gray-600 dark:text-gray-400 mb-6">
                        {{ $restaurant->description ?? __('We are passionate about serving delicious, authentic cuisine in a warm and welcoming atmosphere. Our chefs use only the finest ingredients to create memorable dining experiences for our guests.') }}
                    </p>
                    <div class="space-y-4">
                        @if(isset($siteSettings['contact']['phone']))
                        <div class="flex items-center space-x-3">
                            <i class="fas fa-phone text-primary-600 dark:text-primary-400"></i>
                            <span class="text-gray-700 dark:text-gray-300">{{ $siteSettings['contact']['phone'] }}</span>
                        </div>
                        @endif
                        @if(isset($siteSettings['contact']['address']))
                        <div class="flex items-center space-x-3">
                            <i class="fas fa-map-marker-alt text-primary-600 dark:text-primary-400"></i>
                            <span class="text-gray-700 dark:text-gray-300">{{ $siteSettings['contact']['address'] }}</span>
                        </div>
                        @endif
                        @if(isset($restaurant->opening_time) && isset($restaurant->closing_time))
                        <div class="flex items-center space-x-3">
                            <i class="fas fa-clock text-primary-600 dark:text-primary-400"></i>
                            <span class="text-gray-700 dark:text-gray-300">
                                {{ $restaurant->opening_time }} - {{ $restaurant->closing_time }}
                            </span>
                        </div>
                        @endif
                    </div>
                </div>
                <div class="relative">
                    <div class="bg-primary-100 dark:bg-primary-900 rounded-lg p-8 text-center">
                        <i class="fas fa-utensils text-6xl text-primary-600 dark:text-primary-400 mb-4"></i>
                        <h3 class="text-2xl font-bold text-gray-900 dark:text-white mb-2">
                            {{ __('Quality Food') }}
                        </h3>
                        <p class="text-gray-600 dark:text-gray-400">
                            {{ __('Fresh ingredients, authentic flavors, and exceptional service') }}
                        </p>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Call to Action -->
    <section class="py-16 bg-primary-600 text-white">
        <div class="container mx-auto px-4 text-center">
            <h2 class="text-3xl font-bold mb-4">{{ __('Ready to Order?') }}</h2>
            <p class="text-xl mb-8 opacity-90">
                {{ __('Browse our full menu and place your order online') }}
            </p>
            <a href="{{ route('guest.menu') }}" 
               class="inline-block bg-white text-primary-600 px-8 py-3 rounded-lg font-semibold hover:bg-gray-100 transition-colors">
                {{ __('Order Now') }}
            </a>
        </div>
    </section>
@endsection

@push('scripts')
<script>
    // Add any page-specific JavaScript here
</script>
@endpush
