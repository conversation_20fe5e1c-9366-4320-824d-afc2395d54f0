<!-- Footer -->
<footer class="bg-gray-800 dark:bg-gray-900 text-white py-12 theme-transition">
    <div class="container mx-auto px-4">
        <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
            <!-- Restaurant Info -->
            <div>
                <h3 class="text-xl font-semibold mb-4">
                    {{ $siteSettings['general']['site_name'] ?? $restaurant->name ?? 'Restaurant' }}
                </h3>
                <p class="text-gray-300 mb-4">
                    {{ $siteSettings['general']['site_description'] ?? $restaurant->description ?? 'Delicious food, great atmosphere' }}
                </p>
                <div class="flex space-x-4">
                    @if(isset($siteSettings['social']['facebook_url']) && $siteSettings['social']['facebook_url'])
                    <a href="{{ $siteSettings['social']['facebook_url'] }}" 
                       target="_blank" 
                       rel="noopener noreferrer"
                       class="text-gray-300 hover:text-white transition-colors"
                       aria-label="Facebook">
                        <i class="fab fa-facebook text-xl"></i>
                    </a>
                    @endif
                    
                    @if(isset($siteSettings['social']['instagram_url']) && $siteSettings['social']['instagram_url'])
                    <a href="{{ $siteSettings['social']['instagram_url'] }}" 
                       target="_blank" 
                       rel="noopener noreferrer"
                       class="text-gray-300 hover:text-white transition-colors"
                       aria-label="Instagram">
                        <i class="fab fa-instagram text-xl"></i>
                    </a>
                    @endif
                    
                    @if(isset($siteSettings['social']['twitter_url']) && $siteSettings['social']['twitter_url'])
                    <a href="{{ $siteSettings['social']['twitter_url'] }}" 
                       target="_blank" 
                       rel="noopener noreferrer"
                       class="text-gray-300 hover:text-white transition-colors"
                       aria-label="Twitter">
                        <i class="fab fa-twitter text-xl"></i>
                    </a>
                    @endif
                    
                    @if(isset($siteSettings['social']['youtube_url']) && $siteSettings['social']['youtube_url'])
                    <a href="{{ $siteSettings['social']['youtube_url'] }}" 
                       target="_blank" 
                       rel="noopener noreferrer"
                       class="text-gray-300 hover:text-white transition-colors"
                       aria-label="YouTube">
                        <i class="fab fa-youtube text-xl"></i>
                    </a>
                    @endif
                    
                    @if(isset($siteSettings['social']['linkedin_url']) && $siteSettings['social']['linkedin_url'])
                    <a href="{{ $siteSettings['social']['linkedin_url'] }}" 
                       target="_blank" 
                       rel="noopener noreferrer"
                       class="text-gray-300 hover:text-white transition-colors"
                       aria-label="LinkedIn">
                        <i class="fab fa-linkedin text-xl"></i>
                    </a>
                    @endif
                </div>
            </div>

            <!-- Quick Links -->
            <div>
                <h3 class="text-xl font-semibold mb-4">{{ __('Quick Links') }}</h3>
                <div class="space-y-2">
                    <a href="{{ route('guest.home') }}" 
                       class="block text-gray-300 hover:text-white transition-colors">
                        {{ __('Home') }}
                    </a>
                    <a href="{{ route('guest.menu') }}" 
                       class="block text-gray-300 hover:text-white transition-colors">
                        {{ __('Menu') }}
                    </a>
                    @if(isset($siteSettings['general']['about_page_enabled']) && $siteSettings['general']['about_page_enabled'])
                    <a href="{{ route('guest.page.show', 'about-us') }}" 
                       class="block text-gray-300 hover:text-white transition-colors">
                        {{ __('About Us') }}
                    </a>
                    @endif
                    @if(isset($siteSettings['general']['contact_page_enabled']) && $siteSettings['general']['contact_page_enabled'])
                    <a href="{{ route('guest.page.show', 'contact') }}" 
                       class="block text-gray-300 hover:text-white transition-colors">
                        {{ __('Contact') }}
                    </a>
                    @endif
                    <a href="{{ route('login') }}" 
                       class="block text-gray-300 hover:text-white transition-colors">
                        {{ __('Staff Login') }}
                    </a>
                </div>
            </div>

            <!-- Contact Info -->
            <div>
                <h3 class="text-xl font-semibold mb-4">{{ __('Contact Info') }}</h3>
                <div class="space-y-2 text-gray-300">
                    @if(isset($siteSettings['contact']['address']) && $siteSettings['contact']['address'])
                    <div class="flex items-start space-x-2">
                        <i class="fas fa-map-marker-alt mt-1"></i>
                        <span>
                            {{ $siteSettings['contact']['address'] }}
                            @if(isset($siteSettings['contact']['city']) && $siteSettings['contact']['city'])
                                , {{ $siteSettings['contact']['city'] }}
                            @endif
                            @if(isset($siteSettings['contact']['state']) && $siteSettings['contact']['state'])
                                , {{ $siteSettings['contact']['state'] }}
                            @endif
                            @if(isset($siteSettings['contact']['postal_code']) && $siteSettings['contact']['postal_code'])
                                {{ $siteSettings['contact']['postal_code'] }}
                            @endif
                        </span>
                    </div>
                    @endif
                    
                    @if(isset($siteSettings['contact']['phone']) && $siteSettings['contact']['phone'])
                    <div class="flex items-center space-x-2">
                        <i class="fas fa-phone"></i>
                        <a href="tel:{{ $siteSettings['contact']['phone'] }}" 
                           class="hover:text-white transition-colors">
                            {{ $siteSettings['contact']['phone'] }}
                        </a>
                    </div>
                    @endif
                    
                    @if(isset($siteSettings['contact']['email']) && $siteSettings['contact']['email'])
                    <div class="flex items-center space-x-2">
                        <i class="fas fa-envelope"></i>
                        <a href="mailto:{{ $siteSettings['contact']['email'] }}" 
                           class="hover:text-white transition-colors">
                            {{ $siteSettings['contact']['email'] }}
                        </a>
                    </div>
                    @endif
                    
                    @if(isset($restaurant->opening_time) && isset($restaurant->closing_time))
                    <div class="flex items-center space-x-2">
                        <i class="fas fa-clock"></i>
                        <span>
                            {{ $restaurant->opening_time }} - {{ $restaurant->closing_time }}
                        </span>
                    </div>
                    @endif
                </div>
            </div>
        </div>

        <div class="border-t border-gray-700 mt-8 pt-8 text-center text-gray-400">
            <p>&copy; {{ date('Y') }} {{ $siteSettings['general']['site_name'] ?? $restaurant->name ?? 'Restaurant' }}. {{ __('All rights reserved.') }}</p>
        </div>
    </div>
</footer>
