<!-- Cart Sidebar -->
<div id="cart-sidebar" class="fixed inset-y-0 right-0 z-50 w-96 bg-white dark:bg-gray-800 shadow-xl transform translate-x-full transition-transform duration-300 ease-in-out">
    <!-- Cart Header -->
    <div class="flex items-center justify-between p-4 border-b border-gray-200 dark:border-gray-700">
        <h2 class="text-lg font-semibold text-gray-900 dark:text-white">{{ __('Shopping Cart') }}</h2>
        <button onclick="toggleCart()" class="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300">
            <i class="fas fa-times text-xl"></i>
        </button>
    </div>

    <!-- Cart Content -->
    <div class="flex flex-col h-full">
        <!-- Cart Items -->
        <div id="cart-items" class="flex-1 overflow-y-auto p-4">
            <!-- Empty Cart Message -->
            <div id="empty-cart" class="text-center py-12">
                <i class="fas fa-shopping-cart text-4xl text-gray-300 dark:text-gray-600 mb-4"></i>
                <p class="text-gray-500 dark:text-gray-400">{{ __('Your cart is empty') }}</p>
                <p class="text-sm text-gray-400 dark:text-gray-500 mt-2">{{ __('Add some delicious items from our menu') }}</p>
            </div>

            <!-- Cart Items List (populated by JavaScript) -->
            <div id="cart-items-list" class="space-y-4 hidden">
                <!-- Items will be dynamically added here -->
            </div>
        </div>

        <!-- Cart Footer -->
        <div id="cart-footer" class="hidden border-t border-gray-200 dark:border-gray-700 p-4 bg-gray-50 dark:bg-gray-900">
            <!-- Cart Summary -->
            <div class="space-y-2 mb-4">
                <div class="flex justify-between text-sm">
                    <span class="text-gray-600 dark:text-gray-400">{{ __('Subtotal') }}</span>
                    <span id="cart-subtotal" class="font-medium text-gray-900 dark:text-white">৳0.00</span>
                </div>
                <div class="flex justify-between text-sm">
                    <span class="text-gray-600 dark:text-gray-400">{{ __('Tax') }}</span>
                    <span id="cart-tax" class="font-medium text-gray-900 dark:text-white">৳0.00</span>
                </div>
                <div class="flex justify-between text-lg font-semibold border-t border-gray-200 dark:border-gray-700 pt-2">
                    <span class="text-gray-900 dark:text-white">{{ __('Total') }}</span>
                    <span id="cart-total" class="text-primary-600 dark:text-primary-400">৳0.00</span>
                </div>
            </div>

            <!-- Action Buttons -->
            <div class="space-y-2">
                <button onclick="proceedToCheckout()" 
                        class="w-full bg-primary-600 text-white py-3 px-4 rounded-lg hover:bg-primary-700 transition-colors font-medium">
                    {{ __('Proceed to Checkout') }}
                </button>
                <button onclick="clearCart()" 
                        class="w-full bg-gray-200 dark:bg-gray-700 text-gray-800 dark:text-gray-200 py-2 px-4 rounded-lg hover:bg-gray-300 dark:hover:bg-gray-600 transition-colors">
                    {{ __('Clear Cart') }}
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Cart Overlay -->
<div id="cart-overlay" class="fixed inset-0 bg-black bg-opacity-50 z-40 hidden" onclick="toggleCart()"></div>

<!-- Cart Item Template (hidden) -->
<template id="cart-item-template">
    <div class="cart-item flex items-center space-x-3 p-3 bg-white dark:bg-gray-700 rounded-lg border border-gray-200 dark:border-gray-600">
        <img class="item-image w-16 h-16 object-cover rounded-lg" src="" alt="">
        <div class="flex-1 min-w-0">
            <h4 class="item-name text-sm font-medium text-gray-900 dark:text-white truncate"></h4>
            <p class="item-price text-sm text-gray-600 dark:text-gray-400"></p>
            <div class="flex items-center space-x-2 mt-2">
                <button class="quantity-decrease w-6 h-6 bg-gray-200 dark:bg-gray-600 text-gray-700 dark:text-gray-300 rounded-full flex items-center justify-center hover:bg-gray-300 dark:hover:bg-gray-500 transition-colors">
                    <i class="fas fa-minus text-xs"></i>
                </button>
                <span class="item-quantity text-sm font-medium text-gray-900 dark:text-white px-2">1</span>
                <button class="quantity-increase w-6 h-6 bg-gray-200 dark:bg-gray-600 text-gray-700 dark:text-gray-300 rounded-full flex items-center justify-center hover:bg-gray-300 dark:hover:bg-gray-500 transition-colors">
                    <i class="fas fa-plus text-xs"></i>
                </button>
            </div>
        </div>
        <button class="remove-item text-red-500 hover:text-red-700 dark:text-red-400 dark:hover:text-red-300 transition-colors">
            <i class="fas fa-trash text-sm"></i>
        </button>
    </div>
</template>
