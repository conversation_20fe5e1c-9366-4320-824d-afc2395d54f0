<script>
// Cart functionality for Blade templates
class BladeCart {
    constructor() {
        this.items = [];
        this.isOpen = false;
        this.taxRate = {{ $restaurant->tax_rate ?? 0 }};
        this.serviceCharge = {{ $restaurant->service_charge ?? 0 }};
        this.deliveryCharge = {{ $restaurant->delivery_charge ?? 0 }};
        
        this.init();
    }
    
    init() {
        this.loadFromStorage();
        this.updateCartDisplay();
        this.updateCartCount();
        this.bindEvents();
    }
    
    // Load cart from localStorage
    loadFromStorage() {
        try {
            const stored = localStorage.getItem('restaurant_cart');
            if (stored) {
                this.items = JSON.parse(stored);
            }
        } catch (error) {
            console.error('Error loading cart from storage:', error);
            this.items = [];
        }
    }
    
    // Save cart to localStorage
    saveToStorage() {
        try {
            localStorage.setItem('restaurant_cart', JSON.stringify(this.items));
        } catch (error) {
            console.error('Error saving cart to storage:', error);
        }
    }
    
    // Generate unique cart item ID
    generateItemId(item) {
        const variationsKey = Object.entries(item.variations || {})
            .sort(([a], [b]) => a.localeCompare(b))
            .map(([key, value]) => `${key}:${value}`)
            .join('|');
        
        const addonsKey = (item.addons || [])
            .sort((a, b) => a - b)
            .join(',');
        
        const specialInstructionsKey = (item.specialInstructions || '').trim();
        
        return `${item.menuItemId}_${variationsKey}_${addonsKey}_${specialInstructionsKey}`;
    }
    
    // Add item to cart
    addItem(item) {
        const itemId = this.generateItemId(item);
        const existingItemIndex = this.items.findIndex(cartItem => cartItem.id === itemId);
        
        if (existingItemIndex > -1) {
            // Update quantity of existing item
            this.items[existingItemIndex].quantity += item.quantity;
            this.items[existingItemIndex].totalPrice = 
                this.items[existingItemIndex].quantity * (item.totalPrice / item.quantity);
        } else {
            // Add new item to cart
            this.items.push({
                id: itemId,
                ...item,
                addedAt: new Date().toISOString()
            });
        }
        
        this.saveToStorage();
        this.updateCartDisplay();
        this.updateCartCount();
        this.showNotification(`Added ${item.name} to cart!`);
    }
    
    // Remove item from cart
    removeItem(itemId) {
        const index = this.items.findIndex(item => item.id === itemId);
        if (index > -1) {
            this.items.splice(index, 1);
            this.saveToStorage();
            this.updateCartDisplay();
            this.updateCartCount();
        }
    }
    
    // Update item quantity
    updateQuantity(itemId, quantity) {
        const item = this.items.find(item => item.id === itemId);
        if (item) {
            if (quantity <= 0) {
                this.removeItem(itemId);
            } else {
                const unitPrice = item.totalPrice / item.quantity;
                item.quantity = quantity;
                item.totalPrice = unitPrice * quantity;
                this.saveToStorage();
                this.updateCartDisplay();
                this.updateCartCount();
            }
        }
    }
    
    // Clear cart
    clear() {
        this.items = [];
        this.saveToStorage();
        this.updateCartDisplay();
        this.updateCartCount();
        this.showNotification('Cart cleared!');
    }
    
    // Toggle cart sidebar
    toggle() {
        this.isOpen = !this.isOpen;
        const sidebar = document.getElementById('cart-sidebar');
        const overlay = document.getElementById('cart-overlay');
        
        if (this.isOpen) {
            sidebar.classList.remove('translate-x-full');
            overlay.classList.remove('hidden');
            document.body.style.overflow = 'hidden';
        } else {
            sidebar.classList.add('translate-x-full');
            overlay.classList.add('hidden');
            document.body.style.overflow = '';
        }
    }
    
    // Open cart
    open() {
        if (!this.isOpen) {
            this.toggle();
        }
    }
    
    // Close cart
    close() {
        if (this.isOpen) {
            this.toggle();
        }
    }
    
    // Update cart display
    updateCartDisplay() {
        const emptyCart = document.getElementById('empty-cart');
        const cartItemsList = document.getElementById('cart-items-list');
        const cartFooter = document.getElementById('cart-footer');
        
        if (this.items.length === 0) {
            emptyCart.classList.remove('hidden');
            cartItemsList.classList.add('hidden');
            cartFooter.classList.add('hidden');
        } else {
            emptyCart.classList.add('hidden');
            cartItemsList.classList.remove('hidden');
            cartFooter.classList.remove('hidden');
            
            this.renderCartItems();
            this.updateCartTotals();
        }
    }
    
    // Render cart items
    renderCartItems() {
        const cartItemsList = document.getElementById('cart-items-list');
        const template = document.getElementById('cart-item-template');
        
        cartItemsList.innerHTML = '';
        
        this.items.forEach(item => {
            const itemElement = template.content.cloneNode(true);
            
            // Set item data
            itemElement.querySelector('.item-image').src = item.image || '/images/placeholder-food.jpg';
            itemElement.querySelector('.item-image').alt = item.name;
            itemElement.querySelector('.item-name').textContent = item.name;
            itemElement.querySelector('.item-price').textContent = `৳${(item.totalPrice / item.quantity).toFixed(2)} each`;
            itemElement.querySelector('.item-quantity').textContent = item.quantity;
            
            // Set data attributes
            const cartItemDiv = itemElement.querySelector('.cart-item');
            cartItemDiv.dataset.itemId = item.id;
            
            // Bind events
            itemElement.querySelector('.quantity-decrease').addEventListener('click', () => {
                this.updateQuantity(item.id, item.quantity - 1);
            });
            
            itemElement.querySelector('.quantity-increase').addEventListener('click', () => {
                this.updateQuantity(item.id, item.quantity + 1);
            });
            
            itemElement.querySelector('.remove-item').addEventListener('click', () => {
                this.removeItem(item.id);
            });
            
            cartItemsList.appendChild(itemElement);
        });
    }
    
    // Update cart totals
    updateCartTotals() {
        const subtotal = this.items.reduce((total, item) => total + item.totalPrice, 0);
        const tax = subtotal * (this.taxRate / 100);
        const total = subtotal + tax;
        
        document.getElementById('cart-subtotal').textContent = `৳${subtotal.toFixed(2)}`;
        document.getElementById('cart-tax').textContent = `৳${tax.toFixed(2)}`;
        document.getElementById('cart-total').textContent = `৳${total.toFixed(2)}`;
    }
    
    // Update cart count in header
    updateCartCount() {
        const count = this.items.reduce((total, item) => total + item.quantity, 0);
        const cartCountElements = document.querySelectorAll('.cart-count');
        
        cartCountElements.forEach(element => {
            element.textContent = count;
            element.style.display = count > 0 ? 'inline' : 'none';
        });
    }
    
    // Show notification
    showNotification(message) {
        // Create notification element
        const notification = document.createElement('div');
        notification.className = 'fixed top-4 right-4 bg-green-500 text-white px-6 py-3 rounded-lg shadow-lg z-50 transform translate-x-full transition-transform duration-300';
        notification.innerHTML = `
            <div class="flex items-center">
                <i class="fas fa-check-circle mr-2"></i>
                <span>${message}</span>
            </div>
        `;
        
        document.body.appendChild(notification);
        
        // Animate in
        setTimeout(() => {
            notification.classList.remove('translate-x-full');
        }, 100);
        
        // Animate out and remove
        setTimeout(() => {
            notification.classList.add('translate-x-full');
            setTimeout(() => {
                if (notification.parentNode) {
                    document.body.removeChild(notification);
                }
            }, 300);
        }, 3000);
    }
    
    // Bind events
    bindEvents() {
        // Close cart when clicking overlay
        document.getElementById('cart-overlay')?.addEventListener('click', () => {
            this.close();
        });
        
        // Escape key to close cart
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape' && this.isOpen) {
                this.close();
            }
        });
    }
    
    // Get cart data for checkout
    getCheckoutData() {
        const subtotal = this.items.reduce((total, item) => total + item.totalPrice, 0);
        const tax = subtotal * (this.taxRate / 100);
        const total = subtotal + tax;
        
        return {
            items: this.items.map(item => ({
                menu_item_id: item.menuItemId,
                quantity: item.quantity,
                variations: item.variations || {},
                addons: item.addons || [],
                special_instructions: item.specialInstructions || '',
                unit_price: item.totalPrice / item.quantity,
                total_price: item.totalPrice
            })),
            subtotal: subtotal,
            tax: tax,
            total: total,
            item_count: this.items.reduce((total, item) => total + item.quantity, 0)
        };
    }
}

// Initialize cart
const cart = new BladeCart();

// Global functions for backward compatibility
function toggleCart() {
    cart.toggle();
}

function clearCart() {
    if (confirm('{{ __("Are you sure you want to clear your cart?") }}')) {
        cart.clear();
    }
}

function proceedToCheckout() {
    if (cart.items.length === 0) {
        alert('{{ __("Your cart is empty!") }}');
        return;
    }
    
    // Redirect to checkout page
    window.location.href = '{{ route("guest.checkout") }}';
}

// Function to add menu item to cart (for backward compatibility)
function addMenuItemToCart(menuItemId, name, price, image, quantity = 1, variations = {}, addons = [], specialInstructions = '') {
    const item = {
        menuItemId: menuItemId,
        name: name,
        image: image,
        basePrice: price,
        totalPrice: price * quantity,
        quantity: quantity,
        variations: variations,
        addons: addons,
        specialInstructions: specialInstructions
    };
    
    cart.addItem(item);
}

// Update cart count on page load
document.addEventListener('DOMContentLoaded', function() {
    cart.updateCartCount();
});
</script>
