<script>
// Global variables
let cart = JSON.parse(localStorage.getItem('restaurant_cart') || '[]');
let isCartOpen = false;
let currentTheme = localStorage.getItem('theme') || 'system';

// Initialize on page load
document.addEventListener('DOMContentLoaded', function() {
    initializeTheme();
    updateCartUI();
    
    // Close dropdowns when clicking outside
    document.addEventListener('click', function(event) {
        if (!event.target.closest('#dropdown-menu') && !event.target.closest('[onclick="toggleDropdown()"]')) {
            document.getElementById('dropdown-menu').classList.add('hidden');
        }
    });
});

// Theme Management
function initializeTheme() {
    const savedTheme = localStorage.getItem('theme') || 'system';
    applyTheme(savedTheme);
}

function setTheme(theme) {
    localStorage.setItem('theme', theme);
    applyTheme(theme);
    document.getElementById('dropdown-menu').classList.add('hidden');
}

function applyTheme(theme) {
    const html = document.documentElement;
    
    if (theme === 'dark') {
        html.classList.add('dark');
    } else if (theme === 'light') {
        html.classList.remove('dark');
    } else {
        // System theme
        if (window.matchMedia('(prefers-color-scheme: dark)').matches) {
            html.classList.add('dark');
        } else {
            html.classList.remove('dark');
        }
    }
    
    // Update theme cookie for server-side rendering
    document.cookie = `theme=${theme}; path=/; max-age=31536000`; // 1 year
}

// Language Management
function switchLanguage(locale) {
    // Create a form to submit the language change
    const form = document.createElement('form');
    form.method = 'POST';
    form.action = '{{ route("guest.language.switch") }}';

    // Add CSRF token
    const csrfToken = document.createElement('input');
    csrfToken.type = 'hidden';
    csrfToken.name = '_token';
    csrfToken.value = '{{ csrf_token() }}';
    form.appendChild(csrfToken);

    // Add locale
    const localeInput = document.createElement('input');
    localeInput.type = 'hidden';
    localeInput.name = 'locale';
    localeInput.value = locale;
    form.appendChild(localeInput);

    document.body.appendChild(form);
    form.submit();
}

// Navigation Functions
function toggleDropdown() {
    const dropdown = document.getElementById('dropdown-menu');
    dropdown.classList.toggle('hidden');
}

function toggleMobileMenu() {
    const mobileMenu = document.getElementById('mobile-menu');
    mobileMenu.classList.toggle('hidden');
}

// Cart Management
function toggleCart() {
    const sidebar = document.getElementById('cart-sidebar');
    const overlay = document.getElementById('cart-overlay');
    
    if (isCartOpen) {
        sidebar.classList.add('translate-x-full');
        overlay.classList.add('hidden');
        isCartOpen = false;
    } else {
        sidebar.classList.remove('translate-x-full');
        overlay.classList.remove('hidden');
        isCartOpen = true;
    }
}

function addToCart(item) {
    const existingItem = cart.find(cartItem => cartItem.id === item.id);
    
    if (existingItem) {
        existingItem.quantity += 1;
    } else {
        cart.push({
            id: item.id,
            name: item.name,
            price: item.price,
            image: item.image,
            quantity: 1
        });
    }
    
    saveCart();
    updateCartUI();
    showCartNotification('Item added to cart!');
}

function removeFromCart(itemId) {
    cart = cart.filter(item => item.id !== itemId);
    saveCart();
    updateCartUI();
}

function updateQuantity(itemId, newQuantity) {
    if (newQuantity <= 0) {
        removeFromCart(itemId);
        return;
    }
    
    const item = cart.find(cartItem => cartItem.id === itemId);
    if (item) {
        item.quantity = newQuantity;
        saveCart();
        updateCartUI();
    }
}

function clearCart() {
    if (confirm('{{ __("Are you sure you want to clear your cart?") }}')) {
        cart = [];
        saveCart();
        updateCartUI();
    }
}

function saveCart() {
    localStorage.setItem('restaurant_cart', JSON.stringify(cart));
}

function updateCartUI() {
    const cartCount = cart.reduce((total, item) => total + item.quantity, 0);
    const cartSubtotal = cart.reduce((total, item) => total + (item.totalPrice || item.price * item.quantity), 0);
    const cartTax = cartSubtotal * ({{ $restaurant->tax_rate ?? 0 }} / 100);
    const cartTotal = cartSubtotal + cartTax;
    
    // Update cart count badges
    const cartCountElement = document.getElementById('cart-count');
    const mobileCartCountElement = document.getElementById('mobile-cart-count');
    
    if (cartCount > 0) {
        cartCountElement.textContent = cartCount;
        cartCountElement.classList.remove('hidden');
        mobileCartCountElement.textContent = cartCount;
        mobileCartCountElement.classList.remove('hidden');
    } else {
        cartCountElement.classList.add('hidden');
        mobileCartCountElement.classList.add('hidden');
    }
    
    // Update cart sidebar
    const emptyCart = document.getElementById('empty-cart');
    const cartItemsList = document.getElementById('cart-items-list');
    const cartFooter = document.getElementById('cart-footer');
    
    if (cart.length === 0) {
        emptyCart.classList.remove('hidden');
        cartItemsList.classList.add('hidden');
        cartFooter.classList.add('hidden');
    } else {
        emptyCart.classList.add('hidden');
        cartItemsList.classList.remove('hidden');
        cartFooter.classList.remove('hidden');
        
        // Update cart items
        renderCartItems();
        
        // Update totals
        document.getElementById('cart-subtotal').textContent = `৳${cartSubtotal.toFixed(2)}`;
        document.getElementById('cart-tax').textContent = `৳${cartTax.toFixed(2)}`;
        document.getElementById('cart-total').textContent = `৳${cartTotal.toFixed(2)}`;
    }
}

function renderCartItems() {
    const cartItemsList = document.getElementById('cart-items-list');
    const template = document.getElementById('cart-item-template');

    cartItemsList.innerHTML = '';

    cart.forEach(item => {
        const itemElement = template.content.cloneNode(true);

        itemElement.querySelector('.item-image').src = item.image || '/images/placeholder-food.jpg';
        itemElement.querySelector('.item-image').alt = item.name;
        itemElement.querySelector('.item-name').textContent = item.name;
        itemElement.querySelector('.item-price').textContent = `৳${(item.basePrice || item.price).toFixed(2)} each`;
        itemElement.querySelector('.item-quantity').textContent = item.quantity;
        
        // Add event listeners
        itemElement.querySelector('.quantity-decrease').addEventListener('click', () => {
            updateQuantity(item.id, item.quantity - 1);
        });
        
        itemElement.querySelector('.quantity-increase').addEventListener('click', () => {
            updateQuantity(item.id, item.quantity + 1);
        });
        
        itemElement.querySelector('.remove-item').addEventListener('click', () => {
            removeFromCart(item.id);
        });
        
        cartItemsList.appendChild(itemElement);
    });
}

function showCartNotification(message) {
    // Create notification element
    const notification = document.createElement('div');
    notification.className = 'fixed top-4 right-4 bg-green-500 text-white px-6 py-3 rounded-lg shadow-lg z-50 transform translate-x-full transition-transform duration-300';
    notification.textContent = message;
    
    document.body.appendChild(notification);
    
    // Show notification
    setTimeout(() => {
        notification.classList.remove('translate-x-full');
    }, 100);
    
    // Hide notification after 3 seconds
    setTimeout(() => {
        notification.classList.add('translate-x-full');
        setTimeout(() => {
            document.body.removeChild(notification);
        }, 300);
    }, 3000);
}

function proceedToCheckout() {
    if (cart.length === 0) {
        alert('{{ __("Your cart is empty") }}');
        return;
    }
    
    // Redirect to checkout page or show checkout modal
    window.location.href = '{{ route("guest.checkout") }}';
}

// Enhanced menu item functions (for menu pages)
function addMenuItemToCart(itemId, itemName, itemPrice, itemImage, quantity = 1, variations = {}, addons = [], specialInstructions = '') {
    // Generate unique cart item ID
    const variationsKey = Object.entries(variations || {})
        .sort(([a], [b]) => a.localeCompare(b))
        .map(([key, value]) => `${key}:${value}`)
        .join('|');

    const addonsKey = (addons || [])
        .sort((a, b) => a - b)
        .join(',');

    const specialInstructionsKey = (specialInstructions || '').trim();
    const uniqueId = `${itemId}_${variationsKey}_${addonsKey}_${specialInstructionsKey}`;

    // Check if item with same configuration already exists
    const existingItem = cart.find(cartItem => cartItem.id === uniqueId);

    if (existingItem) {
        existingItem.quantity += quantity;
        existingItem.totalPrice = existingItem.quantity * existingItem.basePrice;
    } else {
        cart.push({
            id: uniqueId,
            menuItemId: itemId,
            name: itemName,
            basePrice: parseFloat(itemPrice),
            totalPrice: parseFloat(itemPrice) * quantity,
            image: itemImage,
            quantity: quantity,
            variations: variations,
            addons: addons,
            specialInstructions: specialInstructions,
            addedAt: new Date().toISOString()
        });
    }

    saveCart();
    updateCartUI();
    showCartNotification(`Added ${itemName} to cart!`);
}

// System theme detection
if (window.matchMedia) {
    window.matchMedia('(prefers-color-scheme: dark)').addEventListener('change', function(e) {
        if (localStorage.getItem('theme') === 'system') {
            applyTheme('system');
        }
    });
}

// Keyboard navigation for accessibility
document.addEventListener('keydown', function(event) {
    // Close cart with Escape key
    if (event.key === 'Escape' && isCartOpen) {
        toggleCart();
    }
    
    // Close dropdown with Escape key
    if (event.key === 'Escape') {
        document.getElementById('dropdown-menu').classList.add('hidden');
    }
});
</script>
