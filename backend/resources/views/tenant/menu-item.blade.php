@extends('layouts.tenant')

@section('title', $menuItem->name . ' - ' . ($siteSettings['general']['site_name'] ?? $restaurant->name ?? 'Restaurant'))
@section('description', $menuItem->description ?? 'Delicious menu item from our restaurant')
@section('og_title', $menuItem->name)
@section('og_description', $menuItem->description ?? 'Delicious menu item from our restaurant')
@section('og_type', 'product')
@section('canonical', request()->url())

@push('head')
    <!-- JSON-LD Structured Data for Menu Item -->
    <script type="application/ld+json">
    {
        "@context": "https://schema.org",
        "@type": "MenuItem",
        "name": "{{ $menuItem->name }}",
        "description": "{{ $menuItem->description ?? '' }}",
        "image": "{{ $menuItem->primary_image_url }}",
        "offers": {
            "@type": "Offer",
            "price": "{{ $menuItem->price }}",
            "priceCurrency": "BDT",
            "availability": "{{ $menuItem->is_available ? 'InStock' : 'OutOfStock' }}"
        },
        "nutrition": {
            "@type": "NutritionInformation",
            "calories": "{{ $menuItem->calories ?? '' }}"
        },
        "suitableForDiet": [
            @if($menuItem->is_vegetarian)"VegetarianDiet"@endif
            @if($menuItem->is_vegan)@if($menuItem->is_vegetarian),@endif"VeganDiet"@endif
            @if($menuItem->is_gluten_free)@if($menuItem->is_vegetarian || $menuItem->is_vegan),@endif"GlutenFreeDiet"@endif
        ]
    }
    </script>
    
    <!-- Custom Styles -->
    <style>
        .image-slider {
            position: relative;
            overflow: hidden;
        }
        
        .image-slider img {
            transition: transform 0.3s ease;
        }
        
        .image-slider:hover img {
            transform: scale(1.05);
        }
        
        .quantity-selector button {
            transition: all 0.2s ease;
        }
        
        .quantity-selector button:hover {
            background-color: #f3f4f6;
        }
        
        .cart-notification {
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 1000;
            transform: translateX(100%);
            transition: transform 0.3s ease;
        }
        
        .cart-notification.show {
            transform: translateX(0);
        }
    </style>
@endpush

@section('content')
    <!-- Breadcrumb -->
    <section class="bg-gray-50 dark:bg-gray-800 py-4">
        <div class="container mx-auto px-4">
            <nav class="flex items-center space-x-2 text-sm">
                <a href="{{ route('guest.home') }}" class="text-gray-600 dark:text-gray-400 hover:text-primary-600 dark:hover:text-primary-400">
                    {{ __('Home') }}
                </a>
                <span class="text-gray-400">/</span>
                <a href="{{ route('guest.menu') }}" class="text-gray-600 dark:text-gray-400 hover:text-primary-600 dark:hover:text-primary-400">
                    {{ __('Menu') }}
                </a>
                <span class="text-gray-400">/</span>
                <span class="text-gray-800 dark:text-gray-200 font-semibold">{{ $menuItem->name }}</span>
            </nav>
        </div>
    </section>

    <!-- Menu Item Details -->
    <section class="py-12">
        <div class="container mx-auto px-4">
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-12">
                <!-- Image Section -->
                <div class="space-y-4">
                    <div class="image-slider aspect-w-1 aspect-h-1 bg-gray-200 dark:bg-gray-700 rounded-lg overflow-hidden">
                        @if($menuItem->primary_image_url)
                            <img src="{{ $menuItem->primary_image_url }}" 
                                 alt="{{ $menuItem->name }}" 
                                 class="w-full h-96 object-cover"
                                 onerror="this.src='data:image/svg+xml;base64,{{ base64_encode('<svg xmlns=\"http://www.w3.org/2000/svg\" width=\"400\" height=\"400\" viewBox=\"0 0 400 400\"><rect width=\"400\" height=\"400\" fill=\"#f3f4f6\"/><circle cx=\"160\" cy=\"140\" r=\"24\" fill=\"#d1d5db\"/><polygon points=\"80,280 160,200 240,240 320,160 360,280\" fill=\"#d1d5db\"/><text x=\"200\" y=\"340\" text-anchor=\"middle\" fill=\"#9ca3af\" font-size=\"32\">' . $menuItem->name . '</text></svg>') }}'">
                        @else
                            <div class="w-full h-96 flex items-center justify-center text-gray-400">
                                <i class="fas fa-utensils text-6xl"></i>
                            </div>
                        @endif
                    </div>
                    
                    <!-- Additional Images -->
                    @if($menuItem->mediaItems && $menuItem->mediaItems->count() > 1)
                        <div class="grid grid-cols-4 gap-2">
                            @foreach($menuItem->mediaItems->take(4) as $media)
                                <div class="aspect-w-1 aspect-h-1 bg-gray-200 dark:bg-gray-700 rounded overflow-hidden cursor-pointer hover:opacity-75 transition-opacity">
                                    <img src="{{ $media->url }}" 
                                         alt="{{ $menuItem->name }}" 
                                         class="w-full h-20 object-cover"
                                         onclick="changeMainImage('{{ $media->url }}')">
                                </div>
                            @endforeach
                        </div>
                    @endif
                </div>

                <!-- Details Section -->
                <div class="space-y-6">
                    <div>
                        <h1 class="text-3xl font-bold text-gray-800 dark:text-white mb-2">{{ $menuItem->name }}</h1>
                        @if($menuItem->description)
                            <p class="text-gray-600 dark:text-gray-400 text-lg">{{ $menuItem->description }}</p>
                        @endif
                    </div>

                    <!-- Price -->
                    <div class="text-3xl font-bold text-primary-600">
                        ৳{{ number_format($menuItem->price, 2) }}
                    </div>

                    <!-- Tags -->
                    <div class="flex flex-wrap gap-2">
                        @if($menuItem->is_vegetarian)
                            <span class="bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200 px-3 py-1 rounded-full text-sm">
                                <i class="fas fa-leaf mr-1"></i>{{ __('Vegetarian') }}
                            </span>
                        @endif
                        @if($menuItem->is_vegan)
                            <span class="bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200 px-3 py-1 rounded-full text-sm">
                                <i class="fas fa-seedling mr-1"></i>{{ __('Vegan') }}
                            </span>
                        @endif
                        @if($menuItem->is_spicy)
                            <span class="bg-red-100 dark:bg-red-900 text-red-800 dark:text-red-200 px-3 py-1 rounded-full text-sm">
                                <i class="fas fa-pepper-hot mr-1"></i>{{ __('Spicy') }}
                            </span>
                        @endif
                        @if($menuItem->is_gluten_free)
                            <span class="bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200 px-3 py-1 rounded-full text-sm">
                                {{ __('Gluten Free') }}
                            </span>
                        @endif
                    </div>

                    <!-- Additional Info -->
                    <div class="grid grid-cols-2 gap-4 text-sm">
                        @if($menuItem->preparation_time)
                            <div class="flex items-center text-gray-600 dark:text-gray-400">
                                <i class="fas fa-clock mr-2 text-primary-600"></i>
                                {{ $menuItem->preparation_time }} {{ __('minutes') }}
                            </div>
                        @endif
                        @if($menuItem->calories)
                            <div class="flex items-center text-gray-600 dark:text-gray-400">
                                <i class="fas fa-fire mr-2 text-primary-600"></i>
                                {{ $menuItem->calories }} {{ __('calories') }}
                            </div>
                        @endif
                    </div>

                    <!-- Combo Components -->
                    @if($menuItem->is_combo && $menuItem->comboItems && $menuItem->comboItems->count() > 0)
                        <div>
                            <h3 class="text-lg font-semibold mb-4">{{ __('This Combo Includes') }}</h3>
                            <div class="grid grid-cols-1 sm:grid-cols-2 gap-4">
                                @php
                                    // Group combo items by menu item ID and component type to calculate quantities
                                    $groupedComponents = [];
                                    foreach($menuItem->comboItems as $item) {
                                        $key = $item->id . '_' . $item->pivot->component_type;
                                        if (isset($groupedComponents[$key])) {
                                            $groupedComponents[$key]['quantity']++;
                                        } else {
                                            $groupedComponents[$key] = [
                                                'item' => $item,
                                                'component_type' => $item->pivot->component_type,
                                                'is_required' => $item->pivot->is_required,
                                                'quantity' => 1,
                                                'sort_order' => $item->pivot->sort_order,
                                            ];
                                        }
                                    }
                                    // Sort by sort_order
                                    uasort($groupedComponents, function($a, $b) {
                                        return $a['sort_order'] <=> $b['sort_order'];
                                    });
                                @endphp

                                @foreach($groupedComponents as $component)
                                    <div class="flex items-center space-x-3 p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
                                        <!-- Circular Image -->
                                        <div class="w-12 h-12 rounded-full bg-gray-200 dark:bg-gray-600 flex items-center justify-center flex-shrink-0 overflow-hidden">
                                            @if($component['item']->primary_image_url)
                                                <img src="{{ $component['item']->primary_image_url }}"
                                                     alt="{{ $component['item']->name }}"
                                                     class="w-full h-full object-cover"
                                                     onerror="this.src='data:image/svg+xml;base64,{{ base64_encode('<svg xmlns=\"http://www.w3.org/2000/svg\" width=\"48\" height=\"48\" viewBox=\"0 0 48 48\"><rect width=\"48\" height=\"48\" fill=\"#f3f4f6\" rx=\"24\"/><circle cx=\"18\" cy=\"18\" r=\"3\" fill=\"#d1d5db\"/><polygon points=\"9,33 18,24 27,27 36,21 39,33\" fill=\"#d1d5db\"/></svg>') }}'">
                                            @else
                                                <span class="text-lg">🍽️</span>
                                            @endif
                                        </div>

                                        <!-- Item Details -->
                                        <div class="flex-1 min-w-0">
                                            <div class="flex items-center space-x-2">
                                                <h4 class="font-medium text-gray-900 dark:text-white truncate">
                                                    {{ $component['item']->name }}
                                                </h4>
                                                @if($component['quantity'] > 1)
                                                    <span class="bg-indigo-100 dark:bg-indigo-900 text-indigo-800 dark:text-indigo-200 px-2 py-1 rounded-full text-xs font-medium">
                                                        {{ $component['quantity'] }}x
                                                    </span>
                                                @endif
                                            </div>
                                            <div class="flex items-center space-x-2 mt-1">
                                                <span class="text-sm text-gray-600 dark:text-gray-400 capitalize">
                                                    {{ str_replace('_', ' ', $component['component_type']) }}
                                                </span>
                                                @if($component['is_required'])
                                                    <span class="bg-red-100 dark:bg-red-900 text-red-800 dark:text-red-200 px-2 py-1 rounded-full text-xs">
                                                        Required
                                                    </span>
                                                @else
                                                    <span class="bg-gray-100 dark:bg-gray-800 text-gray-600 dark:text-gray-400 px-2 py-1 rounded-full text-xs">
                                                        Optional
                                                    </span>
                                                @endif
                                            </div>
                                        </div>

                                        <!-- Price -->
                                        <div class="text-sm font-medium text-gray-700 dark:text-gray-300">
                                            ৳{{ number_format($component['item']->price * $component['quantity'], 2) }}
                                        </div>
                                    </div>
                                @endforeach
                            </div>

                            <!-- Combo Savings -->
                            @php
                                $individualTotal = $groupedComponents ? array_sum(array_map(function($comp) {
                                    return $comp['item']->price * $comp['quantity'];
                                }, $groupedComponents)) : 0;
                                $savings = max(0, $individualTotal - $menuItem->price);
                            @endphp

                            @if($savings > 0)
                                <div class="mt-4 p-3 bg-green-50 dark:bg-green-900 border border-green-200 dark:border-green-700 rounded-lg">
                                    <div class="flex items-center justify-between">
                                        <div>
                                            <div class="text-sm font-medium text-green-800 dark:text-green-200">
                                                💰 You Save with this Combo!
                                            </div>
                                            <div class="text-xs text-green-600 dark:text-green-400">
                                                Individual total: ৳{{ number_format($individualTotal, 2) }}
                                            </div>
                                        </div>
                                        <div class="text-lg font-bold text-green-600 dark:text-green-400">
                                            ৳{{ number_format($savings, 2) }}
                                        </div>
                                    </div>
                                </div>
                            @endif
                        </div>
                    @endif

                    <!-- Ingredients -->
                    @if($menuItem->ingredients)
                        <div>
                            <h3 class="text-lg font-semibold mb-2">{{ __('Ingredients') }}</h3>
                            <p class="text-gray-600 dark:text-gray-400">{{ is_array($menuItem->ingredients) ? implode(', ', $menuItem->ingredients) : $menuItem->ingredients }}</p>
                        </div>
                    @endif

                    <!-- Variations -->
                    @if($menuItem->variations && $menuItem->variations->count() > 0)
                        <div>
                            <h3 class="text-lg font-semibold mb-4">{{ __('Available Variations') }}</h3>
                            <div class="space-y-3">
                                @foreach($menuItem->variations as $variation)
                                    <label class="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-700 rounded-lg cursor-pointer hover:bg-gray-100 dark:hover:bg-gray-600 transition-colors border-2 border-transparent variation-option"
                                           data-variation-id="{{ $variation->id }}"
                                           data-price-modifier="{{ $variation->price_modifier }}">
                                        <div class="flex items-center space-x-3">
                                            <input type="radio"
                                                   name="variation"
                                                   value="{{ $variation->id }}"
                                                   class="text-primary-600 focus:ring-primary-500 focus:ring-2"
                                                   onchange="selectVariation({{ $variation->id }}, {{ $variation->price_modifier }})">
                                            <div>
                                                <span class="font-medium text-gray-900 dark:text-white">{{ $variation->name }}</span>
                                                @if($variation->description)
                                                    <p class="text-sm text-gray-700 dark:text-gray-300">{{ $variation->description }}</p>
                                                @endif
                                            </div>
                                        </div>
                                        <span class="text-sm font-medium text-gray-700 dark:text-gray-300">
                                            @if($variation->price_modifier > 0)
                                                +৳{{ number_format($variation->price_modifier, 2) }}
                                            @elseif($variation->price_modifier < 0)
                                                -৳{{ number_format(abs($variation->price_modifier), 2) }}
                                            @else
                                                {{ __('No extra charge') }}
                                            @endif
                                        </span>
                                    </label>
                                @endforeach
                            </div>
                        </div>
                    @endif

                    <!-- Add-ons -->
                    @if($menuItem->addons && $menuItem->addons->count() > 0)
                        <div>
                            <h3 class="text-lg font-semibold mb-4">{{ __('Available Add-ons') }}</h3>
                            <div class="grid grid-cols-1 sm:grid-cols-2 gap-3">
                                @foreach($menuItem->addons as $addon)
                                    <label class="flex items-center justify-between p-3 border-2 border-gray-200 dark:border-gray-600 rounded-lg cursor-pointer hover:border-primary-300 dark:hover:border-primary-500 transition-colors addon-option"
                                           data-addon-id="{{ $addon->id }}"
                                           data-addon-price="{{ $addon->price }}">
                                        <div class="flex items-center space-x-3">
                                            <input type="checkbox"
                                                   name="addons[]"
                                                   value="{{ $addon->id }}"
                                                   class="text-primary-600 focus:ring-primary-500 focus:ring-2 rounded"
                                                   onchange="toggleAddon({{ $addon->id }}, {{ $addon->price }})">
                                            <div>
                                                <div class="font-medium text-gray-900 dark:text-white">{{ $addon->name }}</div>
                                                @if($addon->description)
                                                    <div class="text-sm text-gray-700 dark:text-gray-300">{{ $addon->description }}</div>
                                                @endif
                                            </div>
                                        </div>
                                        <span class="text-sm font-semibold text-primary-600 dark:text-primary-400">+৳{{ number_format($addon->price, 2) }}</span>
                                    </label>
                                @endforeach
                            </div>
                        </div>
                    @endif

                    <!-- Availability Status -->
                    <div class="p-4 rounded-lg {{ $menuItem->is_available ? 'bg-green-50 dark:bg-green-900 border border-green-200 dark:border-green-700' : 'bg-red-50 dark:bg-red-900 border border-red-200 dark:border-red-700' }}">
                        <div class="flex items-center">
                            <i class="{{ $menuItem->is_available ? 'fas fa-check-circle text-green-600' : 'fas fa-times-circle text-red-600' }} mr-2"></i>
                            <span class="{{ $menuItem->is_available ? 'text-green-800 dark:text-green-200' : 'text-red-800 dark:text-red-200' }} font-semibold">
                                {{ $menuItem->is_available ? __('Available Now') : __('Currently Unavailable') }}
                            </span>
                        </div>
                    </div>

                    <!-- Add to Cart Section -->
                    @if($menuItem->is_available)
                        <div class="space-y-6 border-t pt-6">
                            <div class="space-y-4">
                                <!-- Quantity Selector -->
                                <div class="flex items-center space-x-4">
                                    <label class="text-sm font-medium text-gray-700 dark:text-gray-300">{{ __('Quantity') }}:</label>
                                    <div class="quantity-selector flex items-center border border-gray-300 dark:border-gray-600 rounded-lg">
                                        <button type="button" onclick="decreaseQuantity()"
                                                class="px-3 py-2 text-gray-600 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-200">
                                            <i class="fas fa-minus"></i>
                                        </button>
                                        <span id="quantity-display" class="px-4 py-2 border-x border-gray-300 dark:border-gray-600 text-center min-w-[3rem]">1</span>
                                        <button type="button" onclick="increaseQuantity()"
                                                class="px-3 py-2 text-gray-600 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-200">
                                            <i class="fas fa-plus"></i>
                                        </button>
                                    </div>
                                </div>

                                <!-- Special Instructions -->
                                <div>
                                    <label for="special-instructions" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                                        {{ __('Special Instructions') }} ({{ __('Optional') }})
                                    </label>
                                    <textarea id="special-instructions"
                                              rows="3"
                                              class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:text-white"
                                              placeholder="{{ __('Any special requests or modifications...') }}"></textarea>
                                </div>

                                <!-- Add to Cart Button -->
                                <button onclick="addToCart()"
                                        class="w-full bg-primary-600 text-white py-3 px-6 rounded-lg font-semibold hover:bg-primary-700 transition-colors">
                                    <i class="fas fa-shopping-cart mr-2"></i>
                                    {{ __('Add to Cart') }} - ৳<span id="total-price">{{ number_format($menuItem->price, 2) }}</span>
                                </button>
                            </div>
                        </div>
                    @endif

                    <!-- Alternative Actions -->
                    <div class="space-y-4 border-t pt-6">
                        <div class="flex flex-col sm:flex-row gap-4">
                            @if(isset($siteSettings['contact']['phone']))
                                <a href="tel:{{ $siteSettings['contact']['phone'] }}"
                                   class="flex-1 border border-primary-600 text-primary-600 text-center px-6 py-3 rounded-lg font-semibold hover:bg-primary-50 dark:hover:bg-primary-900 transition-colors">
                                    <i class="fas fa-phone mr-2"></i>
                                    {{ __('Call to Order') }}
                                </a>
                            @endif
                            <a href="{{ route('guest.menu') }}"
                               class="flex-1 border border-gray-300 dark:border-gray-600 text-gray-600 dark:text-gray-400 text-center px-6 py-3 rounded-lg font-semibold hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors">
                                {{ __('Back to Menu') }}
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Related Items -->
    @if($relatedItems && $relatedItems->count() > 0)
        <section class="py-12 bg-gray-50 dark:bg-gray-800">
            <div class="container mx-auto px-4">
                <h2 class="text-2xl font-bold text-center mb-8 text-gray-900 dark:text-white">{{ __('You Might Also Like') }}</h2>
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                    @foreach($relatedItems as $item)
                        <div class="bg-white dark:bg-gray-700 rounded-lg shadow-md overflow-hidden hover:shadow-lg transition-shadow">
                            <div class="h-32 bg-gray-200 dark:bg-gray-600 relative">
                                @if($item->primary_image_url)
                                    <img src="{{ $item->primary_image_url }}"
                                         alt="{{ $item->name }}"
                                         class="w-full h-full object-cover"
                                         onerror="this.src='data:image/svg+xml;base64,{{ base64_encode('<svg xmlns=\"http://www.w3.org/2000/svg\" width=\"200\" height=\"200\" viewBox=\"0 0 200 200\"><rect width=\"200\" height=\"200\" fill=\"#f3f4f6\"/><circle cx=\"80\" cy=\"70\" r=\"12\" fill=\"#d1d5db\"/><polygon points=\"40,140 80,100 120,120 160,80 180,140\" fill=\"#d1d5db\"/><text x=\"100\" y=\"170\" text-anchor=\"middle\" fill=\"#9ca3af\" font-size=\"16\">Menu Item</text></svg>') }}'">
                                @else
                                    <div class="flex items-center justify-center h-full text-gray-400">
                                        <i class="fas fa-utensils text-2xl"></i>
                                    </div>
                                @endif
                                <div class="absolute top-2 right-2">
                                    <span class="bg-primary-600 text-white px-2 py-1 rounded text-xs font-semibold">
                                        ৳{{ number_format($item->price, 2) }}
                                    </span>
                                </div>
                            </div>
                            <div class="p-4">
                                <h3 class="font-semibold mb-2 text-gray-900 dark:text-white">{{ $item->name }}</h3>
                                <a href="{{ route('guest.menu.item', $item->slug) }}"
                                   class="text-primary-600 hover:text-primary-700 text-sm font-semibold">
                                    {{ __('View Details') }} →
                                </a>
                            </div>
                        </div>
                    @endforeach
                </div>
            </div>
        </section>
    @endif

    <!-- Cart Notification -->
    <div id="cart-notification" class="cart-notification bg-green-500 text-white px-6 py-3 rounded-lg shadow-lg">
        <div class="flex items-center">
            <i class="fas fa-check-circle mr-2"></i>
            <span id="notification-message">{{ __('Item added to cart!') }}</span>
        </div>
    </div>
@endsection

@push('head')
<style>
    .variation-option:hover {
        transform: translateY(-1px);
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    }

    .addon-option:hover {
        transform: translateY(-1px);
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    }

    .variation-option.selected,
    .addon-option.selected {
        border-color: #ea580c !important;
        background-color: #fff7ed !important;
    }

    .dark .variation-option.selected,
    .dark .addon-option.selected {
        background-color: #7c2d12 !important;
    }

    .cart-notification {
        position: fixed;
        top: 20px;
        right: 20px;
        z-index: 1000;
        transform: translateX(100%);
        transition: transform 0.3s ease;
    }

    .cart-notification.show {
        transform: translateX(0);
    }
</style>
@endpush

@push('scripts')
<script>
    let currentQuantity = 1;
    const basePrice = {{ $menuItem->price }};
    let selectedVariation = null;
    let selectedAddons = [];
    let variationPriceModifier = 0;
    let addonsTotalPrice = 0;

    // Quantity management
    function increaseQuantity() {
        if (currentQuantity < 99) {
            currentQuantity++;
            updateQuantityDisplay();
        }
    }

    function decreaseQuantity() {
        if (currentQuantity > 1) {
            currentQuantity--;
            updateQuantityDisplay();
        }
    }

    function updateQuantityDisplay() {
        const totalPrice = (basePrice + variationPriceModifier + addonsTotalPrice) * currentQuantity;
        document.getElementById('quantity-display').textContent = currentQuantity;
        document.getElementById('total-price').textContent = totalPrice.toFixed(2);
    }

    // Variation selection
    function selectVariation(variationId, priceModifier) {
        selectedVariation = variationId;
        variationPriceModifier = priceModifier;

        // Update visual selection
        document.querySelectorAll('.variation-option').forEach(option => {
            option.classList.remove('border-primary-500', 'bg-primary-50', 'dark:bg-primary-900');
        });

        const selectedOption = document.querySelector(`[data-variation-id="${variationId}"]`);
        if (selectedOption) {
            selectedOption.classList.add('border-primary-500', 'bg-primary-50', 'dark:bg-primary-900');
        }

        updateQuantityDisplay();
    }

    // Addon selection
    function toggleAddon(addonId, addonPrice) {
        const checkbox = document.querySelector(`input[value="${addonId}"]`);
        const addonOption = document.querySelector(`[data-addon-id="${addonId}"]`);

        if (checkbox.checked) {
            selectedAddons.push({id: addonId, price: addonPrice});
            addonOption.classList.add('border-primary-500', 'bg-primary-50', 'dark:bg-primary-900');
        } else {
            selectedAddons = selectedAddons.filter(addon => addon.id !== addonId);
            addonOption.classList.remove('border-primary-500', 'bg-primary-50', 'dark:bg-primary-900');
        }

        addonsTotalPrice = selectedAddons.reduce((total, addon) => total + addon.price, 0);
        updateQuantityDisplay();
    }

    // Image gallery
    function changeMainImage(imageUrl) {
        const mainImage = document.querySelector('.image-slider img');
        if (mainImage) {
            mainImage.src = imageUrl;
        }
    }

    // Add to cart functionality
    function addToCart() {
        const specialInstructions = document.getElementById('special-instructions').value;

        // Build variations object
        const variations = {};
        if (selectedVariation) {
            variations[selectedVariation] = variationPriceModifier;
        }

        // Build addons array
        const addons = selectedAddons.map(addon => addon.id);

        // Calculate final price including variations and addons
        const finalPrice = basePrice + variationPriceModifier + addonsTotalPrice;

        const cartItem = {
            menuItemId: {{ $menuItem->id }},
            name: '{{ addslashes($menuItem->name) }}',
            image: '{{ $menuItem->primary_image_url ?? '' }}',
            basePrice: finalPrice,
            totalPrice: finalPrice * currentQuantity,
            quantity: currentQuantity,
            variations: variations,
            addons: addons,
            specialInstructions: specialInstructions
        };

        // Try to use existing cart function if available
        if (typeof addMenuItemToCart === 'function') {
            addMenuItemToCart(
                cartItem.menuItemId,
                cartItem.name,
                cartItem.basePrice,
                cartItem.image,
                cartItem.quantity,
                cartItem.variations,
                cartItem.addons,
                cartItem.specialInstructions
            );
        } else {
            // Fallback: add to localStorage cart
            addToLocalStorageCart(cartItem);
        }

        // Show notification
        showCartNotification('{{ __("Added to cart successfully!") }}');

        // Reset form
        resetForm();
    }

    // Reset form function
    function resetForm() {
        currentQuantity = 1;
        selectedVariation = null;
        selectedAddons = [];
        variationPriceModifier = 0;
        addonsTotalPrice = 0;

        // Reset UI
        document.querySelectorAll('.variation-option').forEach(option => {
            option.classList.remove('border-primary-500', 'bg-primary-50', 'dark:bg-primary-900');
        });
        document.querySelectorAll('.addon-option').forEach(option => {
            option.classList.remove('border-primary-500', 'bg-primary-50', 'dark:bg-primary-900');
        });
        document.querySelectorAll('input[name="variation"]').forEach(radio => {
            radio.checked = false;
        });
        document.querySelectorAll('input[name="addons[]"]').forEach(checkbox => {
            checkbox.checked = false;
        });

        updateQuantityDisplay();
        document.getElementById('special-instructions').value = '';
    }

    // Fallback cart functionality
    function addToLocalStorageCart(item) {
        try {
            let cart = JSON.parse(localStorage.getItem('restaurant_cart') || '[]');

            // Generate unique ID for cart item
            const itemId = `${item.menuItemId}_${JSON.stringify(item.variations)}_${JSON.stringify(item.addons)}_${item.specialInstructions}`;

            // Check if item already exists
            const existingItemIndex = cart.findIndex(cartItem => cartItem.id === itemId);

            if (existingItemIndex > -1) {
                // Update existing item
                cart[existingItemIndex].quantity += item.quantity;
                cart[existingItemIndex].totalPrice = cart[existingItemIndex].quantity * item.basePrice;
            } else {
                // Add new item
                cart.push({
                    id: itemId,
                    ...item,
                    addedAt: new Date().toISOString()
                });
            }

            localStorage.setItem('restaurant_cart', JSON.stringify(cart));

            // Update cart count if function exists
            if (typeof updateCartCount === 'function') {
                updateCartCount();
            }
        } catch (error) {
            console.error('Error adding to cart:', error);
        }
    }

    // Show cart notification
    function showCartNotification(message) {
        const notification = document.getElementById('cart-notification');
        const messageElement = document.getElementById('notification-message');

        messageElement.textContent = message;
        notification.classList.add('show');

        setTimeout(() => {
            notification.classList.remove('show');
        }, 3000);
    }

    // Initialize
    document.addEventListener('DOMContentLoaded', function() {
        updateQuantityDisplay();
    });
</script>
@endpush
