@extends('layouts.tenant')

@section('title', $seoData['title'])
@section('description', $seoData['description'])
@section('og_title', $seoData['title'])
@section('og_description', $seoData['description'])
@section('og_type', 'website')
@section('canonical', $seoData['url'])

@push('head')
    <!-- JSON-LD Structured Data -->
    <script type="application/ld+json">
        {!! json_encode($jsonLd, JSON_UNESCAPED_SLASHES | JSON_UNESCAPED_UNICODE) !!}
    </script>

    <!-- Custom Styles -->
    <style>
        .line-clamp-2 {
            display: -webkit-box;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
            overflow: hidden;
        }
    </style>
@endpush

@section('content')
    <!-- Header -->
    <section class="bg-gray-50 dark:bg-gray-800 py-12">
        <div class="container mx-auto px-4">
            <h1 class="text-4xl font-bold text-center mb-4 text-gray-900 dark:text-white">{{ __('Our Menu') }}</h1>
            <p class="text-xl text-gray-600 dark:text-gray-400 text-center">
                {{ __('Discover our delicious selection of dishes') }}
            </p>
        </div>
    </section>

    <!-- Menu Categories -->
    <section class="py-12">
        <div class="container mx-auto px-4">
            @if($categories->count() === 0)
                <div class="text-center py-12">
                    <div class="text-gray-400 dark:text-gray-600 text-6xl mb-4">
                        <i class="fas fa-utensils"></i>
                    </div>
                    <h3 class="text-2xl font-semibold text-gray-600 dark:text-gray-400 mb-2">{{ __('Menu Coming Soon') }}</h3>
                    <p class="text-gray-500 dark:text-gray-500">{{ __('We\'re working on our menu. Please check back later!') }}</p>
                </div>
            @else
                <!-- Category Navigation -->
                <div class="mb-8 overflow-x-auto">
                    <div class="flex space-x-4 pb-4">
                        @foreach($categories as $category)
                        <a href="#category-{{ $category->id }}" 
                           class="flex-shrink-0 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg px-6 py-3 text-gray-700 dark:text-gray-300 hover:bg-primary-50 dark:hover:bg-primary-900 hover:border-primary-200 dark:hover:border-primary-700 hover:text-primary-600 dark:hover:text-primary-400 transition-colors">
                            {{ $category->name }}
                        </a>
                        @endforeach
                    </div>
                </div>

                <!-- Menu Items by Category -->
                @foreach($categories as $category)
                    @if($category->activeMenuItems->count() > 0)
                    <div id="category-{{ $category->id }}" class="mb-16">
                        <div class="text-center mb-8">
                            <h2 class="text-3xl font-bold text-gray-900 dark:text-white mb-2">{{ $category->name }}</h2>
                            @if($category->description)
                            <p class="text-lg text-gray-600 dark:text-gray-400">{{ $category->description }}</p>
                            @endif
                        </div>

                        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                            @foreach($category->activeMenuItems as $item)
                            <div class="bg-white dark:bg-gray-800 rounded-lg shadow-lg overflow-hidden hover:shadow-xl transition-shadow">
                                <div class="relative h-48 bg-gray-200 dark:bg-gray-700">
                                    <a href="{{ route('guest.menu.item', $item->slug) }}" class="block w-full h-full">
                                        @if($item->primary_image_url)
                                            <img src="{{ $item->primary_image_url }}"
                                                 alt="{{ $item->name }}"
                                                 class="w-full h-full object-cover hover:scale-105 transition-transform duration-200">
                                        @else
                                            <div class="w-full h-full flex items-center justify-center hover:bg-gray-300 dark:hover:bg-gray-600 transition-colors">
                                                <i class="fas fa-utensils text-4xl text-gray-400"></i>
                                            </div>
                                        @endif
                                    </a>
                                    
                                    <!-- Price Badge -->
                                    <div class="absolute top-4 right-4">
                                        <span class="bg-primary-600 text-white px-3 py-1 rounded-full text-sm font-semibold">
                                            ৳{{ number_format($item->price, 2) }}
                                        </span>
                                    </div>

                                    <!-- Availability Badge -->
                                    @if(!$item->is_available)
                                    <div class="absolute top-4 left-4">
                                        <span class="bg-red-500 text-white px-3 py-1 rounded-full text-xs font-semibold">
                                            {{ __('Out of Stock') }}
                                        </span>
                                    </div>
                                    @endif
                                </div>

                                <div class="p-6">
                                    <!-- Clickable title that links to detail page -->
                                    <a href="{{ route('guest.menu.item', $item->slug) }}" class="block hover:text-primary-600 transition-colors">
                                        <h3 class="text-xl font-semibold text-gray-900 dark:text-white mb-2">
                                            {{ $item->name }}
                                        </h3>
                                    </a>

                                    @if($item->description)
                                    <p class="text-gray-600 dark:text-gray-400 mb-4 line-clamp-2">
                                        {{ $item->description }}
                                    </p>
                                    @endif

                                    <!-- Dietary Information -->
                                    @if($item->is_vegetarian || $item->is_vegan || $item->is_gluten_free)
                                    <div class="flex space-x-2 mb-4">
                                        @if($item->is_vegetarian)
                                        <span class="bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200 text-xs px-2 py-1 rounded-full">
                                            {{ __('Vegetarian') }}
                                        </span>
                                        @endif
                                        @if($item->is_vegan)
                                        <span class="bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200 text-xs px-2 py-1 rounded-full">
                                            {{ __('Vegan') }}
                                        </span>
                                        @endif
                                        @if($item->is_gluten_free)
                                        <span class="bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200 text-xs px-2 py-1 rounded-full">
                                            {{ __('Gluten Free') }}
                                        </span>
                                        @endif
                                    </div>
                                    @endif

                                    <!-- Action Buttons -->
                                    @if($item->is_available)
                                        @if($item->variations && $item->variations->count() > 0)
                                            <!-- Item has variations - show options button and quick add -->
                                            <div class="space-y-2">
                                                <a href="{{ route('guest.menu.item', $item->slug) }}"
                                                   class="w-full bg-gray-100 dark:bg-gray-700 text-gray-800 dark:text-gray-200 py-2 px-4 rounded-lg hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors text-center block">
                                                    <i class="fas fa-cog mr-2"></i>
                                                    {{ __('Choose Options') }}
                                                </a>
                                                <button onclick="quickAddToCart({{ $item->id }}, '{{ addslashes($item->name) }}', {{ $item->price }}, '{{ $item->primary_image_url ?? '' }}')"
                                                        class="w-full bg-primary-600 text-white py-2 px-4 rounded-lg hover:bg-primary-700 transition-colors">
                                                    <i class="fas fa-plus mr-2"></i>
                                                    {{ __('Quick Add') }}
                                                </button>
                                            </div>
                                        @else
                                            <!-- Simple item - show quantity selector and add to cart -->
                                            <div class="space-y-3">
                                                <!-- Quantity Selector -->
                                                <div class="flex items-center justify-center space-x-3">
                                                    <button onclick="decreaseQuantity({{ $item->id }})"
                                                            class="w-8 h-8 rounded-full border border-gray-300 dark:border-gray-600 flex items-center justify-center hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors">
                                                        <i class="fas fa-minus text-sm"></i>
                                                    </button>
                                                    <span id="quantity-{{ $item->id }}" class="w-8 text-center font-medium text-gray-900 dark:text-white">1</span>
                                                    <button onclick="increaseQuantity({{ $item->id }})"
                                                            class="w-8 h-8 rounded-full border border-gray-300 dark:border-gray-600 flex items-center justify-center hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors">
                                                        <i class="fas fa-plus text-sm"></i>
                                                    </button>
                                                </div>

                                                <!-- Add to Cart Button -->
                                                <button onclick="addToCart({{ $item->id }}, '{{ addslashes($item->name) }}', {{ $item->price }}, '{{ $item->primary_image_url ?? '' }}')"
                                                        class="w-full bg-primary-600 text-white py-2 px-4 rounded-lg hover:bg-primary-700 transition-colors">
                                                    <i class="fas fa-shopping-cart mr-2"></i>
                                                    {{ __('Add to Cart') }}
                                                </button>

                                                <!-- View Details Link -->
                                                <a href="{{ route('guest.menu.item', $item->slug) }}"
                                                   class="w-full text-center text-primary-600 dark:text-primary-400 hover:text-primary-700 dark:hover:text-primary-300 text-sm transition-colors block">
                                                    {{ __('View Details') }}
                                                </a>
                                            </div>
                                        @endif
                                    @else
                                        <button disabled
                                                class="w-full bg-gray-300 dark:bg-gray-600 text-gray-500 dark:text-gray-400 py-2 px-4 rounded-lg cursor-not-allowed">
                                            {{ __('Out of Stock') }}
                                        </button>
                                    @endif
                                </div>
                            </div>
                            @endforeach
                        </div>
                    </div>
                    @endif
                @endforeach
            @endif
        </div>
    </section>

    <!-- Call to Action -->
    <section class="py-16 bg-primary-600 text-white">
        <div class="container mx-auto px-4 text-center">
            <h2 class="text-3xl font-bold mb-4">{{ __('Ready to Order?') }}</h2>
            <p class="text-xl mb-8 opacity-90">
                {{ __('Call us to place your order or visit our restaurant') }}
            </p>
            <div class="space-x-4">
                @if(isset($siteSettings['contact']['phone']))
                <a href="tel:{{ $siteSettings['contact']['phone'] }}" 
                   class="inline-block bg-white text-primary-600 px-8 py-3 rounded-lg font-semibold hover:bg-gray-100 transition-colors">
                    <i class="fas fa-phone mr-2"></i>
                    {{ __('Call') }} {{ $siteSettings['contact']['phone'] }}
                </a>
                @endif
                <a href="{{ route('guest.home') }}" 
                   class="inline-block border-2 border-white text-white px-8 py-3 rounded-lg font-semibold hover:bg-white hover:text-primary-600 transition-colors">
                    {{ __('Back to Home') }}
                </a>
            </div>
        </div>
    </section>
@endsection

@push('scripts')
<script>
    // Smooth scrolling for category navigation
    document.querySelectorAll('a[href^="#category-"]').forEach(anchor => {
        anchor.addEventListener('click', function (e) {
            e.preventDefault();
            const target = document.querySelector(this.getAttribute('href'));
            if (target) {
                target.scrollIntoView({
                    behavior: 'smooth',
                    block: 'start'
                });
            }
        });
    });

    // Highlight active category in navigation
    function highlightActiveCategory() {
        const categories = document.querySelectorAll('[id^="category-"]');
        const navLinks = document.querySelectorAll('a[href^="#category-"]');

        let activeCategory = null;
        categories.forEach(category => {
            const rect = category.getBoundingClientRect();
            if (rect.top <= 100 && rect.bottom >= 100) {
                activeCategory = category;
            }
        });

        navLinks.forEach(link => {
            link.classList.remove('bg-primary-50', 'dark:bg-primary-900', 'border-primary-200', 'dark:border-primary-700', 'text-primary-600', 'dark:text-primary-400');
            link.classList.add('bg-white', 'dark:bg-gray-800', 'border-gray-200', 'dark:border-gray-700', 'text-gray-700', 'dark:text-gray-300');
        });

        if (activeCategory) {
            const activeLink = document.querySelector(`a[href="#${activeCategory.id}"]`);
            if (activeLink) {
                activeLink.classList.remove('bg-white', 'dark:bg-gray-800', 'border-gray-200', 'dark:border-gray-700', 'text-gray-700', 'dark:text-gray-300');
                activeLink.classList.add('bg-primary-50', 'dark:bg-primary-900', 'border-primary-200', 'dark:border-primary-700', 'text-primary-600', 'dark:text-primary-400');
            }
        }
    }

    // Update active category on scroll
    window.addEventListener('scroll', highlightActiveCategory);

    // Initial highlight
    highlightActiveCategory();

    // Quantity management functions
    function increaseQuantity(itemId) {
        const quantityElement = document.getElementById(`quantity-${itemId}`);
        let quantity = parseInt(quantityElement.textContent);
        if (quantity < 99) { // Max quantity limit
            quantity++;
            quantityElement.textContent = quantity;
        }
    }

    function decreaseQuantity(itemId) {
        const quantityElement = document.getElementById(`quantity-${itemId}`);
        let quantity = parseInt(quantityElement.textContent);
        if (quantity > 1) { // Min quantity limit
            quantity--;
            quantityElement.textContent = quantity;
        }
    }

    // Add to cart with quantity
    function addToCart(itemId, itemName, itemPrice, itemImage) {
        const quantityElement = document.getElementById(`quantity-${itemId}`);
        const quantity = parseInt(quantityElement.textContent);

        // Call the existing cart function with quantity
        if (typeof addMenuItemToCart === 'function') {
            addMenuItemToCart(itemId, itemName, itemPrice, itemImage, quantity);
        } else {
            // Fallback: show success message
            showCartMessage(`Added ${quantity}x ${itemName} to cart!`);
        }

        // Reset quantity to 1 after adding
        quantityElement.textContent = '1';
    }

    // Quick add to cart (for items with variations)
    function quickAddToCart(itemId, itemName, itemPrice, itemImage) {
        if (typeof addMenuItemToCart === 'function') {
            addMenuItemToCart(itemId, itemName, itemPrice, itemImage, 1);
        } else {
            // Fallback: show success message
            showCartMessage(`Added ${itemName} to cart!`);
        }
    }

    // Show cart message
    function showCartMessage(message) {
        // Create a temporary notification
        const notification = document.createElement('div');
        notification.className = 'fixed top-4 right-4 bg-green-500 text-white px-6 py-3 rounded-lg shadow-lg z-50 transform translate-x-full transition-transform duration-300';
        notification.textContent = message;
        document.body.appendChild(notification);

        // Animate in
        setTimeout(() => {
            notification.classList.remove('translate-x-full');
        }, 100);

        // Animate out and remove
        setTimeout(() => {
            notification.classList.add('translate-x-full');
            setTimeout(() => {
                document.body.removeChild(notification);
            }, 300);
        }, 3000);
    }
</script>
@endpush
