@extends('layouts.marketing')

@section('title', 'Sign Up - RestaurantPro')
@section('description', 'Create your restaurant account and start your 14-day free trial. No credit card required.')

@push('head')
<script>
    // Real-time slug generation and validation
    function generateSlug() {
        const name = document.getElementById('restaurant_name').value;
        if (!name) return;

        fetch('/generate-slug?name=' + encodeURIComponent(name))
            .then(response => response.json())
            .then(data => {
                document.getElementById('restaurant_slug').value = data.slug;
                document.getElementById('preview_url').textContent = data.url;
                checkSlugAvailability();
            });
    }

    function checkSlugAvailability() {
        const slug = document.getElementById('restaurant_slug').value;
        const feedback = document.getElementById('slug_feedback');
        
        if (!slug) {
            feedback.innerHTML = '';
            return;
        }

        fetch('/check-slug?slug=' + encodeURIComponent(slug))
            .then(response => response.json())
            .then(data => {
                if (data.available) {
                    feedback.innerHTML = '<span class="text-green-600 text-sm">✓ ' + data.message + '</span>';
                    document.getElementById('preview_url').textContent = data.url;
                } else {
                    feedback.innerHTML = '<span class="text-red-600 text-sm">✗ ' + data.message + '</span>';
                }
            });
    }
</script>
@endpush

@section('content')
<section class="py-12 bg-gray-50 dark:bg-gray-900 min-h-screen">
    <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center mb-8">
            <h1 class="text-3xl md:text-4xl font-bold text-gray-900 dark:text-white mb-4">
                Create Your Restaurant Account
            </h1>
            <p class="text-xl text-gray-600 dark:text-gray-400">
                Start your 14-day free trial. No credit card required.
            </p>
        </div>

        <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
            <!-- Signup Form -->
            <div class="lg:col-span-2">
                <div class="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-8">
                    <form method="POST" action="{{ route('marketing.signup.process') }}">
                        @csrf

                        <!-- Personal Information -->
                        <div class="mb-8">
                            <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">Personal Information</h3>
                            
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                <div>
                                    <label for="name" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Full Name</label>
                                    <input type="text" id="name" name="name" value="{{ old('name') }}" required 
                                           class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white">
                                    @error('name')
                                        <p class="text-red-600 text-sm mt-1">{{ $message }}</p>
                                    @enderror
                                </div>

                                <div>
                                    <label for="email" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Email Address</label>
                                    <input type="email" id="email" name="email" value="{{ old('email') }}" required 
                                           class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white">
                                    @error('email')
                                        <p class="text-red-600 text-sm mt-1">{{ $message }}</p>
                                    @enderror
                                </div>

                                <div>
                                    <label for="password" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Password</label>
                                    <input type="password" id="password" name="password" required 
                                           class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white">
                                    @error('password')
                                        <p class="text-red-600 text-sm mt-1">{{ $message }}</p>
                                    @enderror
                                </div>

                                <div>
                                    <label for="password_confirmation" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Confirm Password</label>
                                    <input type="password" id="password_confirmation" name="password_confirmation" required 
                                           class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white">
                                </div>
                            </div>
                        </div>

                        <!-- Restaurant Information -->
                        <div class="mb-8">
                            <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">Restaurant Information</h3>
                            
                            <div class="space-y-4">
                                <div>
                                    <label for="restaurant_name" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Restaurant Name</label>
                                    <input type="text" id="restaurant_name" name="restaurant_name" value="{{ old('restaurant_name') }}" required 
                                           onblur="generateSlug()"
                                           class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white">
                                    @error('restaurant_name')
                                        <p class="text-red-600 text-sm mt-1">{{ $message }}</p>
                                    @enderror
                                </div>

                                <div>
                                    <label for="restaurant_slug" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Restaurant URL</label>
                                    <div class="flex">
                                        <span class="inline-flex items-center px-3 rounded-l-lg border border-r-0 border-gray-300 dark:border-gray-600 bg-gray-50 dark:bg-gray-700 text-gray-500 dark:text-gray-400 text-sm">
                                            http://
                                        </span>
                                        <input type="text" id="restaurant_slug" name="restaurant_slug" value="{{ old('restaurant_slug') }}" required 
                                               onblur="checkSlugAvailability()" pattern="[a-z0-9-]+" 
                                               class="flex-1 px-3 py-2 border border-gray-300 dark:border-gray-600 focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white">
                                        <span class="inline-flex items-center px-3 rounded-r-lg border border-l-0 border-gray-300 dark:border-gray-600 bg-gray-50 dark:bg-gray-700 text-gray-500 dark:text-gray-400 text-sm">
                                            .localhost:8000
                                        </span>
                                    </div>
                                    <div id="slug_feedback" class="mt-1"></div>
                                    <p class="text-gray-500 dark:text-gray-400 text-sm mt-1">Your restaurant URL: <span id="preview_url" class="font-medium"></span></p>
                                    @error('restaurant_slug')
                                        <p class="text-red-600 text-sm mt-1">{{ $message }}</p>
                                    @enderror
                                </div>

                                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                    <div>
                                        <label for="phone" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Phone Number</label>
                                        <input type="tel" id="phone" name="phone" value="{{ old('phone') }}" 
                                               class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white">
                                        @error('phone')
                                            <p class="text-red-600 text-sm mt-1">{{ $message }}</p>
                                        @enderror
                                    </div>

                                    <div>
                                        <label for="address" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Address</label>
                                        <input type="text" id="address" name="address" value="{{ old('address') }}" 
                                               class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white">
                                        @error('address')
                                            <p class="text-red-600 text-sm mt-1">{{ $message }}</p>
                                        @enderror
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Plan Selection -->
                        <div class="mb-8">
                            <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">Choose Your Plan</h3>
                            
                            <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                                @foreach($plans as $planOption)
                                <label class="relative">
                                    <input type="radio" name="plan_id" value="{{ $planOption->id }}" 
                                           {{ ($plan && $plan->id === $planOption->id) || (!$plan && $planOption->slug === 'basic-plus') ? 'checked' : '' }}
                                           class="sr-only peer">
                                    <div class="border-2 border-gray-200 dark:border-gray-600 rounded-lg p-4 cursor-pointer peer-checked:border-blue-500 peer-checked:bg-blue-50 dark:peer-checked:bg-blue-900/20 hover:border-gray-300 dark:hover:border-gray-500 transition-colors">
                                        <div class="text-center">
                                            <h4 class="font-semibold text-gray-900 dark:text-white">{{ $planOption->name }}</h4>
                                            <div class="text-2xl font-bold text-blue-600 dark:text-blue-400 my-2">{{ $planOption->formatted_price }}</div>
                                            <p class="text-sm text-gray-600 dark:text-gray-400">{{ $planOption->description }}</p>
                                        </div>
                                    </div>
                                </label>
                                @endforeach
                            </div>
                            @error('plan_id')
                                <p class="text-red-600 text-sm mt-1">{{ $message }}</p>
                            @enderror
                        </div>

                        <!-- Terms and Conditions -->
                        <div class="mb-8">
                            <label class="flex items-start">
                                <input type="checkbox" name="terms" value="1" required 
                                       class="mt-1 h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                                <span class="ml-2 text-sm text-gray-600 dark:text-gray-400">
                                    I agree to the <a href="#" class="text-blue-600 dark:text-blue-400 hover:underline">Terms of Service</a> 
                                    and <a href="#" class="text-blue-600 dark:text-blue-400 hover:underline">Privacy Policy</a>
                                </span>
                            </label>
                            @error('terms')
                                <p class="text-red-600 text-sm mt-1">{{ $message }}</p>
                            @enderror
                        </div>

                        <!-- Submit Button -->
                        <div class="text-center">
                            <button type="submit" class="w-full bg-blue-600 hover:bg-blue-700 text-white px-8 py-4 rounded-lg font-semibold text-lg transition-colors">
                                Start My Free Trial
                            </button>
                        </div>

                        @if($errors->has('error'))
                            <div class="mt-4 p-4 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg">
                                <p class="text-red-600 dark:text-red-400">{{ $errors->first('error') }}</p>
                            </div>
                        @endif
                    </form>
                </div>
            </div>

            <!-- Plan Summary -->
            <div class="lg:col-span-1">
                <div class="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6 sticky top-8">
                    <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">What You Get</h3>
                    
                    <div class="space-y-3 text-sm">
                        <div class="flex items-center">
                            <svg class="w-4 h-4 text-green-500 mr-2" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"/>
                            </svg>
                            14-day free trial
                        </div>
                        <div class="flex items-center">
                            <svg class="w-4 h-4 text-green-500 mr-2" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"/>
                            </svg>
                            No setup fees
                        </div>
                        <div class="flex items-center">
                            <svg class="w-4 h-4 text-green-500 mr-2" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"/>
                            </svg>
                            Complete POS system
                        </div>
                        <div class="flex items-center">
                            <svg class="w-4 h-4 text-green-500 mr-2" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"/>
                            </svg>
                            Inventory management
                        </div>
                        <div class="flex items-center">
                            <svg class="w-4 h-4 text-green-500 mr-2" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"/>
                            </svg>
                            Customer management
                        </div>
                        <div class="flex items-center">
                            <svg class="w-4 h-4 text-green-500 mr-2" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"/>
                            </svg>
                            Sales reporting
                        </div>
                        <div class="flex items-center">
                            <svg class="w-4 h-4 text-green-500 mr-2" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"/>
                            </svg>
                            24/7 email support
                        </div>
                    </div>

                    <div class="mt-6 pt-6 border-t border-gray-200 dark:border-gray-700">
                        <p class="text-xs text-gray-500 dark:text-gray-400">
                            No credit card required for trial. Cancel anytime during the trial period.
                        </p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>
@endsection
