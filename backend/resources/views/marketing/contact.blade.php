@extends('layouts.marketing')

@section('title', 'Contact Us - RestaurantPro')
@section('description', 'Get in touch with our team for support, sales inquiries, or to schedule a demo of RestaurantPro restaurant management system.')

@section('content')
<!-- Hero Section -->
<section class="bg-gradient-to-br from-blue-600 via-blue-700 to-indigo-800 text-white py-20">
    <div class="max-w-4xl mx-auto text-center px-4 sm:px-6 lg:px-8">
        <h1 class="text-4xl md:text-5xl font-bold mb-6">
            Get in Touch
        </h1>
        <p class="text-xl text-blue-100 mb-8">
            Have questions? We're here to help. Contact our team for support, sales inquiries, or to schedule a demo.
        </p>
    </div>
</section>

<!-- Contact Section -->
<section class="py-20 bg-gray-50 dark:bg-gray-900">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-16">
            <!-- Contact Form -->
            <div>
                <div class="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-8">
                    <h2 class="text-2xl font-bold text-gray-900 dark:text-white mb-6">Send us a Message</h2>
                    
                    @if(session('success'))
                        <div class="mb-6 p-4 bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg">
                            <p class="text-green-600 dark:text-green-400">{{ session('success') }}</p>
                        </div>
                    @endif

                    <form method="POST" action="{{ route('marketing.contact.submit') }}">
                        @csrf
                        
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                            <div>
                                <label for="name" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Full Name</label>
                                <input type="text" id="name" name="name" value="{{ old('name') }}" required 
                                       class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white">
                                @error('name')
                                    <p class="text-red-600 text-sm mt-1">{{ $message }}</p>
                                @enderror
                            </div>

                            <div>
                                <label for="email" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Email Address</label>
                                <input type="email" id="email" name="email" value="{{ old('email') }}" required 
                                       class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white">
                                @error('email')
                                    <p class="text-red-600 text-sm mt-1">{{ $message }}</p>
                                @enderror
                            </div>
                        </div>

                        <div class="mb-4">
                            <label for="phone" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Phone Number (Optional)</label>
                            <input type="tel" id="phone" name="phone" value="{{ old('phone') }}" 
                                   class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white">
                            @error('phone')
                                <p class="text-red-600 text-sm mt-1">{{ $message }}</p>
                            @enderror
                        </div>

                        <div class="mb-4">
                            <label for="subject" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Subject</label>
                            <select id="subject" name="subject" required 
                                    class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white">
                                <option value="">Select a subject</option>
                                <option value="Sales Inquiry" {{ old('subject') === 'Sales Inquiry' ? 'selected' : '' }}>Sales Inquiry</option>
                                <option value="Technical Support" {{ old('subject') === 'Technical Support' ? 'selected' : '' }}>Technical Support</option>
                                <option value="Demo Request" {{ old('subject') === 'Demo Request' ? 'selected' : '' }}>Demo Request</option>
                                <option value="Partnership" {{ old('subject') === 'Partnership' ? 'selected' : '' }}>Partnership</option>
                                <option value="Billing Question" {{ old('subject') === 'Billing Question' ? 'selected' : '' }}>Billing Question</option>
                                <option value="Feature Request" {{ old('subject') === 'Feature Request' ? 'selected' : '' }}>Feature Request</option>
                                <option value="Other" {{ old('subject') === 'Other' ? 'selected' : '' }}>Other</option>
                            </select>
                            @error('subject')
                                <p class="text-red-600 text-sm mt-1">{{ $message }}</p>
                            @enderror
                        </div>

                        <div class="mb-6">
                            <label for="message" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Message</label>
                            <textarea id="message" name="message" rows="6" required 
                                      class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white"
                                      placeholder="Tell us how we can help you...">{{ old('message') }}</textarea>
                            @error('message')
                                <p class="text-red-600 text-sm mt-1">{{ $message }}</p>
                            @enderror
                        </div>

                        <button type="submit" class="w-full bg-blue-600 hover:bg-blue-700 text-white px-6 py-3 rounded-lg font-semibold transition-colors">
                            Send Message
                        </button>
                    </form>
                </div>
            </div>

            <!-- Contact Information -->
            <div class="space-y-8">
                <!-- Contact Details -->
                <div class="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-8">
                    <h3 class="text-xl font-bold text-gray-900 dark:text-white mb-6">Contact Information</h3>
                    
                    <div class="space-y-4">
                        <div class="flex items-start">
                            <svg class="w-6 h-6 text-blue-600 dark:text-blue-400 mt-1 mr-3" fill="currentColor" viewBox="0 0 20 20">
                                <path d="M2.003 5.884L10 9.882l7.997-3.998A2 2 0 0016 4H4a2 2 0 00-1.997 1.884z"/>
                                <path d="M18 8.118l-8 4-8-4V14a2 2 0 002 2h12a2 2 0 002-2V8.118z"/>
                            </svg>
                            <div>
                                <h4 class="font-semibold text-gray-900 dark:text-white">Email</h4>
                                <p class="text-gray-600 dark:text-gray-400"><EMAIL></p>
                                <p class="text-gray-600 dark:text-gray-400"><EMAIL></p>
                            </div>
                        </div>

                        <div class="flex items-start">
                            <svg class="w-6 h-6 text-blue-600 dark:text-blue-400 mt-1 mr-3" fill="currentColor" viewBox="0 0 20 20">
                                <path d="M2 3a1 1 0 011-1h2.153a1 1 0 01.986.836l.74 4.435a1 1 0 01-.54 1.06l-1.548.773a11.037 11.037 0 006.105 6.105l.774-1.548a1 1 0 011.059-.54l4.435.74a1 1 0 01.836.986V17a1 1 0 01-1 1h-2C7.82 18 2 12.18 2 5V3z"/>
                            </svg>
                            <div>
                                <h4 class="font-semibold text-gray-900 dark:text-white">Phone</h4>
                                <p class="text-gray-600 dark:text-gray-400">+880 1234-567890</p>
                                <p class="text-gray-600 dark:text-gray-400">+880 1234-567891 (Sales)</p>
                            </div>
                        </div>

                        <div class="flex items-start">
                            <svg class="w-6 h-6 text-blue-600 dark:text-blue-400 mt-1 mr-3" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M5.05 4.05a7 7 0 119.9 9.9L10 18.9l-4.95-4.95a7 7 0 010-9.9zM10 11a2 2 0 100-4 2 2 0 000 4z" clip-rule="evenodd"/>
                            </svg>
                            <div>
                                <h4 class="font-semibold text-gray-900 dark:text-white">Address</h4>
                                <p class="text-gray-600 dark:text-gray-400">
                                    123 Tech Street<br>
                                    Dhaka 1000, Bangladesh
                                </p>
                            </div>
                        </div>

                        <div class="flex items-start">
                            <svg class="w-6 h-6 text-blue-600 dark:text-blue-400 mt-1 mr-3" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z" clip-rule="evenodd"/>
                            </svg>
                            <div>
                                <h4 class="font-semibold text-gray-900 dark:text-white">Business Hours</h4>
                                <p class="text-gray-600 dark:text-gray-400">
                                    Monday - Friday: 9:00 AM - 6:00 PM<br>
                                    Saturday: 10:00 AM - 4:00 PM<br>
                                    Sunday: Closed
                                </p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Support Options -->
                <div class="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-8">
                    <h3 class="text-xl font-bold text-gray-900 dark:text-white mb-6">Support Options</h3>
                    
                    <div class="space-y-4">
                        <div class="border border-gray-200 dark:border-gray-700 rounded-lg p-4">
                            <h4 class="font-semibold text-gray-900 dark:text-white mb-2">📚 Help Center</h4>
                            <p class="text-gray-600 dark:text-gray-400 text-sm mb-3">
                                Find answers to common questions and browse our documentation.
                            </p>
                            <a href="#" class="text-blue-600 dark:text-blue-400 hover:underline text-sm font-medium">
                                Visit Help Center →
                            </a>
                        </div>

                        <div class="border border-gray-200 dark:border-gray-700 rounded-lg p-4">
                            <h4 class="font-semibold text-gray-900 dark:text-white mb-2">💬 Live Chat</h4>
                            <p class="text-gray-600 dark:text-gray-400 text-sm mb-3">
                                Chat with our support team in real-time during business hours.
                            </p>
                            <button class="text-blue-600 dark:text-blue-400 hover:underline text-sm font-medium">
                                Start Live Chat →
                            </button>
                        </div>

                        <div class="border border-gray-200 dark:border-gray-700 rounded-lg p-4">
                            <h4 class="font-semibold text-gray-900 dark:text-white mb-2">📞 Schedule a Call</h4>
                            <p class="text-gray-600 dark:text-gray-400 text-sm mb-3">
                                Book a call with our sales team to discuss your needs.
                            </p>
                            <a href="#" class="text-blue-600 dark:text-blue-400 hover:underline text-sm font-medium">
                                Schedule Call →
                            </a>
                        </div>
                    </div>
                </div>

                <!-- Quick Links -->
                <div class="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-8">
                    <h3 class="text-xl font-bold text-gray-900 dark:text-white mb-6">Quick Links</h3>
                    
                    <div class="space-y-3">
                        <a href="{{ route('marketing.signup') }}" class="block text-blue-600 dark:text-blue-400 hover:underline">
                            Start Free Trial
                        </a>
                        <a href="{{ route('marketing.plans') }}" class="block text-blue-600 dark:text-blue-400 hover:underline">
                            View Pricing Plans
                        </a>
                        <a href="{{ route('marketing.features') }}" class="block text-blue-600 dark:text-blue-400 hover:underline">
                            Explore Features
                        </a>
                        <a href="#" class="block text-blue-600 dark:text-blue-400 hover:underline">
                            System Status
                        </a>
                        <a href="#" class="block text-blue-600 dark:text-blue-400 hover:underline">
                            API Documentation
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- FAQ Section -->
<section class="py-20 bg-white dark:bg-gray-800">
    <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center mb-12">
            <h2 class="text-3xl font-bold text-gray-900 dark:text-white mb-4">Frequently Asked Questions</h2>
            <p class="text-xl text-gray-600 dark:text-gray-400">Quick answers to common questions</p>
        </div>

        <div class="space-y-6">
            <div class="bg-gray-50 dark:bg-gray-700 rounded-lg p-6">
                <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-2">How quickly can I get started?</h3>
                <p class="text-gray-600 dark:text-gray-400">You can sign up and start using RestaurantPro immediately. The setup process takes less than 10 minutes, and our onboarding team will help you get everything configured.</p>
            </div>

            <div class="bg-gray-50 dark:bg-gray-700 rounded-lg p-6">
                <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-2">Do you offer training and support?</h3>
                <p class="text-gray-600 dark:text-gray-400">Yes! We provide comprehensive training for you and your staff, along with 24/7 email support. Professional plan customers get priority phone support.</p>
            </div>

            <div class="bg-gray-50 dark:bg-gray-700 rounded-lg p-6">
                <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-2">Can I migrate my existing data?</h3>
                <p class="text-gray-600 dark:text-gray-400">Absolutely! Our team can help you migrate your existing menu items, customer data, and other information from your current system.</p>
            </div>

            <div class="bg-gray-50 dark:bg-gray-700 rounded-lg p-6">
                <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-2">Is my data secure?</h3>
                <p class="text-gray-600 dark:text-gray-400">Security is our top priority. We use enterprise-grade encryption, regular backups, and comply with industry security standards to keep your data safe.</p>
            </div>
        </div>
    </div>
</section>

<!-- CTA Section -->
<section class="py-20 bg-gradient-to-r from-blue-600 to-indigo-700 text-white">
    <div class="max-w-4xl mx-auto text-center px-4 sm:px-6 lg:px-8">
        <h2 class="text-3xl md:text-4xl font-bold mb-4">
            Ready to Transform Your Restaurant?
        </h2>
        <p class="text-xl text-blue-100 mb-8">
            Don't wait - start your free trial today and see the difference RestaurantPro can make.
        </p>
        <a href="{{ route('marketing.signup') }}" class="bg-white text-blue-600 px-8 py-4 rounded-lg font-semibold text-lg hover:bg-blue-50 transition-colors">
            Start Free Trial
        </a>
    </div>
</section>
@endsection
