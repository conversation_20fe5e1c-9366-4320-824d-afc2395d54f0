@extends('layouts.marketing')

@section('title', 'Pricing Plans - RestaurantPro')
@section('description', 'Choose the perfect plan for your restaurant. Start with Basic at ৳1,000/month or go Professional at ৳5,000/month. 14-day free trial included.')

@section('content')
<!-- Hero Section -->
<section class="bg-gradient-to-br from-blue-600 via-blue-700 to-indigo-800 text-white py-20">
    <div class="max-w-4xl mx-auto text-center px-4 sm:px-6 lg:px-8">
        <h1 class="text-4xl md:text-5xl font-bold mb-6">
            Simple, Transparent Pricing
        </h1>
        <p class="text-xl text-blue-100 mb-8">
            Choose the plan that fits your restaurant's needs. All plans include a 14-day free trial with no setup fees.
        </p>
        <div class="flex items-center justify-center space-x-4 text-blue-200">
            <div class="flex items-center">
                <svg class="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"/>
                </svg>
                14-day free trial
            </div>
            <div class="flex items-center">
                <svg class="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"/>
                </svg>
                No setup fees
            </div>
            <div class="flex items-center">
                <svg class="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"/>
                </svg>
                Cancel anytime
            </div>
        </div>
    </div>
</section>

<!-- Pricing Plans -->
<section class="py-20 bg-gray-50 dark:bg-gray-900">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
            @foreach($plans as $plan)
            <div class="bg-white dark:bg-gray-800 rounded-lg shadow-lg overflow-hidden {{ $plan->slug === 'basic-plus' ? 'ring-2 ring-blue-500 transform scale-105' : '' }}">
                @if($plan->slug === 'basic-plus')
                <div class="bg-blue-500 text-white text-center py-2 px-4">
                    <span class="font-medium">Most Popular</span>
                </div>
                @endif
                
                <div class="p-8">
                    <div class="text-center mb-8">
                        <h3 class="text-2xl font-bold text-gray-900 dark:text-white mb-2">{{ $plan->name }}</h3>
                        <p class="text-gray-600 dark:text-gray-400 mb-4">{{ $plan->description }}</p>
                        <div class="text-4xl font-bold text-gray-900 dark:text-white mb-2">{{ $plan->formatted_price }}</div>
                        <p class="text-sm text-gray-500 dark:text-gray-400">{{ $plan->trial_days }}-day free trial</p>
                    </div>

                    <!-- Features List -->
                    <div class="space-y-4 mb-8">
                        @foreach($plan->features as $feature)
                        <div class="flex items-start">
                            <svg class="flex-shrink-0 w-5 h-5 text-green-500 mt-0.5 mr-3" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"/>
                            </svg>
                            <span class="text-gray-700 dark:text-gray-300">{{ $feature }}</span>
                        </div>
                        @endforeach
                    </div>

                    <!-- Usage Limits -->
                    <div class="border-t border-gray-200 dark:border-gray-700 pt-6 mb-8">
                        <h4 class="font-semibold text-gray-900 dark:text-white mb-4">Usage Limits</h4>
                        <div class="space-y-3 text-sm">
                            <div class="flex justify-between">
                                <span class="text-gray-600 dark:text-gray-400">Menu Items</span>
                                <span class="font-medium text-gray-900 dark:text-white">{{ $plan->max_menu_items ?: 'Unlimited' }}</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-gray-600 dark:text-gray-400">Orders/Month</span>
                                <span class="font-medium text-gray-900 dark:text-white">{{ number_format($plan->max_orders_per_month) ?: 'Unlimited' }}</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-gray-600 dark:text-gray-400">Custom Pages</span>
                                <span class="font-medium text-gray-900 dark:text-white">{{ $plan->max_pages ?: 'Unlimited' }}</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-gray-600 dark:text-gray-400">Branches</span>
                                <span class="font-medium text-gray-900 dark:text-white">{{ $plan->max_branches ?: 'Unlimited' }}</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-gray-600 dark:text-gray-400">Staff Members</span>
                                <span class="font-medium text-gray-900 dark:text-white">{{ $plan->max_staff ?: 'Unlimited' }}</span>
                            </div>
                        </div>
                    </div>

                    <!-- CTA Button -->
                    <div class="text-center">
                        <a href="{{ route('marketing.signup', ['plan' => $plan->id]) }}" 
                           class="w-full bg-blue-600 hover:bg-blue-700 text-white px-6 py-3 rounded-lg font-semibold transition-colors inline-block">
                            Start Free Trial
                        </a>
                    </div>
                </div>
            </div>
            @endforeach
        </div>
    </div>
</section>

<!-- Feature Comparison Table -->
<section class="py-20 bg-white dark:bg-gray-800">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center mb-12">
            <h2 class="text-3xl font-bold text-gray-900 dark:text-white mb-4">Feature Comparison</h2>
            <p class="text-xl text-gray-600 dark:text-gray-400">Compare all features across our plans</p>
        </div>

        <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                <thead class="bg-gray-50 dark:bg-gray-700">
                    <tr>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                            Feature
                        </th>
                        @foreach($plans as $plan)
                        <th class="px-6 py-3 text-center text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                            {{ $plan->name }}
                        </th>
                        @endforeach
                    </tr>
                </thead>
                <tbody class="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                    @php
                    $features = [
                        ['name' => 'has_delivery', 'label' => 'Basic Delivery'],
                        ['name' => 'has_home_delivery', 'label' => 'Home Delivery'],
                        ['name' => 'has_email_marketing', 'label' => 'Email Marketing'],
                        ['name' => 'has_loyalty_program', 'label' => 'Loyalty Program'],
                        ['name' => 'has_analytics', 'label' => 'Basic Analytics'],
                        ['name' => 'has_advanced_reporting', 'label' => 'Advanced Reporting'],
                        ['name' => 'has_multi_location', 'label' => 'Multi-Location Support'],
                        ['name' => 'has_api_access', 'label' => 'API Access'],
                    ];
                    @endphp

                    @foreach($features as $feature)
                    <tr>
                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900 dark:text-white">
                            {{ $feature['label'] }}
                        </td>
                        @foreach($plans as $plan)
                        <td class="px-6 py-4 whitespace-nowrap text-center">
                            @if($plan->{$feature['name']})
                                <svg class="w-5 h-5 text-green-500 mx-auto" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"/>
                                </svg>
                            @else
                                <svg class="w-5 h-5 text-gray-400 mx-auto" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd"/>
                                </svg>
                            @endif
                        </td>
                        @endforeach
                    </tr>
                    @endforeach
                </tbody>
            </table>
        </div>
    </div>
</section>

<!-- FAQ Section -->
<section class="py-20 bg-gray-50 dark:bg-gray-900">
    <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center mb-12">
            <h2 class="text-3xl font-bold text-gray-900 dark:text-white mb-4">Frequently Asked Questions</h2>
        </div>

        <div class="space-y-8">
            <div class="bg-white dark:bg-gray-800 rounded-lg p-6">
                <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-2">Can I change my plan later?</h3>
                <p class="text-gray-600 dark:text-gray-400">Yes, you can upgrade or downgrade your plan at any time. Changes will be reflected in your next billing cycle.</p>
            </div>

            <div class="bg-white dark:bg-gray-800 rounded-lg p-6">
                <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-2">What happens after the free trial?</h3>
                <p class="text-gray-600 dark:text-gray-400">After your 14-day free trial, you'll be automatically billed for your selected plan. You can cancel anytime during the trial period.</p>
            </div>

            <div class="bg-white dark:bg-gray-800 rounded-lg p-6">
                <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-2">Do you offer customer support?</h3>
                <p class="text-gray-600 dark:text-gray-400">Yes, all plans include email support. Professional plan customers get priority support with faster response times.</p>
            </div>

            <div class="bg-white dark:bg-gray-800 rounded-lg p-6">
                <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-2">Can I cancel my subscription?</h3>
                <p class="text-gray-600 dark:text-gray-400">Yes, you can cancel your subscription at any time. Your account will remain active until the end of your current billing period.</p>
            </div>
        </div>
    </div>
</section>

<!-- CTA Section -->
<section class="py-20 bg-gradient-to-r from-blue-600 to-indigo-700 text-white">
    <div class="max-w-4xl mx-auto text-center px-4 sm:px-6 lg:px-8">
        <h2 class="text-3xl md:text-4xl font-bold mb-4">
            Ready to Get Started?
        </h2>
        <p class="text-xl text-blue-100 mb-8">
            Start your 14-day free trial today. No credit card required.
        </p>
        <div class="flex flex-col sm:flex-row gap-4 justify-center">
            <a href="{{ route('marketing.signup') }}" class="bg-white text-blue-600 px-8 py-4 rounded-lg font-semibold text-lg hover:bg-blue-50 transition-colors">
                Start Free Trial
            </a>
            <a href="{{ route('marketing.contact') }}" class="border-2 border-white text-white px-8 py-4 rounded-lg font-semibold text-lg hover:bg-white hover:text-blue-600 transition-colors">
                Contact Sales
            </a>
        </div>
    </div>
</section>
@endsection
