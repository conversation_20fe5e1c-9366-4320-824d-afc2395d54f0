@extends('layouts.manager')

@section('title', 'Branches')

@section('breadcrumb')
    <li class="flex">
        <div class="flex items-center">
            <a href="{{ route('manager.dashboard') }}" class="text-gray-400 hover:text-gray-500">
                <i class="fas fa-home"></i>
            </a>
        </div>
    </li>
    <li class="flex">
        <div class="flex items-center">
            <i class="fas fa-chevron-right text-gray-300 mx-2"></i>
            <span class="text-gray-500">{{ __('Space Management') }}</span>
        </div>
    </li>
    <li class="flex">
        <div class="flex items-center">
            <i class="fas fa-chevron-right text-gray-300 mx-2"></i>
            <span class="text-gray-900 dark:text-white font-medium">{{ __('Branches') }}</span>
        </div>
    </li>
@endsection

@section('content')
<div class="py-6">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <!-- Header -->
        <div class="md:flex md:items-center md:justify-between mb-6">
            <div class="flex-1 min-w-0">
                <h2 class="text-2xl font-bold leading-7 text-gray-900 dark:text-white sm:text-3xl sm:truncate">
                    {{ __('Branches') }}
                </h2>
                <p class="mt-1 text-sm text-gray-500 dark:text-gray-400">
                    {{ __('Manage your restaurant branches and locations') }}
                </p>
            </div>
            <div class="mt-4 flex md:mt-0 md:ml-4">
                <a href="{{ route('manager.branches.create') }}" 
                   class="ml-3 inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500">
                    <i class="fas fa-plus -ml-1 mr-2"></i>
                    {{ __('Add Branch') }}
                </a>
            </div>
        </div>

        <!-- Filters -->
        <div class="bg-white dark:bg-gray-800 shadow rounded-lg mb-6">
            <div class="p-6">
                <form method="GET" action="{{ route('manager.branches.index') }}" class="space-y-4 md:space-y-0 md:flex md:items-end md:space-x-4">
                    <div class="flex-1">
                        <label for="search" class="block text-sm font-medium text-gray-700 dark:text-gray-300">{{ __('Search') }}</label>
                        <input type="text" 
                               name="search" 
                               id="search"
                               value="{{ request('search') }}"
                               placeholder="{{ __('Search by name, address, or phone...') }}"
                               class="mt-1 block w-full border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:text-white">
                    </div>
                    <div>
                        <label for="status" class="block text-sm font-medium text-gray-700 dark:text-gray-300">{{ __('Status') }}</label>
                        <select name="status" 
                                id="status"
                                class="mt-1 block w-full border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:text-white">
                            <option value="">{{ __('All Statuses') }}</option>
                            <option value="1" {{ request('status') === '1' ? 'selected' : '' }}>{{ __('Active') }}</option>
                            <option value="0" {{ request('status') === '0' ? 'selected' : '' }}>{{ __('Inactive') }}</option>
                        </select>
                    </div>
                    <div class="flex space-x-2">
                        <button type="submit" 
                                class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500">
                            <i class="fas fa-search -ml-1 mr-2"></i>
                            {{ __('Filter') }}
                        </button>
                        @if(request()->hasAny(['search', 'status']))
                            <a href="{{ route('manager.branches.index') }}" 
                               class="inline-flex items-center px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500">
                                {{ __('Clear') }}
                            </a>
                        @endif
                    </div>
                </form>
            </div>
        </div>

        <!-- Branches Grid -->
        <div class="grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-3">
            @forelse($branches as $branch)
                <div class="bg-white dark:bg-gray-800 overflow-hidden shadow rounded-lg">
                    <div class="p-6">
                        <div class="flex items-center justify-between">
                            <div class="flex items-center">
                                <div class="flex-shrink-0">
                                    <div class="h-10 w-10 rounded-full bg-primary-100 dark:bg-primary-900 flex items-center justify-center">
                                        <i class="fas fa-store text-primary-600 dark:text-primary-400"></i>
                                    </div>
                                </div>
                                <div class="ml-4">
                                    <h3 class="text-lg font-medium text-gray-900 dark:text-white">
                                        {{ $branch->name }}
                                        @if($branch->is_main_branch)
                                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200 ml-2">
                                                {{ __('Main') }}
                                            </span>
                                        @endif
                                    </h3>
                                    <p class="text-sm text-gray-500 dark:text-gray-400">
                                        {{ $branch->address }}
                                    </p>
                                </div>
                            </div>
                            <div class="flex items-center space-x-2">
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium {{ $branch->is_active ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200' : 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200' }}">
                                    {{ $branch->is_active ? __('Active') : __('Inactive') }}
                                </span>
                            </div>
                        </div>

                        <div class="mt-4">
                            <div class="flex items-center text-sm text-gray-500 dark:text-gray-400">
                                <i class="fas fa-phone mr-2"></i>
                                {{ $branch->phone }}
                            </div>
                            <div class="flex items-center text-sm text-gray-500 dark:text-gray-400 mt-1">
                                <i class="fas fa-clock mr-2"></i>
                                {{ $branch->opening_time }} - {{ $branch->closing_time }}
                            </div>
                            @if($branch->manager)
                                <div class="flex items-center text-sm text-gray-500 dark:text-gray-400 mt-1">
                                    <i class="fas fa-user-tie mr-2"></i>
                                    {{ $branch->manager->name }}
                                </div>
                            @endif
                        </div>

                        <div class="mt-4 flex items-center justify-between">
                            <div class="flex space-x-4 text-sm text-gray-500 dark:text-gray-400">
                                <span>
                                    <i class="fas fa-layer-group mr-1"></i>
                                    {{ $branch->floors_count }} {{ __('floors') }}
                                </span>
                                <span>
                                    <i class="fas fa-chair mr-1"></i>
                                    {{ $branch->tables_count }} {{ __('tables') }}
                                </span>
                            </div>
                        </div>

                        <div class="mt-6 flex justify-end space-x-2">
                            <a href="{{ route('manager.branches.show', $branch) }}" 
                               class="inline-flex items-center px-3 py-1.5 border border-gray-300 dark:border-gray-600 shadow-sm text-xs font-medium rounded text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500">
                                <i class="fas fa-eye mr-1"></i>
                                {{ __('View') }}
                            </a>
                            <a href="{{ route('manager.branches.edit', $branch) }}" 
                               class="inline-flex items-center px-3 py-1.5 border border-transparent shadow-sm text-xs font-medium rounded text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500">
                                <i class="fas fa-edit mr-1"></i>
                                {{ __('Edit') }}
                            </a>
                            @unless($branch->is_main_branch)
                                <button onclick="confirmDelete('{{ $branch->id }}', '{{ $branch->name }}')" 
                                        class="inline-flex items-center px-3 py-1.5 border border-transparent shadow-sm text-xs font-medium rounded text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500">
                                    <i class="fas fa-trash mr-1"></i>
                                    {{ __('Delete') }}
                                </button>
                            @endunless
                        </div>
                    </div>
                </div>
            @empty
                <div class="col-span-full">
                    <div class="text-center py-12">
                        <i class="fas fa-store text-gray-400 text-6xl mb-4"></i>
                        <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-2">{{ __('No branches found') }}</h3>
                        <p class="text-gray-500 dark:text-gray-400 mb-6">{{ __('Get started by creating your first branch.') }}</p>
                        <a href="{{ route('manager.branches.create') }}" 
                           class="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500">
                            <i class="fas fa-plus -ml-1 mr-2"></i>
                            {{ __('Add Branch') }}
                        </a>
                    </div>
                </div>
            @endforelse
        </div>

        <!-- Pagination -->
        @if($branches->hasPages())
            <div class="mt-6">
                {{ $branches->links() }}
            </div>
        @endif
    </div>
</div>

<!-- Delete Confirmation Modal -->
<div id="deleteModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden z-50">
    <div class="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white dark:bg-gray-800">
        <div class="mt-3 text-center">
            <div class="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-red-100 dark:bg-red-900">
                <i class="fas fa-exclamation-triangle text-red-600 dark:text-red-400"></i>
            </div>
            <h3 class="text-lg font-medium text-gray-900 dark:text-white mt-4">{{ __('Delete Branch') }}</h3>
            <div class="mt-2 px-7 py-3">
                <p class="text-sm text-gray-500 dark:text-gray-400">
                    {{ __('Are you sure you want to delete') }} "<span id="branchName"></span>"? {{ __('This action cannot be undone.') }}
                </p>
            </div>
            <div class="items-center px-4 py-3">
                <form id="deleteForm" method="POST" class="inline">
                    @csrf
                    @method('DELETE')
                    <button type="submit" 
                            class="px-4 py-2 bg-red-500 text-white text-base font-medium rounded-md w-24 mr-2 hover:bg-red-600 focus:outline-none focus:ring-2 focus:ring-red-300">
                        {{ __('Delete') }}
                    </button>
                </form>
                <button onclick="closeDeleteModal()" 
                        class="px-4 py-2 bg-gray-500 text-white text-base font-medium rounded-md w-24 hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-gray-300">
                    {{ __('Cancel') }}
                </button>
            </div>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
function confirmDelete(branchId, branchName) {
    document.getElementById('branchName').textContent = branchName;
    document.getElementById('deleteForm').action = `/manager/branches/${branchId}`;
    document.getElementById('deleteModal').classList.remove('hidden');
}

function closeDeleteModal() {
    document.getElementById('deleteModal').classList.add('hidden');
}

// Close modal when clicking outside
document.getElementById('deleteModal').addEventListener('click', function(e) {
    if (e.target === this) {
        closeDeleteModal();
    }
});
</script>
@endpush
