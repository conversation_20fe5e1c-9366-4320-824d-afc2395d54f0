@extends('layouts.manager')

@section('title', 'Create Branch')

@section('breadcrumb')
    <li class="flex">
        <div class="flex items-center">
            <a href="{{ route('manager.dashboard') }}" class="text-gray-400 hover:text-gray-500">
                <i class="fas fa-home"></i>
            </a>
        </div>
    </li>
    <li class="flex">
        <div class="flex items-center">
            <i class="fas fa-chevron-right text-gray-300 mx-2"></i>
            <span class="text-gray-500">{{ __('Space Management') }}</span>
        </div>
    </li>
    <li class="flex">
        <div class="flex items-center">
            <i class="fas fa-chevron-right text-gray-300 mx-2"></i>
            <a href="{{ route('manager.branches.index') }}" class="text-gray-500 hover:text-gray-700">{{ __('Branches') }}</a>
        </div>
    </li>
    <li class="flex">
        <div class="flex items-center">
            <i class="fas fa-chevron-right text-gray-300 mx-2"></i>
            <span class="text-gray-900 dark:text-white font-medium">{{ __('Create') }}</span>
        </div>
    </li>
@endsection

@push('head')
<!-- Google Maps API -->
@if(config('services.google_maps.api_key'))
<script>
    function initGoogleMaps() {
        if (typeof initMap === 'function') {
            initMap();
        }
    }
    
    function gm_authFailure() {
        console.error('Google Maps authentication failed');
        showMapError('Authentication failed. Please check the API key.');
    }
    
    function loadGoogleMaps() {
        const script = document.createElement('script');
        script.src = 'https://maps.googleapis.com/maps/api/js?key={{ config('services.google_maps.api_key') }}&libraries=places&callback=initGoogleMaps';
        script.async = true;
        script.defer = true;
        script.onerror = function() {
            console.error('Failed to load Google Maps script');
            showMapError('Failed to load Google Maps. Please check your internet connection.');
        };
        document.head.appendChild(script);
    }
</script>
<style>
    #map {
        height: 300px;
        width: 100%;
        border-radius: 0.5rem;
        border: 2px solid #e5e7eb;
    }
    
    .map-loading {
        display: flex;
        align-items: center;
        justify-content: center;
        height: 300px;
        background: #f9fafb;
        border-radius: 0.5rem;
        border: 2px dashed #d1d5db;
    }
</style>
@endif
@endpush

@section('content')
<div class="py-6">
    <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
        <!-- Header -->
        <div class="mb-6">
            <h2 class="text-2xl font-bold leading-7 text-gray-900 dark:text-white sm:text-3xl">
                {{ __('Create New Branch') }}
            </h2>
            <p class="mt-1 text-sm text-gray-500 dark:text-gray-400">
                {{ __('Add a new branch location to your restaurant') }}
            </p>
        </div>

        <!-- Form -->
        <div class="bg-white dark:bg-gray-800 shadow rounded-lg">
            <form method="POST" action="{{ route('manager.branches.store') }}" class="space-y-6 p-6">
                @csrf

                <!-- Basic Information -->
                <div class="grid grid-cols-1 gap-6 sm:grid-cols-2">
                    <div>
                        <label for="name" class="block text-sm font-medium text-gray-700 dark:text-gray-300">
                            {{ __('Branch Name') }} <span class="text-red-500">*</span>
                        </label>
                        <input type="text" 
                               name="name" 
                               id="name"
                               value="{{ old('name') }}"
                               required
                               class="mt-1 block w-full border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:text-white @error('name') border-red-300 @enderror">
                        @error('name')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>

                    <div>
                        <label for="phone" class="block text-sm font-medium text-gray-700 dark:text-gray-300">
                            {{ __('Phone Number') }} <span class="text-red-500">*</span>
                        </label>
                        <input type="tel" 
                               name="phone" 
                               id="phone"
                               value="{{ old('phone') }}"
                               required
                               class="mt-1 block w-full border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:text-white @error('phone') border-red-300 @enderror">
                        @error('phone')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>
                </div>

                <!-- Address -->
                <div>
                    <label for="address" class="block text-sm font-medium text-gray-700 dark:text-gray-300">
                        {{ __('Address') }} <span class="text-red-500">*</span>
                    </label>
                    <textarea name="address" 
                              id="address"
                              rows="3"
                              required
                              class="mt-1 block w-full border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:text-white @error('address') border-red-300 @enderror">{{ old('address') }}</textarea>
                    @error('address')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                </div>

                <!-- Google Maps Location -->
                <div>
                    <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                        {{ __('Location on Map') }}
                    </label>
                    
                    <!-- Address Search Box -->
                    <div class="mb-3">
                        <input type="text" 
                               id="address-search" 
                               placeholder="{{ __('Search for an address...') }}"
                               class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:text-white">
                    </div>
                    
                    <!-- Map Container -->
                    <div id="map-container">
                        <div id="map-loading" class="map-loading">
                            <div class="text-center">
                                <i class="fas fa-spinner fa-spin text-2xl text-gray-400 mb-2"></i>
                                <p class="text-gray-600">{{ __('Loading map...') }}</p>
                            </div>
                        </div>
                        <div id="map" style="display: none;"></div>
                    </div>
                    
                    <!-- Selected Location Display -->
                    <div id="selected-location" class="hidden mt-3 p-3 bg-green-50 dark:bg-green-900 border border-green-200 dark:border-green-700 rounded-lg">
                        <div class="flex items-start space-x-2">
                            <i class="fas fa-map-marker-alt text-green-600 mt-1"></i>
                            <div>
                                <p class="font-medium text-green-800 dark:text-green-200">{{ __('Selected Location') }}</p>
                                <p id="selected-location-text" class="text-sm text-green-700 dark:text-green-300"></p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Hidden Location Fields -->
                <input type="hidden" id="latitude" name="latitude" value="{{ old('latitude') }}">
                <input type="hidden" id="longitude" name="longitude" value="{{ old('longitude') }}">

                <!-- Operating Hours -->
                <div class="grid grid-cols-1 gap-6 sm:grid-cols-2">
                    <div>
                        <label for="opening_time" class="block text-sm font-medium text-gray-700 dark:text-gray-300">
                            {{ __('Opening Time') }} <span class="text-red-500">*</span>
                        </label>
                        <input type="time" 
                               name="opening_time" 
                               id="opening_time"
                               value="{{ old('opening_time', '09:00') }}"
                               required
                               class="mt-1 block w-full border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:text-white @error('opening_time') border-red-300 @enderror">
                        @error('opening_time')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>

                    <div>
                        <label for="closing_time" class="block text-sm font-medium text-gray-700 dark:text-gray-300">
                            {{ __('Closing Time') }} <span class="text-red-500">*</span>
                        </label>
                        <input type="time" 
                               name="closing_time" 
                               id="closing_time"
                               value="{{ old('closing_time', '22:00') }}"
                               required
                               class="mt-1 block w-full border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:text-white @error('closing_time') border-red-300 @enderror">
                        @error('closing_time')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>
                </div>

                <!-- Manager -->
                <div>
                    <label for="manager_id" class="block text-sm font-medium text-gray-700 dark:text-gray-300">
                        {{ __('Branch Manager') }}
                    </label>
                    <select name="manager_id" 
                            id="manager_id"
                            class="mt-1 block w-full border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:text-white @error('manager_id') border-red-300 @enderror">
                        <option value="">{{ __('Select a manager') }}</option>
                        @foreach($managers as $manager)
                            <option value="{{ $manager->id }}" {{ old('manager_id') == $manager->id ? 'selected' : '' }}>
                                {{ $manager->name }}
                            </option>
                        @endforeach
                    </select>
                    @error('manager_id')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                </div>

                <!-- Settings -->
                <div class="grid grid-cols-1 gap-6 sm:grid-cols-2">
                    <div>
                        <label for="sort_order" class="block text-sm font-medium text-gray-700 dark:text-gray-300">
                            {{ __('Sort Order') }}
                        </label>
                        <input type="number" 
                               name="sort_order" 
                               id="sort_order"
                               value="{{ old('sort_order', 0) }}"
                               min="0"
                               class="mt-1 block w-full border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:text-white @error('sort_order') border-red-300 @enderror">
                        @error('sort_order')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>

                    <div class="space-y-4">
                        <div class="flex items-center">
                            <input type="checkbox" 
                                   name="is_active" 
                                   id="is_active"
                                   value="1"
                                   {{ old('is_active', true) ? 'checked' : '' }}
                                   class="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 dark:border-gray-600 rounded">
                            <label for="is_active" class="ml-2 block text-sm text-gray-900 dark:text-white">
                                {{ __('Active') }}
                            </label>
                        </div>

                        <div class="flex items-center">
                            <input type="checkbox" 
                                   name="is_main_branch" 
                                   id="is_main_branch"
                                   value="1"
                                   {{ old('is_main_branch') ? 'checked' : '' }}
                                   class="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 dark:border-gray-600 rounded">
                            <label for="is_main_branch" class="ml-2 block text-sm text-gray-900 dark:text-white">
                                {{ __('Main Branch') }}
                            </label>
                        </div>
                    </div>
                </div>

                <!-- Form Actions -->
                <div class="flex justify-end space-x-3 pt-6 border-t border-gray-200 dark:border-gray-700">
                    <a href="{{ route('manager.branches.index') }}" 
                       class="inline-flex items-center px-4 py-2 border border-gray-300 dark:border-gray-600 shadow-sm text-sm font-medium rounded-md text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500">
                        {{ __('Cancel') }}
                    </a>
                    <button type="submit" 
                            class="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500">
                        <i class="fas fa-save -ml-1 mr-2"></i>
                        {{ __('Create Branch') }}
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
    // Global variables for Google Maps
    let map;
    let marker;
    let geocoder;
    let autocomplete;
    let selectedLocation = null;

    // Default location (Dhaka, Bangladesh)
    const defaultLocation = {
        lat: 23.8103,
        lng: 90.4125
    };

    // Initialize page
    document.addEventListener('DOMContentLoaded', function() {
        // Load Google Maps if API key is available
        @if(config('services.google_maps.api_key'))
            loadGoogleMaps();
        @else
            showMapError('Google Maps API key not configured. Location features are disabled.');
        @endif
    });

    // Show map error message
    function showMapError(message) {
        const mapContainer = document.getElementById('map-container');
        if (mapContainer) {
            mapContainer.innerHTML = `
                <div class="map-loading">
                    <div class="text-center">
                        <i class="fas fa-exclamation-triangle text-2xl text-red-400 mb-2"></i>
                        <p class="text-red-600">${message}</p>
                    </div>
                </div>
            `;
        }
    }

    // Google Maps initialization callback
    function initMap() {
        try {
            // Check if Google Maps is available
            if (typeof google === 'undefined' || !google.maps) {
                throw new Error('Google Maps not loaded');
            }

            geocoder = new google.maps.Geocoder();

            // Initialize map
            map = new google.maps.Map(document.getElementById('map'), {
                zoom: 15,
                center: defaultLocation,
                mapTypeControl: false,
                streetViewControl: false,
                fullscreenControl: false
            });

            // Create marker
            marker = new google.maps.Marker({
                position: defaultLocation,
                map: map,
                draggable: true,
                title: '{{ __("Branch Location") }}'
            });

            // Add click listener to map
            map.addListener('click', function(event) {
                updateMarkerPosition(event.latLng);
            });

            // Add drag listener to marker
            marker.addListener('dragend', function(event) {
                updateMarkerPosition(event.latLng);
            });

            // Initialize autocomplete
            const searchInput = document.getElementById('address-search');
            if (google.maps.places) {
                autocomplete = new google.maps.places.Autocomplete(searchInput, {
                    componentRestrictions: { country: 'bd' }, // Restrict to Bangladesh
                    fields: ['place_id', 'geometry', 'name', 'formatted_address']
                });

                autocomplete.addListener('place_changed', function() {
                    const place = autocomplete.getPlace();
                    if (place.geometry) {
                        updateMarkerPosition(place.geometry.location);
                        map.setCenter(place.geometry.location);
                    }
                });
            } else {
                // Disable autocomplete if Places API not available
                searchInput.placeholder = '{{ __("Address search not available") }}';
                searchInput.disabled = true;
            }

            // Hide loading indicator and show map
            const mapLoading = document.getElementById('map-loading');
            const mapElement = document.getElementById('map');
            if (mapLoading) mapLoading.style.display = 'none';
            if (mapElement) mapElement.style.display = 'block';

        } catch (error) {
            console.error('Error initializing Google Maps:', error);
            showMapError('{{ __("Failed to initialize map. Please try refreshing the page.") }}');
        }
    }

    // Update marker position and get address
    function updateMarkerPosition(latLng) {
        try {
            if (!marker || !geocoder) {
                console.error('Google Maps components not initialized');
                return;
            }

            marker.setPosition(latLng);
            selectedLocation = {
                lat: latLng.lat(),
                lng: latLng.lng()
            };

            // Update hidden form fields
            document.getElementById('latitude').value = selectedLocation.lat;
            document.getElementById('longitude').value = selectedLocation.lng;

            // Reverse geocode to get formatted address
            geocoder.geocode({ location: latLng }, function(results, status) {
                if (status === 'OK' && results[0]) {
                    const formattedAddress = results[0].formatted_address;

                    // Update UI
                    document.getElementById('selected-location').classList.remove('hidden');
                    document.getElementById('selected-location-text').textContent = formattedAddress;
                } else {
                    console.error('Geocoding failed:', status);
                    // Still show coordinates even if geocoding fails
                    document.getElementById('selected-location').classList.remove('hidden');
                    document.getElementById('selected-location-text').textContent = `{{ __('Coordinates:') }} ${selectedLocation.lat}, ${selectedLocation.lng}`;
                }
            });
        } catch (error) {
            console.error('Error updating marker position:', error);
        }
    }
</script>
@endpush
