@extends('layouts.manager')

@section('title', 'Create Table')

@section('breadcrumb')
    <li class="flex">
        <div class="flex items-center">
            <a href="{{ route('manager.dashboard') }}" class="text-gray-400 hover:text-gray-500">
                <i class="fas fa-home"></i>
            </a>
        </div>
    </li>
    <li class="flex">
        <div class="flex items-center">
            <i class="fas fa-chevron-right text-gray-300 mx-2"></i>
            <span class="text-gray-500">{{ __('Space Management') }}</span>
        </div>
    </li>
    <li class="flex">
        <div class="flex items-center">
            <i class="fas fa-chevron-right text-gray-300 mx-2"></i>
            <a href="{{ route('manager.tables.index') }}" class="text-gray-500 hover:text-gray-700">{{ __('Tables') }}</a>
        </div>
    </li>
    <li class="flex">
        <div class="flex items-center">
            <i class="fas fa-chevron-right text-gray-300 mx-2"></i>
            <span class="text-gray-900 dark:text-white font-medium">{{ __('Create') }}</span>
        </div>
    </li>
@endsection

@section('content')
<div class="py-6">
    <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
        <!-- Header -->
        <div class="mb-6">
            <h2 class="text-2xl font-bold leading-7 text-gray-900 dark:text-white sm:text-3xl">
                {{ __('Create New Table') }}
            </h2>
            <p class="mt-1 text-sm text-gray-500 dark:text-gray-400">
                {{ __('Add a new table to a specific floor within a branch') }}
            </p>
        </div>

        <!-- Form -->
        <div class="bg-white dark:bg-gray-800 shadow rounded-lg">
            <form method="POST" action="{{ route('manager.tables.store') }}" class="space-y-6 p-6">
                @csrf

                <!-- Branch and Floor Selection -->
                <div class="grid grid-cols-1 gap-6 sm:grid-cols-2">
                    <div>
                        <label for="branch_id" class="block text-sm font-medium text-gray-700 dark:text-gray-300">
                            {{ __('Branch') }} <span class="text-red-500">*</span>
                        </label>
                        <select name="branch_id" 
                                id="branch_id"
                                required
                                onchange="loadFloors()"
                                class="mt-1 block w-full border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:text-white @error('branch_id') border-red-300 @enderror">
                            <option value="">{{ __('Select a branch') }}</option>
                            @foreach($branches as $branch)
                                <option value="{{ $branch->id }}" {{ old('branch_id', request('branch_id')) == $branch->id ? 'selected' : '' }}>
                                    {{ $branch->name }}
                                </option>
                            @endforeach
                        </select>
                        @error('branch_id')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>

                    <div>
                        <label for="floor_id" class="block text-sm font-medium text-gray-700 dark:text-gray-300">
                            {{ __('Floor') }} <span class="text-red-500">*</span>
                        </label>
                        <select name="floor_id" 
                                id="floor_id"
                                required
                                class="mt-1 block w-full border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:text-white @error('floor_id') border-red-300 @enderror">
                            <option value="">{{ __('Select a floor') }}</option>
                            @foreach($floors as $floor)
                                <option value="{{ $floor->id }}" {{ old('floor_id', request('floor_id')) == $floor->id ? 'selected' : '' }}>
                                    {{ $floor->name }}
                                </option>
                            @endforeach
                        </select>
                        @error('floor_id')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>
                </div>

                <!-- Table Basic Information -->
                <div class="grid grid-cols-1 gap-6 sm:grid-cols-2">
                    <div>
                        <label for="name" class="block text-sm font-medium text-gray-700 dark:text-gray-300">
                            {{ __('Table Name') }} <span class="text-red-500">*</span>
                        </label>
                        <input type="text" 
                               name="name" 
                               id="name"
                               value="{{ old('name') }}"
                               required
                               placeholder="{{ __('e.g., Table 1, Window Table, VIP-A') }}"
                               class="mt-1 block w-full border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:text-white @error('name') border-red-300 @enderror">
                        @error('name')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>

                    <div>
                        <label for="capacity" class="block text-sm font-medium text-gray-700 dark:text-gray-300">
                            {{ __('Capacity') }} <span class="text-red-500">*</span>
                        </label>
                        <input type="number" 
                               name="capacity" 
                               id="capacity"
                               value="{{ old('capacity', 4) }}"
                               required
                               min="1"
                               max="50"
                               class="mt-1 block w-full border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:text-white @error('capacity') border-red-300 @enderror">
                        @error('capacity')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                        <p class="mt-1 text-sm text-gray-500 dark:text-gray-400">
                            {{ __('Maximum number of people this table can seat') }}
                        </p>
                    </div>
                </div>

                <!-- Table Type -->
                <div>
                    <label for="table_type" class="block text-sm font-medium text-gray-700 dark:text-gray-300">
                        {{ __('Table Type') }} <span class="text-red-500">*</span>
                    </label>
                    <select name="table_type" 
                            id="table_type"
                            required
                            class="mt-1 block w-full border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:text-white @error('table_type') border-red-300 @enderror">
                        <option value="">{{ __('Select table type') }}</option>
                        <option value="regular" {{ old('table_type') === 'regular' ? 'selected' : '' }}>
                            {{ __('Regular') }} - {{ __('Standard dining table') }}
                        </option>
                        <option value="vip" {{ old('table_type') === 'vip' ? 'selected' : '' }}>
                            {{ __('VIP') }} - {{ __('Premium seating area') }}
                        </option>
                        <option value="outdoor" {{ old('table_type') === 'outdoor' ? 'selected' : '' }}>
                            {{ __('Outdoor') }} - {{ __('Patio or garden seating') }}
                        </option>
                        <option value="private" {{ old('table_type') === 'private' ? 'selected' : '' }}>
                            {{ __('Private') }} - {{ __('Private dining room') }}
                        </option>
                        <option value="bar" {{ old('table_type') === 'bar' ? 'selected' : '' }}>
                            {{ __('Bar') }} - {{ __('Bar counter seating') }}
                        </option>
                    </select>
                    @error('table_type')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                </div>

                <!-- Description -->
                <div>
                    <label for="description" class="block text-sm font-medium text-gray-700 dark:text-gray-300">
                        {{ __('Description') }}
                    </label>
                    <textarea name="description" 
                              id="description"
                              rows="3"
                              placeholder="{{ __('Optional description of the table location, features, or special notes') }}"
                              class="mt-1 block w-full border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:text-white @error('description') border-red-300 @enderror">{{ old('description') }}</textarea>
                    @error('description')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                </div>

                <!-- Settings -->
                <div class="grid grid-cols-1 gap-6 sm:grid-cols-2">
                    <div>
                        <label for="sort_order" class="block text-sm font-medium text-gray-700 dark:text-gray-300">
                            {{ __('Sort Order') }}
                        </label>
                        <input type="number" 
                               name="sort_order" 
                               id="sort_order"
                               value="{{ old('sort_order', 0) }}"
                               min="0"
                               class="mt-1 block w-full border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:text-white @error('sort_order') border-red-300 @enderror">
                        @error('sort_order')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                        <p class="mt-1 text-sm text-gray-500 dark:text-gray-400">
                            {{ __('Lower numbers appear first in listings') }}
                        </p>
                    </div>

                    <div class="space-y-4">
                        <div class="flex items-center">
                            <input type="checkbox" 
                                   name="is_active" 
                                   id="is_active"
                                   value="1"
                                   {{ old('is_active', true) ? 'checked' : '' }}
                                   class="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 dark:border-gray-600 rounded">
                            <label for="is_active" class="ml-2 block text-sm text-gray-900 dark:text-white">
                                {{ __('Active') }}
                            </label>
                        </div>

                        <div class="flex items-center">
                            <input type="checkbox" 
                                   name="is_reservable" 
                                   id="is_reservable"
                                   value="1"
                                   {{ old('is_reservable', true) ? 'checked' : '' }}
                                   class="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 dark:border-gray-600 rounded">
                            <label for="is_reservable" class="ml-2 block text-sm text-gray-900 dark:text-white">
                                {{ __('Reservable') }}
                            </label>
                        </div>
                    </div>
                </div>

                <!-- Information Box -->
                <div class="bg-blue-50 dark:bg-blue-900 border border-blue-200 dark:border-blue-700 rounded-lg p-4">
                    <div class="flex">
                        <div class="flex-shrink-0">
                            <i class="fas fa-info-circle text-blue-400"></i>
                        </div>
                        <div class="ml-3">
                            <h3 class="text-sm font-medium text-blue-800 dark:text-blue-200">
                                {{ __('Table Organization') }}
                            </h3>
                            <div class="mt-2 text-sm text-blue-700 dark:text-blue-300">
                                <ul class="list-disc list-inside space-y-1">
                                    <li>{{ __('Tables must be assigned to a specific floor within a branch') }}</li>
                                    <li>{{ __('Table names must be unique within each floor') }}</li>
                                    <li>{{ __('A unique QR code will be automatically generated for this table') }}</li>
                                    <li>{{ __('Capacity determines the maximum number of guests for this table') }}</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Form Actions -->
                <div class="flex justify-end space-x-3 pt-6 border-t border-gray-200 dark:border-gray-700">
                    <a href="{{ route('manager.tables.index') }}" 
                       class="inline-flex items-center px-4 py-2 border border-gray-300 dark:border-gray-600 shadow-sm text-sm font-medium rounded-md text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500">
                        {{ __('Cancel') }}
                    </a>
                    <button type="submit" 
                            class="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500">
                        <i class="fas fa-save -ml-1 mr-2"></i>
                        {{ __('Create Table') }}
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
function loadFloors() {
    const branchId = document.getElementById('branch_id').value;
    const floorSelect = document.getElementById('floor_id');
    
    // Clear current options except the first one
    floorSelect.innerHTML = '<option value="">{{ __("Select a floor") }}</option>';
    
    if (branchId) {
        // Show loading state
        floorSelect.innerHTML = '<option value="">{{ __("Loading floors...") }}</option>';
        
        // Fetch floors for the selected branch
        fetch(`/manager/api/floors/by-branch/${branchId}`)
            .then(response => response.json())
            .then(floors => {
                floorSelect.innerHTML = '<option value="">{{ __("Select a floor") }}</option>';
                floors.forEach(floor => {
                    const option = document.createElement('option');
                    option.value = floor.id;
                    option.textContent = floor.name;
                    floorSelect.appendChild(option);
                });
            })
            .catch(error => {
                console.error('Error loading floors:', error);
                floorSelect.innerHTML = '<option value="">{{ __("Error loading floors") }}</option>';
            });
    }
}

document.addEventListener('DOMContentLoaded', function() {
    // Load floors if branch is pre-selected
    const branchSelect = document.getElementById('branch_id');
    if (branchSelect.value) {
        loadFloors();
    }
    
    // Auto-focus on branch selection if no branch is selected
    if (!branchSelect.value) {
        branchSelect.focus();
    }
});
</script>
@endpush
