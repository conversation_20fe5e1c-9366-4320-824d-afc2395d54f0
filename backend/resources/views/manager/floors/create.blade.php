@extends('layouts.manager')

@section('title', 'Create Floor')

@section('breadcrumb')
    <li class="flex">
        <div class="flex items-center">
            <a href="{{ route('manager.dashboard') }}" class="text-gray-400 hover:text-gray-500">
                <i class="fas fa-home"></i>
            </a>
        </div>
    </li>
    <li class="flex">
        <div class="flex items-center">
            <i class="fas fa-chevron-right text-gray-300 mx-2"></i>
            <span class="text-gray-500">{{ __('Space Management') }}</span>
        </div>
    </li>
    <li class="flex">
        <div class="flex items-center">
            <i class="fas fa-chevron-right text-gray-300 mx-2"></i>
            <a href="{{ route('manager.floors.index') }}" class="text-gray-500 hover:text-gray-700">{{ __('Floors') }}</a>
        </div>
    </li>
    <li class="flex">
        <div class="flex items-center">
            <i class="fas fa-chevron-right text-gray-300 mx-2"></i>
            <span class="text-gray-900 dark:text-white font-medium">{{ __('Create') }}</span>
        </div>
    </li>
@endsection

@section('content')
<div class="py-6">
    <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
        <!-- Header -->
        <div class="mb-6">
            <h2 class="text-2xl font-bold leading-7 text-gray-900 dark:text-white sm:text-3xl">
                {{ __('Create New Floor') }}
            </h2>
            <p class="mt-1 text-sm text-gray-500 dark:text-gray-400">
                {{ __('Add a new floor to organize tables within a branch') }}
            </p>
        </div>

        <!-- Form -->
        <div class="bg-white dark:bg-gray-800 shadow rounded-lg">
            <form method="POST" action="{{ route('manager.floors.store') }}" class="space-y-6 p-6">
                @csrf

                <!-- Branch Selection -->
                <div>
                    <label for="branch_id" class="block text-sm font-medium text-gray-700 dark:text-gray-300">
                        {{ __('Branch') }} <span class="text-red-500">*</span>
                    </label>
                    <select name="branch_id" 
                            id="branch_id"
                            required
                            class="mt-1 block w-full border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:text-white @error('branch_id') border-red-300 @enderror">
                        <option value="">{{ __('Select a branch') }}</option>
                        @foreach($branches as $branch)
                            <option value="{{ $branch->id }}" {{ old('branch_id') == $branch->id ? 'selected' : '' }}>
                                {{ $branch->name }}
                            </option>
                        @endforeach
                    </select>
                    @error('branch_id')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                </div>

                <!-- Floor Name -->
                <div>
                    <label for="name" class="block text-sm font-medium text-gray-700 dark:text-gray-300">
                        {{ __('Floor Name') }} <span class="text-red-500">*</span>
                    </label>
                    <input type="text" 
                           name="name" 
                           id="name"
                           value="{{ old('name') }}"
                           required
                           placeholder="{{ __('e.g., Ground Floor, First Floor, Rooftop') }}"
                           class="mt-1 block w-full border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:text-white @error('name') border-red-300 @enderror">
                    @error('name')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                    <p class="mt-1 text-sm text-gray-500 dark:text-gray-400">
                        {{ __('Floor names must be unique within each branch') }}
                    </p>
                </div>

                <!-- Description -->
                <div>
                    <label for="description" class="block text-sm font-medium text-gray-700 dark:text-gray-300">
                        {{ __('Description') }}
                    </label>
                    <textarea name="description" 
                              id="description"
                              rows="3"
                              placeholder="{{ __('Optional description of the floor layout, features, or special notes') }}"
                              class="mt-1 block w-full border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:text-white @error('description') border-red-300 @enderror">{{ old('description') }}</textarea>
                    @error('description')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                </div>

                <!-- Settings -->
                <div class="grid grid-cols-1 gap-6 sm:grid-cols-2">
                    <div>
                        <label for="sort_order" class="block text-sm font-medium text-gray-700 dark:text-gray-300">
                            {{ __('Sort Order') }}
                        </label>
                        <input type="number" 
                               name="sort_order" 
                               id="sort_order"
                               value="{{ old('sort_order', 0) }}"
                               min="0"
                               class="mt-1 block w-full border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:text-white @error('sort_order') border-red-300 @enderror">
                        @error('sort_order')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                        <p class="mt-1 text-sm text-gray-500 dark:text-gray-400">
                            {{ __('Lower numbers appear first in listings') }}
                        </p>
                    </div>

                    <div class="flex items-center justify-center">
                        <div class="flex items-center">
                            <input type="checkbox" 
                                   name="is_active" 
                                   id="is_active"
                                   value="1"
                                   {{ old('is_active', true) ? 'checked' : '' }}
                                   class="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 dark:border-gray-600 rounded">
                            <label for="is_active" class="ml-2 block text-sm text-gray-900 dark:text-white">
                                {{ __('Active') }}
                            </label>
                        </div>
                    </div>
                </div>

                <!-- Information Box -->
                <div class="bg-blue-50 dark:bg-blue-900 border border-blue-200 dark:border-blue-700 rounded-lg p-4">
                    <div class="flex">
                        <div class="flex-shrink-0">
                            <i class="fas fa-info-circle text-blue-400"></i>
                        </div>
                        <div class="ml-3">
                            <h3 class="text-sm font-medium text-blue-800 dark:text-blue-200">
                                {{ __('Floor Organization') }}
                            </h3>
                            <div class="mt-2 text-sm text-blue-700 dark:text-blue-300">
                                <ul class="list-disc list-inside space-y-1">
                                    <li>{{ __('Floors help organize tables within a branch') }}</li>
                                    <li>{{ __('Each floor can have multiple tables assigned to it') }}</li>
                                    <li>{{ __('Floor names must be unique within each branch') }}</li>
                                    <li>{{ __('You can add tables to this floor after creating it') }}</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Form Actions -->
                <div class="flex justify-end space-x-3 pt-6 border-t border-gray-200 dark:border-gray-700">
                    <a href="{{ route('manager.floors.index') }}" 
                       class="inline-flex items-center px-4 py-2 border border-gray-300 dark:border-gray-600 shadow-sm text-sm font-medium rounded-md text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500">
                        {{ __('Cancel') }}
                    </a>
                    <button type="submit" 
                            class="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500">
                        <i class="fas fa-save -ml-1 mr-2"></i>
                        {{ __('Create Floor') }}
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Auto-focus on branch selection if no branch is selected
    const branchSelect = document.getElementById('branch_id');
    if (branchSelect && !branchSelect.value) {
        branchSelect.focus();
    }
    
    // Auto-generate floor name suggestions based on branch
    branchSelect.addEventListener('change', function() {
        const nameInput = document.getElementById('name');
        if (!nameInput.value && this.selectedOptions[0]) {
            // You could add logic here to suggest floor names
            // For now, just focus on the name input
            nameInput.focus();
        }
    });
});
</script>
@endpush
