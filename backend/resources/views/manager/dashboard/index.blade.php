@extends('layouts.manager')

@section('title', 'Manager Dashboard')

@section('breadcrumb')
    <li class="flex">
        <div class="flex items-center">
            <i class="fas fa-home text-gray-400"></i>
            <span class="ml-2 text-gray-900 dark:text-white font-medium">{{ __('Dashboard') }}</span>
        </div>
    </li>
@endsection

@section('content')
<div class="py-6">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <!-- Header -->
        <div class="mb-6">
            <h1 class="text-2xl font-bold text-gray-900 dark:text-white">
                {{ __('Manager Dashboard') }}
            </h1>
            <p class="mt-1 text-sm text-gray-500 dark:text-gray-400">
                {{ __('Overview of your restaurant operations') }}
            </p>
        </div>

        <!-- Statistics Cards -->
        <div class="grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-4 mb-8">
            <!-- Total Branches -->
            <div class="bg-white dark:bg-gray-800 overflow-hidden shadow rounded-lg">
                <div class="p-5">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <div class="w-8 h-8 bg-blue-500 rounded-md flex items-center justify-center">
                                <i class="fas fa-store text-white"></i>
                            </div>
                        </div>
                        <div class="ml-5 w-0 flex-1">
                            <dl>
                                <dt class="text-sm font-medium text-gray-500 dark:text-gray-400 truncate">
                                    {{ __('Total Branches') }}
                                </dt>
                                <dd class="text-lg font-medium text-gray-900 dark:text-white">
                                    {{ $stats['total_branches'] }}
                                </dd>
                            </dl>
                        </div>
                    </div>
                </div>
                <div class="bg-gray-50 dark:bg-gray-700 px-5 py-3">
                    <div class="text-sm">
                        <span class="text-green-600 dark:text-green-400 font-medium">
                            {{ $stats['active_branches'] }}
                        </span>
                        <span class="text-gray-500 dark:text-gray-400">{{ __('active') }}</span>
                    </div>
                </div>
            </div>

            <!-- Total Tables -->
            <div class="bg-white dark:bg-gray-800 overflow-hidden shadow rounded-lg">
                <div class="p-5">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <div class="w-8 h-8 bg-green-500 rounded-md flex items-center justify-center">
                                <i class="fas fa-chair text-white"></i>
                            </div>
                        </div>
                        <div class="ml-5 w-0 flex-1">
                            <dl>
                                <dt class="text-sm font-medium text-gray-500 dark:text-gray-400 truncate">
                                    {{ __('Total Tables') }}
                                </dt>
                                <dd class="text-lg font-medium text-gray-900 dark:text-white">
                                    {{ $stats['total_tables'] }}
                                </dd>
                            </dl>
                        </div>
                    </div>
                </div>
                <div class="bg-gray-50 dark:bg-gray-700 px-5 py-3">
                    <div class="text-sm">
                        <span class="text-green-600 dark:text-green-400 font-medium">
                            {{ $stats['active_tables'] }}
                        </span>
                        <span class="text-gray-500 dark:text-gray-400">{{ __('active') }}</span>
                    </div>
                </div>
            </div>

            <!-- Today's Orders -->
            <div class="bg-white dark:bg-gray-800 overflow-hidden shadow rounded-lg">
                <div class="p-5">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <div class="w-8 h-8 bg-yellow-500 rounded-md flex items-center justify-center">
                                <i class="fas fa-shopping-cart text-white"></i>
                            </div>
                        </div>
                        <div class="ml-5 w-0 flex-1">
                            <dl>
                                <dt class="text-sm font-medium text-gray-500 dark:text-gray-400 truncate">
                                    {{ __("Today's Orders") }}
                                </dt>
                                <dd class="text-lg font-medium text-gray-900 dark:text-white">
                                    {{ $stats['total_orders_today'] }}
                                </dd>
                            </dl>
                        </div>
                    </div>
                </div>
                <div class="bg-gray-50 dark:bg-gray-700 px-5 py-3">
                    <div class="text-sm">
                        <span class="text-green-600 dark:text-green-400 font-medium">
                            ৳{{ number_format($stats['total_revenue_today'], 2) }}
                        </span>
                        <span class="text-gray-500 dark:text-gray-400">{{ __('revenue') }}</span>
                    </div>
                </div>
            </div>

            <!-- Total Staff -->
            <div class="bg-white dark:bg-gray-800 overflow-hidden shadow rounded-lg">
                <div class="p-5">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <div class="w-8 h-8 bg-purple-500 rounded-md flex items-center justify-center">
                                <i class="fas fa-users text-white"></i>
                            </div>
                        </div>
                        <div class="ml-5 w-0 flex-1">
                            <dl>
                                <dt class="text-sm font-medium text-gray-500 dark:text-gray-400 truncate">
                                    {{ __('Total Staff') }}
                                </dt>
                                <dd class="text-lg font-medium text-gray-900 dark:text-white">
                                    {{ $stats['total_staff'] }}
                                </dd>
                            </dl>
                        </div>
                    </div>
                </div>
                <div class="bg-gray-50 dark:bg-gray-700 px-5 py-3">
                    <div class="text-sm">
                        <span class="text-blue-600 dark:text-blue-400 font-medium">
                            {{ $stats['total_customers'] }}
                        </span>
                        <span class="text-gray-500 dark:text-gray-400">{{ __('customers') }}</span>
                    </div>
                </div>
            </div>
        </div>

        <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
            <!-- Branch Performance -->
            <div class="bg-white dark:bg-gray-800 shadow rounded-lg">
                <div class="px-4 py-5 sm:p-6">
                    <h3 class="text-lg leading-6 font-medium text-gray-900 dark:text-white mb-4">
                        {{ __('Branch Performance') }}
                    </h3>
                    <div class="space-y-4">
                        @forelse($branchStats as $branch)
                            <div class="flex items-center justify-between p-4 bg-gray-50 dark:bg-gray-700 rounded-lg">
                                <div class="flex items-center">
                                    <div class="flex-shrink-0">
                                        <div class="w-10 h-10 bg-primary-100 dark:bg-primary-900 rounded-full flex items-center justify-center">
                                            <i class="fas fa-store text-primary-600 dark:text-primary-400"></i>
                                        </div>
                                    </div>
                                    <div class="ml-4">
                                        <h4 class="text-sm font-medium text-gray-900 dark:text-white">
                                            {{ $branch->name }}
                                        </h4>
                                        <p class="text-sm text-gray-500 dark:text-gray-400">
                                            {{ $branch->floors_count }} {{ __('floors') }}, {{ $branch->tables_count }} {{ __('tables') }}
                                        </p>
                                    </div>
                                </div>
                                <div class="text-right">
                                    <p class="text-sm font-medium text-gray-900 dark:text-white">
                                        ৳{{ number_format($branch->revenue_today, 2) }}
                                    </p>
                                    <p class="text-sm text-gray-500 dark:text-gray-400">
                                        {{ $branch->orders_count }} {{ __('orders today') }}
                                    </p>
                                </div>
                            </div>
                        @empty
                            <div class="text-center py-8">
                                <i class="fas fa-store text-gray-400 text-4xl mb-4"></i>
                                <p class="text-gray-500 dark:text-gray-400">{{ __('No branches found') }}</p>
                                <a href="{{ route('manager.branches.create') }}" 
                                   class="mt-2 inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500">
                                    <i class="fas fa-plus mr-1"></i>
                                    {{ __('Add Branch') }}
                                </a>
                            </div>
                        @endforelse
                    </div>
                </div>
            </div>

            <!-- Recent Orders -->
            <div class="bg-white dark:bg-gray-800 shadow rounded-lg">
                <div class="px-4 py-5 sm:p-6">
                    <h3 class="text-lg leading-6 font-medium text-gray-900 dark:text-white mb-4">
                        {{ __('Recent Orders') }}
                    </h3>
                    <div class="space-y-4">
                        @forelse($recentOrders as $order)
                            <div class="flex items-center justify-between p-4 bg-gray-50 dark:bg-gray-700 rounded-lg">
                                <div class="flex items-center">
                                    <div class="flex-shrink-0">
                                        <div class="w-10 h-10 bg-yellow-100 dark:bg-yellow-900 rounded-full flex items-center justify-center">
                                            <i class="fas fa-receipt text-yellow-600 dark:text-yellow-400"></i>
                                        </div>
                                    </div>
                                    <div class="ml-4">
                                        <h4 class="text-sm font-medium text-gray-900 dark:text-white">
                                            {{ $order->order_number }}
                                        </h4>
                                        <p class="text-sm text-gray-500 dark:text-gray-400">
                                            {{ $order->customer->name ?? __('Guest') }}
                                            @if($order->table)
                                                - {{ $order->table->name }}
                                            @endif
                                        </p>
                                    </div>
                                </div>
                                <div class="text-right">
                                    <p class="text-sm font-medium text-gray-900 dark:text-white">
                                        ৳{{ number_format($order->total_amount, 2) }}
                                    </p>
                                    <p class="text-sm text-gray-500 dark:text-gray-400">
                                        {{ $order->created_at->diffForHumans() }}
                                    </p>
                                </div>
                            </div>
                        @empty
                            <div class="text-center py-8">
                                <i class="fas fa-shopping-cart text-gray-400 text-4xl mb-4"></i>
                                <p class="text-gray-500 dark:text-gray-400">{{ __('No recent orders') }}</p>
                            </div>
                        @endforelse
                    </div>
                </div>
            </div>
        </div>

        <!-- Floor Utilization -->
        @if($floorUtilization->count() > 0)
            <div class="mt-8">
                <div class="bg-white dark:bg-gray-800 shadow rounded-lg">
                    <div class="px-4 py-5 sm:p-6">
                        <h3 class="text-lg leading-6 font-medium text-gray-900 dark:text-white mb-4">
                            {{ __('Floor Utilization') }}
                        </h3>
                        <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
                            @foreach($floorUtilization as $floor)
                                <div class="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
                                    <div class="flex items-center justify-between mb-2">
                                        <h4 class="text-sm font-medium text-gray-900 dark:text-white">
                                            {{ $floor->name }}
                                        </h4>
                                        <span class="text-sm text-gray-500 dark:text-gray-400">
                                            {{ $floor->utilization_percentage }}%
                                        </span>
                                    </div>
                                    <div class="w-full bg-gray-200 dark:bg-gray-600 rounded-full h-2 mb-2">
                                        <div class="bg-primary-600 h-2 rounded-full" style="width: {{ $floor->utilization_percentage }}%"></div>
                                    </div>
                                    <p class="text-xs text-gray-500 dark:text-gray-400">
                                        {{ $floor->occupied_tables }}/{{ $floor->total_tables }} {{ __('tables occupied') }}
                                    </p>
                                </div>
                            @endforeach
                        </div>
                    </div>
                </div>
            </div>
        @endif

        <!-- Quick Actions -->
        <div class="mt-8">
            <div class="bg-white dark:bg-gray-800 shadow rounded-lg">
                <div class="px-4 py-5 sm:p-6">
                    <h3 class="text-lg leading-6 font-medium text-gray-900 dark:text-white mb-4">
                        {{ __('Quick Actions') }}
                    </h3>
                    <div class="grid grid-cols-2 sm:grid-cols-4 gap-4">
                        <a href="{{ route('manager.branches.create') }}" 
                           class="flex flex-col items-center p-4 bg-blue-50 dark:bg-blue-900 rounded-lg hover:bg-blue-100 dark:hover:bg-blue-800 transition-colors">
                            <i class="fas fa-store text-blue-600 dark:text-blue-400 text-2xl mb-2"></i>
                            <span class="text-sm font-medium text-blue-900 dark:text-blue-200">{{ __('Add Branch') }}</span>
                        </a>
                        <a href="{{ route('manager.floors.create') }}" 
                           class="flex flex-col items-center p-4 bg-green-50 dark:bg-green-900 rounded-lg hover:bg-green-100 dark:hover:bg-green-800 transition-colors">
                            <i class="fas fa-layer-group text-green-600 dark:text-green-400 text-2xl mb-2"></i>
                            <span class="text-sm font-medium text-green-900 dark:text-green-200">{{ __('Add Floor') }}</span>
                        </a>
                        <a href="{{ route('manager.tables.create') }}" 
                           class="flex flex-col items-center p-4 bg-yellow-50 dark:bg-yellow-900 rounded-lg hover:bg-yellow-100 dark:hover:bg-yellow-800 transition-colors">
                            <i class="fas fa-chair text-yellow-600 dark:text-yellow-400 text-2xl mb-2"></i>
                            <span class="text-sm font-medium text-yellow-900 dark:text-yellow-200">{{ __('Add Table') }}</span>
                        </a>
                        <a href="{{ route('manager.analytics') }}" 
                           class="flex flex-col items-center p-4 bg-purple-50 dark:bg-purple-900 rounded-lg hover:bg-purple-100 dark:hover:bg-purple-800 transition-colors">
                            <i class="fas fa-chart-bar text-purple-600 dark:text-purple-400 text-2xl mb-2"></i>
                            <span class="text-sm font-medium text-purple-900 dark:text-purple-200">{{ __('View Analytics') }}</span>
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
