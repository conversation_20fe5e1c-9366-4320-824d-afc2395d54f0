@extends('layouts.marketing')

@section('title', $seoData['title'])
@section('description', $seoData['description'])

@push('head')
    <link rel="canonical" href="{{ $seoData['canonical'] }}">

    <!-- Open Graph Meta Tags -->
    <meta property="og:title" content="{{ $seoData['title'] }}">
    <meta property="og:description" content="{{ $seoData['description'] }}">
    <meta property="og:type" content="{{ $seoData['og_type'] }}">
    <meta property="og:url" content="{{ $seoData['canonical'] }}">
    <meta property="og:site_name" content="{{ config('app.name') }}">
    @if(isset($seoData['og_image']) && $seoData['og_image'])
        <meta property="og:image" content="{{ $seoData['og_image'] }}">
    @endif

    <!-- Article Meta Tags -->
    @if(isset($seoData['article_author']))
        <meta property="article:author" content="{{ $seoData['article_author'] }}">
    @endif
    @if(isset($seoData['article_published_time']))
        <meta property="article:published_time" content="{{ $seoData['article_published_time'] }}">
    @endif
    @if(isset($seoData['article_modified_time']))
        <meta property="article:modified_time" content="{{ $seoData['article_modified_time'] }}">
    @endif
    @if(isset($seoData['article_section']))
        <meta property="article:section" content="{{ $seoData['article_section'] }}">
    @endif
    @if(isset($seoData['article_tags']) && is_array($seoData['article_tags']))
        @foreach($seoData['article_tags'] as $tag)
            <meta property="article:tag" content="{{ $tag }}">
        @endforeach
    @endif

    <!-- Twitter Card Meta Tags -->
    <meta name="twitter:card" content="summary_large_image">
    <meta name="twitter:title" content="{{ $seoData['title'] }}">
    <meta name="twitter:description" content="{{ $seoData['description'] }}">
    @if(isset($seoData['og_image']) && $seoData['og_image'])
        <meta name="twitter:image" content="{{ $seoData['og_image'] }}">
    @endif

    <!-- JSON-LD Structured Data -->
    <script type="application/ld+json">
        {!! json_encode($jsonLd, JSON_UNESCAPED_SLASHES | JSON_UNESCAPED_UNICODE) !!}
    </script>

    <!-- Breadcrumb JSON-LD -->
    <script type="application/ld+json">
        {!! json_encode($breadcrumbJsonLd, JSON_UNESCAPED_SLASHES | JSON_UNESCAPED_UNICODE) !!}
    </script>
    
    <style>
        .blog-content {
            line-height: 1.8;
        }
        .blog-content h1, .blog-content h2, .blog-content h3, .blog-content h4, .blog-content h5, .blog-content h6 {
            margin-top: 2rem;
            margin-bottom: 1rem;
            font-weight: 600;
        }
        .blog-content h1 { font-size: 2rem; }
        .blog-content h2 { font-size: 1.75rem; }
        .blog-content h3 { font-size: 1.5rem; }
        .blog-content p { margin-bottom: 1rem; }
        .blog-content ul, .blog-content ol { margin-bottom: 1rem; padding-left: 2rem; }
        .blog-content li { margin-bottom: 0.5rem; }
        .blog-content blockquote { 
            border-left: 4px solid #3b82f6; 
            padding-left: 1rem; 
            margin: 1.5rem 0; 
            font-style: italic; 
            color: #6b7280; 
        }
        .blog-content img { 
            max-width: 100%; 
            height: auto; 
            margin: 1.5rem 0; 
            border-radius: 0.5rem; 
        }
    </style>
@endpush

@section('content')

<!-- Breadcrumb -->
<nav class="bg-gray-100 dark:bg-gray-800 py-4">
    <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
        <ol class="flex items-center space-x-2 text-sm text-gray-600 dark:text-gray-400">
            <li><a href="{{ url('/') }}" class="hover:text-gray-900 dark:hover:text-white">{{ __('Home') }}</a></li>
            <li><span class="mx-2">/</span></li>
            <li><a href="{{ route('blog.index') }}" class="hover:text-gray-900 dark:hover:text-white">{{ __('blogs.title') }}</a></li>
            <li><span class="mx-2">/</span></li>
            <li class="text-gray-900 dark:text-white">{{ $blog->title }}</li>
        </ol>
    </div>
</nav>

<!-- Main Content -->
<section class="py-16 bg-gray-50 dark:bg-gray-900">
    <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
        <article class="bg-white dark:bg-gray-800 rounded-lg shadow-lg overflow-hidden">
                <!-- Featured Image -->
                @if($blog->featured_image_url)
                    <div class="aspect-w-16 aspect-h-9">
                        <img src="{{ $blog->featured_image_url }}" 
                             alt="{{ $blog->title }}"
                             class="w-full h-64 md:h-96 object-cover">
                    </div>
                @endif
                
                <!-- Article Header -->
                <header class="p-8 pb-4">
                    <h1 class="text-3xl md:text-4xl font-bold text-gray-900 mb-4">{{ $blog->title }}</h1>
                    
                    <div class="flex flex-wrap items-center gap-4 text-sm text-gray-600 mb-6">
                        @if($blog->author)
                            <div class="flex items-center">
                                <span>{{ __('blogs.by_author', ['author' => $blog->author->name]) }}</span>
                            </div>
                        @endif
                        
                        @if($blog->published_at)
                            <div class="flex items-center">
                                <span>{{ __('blogs.published_on') }}</span>
                                <time datetime="{{ $blog->published_at->toISOString() }}" class="ml-1">
                                    {{ $blog->published_at->format('F j, Y') }}
                                </time>
                            </div>
                        @endif
                    </div>
                    
                    <!-- Tags and Categories -->
                    <div class="flex flex-wrap gap-4 mb-6">
                        @if($blog->tags && count($blog->tags) > 0)
                            <div class="flex flex-wrap gap-2">
                                @foreach($blog->tags as $tag)
                                    <a href="{{ route('blog.tag', $tag) }}" 
                                       class="px-3 py-1 bg-blue-100 text-blue-800 text-sm rounded-full hover:bg-blue-200 transition-colors">
                                        #{{ $tag }}
                                    </a>
                                @endforeach
                            </div>
                        @endif
                        
                        @if($blog->categories && count($blog->categories) > 0)
                            <div class="flex flex-wrap gap-2">
                                @foreach($blog->categories as $category)
                                    <a href="{{ route('blog.category', $category) }}" 
                                       class="px-3 py-1 bg-gray-100 text-gray-800 text-sm rounded-full hover:bg-gray-200 transition-colors">
                                        {{ $category }}
                                    </a>
                                @endforeach
                            </div>
                        @endif
                    </div>
                </header>
                
                <!-- Article Content -->
                <div class="px-8 pb-8">
                    <div class="blog-content text-gray-800 prose prose-lg max-w-none">
                        {!! $blog->content !!}
                    </div>
                </div>
            </article>
            
            <!-- Related Posts -->
            @if($relatedBlogs->count() > 0)
                <section class="mt-12">
                    <h2 class="text-2xl font-bold text-gray-900 mb-6">{{ __('blogs.related_posts') }}</h2>
                    
                    <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                        @foreach($relatedBlogs as $relatedBlog)
                            <article class="bg-white rounded-lg shadow-md overflow-hidden hover:shadow-lg transition-shadow">
                                @if($relatedBlog->featured_image_url)
                                    <img src="{{ $relatedBlog->featured_image_url }}" 
                                         alt="{{ $relatedBlog->title }}"
                                         class="w-full h-32 object-cover">
                                @endif
                                
                                <div class="p-4">
                                    <h3 class="text-lg font-semibold text-gray-900 mb-2">
                                        <a href="{{ route('blog.show', $relatedBlog->slug) }}" class="hover:text-blue-600 transition-colors">
                                            {{ $relatedBlog->title }}
                                        </a>
                                    </h3>
                                    
                                    <p class="text-gray-600 text-sm mb-3">{{ Str::limit($relatedBlog->excerpt, 100) }}</p>
                                    
                                    <div class="text-xs text-gray-500">
                                        @if($relatedBlog->published_at)
                                            <time datetime="{{ $relatedBlog->published_at->toISOString() }}">
                                                {{ $relatedBlog->published_at->format('M j, Y') }}
                                            </time>
                                        @endif
                                    </div>
                                </div>
                            </article>
                        @endforeach
                    </div>
                </section>
            @endif
        </main>

            @endif
        </section>
    </div>
</section>
@endsection
