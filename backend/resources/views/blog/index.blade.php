@extends('layouts.marketing')

@section('title', $seoData['title'])
@section('description', $seoData['description'])

@push('head')
    <link rel="canonical" href="{{ $seoData['canonical'] }}">

    <!-- Open Graph Meta Tags -->
    <meta property="og:title" content="{{ $seoData['title'] }}">
    <meta property="og:description" content="{{ $seoData['description'] }}">
    <meta property="og:type" content="{{ $seoData['og_type'] }}">
    <meta property="og:url" content="{{ $seoData['canonical'] }}">
    <meta property="og:site_name" content="{{ config('app.name') }}">

    <!-- Twitter Card Meta Tags -->
    <meta name="twitter:card" content="summary_large_image">
    <meta name="twitter:title" content="{{ $seoData['title'] }}">
    <meta name="twitter:description" content="{{ $seoData['description'] }}">
@endpush

@section('content')

<!-- <PERSON> Header -->
<section class="bg-gradient-to-br from-blue-600 via-blue-700 to-indigo-800 text-white py-16">
    <div class="max-w-4xl mx-auto text-center px-4 sm:px-6 lg:px-8">
        <h1 class="text-4xl md:text-5xl font-bold mb-6">{{ __('blogs.title') }}</h1>
        <p class="text-xl text-blue-100 mb-8">{{ __('blogs.meta_description_index', ['site' => config('app.name')]) }}</p>
    </div>
</section>

<!-- Main Content -->
<section class="py-16 bg-gray-50 dark:bg-gray-900">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">

        <!-- Search and Filters -->
        <div class="mb-12">
            <form method="GET" class="max-w-4xl mx-auto">
                <div class="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6">
                    <div class="flex flex-wrap gap-4 items-center">
                        <!-- Search -->
                        <div class="flex-1 min-w-64">
                            <input type="text"
                                   name="search"
                                   value="{{ request('search') }}"
                                   placeholder="{{ __('blogs.search_placeholder') }}"
                                   class="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white">
                        </div>
                    
                    <!-- Tag Filter -->
                    @if($allTags->count() > 0)
                    <div>
                        <select name="tag" class="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500">
                            <option value="">{{ __('blogs.all_tags') }}</option>
                            @foreach($allTags as $tag)
                                <option value="{{ $tag }}" {{ request('tag') == $tag ? 'selected' : '' }}>{{ $tag }}</option>
                            @endforeach
                        </select>
                    </div>
                    @endif
                    
                    <!-- Category Filter -->
                    @if($allCategories->count() > 0)
                    <div>
                        <select name="category" class="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500">
                            <option value="">{{ __('blogs.all_categories') }}</option>
                            @foreach($allCategories as $category)
                                <option value="{{ $category }}" {{ request('category') == $category ? 'selected' : '' }}>{{ $category }}</option>
                            @endforeach
                        </select>
                    </div>
                    @endif
                    
                        <button type="submit" class="px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors font-medium">
                            {{ __('Search') }}
                        </button>
                    </div>
                </div>
            </form>
        </div>

        <!-- Blog Posts Grid -->
        @if($blogs->count() > 0)
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mb-12">
                @foreach($blogs as $blog)
                    <article class="bg-white dark:bg-gray-800 rounded-lg shadow-lg overflow-hidden hover:shadow-xl transition-shadow duration-300">
                            @if($blog->featured_image_url)
                                <img src="{{ $blog->featured_image_url }}" 
                                     alt="{{ $blog->title }}"
                                     class="w-full h-48 object-cover">
                            @endif
                            
                            <div class="p-6">
                                <h2 class="text-xl font-semibold text-gray-900 mb-2">
                                    <a href="{{ route('blog.show', $blog->slug) }}" class="hover:text-blue-600 transition-colors">
                                        {{ $blog->title }}
                                    </a>
                                </h2>
                                
                                <p class="text-gray-600 mb-4">{{ $blog->excerpt }}</p>
                                
                                <div class="flex items-center justify-between text-sm text-gray-500">
                                    <div>
                                        @if($blog->author)
                                            <span>{{ __('blogs.by_author', ['author' => $blog->author->name]) }}</span>
                                        @endif
                                        @if($blog->published_at)
                                            <span class="mx-2">•</span>
                                            <time datetime="{{ $blog->published_at->toISOString() }}">
                                                {{ $blog->published_at->format('M j, Y') }}
                                            </time>
                                        @endif
                                    </div>
                                    
                                    <a href="{{ route('blog.show', $blog->slug) }}" 
                                       class="text-blue-600 hover:text-blue-800 font-medium">
                                        {{ __('blogs.read_more') }}
                                    </a>
                                </div>
                                
                                <!-- Tags -->
                                @if($blog->tags && count($blog->tags) > 0)
                                    <div class="mt-4 flex flex-wrap gap-2">
                                        @foreach($blog->tags as $tag)
                                            <a href="{{ route('blog.tag', $tag) }}" 
                                               class="px-2 py-1 bg-gray-100 text-gray-700 text-xs rounded-full hover:bg-gray-200 transition-colors">
                                                #{{ $tag }}
                                            </a>
                                        @endforeach
                                    </div>
                                @endif
                            </div>
                    </article>
                @endforeach
            </div>

            <!-- Pagination -->
            <div class="flex justify-center">
                {{ $blogs->links() }}
            </div>
        @else
            <div class="text-center py-12">
                <div class="max-w-md mx-auto">
                    <div class="text-gray-400 mb-4">
                        <svg class="w-16 h-16 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                        </svg>
                    </div>
                    <h3 class="text-xl font-semibold text-gray-900 dark:text-white mb-2">{{ __('blogs.no_posts') }}</h3>
                    <p class="text-gray-600 dark:text-gray-400">Check back soon for new blog posts!</p>
                </div>
            </div>
        @endif
    </div>
</section>
@endsection
