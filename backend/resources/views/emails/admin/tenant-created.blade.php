<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>New Tenant Created</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
        }
        .header {
            background-color: #d4edda;
            padding: 20px;
            text-align: center;
            border-radius: 8px;
            margin-bottom: 30px;
            border-left: 4px solid #28a745;
        }
        .content {
            background-color: #ffffff;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .details {
            background-color: #f8f9fa;
            padding: 20px;
            border-radius: 6px;
            margin: 20px 0;
        }
        .footer {
            text-align: center;
            margin-top: 30px;
            padding-top: 20px;
            border-top: 1px solid #dee2e6;
            color: #6c757d;
            font-size: 14px;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>✅ New Tenant Successfully Created</h1>
        <p>Tenant setup completed successfully</p>
    </div>

    <div class="content">
        <h2>Tenant Setup Completed</h2>
        
        <p>A new tenant has been successfully created and set up in the system.</p>

        <div class="details">
            <h3>Tenant Details</h3>
            <p><strong>Tenant Name:</strong> {{ $tenant_name }}</p>
            <p><strong>Email:</strong> {{ $tenant_email }}</p>
            <p><strong>Tenant ID:</strong> {{ $tenant_id }}</p>
            <p><strong>Created At:</strong> {{ $created_at }}</p>
            <p><strong>Subscription Plan:</strong> {{ $subscription_plan }}</p>
        </div>

        <h3>Setup Summary</h3>
        <ul>
            <li>✅ Tenant database created and migrated</li>
            <li>✅ Default data seeded (categories, departments, branches)</li>
            <li>✅ Manager user account created</li>
            <li>✅ File system directories set up</li>
            <li>✅ Third-party services configured</li>
            <li>✅ Welcome email sent to tenant</li>
        </ul>

        <p>The tenant can now access their restaurant management system and begin operations.</p>
    </div>

    <div class="footer">
        <p>This is an automated notification from the tenant management system.</p>
    </div>
</body>
</html>
