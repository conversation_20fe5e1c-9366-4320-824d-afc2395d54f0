<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>Tenant Creation Failed</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
        }
        .header {
            background-color: #f8d7da;
            padding: 20px;
            text-align: center;
            border-radius: 8px;
            margin-bottom: 30px;
            border-left: 4px solid #dc3545;
        }
        .content {
            background-color: #ffffff;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .details {
            background-color: #f8f9fa;
            padding: 20px;
            border-radius: 6px;
            margin: 20px 0;
        }
        .error {
            background-color: #f8d7da;
            color: #721c24;
            padding: 15px;
            border-radius: 6px;
            margin: 20px 0;
            border-left: 4px solid #dc3545;
            font-family: monospace;
            font-size: 14px;
        }
        .footer {
            text-align: center;
            margin-top: 30px;
            padding-top: 20px;
            border-top: 1px solid #dee2e6;
            color: #6c757d;
            font-size: 14px;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>❌ Tenant Creation Failed</h1>
        <p>Manual intervention required</p>
    </div>

    <div class="content">
        <h2>Tenant Setup Failure</h2>
        
        <p>A tenant creation process has failed and requires manual intervention.</p>

        <div class="details">
            <h3>Tenant Details</h3>
            <p><strong>Tenant Name:</strong> {{ $tenant_name }}</p>
            <p><strong>Email:</strong> {{ $tenant_email }}</p>
            <p><strong>Tenant ID:</strong> {{ $tenant_id }}</p>
            <p><strong>Failed Step:</strong> {{ $failed_step }}</p>
            <p><strong>Failed At:</strong> {{ $failed_at }}</p>
        </div>

        <div class="error">
            <h3>Error Details</h3>
            <p>{{ $error_message }}</p>
        </div>

        <h3>Required Actions</h3>
        <ul>
            <li>🔍 Investigate the error in the application logs</li>
            <li>🔧 Fix any underlying issues (database, permissions, etc.)</li>
            <li>🔄 Retry the tenant creation process manually if needed</li>
            <li>📧 Contact the tenant to inform them of the delay</li>
            <li>📊 Check the tenant details in the admin panel: <a href="{{ $tenant_url }}">View Tenant</a></li>
        </ul>

        <h3>Common Solutions</h3>
        <ul>
            <li><strong>Database Issues:</strong> Check database connectivity and permissions</li>
            <li><strong>File System Issues:</strong> Verify storage permissions and disk space</li>
            <li><strong>Email Issues:</strong> Check SMTP configuration and credentials</li>
            <li><strong>Queue Issues:</strong> Ensure queue workers are running</li>
        </ul>

        <p><strong>Priority:</strong> High - Customer is waiting for their restaurant setup</p>
    </div>

    <div class="footer">
        <p>This is an automated alert from the tenant management system.</p>
        <p>Please investigate and resolve this issue as soon as possible.</p>
    </div>
</body>
</html>
