/* Tenant Management Enhanced Styles */

/* Bulk Actions */
.bulk-actions-bar {
    @apply bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-4 mb-4;
    transition: all 0.3s ease;
}

.bulk-actions-bar.show {
    @apply opacity-100 transform translate-y-0;
}

.bulk-actions-bar.hide {
    @apply opacity-0 transform -translate-y-2;
}

/* Subscription Status Badges */
.subscription-badge {
    @apply inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium;
}

.subscription-badge.trial {
    @apply bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300;
}

.subscription-badge.active {
    @apply bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300;
}

.subscription-badge.inactive {
    @apply bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-300;
}

.subscription-badge.cancelled {
    @apply bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300;
}

.subscription-badge.expired {
    @apply bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300;
}

/* Action Buttons */
.action-button {
    @apply inline-flex items-center justify-center w-8 h-8 rounded-full transition-all duration-200;
}

.action-button:hover {
    @apply transform scale-110;
}

.action-button.subscribe {
    @apply text-green-600 hover:text-green-900 hover:bg-green-100 dark:text-green-400 dark:hover:text-green-300 dark:hover:bg-green-900/20;
}

.action-button.renew {
    @apply text-yellow-600 hover:text-yellow-900 hover:bg-yellow-100 dark:text-yellow-400 dark:hover:text-yellow-300 dark:hover:bg-yellow-900/20;
}

.action-button.manage {
    @apply text-blue-600 hover:text-blue-900 hover:bg-blue-100 dark:text-blue-400 dark:hover:text-blue-300 dark:hover:bg-blue-900/20;
}

.action-button.view {
    @apply text-blue-600 hover:text-blue-900 dark:text-blue-400 dark:hover:text-blue-300;
}

.action-button.edit {
    @apply text-yellow-600 hover:text-yellow-900 dark:text-yellow-400 dark:hover:text-yellow-300;
}

.action-button.suspend {
    @apply text-orange-600 hover:text-orange-900 dark:text-orange-400 dark:hover:text-orange-300;
}

.action-button.activate {
    @apply text-green-600 hover:text-green-900 dark:text-green-400 dark:hover:text-green-300;
}

.action-button.delete {
    @apply text-red-600 hover:text-red-900 dark:text-red-400 dark:hover:text-red-300;
}

/* Table Enhancements */
.tenant-table {
    @apply min-w-full divide-y divide-gray-200 dark:divide-gray-700;
}

.tenant-table th {
    @apply px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider;
}

.tenant-table td {
    @apply px-6 py-4 whitespace-nowrap;
}

.tenant-row {
    @apply hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors duration-150;
}

.tenant-row.selected {
    @apply bg-blue-50 dark:bg-blue-900/20;
}

/* Modal Enhancements */
.modal-overlay {
    @apply fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity;
}

.modal-container {
    @apply flex items-end justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0;
}

.modal-content {
    @apply inline-block align-bottom bg-white dark:bg-gray-800 rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full;
}

.modal-header {
    @apply bg-white dark:bg-gray-800 px-4 pt-5 pb-4 sm:p-6 sm:pb-4;
}

.modal-footer {
    @apply bg-gray-50 dark:bg-gray-700 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse;
}

/* Loading States */
.loading-spinner {
    @apply animate-spin -ml-1 mr-2 h-4 w-4;
}

.loading-button {
    @apply disabled:opacity-50 disabled:cursor-not-allowed;
}

/* Responsive Design */
@media (max-width: 640px) {
    .tenant-table {
        @apply text-sm;
    }
    
    .tenant-table th,
    .tenant-table td {
        @apply px-3 py-2;
    }
    
    .action-button {
        @apply w-6 h-6;
    }
    
    .bulk-actions-bar {
        @apply p-3;
    }
}

/* Tooltips */
.tooltip {
    @apply relative;
}

.tooltip:hover .tooltip-text {
    @apply opacity-100 visible;
}

.tooltip-text {
    @apply invisible opacity-0 absolute z-10 py-1 px-2 text-xs text-white bg-gray-900 rounded-md bottom-full left-1/2 transform -translate-x-1/2 -translate-y-1 transition-all duration-200;
}

/* Animations */
@keyframes fadeIn {
    from { opacity: 0; transform: translateY(-10px); }
    to { opacity: 1; transform: translateY(0); }
}

@keyframes slideIn {
    from { transform: translateX(-100%); }
    to { transform: translateX(0); }
}

.fade-in {
    animation: fadeIn 0.3s ease-out;
}

.slide-in {
    animation: slideIn 0.3s ease-out;
}

/* Focus States */
.focus-ring {
    @apply focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 dark:focus:ring-offset-gray-800;
}
