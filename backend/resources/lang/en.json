{"Dashboard": "Dashboard", "Orders": "Orders", "Menu": "<PERSON><PERSON>", "Customers": "Customers", "Tables": "Tables", "Floors": "Floors", "Space Management": "Space Management", "Kitchen": "Kitchen", "Reports": "Reports", "Settings": "Settings", "Profile": "Profile", "Logout": "Logout", "Login": "<PERSON><PERSON>", "Register": "Register", "Welcome": "Welcome", "Home": "Home", "About": "About", "Contact": "Contact", "Admin Panel": "Admin Panel", "Restaurant Manager": "Restaurant Manager", "Waiter Panel": "Waiter Panel", "Kitchen Panel": "Kitchen Panel", "Delivery Panel": "Delivery Panel", "Total Revenue": "Total Revenue", "Total Orders": "Total Orders", "Active Orders": "Active Orders", "Completed Orders": "Completed Orders", "Pending Orders": "Pending Orders", "Average Order Value": "Average Order Value", "Total Customers": "Total Customers", "New Customers": "New Customers", "VIP Customers": "VIP Customers", "Order Number": "Order Number", "Order Type": "Order Type", "Order Status": "Order Status", "Customer Name": "Customer Name", "Customer Phone": "Customer Phone", "Customer Email": "Customer <PERSON><PERSON>", "Table Number": "Table Number", "Order Total": "Order Total", "Order Date": "Order Date", "Order Time": "Order Time", "Dine In": "<PERSON>e In", "Takeaway": "Takeaway", "Delivery": "Delivery", "Pending": "Pending", "Confirmed": "Confirmed", "Preparing": "Preparing", "Ready": "Ready", "Out for Delivery": "Out for Delivery", "Delivered": "Delivered", "Completed": "Completed", "Cancelled": "Cancelled", "Food Name": "Food Name", "Food Category": "Food Category", "Food Price": "Food Price", "Food Description": "Food Description", "Food Image": "Food Image", "Unavailable": "Unavailable", "In Stock": "In Stock", "Out of Stock": "Out of Stock", "Customer Details": "Customer Details", "Customer History": "Customer History", "Customer Orders": "Customer Orders", "Customer Reviews": "Customer Reviews", "Loyalty Points": "Loyalty Points", "Customer Tier": "Customer Tier", "Table Status": "Table Status", "Table Capacity": "Table Capacity", "Assigned Waiter": "Assigned Waiter", "Current Order": "Current Order", "Reservation": "Reservation", "Available": "Available", "Occupied": "Occupied", "Reserved": "Reserved", "Cleaning": "Cleaning", "Kitchen Orders": "Kitchen Orders", "Preparation Time": "Preparation Time", "Ready for Pickup": "Ready for Pickup", "Start Preparation": "Start Preparation", "Mark as Ready": "<PERSON> as Ready", "Items Prepared": "Items Prepared", "Average Prep Time": "Average Prep Time", "Delivery Orders": "Delivery Orders", "Delivery Driver": "Delivery Driver", "Delivery Address": "Delivery Address", "Delivery Status": "Delivery Status", "Delivery Time": "Delivery Time", "Driver Location": "Driver Location", "Accept Delivery": "Accept Delivery", "Mark Picked Up": "<PERSON> Picked Up", "Mark Delivered": "<PERSON> Delivered", "Delivery Failed": "Delivery Failed", "Sales Report": "Sales Report", "Order Report": "Order Report", "Customer Report": "Customer Report", "Menu Performance": "Menu Performance", "Staff Performance": "Staff Performance", "Financial Report": "Financial Report", "General Settings": "General Settings", "Payment Settings": "Payment Settings", "Notification Settings": "Notification Settings", "User Management": "User Management", "Role Management": "Role Management", "System Settings": "System Settings", "Save": "Save", "Cancel": "Cancel", "Delete": "Delete", "Edit": "Edit", "Add": "Add", "Update": "Update", "Create": "Create", "View": "View", "Search": "Search", "Filter": "Filter", "Export": "Export", "Import": "Import", "Print": "Print", "Download": "Download", "Upload": "Upload", "Submit": "Submit", "Reset": "Reset", "Clear": "Clear", "Refresh": "Refresh", "Back": "Back", "Next": "Next", "Previous": "Previous", "Close": "Close", "Confirm": "Confirm", "Yes": "Yes", "No": "No", "OK": "OK", "Success": "Success", "Error": "Error", "Warning": "Warning", "Info": "Info", "Loading": "Loading", "Please wait": "Please wait", "No data available": "No data available", "No results found": "No results found", "Language": "Language", "Theme": "Theme", "Light Mode": "Light Mode", "Dark Mode": "Dark Mode", "Notifications": "Notifications", "Email Notifications": "Email Notifications", "SMS Notifications": "SMS Notifications", "Push Notifications": "Push Notifications", "Today": "Today", "Yesterday": "Yesterday", "This Week": "This Week", "This Month": "This Month", "This Year": "This Year", "Last 7 Days": "Last 7 Days", "Last 30 Days": "Last 30 Days", "Custom Range": "Custom Range", "Name": "Name", "Email": "Email", "Phone": "Phone", "Address": "Address", "City": "City", "State": "State", "Country": "Country", "Postal Code": "Postal Code", "Date": "Date", "Time": "Time", "Amount": "Amount", "Quantity": "Quantity", "Price": "Price", "Total": "Total", "Subtotal": "Subtotal", "Tax": "Tax", "Discount": "Discount", "Tip": "Tip", "Service Fee": "Service Fee", "Delivery Fee": "Delivery Fee", "Payment Method": "Payment Method", "Payment Status": "Payment Status", "Cash": "Cash", "Card": "Card", "Online": "Online", "Wallet": "Wallet", "Bank Transfer": "Bank Transfer", "Paid": "Paid", "Unpaid": "Unpaid", "Refunded": "Refunded", "Failed": "Failed", "Rating": "Rating", "Review": "Review", "Reviews": "Reviews", "Feedback": "<PERSON><PERSON><PERSON>", "Comments": "Comments", "Stars": "Stars", "Excellent": "Excellent", "Good": "Good", "Average": "Average", "Poor": "Poor", "Terrible": "Terrible", "admin.dashboard.title": "Admin Dashboard", "admin.dashboard.last_updated": "Last Updated", "admin.dashboard.total_tenants": "Total Tenants", "admin.dashboard.active_tenants": "Active Tenants", "admin.dashboard.trial_tenants": "Trial Tenants", "admin.dashboard.monthly_revenue": "Monthly Revenue", "admin.dashboard.monthly_revenue_chart": "Monthly Revenue Chart", "admin.dashboard.recent_tenants": "Recent Customers", "admin.dashboard.recent_payments": "Recent Orders", "admin.dashboard.no_revenue_data": "No revenue data available", "admin.dashboard.revenue_trend": "Revenue Trend", "admin.dashboard.no_recent_tenants": "No recent customers", "admin.dashboard.no_recent_payments": "No recent orders", "admin.dashboard.tenant": "Customer", "admin.dashboard.amount": "Amount", "admin.dashboard.status": "Status", "admin.dashboard.date": "Date", "delivery.dashboard.title": "Delivery Dashboard", "delivery.dashboard.status": "Status", "delivery.dashboard.go_offline": "Go Offline", "delivery.dashboard.go_online": "Go Online", "delivery.dashboard.todays_deliveries": "Today's Deliveries", "delivery.dashboard.todays_earnings": "Today's Earnings", "delivery.dashboard.avg_delivery_time": "Avg Delivery Time", "delivery.dashboard.rating": "Rating", "delivery.dashboard.available_orders": "Available Orders", "delivery.dashboard.current_delivery": "Current Delivery", "delivery.dashboard.recent_deliveries": "Recent Deliveries", "delivery.dashboard.no_available_orders": "No available orders", "delivery.dashboard.no_current_delivery": "No current delivery", "delivery.dashboard.no_recent_deliveries": "No recent deliveries", "delivery.dashboard.order": "Order", "delivery.dashboard.customer": "Customer", "delivery.dashboard.mark_picked_up": "<PERSON> Picked Up", "delivery.dashboard.mark_delivered": "<PERSON> Delivered", "common.refresh": "Refresh", "branches.title": "Branches", "branches.management": "Branches Management", "branches.create.title": "Create Branch", "branches.edit.title": "Edit Branch", "branches.show.title": "Branch Details", "branches.index.title": "All Branches", "branches.add_branch": "Add Branch", "branches.branch_name": "Branch Name", "branches.branch_address": "Branch Address", "branches.branch_manager": "Branch Manager", "branches.branch_phone": "Branch Phone", "branches.branch_email": "Branch Email", "branches.operating_hours": "Operating Hours", "branches.opening_time": "Opening Time", "branches.closing_time": "Closing Time", "branches.operating_days": "Operating Days", "branches.is_main_branch": "Main Branch", "branches.is_active": "Active", "branches.location": "Location", "branches.employees": "Employees", "branches.menu_items": "Menu Items", "branches.statistics": "Statistics", "branches.total_employees": "Total Employees", "branches.active_employees": "Active Employees", "branches.available_menu_items": "Available Menu Items", "branches.recent_orders": "Recent Orders", "branches.no_manager_assigned": "No manager assigned", "branches.activate": "Activate", "branches.deactivate": "Deactivate", "branches.bulk_actions": "Bulk Actions", "branches.selected": "selected", "branches.all_status": "All Status", "branches.all_managers": "All Managers", "branches.sort_by": "Sort By", "branches.sort_order": "Sort Order", "branches.created_date": "Created Date", "branches.manager_name": "Manager", "branches.branch": "Branch", "branches.status": "Status", "branches.actions": "Actions", "branches.main": "Main", "branches.active": "Active", "branches.inactive": "Inactive", "categories.title": "Categories", "categories.management": "Categories Management", "categories.create.title": "Create Category", "categories.edit.title": "Edit Category", "categories.show.title": "Category Details", "categories.index.title": "All Categories", "categories.add_category": "Add Category", "categories.category_name": "Category Name", "categories.category_description": "Category Description", "categories.parent_category": "Parent Category", "categories.subcategories": "Subcategories", "categories.menu_items": "Menu Items", "categories.image": "Category Image", "categories.is_active": "Active", "categories.sort_order": "Sort Order", "categories.no_parent": "No Parent (Top Level)", "categories.select_parent": "Select Parent Category", "categories.total_items": "Total Items", "categories.active_items": "Active Items", "categories.no_image": "No image selected", "categories.select_image": "Select Image", "categories.remove_image": "Remove Image", "categories.change_image": "Change Image", "forms.required": "Required", "forms.optional": "Optional", "forms.save": "Save", "forms.save_and_continue": "Save & Continue", "forms.cancel": "Cancel", "forms.delete": "Delete", "forms.edit": "Edit", "forms.create": "Create", "forms.update": "Update", "forms.submit": "Submit", "forms.reset": "Reset", "forms.clear": "Clear", "forms.search": "Search", "forms.filter": "Filter", "forms.select": "Select", "forms.choose": "<PERSON><PERSON>", "forms.browse": "Browse", "forms.upload": "Upload", "forms.download": "Download", "forms.preview": "Preview", "forms.remove": "Remove", "forms.add": "Add", "forms.close": "Close", "forms.saving": "Saving...", "forms.updating": "Updating...", "messages.success": "Success", "messages.error": "Error", "messages.warning": "Warning", "messages.info": "Information", "messages.confirm_delete": "Are you sure you want to delete this item?", "messages.confirm_bulk_delete": "Are you sure you want to delete the selected items?", "messages.no_items_selected": "No items selected", "messages.operation_successful": "Operation completed successfully", "messages.operation_failed": "Operation failed", "messages.loading": "Loading...", "messages.saving": "Saving...", "messages.deleting": "Deleting...", "messages.processing": "Processing...", "pagination.showing": "Showing", "pagination.to": "to", "pagination.of": "of", "pagination.results": "results", "pagination.previous": "Previous", "pagination.next": "Next", "language.select_language": "Select Language", "language.current_language": "Current Language", "language.english": "English", "language.bengali": "Bengali", "subscription_plans.title": "Subscription Plans", "subscription_plans.management": "Subscription Plans Management", "subscription_plans.create.title": "Create Subscription Plan", "subscription_plans.edit.title": "Edit Subscription Plan", "subscription_plans.show.title": "Subscription Plan Details", "subscription_plans.add_plan": "Add Plan", "subscription_plans.plan_name": "Plan Name", "subscription_plans.plan_name_placeholder": "Enter plan name", "subscription_plans.description": "Description", "subscription_plans.description_placeholder": "Enter plan description", "subscription_plans.price": "Price", "subscription_plans.price_placeholder": "0.00", "subscription_plans.billing_cycle": "Billing Cycle", "subscription_plans.select_billing_cycle": "Select billing cycle", "subscription_plans.monthly": "Monthly", "subscription_plans.yearly": "Yearly", "subscription_plans.trial_days": "Trial Days", "subscription_plans.trial_days_placeholder": "14", "subscription_plans.features": "Features", "subscription_plans.feature_placeholder": "Enter feature description", "subscription_plans.add_feature": "Add Feature", "subscription_plans.limits": "Limits", "subscription_plans.max_orders_per_month": "Max Orders Per Month", "subscription_plans.max_menu_items": "Max Menu Items", "subscription_plans.max_tables": "Max Tables", "subscription_plans.max_staff": "Max Staff", "subscription_plans.unlimited": "Unlimited", "subscription_plans.advanced_features": "Advanced Features", "subscription_plans.has_delivery": "Delivery Support", "subscription_plans.has_analytics": "Analytics & Reports", "subscription_plans.has_multi_location": "Multi-Location Support", "subscription_plans.has_api_access": "API Access", "subscription_plans.is_active": "Active", "subscription_plans.status": "Status", "subscription_plans.active": "Active", "subscription_plans.inactive": "Inactive", "subscription_plans.all_status": "All Status", "subscription_plans.all_cycles": "All Cycles", "subscription_plans.subscribers": "Subscribers", "subscription_plans.actions": "Actions", "subscription_plans.activate": "Activate", "subscription_plans.deactivate": "Deactivate", "subscription_plans.confirm_toggle_status": "Are you sure you want to change the status of this plan?", "subscription_plans.confirm_delete": "Are you sure you want to delete this plan?", "subscription_plans.search_placeholder": "Search plans...", "subscription_plans.total_plans": "Total Plans", "subscription_plans.active_plans": "Active Plans", "subscription_plans.total_subscriptions": "Total Subscriptions", "subscription_plans.basic_information": "Basic Information", "subscription_plans.plan_details": "Plan Details", "subscription_plans.statistics": "Statistics", "subscription_plans.total_subscribers": "Total Subscribers", "subscription_plans.active_subscribers": "Active Subscribers", "subscription_plans.trial_subscribers": "Trial Subscribers", "subscription_plans.monthly_revenue": "Monthly Revenue", "subscription_plans.recent_subscribers": "Recent Subscribers", "subscription_plans.no_subscribers": "No subscribers yet", "subscription_plans.days": "days", "subscription_plans.created_at": "Created At", "subscription_plans.bulk_actions": "Bulk Actions", "subscription_plans.select_all": "Select All", "subscription_plans.bulk_activate": "Activate Selected", "subscription_plans.bulk_deactivate": "Deactivate Selected", "subscription_plans.bulk_delete": "Delete Selected", "subscription_plans.selected_count": "selected", "blogs.title": "Title", "blogs.management": "Blog Management", "blogs.create.title": "Create Blog Post", "blogs.edit.title": "Edit Blog Post", "blogs.show.title": "Blog Post Details", "blogs.add_blog": "Add Blog Post", "blogs.blog_title": "Blog Title", "blogs.content": "Content", "blogs.excerpt": "Excerpt", "blogs.featured_image": "Featured Image", "blogs.meta_description": "Meta Description", "blogs.status": "Status", "blogs.published": "Published", "blogs.draft": "Draft", "blogs.publish": "Publish", "blogs.author": "Author", "blogs.published_at": "Published At", "blogs.tags": "Tags", "blogs.categories": "Categories", "blogs.sort_order": "Sort Order", "blogs.actions": "Actions", "blogs.all_status": "All Status", "blogs.all_authors": "All Authors", "blogs.sort_by": "Sort By", "blogs.newest_first": "Newest First", "blogs.title_az": "Title A-Z", "blogs.published_date": "Published Date", "blogs.selected": "selected", "blogs.delete": "Delete", "blogs.confirm_delete": "Are you sure you want to delete this blog post?", "blogs.confirm_bulk_delete": "Are you sure you want to delete the selected blog posts?", "blogs.confirm_bulk_action": "Are you sure you want to {action} the selected blog posts?", "blogs.search_placeholder": "Search blog posts...", "blogs.total_blogs": "Total Blogs", "blogs.published_blogs": "Published Blogs", "blogs.draft_blogs": "Draft Blogs", "blogs.total_authors": "Total Authors", "blogs.no_author": "No Author", "pages.title": "Pages", "pages.management": "Page Management", "pages.create.title": "Create Page", "pages.edit.title": "Edit Page", "pages.show.title": "Page Details", "pages.add_page": "Add Page", "pages.page_title": "Page Title", "pages.content": "Content", "pages.meta_description": "Meta Description", "pages.status": "Status", "pages.active": "Active", "pages.inactive": "Inactive", "pages.slug": "Slug", "pages.sort_order": "Sort Order", "pages.actions": "Actions", "pages.all_status": "All Status", "pages.confirm_delete": "Are you sure you want to delete this page?", "pages.search_placeholder": "Search pages...", "pages.total_pages": "Total Pages", "pages.active_pages": "Active Pages", "pages.inactive_pages": "Inactive Pages", "pages.selected": "selected", "pages.activate": "Activate", "pages.deactivate": "Deactivate", "pages.confirm_toggle_status": "Are you sure you want to change the status of this page?", "pages.confirm_bulk_delete": "Are you sure you want to delete the selected pages?", "pages.confirm_bulk_action": "Are you sure you want to {action} the selected pages?", "pages.newest_first": "Newest First", "pages.title_az": "Title A-Z", "pages.created_at": "Created At", "blogs.basic_information": "Basic Information", "blogs.title_placeholder": "Enter blog title", "blogs.slug": "Slug", "blogs.slug_placeholder": "auto-generated-from-title", "blogs.slug_help": "Leave empty to auto-generate from title", "blogs.excerpt_placeholder": "Brief description of the blog post", "blogs.select_author": "Select Author", "blogs.no_image": "No image selected", "blogs.select_image": "Select Image", "blogs.change_image": "Change Image", "blogs.remove_image": "Remove Image", "blogs.content_placeholder": "Write your blog content here...", "blogs.seo_meta": "SEO & Meta Information", "blogs.meta_description_placeholder": "Brief description for search engines", "blogs.sort_order_placeholder": "0", "blogs.tags_placeholder": "tag1, tag2, tag3", "blogs.tags_help": "Separate tags with commas", "blogs.categories_placeholder": "category1, category2", "blogs.categories_help": "Separate categories with commas", "blogs.save_draft": "Save as Draft", "blogs.blog_details": "Blog Details", "blogs.updated_at": "Updated At", "blogs.unpublish": "Unpublish", "blogs.duplicate": "Duplicate", "blogs.view_public": "View Public", "blogs.confirm_publish": "Are you sure you want to publish this blog post?", "blogs.confirm_unpublish": "Are you sure you want to unpublish this blog post?", "blogs.confirm_duplicate": "Are you sure you want to duplicate this blog post?", "pages.basic_information": "Basic Information", "pages.title_placeholder": "Enter page title", "pages.slug_placeholder": "auto-generated-from-title", "pages.slug_help": "Leave empty to auto-generate from title", "pages.banner_image": "Banner Image", "pages.no_image": "No image selected", "pages.select_image": "Select Image", "pages.change_image": "Change Image", "pages.remove_image": "Remove Image", "pages.content_placeholder": "Write your page content here...", "pages.seo_meta": "SEO & Meta Information", "pages.meta_description_placeholder": "Brief description for search engines", "pages.meta_description_help": "Recommended length: 150-160 characters", "pages.save_draft": "Save as Draft", "pages.sort_order_placeholder": "0", "pages.page_details": "Page Details", "pages.created": "Created", "pages.updated": "Updated", "pages.updated_at": "Updated At", "pages.duplicate": "Duplicate", "pages.view_public": "View Public", "pages.confirm_activate": "Are you sure you want to activate this page?", "pages.confirm_deactivate": "Are you sure you want to deactivate this page?", "pages.confirm_duplicate": "Are you sure you want to duplicate this page?", "tenants.title": "Tenants", "tenants.management": "Tenant Management", "tenants.tenant_name": "Tenant Name", "tenants.status": "Status", "tenants.subscribed_at": "Subscribed At", "tenants.actions": "Actions", "tenants.active": "Active", "tenants.trial": "Trial", "tenants.suspended": "Suspended", "tenants.expired": "Expired", "tenants.analytics": "Analytics Dashboard", "tenants.usage_stats": "Plan Usage & Limits", "tenants.payment_history": "Payment History", "tenants.order_analytics": "Order Statistics", "tenants.revenue_analytics": "Revenue Statistics", "tenants.menu_analytics": "Menu Statistics", "tenants.staff_analytics": "Staff Statistics", "tenants.customer_analytics": "Customer Statistics", "tenants.total_orders": "Total Orders", "tenants.total_revenue": "Total Revenue", "tenants.active_menu_items": "Active Menu Items", "tenants.active_staff": "Active Staff", "tenants.this_month": "This Month", "tenants.last_month": "Last Month", "tenants.growth": "Growth", "tenants.avg_order_value": "Avg Order Value", "tenants.popular_items": "Popular Items", "tenants.by_role": "By Role", "tenants.plan_usage": "Plan Usage", "tenants.usage_percentage": "Usage Percentage", "admin.dashboard": "Admin Dashboard", "admin.profile": "Profile", "admin.log_out": "Log Out", "blogs.created_successfully": "Blog post created successfully", "blogs.updated_successfully": "Blog post updated successfully", "blogs.deleted_successfully": "Blog post deleted successfully", "blogs.bulk_published": "Selected blog posts have been published", "blogs.bulk_drafted": "Selected blog posts have been moved to draft", "blogs.bulk_deleted": "Selected blog posts have been deleted", "blogs.meta_description_index": "Read the latest blog posts and articles from :site", "blogs.tag_title": "Posts tagged with ':tag'", "blogs.tag_description": "Browse all blog posts tagged with ':tag' on :site", "blogs.category_title": "Posts in ':category' category", "blogs.category_description": "Browse all blog posts in the ':category' category on :site", "blogs.read_more": "Read More", "blogs.published_on": "Published on", "blogs.by_author": "by :author", "blogs.related_posts": "Related Posts", "blogs.no_posts": "No blog posts found", "blogs.filter_by_tag": "Filter by Tag", "blogs.filter_by_category": "Filter by Category", "blogs.all_tags": "All Tags", "blogs.all_categories": "All Categories", "blogs.all_posts": "All Posts", "blogs.no_posts_description": "Check back soon for new blog posts!", "Light": "Light", "Dark": "Dark", "System": "System", "Call Now": "Call Now", "Staff Login": "Staff Login", "Cart": "<PERSON><PERSON>", "Shopping Cart": "Shopping Cart", "Your cart is empty": "Your cart is empty", "Add some delicious items from our menu": "Add some delicious items from our menu", "Proceed to Checkout": "Proceed to Checkout", "Clear Cart": "Clear Cart", "Add to Cart": "Add to Cart", "Vegetarian": "Vegetarian", "Vegan": "Vegan", "Gluten Free": "Gluten Free", "Featured Dishes": "Featured Dishes", "Try our most popular and delicious items": "Try our most popular and delicious items", "View Full Menu": "View Full Menu", "Menu Categories": "Menu Categories", "Explore our diverse range of cuisines": "Explore our diverse range of cuisines", "About Our Restaurant": "About Our Restaurant", "We are passionate about serving delicious, authentic cuisine in a warm and welcoming atmosphere. Our chefs use only the finest ingredients to create memorable dining experiences for our guests.": "We are passionate about serving delicious, authentic cuisine in a warm and welcoming atmosphere. Our chefs use only the finest ingredients to create memorable dining experiences for our guests.", "Quality Food": "Quality Food", "Fresh ingredients, authentic flavors, and exceptional service": "Fresh ingredients, authentic flavors, and exceptional service", "Ready to Order?": "Ready to Order?", "Browse our full menu and place your order online": "Browse our full menu and place your order online", "Order Now": "Order Now", "Discover our delicious selection of dishes": "Discover our delicious selection of dishes", "Menu Coming Soon": "<PERSON>u Coming Soon", "We're working on our menu. Please check back later!": "We're working on our menu. Please check back later!", "Back to Home": "Back to Home", "Checkout": "Checkout", "Complete your order": "Complete your order", "Order Summary": "Order Summary", "Customer Information": "Customer Information", "Full Name": "Full Name", "Phone Number": "Phone Number", "Email Address": "Email Address", "Special Instructions": "Special Instructions", "Any special requests or notes...": "Any special requests or notes...", "Cash on Delivery": "Cash on Delivery", "bKash": "bKash", "Nagad": "Nagad", "Place Order": "Place Order", "Add some items to your cart before checkout": "Add some items to your cart before checkout", "Browse Menu": "<PERSON><PERSON><PERSON> Menu", "Placing Order...": "Placing Order...", "Order placed successfully! We will contact you soon.": "Order placed successfully! We will contact you soon.", "Are you sure you want to clear your cart?": "Are you sure you want to clear your cart?", "Quick Links": "Quick Links", "About Us": "About Us", "Contact Info": "Contact Info", "All rights reserved.": "All rights reserved.", "pages.created_successfully": "Page created successfully", "pages.updated_successfully": "Page updated successfully", "pages.deleted_successfully": "Page deleted successfully", "pages.bulk_activated": "Selected pages have been activated", "pages.bulk_deactivated": "Selected pages have been deactivated", "pages.bulk_deleted": "Selected pages have been deleted", "dashboard": "Dashboard", "orders": "Orders", "menu": "<PERSON><PERSON>", "tables": "Tables", "floors": "Floors", "customers": "Customers", "staff": "Staff", "reports": "Reports", "settings": "Settings", "inventory": "Inventory", "reservations": "Reservations", "delivery": "Delivery", "kitchen": "Kitchen", "analytics": "Analytics", "departments": "Departments", "shifts": "Shifts", "time_entries": "Time Entries", "branches": "Branches", "employees": "Employees", "staff_management": "Staff Management", "floors.title": "Floors", "floors.management": "Floor Management", "floors.create.title": "Create Floor", "floors.edit.title": "Edit Floor", "floors.show.title": "Floor Details", "floors.index.title": "All Floors", "floors.add_floor": "Add Floor", "floors.floor_name": "Floor Name", "floors.floor_description": "Floor Description", "floors.branch": "Branch", "floors.select_branch": "Select Branch", "floors.tables": "Tables", "floors.total_tables": "Total Tables", "floors.active_tables": "Active Tables", "floors.sort_order": "Sort Order", "floors.is_active": "Active", "floors.status": "Status", "floors.actions": "Actions", "floors.active": "Active", "floors.inactive": "Inactive", "floors.all_status": "All Status", "floors.all_branches": "All Branches", "floors.created_successfully": "Floor created successfully", "floors.updated_successfully": "Floor updated successfully", "floors.deleted_successfully": "Floor deleted successfully", "floors.cannot_delete_has_tables": "Cannot delete floor that has tables assigned", "pos.title": "Point of Sale", "pos.table_view": "Table View", "pos.select_branch": "Select Branch", "pos.no_branch_selected": "No branch selected", "pos.branch_switched_successfully": "Branch switched successfully", "pos.floor_tabs": "Floor Tabs", "pos.all_floors": "All Floors", "pos.table_status": "Table Status", "pos.table_capacity": "Capacity", "pos.table_number": "Table Number", "pos.select_table": "Select Table", "pos.table_available": "Available", "pos.table_occupied": "Occupied", "pos.table_reserved": "Reserved", "pos.table_cleaning": "Cleaning", "pos.no_tables": "No Tables Available", "pos.no_floors": "No floors available", "pos.create_order": "Create Order", "pos.view_order": "View Order", "pos.clear_table": "Clear Table", "pos.assign_table": "Assign Table", "pos.new_order": "New Order", "pos.current_order": "Current Order", "pos.active_orders": "Active Orders", "pos.order_items": "Order Items", "pos.add_item": "Add Item", "pos.remove_item": "Remove Item", "pos.update_quantity": "Update Quantity", "pos.apply_discount": "Apply Discount", "pos.process_payment": "Process Payment", "pos.send_to_kitchen": "Send to Kitchen", "pos.close_order": "Close Order", "pos.cancel_order": "Cancel Order", "pos.order_summary": "Order Summary", "pos.payment_methods": "Payment Methods", "pos.cash_payment": "Cash Payment", "pos.card_payment": "Card Payment", "pos.digital_wallet": "Digital Wallet", "pos.split_payment": "Split Payment", "pos.discount_types": "Discount Types", "pos.percentage_discount": "Percentage Discount", "pos.fixed_discount": "Fixed Amount Discount", "pos.complimentary": "Complimentary", "pos.guest_count": "Guest Count", "pos.special_instructions": "Special Instructions", "pos.order_notes": "Order Notes", "pos.kitchen_notes": "Kitchen Notes", "pos.item_modifiers": "Item Modifiers", "pos.order_total": "Order Total", "pos.amount_paid": "Amount <PERSON>", "pos.remaining_balance": "Remaining Balance", "pos.change_due": "Change Due", "pos.receipt": "Receipt", "pos.print_receipt": "Print Receipt", "pos.email_receipt": "Email Receipt", "pos.order_number": "Order Number", "pos.order_time": "Order Time", "pos.served_by": "Served By", "kitchen.title": "Kitchen Display", "kitchen.dashboard": "Kitchen Dashboard", "kitchen.pending_orders": "Pending Orders", "kitchen.preparing_orders": "Preparing Orders", "kitchen.ready_orders": "Ready Orders", "kitchen.completed_orders": "Completed Orders", "kitchen.order_queue": "Order Queue", "kitchen.start_preparing": "Start Preparing", "kitchen.mark_ready": "<PERSON>", "kitchen.mark_served": "<PERSON>", "kitchen.preparation_time": "Preparation Time", "kitchen.estimated_time": "Estimated Time", "kitchen.cooking_instructions": "Cooking Instructions", "kitchen.special_requests": "Special Requests", "kitchen.allergen_info": "Allergen Information", "kitchen.item_status": "Item Status", "kitchen.order_status": "Order Status", "kitchen.kitchen_notes": "Kitchen Notes", "kitchen.prep_time_minutes": "Prep Time (minutes)", "kitchen.average_prep_time": "Average Prep Time", "kitchen.orders_in_queue": "Orders in Queue", "kitchen.items_pending": "Items Pending", "kitchen.refresh_queue": "Refresh <PERSON>", "kitchen.order_details": "Order Details", "kitchen.item_details": "<PERSON><PERSON>", "kitchen.update_status": "Update Status", "kitchen.cancel_item": "Cancel Item", "kitchen.mark_unavailable": "<PERSON>", "kitchen.cooking_time": "Cooking Time", "kitchen.ready_for_pickup": "Ready for Pickup", "kitchen.order_received": "Order Received", "kitchen.in_preparation": "In Preparation", "kitchen.ready_to_serve": "Ready to Serve", "kitchen.served": "Served", "navigation.dashboard": "Dashboard", "navigation.analytics": "Analytics", "navigation.orders": "Orders", "navigation.all_orders": "All Orders", "navigation.kitchen_orders": "Kitchen Orders", "navigation.create_order": "Create Order", "navigation.pos_system": "POS System", "navigation.pos_main": "Point of Sale", "navigation.pos_tables": "Table View", "navigation.kitchen_display": "Kitchen Display", "navigation.menu_management": "Menu Management", "navigation.menu_items": "Menu Items", "navigation.categories": "Categories", "navigation.tables": "Tables", "navigation.customers": "Customers", "navigation.staff_management": "Staff Management", "navigation.branches": "Branches", "navigation.floors": "Floors", "navigation.staff": "Staff", "navigation.departments": "Departments", "navigation.employees": "Employees", "navigation.shifts": "Shifts", "navigation.inventory_management": "Inventory Management", "navigation.inventory": "Inventory", "navigation.purchase_orders": "Purchase Orders", "navigation.vendors": "Vend<PERSON>", "navigation.reports": "Reports", "navigation.all_reports": "All Reports", "navigation.sales_reports": "Sales Reports", "navigation.inventory_reports": "Inventory Reports", "navigation.staff_reports": "Staff Reports", "navigation.settings": "Settings", "navigation.site_settings": "Site Settings", "navigation.profile": "Profile", "dashboard_titles.manager_dashboard": "Manager Dashboard", "dashboard_titles.kitchen_dashboard": "Kitchen Dashboard", "actions.logout": "Log Out", "order_management.pending": "Pending", "order_management.preparing": "Cooking", "order_management.ready": "Ready", "kitchen.completed_today": "Completed Today", "No pending orders": "No pending orders", "No received orders": "No received orders", "No orders in preparation": "No orders in preparation", "No orders ready": "No orders ready", "menu_variations.title": "Menu Variations & Add-ons", "menu_variations.sizes": "Size Variations", "menu_variations.addons": "Add-ons", "menu_variations.combos": "Combo Menus", "menu_variations.size_name": "Size Name", "menu_variations.price_modifier": "Price Modifier", "menu_variations.addon_name": "Add-on Name", "menu_variations.addon_price": "Add-on Price", "menu_variations.addon_category": "Category", "menu_variations.max_quantity": "Max Quantity", "menu_variations.combo_name": "Combo Name", "menu_variations.combo_price": "Combo Price", "menu_variations.combo_components": "Combo Components", "menu_variations.component_type": "Component Type", "menu_variations.main_item": "Main Item", "menu_variations.side_item": "Side Item", "menu_variations.drink": "Drink", "menu_variations.dessert": "Dessert", "menu_variations.is_required": "Required", "menu_variations.is_available": "Available", "menu_variations.sort_order": "Sort Order", "menu_variations.description": "Description", "menu_variations.add_size": "<PERSON><PERSON>", "menu_variations.add_addon": "Add Add-on", "menu_variations.add_combo": "Add Combo", "menu_variations.edit_size": "<PERSON>", "menu_variations.edit_addon": "Edit Add-on", "menu_variations.edit_combo": "<PERSON><PERSON>", "menu_variations.delete_size": "Delete Size", "menu_variations.delete_addon": "Delete Add-on", "menu_variations.delete_combo": "Delete Combo", "menu_variations.size_created": "Size variation created successfully", "menu_variations.addon_created": "Add-on created successfully", "menu_variations.combo_created": "Combo menu created successfully", "menu_variations.size_updated": "Size variation updated successfully", "menu_variations.addon_updated": "Add-on updated successfully", "menu_variations.combo_updated": "Combo menu updated successfully", "menu_variations.size_deleted": "Size variation deleted successfully", "menu_variations.addon_deleted": "Add-on deleted successfully", "menu_variations.combo_deleted": "Combo menu deleted successfully", "menu_items.create.title": "Create New Menu <PERSON>em", "menu_items.create.basic_info": "Basic Information", "menu_items.create.name_field": "Item Name", "menu_items.create.name_placeholder": "Enter menu item name", "menu_items.create.description_field": "Description", "menu_items.create.description_placeholder": "Describe your menu item", "menu_items.create.category_field": "Category", "menu_items.create.category_placeholder": "Select a category", "menu_items.create.subcategory_field": "Subcategory", "menu_items.create.subcategory_placeholder": "Select a subcategory (optional)", "menu_items.create.price_field": "Base Price", "menu_items.create.price_placeholder": "0.00", "menu_items.create.preparation_time": "Preparation Time (minutes)", "menu_items.create.calories": "Calories", "menu_items.create.ingredients": "Ingredients", "menu_items.create.ingredients_placeholder": "List main ingredients", "menu_items.create.image_section": "Images", "menu_items.create.select_images": "Select Images", "menu_items.create.dietary_preferences": "Dietary Preferences", "menu_items.create.is_vegetarian": "Vegetarian", "menu_items.create.is_vegan": "Vegan", "menu_items.create.is_gluten_free": "Gluten Free", "menu_items.create.is_spicy": "Spicy", "menu_items.create.availability": "Availability", "menu_items.create.is_available": "Available for ordering", "menu_items.create.is_featured": "Featured item", "menu_items.create.sort_order": "Sort Order", "menu_items.create.save_button": "Create <PERSON><PERSON>", "menu_items.create.cancel_button": "Cancel", "menu_items.create.preview_button": "Preview", "menu_items.create.auto_save": "Auto-saved", "menu_items.create.advanced_options": "Advanced Options", "menu_items.create.size_variations": "Size Variations", "menu_items.create.size_variations_desc": "Define different sizes with price adjustments", "menu_items.create.add_size_variation": "Add Size Variation", "menu_items.create.size_name": "Size Name", "menu_items.create.size_name_bn": "<PERSON><PERSON> (Bengali)", "menu_items.create.price_modifier": "Price Modifier", "menu_items.create.price_modifier_help": "Amount to add (+) or subtract (-) from base price", "menu_items.create.size_available": "Available", "menu_items.create.remove_size": "Remove <PERSON>", "menu_items.create.addons_section": "Add-ons", "menu_items.create.addons_desc": "Select add-ons that customers can choose with this item", "menu_items.create.addon_categories": "Add-on Categories", "menu_items.create.select_addons": "Select Add-ons", "menu_items.create.no_addons": "No add-ons available", "menu_items.create.addon_price": "Price", "menu_items.create.combo_section": "Combo Menu Integration", "menu_items.create.combo_desc": "Configure this item for combo menu inclusion", "menu_items.create.combo_eligible": "Available for combo menus", "menu_items.create.combo_types": "Combo Component Types", "menu_items.create.combo_main": "Main Item", "menu_items.create.combo_side": "Side Item", "menu_items.create.combo_drink": "Drink", "menu_items.create.combo_dessert": "Dessert", "menu_items.create.price_preview": "Price Preview", "menu_items.create.base_price_label": "Base Price", "menu_items.create.with_variations": "With Size Variations", "menu_items.create.estimated_range": "Estimated Price Range", "menu_items.create.validation.name_required": "Item name is required", "menu_items.create.validation.price_required": "Base price is required", "menu_items.create.validation.price_min": "Price must be greater than 0", "menu_items.create.validation.category_required": "Category is required", "menu_items.create.validation.size_name_required": "Size name is required", "menu_items.create.validation.price_modifier_number": "Price modifier must be a number", "menu_items.create.success": "Menu item created successfully", "menu_items.create.error": "Failed to create menu item", "menu_items.create.saving": "Saving menu item...", "No Branch Access": "No Branch Access", "What you can do:": "What you can do:", "Contact your administrator to assign you to a branch": "Contact your administrator to assign you to a branch", "Check if you have the correct role permissions": "Check if you have the correct role permissions", "Ensure your employee record is properly configured": "Ensure your employee record is properly configured", "Check if you have the correct kitchen role permissions": "Check if you have the correct kitchen role permissions", "Go to Dashboard": "Go to Dashboard", "Refresh Page": "Refresh Page", "If this problem persists, please contact your system administrator.": "If this problem persists, please contact your system administrator."}