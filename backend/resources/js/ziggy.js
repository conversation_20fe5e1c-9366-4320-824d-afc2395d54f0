const Ziggy = {"url":"http:\/\/localhost:8000","port":8000,"defaults":{},"routes":{"cashier.payment":{"uri":"stripe\/payment\/{id}","methods":["GET","HEAD"],"parameters":["id"]},"cashier.webhook":{"uri":"stripe\/webhook","methods":["POST"]},"login":{"uri":"login","methods":["GET","HEAD"]},"logout":{"uri":"logout","methods":["POST"]},"password.request":{"uri":"forgot-password","methods":["GET","HEAD"]},"password.reset":{"uri":"reset-password\/{token}","methods":["GET","HEAD"],"parameters":["token"]},"password.email":{"uri":"forgot-password","methods":["POST"]},"password.update":{"uri":"reset-password","methods":["POST"]},"register":{"uri":"register","methods":["GET","HEAD"]},"register.store":{"uri":"register","methods":["POST"]},"verification.notice":{"uri":"email\/verify","methods":["GET","HEAD"]},"verification.verify":{"uri":"email\/verify\/{id}\/{hash}","methods":["GET","HEAD"],"parameters":["id","hash"]},"verification.send":{"uri":"email\/verification-notification","methods":["POST"]},"user-profile-information.update":{"uri":"user\/profile-information","methods":["PUT"]},"user-password.update":{"uri":"user\/password","methods":["PUT"]},"password.confirm":{"uri":"user\/confirm-password","methods":["GET","HEAD"]},"password.confirmation":{"uri":"user\/confirmed-password-status","methods":["GET","HEAD"]},"password.confirm.store":{"uri":"user\/confirm-password","methods":["POST"]},"two-factor.login":{"uri":"two-factor-challenge","methods":["GET","HEAD"]},"two-factor.login.store":{"uri":"two-factor-challenge","methods":["POST"]},"two-factor.enable":{"uri":"user\/two-factor-authentication","methods":["POST"]},"two-factor.confirm":{"uri":"user\/confirmed-two-factor-authentication","methods":["POST"]},"two-factor.disable":{"uri":"user\/two-factor-authentication","methods":["DELETE"]},"two-factor.qr-code":{"uri":"user\/two-factor-qr-code","methods":["GET","HEAD"]},"two-factor.secret-key":{"uri":"user\/two-factor-secret-key","methods":["GET","HEAD"]},"two-factor.recovery-codes":{"uri":"user\/two-factor-recovery-codes","methods":["GET","HEAD"]},"profile.show":{"uri":"user\/profile","methods":["GET","HEAD"]},"other-browser-sessions.destroy":{"uri":"user\/other-browser-sessions","methods":["DELETE"]},"current-user-photo.destroy":{"uri":"user\/profile-photo","methods":["DELETE"]},"current-user.destroy":{"uri":"user","methods":["DELETE"]},"api-tokens.index":{"uri":"user\/api-tokens","methods":["GET","HEAD"]},"api-tokens.store":{"uri":"user\/api-tokens","methods":["POST"]},"api-tokens.update":{"uri":"user\/api-tokens\/{token}","methods":["PUT"],"parameters":["token"]},"api-tokens.destroy":{"uri":"user\/api-tokens\/{token}","methods":["DELETE"],"parameters":["token"]},"teams.create":{"uri":"teams\/create","methods":["GET","HEAD"]},"teams.store":{"uri":"teams","methods":["POST"]},"teams.show":{"uri":"teams\/{team}","methods":["GET","HEAD"],"parameters":["team"]},"teams.update":{"uri":"teams\/{team}","methods":["PUT"],"parameters":["team"]},"teams.destroy":{"uri":"teams\/{team}","methods":["DELETE"],"parameters":["team"]},"current-team.update":{"uri":"current-team","methods":["PUT"]},"team-members.store":{"uri":"teams\/{team}\/members","methods":["POST"],"parameters":["team"]},"team-members.update":{"uri":"teams\/{team}\/members\/{user}","methods":["PUT"],"parameters":["team","user"]},"team-members.destroy":{"uri":"teams\/{team}\/members\/{user}","methods":["DELETE"],"parameters":["team","user"]},"team-invitations.accept":{"uri":"team-invitations\/{invitation}","methods":["GET","HEAD"],"parameters":["invitation"]},"team-invitations.destroy":{"uri":"team-invitations\/{invitation}","methods":["DELETE"],"parameters":["invitation"]},"sanctum.csrf-cookie":{"uri":"sanctum\/csrf-cookie","methods":["GET","HEAD"]},"stancl.tenancy.asset":{"uri":"tenancy\/assets\/{path?}","methods":["GET","HEAD"],"wheres":{"path":"(.*)"},"parameters":["path"]},"marketing.home":{"uri":"\/","methods":["GET","HEAD"],"domain":"localhost"},"marketing.plans":{"uri":"plans","methods":["GET","HEAD"],"domain":"localhost"},"marketing.contact":{"uri":"contact","methods":["GET","HEAD"],"domain":"localhost"},"marketing.contact.submit":{"uri":"contact","methods":["POST"],"domain":"localhost"},"marketing.signup":{"uri":"signup","methods":["GET","HEAD"],"domain":"localhost"},"marketing.signup.process":{"uri":"signup","methods":["POST"],"domain":"localhost"},"marketing.check-slug":{"uri":"check-slug","methods":["GET","HEAD"],"domain":"localhost"},"marketing.generate-slug":{"uri":"generate-slug","methods":["GET","HEAD"],"domain":"localhost"},"marketing.features":{"uri":"features","methods":["GET","HEAD"],"domain":"localhost"},"admin.dashboard":{"uri":"admin\/dashboard","methods":["GET","HEAD"]},"admin.tenants.index":{"uri":"admin\/tenants","methods":["GET","HEAD"],"domain":"localhost"},"admin.tenants.create":{"uri":"admin\/tenants\/create","methods":["GET","HEAD"],"domain":"localhost"},"admin.tenants.store":{"uri":"admin\/tenants","methods":["POST"],"domain":"localhost"},"admin.tenants.show":{"uri":"admin\/tenants\/{tenant}","methods":["GET","HEAD"],"domain":"localhost","parameters":["tenant"],"bindings":{"tenant":"id"}},"admin.tenants.edit":{"uri":"admin\/tenants\/{tenant}\/edit","methods":["GET","HEAD"],"domain":"localhost","parameters":["tenant"],"bindings":{"tenant":"id"}},"admin.tenants.update":{"uri":"admin\/tenants\/{tenant}","methods":["PUT","PATCH"],"domain":"localhost","parameters":["tenant"],"bindings":{"tenant":"id"}},"admin.tenants.destroy":{"uri":"admin\/tenants\/{tenant}","methods":["DELETE"],"domain":"localhost","parameters":["tenant"],"bindings":{"tenant":"id"}},"admin.tenants.suspend":{"uri":"admin\/tenants\/{tenant}\/suspend","methods":["POST"],"domain":"localhost","parameters":["tenant"],"bindings":{"tenant":"id"}},"admin.tenants.activate":{"uri":"admin\/tenants\/{tenant}\/activate","methods":["POST"],"domain":"localhost","parameters":["tenant"],"bindings":{"tenant":"id"}},"admin.tenants.impersonate":{"uri":"admin\/tenants\/{tenant}\/impersonate","methods":["GET","HEAD"],"domain":"localhost","parameters":["tenant"],"bindings":{"tenant":"id"}},"admin.subscription-plans.index":{"uri":"admin\/subscription-plans","methods":["GET","HEAD"],"domain":"localhost"},"admin.subscription-plans.create":{"uri":"admin\/subscription-plans\/create","methods":["GET","HEAD"],"domain":"localhost"},"admin.subscription-plans.store":{"uri":"admin\/subscription-plans","methods":["POST"],"domain":"localhost"},"admin.subscription-plans.show":{"uri":"admin\/subscription-plans\/{subscription_plan}","methods":["GET","HEAD"],"domain":"localhost","parameters":["subscription_plan"]},"admin.subscription-plans.edit":{"uri":"admin\/subscription-plans\/{subscription_plan}\/edit","methods":["GET","HEAD"],"domain":"localhost","parameters":["subscription_plan"]},"admin.subscription-plans.update":{"uri":"admin\/subscription-plans\/{subscription_plan}","methods":["PUT","PATCH"],"domain":"localhost","parameters":["subscription_plan"]},"admin.subscription-plans.destroy":{"uri":"admin\/subscription-plans\/{subscription_plan}","methods":["DELETE"],"domain":"localhost","parameters":["subscription_plan"]},"admin.subscription-plans.toggle-status":{"uri":"admin\/subscription-plans\/{plan}\/toggle-status","methods":["POST"],"domain":"localhost","parameters":["plan"],"bindings":{"plan":"id"}},"admin.subscription-plans.bulk-action":{"uri":"admin\/subscription-plans\/bulk-action","methods":["POST"],"domain":"localhost"},"admin.payments.index":{"uri":"admin\/payments","methods":["GET","HEAD"],"domain":"localhost"},"admin.payments.show":{"uri":"admin\/payments\/{payment}","methods":["GET","HEAD"],"domain":"localhost","parameters":["payment"],"bindings":{"payment":"id"}},"admin.payments.refund":{"uri":"admin\/payments\/{payment}\/refund","methods":["POST"],"domain":"localhost","parameters":["payment"],"bindings":{"payment":"id"}},"payment.return":{"uri":"return","methods":["GET","HEAD"],"domain":"localhost"},"payment.webhook":{"uri":"webhook","methods":["POST"],"domain":"localhost"},"payment.success":{"uri":"payment\/success","methods":["GET","HEAD"],"domain":"localhost"},"payment.cancel":{"uri":"payment\/cancel","methods":["GET","HEAD"],"domain":"localhost"},"payment.fail":{"uri":"payment\/fail","methods":["GET","HEAD"],"domain":"localhost"},"language.switch":{"uri":"language\/switch","methods":["POST"],"domain":"localhost"},"language.current":{"uri":"language\/current","methods":["GET","HEAD"],"domain":"localhost"},"language.translations":{"uri":"language\/translations","methods":["GET","HEAD"],"domain":"localhost"},"admin.index":{"uri":"admin","methods":["GET","HEAD"],"domain":"localhost"},"admin.dynamic-pages.index":{"uri":"admin\/dynamic-pages","methods":["GET","HEAD"],"domain":"localhost"},"admin.dynamic-pages.create":{"uri":"admin\/dynamic-pages\/create","methods":["GET","HEAD"],"domain":"localhost"},"admin.dynamic-pages.store":{"uri":"admin\/dynamic-pages","methods":["POST"],"domain":"localhost"},"admin.dynamic-pages.show":{"uri":"admin\/dynamic-pages\/{dynamic_page}","methods":["GET","HEAD"],"domain":"localhost","parameters":["dynamic_page"]},"admin.dynamic-pages.edit":{"uri":"admin\/dynamic-pages\/{dynamic_page}\/edit","methods":["GET","HEAD"],"domain":"localhost","parameters":["dynamic_page"]},"admin.dynamic-pages.update":{"uri":"admin\/dynamic-pages\/{dynamic_page}","methods":["PUT","PATCH"],"domain":"localhost","parameters":["dynamic_page"]},"admin.dynamic-pages.destroy":{"uri":"admin\/dynamic-pages\/{dynamic_page}","methods":["DELETE"],"domain":"localhost","parameters":["dynamic_page"]},"admin.dynamic-pages.bulk-action":{"uri":"admin\/dynamic-pages\/bulk-action","methods":["POST"],"domain":"localhost"},"admin.blogs.index":{"uri":"admin\/blogs","methods":["GET","HEAD"],"domain":"localhost"},"admin.blogs.create":{"uri":"admin\/blogs\/create","methods":["GET","HEAD"],"domain":"localhost"},"admin.blogs.store":{"uri":"admin\/blogs","methods":["POST"],"domain":"localhost"},"admin.blogs.show":{"uri":"admin\/blogs\/{blog}","methods":["GET","HEAD"],"domain":"localhost","parameters":["blog"],"bindings":{"blog":"slug"}},"admin.blogs.edit":{"uri":"admin\/blogs\/{blog}\/edit","methods":["GET","HEAD"],"domain":"localhost","parameters":["blog"],"bindings":{"blog":"slug"}},"admin.blogs.update":{"uri":"admin\/blogs\/{blog}","methods":["PUT","PATCH"],"domain":"localhost","parameters":["blog"],"bindings":{"blog":"slug"}},"admin.blogs.destroy":{"uri":"admin\/blogs\/{blog}","methods":["DELETE"],"domain":"localhost","parameters":["blog"],"bindings":{"blog":"slug"}},"admin.blogs.bulk-action":{"uri":"admin\/blogs\/bulk-action","methods":["POST"],"domain":"localhost"},"blog.index":{"uri":"blog","methods":["GET","HEAD"],"domain":"localhost"},"blog.tag":{"uri":"blog\/tag\/{tag}","methods":["GET","HEAD"],"domain":"localhost","parameters":["tag"]},"blog.category":{"uri":"blog\/category\/{category}","methods":["GET","HEAD"],"domain":"localhost","parameters":["category"]},"blog.show":{"uri":"blog\/{slug}","methods":["GET","HEAD"],"domain":"localhost","parameters":["slug"]},"page.show":{"uri":"page\/{slug}","methods":["GET","HEAD"],"domain":"localhost","parameters":["slug"]},"storage.local":{"uri":"storage\/{path}","methods":["GET","HEAD"],"wheres":{"path":".*"},"parameters":["path"]},"user.language.update":{"uri":"user\/language","methods":["PUT"]},"languages.index":{"uri":"languages","methods":["GET","HEAD"]},"dashboard":{"uri":"dashboard","methods":["GET","HEAD"]},"dashboard.stats":{"uri":"dashboard\/stats","methods":["GET","HEAD"]},"manager.dashboard":{"uri":"manager\/dashboard","methods":["GET","HEAD"]},"manager.analytics":{"uri":"manager\/analytics","methods":["GET","HEAD"]},"manager.branches.index":{"uri":"manager\/branches","methods":["GET","HEAD"]},"manager.branches.create":{"uri":"manager\/branches\/create","methods":["GET","HEAD"]},"manager.branches.store":{"uri":"manager\/branches","methods":["POST"]},"manager.branches.show":{"uri":"manager\/branches\/{branch}","methods":["GET","HEAD"],"parameters":["branch"],"bindings":{"branch":"id"}},"manager.branches.edit":{"uri":"manager\/branches\/{branch}\/edit","methods":["GET","HEAD"],"parameters":["branch"],"bindings":{"branch":"id"}},"manager.branches.update":{"uri":"manager\/branches\/{branch}","methods":["PUT","PATCH"],"parameters":["branch"],"bindings":{"branch":"id"}},"manager.branches.destroy":{"uri":"manager\/branches\/{branch}","methods":["DELETE"],"parameters":["branch"],"bindings":{"branch":"id"}},"manager.branches.toggle-status":{"uri":"manager\/branches\/{branch}\/toggle-status","methods":["POST"],"parameters":["branch"],"bindings":{"branch":"id"}},"manager.floors.index":{"uri":"manager\/floors","methods":["GET","HEAD"]},"manager.floors.create":{"uri":"manager\/floors\/create","methods":["GET","HEAD"]},"manager.floors.store":{"uri":"manager\/floors","methods":["POST"]},"manager.floors.show":{"uri":"manager\/floors\/{floor}","methods":["GET","HEAD"],"parameters":["floor"],"bindings":{"floor":"id"}},"manager.floors.edit":{"uri":"manager\/floors\/{floor}\/edit","methods":["GET","HEAD"],"parameters":["floor"],"bindings":{"floor":"id"}},"manager.floors.update":{"uri":"manager\/floors\/{floor}","methods":["PUT","PATCH"],"parameters":["floor"],"bindings":{"floor":"id"}},"manager.floors.destroy":{"uri":"manager\/floors\/{floor}","methods":["DELETE"],"parameters":["floor"],"bindings":{"floor":"id"}},"manager.floors.toggle-status":{"uri":"manager\/floors\/{floor}\/toggle-status","methods":["POST"],"parameters":["floor"]},"manager.floors.by-branch":{"uri":"manager\/api\/floors\/by-branch\/{branchId}","methods":["GET","HEAD"],"parameters":["branchId"]},"manager.tables.index":{"uri":"manager\/tables","methods":["GET","HEAD"]},"manager.tables.create":{"uri":"manager\/tables\/create","methods":["GET","HEAD"]},"manager.tables.store":{"uri":"manager\/tables","methods":["POST"]},"manager.tables.show":{"uri":"manager\/tables\/{table}","methods":["GET","HEAD"],"parameters":["table"],"bindings":{"table":"id"}},"manager.tables.edit":{"uri":"manager\/tables\/{table}\/edit","methods":["GET","HEAD"],"parameters":["table"],"bindings":{"table":"id"}},"manager.tables.update":{"uri":"manager\/tables\/{table}","methods":["PUT","PATCH"],"parameters":["table"],"bindings":{"table":"id"}},"manager.tables.destroy":{"uri":"manager\/tables\/{table}","methods":["DELETE"],"parameters":["table"],"bindings":{"table":"id"}},"manager.tables.layout":{"uri":"manager\/tables-layout","methods":["GET","HEAD"]},"manager.tables.toggle-status":{"uri":"manager\/tables\/{table}\/toggle-status","methods":["POST"],"parameters":["table"],"bindings":{"table":"id"}},"manager.tables.qr-code":{"uri":"manager\/tables\/{table}\/qr-code","methods":["GET","HEAD"],"parameters":["table"],"bindings":{"table":"id"}},"manager.tables.update-layout":{"uri":"manager\/tables\/update-layout","methods":["POST"]},"manager.tables.bulk-update":{"uri":"manager\/tables\/bulk-update","methods":["POST"]},"waiter.dashboard":{"uri":"waiter\/dashboard","methods":["GET","HEAD"]},"waiter.tables":{"uri":"waiter\/tables","methods":["GET","HEAD"]},"waiter.orders":{"uri":"waiter\/orders","methods":["GET","HEAD"]},"waiter.menu":{"uri":"waiter\/menu","methods":["GET","HEAD"]},"waiter.customers":{"uri":"waiter\/customers","methods":["GET","HEAD"]},"waiter.reservations":{"uri":"waiter\/reservations","methods":["GET","HEAD"]},"waiter.take-order":{"uri":"waiter\/take-order","methods":["GET","HEAD"]},"waiter.order-details":{"uri":"waiter\/orders\/{order}","methods":["GET","HEAD"],"parameters":["order"],"bindings":{"order":"id"}},"waiter.update-order-status":{"uri":"waiter\/orders\/{order}\/status","methods":["POST"],"parameters":["order"],"bindings":{"order":"id"}},"waiter.customer-info":{"uri":"waiter\/customers\/{customer}","methods":["GET","HEAD"],"parameters":["customer"],"bindings":{"customer":"id"}},"waiter.assign-table":{"uri":"waiter\/tables\/{table}\/assign","methods":["POST"],"parameters":["table"],"bindings":{"table":"id"}},"waiter.clear-table":{"uri":"waiter\/tables\/{table}\/clear","methods":["POST"],"parameters":["table"],"bindings":{"table":"id"}},"kitchen.dashboard":{"uri":"kitchen\/dashboard","methods":["GET","HEAD"]},"kitchen.display":{"uri":"kitchen\/display","methods":["GET","HEAD"]},"kitchen.start-preparation":{"uri":"kitchen\/items\/{orderItem}\/start","methods":["POST"],"parameters":["orderItem"],"bindings":{"orderItem":"id"}},"kitchen.mark-ready":{"uri":"kitchen\/items\/{orderItem}\/ready","methods":["POST"],"parameters":["orderItem"],"bindings":{"orderItem":"id"}},"kitchen.orders.show":{"uri":"kitchen\/orders\/{order}","methods":["GET","HEAD"],"parameters":["order"],"bindings":{"order":"id"}},"kitchen.bulk-update":{"uri":"kitchen\/items\/bulk-update","methods":["POST"]},"delivery.dashboard":{"uri":"delivery\/dashboard","methods":["GET","HEAD"]},"delivery.history":{"uri":"delivery\/history","methods":["GET","HEAD"]},"delivery.earnings":{"uri":"delivery\/earnings","methods":["GET","HEAD"]},"delivery.accept":{"uri":"delivery\/orders\/{deliveryOrder}\/accept","methods":["POST"],"parameters":["deliveryOrder"],"bindings":{"deliveryOrder":"id"}},"delivery.pickup":{"uri":"delivery\/orders\/{deliveryOrder}\/pickup","methods":["POST"],"parameters":["deliveryOrder"],"bindings":{"deliveryOrder":"id"}},"delivery.deliver":{"uri":"delivery\/orders\/{deliveryOrder}\/deliver","methods":["POST"],"parameters":["deliveryOrder"],"bindings":{"deliveryOrder":"id"}},"delivery.fail":{"uri":"delivery\/orders\/{deliveryOrder}\/fail","methods":["POST"],"parameters":["deliveryOrder"],"bindings":{"deliveryOrder":"id"}},"delivery.update-location":{"uri":"delivery\/personnel\/{deliveryPersonnel}\/update-location","methods":["POST"],"parameters":["deliveryPersonnel"]},"settings.index":{"uri":"settings","methods":["GET","HEAD"]},"settings.profile.update":{"uri":"settings\/profile","methods":["POST"]},"settings.password.update":{"uri":"settings\/password","methods":["POST"]},"settings.preferences.update":{"uri":"settings\/preferences","methods":["POST"]},"settings.notifications.update":{"uri":"settings\/notifications","methods":["POST"]},"settings.avatar.update":{"uri":"settings\/avatar","methods":["POST"]},"settings.activity":{"uri":"settings\/activity","methods":["GET","HEAD"]},"settings.export":{"uri":"settings\/export","methods":["GET","HEAD"]},"settings.account.delete":{"uri":"settings\/account","methods":["DELETE"]},"settings.security":{"uri":"settings\/security","methods":["GET","HEAD"]},"settings.two-factor.toggle":{"uri":"settings\/two-factor","methods":["POST"]},"restaurant.index":{"uri":"restaurant","methods":["GET","HEAD"]},"restaurant.profile.update":{"uri":"restaurant\/profile","methods":["POST"]},"restaurant.settings.update":{"uri":"restaurant\/settings","methods":["POST"]},"restaurant.status.update":{"uri":"restaurant\/status","methods":["POST"]},"restaurant.analytics":{"uri":"restaurant\/analytics","methods":["GET","HEAD"]},"restaurant.export":{"uri":"restaurant\/export","methods":["POST"]},"restaurant.qr-code":{"uri":"restaurant\/qr-code","methods":["GET","HEAD"]},"restaurant.branding.update":{"uri":"restaurant\/branding","methods":["POST"]},"restaurant.stats":{"uri":"restaurant\/stats","methods":["GET","HEAD"]},"categories.index":{"uri":"categories","methods":["GET","HEAD"]},"categories.create":{"uri":"categories\/create","methods":["GET","HEAD"]},"categories.store":{"uri":"categories","methods":["POST"]},"categories.show":{"uri":"categories\/{category}","methods":["GET","HEAD"],"parameters":["category"],"bindings":{"category":"id"}},"categories.edit":{"uri":"categories\/{category}\/edit","methods":["GET","HEAD"],"parameters":["category"],"bindings":{"category":"id"}},"categories.update":{"uri":"categories\/{category}","methods":["PUT","PATCH"],"parameters":["category"],"bindings":{"category":"id"}},"categories.destroy":{"uri":"categories\/{category}","methods":["DELETE"],"parameters":["category"],"bindings":{"category":"id"}},"categories.toggle-status":{"uri":"categories\/{category}\/toggle-status","methods":["POST"],"parameters":["category"],"bindings":{"category":"id"}},"categories.bulk-update":{"uri":"categories\/bulk-update","methods":["POST"]},"subcategories.index":{"uri":"subcategories","methods":["GET","HEAD"]},"subcategories.create":{"uri":"subcategories\/create","methods":["GET","HEAD"]},"subcategories.store":{"uri":"subcategories","methods":["POST"]},"subcategories.show":{"uri":"subcategories\/{subcategory}","methods":["GET","HEAD"],"parameters":["subcategory"],"bindings":{"subcategory":"id"}},"subcategories.edit":{"uri":"subcategories\/{subcategory}\/edit","methods":["GET","HEAD"],"parameters":["subcategory"],"bindings":{"subcategory":"id"}},"subcategories.update":{"uri":"subcategories\/{subcategory}","methods":["PUT","PATCH"],"parameters":["subcategory"],"bindings":{"subcategory":"id"}},"subcategories.destroy":{"uri":"subcategories\/{subcategory}","methods":["DELETE"],"parameters":["subcategory"],"bindings":{"subcategory":"id"}},"subcategories.by-category":{"uri":"categories\/{category}\/subcategories","methods":["GET","HEAD"],"parameters":["category"]},"subcategories.toggle-status":{"uri":"subcategories\/{subcategory}\/toggle-status","methods":["POST"],"parameters":["subcategory"],"bindings":{"subcategory":"id"}},"subcategories.bulk-update":{"uri":"subcategories\/bulk-update","methods":["POST"]},"menu-items.index":{"uri":"menu-items","methods":["GET","HEAD"]},"menu-items.create":{"uri":"menu-items\/create","methods":["GET","HEAD"]},"menu-items.store":{"uri":"menu-items","methods":["POST"]},"menu-items.show":{"uri":"menu-items\/{menu_item}","methods":["GET","HEAD"],"parameters":["menu_item"]},"menu-items.edit":{"uri":"menu-items\/{menu_item}\/edit","methods":["GET","HEAD"],"parameters":["menu_item"]},"menu-items.update":{"uri":"menu-items\/{menu_item}","methods":["PUT","PATCH"],"parameters":["menu_item"]},"menu-items.destroy":{"uri":"menu-items\/{menu_item}","methods":["DELETE"],"parameters":["menu_item"]},"menu-items.toggle-status":{"uri":"menu-items\/{menuItem}\/toggle-status","methods":["POST"],"parameters":["menuItem"],"bindings":{"menuItem":"slug"}},"menu-items.toggle-availability":{"uri":"menu-items\/{menuItem}\/toggle-availability","methods":["POST"],"parameters":["menuItem"],"bindings":{"menuItem":"slug"}},"menu-items.bulk-update":{"uri":"menu-items\/bulk-update","methods":["POST"]},"menu-items.duplicate":{"uri":"menu-items\/{menuItem}\/duplicate","methods":["POST"],"parameters":["menuItem"],"bindings":{"menuItem":"slug"}},"menu-item-addons.index":{"uri":"menu-item-addons","methods":["GET","HEAD"]},"menu-item-addons.create":{"uri":"menu-item-addons\/create","methods":["GET","HEAD"]},"menu-item-addons.store":{"uri":"menu-item-addons","methods":["POST"]},"menu-item-addons.show":{"uri":"menu-item-addons\/{menu_item_addon}","methods":["GET","HEAD"],"parameters":["menu_item_addon"]},"menu-item-addons.edit":{"uri":"menu-item-addons\/{menu_item_addon}\/edit","methods":["GET","HEAD"],"parameters":["menu_item_addon"]},"menu-item-addons.update":{"uri":"menu-item-addons\/{menu_item_addon}","methods":["PUT","PATCH"],"parameters":["menu_item_addon"]},"menu-item-addons.destroy":{"uri":"menu-item-addons\/{menu_item_addon}","methods":["DELETE"],"parameters":["menu_item_addon"]},"menu-item-addons.toggle-availability":{"uri":"menu-item-addons\/{menuItemAddon}\/toggle-availability","methods":["POST"],"parameters":["menuItemAddon"],"bindings":{"menuItemAddon":"id"}},"menu-item-addons.bulk-toggle-availability":{"uri":"menu-item-addons\/bulk-toggle-availability","methods":["POST"]},"menu-item-addons.api":{"uri":"menu-item-addons-api","methods":["GET","HEAD"]},"combo-menus.index":{"uri":"combo-menus","methods":["GET","HEAD"]},"combo-menus.create":{"uri":"combo-menus\/create","methods":["GET","HEAD"]},"combo-menus.store":{"uri":"combo-menus","methods":["POST"]},"combo-menus.show":{"uri":"combo-menus\/{combo_menu}","methods":["GET","HEAD"],"parameters":["combo_menu"]},"combo-menus.edit":{"uri":"combo-menus\/{combo_menu}\/edit","methods":["GET","HEAD"],"parameters":["combo_menu"]},"combo-menus.update":{"uri":"combo-menus\/{combo_menu}","methods":["PUT","PATCH"],"parameters":["combo_menu"]},"combo-menus.destroy":{"uri":"combo-menus\/{combo_menu}","methods":["DELETE"],"parameters":["combo_menu"]},"combo-menus.toggle-availability":{"uri":"combo-menus\/{comboMenu}\/toggle-availability","methods":["POST"],"parameters":["comboMenu"],"bindings":{"comboMenu":"id"}},"combo-menus.api":{"uri":"combo-menus-api","methods":["GET","HEAD"]},"floors.index":{"uri":"floors","methods":["GET","HEAD"]},"floors.create":{"uri":"floors\/create","methods":["GET","HEAD"]},"floors.store":{"uri":"floors","methods":["POST"]},"floors.show":{"uri":"floors\/{floor}","methods":["GET","HEAD"],"parameters":["floor"],"bindings":{"floor":"id"}},"floors.edit":{"uri":"floors\/{floor}\/edit","methods":["GET","HEAD"],"parameters":["floor"],"bindings":{"floor":"id"}},"floors.update":{"uri":"floors\/{floor}","methods":["PUT","PATCH"],"parameters":["floor"],"bindings":{"floor":"id"}},"floors.destroy":{"uri":"floors\/{floor}","methods":["DELETE"],"parameters":["floor"],"bindings":{"floor":"id"}},"floors.toggle-status":{"uri":"floors\/{floor}\/toggle-status","methods":["POST"],"parameters":["floor"]},"floors.by-branch":{"uri":"floors\/by-branch\/{branchId}","methods":["GET","HEAD"],"parameters":["branchId"]},"reservations.index":{"uri":"reservations","methods":["GET","HEAD"]},"reservations.create":{"uri":"reservations\/create","methods":["GET","HEAD"]},"reservations.store":{"uri":"reservations","methods":["POST"]},"reservations.show":{"uri":"reservations\/{reservation}","methods":["GET","HEAD"],"parameters":["reservation"],"bindings":{"reservation":"id"}},"reservations.edit":{"uri":"reservations\/{reservation}\/edit","methods":["GET","HEAD"],"parameters":["reservation"],"bindings":{"reservation":"id"}},"reservations.update":{"uri":"reservations\/{reservation}","methods":["PUT","PATCH"],"parameters":["reservation"],"bindings":{"reservation":"id"}},"reservations.destroy":{"uri":"reservations\/{reservation}","methods":["DELETE"],"parameters":["reservation"],"bindings":{"reservation":"id"}},"reservations.calendar":{"uri":"reservations-calendar","methods":["GET","HEAD"]},"reservations.confirm":{"uri":"reservations\/{reservation}\/confirm","methods":["POST"],"parameters":["reservation"],"bindings":{"reservation":"id"}},"reservations.check-in":{"uri":"reservations\/{reservation}\/check-in","methods":["POST"],"parameters":["reservation"],"bindings":{"reservation":"id"}},"reservations.complete":{"uri":"reservations\/{reservation}\/complete","methods":["POST"],"parameters":["reservation"],"bindings":{"reservation":"id"}},"reservations.cancel":{"uri":"reservations\/{reservation}\/cancel","methods":["POST"],"parameters":["reservation"],"bindings":{"reservation":"id"}},"reservations.no-show":{"uri":"reservations\/{reservation}\/no-show","methods":["POST"],"parameters":["reservation"],"bindings":{"reservation":"id"}},"reservations.available-slots":{"uri":"reservations\/available-slots","methods":["GET","HEAD"]},"waitlist.index":{"uri":"waitlist","methods":["GET","HEAD"]},"waitlist.create":{"uri":"waitlist\/create","methods":["GET","HEAD"]},"waitlist.store":{"uri":"waitlist","methods":["POST"]},"waitlist.show":{"uri":"waitlist\/{waitlist}","methods":["GET","HEAD"],"parameters":["waitlist"],"bindings":{"waitlist":"id"}},"waitlist.edit":{"uri":"waitlist\/{waitlist}\/edit","methods":["GET","HEAD"],"parameters":["waitlist"],"bindings":{"waitlist":"id"}},"waitlist.update":{"uri":"waitlist\/{waitlist}","methods":["PUT","PATCH"],"parameters":["waitlist"],"bindings":{"waitlist":"id"}},"waitlist.destroy":{"uri":"waitlist\/{waitlist}","methods":["DELETE"],"parameters":["waitlist"],"bindings":{"waitlist":"id"}},"waitlist.board":{"uri":"waitlist-board","methods":["GET","HEAD"]},"waitlist.notify":{"uri":"waitlist\/{waitlist}\/notify","methods":["POST"],"parameters":["waitlist"],"bindings":{"waitlist":"id"}},"waitlist.seat":{"uri":"waitlist\/{waitlist}\/seat","methods":["POST"],"parameters":["waitlist"],"bindings":{"waitlist":"id"}},"waitlist.mark-left":{"uri":"waitlist\/{waitlist}\/mark-left","methods":["POST"],"parameters":["waitlist"],"bindings":{"waitlist":"id"}},"waitlist.mark-no-show":{"uri":"waitlist\/{waitlist}\/mark-no-show","methods":["POST"],"parameters":["waitlist"],"bindings":{"waitlist":"id"}},"waitlist.update-wait-times":{"uri":"waitlist\/update-wait-times","methods":["POST"]},"waitlist.next-customer":{"uri":"waitlist\/next-customer","methods":["GET","HEAD"]},"waitlist.available-tables":{"uri":"waitlist\/available-tables","methods":["GET","HEAD"]},"test.pos":{"uri":"test-pos","methods":["GET","HEAD"]},"debug.branch-access":{"uri":"debug\/branch-access","methods":["GET","HEAD"]},"pos.index":{"uri":"pos","methods":["GET","HEAD"]},"pos.table-view":{"uri":"pos\/table-view","methods":["GET","HEAD"]},"pos.switch-branch":{"uri":"pos\/switch-branch","methods":["POST"]},"pos.orders.create":{"uri":"pos\/orders","methods":["POST"]},"pos.orders.add-item":{"uri":"pos\/orders\/{order}\/add-item","methods":["POST"],"parameters":["order"],"bindings":{"order":"id"}},"pos.order-items.update-quantity":{"uri":"pos\/order-items\/{orderItem}\/quantity","methods":["PUT"],"parameters":["orderItem"],"bindings":{"orderItem":"id"}},"pos.order-items.remove":{"uri":"pos\/order-items\/{orderItem}","methods":["DELETE"],"parameters":["orderItem"],"bindings":{"orderItem":"id"}},"pos.orders.apply-discount":{"uri":"pos\/orders\/{order}\/discount","methods":["POST"],"parameters":["order"],"bindings":{"order":"id"}},"pos.orders.process-payment":{"uri":"pos\/orders\/{order}\/payment","methods":["POST"],"parameters":["order"],"bindings":{"order":"id"}},"pos.orders.send-to-kitchen":{"uri":"pos\/orders\/{order}\/send-to-kitchen","methods":["POST"],"parameters":["order"],"bindings":{"order":"id"}},"pos.orders.close":{"uri":"pos\/orders\/{order}\/close","methods":["POST"],"parameters":["order"],"bindings":{"order":"id"}},"pos.orders.summary":{"uri":"pos\/orders\/{order}\/summary","methods":["GET","HEAD"],"parameters":["order"],"bindings":{"order":"id"}},"kitchen.index":{"uri":"kitchen","methods":["GET","HEAD"]},"kitchen.orders.update-status":{"uri":"kitchen\/orders\/{order}\/status","methods":["PUT"],"parameters":["order"],"bindings":{"order":"id"}},"kitchen.order-items.update-status":{"uri":"kitchen\/order-items\/{orderItem}\/status","methods":["PUT"],"parameters":["orderItem"],"bindings":{"orderItem":"id"}},"kitchen.orders.start-preparing":{"uri":"kitchen\/orders\/{order}\/start-preparing","methods":["POST"],"parameters":["order"],"bindings":{"order":"id"}},"kitchen.orders.mark-ready":{"uri":"kitchen\/orders\/{order}\/mark-ready","methods":["POST"],"parameters":["order"],"bindings":{"order":"id"}},"kitchen.orders.mark-served":{"uri":"kitchen\/orders\/{order}\/mark-served","methods":["POST"],"parameters":["order"],"bindings":{"order":"id"}},"kitchen.queue":{"uri":"kitchen\/queue","methods":["GET","HEAD"]},"orders.index":{"uri":"orders","methods":["GET","HEAD"]},"orders.create":{"uri":"orders\/create","methods":["GET","HEAD"]},"guest.orders.store":{"uri":"orders","methods":["POST"]},"orders.show":{"uri":"orders\/{order}","methods":["GET","HEAD"],"parameters":["order"],"bindings":{"order":"id"}},"orders.edit":{"uri":"orders\/{order}\/edit","methods":["GET","HEAD"],"parameters":["order"],"bindings":{"order":"id"}},"orders.update":{"uri":"orders\/{order}","methods":["PUT","PATCH"],"parameters":["order"],"bindings":{"order":"id"}},"orders.destroy":{"uri":"orders\/{order}","methods":["DELETE"],"parameters":["order"],"bindings":{"order":"id"}},"orders.update-status":{"uri":"orders\/{order}\/update-status","methods":["POST"],"parameters":["order"],"bindings":{"order":"id"}},"orders.update-payment":{"uri":"orders\/{order}\/update-payment","methods":["POST"],"parameters":["order"],"bindings":{"order":"id"}},"orders.invoice":{"uri":"orders\/{order}\/invoice","methods":["GET","HEAD"],"parameters":["order"],"bindings":{"order":"id"}},"orders.print":{"uri":"orders\/{order}\/print","methods":["GET","HEAD"],"parameters":["order"],"bindings":{"order":"id"}},"kitchen.orders.start-preparation":{"uri":"kitchen\/orders\/{order}\/start-preparation","methods":["POST"],"parameters":["order"],"bindings":{"order":"id"}},"customers.index":{"uri":"customers","methods":["GET","HEAD"]},"customers.create":{"uri":"customers\/create","methods":["GET","HEAD"]},"customers.store":{"uri":"customers","methods":["POST"]},"customers.show":{"uri":"customers\/{customer}","methods":["GET","HEAD"],"parameters":["customer"],"bindings":{"customer":"id"}},"customers.edit":{"uri":"customers\/{customer}\/edit","methods":["GET","HEAD"],"parameters":["customer"],"bindings":{"customer":"id"}},"customers.update":{"uri":"customers\/{customer}","methods":["PUT","PATCH"],"parameters":["customer"],"bindings":{"customer":"id"}},"customers.destroy":{"uri":"customers\/{customer}","methods":["DELETE"],"parameters":["customer"],"bindings":{"customer":"id"}},"customers.orders":{"uri":"customers\/{customer}\/orders","methods":["GET","HEAD"],"parameters":["customer"],"bindings":{"customer":"id"}},"staff.index":{"uri":"staff","methods":["GET","HEAD"]},"staff.create":{"uri":"staff\/create","methods":["GET","HEAD"]},"staff.store":{"uri":"staff","methods":["POST"]},"staff.show":{"uri":"staff\/{staff}","methods":["GET","HEAD"],"parameters":["staff"],"bindings":{"staff":"id"}},"staff.edit":{"uri":"staff\/{staff}\/edit","methods":["GET","HEAD"],"parameters":["staff"],"bindings":{"staff":"id"}},"staff.update":{"uri":"staff\/{staff}","methods":["PUT"],"parameters":["staff"],"bindings":{"staff":"id"}},"staff.destroy":{"uri":"staff\/{staff}","methods":["DELETE"],"parameters":["staff"],"bindings":{"staff":"id"}},"staff.toggle-status":{"uri":"staff\/{staff}\/toggle-status","methods":["POST"],"parameters":["staff"],"bindings":{"staff":"id"}},"staff.reset-password":{"uri":"staff\/{staff}\/reset-password","methods":["POST"],"parameters":["staff"],"bindings":{"staff":"id"}},"reports.index":{"uri":"reports","methods":["GET","HEAD"]},"reports.sales":{"uri":"reports\/sales","methods":["GET","HEAD"]},"reports.inventory":{"uri":"reports\/inventory","methods":["GET","HEAD"]},"reports.staff":{"uri":"reports\/staff","methods":["GET","HEAD"]},"reports.customer":{"uri":"reports\/customer","methods":["GET","HEAD"]},"reports.financial":{"uri":"reports\/financial","methods":["GET","HEAD"]},"reports.operational":{"uri":"reports\/operational","methods":["GET","HEAD"]},"reports.export":{"uri":"reports\/export","methods":["POST"]},"reports.schedule":{"uri":"reports\/schedule","methods":["POST"]},"reports.custom-builder":{"uri":"reports\/custom-builder","methods":["GET","HEAD"]},"reports.custom-generate":{"uri":"reports\/custom-generate","methods":["POST"]},"delivery.index":{"uri":"delivery","methods":["GET","HEAD"]},"delivery.map":{"uri":"delivery\/map","methods":["GET","HEAD"]},"delivery.assign-delivery-person":{"uri":"delivery\/orders\/{order}\/assign-delivery-person","methods":["POST"],"parameters":["order"],"bindings":{"order":"id"}},"delivery.auto-assign":{"uri":"delivery\/orders\/{order}\/auto-assign","methods":["POST"],"parameters":["order"],"bindings":{"order":"id"}},"delivery.mark-picked-up":{"uri":"delivery\/orders\/{order}\/mark-picked-up","methods":["POST"],"parameters":["order"],"bindings":{"order":"id"}},"delivery.mark-delivered":{"uri":"delivery\/orders\/{order}\/mark-delivered","methods":["POST"],"parameters":["order"],"bindings":{"order":"id"}},"delivery.create-routes":{"uri":"delivery\/create-routes","methods":["POST"]},"delivery.start-route":{"uri":"delivery\/routes\/{route}\/start","methods":["POST"],"parameters":["route"],"bindings":{"route":"id"}},"delivery.complete-route":{"uri":"delivery\/routes\/{route}\/complete","methods":["POST"],"parameters":["route"],"bindings":{"route":"id"}},"delivery.zone-for-address":{"uri":"delivery\/zone-for-address","methods":["GET","HEAD"]},"delivery.calculate-delivery":{"uri":"delivery\/calculate-delivery","methods":["GET","HEAD"]},"delivery-zones.index":{"uri":"delivery-zones","methods":["GET","HEAD"]},"delivery-zones.create":{"uri":"delivery-zones\/create","methods":["GET","HEAD"]},"delivery-zones.store":{"uri":"delivery-zones","methods":["POST"]},"delivery-zones.show":{"uri":"delivery-zones\/{delivery_zone}","methods":["GET","HEAD"],"parameters":["delivery_zone"]},"delivery-zones.edit":{"uri":"delivery-zones\/{delivery_zone}\/edit","methods":["GET","HEAD"],"parameters":["delivery_zone"]},"delivery-zones.update":{"uri":"delivery-zones\/{delivery_zone}","methods":["PUT","PATCH"],"parameters":["delivery_zone"]},"delivery-zones.destroy":{"uri":"delivery-zones\/{delivery_zone}","methods":["DELETE"],"parameters":["delivery_zone"]},"delivery-zones.map":{"uri":"delivery-zones-map","methods":["GET","HEAD"]},"delivery-zones.toggle-status":{"uri":"delivery-zones\/{deliveryZone}\/toggle-status","methods":["POST"],"parameters":["deliveryZone"],"bindings":{"deliveryZone":"id"}},"delivery-zones.reorder":{"uri":"delivery-zones\/reorder","methods":["POST"]},"delivery-zones.test-coordinates":{"uri":"delivery-zones\/{deliveryZone}\/test-coordinates","methods":["POST"],"parameters":["deliveryZone"],"bindings":{"deliveryZone":"id"}},"delivery-zones.coverage":{"uri":"delivery-zones-coverage","methods":["GET","HEAD"]},"delivery-zones.bulk-update":{"uri":"delivery-zones\/bulk-update","methods":["POST"]},"delivery-zones.export-geojson":{"uri":"delivery-zones\/export\/geojson","methods":["GET","HEAD"]},"delivery-personnel.index":{"uri":"delivery-personnel","methods":["GET","HEAD"]},"delivery-personnel.create":{"uri":"delivery-personnel\/create","methods":["GET","HEAD"]},"delivery-personnel.store":{"uri":"delivery-personnel","methods":["POST"]},"delivery-personnel.show":{"uri":"delivery-personnel\/{delivery_personnel}","methods":["GET","HEAD"],"parameters":["delivery_personnel"]},"delivery-personnel.edit":{"uri":"delivery-personnel\/{delivery_personnel}\/edit","methods":["GET","HEAD"],"parameters":["delivery_personnel"]},"delivery-personnel.update":{"uri":"delivery-personnel\/{delivery_personnel}","methods":["PUT","PATCH"],"parameters":["delivery_personnel"]},"delivery-personnel.destroy":{"uri":"delivery-personnel\/{delivery_personnel}","methods":["DELETE"],"parameters":["delivery_personnel"]},"delivery-personnel.toggle-status":{"uri":"delivery-personnel\/{deliveryPersonnel}\/toggle-status","methods":["POST"],"parameters":["deliveryPersonnel"],"bindings":{"deliveryPersonnel":"id"}},"delivery-personnel.update-status":{"uri":"delivery-personnel\/{deliveryPersonnel}\/update-status","methods":["POST"],"parameters":["deliveryPersonnel"],"bindings":{"deliveryPersonnel":"id"}},"delivery-personnel.available":{"uri":"delivery-personnel-available","methods":["GET","HEAD"]},"delivery-personnel.bulk-update":{"uri":"delivery-personnel\/bulk-update","methods":["POST"]},"expenses.index":{"uri":"expenses","methods":["GET","HEAD"]},"expenses.create":{"uri":"expenses\/create","methods":["GET","HEAD"]},"expenses.store":{"uri":"expenses","methods":["POST"]},"expenses.show":{"uri":"expenses\/{expense}","methods":["GET","HEAD"],"parameters":["expense"],"bindings":{"expense":"id"}},"expenses.edit":{"uri":"expenses\/{expense}\/edit","methods":["GET","HEAD"],"parameters":["expense"],"bindings":{"expense":"id"}},"expenses.update":{"uri":"expenses\/{expense}","methods":["PUT","PATCH"],"parameters":["expense"],"bindings":{"expense":"id"}},"expenses.destroy":{"uri":"expenses\/{expense}","methods":["DELETE"],"parameters":["expense"],"bindings":{"expense":"id"}},"expenses.approve":{"uri":"expenses\/{expense}\/approve","methods":["POST"],"parameters":["expense"],"bindings":{"expense":"id"}},"expenses.reject":{"uri":"expenses\/{expense}\/reject","methods":["POST"],"parameters":["expense"],"bindings":{"expense":"id"}},"expenses.mark-paid":{"uri":"expenses\/{expense}\/mark-paid","methods":["POST"],"parameters":["expense"],"bindings":{"expense":"id"}},"expenses.duplicate":{"uri":"expenses\/{expense}\/duplicate","methods":["POST"],"parameters":["expense"],"bindings":{"expense":"id"}},"expenses.bulk-update":{"uri":"expenses\/bulk-update","methods":["POST"]},"expense-categories.index":{"uri":"expense-categories","methods":["GET","HEAD"]},"expense-categories.create":{"uri":"expense-categories\/create","methods":["GET","HEAD"]},"expense-categories.store":{"uri":"expense-categories","methods":["POST"]},"expense-categories.show":{"uri":"expense-categories\/{expense_category}","methods":["GET","HEAD"],"parameters":["expense_category"]},"expense-categories.edit":{"uri":"expense-categories\/{expense_category}\/edit","methods":["GET","HEAD"],"parameters":["expense_category"]},"expense-categories.update":{"uri":"expense-categories\/{expense_category}","methods":["PUT","PATCH"],"parameters":["expense_category"]},"expense-categories.destroy":{"uri":"expense-categories\/{expense_category}","methods":["DELETE"],"parameters":["expense_category"]},"expense-categories.toggle-status":{"uri":"expense-categories\/{expenseCategory}\/toggle-status","methods":["POST"],"parameters":["expenseCategory"],"bindings":{"expenseCategory":"id"}},"expense-categories.reorder":{"uri":"expense-categories\/reorder","methods":["POST"]},"expense-categories.bulk-update":{"uri":"expense-categories\/bulk-update","methods":["POST"]},"expense-categories.hierarchy":{"uri":"expense-categories-hierarchy","methods":["GET","HEAD"]},"expense-categories.budget-overview":{"uri":"expense-categories-budget-overview","methods":["GET","HEAD"]},"vendors.index":{"uri":"vendors","methods":["GET","HEAD"]},"vendors.create":{"uri":"vendors\/create","methods":["GET","HEAD"]},"vendors.store":{"uri":"vendors","methods":["POST"]},"vendors.show":{"uri":"vendors\/{vendor}","methods":["GET","HEAD"],"parameters":["vendor"],"bindings":{"vendor":"id"}},"vendors.edit":{"uri":"vendors\/{vendor}\/edit","methods":["GET","HEAD"],"parameters":["vendor"],"bindings":{"vendor":"id"}},"vendors.update":{"uri":"vendors\/{vendor}","methods":["PUT","PATCH"],"parameters":["vendor"],"bindings":{"vendor":"id"}},"vendors.destroy":{"uri":"vendors\/{vendor}","methods":["DELETE"],"parameters":["vendor"],"bindings":{"vendor":"id"}},"vendors.toggle-status":{"uri":"vendors\/{vendor}\/toggle-status","methods":["POST"],"parameters":["vendor"],"bindings":{"vendor":"id"}},"vendors.update-rating":{"uri":"vendors\/{vendor}\/update-rating","methods":["POST"],"parameters":["vendor"],"bindings":{"vendor":"id"}},"vendors.bulk-update":{"uri":"vendors\/bulk-update","methods":["POST"]},"vendors.payment-summary":{"uri":"vendors-payment-summary","methods":["GET","HEAD"]},"budgets.index":{"uri":"budgets","methods":["GET","HEAD"]},"budgets.create":{"uri":"budgets\/create","methods":["GET","HEAD"]},"budgets.store":{"uri":"budgets","methods":["POST"]},"budgets.show":{"uri":"budgets\/{budget}","methods":["GET","HEAD"],"parameters":["budget"],"bindings":{"budget":"id"}},"budgets.edit":{"uri":"budgets\/{budget}\/edit","methods":["GET","HEAD"],"parameters":["budget"],"bindings":{"budget":"id"}},"budgets.update":{"uri":"budgets\/{budget}","methods":["PUT","PATCH"],"parameters":["budget"],"bindings":{"budget":"id"}},"budgets.destroy":{"uri":"budgets\/{budget}","methods":["DELETE"],"parameters":["budget"],"bindings":{"budget":"id"}},"budgets.dashboard":{"uri":"budgets-dashboard","methods":["GET","HEAD"]},"budgets.toggle-status":{"uri":"budgets\/{budget}\/toggle-status","methods":["POST"],"parameters":["budget"],"bindings":{"budget":"id"}},"budgets.create-next-period":{"uri":"budgets\/{budget}\/create-next-period","methods":["POST"],"parameters":["budget"],"bindings":{"budget":"id"}},"budgets.update-spent-amounts":{"uri":"budgets\/update-spent-amounts","methods":["POST"]},"budgets.alerts":{"uri":"budget-alerts","methods":["GET","HEAD"]},"budgets.mark-alert-read":{"uri":"budget-alerts\/{alert}\/mark-read","methods":["POST"],"parameters":["alert"]},"inventory.dashboard":{"uri":"inventory-dashboard","methods":["GET","HEAD"]},"inventory.index":{"uri":"inventory","methods":["GET","HEAD"]},"inventory.create":{"uri":"inventory\/create","methods":["GET","HEAD"]},"inventory.store":{"uri":"inventory","methods":["POST"]},"inventory.show":{"uri":"inventory\/{inventory}","methods":["GET","HEAD"],"parameters":["inventory"],"bindings":{"inventory":"id"}},"inventory.edit":{"uri":"inventory\/{inventory}\/edit","methods":["GET","HEAD"],"parameters":["inventory"],"bindings":{"inventory":"id"}},"inventory.update":{"uri":"inventory\/{inventory}","methods":["PUT","PATCH"],"parameters":["inventory"],"bindings":{"inventory":"id"}},"inventory.destroy":{"uri":"inventory\/{inventory}","methods":["DELETE"],"parameters":["inventory"],"bindings":{"inventory":"id"}},"inventory.adjust-stock":{"uri":"inventory\/{inventory}\/adjust-stock","methods":["POST"],"parameters":["inventory"],"bindings":{"inventory":"id"}},"inventory.add-stock":{"uri":"inventory\/{inventory}\/add-stock","methods":["POST"],"parameters":["inventory"],"bindings":{"inventory":"id"}},"inventory.remove-stock":{"uri":"inventory\/{inventory}\/remove-stock","methods":["POST"],"parameters":["inventory"],"bindings":{"inventory":"id"}},"inventory-categories.index":{"uri":"inventory-categories","methods":["GET","HEAD"]},"inventory-categories.create":{"uri":"inventory-categories\/create","methods":["GET","HEAD"]},"inventory-categories.store":{"uri":"inventory-categories","methods":["POST"]},"inventory-categories.show":{"uri":"inventory-categories\/{inventory_category}","methods":["GET","HEAD"],"parameters":["inventory_category"]},"inventory-categories.edit":{"uri":"inventory-categories\/{inventory_category}\/edit","methods":["GET","HEAD"],"parameters":["inventory_category"]},"inventory-categories.update":{"uri":"inventory-categories\/{inventory_category}","methods":["PUT","PATCH"],"parameters":["inventory_category"]},"inventory-categories.destroy":{"uri":"inventory-categories\/{inventory_category}","methods":["DELETE"],"parameters":["inventory_category"]},"inventory-categories.toggle-status":{"uri":"inventory-categories\/{inventoryCategory}\/toggle-status","methods":["POST"],"parameters":["inventoryCategory"],"bindings":{"inventoryCategory":"id"}},"inventory-categories.reorder":{"uri":"inventory-categories\/reorder","methods":["POST"]},"inventory-categories.bulk-update":{"uri":"inventory-categories\/bulk-update","methods":["POST"]},"inventory-categories.hierarchy":{"uri":"inventory-categories-hierarchy","methods":["GET","HEAD"]},"purchase-orders.index":{"uri":"purchase-orders","methods":["GET","HEAD"]},"purchase-orders.create":{"uri":"purchase-orders\/create","methods":["GET","HEAD"]},"purchase-orders.store":{"uri":"purchase-orders","methods":["POST"]},"purchase-orders.show":{"uri":"purchase-orders\/{purchase_order}","methods":["GET","HEAD"],"parameters":["purchase_order"]},"purchase-orders.edit":{"uri":"purchase-orders\/{purchase_order}\/edit","methods":["GET","HEAD"],"parameters":["purchase_order"]},"purchase-orders.update":{"uri":"purchase-orders\/{purchase_order}","methods":["PUT","PATCH"],"parameters":["purchase_order"]},"purchase-orders.destroy":{"uri":"purchase-orders\/{purchase_order}","methods":["DELETE"],"parameters":["purchase_order"]},"purchase-orders.approve":{"uri":"purchase-orders\/{purchaseOrder}\/approve","methods":["POST"],"parameters":["purchaseOrder"],"bindings":{"purchaseOrder":"id"}},"purchase-orders.send-to-vendor":{"uri":"purchase-orders\/{purchaseOrder}\/send-to-vendor","methods":["POST"],"parameters":["purchaseOrder"],"bindings":{"purchaseOrder":"id"}},"purchase-orders.mark-received":{"uri":"purchase-orders\/{purchaseOrder}\/mark-received","methods":["POST"],"parameters":["purchaseOrder"],"bindings":{"purchaseOrder":"id"}},"purchase-orders.partial-receive":{"uri":"purchase-orders\/{purchaseOrder}\/partial-receive","methods":["POST"],"parameters":["purchaseOrder"],"bindings":{"purchaseOrder":"id"}},"purchase-orders.cancel":{"uri":"purchase-orders\/{purchaseOrder}\/cancel","methods":["POST"],"parameters":["purchaseOrder"],"bindings":{"purchaseOrder":"id"}},"purchase-orders.duplicate":{"uri":"purchase-orders\/{purchaseOrder}\/duplicate","methods":["POST"],"parameters":["purchaseOrder"],"bindings":{"purchaseOrder":"id"}},"purchase-orders.generate-auto":{"uri":"purchase-orders-generate-auto","methods":["POST"]},"waste.dashboard":{"uri":"waste-dashboard","methods":["GET","HEAD"]},"waste.index":{"uri":"waste","methods":["GET","HEAD"]},"waste.create":{"uri":"waste\/create","methods":["GET","HEAD"]},"waste.store":{"uri":"waste","methods":["POST"]},"waste.show":{"uri":"waste\/{waste}","methods":["GET","HEAD"],"parameters":["waste"],"bindings":{"waste":"id"}},"waste.edit":{"uri":"waste\/{waste}\/edit","methods":["GET","HEAD"],"parameters":["waste"],"bindings":{"waste":"id"}},"waste.update":{"uri":"waste\/{waste}","methods":["PUT","PATCH"],"parameters":["waste"],"bindings":{"waste":"id"}},"waste.destroy":{"uri":"waste\/{waste}","methods":["DELETE"],"parameters":["waste"],"bindings":{"waste":"id"}},"waste.bulk-create-expired":{"uri":"waste-bulk-create-expired","methods":["POST"]},"waste.analytics":{"uri":"waste-analytics","methods":["GET","HEAD"]},"departments.index":{"uri":"departments","methods":["GET","HEAD"]},"departments.create":{"uri":"departments\/create","methods":["GET","HEAD"]},"departments.store":{"uri":"departments","methods":["POST"]},"departments.show":{"uri":"departments\/{department}","methods":["GET","HEAD"],"parameters":["department"],"bindings":{"department":"id"}},"departments.edit":{"uri":"departments\/{department}\/edit","methods":["GET","HEAD"],"parameters":["department"],"bindings":{"department":"id"}},"departments.update":{"uri":"departments\/{department}","methods":["PUT","PATCH"],"parameters":["department"],"bindings":{"department":"id"}},"departments.destroy":{"uri":"departments\/{department}","methods":["DELETE"],"parameters":["department"],"bindings":{"department":"id"}},"departments.toggle-status":{"uri":"departments\/{department}\/toggle-status","methods":["POST"],"parameters":["department"],"bindings":{"department":"id"}},"departments.reorder":{"uri":"departments\/reorder","methods":["POST"]},"departments.bulk-update":{"uri":"departments\/bulk-update","methods":["POST"]},"employees.index":{"uri":"employees","methods":["GET","HEAD"]},"employees.create":{"uri":"employees\/create","methods":["GET","HEAD"]},"employees.store":{"uri":"employees","methods":["POST"]},"employees.show":{"uri":"employees\/{employee}","methods":["GET","HEAD"],"parameters":["employee"],"bindings":{"employee":"id"}},"employees.edit":{"uri":"employees\/{employee}\/edit","methods":["GET","HEAD"],"parameters":["employee"],"bindings":{"employee":"id"}},"employees.update":{"uri":"employees\/{employee}","methods":["PUT","PATCH"],"parameters":["employee"],"bindings":{"employee":"id"}},"employees.destroy":{"uri":"employees\/{employee}","methods":["DELETE"],"parameters":["employee"],"bindings":{"employee":"id"}},"employees.toggle-status":{"uri":"employees\/{employee}\/toggle-status","methods":["POST"],"parameters":["employee"],"bindings":{"employee":"id"}},"employees.reset-password":{"uri":"employees\/{employee}\/reset-password","methods":["POST"],"parameters":["employee"],"bindings":{"employee":"id"}},"employees.performance":{"uri":"employees\/{employee}\/performance","methods":["GET","HEAD"],"parameters":["employee"],"bindings":{"employee":"id"}},"employees.export":{"uri":"employees\/export","methods":["POST"]},"employees.bulk-update":{"uri":"employees\/bulk-update","methods":["POST"]},"branches.index":{"uri":"branches","methods":["GET","HEAD"]},"branches.create":{"uri":"branches\/create","methods":["GET","HEAD"]},"branches.store":{"uri":"branches","methods":["POST"]},"branches.show":{"uri":"branches\/{branch}","methods":["GET","HEAD"],"parameters":["branch"],"bindings":{"branch":"id"}},"branches.edit":{"uri":"branches\/{branch}\/edit","methods":["GET","HEAD"],"parameters":["branch"],"bindings":{"branch":"id"}},"branches.update":{"uri":"branches\/{branch}","methods":["PUT","PATCH"],"parameters":["branch"],"bindings":{"branch":"id"}},"branches.destroy":{"uri":"branches\/{branch}","methods":["DELETE"],"parameters":["branch"],"bindings":{"branch":"id"}},"branches.toggle-status":{"uri":"branches\/{branch}\/toggle-status","methods":["POST"],"parameters":["branch"],"bindings":{"branch":"id"}},"branches.bulk-update":{"uri":"branches\/bulk-update","methods":["POST"]},"branches.api":{"uri":"branches-api","methods":["GET","HEAD"]},"test.translations":{"uri":"test-translations","methods":["GET","HEAD"]},"enhanced-employees.index":{"uri":"enhanced-employees","methods":["GET","HEAD"]},"enhanced-employees.create":{"uri":"enhanced-employees\/create","methods":["GET","HEAD"]},"enhanced-employees.store":{"uri":"enhanced-employees","methods":["POST"]},"enhanced-employees.show":{"uri":"enhanced-employees\/{enhanced_employee}","methods":["GET","HEAD"],"parameters":["enhanced_employee"]},"enhanced-employees.edit":{"uri":"enhanced-employees\/{enhanced_employee}\/edit","methods":["GET","HEAD"],"parameters":["enhanced_employee"]},"enhanced-employees.update":{"uri":"enhanced-employees\/{enhanced_employee}","methods":["PUT","PATCH"],"parameters":["enhanced_employee"]},"enhanced-employees.destroy":{"uri":"enhanced-employees\/{enhanced_employee}","methods":["DELETE"],"parameters":["enhanced_employee"]},"enhanced-employees.api":{"uri":"enhanced-employees-api","methods":["GET","HEAD"]},"employee-shifts.index":{"uri":"employee-shifts","methods":["GET","HEAD"]},"employee-shifts.create":{"uri":"employee-shifts\/create","methods":["GET","HEAD"]},"employee-shifts.store":{"uri":"employee-shifts","methods":["POST"]},"employee-shifts.show":{"uri":"employee-shifts\/{employee_shift}","methods":["GET","HEAD"],"parameters":["employee_shift"]},"employee-shifts.edit":{"uri":"employee-shifts\/{employee_shift}\/edit","methods":["GET","HEAD"],"parameters":["employee_shift"]},"employee-shifts.update":{"uri":"employee-shifts\/{employee_shift}","methods":["PUT","PATCH"],"parameters":["employee_shift"]},"employee-shifts.destroy":{"uri":"employee-shifts\/{employee_shift}","methods":["DELETE"],"parameters":["employee_shift"]},"employee-shifts.calendar":{"uri":"employee-shifts-calendar","methods":["GET","HEAD"]},"shifts.index":{"uri":"shifts","methods":["GET","HEAD"]},"shifts.create":{"uri":"shifts\/create","methods":["GET","HEAD"]},"shifts.store":{"uri":"shifts","methods":["POST"]},"shifts.show":{"uri":"shifts\/{shift}","methods":["GET","HEAD"],"parameters":["shift"],"bindings":{"shift":"id"}},"shifts.edit":{"uri":"shifts\/{shift}\/edit","methods":["GET","HEAD"],"parameters":["shift"],"bindings":{"shift":"id"}},"shifts.update":{"uri":"shifts\/{shift}","methods":["PUT","PATCH"],"parameters":["shift"],"bindings":{"shift":"id"}},"shifts.destroy":{"uri":"shifts\/{shift}","methods":["DELETE"],"parameters":["shift"],"bindings":{"shift":"id"}},"shifts.start":{"uri":"shifts\/{shift}\/start","methods":["POST"],"parameters":["shift"],"bindings":{"shift":"id"}},"shifts.end":{"uri":"shifts\/{shift}\/end","methods":["POST"],"parameters":["shift"],"bindings":{"shift":"id"}},"shifts.calendar":{"uri":"shifts\/calendar","methods":["GET","HEAD"]},"shifts.export":{"uri":"shifts\/export","methods":["POST"]},"shifts.stats":{"uri":"shifts\/stats","methods":["GET","HEAD"]},"time-entries.index":{"uri":"time-entries","methods":["GET","HEAD"]},"time-entries.create":{"uri":"time-entries\/create","methods":["GET","HEAD"]},"time-entries.store":{"uri":"time-entries","methods":["POST"]},"time-entries.show":{"uri":"time-entries\/{time_entry}","methods":["GET","HEAD"],"parameters":["time_entry"]},"time-entries.edit":{"uri":"time-entries\/{time_entry}\/edit","methods":["GET","HEAD"],"parameters":["time_entry"]},"time-entries.update":{"uri":"time-entries\/{time_entry}","methods":["PUT","PATCH"],"parameters":["time_entry"]},"time-entries.destroy":{"uri":"time-entries\/{time_entry}","methods":["DELETE"],"parameters":["time_entry"]},"time-entries.my-entries":{"uri":"time-entries\/my-entries","methods":["GET","HEAD"]},"time-entries.clock-in":{"uri":"time-entries\/clock-in","methods":["POST"]},"time-entries.clock-out":{"uri":"time-entries\/clock-out","methods":["POST"]},"time-entries.attendance-report":{"uri":"time-entries\/attendance-report","methods":["GET","HEAD"]},"tenant.asset":{"uri":"tenant-assets\/{path}","methods":["GET","HEAD"],"wheres":{"path":".*"},"parameters":["path"]},"media.index":{"uri":"media","methods":["GET","HEAD"]},"media.upload":{"uri":"media\/upload","methods":["POST"]},"media.picker":{"uri":"media\/picker","methods":["GET","HEAD"]},"media.search":{"uri":"media\/search","methods":["GET","HEAD"]},"media.create-folder":{"uri":"media\/create-folder","methods":["POST"]},"media.show":{"uri":"media\/{media}","methods":["GET","HEAD"],"parameters":["media"]},"media.update":{"uri":"media\/{media}","methods":["PUT"],"parameters":["media"],"bindings":{"media":"id"}},"media.destroy":{"uri":"media\/{media}","methods":["DELETE"],"parameters":["media"]},"media.bulk-delete":{"uri":"media\/bulk-delete","methods":["POST"]},"site-settings.index":{"uri":"site-settings","methods":["GET","HEAD"]},"site-settings.update":{"uri":"site-settings","methods":["PUT"]},"site-settings.public":{"uri":"site-settings\/public","methods":["GET","HEAD"]},"pages.index":{"uri":"pages","methods":["GET","HEAD"]},"pages.create":{"uri":"pages\/create","methods":["GET","HEAD"]},"pages.store":{"uri":"pages","methods":["POST"]},"pages.show":{"uri":"pages\/{page}","methods":["GET","HEAD"],"parameters":["page"],"bindings":{"page":"slug"}},"pages.edit":{"uri":"pages\/{page}\/edit","methods":["GET","HEAD"],"parameters":["page"],"bindings":{"page":"slug"}},"pages.update":{"uri":"pages\/{page}","methods":["PUT","PATCH"],"parameters":["page"],"bindings":{"page":"slug"}},"pages.destroy":{"uri":"pages\/{page}","methods":["DELETE"],"parameters":["page"],"bindings":{"page":"slug"}},"pages.toggle-status":{"uri":"pages\/{page}\/toggle-status","methods":["POST"],"parameters":["page"],"bindings":{"page":"slug"}},"settings.update.general":{"uri":"settings\/general","methods":["PUT"]},"settings.update.payment":{"uri":"settings\/payment","methods":["PUT"]},"settings.update.notification":{"uri":"settings\/notification","methods":["PUT"]},"guest.home":{"uri":"\/","methods":["GET","HEAD"]},"guest.menu":{"uri":"menu","methods":["GET","HEAD"]},"guest.menu.category":{"uri":"menu\/{category}","methods":["GET","HEAD"],"parameters":["category"],"bindings":{"category":"slug"}},"guest.menu.item":{"uri":"item\/{menuItem}","methods":["GET","HEAD"],"parameters":["menuItem"],"bindings":{"menuItem":"slug"}},"guest.language.switch":{"uri":"language\/switch","methods":["POST"]},"guest.checkout":{"uri":"checkout","methods":["GET","HEAD"]},"guest.page.show":{"uri":"{page}","methods":["GET","HEAD"],"parameters":["page"],"bindings":{"page":"slug"}},"guest.order.place":{"uri":"order","methods":["POST"]},"guest.order.track":{"uri":"order\/{order}\/track","methods":["GET","HEAD"],"parameters":["order"],"bindings":{"order":"id"}},"guest.table.qr":{"uri":"table\/{table}\/qr","methods":["GET","HEAD"],"parameters":["table"],"bindings":{"table":"id"}},"api.public.menu.index":{"uri":"api\/public\/menu","methods":["GET","HEAD"]},"api.public.menu.featured":{"uri":"api\/public\/menu\/featured","methods":["GET","HEAD"]},"api.public.menu.popular":{"uri":"api\/public\/menu\/popular","methods":["GET","HEAD"]},"api.public.menu.new":{"uri":"api\/public\/menu\/new","methods":["GET","HEAD"]},"api.public.menu.category":{"uri":"api\/public\/menu\/categories\/{category}","methods":["GET","HEAD"],"parameters":["category"],"bindings":{"category":"id"}},"api.public.menu.food":{"uri":"api\/public\/menu\/foods\/{food}","methods":["GET","HEAD"],"parameters":["food"],"bindings":{"food":"id"}},"api.public.categories.index":{"uri":"api\/public\/categories","methods":["GET","HEAD"]},"api.public.restaurant.show":{"uri":"api\/public\/restaurant","methods":["GET","HEAD"]},"api.public.orders.store":{"uri":"api\/public\/orders","methods":["POST"]},"api.public.orders.track":{"uri":"api\/public\/orders\/{order}\/track","methods":["GET","HEAD"],"parameters":["order"],"bindings":{"order":"id"}},"api.orders.index":{"uri":"api\/orders","methods":["GET","HEAD"]},"api.orders.store":{"uri":"api\/orders","methods":["POST"]},"api.orders.show":{"uri":"api\/orders\/{order}","methods":["GET","HEAD"],"parameters":["order"],"bindings":{"order":"id"}},"api.orders.update":{"uri":"api\/orders\/{order}","methods":["PUT","PATCH"],"parameters":["order"],"bindings":{"order":"id"}},"api.orders.destroy":{"uri":"api\/orders\/{order}","methods":["DELETE"],"parameters":["order"]},"api.orders.update-status":{"uri":"api\/orders\/{order}\/status","methods":["POST"],"parameters":["order"],"bindings":{"order":"id"}},"api.orders.cancel":{"uri":"api\/orders\/{order}\/cancel","methods":["POST"],"parameters":["order"],"bindings":{"order":"id"}},"api.orders.statistics":{"uri":"api\/orders\/statistics","methods":["GET","HEAD"]},"api.kitchen.orders":{"uri":"api\/kitchen\/orders","methods":["GET","HEAD"]},"api.customers.orders.active":{"uri":"api\/customers\/{customer}\/orders\/active","methods":["GET","HEAD"],"parameters":["customer"],"bindings":{"customer":"id"}},"api.foods.index":{"uri":"api\/foods","methods":["GET","HEAD"]},"api.foods.store":{"uri":"api\/foods","methods":["POST"]},"api.foods.show":{"uri":"api\/foods\/{food}","methods":["GET","HEAD"],"parameters":["food"],"bindings":{"food":"id"}},"api.foods.update":{"uri":"api\/foods\/{food}","methods":["PUT","PATCH"],"parameters":["food"],"bindings":{"food":"id"}},"api.foods.destroy":{"uri":"api\/foods\/{food}","methods":["DELETE"],"parameters":["food"],"bindings":{"food":"id"}},"api.foods.toggle-availability":{"uri":"api\/foods\/{food}\/toggle-availability","methods":["POST"],"parameters":["food"],"bindings":{"food":"id"}},"api.foods.bulk-availability":{"uri":"api\/foods\/bulk-availability","methods":["POST"]},"api.food-categories.index":{"uri":"api\/food-categories","methods":["GET","HEAD"]},"api.food-categories.store":{"uri":"api\/food-categories","methods":["POST"]},"api.food-categories.show":{"uri":"api\/food-categories\/{food_category}","methods":["GET","HEAD"],"parameters":["food_category"]},"api.food-categories.update":{"uri":"api\/food-categories\/{food_category}","methods":["PUT","PATCH"],"parameters":["food_category"]},"api.food-categories.destroy":{"uri":"api\/food-categories\/{food_category}","methods":["DELETE"],"parameters":["food_category"]},"api.customers.index":{"uri":"api\/customers","methods":["GET","HEAD"]},"api.customers.store":{"uri":"api\/customers","methods":["POST"]},"api.customers.show":{"uri":"api\/customers\/{customer}","methods":["GET","HEAD"],"parameters":["customer"],"bindings":{"customer":"id"}},"api.customers.update":{"uri":"api\/customers\/{customer}","methods":["PUT","PATCH"],"parameters":["customer"],"bindings":{"customer":"id"}},"api.customers.destroy":{"uri":"api\/customers\/{customer}","methods":["DELETE"],"parameters":["customer"],"bindings":{"customer":"id"}},"api.customers.statistics":{"uri":"api\/customers\/statistics","methods":["GET","HEAD"]},"api.customers.loyalty-points.add":{"uri":"api\/customers\/{customer}\/loyalty-points\/add","methods":["POST"],"parameters":["customer"],"bindings":{"customer":"id"}},"api.customers.loyalty-points.redeem":{"uri":"api\/customers\/{customer}\/loyalty-points\/redeem","methods":["POST"],"parameters":["customer"],"bindings":{"customer":"id"}},"api.customers.order-history":{"uri":"api\/customers\/{customer}\/order-history","methods":["GET","HEAD"],"parameters":["customer"],"bindings":{"customer":"id"}},"api.customers.addresses":{"uri":"api\/customers\/{customer}\/addresses","methods":["GET","HEAD"],"parameters":["customer"],"bindings":{"customer":"id"}},"api.customers.update-tier":{"uri":"api\/customers\/{customer}\/tier","methods":["POST"],"parameters":["customer"],"bindings":{"customer":"id"}},"api.customers.toggle-block":{"uri":"api\/customers\/{customer}\/toggle-block","methods":["POST"],"parameters":["customer"],"bindings":{"customer":"id"}},"api.customer-addresses.index":{"uri":"api\/customer-addresses","methods":["GET","HEAD"]},"api.customer-addresses.store":{"uri":"api\/customer-addresses","methods":["POST"]},"api.customer-addresses.show":{"uri":"api\/customer-addresses\/{customer_address}","methods":["GET","HEAD"],"parameters":["customer_address"]},"api.customer-addresses.update":{"uri":"api\/customer-addresses\/{customer_address}","methods":["PUT","PATCH"],"parameters":["customer_address"]},"api.customer-addresses.destroy":{"uri":"api\/customer-addresses\/{customer_address}","methods":["DELETE"],"parameters":["customer_address"]},"api.customer-addresses.set-default":{"uri":"api\/customer-addresses\/{address}\/set-default","methods":["POST"],"parameters":["address"]},"api.customer-addresses.validate":{"uri":"api\/customer-addresses\/validate","methods":["POST"]},"api.customer-addresses.delivery-addresses":{"uri":"api\/customer-addresses\/delivery-addresses","methods":["GET","HEAD"]},"api.customer-addresses.search":{"uri":"api\/customer-addresses\/search","methods":["GET","HEAD"]},"api.customer-addresses.suggestions":{"uri":"api\/customer-addresses\/suggestions","methods":["GET","HEAD"]},"api.customer-addresses.place-details":{"uri":"api\/customer-addresses\/place-details","methods":["GET","HEAD"]},"api.table-reservations.index":{"uri":"api\/table-reservations","methods":["GET","HEAD"]},"api.table-reservations.store":{"uri":"api\/table-reservations","methods":["POST"]},"api.table-reservations.show":{"uri":"api\/table-reservations\/{table_reservation}","methods":["GET","HEAD"],"parameters":["table_reservation"]},"api.table-reservations.update":{"uri":"api\/table-reservations\/{table_reservation}","methods":["PUT","PATCH"],"parameters":["table_reservation"]},"api.table-reservations.destroy":{"uri":"api\/table-reservations\/{table_reservation}","methods":["DELETE"],"parameters":["table_reservation"]},"api.table-reservations.cancel":{"uri":"api\/table-reservations\/{reservation}\/cancel","methods":["POST"],"parameters":["reservation"]},"api.table-reservations.available-tables":{"uri":"api\/table-reservations\/available-tables","methods":["GET","HEAD"]},"api.table-reservations.available-time-slots":{"uri":"api\/table-reservations\/available-time-slots","methods":["GET","HEAD"]},"api.delivery-drivers.index":{"uri":"api\/delivery-drivers","methods":["GET","HEAD"]},"api.delivery-drivers.store":{"uri":"api\/delivery-drivers","methods":["POST"]},"api.delivery-drivers.show":{"uri":"api\/delivery-drivers\/{delivery_driver}","methods":["GET","HEAD"],"parameters":["delivery_driver"]},"api.delivery-drivers.update":{"uri":"api\/delivery-drivers\/{delivery_driver}","methods":["PUT","PATCH"],"parameters":["delivery_driver"]},"api.delivery-drivers.destroy":{"uri":"api\/delivery-drivers\/{delivery_driver}","methods":["DELETE"],"parameters":["delivery_driver"]},"api.delivery-orders.index":{"uri":"api\/delivery-orders","methods":["GET","HEAD"]},"api.delivery-orders.store":{"uri":"api\/delivery-orders","methods":["POST"]},"api.delivery-orders.show":{"uri":"api\/delivery-orders\/{delivery_order}","methods":["GET","HEAD"],"parameters":["delivery_order"]},"api.delivery-orders.update":{"uri":"api\/delivery-orders\/{delivery_order}","methods":["PUT","PATCH"],"parameters":["delivery_order"]},"api.delivery-orders.destroy":{"uri":"api\/delivery-orders\/{delivery_order}","methods":["DELETE"],"parameters":["delivery_order"]},"api.delivery-orders.assign":{"uri":"api\/delivery-orders\/{deliveryOrder}\/assign","methods":["POST"],"parameters":["deliveryOrder"]},"api.delivery-orders.pickup":{"uri":"api\/delivery-orders\/{deliveryOrder}\/pickup","methods":["POST"],"parameters":["deliveryOrder"]},"api.delivery-orders.deliver":{"uri":"api\/delivery-orders\/{deliveryOrder}\/deliver","methods":["POST"],"parameters":["deliveryOrder"]},"api.delivery-orders.fail":{"uri":"api\/delivery-orders\/{deliveryOrder}\/fail","methods":["POST"],"parameters":["deliveryOrder"]},"api.delivery-drivers.update-location":{"uri":"api\/delivery-drivers\/{driver}\/location","methods":["POST"],"parameters":["driver"]},"api.food-reviews.index":{"uri":"api\/food-reviews","methods":["GET","HEAD"]},"api.food-reviews.store":{"uri":"api\/food-reviews","methods":["POST"]},"api.food-reviews.show":{"uri":"api\/food-reviews\/{food_review}","methods":["GET","HEAD"],"parameters":["food_review"]},"api.food-reviews.update":{"uri":"api\/food-reviews\/{food_review}","methods":["PUT","PATCH"],"parameters":["food_review"]},"api.food-reviews.destroy":{"uri":"api\/food-reviews\/{food_review}","methods":["DELETE"],"parameters":["food_review"]},"api.restaurant-reviews.index":{"uri":"api\/restaurant-reviews","methods":["GET","HEAD"]},"api.restaurant-reviews.store":{"uri":"api\/restaurant-reviews","methods":["POST"]},"api.restaurant-reviews.show":{"uri":"api\/restaurant-reviews\/{restaurant_review}","methods":["GET","HEAD"],"parameters":["restaurant_review"]},"api.restaurant-reviews.update":{"uri":"api\/restaurant-reviews\/{restaurant_review}","methods":["PUT","PATCH"],"parameters":["restaurant_review"]},"api.restaurant-reviews.destroy":{"uri":"api\/restaurant-reviews\/{restaurant_review}","methods":["DELETE"],"parameters":["restaurant_review"]},"api.food-reviews.approve":{"uri":"api\/food-reviews\/{review}\/approve","methods":["POST"],"parameters":["review"]},"api.food-reviews.reject":{"uri":"api\/food-reviews\/{review}\/reject","methods":["POST"],"parameters":["review"]},"api.food-reviews.feature":{"uri":"api\/food-reviews\/{review}\/feature","methods":["POST"],"parameters":["review"]},"api.food-reviews.respond":{"uri":"api\/food-reviews\/{review}\/respond","methods":["POST"],"parameters":["review"]},"api.coupons.index":{"uri":"api\/coupons","methods":["GET","HEAD"]},"api.coupons.store":{"uri":"api\/coupons","methods":["POST"]},"api.coupons.show":{"uri":"api\/coupons\/{coupon}","methods":["GET","HEAD"],"parameters":["coupon"],"bindings":{"coupon":"id"}},"api.coupons.update":{"uri":"api\/coupons\/{coupon}","methods":["PUT","PATCH"],"parameters":["coupon"],"bindings":{"coupon":"id"}},"api.coupons.destroy":{"uri":"api\/coupons\/{coupon}","methods":["DELETE"],"parameters":["coupon"],"bindings":{"coupon":"id"}},"api.promotions.index":{"uri":"api\/promotions","methods":["GET","HEAD"]},"api.promotions.store":{"uri":"api\/promotions","methods":["POST"]},"api.promotions.show":{"uri":"api\/promotions\/{promotion}","methods":["GET","HEAD"],"parameters":["promotion"],"bindings":{"promotion":"id"}},"api.promotions.update":{"uri":"api\/promotions\/{promotion}","methods":["PUT","PATCH"],"parameters":["promotion"],"bindings":{"promotion":"id"}},"api.promotions.destroy":{"uri":"api\/promotions\/{promotion}","methods":["DELETE"],"parameters":["promotion"],"bindings":{"promotion":"id"}},"api.coupons.validate":{"uri":"api\/coupons\/{coupon}\/validate","methods":["POST"],"parameters":["coupon"]},"api.coupons.usage-statistics":{"uri":"api\/coupons\/usage\/statistics","methods":["GET","HEAD"]},"api.analytics.dashboard":{"uri":"api\/analytics\/dashboard","methods":["GET","HEAD"]},"api.analytics.sales":{"uri":"api\/analytics\/sales","methods":["GET","HEAD"]},"api.analytics.orders":{"uri":"api\/analytics\/orders","methods":["GET","HEAD"]},"api.analytics.customers":{"uri":"api\/analytics\/customers","methods":["GET","HEAD"]},"api.analytics.menu-performance":{"uri":"api\/analytics\/menu-performance","methods":["GET","HEAD"]},"api.analytics.delivery-performance":{"uri":"api\/analytics\/delivery-performance","methods":["GET","HEAD"]}}};
if (typeof window !== 'undefined' && typeof window.Ziggy !== 'undefined') {
  Object.assign(Ziggy.routes, window.Ziggy.routes);
}
export { Ziggy };
