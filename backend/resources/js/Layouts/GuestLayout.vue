<template>
    <div class="min-h-screen bg-white">
        <!-- Navigation -->
        <nav class="bg-white shadow-lg sticky top-0 z-50">
            <div class="container mx-auto px-4">
                <div class="flex justify-between items-center py-4">
                    <!-- Logo -->
                    <Link :href="route('guest.home')" class="flex items-center space-x-2">
                        <div class="w-10 h-10 bg-orange-600 rounded-lg flex items-center justify-center">
                            <i class="fas fa-utensils text-white"></i>
                        </div>
                        <span class="text-xl font-bold text-gray-800">
                            {{ $page.props.siteSettings?.general?.site_name || $page.props.restaurant?.name || 'Restaurant' }}
                        </span>
                    </Link>

                    <!-- Desktop Navigation -->
                    <div class="hidden md:flex items-center space-x-8">
                        <Link
                            :href="route('guest.home')"
                            :class="[
                                'text-gray-700 hover:text-orange-600 transition-colors',
                                route().current('guest.home') ? 'text-orange-600 font-semibold' : ''
                            ]"
                        >
                            Home
                        </Link>
                        <Link
                            :href="route('guest.menu')"
                            :class="[
                                'text-gray-700 hover:text-orange-600 transition-colors',
                                route().current('guest.menu*') ? 'text-orange-600 font-semibold' : ''
                            ]"
                        >
                            Menu
                        </Link>
                        <!-- Cart Button -->
                        <button
                            @click="toggleCart"
                            class="relative bg-orange-600 text-white px-4 py-2 rounded-lg hover:bg-orange-700 transition-colors mr-4"
                        >
                            <i class="fas fa-shopping-cart"></i>
                            <span v-if="cartCount > 0" class="absolute -top-2 -right-2 bg-red-500 text-white text-xs rounded-full h-5 w-5 flex items-center justify-center">
                                {{ cartCount }}
                            </span>
                        </button>

                        <a
                            :href="`tel:${$page.props.siteSettings?.contact?.phone || $page.props.restaurant?.phone}`"
                            class="bg-orange-600 text-white px-6 py-2 rounded-lg hover:bg-orange-700 transition-colors"
                        >
                            Call Now
                        </a>
                    </div>

                    <!-- Mobile Menu Button -->
                    <button
                        @click="mobileMenuOpen = !mobileMenuOpen"
                        class="md:hidden text-gray-700 hover:text-orange-600"
                    >
                        <i class="fas fa-bars text-xl"></i>
                    </button>
                </div>

                <!-- Mobile Navigation -->
                <div v-show="mobileMenuOpen" class="md:hidden py-4 border-t">
                    <div class="flex flex-col space-y-4">
                        <Link
                            :href="route('guest.home')"
                            :class="[
                                'text-gray-700 hover:text-orange-600 transition-colors',
                                route().current('guest.home') ? 'text-orange-600 font-semibold' : ''
                            ]"
                            @click="mobileMenuOpen = false"
                        >
                            Home
                        </Link>
                        <Link
                            :href="route('guest.menu')"
                            :class="[
                                'text-gray-700 hover:text-orange-600 transition-colors',
                                route().current('guest.menu*') ? 'text-orange-600 font-semibold' : ''
                            ]"
                            @click="mobileMenuOpen = false"
                        >
                            Menu
                        </Link>
                        <!-- Mobile Cart Button -->
                        <button
                            @click="toggleCart"
                            class="relative bg-orange-600 text-white px-6 py-2 rounded-lg hover:bg-orange-700 transition-colors text-center"
                        >
                            <i class="fas fa-shopping-cart mr-2"></i>
                            Cart
                            <span v-if="cartCount > 0" class="ml-2 bg-red-500 text-white text-xs rounded-full px-2 py-1">
                                {{ cartCount }}
                            </span>
                        </button>

                        <a
                            :href="`tel:${$page.props.siteSettings?.contact?.phone || $page.props.restaurant?.phone}`"
                            class="bg-orange-600 text-white px-6 py-2 rounded-lg hover:bg-orange-700 transition-colors text-center"
                        >
                            Call Now
                        </a>
                    </div>
                </div>
            </div>
        </nav>

        <!-- Main Content -->
        <main>
            <slot />
        </main>

        <!-- Footer -->
        <footer class="bg-gray-800 text-white py-12">
            <div class="container mx-auto px-4">
                <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
                    <!-- Restaurant Info -->
                    <div>
                        <h3 class="text-xl font-semibold mb-4">
                            {{ $page.props.siteSettings?.general?.site_name || $page.props.restaurant?.name || 'Restaurant' }}
                        </h3>
                        <p class="text-gray-300 mb-4">
                            {{ $page.props.siteSettings?.general?.site_description || $page.props.restaurant?.description || 'Delicious food, great atmosphere' }}
                        </p>
                        <div class="flex space-x-4">
                            <a
                                v-if="$page.props.siteSettings?.social?.facebook_url"
                                :href="$page.props.siteSettings.social.facebook_url"
                                target="_blank"
                                class="text-gray-300 hover:text-white transition-colors"
                            >
                                <i class="fab fa-facebook text-xl"></i>
                            </a>
                            <a
                                v-if="$page.props.siteSettings?.social?.instagram_url"
                                :href="$page.props.siteSettings.social.instagram_url"
                                target="_blank"
                                class="text-gray-300 hover:text-white transition-colors"
                            >
                                <i class="fab fa-instagram text-xl"></i>
                            </a>
                            <a
                                v-if="$page.props.siteSettings?.social?.twitter_url"
                                :href="$page.props.siteSettings.social.twitter_url"
                                target="_blank"
                                class="text-gray-300 hover:text-white transition-colors"
                            >
                                <i class="fab fa-twitter text-xl"></i>
                            </a>
                            <a
                                v-if="$page.props.siteSettings?.social?.youtube_url"
                                :href="$page.props.siteSettings.social.youtube_url"
                                target="_blank"
                                class="text-gray-300 hover:text-white transition-colors"
                            >
                                <i class="fab fa-youtube text-xl"></i>
                            </a>
                            <a
                                v-if="$page.props.siteSettings?.social?.linkedin_url"
                                :href="$page.props.siteSettings.social.linkedin_url"
                                target="_blank"
                                class="text-gray-300 hover:text-white transition-colors"
                            >
                                <i class="fab fa-linkedin text-xl"></i>
                            </a>
                        </div>
                    </div>

                    <!-- Quick Links -->
                    <div>
                        <h3 class="text-xl font-semibold mb-4">Quick Links</h3>
                        <div class="space-y-2">
                            <Link
                                :href="route('guest.home')"
                                class="block text-gray-300 hover:text-white transition-colors"
                            >
                                Home
                            </Link>
                            <Link
                                :href="route('guest.menu')"
                                class="block text-gray-300 hover:text-white transition-colors"
                            >
                                Menu
                            </Link>
                            <Link
                                :href="route('guest.page.show', 'about-us')"
                                class="block text-gray-300 hover:text-white transition-colors"
                            >
                                About Us
                            </Link>
                            <Link
                                :href="route('guest.page.show', 'contact')"
                                class="block text-gray-300 hover:text-white transition-colors"
                            >
                                Contact
                            </Link>
                            <Link
                                :href="route('login')"
                                class="block text-gray-300 hover:text-white transition-colors"
                            >
                                Staff Login
                            </Link>
                        </div>
                    </div>

                    <!-- Contact Info -->
                    <div>
                        <h3 class="text-xl font-semibold mb-4">Contact Info</h3>
                        <div class="space-y-2 text-gray-300">
                            <div v-if="$page.props.siteSettings?.contact?.address" class="flex items-center space-x-2">
                                <i class="fas fa-map-marker-alt"></i>
                                <span>
                                    {{ $page.props.siteSettings.contact.address }}
                                    <span v-if="$page.props.siteSettings.contact.city">
                                        , {{ $page.props.siteSettings.contact.city }}
                                    </span>
                                    <span v-if="$page.props.siteSettings.contact.state">
                                        , {{ $page.props.siteSettings.contact.state }}
                                    </span>
                                    <span v-if="$page.props.siteSettings.contact.postal_code">
                                        {{ $page.props.siteSettings.contact.postal_code }}
                                    </span>
                                </span>
                            </div>
                            <div v-if="$page.props.siteSettings?.contact?.phone" class="flex items-center space-x-2">
                                <i class="fas fa-phone"></i>
                                <a
                                    :href="`tel:${$page.props.siteSettings.contact.phone}`"
                                    class="hover:text-white transition-colors"
                                >
                                    {{ $page.props.siteSettings.contact.phone }}
                                </a>
                            </div>
                            <div v-if="$page.props.siteSettings?.contact?.email" class="flex items-center space-x-2">
                                <i class="fas fa-envelope"></i>
                                <a
                                    :href="`mailto:${$page.props.siteSettings.contact.email}`"
                                    class="hover:text-white transition-colors"
                                >
                                    {{ $page.props.siteSettings.contact.email }}
                                </a>
                            </div>
                            <div v-if="$page.props.restaurant?.opening_time" class="flex items-center space-x-2">
                                <i class="fas fa-clock"></i>
                                <span>
                                    {{ $page.props.restaurant.opening_time }} -
                                    {{ $page.props.restaurant.closing_time }}
                                </span>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="border-t border-gray-700 mt-8 pt-8 text-center text-gray-400">
                    <p>&copy; {{ new Date().getFullYear() }} {{ $page.props.siteSettings?.general?.site_name || $page.props.restaurant?.name || 'Restaurant' }}. All rights reserved.</p>
                </div>
            </div>
        </footer>

        <!-- Cart Sidebar -->
        <CartSidebar />
    </div>
</template>

<script setup>
import { ref } from 'vue'
import { Link } from '@inertiajs/vue3'
import { useCart } from '@/Composables/useCart'
import CartSidebar from '@/Components/Guest/CartSidebar.vue'

const mobileMenuOpen = ref(false)

// Cart functionality
const { cartCount, toggleCart, isCartOpen } = useCart()
</script>
