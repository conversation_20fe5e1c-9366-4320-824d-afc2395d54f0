<script setup>
import { ref, onMounted, computed, watch } from 'vue';
import { Head, Link, router } from '@inertiajs/vue3';
import { useI18n } from 'vue-i18n';
import ApplicationMark from '@/Components/ApplicationMark.vue';
import Banner from '@/Components/Banner.vue';
import Dropdown from '@/Components/Dropdown.vue';
import DropdownLink from '@/Components/DropdownLink.vue';
import NavLink from '@/Components/NavLink.vue';
import ResponsiveNavLink from '@/Components/ResponsiveNavLink.vue';
import Calculator from '@/Components/Calculator.vue';
import LanguageSwitcher from '@/Components/LanguageSwitcher.vue';

defineProps({
    title: String,
});

// Vue i18n integration
const { t, locale } = useI18n();

// Reactive state
const showingNavigationDropdown = ref(false);
const sidebarCollapsed = ref(false);
const darkMode = ref(false);
const expandedMenus = ref(new Set());
const mobileSidebarOpen = ref(false);
const calculatorOpen = ref(false);

// Initialize theme and sidebar state from localStorage
onMounted(() => {
    // Load saved preferences
    const savedCollapsed = localStorage.getItem('sidebar-collapsed');
    const savedTheme = localStorage.getItem('theme');
    const savedExpandedMenus = localStorage.getItem('expanded-menus');

    if (savedCollapsed !== null) {
        sidebarCollapsed.value = JSON.parse(savedCollapsed);
    }

    if (savedTheme) {
        darkMode.value = savedTheme === 'dark';
        updateTheme();
    } else {
        // Check system preference
        darkMode.value = window.matchMedia('(prefers-color-scheme: dark)').matches;
        updateTheme();
    }

    if (savedExpandedMenus) {
        expandedMenus.value = new Set(JSON.parse(savedExpandedMenus));
    }

    // Listen for system theme changes
    window.matchMedia('(prefers-color-scheme: dark)').addEventListener('change', (e) => {
        if (!localStorage.getItem('theme')) {
            darkMode.value = e.matches;
            updateTheme();
        }
    });

    // Close mobile sidebar when clicking outside
    document.addEventListener('click', (e) => {
        if (mobileSidebarOpen.value && !e.target.closest('.mobile-sidebar') && !e.target.closest('.mobile-menu-button')) {
            mobileSidebarOpen.value = false;
        }
    });

    // Handle keyboard navigation
    document.addEventListener('keydown', (e) => {
        // Close mobile sidebar with Escape key
        if (e.key === 'Escape' && mobileSidebarOpen.value) {
            mobileSidebarOpen.value = false;
        }

        // Toggle sidebar with Ctrl+B
        if (e.ctrlKey && e.key === 'b') {
            e.preventDefault();
            toggleSidebar();
        }

        // Toggle theme with Ctrl+Shift+T
        if (e.ctrlKey && e.shiftKey && e.key === 'T') {
            e.preventDefault();
            toggleTheme();
        }
    });
});

// Watch for changes and save to localStorage
watch(sidebarCollapsed, (newValue) => {
    localStorage.setItem('sidebar-collapsed', JSON.stringify(newValue));
});

watch(darkMode, (newValue) => {
    localStorage.setItem('theme', newValue ? 'dark' : 'light');
    updateTheme();
});

watch(expandedMenus, (newValue) => {
    localStorage.setItem('expanded-menus', JSON.stringify([...newValue]));
}, { deep: true });

// Theme management
const updateTheme = () => {
    if (darkMode.value) {
        document.documentElement.classList.add('dark');
    } else {
        document.documentElement.classList.remove('dark');
    }
};

const toggleTheme = () => {
    darkMode.value = !darkMode.value;
};

const toggleSidebar = () => {
    sidebarCollapsed.value = !sidebarCollapsed.value;
};

const toggleCalculator = () => {
    calculatorOpen.value = !calculatorOpen.value;
};

const toggleMobileSidebar = () => {
    mobileSidebarOpen.value = !mobileSidebarOpen.value;
    showingNavigationDropdown.value = false;
};

const toggleMenu = (menuKey) => {
    if (expandedMenus.value.has(menuKey)) {
        expandedMenus.value.delete(menuKey);
    } else {
        expandedMenus.value.add(menuKey);
    }
};

const logout = () => {
    router.post(route('logout'));
};

// Enhanced navigation structure with tree hierarchy - using only existing routes
const navigationItems = [
    {
        name: 'dashboard',
        route: 'manager.dashboard',
        icon: 'M3 4a1 1 0 011-1h16a1 1 0 011 1v2.586a1 1 0 01-.293.707l-6.414 6.414a1 1 0 00-.293.707V17l-4 4v-6.586a1 1 0 00-.293-.707L3.293 7.293A1 1 0 013 6.586V4z',
        type: 'single'
    },
    {
        name: 'analytics',
        route: 'manager.analytics',
        icon: 'M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z',
        type: 'single'
    },
    {
        name: 'orders',
        icon: 'M16 11V7a4 4 0 00-8 0v4M5 9h14l1 12H4L5 9z',
        type: 'group',
        children: [
            { name: 'all_orders', route: 'orders.index', icon: 'M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2' },
            { name: 'kitchen_orders', route: 'kitchen.index', icon: 'M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z' },
            { name: 'create_order', route: 'orders.create', icon: 'M12 6v6m0 0v6m0-6h6m-6 0H6' }
        ]
    },
    {
        name: 'pos_system',
        icon: 'M9 17V7m0 10a2 2 0 01-2 2H5a2 2 0 01-2-2V7a2 2 0 012-2h2a2 2 0 012 2m0 10a2 2 0 002 2h2a2 2 0 002-2M7 7V3a2 2 0 012-2h6a2 2 0 012 2v4M7 21h10a2 2 0 002-2v-5a2 2 0 00-2-2H7a2 2 0 00-2 2v5a2 2 0 002 2z',
        type: 'group',
        children: [
            { name: 'pos_main', route: 'pos.index', icon: 'M9 17V7m0 10a2 2 0 01-2 2H5a2 2 0 01-2-2V7a2 2 0 012-2h2a2 2 0 012 2m0 10a2 2 0 002 2h2a2 2 0 002-2M7 7V3a2 2 0 012-2h6a2 2 0 012 2v4M7 21h10a2 2 0 002-2v-5a2 2 0 00-2-2H7a2 2 0 00-2 2v5a2 2 0 002 2z' },
            { name: 'pos_tables', route: 'pos.table-view', icon: 'M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2H5a2 2 0 00-2-2z' },
            { name: 'kitchen_display', route: 'kitchen.index', icon: 'M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z' }
        ]
    },
    {
        name: 'menu_management',
        icon: 'M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2',
        type: 'group',
        children: [
            { name: 'menu_items', route: 'menu-items.index', icon: 'M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2' },
            { name: 'categories', route: 'categories.index', icon: 'M19 11H5m14-4H5m14 8H5' }
        ]
    },
    {
        name: 'space_management',
        icon: 'M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4',
        type: 'group',
        children: [
            { name: 'branches', route: 'manager.branches.index', icon: 'M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4' },
            { name: 'floors', route: 'manager.floors.index', icon: 'M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2H5a2 2 0 00-2-2z' },
            { name: 'tables', route: 'manager.tables.index', icon: 'M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2H5a2 2 0 00-2-2z' }
        ]
    },
    {
        name: 'customers',
        route: 'customers.index',
        icon: 'M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z',
        type: 'single'
    },
    {
        name: 'staff_management',
        icon: 'M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 715.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 919.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z',
        type: 'group',
        children: [
            { name: 'staff', route: 'staff.index', icon: 'M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z' },
            { name: 'departments', route: 'departments.index', icon: 'M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4' },
            { name: 'employees', route: 'employees.index', icon: 'M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 715.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 919.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z' },
            { name: 'shifts', route: 'shifts.index', icon: 'M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z' }
        ]
    },
    {
        name: 'inventory_management',
        icon: 'M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4',
        type: 'group',
        children: [
            { name: 'inventory', route: 'inventory.index', icon: 'M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4' },
            { name: 'purchase_orders', route: 'purchase-orders.index', icon: 'M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-6 9l2 2 4-4' },
            { name: 'vendors', route: 'vendors.index', icon: 'M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4' }
        ]
    },
    {
        name: 'reports',
        icon: 'M9 17v-2m3 2v-4m3 2v-6m2 10H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z',
        type: 'group',
        children: [
            { name: 'all_reports', route: 'reports.index', icon: 'M9 17v-2m3 2v-4m3 2v-6m2 10H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z' },
            { name: 'sales_reports', route: 'reports.sales', icon: 'M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z' },
            { name: 'inventory_reports', route: 'reports.inventory', icon: 'M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4' },
            { name: 'staff_reports', route: 'reports.staff', icon: 'M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z' }
        ]
    },
    {
        name: 'human_resources',
        icon: 'M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 715.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 919.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z',
        type: 'group',
        children: [
            { name: 'hrm_dashboard', route: 'hrm.dashboard', icon: 'M3 4a1 1 0 011-1h16a1 1 0 011 1v2.586a1 1 0 01-.293.707l-6.414 6.414a1 1 0 00-.293.707V17l-4 4v-6.586a1 1 0 00-.293-.707L3.293 7.293A1 1 0 013 6.586V4z' },
            { name: 'leave_management', route: 'hrm.leave.index', icon: 'M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z' },
            { name: 'attendance', route: 'hrm.attendance.index', icon: 'M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z' },
            { name: 'payroll', route: 'hrm.payroll.index', icon: 'M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z' },
            { name: 'employee_loans', route: 'hrm.loans.index', icon: 'M2.25 8.25h19.5M2.25 9h19.5m-16.5 5.25h6m-6 2.25h3m-3.75 3h15a2.25 2.25 0 002.25-2.25V6.75A2.25 2.25 0 0019.5 4.5h-15a2.25 2.25 0 00-2.25 2.25v10.5A2.25 2.25 0 004.5 19.5z' },
            { name: 'leave_types', route: 'hrm.leave-types.index', icon: 'M9 12h3.75M9 15h3.75M9 18h3.75m3 .75H18a2.25 2.25 0 002.25-2.25V6.108c0-1.135-.845-2.098-1.976-2.192a48.424 48.424 0 00-1.123-.08m-5.801 0c-.065.21-.1.433-.1.664 0 .414.336.75.75.75h4.5a.75.75 0 00.75-.75 2.25 2.25 0 00-.1-.664m-5.8 0A2.251 2.251 0 0113.5 2.25H15c1.012 0 1.867.668 2.15 1.586m-5.8 0c-.376.023-.75.05-1.124.08C9.095 4.01 8.25 4.973 8.25 6.108V8.25m0 0H4.875c-.621 0-1.125.504-1.125 1.125v11.25c0 .621.504 1.125 1.125 1.125h9.75c.621 0 1.125-.504 1.125-1.125V9.375c0-.621-.504-1.125-1.125-1.125H8.25zM6.75 12h.008v.008H6.75V12zm0 3h.008v.008H6.75V15zm0 3h.008v.008H6.75V18z' }
        ]
    },
    {
        name: 'settings',
        icon: 'M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z M15 12a3 3 0 11-6 0 3 3 0 016 0z',
        type: 'group',
        children: [
            { name: 'site_settings', route: 'site-settings.index', icon: 'M9.594 3.94c.09-.542.56-.94 1.11-.94h2.593c.55 0 1.02.398 1.11.94l.213 1.281c.063.374.313.686.645.87.074.04.147.083.22.127.324.196.72.257 1.075.124l1.217-.456a1.125 1.125 0 011.37.49l1.296 2.247a1.125 1.125 0 01-.26 1.431l-1.003.827c-.293.24-.438.613-.431.992a6.759 6.759 0 010 .255c-.007.378.138.75.43.99l1.005.828c.424.35.534.954.26 1.43l-1.298 2.247a1.125 1.125 0 01-1.369.491l-1.217-.456c-.355-.133-.75-.072-1.076.124a6.57 6.57 0 01-.22.128c-.331.183-.581.495-.644.869l-.213 1.28c-.09.543-.56.941-1.11.941h-2.594c-.55 0-1.02-.398-1.11-.94l-.213-1.281c-.062-.374-.312-.686-.644-.87a6.52 6.52 0 01-.22-.127c-.325-.196-.72-.257-1.076-.124l-1.217.456a1.125 1.125 0 01-1.369-.49l-1.297-2.247a1.125 1.125 0 01.26-1.431l1.004-.827c.292-.24.437-.613.43-.992a6.932 6.932 0 010-.255c.007-.378-.138-.75-.43-.99l-1.004-.828a1.125 1.125 0 01-.26-1.43l1.297-2.247a1.125 1.125 0 011.37-.491l1.216.456c.356.133.751.072 1.076-.124.072-.044.146-.087.22-.128.332-.183.582-.495.644-.869l.214-1.281z M15 12a3 3 0 11-6 0 3 3 0 016 0z' },
            { name: 'payment_methods', route: 'payment-methods.index', icon: 'M2.25 8.25h19.5M2.25 9h19.5m-16.5 5.25h6m-6 2.25h3m-3.75 3h15a2.25 2.25 0 002.25-2.25V6.75A2.25 2.25 0 0019.5 4.5h-15a2.25 2.25 0 00-2.25 2.25v10.5A2.25 2.25 0 004.5 19.5z' },
            { name: 'pages', route: 'pages.index', icon: 'M19.5 14.25v-2.625a3.375 3.375 0 00-3.375-3.375h-1.5A1.125 1.125 0 0113.5 7.125v-1.5a3.375 3.375 0 00-3.375-3.375H8.25m0 12.75h7.5m-7.5 3H12M10.5 2.25H5.625c-.621 0-1.125.504-1.125 1.125v17.25c0 .621.504 1.125 1.125 1.125h12.75c.621 0 1.125-.504 1.125-1.125V11.25a9 9 0 00-9-9z' },
            { name: 'user_settings', route: 'settings.index', icon: 'M15.75 6a3.75 3.75 0 11-7.5 0 3.75 3.75 0 017.5 0zM4.501 20.118a7.5 7.5 0 0114.998 0A17.933 17.933 0 0112 21.75c-2.676 0-5.216-.584-7.499-1.632z' }
        ]
    }
];

// Helper functions
const isMenuExpanded = (menuKey) => expandedMenus.value.has(menuKey);

const isRouteActive = (routeName) => {
    return route().current(routeName + '*');
};

const isGroupActive = (group) => {
    if (group.type === 'single') {
        return isRouteActive(group.route);
    }
    return group.children?.some(child => isRouteActive(child.route));
};

const sidebarWidth = computed(() => {
    return sidebarCollapsed.value ? 'w-16' : 'w-64';
});
</script>

<template>
    <div>
        <Head :title="title" />
        <Banner />

        <div class="min-h-screen bg-gray-50 dark:bg-gray-900 transition-colors duration-200">
            <!-- Top Navigation -->
            <nav class="bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700 shadow-sm fixed w-full z-30 transition-colors duration-200">
                <div class="px-4 sm:px-6 lg:px-8">
                    <div class="flex justify-between h-16">
                        <div class="flex items-center">
                            <!-- Sidebar Toggle Button -->
                            <button
                                @click="toggleSidebar"
                                class="hidden lg:flex items-center justify-center w-10 h-10 rounded-lg text-gray-500 hover:text-gray-900 hover:bg-gray-100 dark:text-gray-400 dark:hover:text-white dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-indigo-500 transition-all duration-200 mr-4"
                                :title="sidebarCollapsed ? 'Expand sidebar (Ctrl+B)' : 'Collapse sidebar (Ctrl+B)'"
                                :aria-label="sidebarCollapsed ? 'Expand sidebar' : 'Collapse sidebar'"
                                aria-expanded="false"
                            >
                                <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24" aria-hidden="true">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16" />
                                </svg>
                            </button>

                            <!-- Logo -->
                            <div class="flex items-center">
                                <Link :href="route('manager.dashboard')" class="flex items-center">
                                    <ApplicationMark class="block h-8 w-auto" />
                                    <span v-if="!sidebarCollapsed || true" class="ml-2 text-xl font-semibold text-gray-900 dark:text-white">
                                        RestaurantPOS
                                    </span>
                                </Link>
                            </div>

                            <!-- Manager Badge -->
                            <div class="hidden sm:flex sm:items-center sm:ml-6">
                                <span class="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-indigo-100 text-indigo-800 dark:bg-indigo-900 dark:text-indigo-200">
                                    <svg class="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                                        <path fill-rule="evenodd" d="M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z" clip-rule="evenodd" />
                                    </svg>
                                    {{ t('dashboard_titles.manager_dashboard', 'Manager Dashboard') }}
                                </span>
                            </div>
                        </div>

                        <div class="hidden sm:flex sm:items-center sm:space-x-4">
                            <!-- Theme Toggle -->
                            <button
                                @click="toggleTheme"
                                class="p-2 rounded-lg text-gray-500 hover:text-gray-900 hover:bg-gray-100 dark:text-gray-400 dark:hover:text-white dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-indigo-500 transition-all duration-200"
                                :title="darkMode ? 'Switch to light mode (Ctrl+Shift+T)' : 'Switch to dark mode (Ctrl+Shift+T)'"
                                :aria-label="darkMode ? 'Switch to light mode' : 'Switch to dark mode'"
                            >
                                <svg v-if="darkMode" class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M10 2a1 1 0 011 1v1a1 1 0 11-2 0V3a1 1 0 011-1zm4 8a4 4 0 11-8 0 4 4 0 018 0zm-.464 4.95l.707.707a1 1 0 001.414-1.414l-.707-.707a1 1 0 00-1.414 1.414zm2.12-10.607a1 1 0 010 1.414l-.706.707a1 1 0 11-1.414-1.414l.707-.707a1 1 0 011.414 0zM17 11a1 1 0 100-2h-1a1 1 0 100 2h1zm-7 4a1 1 0 011 1v1a1 1 0 11-2 0v-1a1 1 0 011-1zM5.05 6.464A1 1 0 106.465 5.05l-.708-.707a1 1 0 00-1.414 1.414l.707.707zm1.414 8.486l-.707.707a1 1 0 01-1.414-1.414l.707-.707a1 1 0 011.414 1.414zM4 11a1 1 0 100-2H3a1 1 0 000 2h1z" clip-rule="evenodd" />
                                </svg>
                                <svg v-else class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                                    <path d="M17.293 13.293A8 8 0 016.707 2.707a8.001 8.001 0 1010.586 10.586z" />
                                </svg>
                            </button>

                            <!-- Calculator Toggle -->
                            <button
                                @click="toggleCalculator"
                                class="p-2 rounded-lg text-gray-500 hover:text-gray-900 hover:bg-gray-100 dark:text-gray-400 dark:hover:text-white dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-indigo-500 transition-all duration-200"
                                title="Open Calculator"
                                aria-label="Open Calculator"
                            >
                                <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M6 2a2 2 0 00-2 2v12a2 2 0 002 2h8a2 2 0 002-2V4a2 2 0 00-2-2H6zm1 2a1 1 0 000 2h6a1 1 0 100-2H7zm6 7a1 1 0 112 0v3a1 1 0 11-2 0v-3zm-3 3a1 1 0 100 2h.01a1 1 0 100-2H10zm-4 0a1 1 0 100 2h.01a1 1 0 100-2H6zm2-3a1 1 0 100 2h.01a1 1 0 100-2H8zm2 0a1 1 0 100 2h.01a1 1 0 100-2H10zm0-3a1 1 0 100 2h.01a1 1 0 100-2H10zm-2 0a1 1 0 100 2h.01a1 1 0 100-2H8zm-2 0a1 1 0 100 2h.01a1 1 0 100-2H6z" clip-rule="evenodd" />
                                </svg>
                            </button>

                            <!-- Language Switcher -->
                            <LanguageSwitcher />

                            <!-- Notifications -->
                            <button class="relative p-2 rounded-lg text-gray-500 hover:text-gray-900 hover:bg-gray-100 dark:text-gray-400 dark:hover:text-white dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-indigo-500 transition-all duration-200">
                                <span class="sr-only">View notifications</span>
                                <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 17h5l-5 5v-5zM4.868 19.718A8.966 8.966 0 0112 17a8.966 8.966 0 017.132 2.718M12 9a3 3 0 100-6 3 3 0 000 6zm0 0v8" />
                                </svg>
                                <!-- Notification badge -->
                                <span class="absolute top-1 right-1 block h-2 w-2 rounded-full bg-red-400 ring-2 ring-white dark:ring-gray-800"></span>
                            </button>

                            <!-- User Dropdown -->
                            <Dropdown align="right" width="48">
                                <template #trigger>
                                    <button class="flex items-center text-sm rounded-full focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 transition-all duration-200">
                                        <img class="h-8 w-8 rounded-full object-cover ring-2 ring-gray-200 dark:ring-gray-700" :src="$page.props.auth.user.profile_photo_url" :alt="$page.props.auth.user.name">
                                    </button>
                                </template>

                                <template #content>
                                    <div class="px-4 py-3 border-b border-gray-200 dark:border-gray-600">
                                        <p class="text-sm text-gray-900 dark:text-white font-medium">{{ $page.props.auth.user.name }}</p>
                                        <p class="text-sm text-gray-500 dark:text-gray-400">{{ $page.props.auth.user.email }}</p>
                                    </div>
                                    <DropdownLink :href="route('profile.show')">
                                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                                        </svg>
                                        {{ t('navigation.profile', 'Profile') }}
                                    </DropdownLink>
                                    <div class="border-t border-gray-200 dark:border-gray-600"></div>
                                    <form @submit.prevent="logout">
                                        <DropdownLink as="button">
                                            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1" />
                                            </svg>
                                            {{ t('actions.logout', 'Log Out') }}
                                        </DropdownLink>
                                    </form>
                                </template>
                            </Dropdown>
                        </div>

                        <!-- Mobile menu button -->
                        <div class="flex items-center sm:hidden">
                            <button
                                @click="toggleMobileSidebar"
                                class="mobile-menu-button inline-flex items-center justify-center p-2 rounded-md text-gray-400 hover:text-gray-500 hover:bg-gray-100 dark:hover:bg-gray-700 focus:outline-none focus:bg-gray-100 focus:text-gray-500 transition duration-150 ease-in-out"
                                :aria-label="mobileSidebarOpen ? 'Close main menu' : 'Open main menu'"
                                :aria-expanded="mobileSidebarOpen"
                            >
                                <span class="sr-only">{{ mobileSidebarOpen ? 'Close main menu' : 'Open main menu' }}</span>
                                <svg class="h-6 w-6" stroke="currentColor" fill="none" viewBox="0 0 24 24">
                                    <path :class="{'hidden': mobileSidebarOpen, 'inline-flex': !mobileSidebarOpen }" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16" />
                                    <path :class="{'hidden': !mobileSidebarOpen, 'inline-flex': mobileSidebarOpen }" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                                </svg>
                            </button>
                        </div>
                    </div>
                </div>
            </nav>

            <!-- Mobile Sidebar Overlay -->
            <div
                v-show="mobileSidebarOpen"
                class="fixed inset-0 z-40 lg:hidden"
                @click="mobileSidebarOpen = false"
            >
                <div class="fixed inset-0 bg-gray-600 bg-opacity-75 transition-opacity duration-300"></div>
            </div>

            <!-- Mobile Sidebar -->
            <div
                :class="[
                    'mobile-sidebar fixed inset-y-0 left-0 z-50 w-64 bg-white dark:bg-gray-800 transform transition-transform duration-300 ease-in-out lg:hidden',
                    mobileSidebarOpen ? 'translate-x-0' : '-translate-x-full'
                ]"
            >
                <div class="flex flex-col h-full">
                    <!-- Mobile sidebar header -->
                    <div class="flex items-center justify-between h-16 px-4 border-b border-gray-200 dark:border-gray-700">
                        <span class="text-lg font-semibold text-gray-900 dark:text-white">Menu</span>
                        <button
                            @click="mobileSidebarOpen = false"
                            class="p-2 rounded-md text-gray-400 hover:text-gray-500 hover:bg-gray-100 dark:hover:bg-gray-700"
                        >
                            <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                            </svg>
                        </button>
                    </div>

                    <!-- Mobile navigation -->
                    <nav class="flex-1 px-4 py-4 space-y-2 overflow-y-auto" role="navigation" aria-label="Mobile navigation menu">
                        <template v-for="item in navigationItems" :key="item.name">
                            <!-- Single menu item -->
                            <template v-if="item.type === 'single'">
                                <Link
                                    :href="route(item.route)"
                                    @click="mobileSidebarOpen = false"
                                    :class="[
                                        isRouteActive(item.route)
                                            ? 'bg-indigo-100 text-indigo-700 dark:bg-indigo-900 dark:text-indigo-200'
                                            : 'text-gray-600 hover:bg-gray-50 hover:text-gray-900 dark:text-gray-300 dark:hover:bg-gray-700 dark:hover:text-white',
                                        'group flex items-center px-3 py-2 text-sm font-medium rounded-lg transition-all duration-200'
                                    ]"
                                >
                                    <svg class="mr-3 h-5 w-5 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" :d="item.icon" />
                                    </svg>
                                    {{ t(`navigation.${item.name}`, item.name.charAt(0).toUpperCase() + item.name.slice(1)) }}
                                </Link>
                            </template>

                            <!-- Group menu item -->
                            <template v-else>
                                <div class="space-y-1">
                                    <button
                                        @click="toggleMenu(item.name)"
                                        :class="[
                                            isGroupActive(item)
                                                ? 'bg-indigo-50 text-indigo-700 dark:bg-indigo-900 dark:text-indigo-200'
                                                : 'text-gray-600 hover:bg-gray-50 hover:text-gray-900 dark:text-gray-300 dark:hover:bg-gray-700 dark:hover:text-white',
                                            'group w-full flex items-center justify-between px-3 py-2 text-sm font-medium rounded-lg transition-all duration-200'
                                        ]"
                                    >
                                        <div class="flex items-center">
                                            <svg class="mr-3 h-5 w-5 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" :d="item.icon" />
                                            </svg>
                                            {{ t(`navigation.${item.name}`, item.name.charAt(0).toUpperCase() + item.name.slice(1)) }}
                                        </div>
                                        <svg
                                            :class="[
                                                'h-4 w-4 transition-transform duration-200',
                                                isMenuExpanded(item.name) ? 'rotate-90' : ''
                                            ]"
                                            fill="none" stroke="currentColor" viewBox="0 0 24 24"
                                        >
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
                                        </svg>
                                    </button>

                                    <!-- Submenu -->
                                    <div
                                        v-show="isMenuExpanded(item.name)"
                                        class="ml-6 space-y-1"
                                    >
                                        <Link
                                            v-for="child in item.children"
                                            :key="child.name"
                                            :href="route(child.route)"
                                            @click="mobileSidebarOpen = false"
                                            :class="[
                                                isRouteActive(child.route)
                                                    ? 'bg-indigo-100 text-indigo-700 dark:bg-indigo-900 dark:text-indigo-200'
                                                    : 'text-gray-500 hover:bg-gray-50 hover:text-gray-700 dark:text-gray-400 dark:hover:bg-gray-700 dark:hover:text-gray-300',
                                                'group flex items-center px-3 py-2 text-sm rounded-lg transition-all duration-200'
                                            ]"
                                        >
                                            <svg class="mr-3 h-4 w-4 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" :d="child.icon" />
                                            </svg>
                                            {{ t(`navigation.${child.name}`, child.name.charAt(0).toUpperCase() + child.name.slice(1)) }}
                                        </Link>
                                    </div>
                                </div>
                            </template>
                        </template>
                    </nav>
                </div>
            </div>

            <div class="flex pt-16">
                <!-- Desktop Sidebar -->
                <div class="hidden lg:flex lg:flex-shrink-0">
                    <div
                        :class="[
                            'flex flex-col bg-white dark:bg-gray-800 border-r border-gray-200 dark:border-gray-700 transition-all duration-300',
                            sidebarWidth
                        ]"
                    >
                        <div class="flex flex-col flex-grow pt-5 pb-4 overflow-y-auto">
                            <nav class="mt-5 flex-1 px-2 space-y-1" role="navigation" aria-label="Main navigation menu">
                                <template v-for="item in navigationItems" :key="item.name">
                                    <!-- Single menu item -->
                                    <template v-if="item.type === 'single'">
                                        <Link
                                            :href="route(item.route)"
                                            :class="[
                                                isRouteActive(item.route)
                                                    ? 'bg-indigo-100 text-indigo-700 dark:bg-indigo-900 dark:text-indigo-200'
                                                    : 'text-gray-600 hover:bg-gray-50 hover:text-gray-900 dark:text-gray-300 dark:hover:bg-gray-700 dark:hover:text-white',
                                                'group flex items-center px-3 py-2 text-sm font-medium rounded-lg transition-all duration-200',
                                                sidebarCollapsed ? 'justify-center' : ''
                                            ]"
                                            :title="sidebarCollapsed ? t(`navigation.${item.name}`, item.name.charAt(0).toUpperCase() + item.name.slice(1)) : ''"
                                        >
                                            <svg class="h-5 w-5 flex-shrink-0" :class="sidebarCollapsed ? '' : 'mr-3'" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" :d="item.icon" />
                                            </svg>
                                            <span v-if="!sidebarCollapsed" class="truncate">
                                                {{ t(`navigation.${item.name}`, item.name.charAt(0).toUpperCase() + item.name.slice(1)) }}
                                            </span>
                                        </Link>
                                    </template>

                                    <!-- Group menu item -->
                                    <template v-else>
                                        <div class="space-y-1">
                                            <button
                                                @click="toggleMenu(item.name)"
                                                :class="[
                                                    isGroupActive(item)
                                                        ? 'bg-indigo-50 text-indigo-700 dark:bg-indigo-900 dark:text-indigo-200'
                                                        : 'text-gray-600 hover:bg-gray-50 hover:text-gray-900 dark:text-gray-300 dark:hover:bg-gray-700 dark:hover:text-white',
                                                    'group w-full flex items-center px-3 py-2 text-sm font-medium rounded-lg transition-all duration-200',
                                                    sidebarCollapsed ? 'justify-center' : 'justify-between'
                                                ]"
                                                :title="sidebarCollapsed ? t(`navigation.${item.name}`, item.name.charAt(0).toUpperCase() + item.name.slice(1)) : ''"
                                            >
                                                <div class="flex items-center">
                                                    <svg class="h-5 w-5 flex-shrink-0" :class="sidebarCollapsed ? '' : 'mr-3'" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" :d="item.icon" />
                                                    </svg>
                                                    <span v-if="!sidebarCollapsed" class="truncate">
                                                        {{ t(`navigation.${item.name}`, item.name.charAt(0).toUpperCase() + item.name.slice(1)) }}
                                                    </span>
                                                </div>
                                                <svg
                                                    v-if="!sidebarCollapsed"
                                                    :class="[
                                                        'h-4 w-4 transition-transform duration-200',
                                                        isMenuExpanded(item.name) ? 'rotate-90' : ''
                                                    ]"
                                                    fill="none" stroke="currentColor" viewBox="0 0 24 24"
                                                >
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
                                                </svg>
                                            </button>

                                            <!-- Submenu -->
                                            <div
                                                v-show="isMenuExpanded(item.name) && !sidebarCollapsed"
                                                class="ml-6 space-y-1"
                                            >
                                                <Link
                                                    v-for="child in item.children"
                                                    :key="child.name"
                                                    :href="route(child.route)"
                                                    :class="[
                                                        isRouteActive(child.route)
                                                            ? 'bg-indigo-100 text-indigo-700 dark:bg-indigo-900 dark:text-indigo-200'
                                                            : 'text-gray-500 hover:bg-gray-50 hover:text-gray-700 dark:text-gray-400 dark:hover:bg-gray-700 dark:hover:text-gray-300',
                                                        'group flex items-center px-3 py-2 text-sm rounded-lg transition-all duration-200'
                                                    ]"
                                                >
                                                    <svg class="mr-3 h-4 w-4 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" :d="child.icon" />
                                                    </svg>
                                                    {{ t(`navigation.${child.name}`, child.name.charAt(0).toUpperCase() + child.name.slice(1)) }}
                                                </Link>
                                            </div>
                                        </div>
                                    </template>
                                </template>
                            </nav>
                        </div>
                    </div>
                </div>

                <!-- Main content -->
                <div class="flex flex-col flex-1 overflow-hidden">
                    <!-- Page Heading -->
                    <header v-if="$slots.header" class="bg-white dark:bg-gray-800 shadow-sm border-b border-gray-200 dark:border-gray-700">
                        <div class="max-w-7xl mx-auto py-6 px-4 sm:px-6 lg:px-8">
                            <slot name="header" />
                        </div>
                    </header>

                    <!-- Page Content -->
                    <main class="flex-1 relative overflow-y-auto focus:outline-none bg-gray-50 dark:bg-gray-900">
                        <div class="py-6">
                            <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                                <slot />
                            </div>
                        </div>
                    </main>
                </div>
            </div>
        </div>
    </div>

    <!-- Calculator Modal -->
    <Calculator :is-open="calculatorOpen" @close="calculatorOpen = false" />
</template>

<style scoped>
/* Custom scrollbar for sidebar */
.overflow-y-auto::-webkit-scrollbar {
    width: 6px;
}

.overflow-y-auto::-webkit-scrollbar-track {
    @apply bg-gray-100 dark:bg-gray-700;
}

.overflow-y-auto::-webkit-scrollbar-thumb {
    @apply bg-gray-300 dark:bg-gray-600 rounded-full;
}

.overflow-y-auto::-webkit-scrollbar-thumb:hover {
    @apply bg-gray-400 dark:bg-gray-500;
}

/* Smooth transitions for theme switching */
* {
    transition: background-color 0.2s ease-in-out, border-color 0.2s ease-in-out, color 0.2s ease-in-out;
}

/* Mobile sidebar slide animation */
.mobile-sidebar {
    box-shadow: 0 10px 25px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
}

/* Tooltip styles for collapsed sidebar */
.group:hover .tooltip {
    @apply opacity-100 visible;
}

.tooltip {
    @apply opacity-0 invisible absolute left-full ml-2 px-2 py-1 bg-gray-900 text-white text-xs rounded whitespace-nowrap z-50 transition-all duration-200;
}

/* Focus styles for accessibility */
button:focus,
a:focus {
    @apply outline-none ring-2 ring-indigo-500 ring-offset-2 dark:ring-offset-gray-800;
}

/* Animation for menu expansion */
@keyframes slideDown {
    from {
        opacity: 0;
        transform: translateY(-10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.submenu-enter-active {
    animation: slideDown 0.2s ease-out;
}

/* Responsive design improvements */
@media (max-width: 1024px) {
    .sidebar-collapsed {
        width: 0;
        overflow: hidden;
    }
}

/* Dark mode specific styles */
.dark .mobile-sidebar {
    box-shadow: 0 10px 25px -3px rgba(0, 0, 0, 0.3), 0 4px 6px -2px rgba(0, 0, 0, 0.2);
}

/* Notification badge pulse animation */
@keyframes pulse {
    0%, 100% {
        opacity: 1;
    }
    50% {
        opacity: 0.5;
    }
}

.notification-badge {
    animation: pulse 2s infinite;
}

/* Hover effects for better UX */
.nav-item {
    position: relative;
    overflow: hidden;
}

.nav-item::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
    transition: left 0.5s;
}

.nav-item:hover::before {
    left: 100%;
}

/* Improved focus indicators */
.focus-visible {
    @apply ring-2 ring-indigo-500 ring-offset-2 dark:ring-offset-gray-800;
}

/* Loading states */
.loading {
    @apply opacity-50 pointer-events-none;
}

/* Micro-interactions */
.button-press {
    transform: scale(0.98);
    transition: transform 0.1s ease-in-out;
}

.button-press:active {
    transform: scale(0.95);
}
</style>