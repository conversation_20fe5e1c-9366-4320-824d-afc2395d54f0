<script setup>
import { ref, onMounted } from 'vue';
import { Head, Link, router } from '@inertiajs/vue3';
import { useI18n } from 'vue-i18n';
import ApplicationMark from '@/Components/ApplicationMark.vue';
import Banner from '@/Components/Banner.vue';
import Dropdown from '@/Components/Dropdown.vue';
import DropdownLink from '@/Components/DropdownLink.vue';
import LanguageSwitcher from '@/Components/LanguageSwitcher.vue';

defineProps({
    title: String,
});

// Vue i18n integration
const { t, locale } = useI18n();

const logout = () => {
    router.post(route('logout'));
};

// Kitchen navigation items - minimal for kitchen environment
const navigationItems = [
    { name: 'dashboard', route: 'kitchen.index', icon: '🏠' },
    { name: 'orders', route: 'kitchen.index', icon: '🍳' },
    { name: 'menu', route: 'menu-items.index', icon: '📋' },
    { name: 'inventory', route: 'inventory.index', icon: '📦' },
];
</script>

<template>
    <div>
        <Head :title="title" />
        <Banner />

        <div class="min-h-screen bg-orange-50 dark:bg-gray-900">
            <!-- Top Navigation Bar - Optimized for kitchen environment -->
            <nav class="bg-orange-600 dark:bg-orange-800 shadow-lg">
                <div class="max-w-full mx-auto px-4 sm:px-6 lg:px-8">
                    <div class="flex justify-between h-20"> <!-- Taller for kitchen visibility -->
                        <div class="flex items-center">
                            <!-- Logo -->
                            <div class="shrink-0 flex items-center">
                                <Link :href="route('kitchen.dashboard')">
                                    <ApplicationMark class="block h-12 w-auto text-white" />
                                </Link>
                            </div>

                            <!-- Kitchen Badge -->
                            <div class="flex items-center ml-6">
                                <span class="inline-flex items-center px-4 py-2 rounded-full text-lg font-bold bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-200">
                                    👨‍🍳 {{ t('dashboard_titles.kitchen_dashboard', 'Kitchen Dashboard') }}
                                </span>
                            </div>

                            <!-- Order Status Indicators -->
                            <div class="hidden lg:flex lg:items-center lg:ml-8 space-x-6">
                                <div class="flex items-center px-4 py-2 bg-red-100 text-red-800 rounded-lg text-lg font-bold">
                                    <span class="mr-2">🔥</span>
                                    <span>3 {{ t('order_management.pending', 'Pending') }}</span>
                                </div>
                                <div class="flex items-center px-4 py-2 bg-yellow-100 text-yellow-800 rounded-lg text-lg font-bold">
                                    <span class="mr-2">🍳</span>
                                    <span>5 {{ t('order_management.preparing', 'Cooking') }}</span>
                                </div>
                                <div class="flex items-center px-4 py-2 bg-green-100 text-green-800 rounded-lg text-lg font-bold">
                                    <span class="mr-2">✅</span>
                                    <span>2 {{ t('order_management.ready', 'Ready') }}</span>
                                </div>
                            </div>
                        </div>

                        <div class="flex items-center space-x-4">
                            <!-- Current Time -->
                            <div class="hidden sm:flex items-center px-4 py-2 bg-white text-orange-600 rounded-lg text-lg font-bold">
                                <span class="mr-2">🕐</span>
                                <span id="current-time">{{ new Date().toLocaleTimeString() }}</span>
                            </div>

                            <!-- Language Switcher -->
                            <LanguageSwitcher />

                            <!-- User Dropdown -->
                            <Dropdown align="right" width="48">
                                <template #trigger>
                                    <button class="flex text-sm border-2 border-white rounded-full focus:outline-none focus:border-gray-300 transition">
                                        <img class="h-10 w-10 rounded-full object-cover" :src="$page.props.auth.user.profile_photo_url" :alt="$page.props.auth.user.name">
                                    </button>
                                </template>

                                <template #content>
                                    <div class="block px-4 py-2 text-xs text-gray-400">
                                        {{ $page.props.auth.user.name }}
                                    </div>
                                    <DropdownLink :href="route('profile.show')">
                                        {{ t('navigation.profile', 'Profile') }}
                                    </DropdownLink>
                                    <div class="border-t border-gray-200 dark:border-gray-600"></div>
                                    <form @submit.prevent="logout">
                                        <DropdownLink as="button">
                                            {{ t('actions.logout', 'Log Out') }}
                                        </DropdownLink>
                                    </form>
                                </template>
                            </Dropdown>
                        </div>
                    </div>
                </div>
            </nav>

            <!-- Main Content Area - Full width for kitchen display -->
            <div class="flex">
                <!-- Minimal Sidebar -->
                <div class="hidden lg:flex lg:flex-shrink-0">
                    <div class="flex flex-col w-20"> <!-- Very narrow sidebar -->
                        <div class="flex flex-col flex-grow bg-white dark:bg-gray-800 pt-5 pb-4 overflow-y-auto border-r border-gray-200 dark:border-gray-700">
                            <nav class="mt-5 flex-1 px-2 space-y-4">
                                <template v-for="item in navigationItems" :key="item.name">
                                    <Link
                                        :href="route(item.route)"
                                        :class="[
                                            route().current(item.route + '*') 
                                                ? 'bg-orange-100 text-orange-700 dark:bg-orange-900 dark:text-orange-200' 
                                                : 'text-gray-600 hover:bg-gray-50 hover:text-gray-900 dark:text-gray-300 dark:hover:bg-gray-700 dark:hover:text-white',
                                            'group flex flex-col items-center px-2 py-4 text-xs font-medium rounded-md transition-colors duration-150 ease-in-out'
                                        ]"
                                        :title="t(`navigation.${item.name}`, item.name)"
                                    >
                                        <span class="text-2xl mb-1">{{ item.icon }}</span>
                                        <span class="text-center">{{ t(`navigation.${item.name}`, item.name.charAt(0).toUpperCase() + item.name.slice(1)) }}</span>
                                    </Link>
                                </template>
                            </nav>
                        </div>
                    </div>
                </div>

                <!-- Main content - Full width for kitchen operations -->
                <div class="flex flex-col flex-1 overflow-hidden">
                    <!-- Page Heading -->
                    <header v-if="$slots.header" class="bg-white dark:bg-gray-800 shadow-sm">
                        <div class="max-w-full mx-auto py-4 px-4 sm:px-6 lg:px-8">
                            <slot name="header" />
                        </div>
                    </header>

                    <!-- Page Content -->
                    <main class="flex-1 relative overflow-y-auto focus:outline-none bg-orange-50 dark:bg-gray-900">
                        <div class="py-6">
                            <div class="max-w-full mx-auto px-4 sm:px-6 lg:px-8">
                                <slot />
                            </div>
                        </div>
                    </main>
                </div>
            </div>

            <!-- Mobile Navigation - Simplified for kitchen -->
            <div class="lg:hidden fixed bottom-0 left-0 right-0 bg-white dark:bg-gray-800 border-t border-gray-200 dark:border-gray-700 z-50">
                <div class="flex justify-around py-2">
                    <template v-for="item in navigationItems" :key="item.name">
                        <Link
                            :href="route(item.route)"
                            :class="[
                                route().current(item.route + '*') 
                                    ? 'text-orange-600 dark:text-orange-400' 
                                    : 'text-gray-600 dark:text-gray-400',
                                'flex flex-col items-center px-3 py-2 text-xs font-medium'
                            ]"
                        >
                            <span class="text-xl mb-1">{{ item.icon }}</span>
                            <span>{{ t(`navigation.${item.name}`, item.name) }}</span>
                        </Link>
                    </template>
                </div>
            </div>
        </div>
    </div>
</template>

<style scoped>
/* Auto-update time */
@keyframes pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.8; }
}

#current-time {
    animation: pulse 2s infinite;
}
</style>
