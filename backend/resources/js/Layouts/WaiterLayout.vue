<script setup>
import { ref, onMounted } from 'vue';
import { Head, Link, router } from '@inertiajs/vue3';
import { useI18n } from 'vue-i18n';
import ApplicationMark from '@/Components/ApplicationMark.vue';
import Banner from '@/Components/Banner.vue';
import Dropdown from '@/Components/Dropdown.vue';
import DropdownLink from '@/Components/DropdownLink.vue';
import NavLink from '@/Components/NavLink.vue';
import ResponsiveNavLink from '@/Components/ResponsiveNavLink.vue';
import LanguageSwitcher from '@/Components/LanguageSwitcher.vue';

defineProps({
    title: String,
});

// Vue i18n integration
const { t, locale } = useI18n();

const showingNavigationDropdown = ref(false);

const logout = () => {
    router.post(route('logout'));
};

// Get navigation label with proper translation
const getNavigationLabel = (itemName) => {
    const labels = {
        dashboard: t('navigation.dashboard', 'Dashboard'),
        tables: t('navigation.tables', 'Tables'),
        orders: t('navigation.orders', 'Orders'),
        pos: t('navigation.pos', 'POS System'),
        menu: t('navigation.menu', 'Menu'),
        customers: t('navigation.customers', 'Customers'),
        reservations: t('navigation.reservations', 'Reservations'),
    };

    return labels[itemName] || itemName.charAt(0).toUpperCase() + itemName.slice(1);
};

// Waiter navigation items - streamlined for table service
const navigationItems = [
    { name: 'dashboard', route: 'waiter.dashboard', icon: '🏠' },
    { name: 'tables', route: 'waiter.tables', icon: '🪑' },
    { name: 'orders', route: 'waiter.orders', icon: '🛒' },
    { name: 'pos', route: 'pos.index', icon: '💳' },
    { name: 'menu', route: 'waiter.menu', icon: '📋' },
    { name: 'customers', route: 'waiter.customers', icon: '👥' },
    { name: 'reservations', route: 'waiter.reservations', icon: '📅' },
];
</script>

<template>
    <div>
        <Head :title="title" />
        <Banner />

        <div class="min-h-screen bg-gray-50 dark:bg-gray-900">
            <!-- Top Navigation Bar -->
            <nav class="bg-green-600 dark:bg-green-800 shadow-lg">
                <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                    <div class="flex justify-between h-16">
                        <div class="flex items-center">
                            <!-- Logo -->
                            <div class="shrink-0 flex items-center">
                                <Link :href="route('waiter.dashboard')">
                                    <ApplicationMark class="block h-9 w-auto text-white" />
                                </Link>
                            </div>

                            <!-- Waiter Badge -->
                            <div class="hidden sm:flex sm:items-center sm:ml-6">
                                <span class="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200">
                                    🍽️ {{ t('dashboard_titles.waiter_dashboard', 'Waiter Dashboard') }}
                                </span>
                            </div>

                            <!-- Quick Actions -->
                            <div class="hidden md:flex md:items-center md:ml-8 space-x-4">
                                <Link
                                    :href="route('pos.index')"
                                    class="inline-flex items-center px-4 py-2 bg-white text-green-600 text-sm font-medium rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 transition-colors duration-200"
                                >
                                    💳 {{ t('navigation.pos', 'POS System') }}
                                </Link>
                                <Link
                                    :href="route('waiter.orders.create')"
                                    class="inline-flex items-center px-4 py-2 bg-yellow-500 text-white text-sm font-medium rounded-md hover:bg-yellow-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-yellow-500 transition-colors duration-200"
                                >
                                    ➕ {{ t('order_management.new_order', 'New Order') }}
                                </Link>
                            </div>
                        </div>

                        <div class="hidden sm:flex sm:items-center sm:ml-6 space-x-4">
                            <!-- Language Switcher -->
                            <LanguageSwitcher />

                            <!-- Active Orders Counter -->
                            <div class="flex items-center px-3 py-1 bg-yellow-100 text-yellow-800 rounded-full text-sm font-medium">
                                <span class="mr-1">🔥</span>
                                <span>5 {{ t('order_management.pending', 'Active') }}</span>
                            </div>

                            <!-- User Dropdown -->
                            <Dropdown align="right" width="48">
                                <template #trigger>
                                    <button class="flex text-sm border-2 border-white rounded-full focus:outline-none focus:border-gray-300 transition">
                                        <img class="h-8 w-8 rounded-full object-cover" :src="$page.props.auth.user.profile_photo_url" :alt="$page.props.auth.user.name">
                                    </button>
                                </template>

                                <template #content>
                                    <div class="block px-4 py-2 text-xs text-gray-400">
                                        {{ $page.props.auth.user.name }}
                                    </div>
                                    <DropdownLink :href="route('profile.show')">
                                        {{ t('navigation.profile', 'Profile') }}
                                    </DropdownLink>
                                    <div class="border-t border-gray-200 dark:border-gray-600"></div>
                                    <form @submit.prevent="logout">
                                        <DropdownLink as="button">
                                            {{ t('actions.logout', 'Log Out') }}
                                        </DropdownLink>
                                    </form>
                                </template>
                            </Dropdown>
                        </div>

                        <!-- Mobile menu button -->
                        <div class="flex items-center sm:hidden">
                            <button @click="showingNavigationDropdown = !showingNavigationDropdown" class="inline-flex items-center justify-center p-2 rounded-md text-white hover:text-gray-200 hover:bg-green-700 focus:outline-none focus:bg-green-700 transition duration-150 ease-in-out">
                                <svg class="h-6 w-6" stroke="currentColor" fill="none" viewBox="0 0 24 24">
                                    <path :class="{'hidden': showingNavigationDropdown, 'inline-flex': ! showingNavigationDropdown }" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16" />
                                    <path :class="{'hidden': ! showingNavigationDropdown, 'inline-flex': showingNavigationDropdown }" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                                </svg>
                            </button>
                        </div>
                    </div>
                </div>
            </nav>

            <!-- Main Content Area -->
            <div class="flex">
                <!-- Sidebar Navigation -->
                <div class="hidden lg:flex lg:flex-shrink-0">
                    <div class="flex flex-col w-56">
                        <div class="flex flex-col flex-grow bg-white dark:bg-gray-800 pt-5 pb-4 overflow-y-auto border-r border-gray-200 dark:border-gray-700">
                            <nav class="mt-5 flex-1 px-2 space-y-2">
                                <template v-for="item in navigationItems" :key="item.name">
                                    <Link
                                        :href="route(item.route)"
                                        :class="[
                                            route().current(item.route + '*') || (item.name === 'pos' && route().current('pos.*'))
                                                ? 'bg-green-100 text-green-700 dark:bg-green-900 dark:text-green-200'
                                                : 'text-gray-600 hover:bg-gray-50 hover:text-gray-900 dark:text-gray-300 dark:hover:bg-gray-700 dark:hover:text-white',
                                            'group flex items-center px-3 py-3 text-sm font-medium rounded-md transition-colors duration-150 ease-in-out',
                                            item.name === 'pos' ? 'border-l-4 border-blue-500' : ''
                                        ]"
                                    >
                                        <span class="mr-3 text-xl">{{ item.icon }}</span>
                                        <span class="font-medium">{{ getNavigationLabel(item.name) }}</span>
                                        <span v-if="item.name === 'pos'" class="ml-auto inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200">
                                            System
                                        </span>
                                    </Link>
                                </template>
                            </nav>

                            <!-- Quick Stats -->
                            <div class="px-2 mt-6">
                                <div class="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
                                    <h3 class="text-sm font-medium text-gray-900 dark:text-gray-100 mb-3">{{ t('time_date.today', 'Today') }}</h3>
                                    <div class="space-y-2">
                                        <div class="flex justify-between text-sm">
                                            <span class="text-gray-600 dark:text-gray-400">{{ t('order_management.orders', 'Orders') }}</span>
                                            <span class="font-medium text-gray-900 dark:text-gray-100">12</span>
                                        </div>
                                        <div class="flex justify-between text-sm">
                                            <span class="text-gray-600 dark:text-gray-400">{{ t('table_management.tables', 'Tables') }}</span>
                                            <span class="font-medium text-gray-900 dark:text-gray-100">8/15</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Main content -->
                <div class="flex flex-col flex-1 overflow-hidden">
                    <!-- Page Heading -->
                    <header v-if="$slots.header" class="bg-white dark:bg-gray-800 shadow-sm">
                        <div class="max-w-7xl mx-auto py-4 px-4 sm:px-6 lg:px-8">
                            <slot name="header" />
                        </div>
                    </header>

                    <!-- Page Content -->
                    <main class="flex-1 relative overflow-y-auto focus:outline-none bg-gray-50 dark:bg-gray-900">
                        <div class="py-6">
                            <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                                <slot />
                            </div>
                        </div>
                    </main>
                </div>
            </div>

            <!-- Mobile Navigation Menu -->
            <div :class="{'block': showingNavigationDropdown, 'hidden': ! showingNavigationDropdown}" class="sm:hidden">
                <div class="pt-2 pb-3 space-y-1 bg-green-600 dark:bg-green-800">
                    <template v-for="item in navigationItems" :key="item.name">
                        <ResponsiveNavLink
                            :href="route(item.route)"
                            :active="route().current(item.route + '*') || (item.name === 'pos' && route().current('pos.*'))"
                            class="flex items-center text-white hover:bg-green-700"
                        >
                            <span class="mr-3 text-lg">{{ item.icon }}</span>
                            {{ getNavigationLabel(item.name) }}
                        </ResponsiveNavLink>
                    </template>
                </div>
            </div>
        </div>
    </div>
</template>
