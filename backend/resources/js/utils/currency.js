/**
 * Global currency helper functions
 * 
 * This module provides centralized currency formatting and management
 * for the restaurant management system.
 */

/**
 * Get the current currency symbol/code
 * @returns {string} The currency symbol or code
 */
export function getCurrency() {
    // For now, return BDT as requested
    // In the future, this could be made dynamic based on:
    // - User preferences
    // - Restaurant settings
    // - Tenant configuration
    // - Browser locale
    return 'BDT';
}

/**
 * Get the currency symbol for display
 * @returns {string} The currency symbol
 */
export function getCurrencySymbol() {
    const currency = getCurrency();
    const symbols = {
        'USD': '$',
        'BDT': '৳',
        'EUR': '€',
        'GBP': '£',
        'JPY': '¥',
        'INR': '₹'
    };
    return symbols[currency] || currency;
}

/**
 * Format a price with the current currency
 * @param {number} price - The price to format
 * @param {object} options - Formatting options
 * @returns {string} Formatted price string
 */
export function formatPrice(price, options = {}) {
    const currency = getCurrency();
    const amount = parseFloat(price || 0);
    
    const defaultOptions = {
        style: 'currency',
        currency: currency,
        minimumFractionDigits: 2,
        maximumFractionDigits: 2
    };
    
    const formatOptions = { ...defaultOptions, ...options };
    
    try {
        // For BDT, use custom formatting since Intl doesn't handle it well
        if (currency === 'BDT') {
            const formatted = new Intl.NumberFormat('en-US', {
                minimumFractionDigits: formatOptions.minimumFractionDigits,
                maximumFractionDigits: formatOptions.maximumFractionDigits
            }).format(amount);
            return `৳${formatted}`;
        }
        
        // For other currencies, use standard Intl formatting
        return new Intl.NumberFormat('en-US', formatOptions).format(amount);
    } catch (error) {
        console.warn('Currency formatting error:', error);
        // Fallback formatting
        return `${getCurrencySymbol()}${amount.toFixed(2)}`;
    }
}

/**
 * Format a price without currency symbol (just the number)
 * @param {number} price - The price to format
 * @param {object} options - Formatting options
 * @returns {string} Formatted number string
 */
export function formatNumber(price, options = {}) {
    const amount = parseFloat(price || 0);
    
    const defaultOptions = {
        minimumFractionDigits: 2,
        maximumFractionDigits: 2
    };
    
    const formatOptions = { ...defaultOptions, ...options };
    
    return new Intl.NumberFormat('en-US', formatOptions).format(amount);
}

/**
 * Parse a currency string back to a number
 * @param {string} currencyString - The currency string to parse
 * @returns {number} The parsed number
 */
export function parseCurrency(currencyString) {
    if (typeof currencyString === 'number') {
        return currencyString;
    }
    
    if (!currencyString) {
        return 0;
    }
    
    // Remove currency symbols and parse
    const cleaned = currencyString
        .toString()
        .replace(/[৳$€£¥₹,\s]/g, '')
        .replace(/[^\d.-]/g, '');
    
    return parseFloat(cleaned) || 0;
}

/**
 * Get currency configuration for the current tenant
 * This function can be extended in the future to support
 * dynamic currency configuration per tenant/restaurant
 * @returns {object} Currency configuration
 */
export function getCurrencyConfig() {
    const currency = getCurrency();
    
    return {
        code: currency,
        symbol: getCurrencySymbol(),
        name: getCurrencyName(currency),
        decimals: 2,
        thousandsSeparator: ',',
        decimalSeparator: '.'
    };
}

/**
 * Get the full name of a currency
 * @param {string} currencyCode - The currency code
 * @returns {string} The currency name
 */
function getCurrencyName(currencyCode) {
    const names = {
        'USD': 'US Dollar',
        'BDT': 'Bangladeshi Taka',
        'EUR': 'Euro',
        'GBP': 'British Pound',
        'JPY': 'Japanese Yen',
        'INR': 'Indian Rupee'
    };
    return names[currencyCode] || currencyCode;
}
