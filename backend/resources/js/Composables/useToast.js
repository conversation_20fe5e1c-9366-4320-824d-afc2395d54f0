import { ref, reactive } from 'vue'

// Global toast state
const toasts = ref([])
let toastId = 0

export function useToast() {
    // Add a new toast
    const addToast = (message, type = 'success', duration = 4000) => {
        const id = ++toastId
        const toast = {
            id,
            message,
            type, // success, error, warning, info
            duration,
            visible: true
        }
        
        toasts.value.push(toast)
        
        // Auto remove after duration
        if (duration > 0) {
            setTimeout(() => {
                removeToast(id)
            }, duration)
        }
        
        return id
    }
    
    // Remove a toast
    const removeToast = (id) => {
        const index = toasts.value.findIndex(toast => toast.id === id)
        if (index > -1) {
            toasts.value.splice(index, 1)
        }
    }
    
    // Clear all toasts
    const clearToasts = () => {
        toasts.value = []
    }
    
    // Convenience methods
    const success = (message, duration = 4000) => addToast(message, 'success', duration)
    const error = (message, duration = 6000) => addToast(message, 'error', duration)
    const warning = (message, duration = 5000) => addToast(message, 'warning', duration)
    const info = (message, duration = 4000) => addToast(message, 'info', duration)
    
    return {
        toasts,
        addToast,
        removeToast,
        clearToasts,
        success,
        error,
        warning,
        info
    }
}
