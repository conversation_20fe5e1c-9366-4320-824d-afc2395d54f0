import { ref, computed, watch } from 'vue'

// Global cart state
const cartItems = ref([])
const isCartOpen = ref(false)

// Load cart from localStorage on initialization
const loadCartFromStorage = () => {
    try {
        const stored = localStorage.getItem('restaurant_cart')
        if (stored) {
            cartItems.value = JSON.parse(stored)
        }
    } catch (error) {
        console.error('Error loading cart from storage:', error)
        cartItems.value = []
    }
}

// Save cart to localStorage
const saveCartToStorage = () => {
    try {
        localStorage.setItem('restaurant_cart', JSON.stringify(cartItems.value))
    } catch (error) {
        console.error('Error saving cart to storage:', error)
    }
}

// Initialize cart
loadCartFromStorage()

// Watch for cart changes and save to storage
watch(cartItems, saveCartToStorage, { deep: true })

export function useCart() {
    // Computed properties
    const cartCount = computed(() => {
        return cartItems.value.reduce((total, item) => total + item.quantity, 0)
    })
    
    const cartTotal = computed(() => {
        return cartItems.value.reduce((total, item) => total + item.totalPrice, 0)
    })
    
    const cartSubtotal = computed(() => {
        return cartItems.value.reduce((total, item) => total + ((item.basePrice || item.totalPrice / item.quantity) * item.quantity), 0)
    })
    
    const cartExtrasTotal = computed(() => {
        return cartTotal.value - cartSubtotal.value
    })
    
    // Methods
    const addItemToCart = (item) => {
        // Generate unique ID for cart item based on menu item + variations + addons
        const itemId = generateCartItemId(item)
        
        // Check if item with same configuration already exists
        const existingItemIndex = cartItems.value.findIndex(cartItem => cartItem.id === itemId)
        
        if (existingItemIndex > -1) {
            // Update quantity of existing item
            cartItems.value[existingItemIndex].quantity += item.quantity
            cartItems.value[existingItemIndex].totalPrice = 
                cartItems.value[existingItemIndex].quantity * (item.totalPrice / item.quantity)
        } else {
            // Add new item to cart
            cartItems.value.push({
                id: itemId,
                ...item,
                addedAt: new Date().toISOString()
            })
        }
    }
    
    const removeItemFromCart = (itemId) => {
        const index = cartItems.value.findIndex(item => item.id === itemId)
        if (index > -1) {
            cartItems.value.splice(index, 1)
        }
    }
    
    const updateItemQuantity = (itemId, quantity) => {
        const item = cartItems.value.find(item => item.id === itemId)
        if (item) {
            if (quantity <= 0) {
                removeItemFromCart(itemId)
            } else {
                const unitPrice = item.totalPrice / item.quantity
                item.quantity = quantity
                item.totalPrice = unitPrice * quantity
            }
        }
    }
    
    const clearCart = () => {
        cartItems.value = []
    }
    
    const openCart = () => {
        isCartOpen.value = true
    }
    
    const closeCart = () => {
        isCartOpen.value = false
    }
    
    const toggleCart = () => {
        isCartOpen.value = !isCartOpen.value
    }
    
    // Helper function to generate unique cart item ID
    const generateCartItemId = (item) => {
        const variationsKey = Object.entries(item.variations || {})
            .sort(([a], [b]) => a.localeCompare(b))
            .map(([key, value]) => `${key}:${value}`)
            .join('|')
        
        const addonsKey = (item.addons || [])
            .sort((a, b) => a - b)
            .join(',')
        
        const specialInstructionsKey = (item.specialInstructions || '').trim()
        
        return `${item.menuItemId}_${variationsKey}_${addonsKey}_${specialInstructionsKey}`
    }
    
    // Get cart item details with variation and addon names
    const getCartItemDetails = (item) => {
        const details = {
            variations: [],
            addons: [],
            hasCustomizations: false
        }
        
        // This would need to be enhanced with actual variation/addon data
        // For now, we'll return the basic structure
        details.hasCustomizations = 
            Object.keys(item.variations || {}).length > 0 || 
            (item.addons || []).length > 0 || 
            (item.specialInstructions || '').trim().length > 0
        
        return details
    }
    
    // Prepare cart data for order submission
    const prepareOrderData = () => {
        return {
            items: cartItems.value.map(item => ({
                menu_item_id: item.menuItemId,
                quantity: item.quantity,
                variations: item.variations || {},
                addons: item.addons || [],
                special_instructions: item.specialInstructions || '',
                unit_price: item.basePrice,
                total_price: item.totalPrice
            })),
            subtotal: cartSubtotal.value,
            total: cartTotal.value,
            item_count: cartCount.value
        }
    }
    
    return {
        // State
        cartItems: computed(() => cartItems.value),
        isCartOpen: computed(() => isCartOpen.value),
        
        // Computed
        cartCount,
        cartTotal,
        cartSubtotal,
        cartExtrasTotal,
        
        // Methods
        addItemToCart,
        removeItemFromCart,
        updateItemQuantity,
        clearCart,
        openCart,
        closeCart,
        toggleCart,
        getCartItemDetails,
        prepareOrderData
    }
}
