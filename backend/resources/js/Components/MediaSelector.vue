<script setup>
import { ref, computed } from 'vue';
import MediaLibraryModal from './MediaLibrary/MediaLibraryModal.vue';

const props = defineProps({
    modelValue: {
        type: Array,
        default: () => [],
    },
    label: {
        type: String,
        default: 'Media',
    },
    placeholder: {
        type: String,
        default: 'Select media files...',
    },
    multiple: {
        type: Boolean,
        default: true,
    },
    maxFiles: {
        type: Number,
        default: 5,
    },
    accept: {
        type: String,
        default: 'image/*',
    },
    required: {
        type: Boolean,
        default: false,
    },
    error: {
        type: String,
        default: null,
    },
});

const emit = defineEmits(['update:modelValue']);

// Reactive data
const showModal = ref(false);

// Computed
const hasSelection = computed(() => {
    return props.modelValue && props.modelValue.length > 0;
});

const displayText = computed(() => {
    if (!hasSelection.value) {
        return props.placeholder;
    }

    const count = props.modelValue.length;
    return count === 1 ? '1 file selected' : `${count} files selected`;
});

const canAddMore = computed(() => {
    return !props.modelValue || props.modelValue.length < props.maxFiles;
});

// Methods
const openModal = () => {
    if (canAddMore.value) {
        showModal.value = true;
    }
};

const closeModal = () => {
    showModal.value = false;
};

const handleSelection = (media) => {
    if (props.multiple) {
        const currentIds = props.modelValue || [];
        const newId = media.id;

        // Add new selection if not already present and under limit
        if (!currentIds.includes(newId) && currentIds.length < props.maxFiles) {
            const updatedIds = [...currentIds, newId];
            emit('update:modelValue', updatedIds);
        }
    } else {
        emit('update:modelValue', [media.id]);
    }
    closeModal();
};

const removeFile = (index) => {
    const updated = [...props.modelValue];
    updated.splice(index, 1);
    emit('update:modelValue', updated);
};

const clearAll = () => {
    emit('update:modelValue', []);
};

const formatFileSize = (bytes) => {
    if (!bytes) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
};
</script>

<template>
    <div class="media-selector">
        <!-- Label -->
        <label v-if="label" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            {{ label }}
            <span v-if="required" class="text-red-500">*</span>
        </label>

        <!-- Selected Files Display -->
        <div v-if="hasSelection" class="mb-4 space-y-2">
            <div class="flex items-center justify-between">
                <span class="text-sm font-medium text-gray-700 dark:text-gray-300">
                    Selected Files ({{ modelValue.length }}/{{ maxFiles }})
                </span>
                <button
                    type="button"
                    @click="clearAll"
                    class="text-sm text-red-600 hover:text-red-800 dark:text-red-400 dark:hover:text-red-300"
                >
                    Clear All
                </button>
            </div>
            <div class="grid grid-cols-2 md:grid-cols-4 gap-2">
                <div
                    v-for="(mediaId, index) in modelValue"
                    :key="mediaId"
                    class="relative group border border-gray-200 dark:border-gray-600 rounded-lg p-2 bg-gray-50 dark:bg-gray-700"
                >
                    <div class="aspect-square bg-gray-200 dark:bg-gray-600 rounded flex items-center justify-center">
                        <svg class="w-8 h-8 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
                        </svg>
                    </div>
                    <button
                        type="button"
                        @click="removeFile(index)"
                        class="absolute -top-1 -right-1 bg-red-500 text-white rounded-full w-5 h-5 flex items-center justify-center text-xs hover:bg-red-600 transition-colors"
                    >
                        ×
                    </button>
                    <div class="mt-1 text-xs text-gray-600 dark:text-gray-400 truncate">
                        Media #{{ mediaId }}
                    </div>
                </div>
            </div>
        </div>

        <!-- Picker Button -->
        <div
            @click.stop="openModal"
            :class="[
                'relative border-2 border-dashed rounded-lg p-6 cursor-pointer transition-colors hover:border-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500',
                error ? 'border-red-300' : 'border-gray-300 dark:border-gray-600',
                hasSelection ? 'bg-gray-50 dark:bg-gray-700' : 'bg-white dark:bg-gray-800',
                !canAddMore ? 'opacity-50 cursor-not-allowed' : ''
            ]"
        >
            <div class="text-center">
                <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
                </svg>
                <div class="mt-2">
                    <div class="text-sm font-medium text-gray-900 dark:text-gray-100">
                        {{ canAddMore ? (hasSelection ? 'Add more files' : displayText) : 'Maximum files reached' }}
                    </div>
                    <div class="text-xs text-gray-500 dark:text-gray-400 mt-1">
                        {{ canAddMore ? `Click to select from media library (max ${maxFiles})` : `You can select up to ${maxFiles} files` }}
                    </div>
                </div>
            </div>
        </div>

        <!-- Error Message -->
        <div v-if="error" class="mt-1 text-sm text-red-600">
            {{ error }}
        </div>

        <!-- Help Text -->
        <div v-else-if="!hasSelection" class="mt-1 text-sm text-gray-500 dark:text-gray-400">
            Select {{ multiple ? 'up to ' + maxFiles + ' files' : 'a file' }} from your media library
        </div>

        <!-- Media Library Modal -->
        <MediaLibraryModal
            :show="showModal"
            :onSelect="handleSelection"
            @close="closeModal"
        />
    </div>
</template>
