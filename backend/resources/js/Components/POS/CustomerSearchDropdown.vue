<script setup>
import { ref, computed, onMounted, onUnmounted } from 'vue';
import CreateCustomerModal from './CreateCustomerModal.vue';

const props = defineProps({
    customers: {
        type: Array,
        default: () => []
    },
    selectedCustomer: {
        type: Object,
        default: null
    },
    placeholder: {
        type: String,
        default: 'Search customer...'
    },
    loading: {
        type: Boolean,
        default: false
    }
});

const emit = defineEmits(['select-customer', 'create-customer', 'refresh-customers']);

// Reactive state
const searchQuery = ref('');
const isOpen = ref(false);
const dropdownRef = ref(null);
const inputRef = ref(null);
const showCreateModal = ref(false);

// Computed properties
const filteredCustomers = computed(() => {
    if (!searchQuery.value) {
        return props.customers.slice(0, 10); // Show first 10 customers when no search
    }
    
    const query = searchQuery.value.toLowerCase();
    return props.customers.filter(customer => 
        customer.name?.toLowerCase().includes(query) ||
        customer.phone?.includes(query) ||
        customer.email?.toLowerCase().includes(query)
    ).slice(0, 10); // Limit to 10 results
});

// Remove displayValue computed property - we'll use searchQuery directly

// Methods
const openDropdown = () => {
    isOpen.value = true;
    // Allow typing even if customer is selected
};

const closeDropdown = () => {
    isOpen.value = false;
    // Keep the current search query when closing
};

const selectCustomer = (customer) => {
    if (customer.type !== 'walk-in') {
        searchQuery.value = customer.name;
    } else {
        searchQuery.value = '';
    }
    isOpen.value = false;
    emit('select-customer', customer);
};

const clearSelection = () => {
    searchQuery.value = '';
    emit('select-customer', null);
    inputRef.value?.focus();
};

const createNewCustomer = () => {
    isOpen.value = false;
    emit('create-customer', searchQuery.value);
};

const openCreateModal = () => {
    showCreateModal.value = true;
    isOpen.value = false;
};

const closeCreateModal = () => {
    showCreateModal.value = false;
};

const handleCustomerCreated = (customer) => {
    // Select the newly created customer
    selectCustomer(customer);
    // Emit event to refresh customers list
    emit('refresh-customers');
    closeCreateModal();
};

// Handle click outside
const handleClickOutside = (event) => {
    if (dropdownRef.value && !dropdownRef.value.contains(event.target)) {
        closeDropdown();
    }
};

onMounted(() => {
    document.addEventListener('click', handleClickOutside);
    if (props.selectedCustomer) {
        searchQuery.value = props.selectedCustomer.name;
    }
});

onUnmounted(() => {
    document.removeEventListener('click', handleClickOutside);
});
</script>

<template>
    <div ref="dropdownRef" class="relative">
        <!-- Search Input with Plus Button -->
        <div class="relative flex">
            <input
                ref="inputRef"
                v-model="searchQuery"
                type="text"
                :placeholder="placeholder"
                @focus="openDropdown"
                @input="openDropdown"
                class="flex-1 px-3 py-2 pr-8 border border-gray-300 rounded-l-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            />

            <!-- Clear button -->
            <button
                v-if="selectedCustomer || searchQuery"
                @click="clearSelection"
                class="absolute right-12 top-2 text-gray-400 hover:text-gray-600 z-10"
            >
                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                </svg>
            </button>

            <!-- Plus Button -->
            <button
                type="button"
                @click="openCreateModal"
                class="px-3 py-2 bg-blue-600 text-white border border-blue-600 rounded-r-lg hover:bg-blue-700 focus:ring-2 focus:ring-blue-500 focus:outline-none transition-colors"
                title="Add new customer"
            >
                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                </svg>
            </button>
        </div>

        <!-- Dropdown -->
        <div
            v-if="isOpen"
            class="absolute z-50 w-full mt-1 bg-white border border-gray-300 rounded-lg shadow-lg max-h-60 overflow-y-auto"
        >
            <!-- Loading state -->
            <div v-if="loading || (!customers.length && searchQuery)" class="p-4 text-center text-gray-500">
                <svg class="w-6 h-6 mx-auto mb-2 animate-spin" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                </svg>
                {{ loading ? 'Loading customers...' : 'Searching...' }}
            </div>

            <!-- Customer list -->
            <div v-else-if="filteredCustomers.length > 0">
                <div
                    v-for="customer in filteredCustomers"
                    :key="customer.id"
                    @click="selectCustomer(customer)"
                    class="p-3 hover:bg-gray-50 cursor-pointer border-b border-gray-100 last:border-b-0"
                >
                    <div class="flex items-center space-x-3">
                        <!-- Customer Avatar -->
                        <div class="w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center text-white text-sm font-medium">
                            {{ customer.name?.charAt(0)?.toUpperCase() || '?' }}
                        </div>
                        
                        <!-- Customer Info -->
                        <div class="flex-1 min-w-0">
                            <div class="font-medium text-gray-900 truncate">
                                {{ customer.name }}
                            </div>
                            <div class="text-sm text-gray-500 truncate">
                                <span v-if="customer.phone">📞 {{ customer.phone }}</span>
                                <span v-if="customer.email" class="ml-2">✉️ {{ customer.email }}</span>
                            </div>
                        </div>
                        
                        <!-- Customer Type Badge -->
                        <div class="flex-shrink-0">
                            <span
                                :class="[
                                    'inline-flex px-2 py-1 rounded-full text-xs font-medium',
                                    customer.tier === 'vip' ? 'bg-purple-100 text-purple-800' :
                                    customer.tier === 'gold' ? 'bg-yellow-100 text-yellow-800' :
                                    customer.tier === 'silver' ? 'bg-gray-100 text-gray-800' :
                                    'bg-green-100 text-green-800'
                                ]"
                            >
                                {{ customer.tier || 'Regular' }}
                            </span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- No results -->
            <div v-else class="p-4 text-center text-gray-500">
                <div class="mb-2">No customers found</div>
                <button
                    v-if="searchQuery"
                    @click="createNewCustomer"
                    class="text-blue-600 hover:text-blue-800 text-sm font-medium"
                >
                    + Create "{{ searchQuery }}" as new customer
                </button>
            </div>

            <!-- Walk-in option -->
            <div class="border-t border-gray-200">
                <div
                    @click="selectCustomer({ id: null, name: 'Walk-in Customer', type: 'walk-in' })"
                    class="p-3 hover:bg-gray-50 cursor-pointer flex items-center space-x-3"
                >
                    <div class="w-8 h-8 bg-gray-400 rounded-full flex items-center justify-center text-white text-sm">
                        👤
                    </div>
                    <div>
                        <div class="font-medium text-gray-900">Walk-in Customer</div>
                        <div class="text-sm text-gray-500">No customer information required</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Create Customer Modal -->
        <CreateCustomerModal
            :show="showCreateModal"
            @close="closeCreateModal"
            @customer-created="handleCustomerCreated"
        />
    </div>
</template>

<style scoped>
/* Custom scrollbar for dropdown */
.overflow-y-auto::-webkit-scrollbar {
    width: 6px;
}

.overflow-y-auto::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 3px;
}

.overflow-y-auto::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 3px;
}

.overflow-y-auto::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}
</style>
