<template>
    <div class="fixed inset-0 z-50 overflow-y-auto">
        <div class="flex items-center justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
            <!-- Background overlay -->
            <div class="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" @click="$emit('close')"></div>

            <!-- Modal panel -->
            <div class="inline-block align-bottom bg-white dark:bg-gray-800 rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-4xl sm:w-full">
                <!-- Header -->
                <div class="bg-white dark:bg-gray-800 px-6 py-4 border-b border-gray-200 dark:border-gray-700">
                    <div class="flex items-center justify-between">
                        <h3 class="text-lg font-medium text-gray-900 dark:text-white">
                            👤 Select Customer
                        </h3>
                        <button
                            @click="$emit('close')"
                            class="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
                        >
                            <i class="fas fa-times text-xl"></i>
                        </button>
                    </div>
                </div>

                <!-- Search and Add New -->
                <div class="bg-gray-50 dark:bg-gray-700 px-6 py-4">
                    <div class="flex space-x-4">
                        <!-- Search Input -->
                        <div class="flex-1 relative">
                            <input
                                v-model="searchQuery"
                                type="text"
                                placeholder="Search by name or phone..."
                                class="w-full pl-10 pr-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-800 dark:text-white"
                            >
                            <i class="fas fa-search absolute left-3 top-3 text-gray-400"></i>
                        </div>
                        
                        <!-- Add New Customer Button -->
                        <button
                            @click="showAddCustomerForm = true"
                            class="px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors whitespace-nowrap"
                        >
                            <i class="fas fa-plus mr-2"></i>
                            Add New
                        </button>
                    </div>
                </div>

                <!-- Customer List -->
                <div class="bg-white dark:bg-gray-800 max-h-96 overflow-y-auto">
                    <div v-if="filteredCustomers.length === 0" class="text-center py-8 text-gray-500 dark:text-gray-400">
                        <i class="fas fa-users text-4xl mb-3 opacity-50"></i>
                        <p>No customers found</p>
                        <p class="text-sm">Try adjusting your search or add a new customer</p>
                    </div>

                    <div v-else class="divide-y divide-gray-200 dark:divide-gray-700">
                        <div
                            v-for="customer in filteredCustomers"
                            :key="customer.id"
                            @click="selectCustomer(customer)"
                            class="p-4 hover:bg-gray-50 dark:hover:bg-gray-700 cursor-pointer transition-colors"
                        >
                            <div class="flex items-center justify-between">
                                <div class="flex-1">
                                    <div class="flex items-center space-x-3">
                                        <div class="w-10 h-10 bg-blue-500 rounded-full flex items-center justify-center">
                                            <i class="fas fa-user text-white"></i>
                                        </div>
                                        <div>
                                            <div class="font-medium text-gray-900 dark:text-white">
                                                {{ customer.name }}
                                            </div>
                                            <div class="text-sm text-gray-600 dark:text-gray-400">
                                                📞 {{ customer.phone }}
                                            </div>
                                            <div v-if="customer.email" class="text-sm text-gray-600 dark:text-gray-400">
                                                ✉️ {{ customer.email }}
                                            </div>
                                        </div>
                                    </div>
                                    <div v-if="customer.delivery_address" class="mt-2 text-sm text-gray-600 dark:text-gray-400">
                                        📍 {{ customer.delivery_address }}
                                    </div>
                                </div>
                                <div class="text-right">
                                    <div class="text-sm font-medium text-gray-900 dark:text-white">
                                        {{ customer.total_orders || 0 }} orders
                                    </div>
                                    <div class="text-xs text-gray-500 dark:text-gray-400">
                                        Last: {{ formatDate(customer.last_order_date) }}
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Add Customer Form -->
                <div v-if="showAddCustomerForm" class="border-t border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-700 p-6">
                    <h4 class="text-lg font-medium text-gray-900 dark:text-white mb-4">Add New Customer</h4>
                    
                    <form @submit.prevent="addCustomer" class="space-y-4">
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div>
                                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                                    Name *
                                </label>
                                <input
                                    v-model="newCustomer.name"
                                    type="text"
                                    required
                                    class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-800 dark:text-white"
                                    placeholder="Customer name"
                                >
                            </div>
                            
                            <div>
                                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                                    Phone *
                                </label>
                                <input
                                    v-model="newCustomer.phone"
                                    type="tel"
                                    required
                                    class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-800 dark:text-white"
                                    placeholder="Phone number"
                                >
                            </div>
                            
                            <div>
                                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                                    Email
                                </label>
                                <input
                                    v-model="newCustomer.email"
                                    type="email"
                                    class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-800 dark:text-white"
                                    placeholder="Email address"
                                >
                            </div>
                            
                            <div>
                                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                                    Delivery Address *
                                </label>
                                <input
                                    v-model="newCustomer.delivery_address"
                                    type="text"
                                    required
                                    class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-800 dark:text-white"
                                    placeholder="Full delivery address"
                                >
                            </div>
                        </div>
                        
                        <div>
                            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                                Special Instructions
                            </label>
                            <textarea
                                v-model="newCustomer.special_instructions"
                                rows="2"
                                class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-800 dark:text-white"
                                placeholder="Any special delivery instructions..."
                            ></textarea>
                        </div>
                        
                        <div class="flex justify-end space-x-3">
                            <button
                                type="button"
                                @click="showAddCustomerForm = false"
                                class="px-4 py-2 text-gray-700 dark:text-gray-300 border border-gray-300 dark:border-gray-600 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-600 transition-colors"
                            >
                                Cancel
                            </button>
                            <button
                                type="submit"
                                :disabled="isAddingCustomer"
                                class="px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
                            >
                                <i v-if="isAddingCustomer" class="fas fa-spinner fa-spin mr-2"></i>
                                Add Customer
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</template>

<script setup>
import { ref, computed } from 'vue'
import axios from 'axios'

const props = defineProps({
    customers: Array,
})

const emit = defineEmits(['select-customer', 'close'])

// Reactive state
const searchQuery = ref('')
const showAddCustomerForm = ref(false)
const isAddingCustomer = ref(false)
const newCustomer = ref({
    name: '',
    phone: '',
    email: '',
    delivery_address: '',
    special_instructions: '',
})

// Computed
const filteredCustomers = computed(() => {
    if (!searchQuery.value) return props.customers

    const query = searchQuery.value.toLowerCase()
    return props.customers.filter(customer =>
        customer.name.toLowerCase().includes(query) ||
        customer.phone.includes(query) ||
        (customer.email && customer.email.toLowerCase().includes(query))
    )
})

// Methods
const selectCustomer = (customer) => {
    emit('select-customer', customer)
}

const addCustomer = async () => {
    isAddingCustomer.value = true
    
    try {
        const response = await axios.post(route('pos.customers.store'), newCustomer.value)
        
        if (response.data.success) {
            const customer = response.data.customer
            emit('select-customer', customer)
            
            // Reset form
            newCustomer.value = {
                name: '',
                phone: '',
                email: '',
                delivery_address: '',
                special_instructions: '',
            }
            showAddCustomerForm.value = false
        }
    } catch (error) {
        console.error('Failed to add customer:', error)
        alert('Failed to add customer. Please try again.')
    } finally {
        isAddingCustomer.value = false
    }
}

const formatDate = (date) => {
    if (!date) return 'Never'
    return new Date(date).toLocaleDateString()
}
</script>
