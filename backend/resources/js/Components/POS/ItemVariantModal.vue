<template>
    <div v-if="show" class="fixed inset-0 z-50 overflow-y-auto">
        <!-- Backdrop -->
        <div class="fixed inset-0 bg-black bg-opacity-50 transition-opacity" @click="closeModal"></div>
        
        <!-- Modal -->
        <div class="flex min-h-full items-center justify-center p-4">
            <div class="relative bg-white rounded-lg shadow-xl max-w-md w-full max-h-[90vh] overflow-y-auto">
                <!-- Header -->
                <div class="flex items-center justify-between p-6 border-b border-gray-200">
                    <div class="flex items-center space-x-3">
                        <img 
                            v-if="item?.primary_image_url" 
                            :src="item.primary_image_url" 
                            :alt="item.name"
                            class="w-12 h-12 rounded-lg object-cover"
                        >
                        <div class="w-12 h-12 bg-gray-200 rounded-lg flex items-center justify-center" v-else>
                            <svg class="w-6 h-6 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                            </svg>
                        </div>
                        <div>
                            <h3 class="text-lg font-semibold text-gray-900">{{ item?.name }}</h3>
                            <p class="text-sm text-gray-500">${{ formatPrice(item?.price) }}</p>
                        </div>
                    </div>
                    <button @click="closeModal" class="text-gray-400 hover:text-gray-600">
                        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                        </svg>
                    </button>
                </div>

                <!-- Content -->
                <div class="p-6 space-y-6">
                    <!-- Quantity Selector -->
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Quantity</label>
                        <div class="flex items-center space-x-3">
                            <button 
                                @click="decrementQuantity"
                                :disabled="quantity <= 1"
                                class="w-10 h-10 rounded-full border border-gray-300 flex items-center justify-center hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                            >
                                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 12H4"></path>
                                </svg>
                            </button>
                            <span class="text-xl font-semibold min-w-[3rem] text-center">{{ quantity }}</span>
                            <button 
                                @click="incrementQuantity"
                                class="w-10 h-10 rounded-full border border-gray-300 flex items-center justify-center hover:bg-gray-50"
                            >
                                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                                </svg>
                            </button>
                        </div>
                    </div>

                    <!-- Variants (if available) -->
                    <div v-if="item?.variants && item.variants.length > 0">
                        <label class="block text-sm font-medium text-gray-700 mb-2">Select Variant</label>
                        <div class="space-y-2">
                            <label 
                                v-for="variant in item.variants" 
                                :key="variant.id"
                                class="flex items-center justify-between p-3 border border-gray-200 rounded-lg cursor-pointer hover:bg-gray-50"
                                :class="{ 'border-blue-500 bg-blue-50': selectedVariant?.id === variant.id }"
                            >
                                <div class="flex items-center space-x-3">
                                    <input 
                                        type="radio" 
                                        :value="variant.id" 
                                        v-model="selectedVariantId"
                                        class="text-blue-600 focus:ring-blue-500"
                                    >
                                    <div>
                                        <div class="font-medium text-gray-900">{{ variant.name }}</div>
                                        <div v-if="variant.description" class="text-sm text-gray-500">{{ variant.description }}</div>
                                    </div>
                                </div>
                                <div class="text-sm font-medium text-gray-900">
                                    +${{ formatPrice(variant.price_difference || 0) }}
                                </div>
                            </label>
                        </div>
                    </div>

                    <!-- Addons (if available) -->
                    <div v-if="item?.addons && item.addons.length > 0">
                        <label class="block text-sm font-medium text-gray-700 mb-2">Add-ons</label>
                        <div class="space-y-2">
                            <label 
                                v-for="addon in item.addons" 
                                :key="addon.id"
                                class="flex items-center justify-between p-3 border border-gray-200 rounded-lg cursor-pointer hover:bg-gray-50"
                                :class="{ 'border-blue-500 bg-blue-50': selectedAddons.includes(addon.id) }"
                            >
                                <div class="flex items-center space-x-3">
                                    <input 
                                        type="checkbox" 
                                        :value="addon.id" 
                                        v-model="selectedAddons"
                                        class="text-blue-600 focus:ring-blue-500 rounded"
                                    >
                                    <div>
                                        <div class="font-medium text-gray-900">{{ addon.name }}</div>
                                        <div v-if="addon.description" class="text-sm text-gray-500">{{ addon.description }}</div>
                                    </div>
                                </div>
                                <div class="text-sm font-medium text-gray-900">
                                    +${{ formatPrice(addon.price) }}
                                </div>
                            </label>
                        </div>
                    </div>

                    <!-- Special Instructions -->
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Special Instructions</label>
                        <textarea 
                            v-model="specialInstructions"
                            rows="3"
                            class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                            placeholder="Any special requests or modifications..."
                        ></textarea>
                    </div>
                </div>

                <!-- Footer -->
                <div class="flex items-center justify-between p-6 border-t border-gray-200">
                    <div class="text-lg font-semibold text-gray-900">
                        Total: ${{ formatPrice(totalPrice) }}
                    </div>
                    <div class="flex space-x-3">
                        <button 
                            @click="closeModal"
                            class="px-4 py-2 text-gray-700 bg-gray-100 rounded-lg hover:bg-gray-200 transition-colors"
                        >
                            Cancel
                        </button>
                        <button 
                            @click="addToCart"
                            class="px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
                        >
                            Add to Cart
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script setup>
import { ref, computed, watch } from 'vue';

const props = defineProps({
    show: Boolean,
    item: Object,
});

const emit = defineEmits(['close', 'add-to-cart']);

// Reactive state
const quantity = ref(1);
const selectedVariantId = ref(null);
const selectedAddons = ref([]);
const specialInstructions = ref('');

// Computed properties
const selectedVariant = computed(() => {
    if (!selectedVariantId.value || !props.item?.variants) return null;
    return props.item.variants.find(v => v.id === selectedVariantId.value);
});

const totalPrice = computed(() => {
    let basePrice = parseFloat(props.item?.price || 0);
    
    // Add variant price difference
    if (selectedVariant.value) {
        basePrice += parseFloat(selectedVariant.value.price_difference || 0);
    }
    
    // Add addon prices
    if (props.item?.addons) {
        selectedAddons.value.forEach(addonId => {
            const addon = props.item.addons.find(a => a.id === addonId);
            if (addon) {
                basePrice += parseFloat(addon.price || 0);
            }
        });
    }
    
    return basePrice * quantity.value;
});

// Methods
const formatPrice = (price) => {
    return parseFloat(price || 0).toFixed(2);
};

const incrementQuantity = () => {
    quantity.value++;
    playSound('increment');
};

const decrementQuantity = () => {
    if (quantity.value > 1) {
        quantity.value--;
        playSound('decrement');
    }
};

const playSound = (type) => {
    // Create audio context for sound effects
    const audioContext = new (window.AudioContext || window.webkitAudioContext)();
    
    let frequency;
    switch (type) {
        case 'add':
            frequency = 800; // Higher pitch for add
            break;
        case 'increment':
            frequency = 600; // Medium pitch for increment
            break;
        case 'decrement':
            frequency = 400; // Lower pitch for decrement
            break;
        default:
            frequency = 500;
    }
    
    const oscillator = audioContext.createOscillator();
    const gainNode = audioContext.createGain();
    
    oscillator.connect(gainNode);
    gainNode.connect(audioContext.destination);
    
    oscillator.frequency.setValueAtTime(frequency, audioContext.currentTime);
    oscillator.type = 'sine';
    
    gainNode.gain.setValueAtTime(0.1, audioContext.currentTime);
    gainNode.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + 0.2);
    
    oscillator.start(audioContext.currentTime);
    oscillator.stop(audioContext.currentTime + 0.2);
};

const addToCart = () => {
    const cartItem = {
        menu_item_id: props.item.id,
        food_name: props.item.name,
        food_description: props.item.description,
        quantity: quantity.value,
        variant_id: selectedVariantId.value,
        addons: selectedAddons.value,
        special_instructions: specialInstructions.value,
        modifiers: {
            variant_id: selectedVariantId.value,
            addons: selectedAddons.value
        },
        unit_price: parseFloat(props.item.price),
        total_price: totalPrice.value,
    };
    
    playSound('add');
    emit('add-to-cart', cartItem);
    closeModal();
};

const closeModal = () => {
    // Reset form
    quantity.value = 1;
    selectedVariantId.value = null;
    selectedAddons.value = [];
    specialInstructions.value = '';
    
    emit('close');
};

// Watch for item changes to set default variant
watch(() => props.item, (newItem) => {
    if (newItem?.variants && newItem.variants.length > 0) {
        selectedVariantId.value = newItem.variants[0].id;
    }
}, { immediate: true });
</script>
