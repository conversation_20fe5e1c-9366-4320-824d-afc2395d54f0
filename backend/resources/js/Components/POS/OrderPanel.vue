<script setup>
import { computed, watch } from 'vue';

const props = defineProps({
    order: Object,
    activeOrders: Array,
    selectedTable: Object,
    loading: Boolean,
    draftItems: {
        type: Array,
        default: () => []
    },
    isDraftMode: {
        type: Boolean,
        default: false
    },
    canPlaceOrder: {
        type: <PERSON><PERSON>an,
        default: false
    }
});

const emit = defineEmits([
    'select-order',
    'update-quantity',
    'remove-item',
    'open-payment',
    'open-discount',
    'send-to-kitchen',
    'close-order',
    'place-order',
    'update-draft-quantity',
    'remove-draft-item',
    'open-home-delivery'
]);

// Computed properties
const orderItems = computed(() => {
    if (props.isDraftMode) {
        return props.draftItems || [];
    }
    return props.order?.items?.filter(item => !item.is_cancelled) || [];
});

const orderTotal = computed(() => {
    if (props.isDraftMode) {
        return props.draftItems.reduce((total, item) => total + item.total_price, 0);
    }
    return props.order?.total_amount || 0;
});

const orderSubtotal = computed(() => {
    if (props.isDraftMode) {
        return props.draftItems.reduce((total, item) => total + item.total_price, 0);
    }
    return props.order?.subtotal || 0;
});

const orderTax = computed(() => {
    if (props.isDraftMode) return 0;
    return props.order?.tax_amount || 0;
});

const orderDiscount = computed(() => {
    if (props.isDraftMode) return 0;
    return props.order?.discount_amount || 0;
});

const canSendToKitchen = computed(() => {
    return props.order && orderItems.value.length > 0 && props.order.status === 'pending';
});

const canProcessPayment = computed(() => {
    return props.order && orderItems.value.length > 0 && ['confirmed', 'preparing', 'ready'].includes(props.order.status);
});

const isOrderPaid = computed(() => {
    return props.order?.payment_status === 'paid';
});

// Format currency
const formatPrice = (price) => {
    return new Intl.NumberFormat('en-US', {
        style: 'currency',
        currency: 'USD'
    }).format(price || 0);
};

// Format time
const formatTime = (dateString) => {
    if (!dateString) return '';
    return new Date(dateString).toLocaleTimeString();
};

// Handle quantity change
const updateQuantity = (item, change) => {
    const newQuantity = item.quantity + change;
    if (newQuantity > 0) {
        if (props.isDraftMode) {
            emit('update-draft-quantity', item, newQuantity);
        } else {
            emit('update-quantity', item, newQuantity);
        }
    }
};

// Handle item removal
const removeItem = (item) => {
    if (props.isDraftMode) {
        emit('remove-draft-item', item);
    } else {
        emit('remove-item', item);
    }
};

// Handle order selection
const selectOrder = (order) => {
    emit('select-order', order);
};

// Action handlers
const openPayment = () => emit('open-payment');
const openDiscount = () => emit('open-discount');
const sendToKitchen = () => emit('send-to-kitchen');
const closeOrder = () => emit('close-order');
const placeOrder = () => emit('place-order');
const openHomeDelivery = () => emit('open-home-delivery');


</script>

<template>
    <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm h-full flex flex-col">
        <!-- Header -->
        <div class="p-4 border-b border-gray-200 dark:border-gray-700">
            <h2 class="text-lg font-semibold text-gray-900 dark:text-white">
                {{ isDraftMode ? 'Draft Order' : (order ? 'Current Order' : 'Active Orders') }}
            </h2>

            <!-- Draft Mode Info -->
            <div v-if="isDraftMode" class="mt-2 text-sm text-gray-600 dark:text-gray-400">
                <div class="flex items-center space-x-2">
                    <span class="inline-flex px-2 py-1 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                        Draft
                    </span>
                    <span v-if="selectedTable">Table: {{ selectedTable.name }}</span>
                    <span v-else class="text-red-600">No table selected</span>
                </div>
            </div>

            <!-- Current Order Info -->
            <div v-else-if="order" class="mt-2 text-sm text-gray-600 dark:text-gray-400">
                <div class="flex justify-between">
                    <span>Order #: {{ order.order_number }}</span>
                    <span>{{ formatTime(order.created_at) }}</span>
                </div>
                <div v-if="selectedTable" class="mt-1">
                    Table: {{ selectedTable.name }}
                </div>
            </div>
        </div>

        <!-- Content -->
        <div class="flex-1 overflow-hidden">
            <!-- Current Order View (Draft Mode or Placed Order) -->
            <div v-if="order || isDraftMode" class="h-full flex flex-col">
                <!-- Order Items -->
                <div class="flex-1 overflow-y-auto p-4">


                    <div v-if="orderItems.length === 0" class="text-center py-8">
                        <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 11V7a4 4 0 00-8 0v4M5 9h14l1 12H4L5 9z"></path>
                        </svg>
                        <p class="mt-2 text-sm text-gray-500 dark:text-gray-400">
                            No items in order
                        </p>
                    </div>

                    <div v-else class="space-y-3">
                        <div
                            v-for="item in orderItems"
                            :key="item.id"
                            class="bg-gray-50 dark:bg-gray-700 rounded-lg p-3"
                        >
                            <!-- Item Header -->
                            <div class="flex justify-between items-start mb-2">
                                <h4 class="font-medium text-gray-900 dark:text-white text-sm">
                                    {{ item.food_name || item.menu_item?.name || item.name || 'Unknown Item' }}
                                </h4>
                                <button
                                    @click="removeItem(item)"
                                    :disabled="loading"
                                    class="text-red-500 hover:text-red-700 disabled:opacity-50"
                                >
                                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                                    </svg>
                                </button>
                            </div>

                            <!-- Quantity Controls -->
                            <div class="flex justify-between items-center">
                                <div class="flex items-center space-x-2">
                                    <button
                                        @click="updateQuantity(item, -1)"
                                        :disabled="loading || item.quantity <= 1"
                                        class="w-8 h-8 rounded-full bg-gray-200 dark:bg-gray-600 flex items-center justify-center disabled:opacity-50"
                                    >
                                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 12H4"></path>
                                        </svg>
                                    </button>
                                    
                                    <span class="w-8 text-center font-medium">{{ item.quantity }}</span>
                                    
                                    <button
                                        @click="updateQuantity(item, 1)"
                                        :disabled="loading"
                                        class="w-8 h-8 rounded-full bg-gray-200 dark:bg-gray-600 flex items-center justify-center disabled:opacity-50"
                                    >
                                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                                        </svg>
                                    </button>
                                </div>

                                <div class="text-right">
                                    <div class="text-sm text-gray-600 dark:text-gray-400">
                                        {{ formatPrice(item.unit_price) }} × {{ item.quantity }}
                                    </div>
                                    <div class="font-medium text-gray-900 dark:text-white">
                                        {{ formatPrice(item.total_price) }}
                                    </div>
                                </div>
                            </div>

                            <!-- Special Instructions -->
                            <div v-if="item.special_instructions" class="mt-2 text-xs text-gray-600 dark:text-gray-400">
                                📝 {{ item.special_instructions }}
                            </div>

                            <!-- Kitchen Status -->
                            <div v-if="item.kitchen_status" class="mt-2">
                                <span :class="[
                                    'inline-flex px-2 py-1 text-xs font-medium rounded-full',
                                    item.kitchen_status === 'pending' ? 'bg-yellow-100 text-yellow-800' :
                                    item.kitchen_status === 'preparing' ? 'bg-blue-100 text-blue-800' :
                                    item.kitchen_status === 'ready' ? 'bg-green-100 text-green-800' :
                                    'bg-gray-100 text-gray-800'
                                ]">
                                    {{ item.kitchen_status || 'draft' }}
                                </span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Order Summary -->
                <div class="border-t border-gray-200 dark:border-gray-700 p-4 space-y-2">
                    <div class="flex justify-between text-sm">
                        <span class="text-gray-600 dark:text-gray-400">Subtotal</span>
                        <span class="text-gray-900 dark:text-white">{{ formatPrice(orderSubtotal) }}</span>
                    </div>

                    <div v-if="orderDiscount > 0" class="flex justify-between text-sm">
                        <span class="text-gray-600 dark:text-gray-400">Discount</span>
                        <span class="text-red-600">-{{ formatPrice(orderDiscount) }}</span>
                    </div>

                    <div v-if="orderTax > 0" class="flex justify-between text-sm">
                        <span class="text-gray-600 dark:text-gray-400">Tax</span>
                        <span class="text-gray-900 dark:text-white">{{ formatPrice(orderTax) }}</span>
                    </div>

                    <div class="flex justify-between font-semibold text-lg border-t pt-2">
                        <span class="text-gray-900 dark:text-white">Total</span>
                        <span class="text-gray-900 dark:text-white">{{ formatPrice(orderTotal) }}</span>
                    </div>
                </div>

                <!-- Action Buttons -->
                <div class="border-t border-gray-200 dark:border-gray-700 p-4 space-y-2">
                    <!-- Draft Mode Actions -->
                    <div v-if="isDraftMode">
                        <div class="space-y-2">
                            <button
                                @click="placeOrder"
                                :disabled="loading || !canPlaceOrder"
                                class="w-full px-4 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 font-medium"
                            >
                                🚀 Place Order
                            </button>

                            <button
                                @click="openHomeDelivery"
                                :disabled="loading || orderItems.length === 0"
                                class="w-full px-4 py-3 bg-green-600 text-white rounded-lg hover:bg-green-700 disabled:opacity-50 font-medium"
                            >
                                🏠 Home Delivery
                            </button>
                        </div>
                        <p v-if="!canPlaceOrder && orderItems.length > 0" class="text-xs text-gray-500 mt-2 text-center">
                            Select a table to place order or use home delivery
                        </p>
                        <p v-else-if="orderItems.length === 0" class="text-xs text-gray-500 mt-2 text-center">
                            Add items to place order
                        </p>
                    </div>

                    <!-- Placed Order Actions -->
                    <div v-else>
                        <div class="grid grid-cols-2 gap-2">
                            <button
                                @click="openDiscount"
                                :disabled="loading || orderItems.length === 0"
                                class="px-3 py-2 bg-orange-600 text-white rounded-lg hover:bg-orange-700 disabled:opacity-50 text-sm"
                            >
                                💰 Apply Discount
                            </button>

                            <button
                                @click="sendToKitchen"
                                :disabled="loading || !canSendToKitchen"
                                class="px-3 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 text-sm"
                            >
                                👨‍🍳 Send to Kitchen
                            </button>
                        </div>

                        <button
                            @click="openPayment"
                            :disabled="loading || !canProcessPayment"
                            class="w-full px-4 py-3 bg-green-600 text-white rounded-lg hover:bg-green-700 disabled:opacity-50 font-medium"
                        >
                            💳 Process Payment
                        </button>

                        <button
                            v-if="isOrderPaid"
                            @click="closeOrder"
                            :disabled="loading"
                            class="w-full px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 disabled:opacity-50"
                        >
                            ✅ Close Order
                        </button>
                    </div>
                </div>
            </div>

            <!-- Active Orders List -->
            <div v-else class="p-4">
                <div v-if="activeOrders.length === 0" class="text-center py-8">
                    <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"></path>
                    </svg>
                    <p class="mt-2 text-sm text-gray-500 dark:text-gray-400">
                        No active orders
                    </p>
                </div>

                <div v-else class="space-y-3">
                    <div
                        v-for="activeOrder in activeOrders"
                        :key="activeOrder.id"
                        @click="selectOrder(activeOrder)"
                        class="bg-gray-50 dark:bg-gray-700 rounded-lg p-3 cursor-pointer hover:bg-gray-100 dark:hover:bg-gray-600 transition-colors"
                    >
                        <div class="flex justify-between items-start">
                            <div>
                                <h4 class="font-medium text-gray-900 dark:text-white">
                                    {{ activeOrder.order_number }}
                                </h4>
                                <p class="text-sm text-gray-600 dark:text-gray-400">
                                    {{ activeOrder.table?.name || 'No table' }}
                                </p>
                                <p class="text-xs text-gray-500 dark:text-gray-500">
                                    {{ formatTime(activeOrder.created_at) }}
                                </p>
                            </div>
                            <div class="text-right">
                                <div class="font-medium text-gray-900 dark:text-white">
                                    {{ formatPrice(activeOrder.total_amount) }}
                                </div>
                                <span :class="[
                                    'inline-flex px-2 py-1 text-xs font-medium rounded-full',
                                    activeOrder.status === 'pending' ? 'bg-yellow-100 text-yellow-800' :
                                    activeOrder.status === 'confirmed' ? 'bg-blue-100 text-blue-800' :
                                    activeOrder.status === 'preparing' ? 'bg-orange-100 text-orange-800' :
                                    activeOrder.status === 'ready' ? 'bg-green-100 text-green-800' :
                                    'bg-gray-100 text-gray-800'
                                ]">
                                    {{ activeOrder.status }}
                                </span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<style scoped>
/* Custom scrollbar for order items */
.overflow-y-auto::-webkit-scrollbar {
    width: 4px;
}

.overflow-y-auto::-webkit-scrollbar-track {
    background: transparent;
}

.overflow-y-auto::-webkit-scrollbar-thumb {
    background: #d1d5db;
    border-radius: 2px;
}

.overflow-y-auto::-webkit-scrollbar-thumb:hover {
    background: #9ca3af;
}
</style>
