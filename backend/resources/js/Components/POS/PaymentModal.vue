<script setup>
import { ref, computed } from 'vue';
import Modal from '@/Components/Modal.vue';
import PrimaryButton from '@/Components/PrimaryButton.vue';
import SecondaryButton from '@/Components/SecondaryButton.vue';
import TextInput from '@/Components/TextInput.vue';
import InputLabel from '@/Components/InputLabel.vue';
import InputError from '@/Components/InputError.vue';

const props = defineProps({
    order: Object,
    paymentMethods: Array
});

const emit = defineEmits(['process-payment', 'close']);

// Reactive state
const form = ref({
    payment_method: 'cash',
    amount: 0,
    transaction_reference: '',
    notes: ''
});

const errors = ref({});
const processing = ref(false);

// Computed properties
const orderTotal = computed(() => props.order?.total_amount || 0);
const remainingBalance = computed(() => props.order?.remaining_balance || orderTotal.value);
const totalPaid = computed(() => props.order?.total_paid_amount || 0);

const isFullPayment = computed(() => form.value.amount >= remainingBalance.value);
const changeAmount = computed(() => {
    if (form.value.payment_method === 'cash' && form.value.amount > remainingBalance.value) {
        return form.value.amount - remainingBalance.value;
    }
    return 0;
});

// Initialize form
const initializeForm = () => {
    form.value.amount = remainingBalance.value;
    form.value.payment_method = 'cash';
    form.value.transaction_reference = '';
    form.value.notes = '';
    errors.value = {};
};

// Format currency
const formatPrice = (price) => {
    return new Intl.NumberFormat('en-US', {
        style: 'currency',
        currency: 'USD'
    }).format(price || 0);
};

// Validation
const validateForm = () => {
    errors.value = {};
    
    if (!form.value.amount || form.value.amount <= 0) {
        errors.value.amount = 'Amount is required and must be greater than 0';
    }
    
    if (form.value.amount > remainingBalance.value && form.value.payment_method !== 'cash') {
        errors.value.amount = 'Amount cannot exceed remaining balance for non-cash payments';
    }
    
    if (['card', 'digital_wallet', 'bank_transfer'].includes(form.value.payment_method) && !form.value.transaction_reference) {
        errors.value.transaction_reference = 'Transaction reference is required for this payment method';
    }
    
    return Object.keys(errors.value).length === 0;
};

// Handle payment method change
const onPaymentMethodChange = () => {
    if (form.value.payment_method !== 'cash' && form.value.amount > remainingBalance.value) {
        form.value.amount = remainingBalance.value;
    }
};

// Quick amount buttons
const setQuickAmount = (amount) => {
    form.value.amount = amount;
};

// Process payment
const processPayment = async () => {
    if (!validateForm()) return;
    
    processing.value = true;
    
    try {
        await emit('process-payment', { ...form.value });
        close();
    } catch (error) {
        console.error('Payment processing error:', error);
    } finally {
        processing.value = false;
    }
};

// Close modal
const close = () => {
    emit('close');
};

// Initialize when component mounts
initializeForm();
</script>

<template>
    <Modal :show="true" @close="close" max-width="2xl">
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-xl overflow-hidden">
            <!-- Header -->
            <div class="bg-gradient-to-r from-blue-600 to-blue-700 px-6 py-4">
                <div class="flex justify-between items-center">
                    <div class="flex items-center space-x-3">
                        <div class="bg-white/20 rounded-full p-2">
                            <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 9V7a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2m2 4h10a2 2 0 002-2v-6a2 2 0 00-2-2H9a2 2 0 00-2 2v6a2 2 0 002 2zm7-5a2 2 0 11-4 0 2 2 0 014 0z"></path>
                            </svg>
                        </div>
                        <h2 class="text-xl font-bold text-white">
                            Process Payment
                        </h2>
                    </div>
                    <button
                        @click="close"
                        class="text-white/80 hover:text-white transition-colors duration-200 p-1 rounded-full hover:bg-white/20"
                    >
                        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                        </svg>
                    </button>
                </div>
            </div>

            <div class="p-6">
                <!-- Order Summary -->
                <div class="bg-gradient-to-br from-gray-50 to-gray-100 dark:from-gray-700 dark:to-gray-800 rounded-xl p-6 mb-6 border border-gray-200 dark:border-gray-600">
                    <div class="flex items-center mb-4">
                        <div class="bg-blue-100 dark:bg-blue-900 rounded-full p-2 mr-3">
                            <svg class="w-5 h-5 text-blue-600 dark:text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                            </svg>
                        </div>
                        <h3 class="text-lg font-semibold text-gray-900 dark:text-white">Order Summary</h3>
                    </div>
                    <div class="space-y-3">
                        <div class="flex justify-between items-center">
                            <span class="text-sm font-medium text-gray-600 dark:text-gray-400">Order Number:</span>
                            <span class="text-sm font-bold text-gray-900 dark:text-white bg-gray-200 dark:bg-gray-700 px-2 py-1 rounded">{{ order.order_number }}</span>
                        </div>
                        <div class="flex justify-between items-center">
                            <span class="text-sm font-medium text-gray-600 dark:text-gray-400">Order Total:</span>
                            <span class="text-sm font-semibold text-gray-900 dark:text-white">{{ formatPrice(orderTotal) }}</span>
                        </div>
                        <div v-if="totalPaid > 0" class="flex justify-between items-center">
                            <span class="text-sm font-medium text-gray-600 dark:text-gray-400">Amount Paid:</span>
                            <span class="text-sm font-semibold text-green-600 dark:text-green-400">{{ formatPrice(totalPaid) }}</span>
                        </div>
                        <div class="flex justify-between items-center p-3 bg-blue-50 dark:bg-blue-900/30 rounded-lg border-l-4 border-blue-500">
                            <span class="text-sm font-bold text-blue-900 dark:text-blue-100">Remaining Balance:</span>
                            <span class="text-lg font-bold text-blue-900 dark:text-blue-100">{{ formatPrice(remainingBalance) }}</span>
                        </div>
                    </div>
                </div>

                <!-- Payment Form -->
                <form @submit.prevent="processPayment" class="space-y-6">
                    <!-- Payment Method -->
                    <div>
                        <div class="flex items-center mb-3">
                            <div class="bg-green-100 dark:bg-green-900 rounded-full p-1.5 mr-2">
                                <svg class="w-4 h-4 text-green-600 dark:text-green-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 10h18M7 15h1m4 0h1m-7 4h12a3 3 0 003-3V8a3 3 0 00-3-3H6a3 3 0 00-3 3v8a3 3 0 003 3z"></path>
                                </svg>
                            </div>
                            <h4 class="text-base font-semibold text-gray-900 dark:text-white">Payment Methods</h4>
                        </div>
                        <div class="grid grid-cols-2 gap-2">
                            <label
                                v-for="method in paymentMethods"
                                :key="method.value"
                                :class="[
                                    'relative flex cursor-pointer rounded-lg border-2 p-3 transition-all duration-200 hover:shadow-sm payment-method-card',
                                    form.payment_method === method.value
                                        ? 'border-blue-500 ring-1 ring-blue-200 bg-blue-50 dark:bg-blue-900/30 shadow-sm'
                                        : 'border-gray-200 dark:border-gray-600 hover:border-blue-300 dark:hover:border-blue-500 bg-white dark:bg-gray-700'
                                ]"
                            >
                                <input
                                    v-model="form.payment_method"
                                    :value="method.value"
                                    type="radio"
                                    class="sr-only"
                                    @change="onPaymentMethodChange"
                                />
                                <div class="flex w-full items-center">
                                    <div :class="[
                                        'w-8 h-8 rounded-full flex items-center justify-center mr-2.5 flex-shrink-0',
                                        form.payment_method === method.value
                                            ? 'bg-blue-500 text-white'
                                            : 'bg-gray-100 dark:bg-gray-600 text-gray-600 dark:text-gray-300'
                                    ]">
                                        <!-- Payment method icons -->
                                        <svg v-if="method.value === 'cash'" class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 9V7a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2m2 4h10a2 2 0 002-2v-6a2 2 0 00-2-2H9a2 2 0 00-2 2v6a2 2 0 002 2zm7-5a2 2 0 11-4 0 2 2 0 014 0z"></path>
                                        </svg>
                                        <svg v-else-if="method.value === 'card'" class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 10h18M7 15h1m4 0h1m-7 4h12a3 3 0 003-3V8a3 3 0 00-3-3H6a3 3 0 00-3 3v8a3 3 0 003 3z"></path>
                                        </svg>
                                        <svg v-else-if="method.value === 'mobile_payment'" class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 18h.01M8 21h8a2 2 0 002-2V5a2 2 0 00-2-2H8a2 2 0 00-2 2v14a2 2 0 002 2z"></path>
                                        </svg>
                                        <svg v-else-if="method.value === 'bank_transfer'" class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 14v3m4-3v3m4-3v3M3 21h18M3 10h18M10.5 3L12 2l1.5 1H21l-1 6H4l-1-6h7.5z"></path>
                                        </svg>
                                        <svg v-else class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                                        </svg>
                                    </div>
                                    <div class="flex-1 min-w-0">
                                        <div :class="[
                                            'font-medium text-sm truncate',
                                            form.payment_method === method.value
                                                ? 'text-blue-900 dark:text-blue-100'
                                                : 'text-gray-900 dark:text-white'
                                        ]">
                                            {{ method.label }}
                                        </div>
                                    </div>
                                    <div
                                        v-if="form.payment_method === method.value"
                                        class="flex-shrink-0 ml-2 text-blue-500"
                                    >
                                        <svg class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                                            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
                                        </svg>
                                    </div>
                                </div>
                            </label>
                        </div>
                    </div>

                    <!-- Payment Amount -->
                    <div>
                        <div class="flex items-center mb-3">
                            <div class="bg-purple-100 dark:bg-purple-900 rounded-full p-1.5 mr-2">
                                <svg class="w-4 h-4 text-purple-600 dark:text-purple-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"></path>
                                </svg>
                            </div>
                            <h4 class="text-base font-semibold text-gray-900 dark:text-white">Amount</h4>
                        </div>

                        <div class="relative">
                            <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                <span class="text-gray-500 text-lg font-semibold">$</span>
                            </div>
                            <input
                                id="amount"
                                v-model.number="form.amount"
                                type="number"
                                step="0.01"
                                min="0.01"
                                :max="form.payment_method === 'cash' ? null : remainingBalance"
                                class="block w-full pl-8 pr-4 py-3 text-xl font-bold border-2 border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white transition-all duration-200"
                                placeholder="0.00"
                                required
                            />
                        </div>
                        <InputError :message="errors.amount" class="mt-2" />

                        <!-- Quick Amount Buttons -->
                        <div class="mt-3">
                            <p class="text-xs font-medium text-gray-700 dark:text-gray-300 mb-2">Quick Selection:</p>
                            <div class="flex flex-wrap gap-1.5">
                                <button
                                    type="button"
                                    @click="setQuickAmount(remainingBalance)"
                                    class="px-3 py-1.5 text-xs font-medium bg-blue-500 text-white rounded-md hover:bg-blue-600 transition-colors duration-200"
                                >
                                    Exact ({{ formatPrice(remainingBalance) }})
                                </button>
                                <button
                                    v-if="form.payment_method === 'cash'"
                                    v-for="amount in [5, 10, 20, 50, 100]"
                                    :key="amount"
                                    type="button"
                                    @click="setQuickAmount(amount)"
                                    class="px-3 py-1.5 text-xs font-medium bg-gray-100 dark:bg-gray-600 text-gray-800 dark:text-gray-200 rounded-md hover:bg-gray-200 dark:hover:bg-gray-500 transition-colors duration-200"
                                >
                                    ${{ amount }}
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- Change Amount (for cash payments) -->
                    <div v-if="changeAmount > 0" class="bg-gradient-to-r from-green-50 to-emerald-50 dark:from-green-900/30 dark:to-emerald-900/30 rounded-xl p-4 border-l-4 border-green-500">
                        <div class="flex items-center justify-between">
                            <div class="flex items-center">
                                <div class="bg-green-500 rounded-full p-2 mr-3">
                                    <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"></path>
                                    </svg>
                                </div>
                                <span class="text-green-800 dark:text-green-200 font-semibold">
                                    Change Due:
                                </span>
                            </div>
                            <span class="text-2xl font-bold text-green-800 dark:text-green-200">
                                {{ formatPrice(changeAmount) }}
                            </span>
                        </div>
                    </div>

                    <!-- Transaction Reference -->
                    <div v-if="['card', 'mobile_payment', 'bank_transfer'].includes(form.payment_method)">
                        <div class="flex items-center mb-2">
                            <div class="bg-orange-100 dark:bg-orange-900 rounded-full p-1.5 mr-2">
                                <svg class="w-4 h-4 text-orange-600 dark:text-orange-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 4V2a1 1 0 011-1h8a1 1 0 011 1v2m-9 0h10m-9 0a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V6a2 2 0 00-2-2"></path>
                                </svg>
                            </div>
                            <h4 class="text-base font-semibold text-gray-900 dark:text-white">Transaction Reference</h4>
                        </div>
                        <input
                            id="transaction_reference"
                            v-model="form.transaction_reference"
                            type="text"
                            class="block w-full px-3 py-2.5 border-2 border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white transition-all duration-200"
                            placeholder="Enter transaction reference or ID"
                        />
                        <InputError :message="errors.transaction_reference" class="mt-2" />
                    </div>

                    <!-- Notes -->
                    <div>
                        <div class="flex items-center mb-2">
                            <div class="bg-gray-100 dark:bg-gray-600 rounded-full p-1.5 mr-2">
                                <svg class="w-4 h-4 text-gray-600 dark:text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
                                </svg>
                            </div>
                            <h4 class="text-base font-semibold text-gray-900 dark:text-white">Notes</h4>
                        </div>
                        <textarea
                            id="notes"
                            v-model="form.notes"
                            rows="2"
                            class="block w-full px-3 py-2.5 border-2 border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white transition-all duration-200 resize-none"
                            placeholder="Optional payment notes..."
                        ></textarea>
                    </div>

                    <!-- Action Buttons -->
                    <div class="flex justify-end space-x-3 pt-4 border-t border-gray-200 dark:border-gray-600">
                        <button
                            type="button"
                            @click="close"
                            :disabled="processing"
                            class="px-4 py-2 text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-gray-500 transition-all duration-200 disabled:opacity-50"
                        >
                            Cancel
                        </button>

                        <button
                            type="submit"
                            :disabled="processing"
                            class="px-6 py-2 text-sm font-bold text-white bg-gradient-to-r from-blue-600 to-blue-700 rounded-lg hover:from-blue-700 hover:to-blue-800 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition-all duration-200 disabled:opacity-50 shadow-md hover:shadow-lg"
                        >
                            <span v-if="processing" class="flex items-center">
                                <svg class="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                                    <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                                    <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                                </svg>
                                Processing...
                            </span>
                            <span v-else class="flex items-center">
                                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 9V7a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2m2 4h10a2 2 0 002-2v-6a2 2 0 00-2-2H9a2 2 0 00-2 2v6a2 2 0 002 2zm7-5a2 2 0 11-4 0 2 2 0 014 0z"></path>
                                </svg>
                                Process Payment ({{ formatPrice(form.amount) }})
                            </span>
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </Modal>
</template>

<style scoped>
/* Custom radio button styling */
input[type="radio"]:checked + div {
    @apply ring-2 ring-blue-500;
}

/* Custom animations */
@keyframes slideInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes pulse {
    0%, 100% {
        opacity: 1;
    }
    50% {
        opacity: 0.8;
    }
}

.slide-in-up {
    animation: slideInUp 0.3s ease-out;
}

.pulse-animation {
    animation: pulse 2s infinite;
}

/* Custom input focus effects */
input:focus, textarea:focus {
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

/* Hover effects for payment method cards */
.payment-method-card {
    transition: all 0.2s ease-in-out;
}

.payment-method-card:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* Custom scrollbar for textarea */
textarea::-webkit-scrollbar {
    width: 6px;
}

textarea::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 3px;
}

textarea::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 3px;
}

textarea::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}
</style>
