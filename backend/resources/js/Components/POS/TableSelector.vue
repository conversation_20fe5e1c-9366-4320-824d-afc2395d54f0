<script setup>
import { ref, computed } from 'vue';

const props = defineProps({
    floors: {
        type: Array,
        default: () => []
    },
    selectedWaiter: {
        type: Object,
        default: null
    }
});

const emit = defineEmits(['select-table', 'close']);

// Reactive state
const selectedFloor = ref(props.floors[0]?.id || null);
const searchQuery = ref('');

// Computed properties
const filteredTables = computed(() => {
    if (!selectedFloor.value) return [];

    const floor = props.floors.find(f => f.id === selectedFloor.value);
    if (!floor) return [];

    let tables = floor.tables || [];

    // Show all tables - no filtering by waiter assignment
    // This allows waiters to select any available table

    // Filter by search query
    if (searchQuery.value) {
        tables = tables.filter(table =>
            table.name.toLowerCase().includes(searchQuery.value.toLowerCase()) ||
            table.number?.toString().includes(searchQuery.value)
        );
    }

    return tables;
});

const availableTables = computed(() => {
    return filteredTables.value.filter(table => table.status === 'available');
});

// Methods
const selectFloor = (floorId) => {
    selectedFloor.value = floorId;
};

const selectTable = (table) => {
    if (table.status !== 'available') {
        alert('This table is not available');
        return;
    }
    emit('select-table', table);
};

const close = () => {
    emit('close');
};
</script>

<template>
    <!-- Lightweight Modal Overlay -->
    <div class="fixed inset-0 z-50 overflow-y-auto">
        <!-- Backdrop -->
        <div class="fixed inset-0 bg-black bg-opacity-50 transition-opacity" @click="close"></div>

        <!-- Modal -->
        <div class="flex min-h-full items-center justify-center p-4">
            <div class="relative bg-white rounded-lg shadow-xl max-w-2xl w-full max-h-[80vh] overflow-y-auto">
                <!-- Header -->
                <div class="flex items-center justify-between p-4 border-b border-gray-200">
                    <div>
                        <h3 class="text-lg font-semibold text-gray-900">Select Table</h3>
                        <p class="text-sm text-gray-600">
                            Choose any available table
                        </p>
                    </div>
                    <button
                        @click="close"
                        class="text-gray-400 hover:text-gray-600 transition-colors"
                    >
                        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                        </svg>
                    </button>
                </div>

                <!-- Content -->
                <div class="p-4">
                    <!-- Search -->
                    <div class="mb-4">
                        <div class="relative">
                            <input
                                v-model="searchQuery"
                                type="text"
                                placeholder="Search tables..."
                                class="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                            />
                            <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                <svg class="h-5 w-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                                </svg>
                            </div>
                        </div>
                    </div>

                    <!-- Floor Tabs -->
                    <div v-if="floors.length > 1" class="mb-4">
                        <div class="flex flex-wrap gap-2">
                            <button
                                v-for="floor in floors"
                                :key="floor.id"
                                @click="selectFloor(floor.id)"
                                :class="[
                                    'px-3 py-1.5 rounded-lg text-sm font-medium transition-colors',
                                    selectedFloor === floor.id
                                        ? 'bg-blue-600 text-white'
                                        : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                                ]"
                            >
                                {{ floor.name }}
                            </button>
                        </div>
                    </div>

                    <!-- Available Tables -->
                    <div v-if="availableTables.length > 0">
                        <h4 class="text-sm font-medium text-gray-900 mb-3">
                            Available Tables ({{ availableTables.length }})
                        </h4>
                        <div class="grid grid-cols-3 sm:grid-cols-4 gap-3 mb-4">
                            <button
                                v-for="table in availableTables"
                                :key="table.id"
                                @click="selectTable(table)"
                                class="p-3 bg-green-50 border-2 border-green-200 rounded-lg hover:bg-green-100 hover:border-green-300 transition-all duration-200 hover:scale-105 focus:outline-none focus:ring-2 focus:ring-green-500"
                            >
                                <div class="text-center">
                                    <div class="text-lg mb-1">🪑</div>
                                    <div class="font-semibold text-sm text-gray-900">{{ table.name }}</div>
                                    <div class="text-xs text-gray-600">👥 {{ table.capacity }}</div>
                                </div>
                            </button>
                        </div>
                    </div>

                    <!-- No Available Tables -->
                    <div v-else class="text-center py-8">
                        <div class="text-4xl mb-2">🪑</div>
                        <h3 class="text-lg font-medium text-gray-900 mb-1">No Available Tables</h3>
                        <p class="text-sm text-gray-500">
                            {{ searchQuery ? 'No tables match your search criteria.' : 'All tables are currently occupied or reserved.' }}
                        </p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>
