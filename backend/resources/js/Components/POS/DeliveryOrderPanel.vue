<template>
    <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm h-full flex flex-col">
        <!-- Header -->
        <div class="p-4 border-b border-gray-200 dark:border-gray-700">
            <div class="flex items-center justify-between">
                <h2 class="text-lg font-semibold text-gray-900 dark:text-white">
                    🚚 Delivery Orders
                </h2>
                <div class="text-sm text-gray-500">
                    {{ deliveryOrders.length }} active orders
                </div>
            </div>
        </div>

        <!-- Current Order Section -->
        <div v-if="order" class="p-4 border-b border-gray-200 dark:border-gray-700">
            <div class="bg-green-50 dark:bg-green-900/20 rounded-lg p-4">
                <div class="flex items-center justify-between mb-3">
                    <h3 class="font-medium text-green-800 dark:text-green-200">
                        Current Order #{{ order.order_number }}
                    </h3>
                    <button
                        @click="$emit('close-order')"
                        class="text-green-600 hover:text-green-800 dark:text-green-400"
                    >
                        <i class="fas fa-times"></i>
                    </button>
                </div>

                <!-- Customer Info -->
                <div v-if="selectedCustomer" class="mb-3 p-3 bg-white dark:bg-gray-800 rounded-md">
                    <div class="flex items-center justify-between">
                        <div>
                            <div class="font-medium text-gray-900 dark:text-white">
                                {{ selectedCustomer.name }}
                            </div>
                            <div class="text-sm text-gray-600 dark:text-gray-400">
                                📞 {{ selectedCustomer.phone }}
                            </div>
                            <div class="text-sm text-gray-600 dark:text-gray-400">
                                📍 {{ selectedCustomer.address }}
                            </div>
                        </div>
                        <div class="text-xs text-gray-500">
                            {{ selectedCustomer.total_orders || 0 }} orders
                        </div>
                    </div>
                </div>

                <!-- Rider Assignment -->
                <div class="mb-3">
                    <div v-if="selectedRider" class="p-3 bg-blue-50 dark:bg-blue-900/20 rounded-md">
                        <div class="flex items-center justify-between">
                            <div class="flex items-center space-x-3">
                                <div class="w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center">
                                    <i class="fas fa-motorcycle text-white text-sm"></i>
                                </div>
                                <div>
                                    <div class="font-medium text-blue-800 dark:text-blue-200">
                                        {{ selectedRider.name }}
                                    </div>
                                    <div class="text-sm text-blue-600 dark:text-blue-400">
                                        📞 {{ selectedRider.phone }}
                                    </div>
                                </div>
                            </div>
                            <div class="text-xs text-blue-600 dark:text-blue-400">
                                ⭐ {{ selectedRider.average_rating?.toFixed(1) || '5.0' }}
                            </div>
                        </div>
                    </div>
                    <button
                        v-else
                        @click="$emit('assign-rider')"
                        class="w-full p-3 border-2 border-dashed border-blue-300 rounded-md text-blue-600 hover:border-blue-400 hover:bg-blue-50 dark:border-blue-600 dark:text-blue-400 dark:hover:bg-blue-900/20 transition-colors"
                    >
                        <i class="fas fa-plus mr-2"></i>
                        Assign Rider
                    </button>
                </div>

                <!-- Order Items -->
                <div class="space-y-2 mb-4">
                    <div
                        v-for="item in order.items"
                        :key="item.id"
                        class="flex items-center justify-between p-2 bg-gray-50 dark:bg-gray-700 rounded-md"
                    >
                        <div class="flex-1">
                            <div class="font-medium text-gray-900 dark:text-white">
                                {{ item.menu_item?.name }}
                            </div>
                            <div class="text-sm text-gray-600 dark:text-gray-400">
                                ${{ item.price }} × {{ item.quantity }}
                            </div>
                        </div>
                        <div class="flex items-center space-x-2">
                            <button
                                @click="$emit('update-quantity', item.id, item.quantity - 1)"
                                class="w-6 h-6 rounded-full bg-red-100 text-red-600 hover:bg-red-200 dark:bg-red-900/30 dark:text-red-400 flex items-center justify-center"
                            >
                                <i class="fas fa-minus text-xs"></i>
                            </button>
                            <span class="w-8 text-center text-sm font-medium">{{ item.quantity }}</span>
                            <button
                                @click="$emit('update-quantity', item.id, item.quantity + 1)"
                                class="w-6 h-6 rounded-full bg-green-100 text-green-600 hover:bg-green-200 dark:bg-green-900/30 dark:text-green-400 flex items-center justify-center"
                            >
                                <i class="fas fa-plus text-xs"></i>
                            </button>
                            <button
                                @click="$emit('remove-item', item.id)"
                                class="w-6 h-6 rounded-full bg-red-100 text-red-600 hover:bg-red-200 dark:bg-red-900/30 dark:text-red-400 flex items-center justify-center ml-2"
                            >
                                <i class="fas fa-trash text-xs"></i>
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Order Summary -->
                <div class="border-t pt-3 space-y-2">
                    <div class="flex justify-between text-sm">
                        <span>Subtotal:</span>
                        <span>${{ order.subtotal?.toFixed(2) || '0.00' }}</span>
                    </div>
                    <div class="flex justify-between text-sm">
                        <span>Delivery Fee:</span>
                        <span>${{ order.delivery_fee?.toFixed(2) || '5.00' }}</span>
                    </div>
                    <div class="flex justify-between text-sm">
                        <span>Tax:</span>
                        <span>${{ order.tax_amount?.toFixed(2) || '0.00' }}</span>
                    </div>
                    <div class="flex justify-between font-semibold text-lg border-t pt-2">
                        <span>Total:</span>
                        <span>${{ order.total_amount?.toFixed(2) || '0.00' }}</span>
                    </div>
                </div>

                <!-- Action Buttons -->
                <div class="grid grid-cols-2 gap-2 mt-4">
                    <button
                        @click="$emit('open-discount')"
                        class="px-3 py-2 bg-yellow-500 text-white rounded-md hover:bg-yellow-600 transition-colors text-sm"
                    >
                        💰 Discount
                    </button>
                    <button
                        @click="$emit('send-to-kitchen')"
                        :disabled="!order.items?.length || loading"
                        class="px-3 py-2 bg-orange-500 text-white rounded-md hover:bg-orange-600 disabled:opacity-50 disabled:cursor-not-allowed transition-colors text-sm"
                    >
                        👨‍🍳 Kitchen
                    </button>
                    <button
                        @click="$emit('open-payment')"
                        :disabled="!order.items?.length || loading"
                        class="col-span-2 px-4 py-3 bg-green-600 text-white rounded-md hover:bg-green-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors font-medium"
                    >
                        💳 Process Payment
                    </button>
                </div>
            </div>
        </div>

        <!-- Active Delivery Orders -->
        <div class="flex-1 overflow-y-auto p-4">
            <h3 class="font-medium text-gray-900 dark:text-white mb-3">Active Delivery Orders</h3>
            
            <div v-if="deliveryOrders.length === 0" class="text-center py-8 text-gray-500 dark:text-gray-400">
                <i class="fas fa-truck text-4xl mb-3 opacity-50"></i>
                <p>No active delivery orders</p>
                <p class="text-sm">Create a new delivery order to get started</p>
            </div>

            <div v-else class="space-y-3">
                <div
                    v-for="deliveryOrder in deliveryOrders"
                    :key="deliveryOrder.id"
                    @click="$emit('select-order', deliveryOrder)"
                    :class="[
                        'p-3 rounded-lg border-2 cursor-pointer transition-all',
                        order?.id === deliveryOrder.id
                            ? 'border-green-500 bg-green-50 dark:bg-green-900/20'
                            : 'border-gray-200 dark:border-gray-600 hover:border-green-300 hover:bg-gray-50 dark:hover:bg-gray-700'
                    ]"
                >
                    <div class="flex items-center justify-between mb-2">
                        <div class="font-medium text-gray-900 dark:text-white">
                            #{{ deliveryOrder.order_number }}
                        </div>
                        <div class="flex items-center space-x-2">
                            <span
                                :class="[
                                    'px-2 py-1 rounded-full text-xs font-medium',
                                    getDeliveryStatusColor(deliveryOrder.delivery_status)
                                ]"
                            >
                                {{ getDeliveryStatusText(deliveryOrder.delivery_status) }}
                            </span>
                            <span class="text-sm font-medium text-gray-900 dark:text-white">
                                ${{ deliveryOrder.total_amount?.toFixed(2) }}
                            </span>
                        </div>
                    </div>
                    
                    <div class="text-sm text-gray-600 dark:text-gray-400">
                        👤 {{ deliveryOrder.customer_name }}
                    </div>
                    
                    <div v-if="deliveryOrder.rider" class="text-sm text-blue-600 dark:text-blue-400">
                        🏍️ {{ deliveryOrder.rider.name }}
                    </div>
                    
                    <div class="text-xs text-gray-500 dark:text-gray-400 mt-1">
                        {{ formatTime(deliveryOrder.created_at) }}
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script setup>
import { computed } from 'vue'

const props = defineProps({
    order: Object,
    deliveryOrders: Array,
    selectedCustomer: Object,
    selectedRider: Object,
    riders: Array,
    loading: Boolean,
})

const emit = defineEmits([
    'select-order',
    'update-quantity',
    'remove-item',
    'open-payment',
    'open-discount',
    'send-to-kitchen',
    'assign-rider',
    'close-order',
])

const getDeliveryStatusColor = (status) => {
    const colors = {
        pending: 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-400',
        assigned: 'bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-400',
        picked_up: 'bg-purple-100 text-purple-800 dark:bg-purple-900/30 dark:text-purple-400',
        en_route: 'bg-orange-100 text-orange-800 dark:bg-orange-900/30 dark:text-orange-400',
        delivered: 'bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400',
        failed: 'bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-400',
    }
    return colors[status] || colors.pending
}

const getDeliveryStatusText = (status) => {
    const texts = {
        pending: 'Pending',
        assigned: 'Assigned',
        picked_up: 'Picked Up',
        en_route: 'En Route',
        delivered: 'Delivered',
        failed: 'Failed',
    }
    return texts[status] || 'Unknown'
}

const formatTime = (timestamp) => {
    return new Date(timestamp).toLocaleTimeString('en-US', {
        hour: '2-digit',
        minute: '2-digit',
    })
}
</script>
