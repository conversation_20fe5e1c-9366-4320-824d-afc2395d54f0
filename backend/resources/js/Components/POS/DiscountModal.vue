<script setup>
import { ref, computed } from 'vue';
import Modal from '@/Components/Modal.vue';
import PrimaryButton from '@/Components/PrimaryButton.vue';
import SecondaryButton from '@/Components/SecondaryButton.vue';
import TextInput from '@/Components/TextInput.vue';
import InputLabel from '@/Components/InputLabel.vue';
import InputError from '@/Components/InputError.vue';

const props = defineProps({
    order: Object,
    discountTypes: Array
});

const emit = defineEmits(['apply-discount', 'close']);

// Reactive state
const form = ref({
    discount_type: 'percentage',
    discount_name: '',
    discount_value: 0,
    reason: ''
});

const errors = ref({});
const processing = ref(false);

// Computed properties
const orderSubtotal = computed(() => props.order?.subtotal || 0);

const calculatedDiscount = computed(() => {
    if (!form.value.discount_value || form.value.discount_value <= 0) return 0;
    
    if (form.value.discount_type === 'percentage') {
        const percentage = Math.min(form.value.discount_value, 100);
        return (orderSubtotal.value * percentage) / 100;
    } else {
        return Math.min(form.value.discount_value, orderSubtotal.value);
    }
});

const newTotal = computed(() => {
    return Math.max(0, orderSubtotal.value - calculatedDiscount.value);
});

// Initialize form
const initializeForm = () => {
    form.value = {
        discount_type: 'percentage',
        discount_name: '',
        discount_value: 0,
        reason: ''
    };
    errors.value = {};
};

// Format currency
const formatPrice = (price) => {
    return new Intl.NumberFormat('en-US', {
        style: 'currency',
        currency: 'USD'
    }).format(price || 0);
};

// Validation
const validateForm = () => {
    errors.value = {};
    
    if (!form.value.discount_name.trim()) {
        errors.value.discount_name = 'Discount name is required';
    }
    
    if (!form.value.discount_value || form.value.discount_value <= 0) {
        errors.value.discount_value = 'Discount value must be greater than 0';
    }
    
    if (form.value.discount_type === 'percentage' && form.value.discount_value > 100) {
        errors.value.discount_value = 'Percentage cannot exceed 100%';
    }
    
    if (form.value.discount_type === 'fixed_amount' && form.value.discount_value > orderSubtotal.value) {
        errors.value.discount_value = 'Discount amount cannot exceed order subtotal';
    }
    
    if (!form.value.reason.trim()) {
        errors.value.reason = 'Reason for discount is required';
    }
    
    return Object.keys(errors.value).length === 0;
};

// Quick discount buttons
const applyQuickDiscount = (type, value, name) => {
    form.value.discount_type = type;
    form.value.discount_value = value;
    form.value.discount_name = name;
};

// Apply discount
const applyDiscount = async () => {
    if (!validateForm()) return;
    
    processing.value = true;
    
    try {
        await emit('apply-discount', { ...form.value });
        close();
    } catch (error) {
        console.error('Discount application error:', error);
    } finally {
        processing.value = false;
    }
};

// Close modal
const close = () => {
    emit('close');
};

// Initialize when component mounts
initializeForm();
</script>

<template>
    <Modal :show="true" @close="close" max-width="2xl">
        <div class="p-6">
            <!-- Header -->
            <div class="flex justify-between items-center mb-6">
                <h2 class="text-xl font-semibold text-gray-900 dark:text-white">
                    {{ $t('pos.apply_discount') }}
                </h2>
                <button
                    @click="close"
                    class="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
                >
                    <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                    </svg>
                </button>
            </div>

            <!-- Order Summary -->
            <div class="bg-gray-50 dark:bg-gray-700 rounded-lg p-4 mb-6">
                <h3 class="font-medium text-gray-900 dark:text-white mb-3">{{ $t('pos.order_summary') }}</h3>
                <div class="space-y-2 text-sm">
                    <div class="flex justify-between">
                        <span class="text-gray-600 dark:text-gray-400">{{ $t('pos.order_number') }}:</span>
                        <span class="text-gray-900 dark:text-white">{{ order.order_number }}</span>
                    </div>
                    <div class="flex justify-between">
                        <span class="text-gray-600 dark:text-gray-400">{{ $t('Subtotal') }}:</span>
                        <span class="text-gray-900 dark:text-white">{{ formatPrice(orderSubtotal) }}</span>
                    </div>
                    <div v-if="calculatedDiscount > 0" class="flex justify-between text-red-600">
                        <span>{{ $t('Discount') }}:</span>
                        <span>-{{ formatPrice(calculatedDiscount) }}</span>
                    </div>
                    <div class="flex justify-between font-semibold border-t pt-2">
                        <span class="text-gray-900 dark:text-white">{{ $t('New Total') }}:</span>
                        <span class="text-gray-900 dark:text-white">{{ formatPrice(newTotal) }}</span>
                    </div>
                </div>
            </div>

            <!-- Quick Discount Buttons -->
            <div class="mb-6">
                <h3 class="text-sm font-medium text-gray-900 dark:text-white mb-3">{{ $t('Quick Discounts') }}</h3>
                <div class="grid grid-cols-2 md:grid-cols-4 gap-2">
                    <button
                        type="button"
                        @click="applyQuickDiscount('percentage', 5, '5% Discount')"
                        class="px-3 py-2 text-sm bg-blue-100 text-blue-800 rounded-lg hover:bg-blue-200 transition-colors"
                    >
                        5% Off
                    </button>
                    <button
                        type="button"
                        @click="applyQuickDiscount('percentage', 10, '10% Discount')"
                        class="px-3 py-2 text-sm bg-blue-100 text-blue-800 rounded-lg hover:bg-blue-200 transition-colors"
                    >
                        10% Off
                    </button>
                    <button
                        type="button"
                        @click="applyQuickDiscount('percentage', 15, '15% Discount')"
                        class="px-3 py-2 text-sm bg-blue-100 text-blue-800 rounded-lg hover:bg-blue-200 transition-colors"
                    >
                        15% Off
                    </button>
                    <button
                        type="button"
                        @click="applyQuickDiscount('percentage', 20, '20% Discount')"
                        class="px-3 py-2 text-sm bg-blue-100 text-blue-800 rounded-lg hover:bg-blue-200 transition-colors"
                    >
                        20% Off
                    </button>
                    <button
                        type="button"
                        @click="applyQuickDiscount('fixed_amount', 5, '$5 Off')"
                        class="px-3 py-2 text-sm bg-green-100 text-green-800 rounded-lg hover:bg-green-200 transition-colors"
                    >
                        $5 Off
                    </button>
                    <button
                        type="button"
                        @click="applyQuickDiscount('fixed_amount', 10, '$10 Off')"
                        class="px-3 py-2 text-sm bg-green-100 text-green-800 rounded-lg hover:bg-green-200 transition-colors"
                    >
                        $10 Off
                    </button>
                    <button
                        type="button"
                        @click="applyQuickDiscount('percentage', 100, 'Complimentary')"
                        class="px-3 py-2 text-sm bg-purple-100 text-purple-800 rounded-lg hover:bg-purple-200 transition-colors"
                    >
                        Free
                    </button>
                    <button
                        type="button"
                        @click="applyQuickDiscount('percentage', 50, 'Staff Discount')"
                        class="px-3 py-2 text-sm bg-orange-100 text-orange-800 rounded-lg hover:bg-orange-200 transition-colors"
                    >
                        Staff 50%
                    </button>
                </div>
            </div>

            <!-- Discount Form -->
            <form @submit.prevent="applyDiscount" class="space-y-6">
                <!-- Discount Type -->
                <div>
                    <InputLabel for="discount_type" :value="$t('pos.discount_types')" />
                    <div class="mt-2 grid grid-cols-2 gap-3">
                        <label
                            v-for="type in discountTypes"
                            :key="type.value"
                            :class="[
                                'relative flex cursor-pointer rounded-lg border p-4 focus:outline-none',
                                form.discount_type === type.value
                                    ? 'border-blue-600 ring-2 ring-blue-600 bg-blue-50 dark:bg-blue-900/20'
                                    : 'border-gray-300 dark:border-gray-600 hover:border-gray-400'
                            ]"
                        >
                            <input
                                v-model="form.discount_type"
                                :value="type.value"
                                type="radio"
                                class="sr-only"
                            />
                            <div class="flex w-full items-center justify-between">
                                <div class="flex items-center">
                                    <div class="text-sm">
                                        <div class="font-medium text-gray-900 dark:text-white">
                                            {{ type.label }}
                                        </div>
                                    </div>
                                </div>
                                <div
                                    v-if="form.discount_type === type.value"
                                    class="shrink-0 text-blue-600"
                                >
                                    <svg class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                                        <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
                                    </svg>
                                </div>
                            </div>
                        </label>
                    </div>
                </div>

                <!-- Discount Name -->
                <div>
                    <InputLabel for="discount_name" :value="$t('Discount Name')" />
                    <TextInput
                        id="discount_name"
                        v-model="form.discount_name"
                        type="text"
                        class="mt-2 block w-full"
                        :placeholder="$t('Enter discount name')"
                        required
                    />
                    <InputError :message="errors.discount_name" class="mt-2" />
                </div>

                <!-- Discount Value -->
                <div>
                    <InputLabel for="discount_value" :value="form.discount_type === 'percentage' ? $t('Discount Percentage') : $t('Discount Amount')" />
                    <div class="mt-2 relative">
                        <TextInput
                            id="discount_value"
                            v-model.number="form.discount_value"
                            type="number"
                            step="0.01"
                            min="0.01"
                            :max="form.discount_type === 'percentage' ? 100 : orderSubtotal"
                            class="block w-full pr-12"
                            required
                        />
                        <div class="absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none">
                            <span class="text-gray-500 text-sm">
                                {{ form.discount_type === 'percentage' ? '%' : '$' }}
                            </span>
                        </div>
                    </div>
                    <InputError :message="errors.discount_value" class="mt-2" />
                    
                    <!-- Calculated Discount Display -->
                    <div v-if="calculatedDiscount > 0" class="mt-2 text-sm text-gray-600 dark:text-gray-400">
                        {{ $t('Discount Amount') }}: {{ formatPrice(calculatedDiscount) }}
                    </div>
                </div>

                <!-- Reason -->
                <div>
                    <InputLabel for="reason" :value="$t('Reason for Discount')" />
                    <textarea
                        id="reason"
                        v-model="form.reason"
                        rows="3"
                        class="mt-2 block w-full border-gray-300 dark:border-gray-700 dark:bg-gray-900 dark:text-gray-300 focus:border-indigo-500 dark:focus:border-indigo-600 focus:ring-indigo-500 dark:focus:ring-indigo-600 rounded-md shadow-sm"
                        :placeholder="$t('Enter reason for applying this discount')"
                        required
                    ></textarea>
                    <InputError :message="errors.reason" class="mt-2" />
                </div>

                <!-- Action Buttons -->
                <div class="flex justify-end space-x-3 pt-4 border-t border-gray-200 dark:border-gray-700">
                    <SecondaryButton @click="close" :disabled="processing">
                        {{ $t('Cancel') }}
                    </SecondaryButton>
                    
                    <PrimaryButton type="submit" :disabled="processing || calculatedDiscount <= 0">
                        <span v-if="processing">{{ $t('Applying...') }}</span>
                        <span v-else>
                            💰 {{ $t('Apply Discount') }} (-{{ formatPrice(calculatedDiscount) }})
                        </span>
                    </PrimaryButton>
                </div>
            </form>
        </div>
    </Modal>
</template>

<style scoped>
/* Custom radio button styling */
input[type="radio"]:checked + div {
    @apply ring-2 ring-blue-600;
}
</style>
