<template>
    <div class="fixed inset-0 z-50 overflow-y-auto" aria-labelledby="modal-title" role="dialog" aria-modal="true">
        <div class="flex items-end justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
            <div class="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" aria-hidden="true" @click="$emit('close')"></div>

            <!-- This element is to trick the browser into centering the modal contents. -->
            <span class="hidden sm:inline-block sm:align-middle sm:h-screen" aria-hidden="true">&#8203;</span>

            <div class="relative inline-block align-bottom bg-white dark:bg-gray-800 rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-4xl sm:w-full">
                <div class="bg-white dark:bg-gray-800 px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
                    <div class="sm:flex sm:items-start">
                        <div class="mx-auto flex-shrink-0 flex items-center justify-center h-12 w-12 rounded-full bg-green-100 dark:bg-green-900 sm:mx-0 sm:h-10 sm:w-10">
                            <svg class="h-6 w-6 text-green-600 dark:text-green-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 3h2l.4 2M7 13h10l4-8H5.4m0 0L7 13m0 0l-1.1 5H17M9 19.5a1.5 1.5 0 11-3 0 1.5 1.5 0 013 0zM20.5 19.5a1.5 1.5 0 11-3 0 1.5 1.5 0 013 0z"></path>
                            </svg>
                        </div>
                        <div class="mt-3 text-center sm:mt-0 sm:ml-4 sm:text-left flex-1">
                            <h3 class="text-lg leading-6 font-medium text-gray-900 dark:text-gray-100" id="modal-title">
                                Home Delivery Order
                            </h3>
                            <div class="mt-2">
                                <p class="text-sm text-gray-500 dark:text-gray-400">
                                    Set up delivery details for this order
                                </p>
                            </div>
                        </div>
                    </div>

                    <form @submit.prevent="submitDeliveryOrder" class="mt-6">
                        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                            <!-- Left Column - Customer & Delivery Info -->
                            <div class="space-y-6">
                                <!-- Customer Information -->
                                <div>
                                    <h4 class="text-md font-medium text-gray-900 dark:text-gray-100 mb-4">Customer Information</h4>
                                    <div class="space-y-4">
                                        <div>
                                            <label for="customer_name" class="block text-sm font-medium text-gray-700 dark:text-gray-300">
                                                Customer Name *
                                            </label>
                                            <input
                                                type="text"
                                                id="customer_name"
                                                v-model="deliveryForm.customer_name"
                                                required
                                                class="mt-1 block w-full border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-gray-300 rounded-md shadow-sm focus:ring-green-500 focus:border-green-500"
                                                placeholder="Enter customer name"
                                            />
                                        </div>
                                        <div>
                                            <label for="customer_phone" class="block text-sm font-medium text-gray-700 dark:text-gray-300">
                                                Phone Number *
                                            </label>
                                            <input
                                                type="tel"
                                                id="customer_phone"
                                                v-model="deliveryForm.customer_phone"
                                                required
                                                class="mt-1 block w-full border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-gray-300 rounded-md shadow-sm focus:ring-green-500 focus:border-green-500"
                                                placeholder="Enter phone number"
                                            />
                                        </div>
                                        <div>
                                            <label for="customer_email" class="block text-sm font-medium text-gray-700 dark:text-gray-300">
                                                Email (Optional)
                                            </label>
                                            <input
                                                type="email"
                                                id="customer_email"
                                                v-model="deliveryForm.customer_email"
                                                class="mt-1 block w-full border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-gray-300 rounded-md shadow-sm focus:ring-green-500 focus:border-green-500"
                                                placeholder="Enter email address"
                                            />
                                        </div>
                                    </div>
                                </div>

                                <!-- Delivery Address -->
                                <div>
                                    <h4 class="text-md font-medium text-gray-900 dark:text-gray-100 mb-4">Delivery Address</h4>
                                    <div class="space-y-4">
                                        <div>
                                            <label for="delivery_address" class="block text-sm font-medium text-gray-700 dark:text-gray-300">
                                                Address *
                                            </label>
                                            <input
                                                type="text"
                                                id="delivery_address"
                                                ref="addressInput"
                                                v-model="deliveryForm.delivery_address"
                                                required
                                                class="mt-1 block w-full border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-gray-300 rounded-md shadow-sm focus:ring-green-500 focus:border-green-500"
                                                placeholder="Enter delivery address or click on map"
                                            />
                                        </div>
                                        <div>
                                            <label for="delivery_instructions" class="block text-sm font-medium text-gray-700 dark:text-gray-300">
                                                Delivery Instructions
                                            </label>
                                            <textarea
                                                id="delivery_instructions"
                                                v-model="deliveryForm.delivery_instructions"
                                                rows="3"
                                                class="mt-1 block w-full border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-gray-300 rounded-md shadow-sm focus:ring-green-500 focus:border-green-500"
                                                placeholder="Special delivery instructions (e.g., gate code, apartment number)"
                                            ></textarea>
                                        </div>
                                    </div>
                                </div>

                                <!-- Delivery Details -->
                                <div>
                                    <h4 class="text-md font-medium text-gray-900 dark:text-gray-100 mb-4">Delivery Details</h4>
                                    <div class="space-y-4">
                                        <div>
                                            <label for="delivery_rider" class="block text-sm font-medium text-gray-700 dark:text-gray-300">
                                                Delivery Rider
                                            </label>
                                            <select
                                                id="delivery_rider"
                                                v-model="deliveryForm.delivery_rider_id"
                                                class="mt-1 block w-full border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-gray-300 rounded-md shadow-sm focus:ring-green-500 focus:border-green-500"
                                            >
                                                <option value="">Auto-assign available rider</option>
                                                <option
                                                    v-for="rider in availableRiders"
                                                    :key="rider.id"
                                                    :value="rider.id"
                                                >
                                                    {{ rider.name }} ({{ rider.status }})
                                                </option>
                                            </select>
                                        </div>
                                        
                                        <div class="grid grid-cols-2 gap-4">
                                            <div>
                                                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300">
                                                    Estimated Delivery Time
                                                </label>
                                                <div class="mt-1 text-sm text-gray-900 dark:text-gray-100 bg-gray-50 dark:bg-gray-700 p-2 rounded-md">
                                                    {{ estimatedDeliveryTime }} minutes
                                                </div>
                                            </div>
                                            <div>
                                                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300">
                                                    Delivery Fee
                                                </label>
                                                <div class="mt-1 text-sm text-gray-900 dark:text-gray-100 bg-gray-50 dark:bg-gray-700 p-2 rounded-md">
                                                    ${{ deliveryFee.toFixed(2) }}
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Right Column - Map & Order Summary -->
                            <div class="space-y-6">
                                <!-- Interactive Map -->
                                <div>
                                    <h4 class="text-md font-medium text-gray-900 dark:text-gray-100 mb-4">Delivery Location</h4>
                                    <div class="relative">
                                        <div
                                            ref="mapContainer"
                                            class="w-full h-64 bg-gray-200 dark:bg-gray-700 rounded-lg"
                                            :class="{ 'opacity-50': mapLoading }"
                                        ></div>
                                        <div v-if="mapLoading" class="absolute inset-0 flex items-center justify-center">
                                            <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-green-600"></div>
                                        </div>
                                        <div v-if="mapError" class="absolute inset-0 flex items-center justify-center bg-red-50 dark:bg-red-900 rounded-lg">
                                            <p class="text-red-600 dark:text-red-400 text-sm">{{ mapError }}</p>
                                        </div>
                                    </div>
                                    <p class="text-xs text-gray-500 dark:text-gray-400 mt-2">
                                        Click on the map to set delivery location or use the address search above
                                    </p>
                                </div>

                                <!-- Order Summary -->
                                <div>
                                    <h4 class="text-md font-medium text-gray-900 dark:text-gray-100 mb-4">Order Summary</h4>
                                    <div class="bg-gray-50 dark:bg-gray-700 rounded-lg p-4 space-y-3">
                                        <div class="space-y-2">
                                            <div
                                                v-for="item in orderItems"
                                                :key="item.id"
                                                class="flex justify-between text-sm"
                                            >
                                                <span>{{ item.name || item.food_name }} × {{ item.quantity }}</span>
                                                <span>${{ (item.total_price || (item.unit_price * item.quantity)).toFixed(2) }}</span>
                                            </div>
                                        </div>
                                        
                                        <div class="border-t border-gray-200 dark:border-gray-600 pt-3 space-y-2">
                                            <div class="flex justify-between text-sm">
                                                <span>Subtotal</span>
                                                <span>${{ orderSubtotal.toFixed(2) }}</span>
                                            </div>
                                            <div class="flex justify-between text-sm">
                                                <span>Delivery Fee</span>
                                                <span>${{ deliveryFee.toFixed(2) }}</span>
                                            </div>
                                            <div class="flex justify-between font-semibold text-base border-t border-gray-200 dark:border-gray-600 pt-2">
                                                <span>Total</span>
                                                <span>${{ (orderSubtotal + deliveryFee).toFixed(2) }}</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Action Buttons -->
                        <div class="mt-6 sm:flex sm:flex-row-reverse">
                            <button
                                type="submit"
                                :disabled="processing || !isFormValid"
                                class="w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-green-600 text-base font-medium text-white hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 sm:ml-3 sm:w-auto sm:text-sm disabled:bg-gray-400 disabled:cursor-not-allowed"
                            >
                                {{ processing ? 'Creating Order...' : 'Create Delivery Order' }}
                            </button>
                            <button
                                type="button"
                                @click="$emit('close')"
                                class="mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 dark:border-gray-600 shadow-sm px-4 py-2 bg-white dark:bg-gray-700 text-base font-medium text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm"
                            >
                                Cancel
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted, nextTick, watch } from 'vue'

const props = defineProps({
    show: {
        type: Boolean,
        default: false
    },
    orderItems: {
        type: Array,
        required: true
    },
    selectedCustomer: {
        type: Object,
        default: null
    },
    branch: {
        type: Object,
        required: true
    },
    availableRiders: {
        type: Array,
        default: () => []
    }
})

const emit = defineEmits(['close', 'submit'])

// Reactive state
const processing = ref(false)
const mapLoading = ref(true)
const mapError = ref('')
const mapContainer = ref(null)
const addressInput = ref(null)

// Google Maps objects
let map = null
let marker = null
let autocomplete = null
let geocoder = null

// Form data
const deliveryForm = reactive({
    customer_name: props.selectedCustomer?.name || '',
    customer_phone: props.selectedCustomer?.phone || '',
    customer_email: props.selectedCustomer?.email || '',
    delivery_address: '',
    delivery_latitude: null,
    delivery_longitude: null,
    delivery_instructions: '',
    delivery_rider_id: '',
})

// Computed properties
const orderSubtotal = computed(() => {
    return props.orderItems.reduce((total, item) => {
        return total + (item.total_price || (item.unit_price * item.quantity))
    }, 0)
})

const deliveryFee = computed(() => {
    // Calculate delivery fee based on distance/zone
    // For now, return a base fee - this will be enhanced with zone calculation
    return 5.00
})

const estimatedDeliveryTime = computed(() => {
    // Calculate estimated delivery time based on distance
    // For now, return a base time - this will be enhanced with distance calculation
    return 30
})

const isFormValid = computed(() => {
    return deliveryForm.customer_name &&
           deliveryForm.customer_phone &&
           deliveryForm.delivery_address &&
           deliveryForm.delivery_latitude &&
           deliveryForm.delivery_longitude
})

// Methods
const submitDeliveryOrder = async () => {
    if (!isFormValid.value) return

    processing.value = true
    try {
        const orderData = {
            ...deliveryForm,
            order_type: 'delivery',
            items: props.orderItems,
            delivery_fee: deliveryFee.value,
            estimated_delivery_time: estimatedDeliveryTime.value,
            branch_id: props.branch.id
        }

        emit('submit', orderData)
    } catch (error) {
        console.error('Error submitting delivery order:', error)
        alert('Failed to create delivery order. Please try again.')
    } finally {
        processing.value = false
    }
}

// Initialize component
onMounted(() => {
    if (props.show) {
        nextTick(() => {
            initializeGoogleMaps()
        })
    }
})

// Watch for show prop changes
watch(() => props.show, (newValue) => {
    if (newValue) {
        nextTick(() => {
            initializeGoogleMaps()
        })
    }
})

// Google Maps initialization
const initializeGoogleMaps = async () => {
    try {
        mapLoading.value = true
        
        // Check if Google Maps is already loaded
        if (typeof google !== 'undefined' && google.maps) {
            await initializeMap()
            return
        }

        // Load Google Maps script if not already loaded
        const apiKey = window.googleMapsApiKey || ''
        if (!apiKey) {
            throw new Error('Google Maps API key not configured')
        }

        // Google Maps will be loaded by the existing script in the layout
        // Wait for it to be available
        let attempts = 0
        const maxAttempts = 50
        
        const checkGoogleMaps = () => {
            if (typeof google !== 'undefined' && google.maps) {
                initializeMap()
            } else if (attempts < maxAttempts) {
                attempts++
                setTimeout(checkGoogleMaps, 100)
            } else {
                throw new Error('Google Maps failed to load')
            }
        }
        
        checkGoogleMaps()
    } catch (error) {
        console.error('Error loading Google Maps:', error)
        mapError.value = 'Failed to load map. Please try refreshing the page.'
        mapLoading.value = false
    }
}

const initializeMap = async () => {
    try {
        await nextTick()
        
        if (!mapContainer.value) {
            throw new Error('Map container not found')
        }

        geocoder = new google.maps.Geocoder()

        // Default center (branch location or default)
        const defaultCenter = {
            lat: props.branch.latitude || 40.7128,
            lng: props.branch.longitude || -74.0060
        }

        // Create map
        map = new google.maps.Map(mapContainer.value, {
            zoom: 13,
            center: defaultCenter,
            mapTypeControl: false,
            streetViewControl: false,
            fullscreenControl: false
        })

        // Create marker
        marker = new google.maps.Marker({
            position: defaultCenter,
            map: map,
            draggable: true,
            title: 'Delivery Location'
        })

        // Add map click listener
        map.addListener('click', (event) => {
            updateDeliveryLocation(event.latLng)
        })

        // Add marker drag listener
        marker.addListener('dragend', (event) => {
            updateDeliveryLocation(event.latLng)
        })

        // Initialize autocomplete
        if (addressInput.value && google.maps.places) {
            autocomplete = new google.maps.places.Autocomplete(addressInput.value, {
                fields: ['place_id', 'geometry', 'name', 'formatted_address']
            })

            autocomplete.addListener('place_changed', () => {
                const place = autocomplete.getPlace()
                if (place.geometry) {
                    updateDeliveryLocation(place.geometry.location)
                    map.setCenter(place.geometry.location)
                    deliveryForm.delivery_address = place.formatted_address
                }
            })
        }

        mapLoading.value = false
    } catch (error) {
        console.error('Error initializing map:', error)
        mapError.value = 'Failed to initialize map. Please try refreshing the page.'
        mapLoading.value = false
    }
}

const updateDeliveryLocation = (latLng) => {
    const lat = latLng.lat()
    const lng = latLng.lng()
    
    deliveryForm.delivery_latitude = lat
    deliveryForm.delivery_longitude = lng
    
    marker.setPosition(latLng)
    
    // Reverse geocode to get address
    if (geocoder) {
        geocoder.geocode({ location: latLng }, (results, status) => {
            if (status === 'OK' && results[0]) {
                deliveryForm.delivery_address = results[0].formatted_address
            }
        })
    }
}
</script>
