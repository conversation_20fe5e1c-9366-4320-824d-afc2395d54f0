<script setup>
import { computed } from 'vue';

const props = defineProps({
    items: {
        type: Array,
        default: () => []
    },
    loading: {
        type: Boolean,
        default: false
    }
});

const emit = defineEmits(['add-item']);

// Format price for display
const formatPrice = (price) => {
    return new Intl.NumberFormat('en-US', {
        style: 'currency',
        currency: 'USD'
    }).format(price);
};

// Handle item click
const addItem = (item) => {
    if (props.loading) return;
    emit('add-item', item);
};

// Check if item is available
const isItemAvailable = (item) => {
    return item.is_available !== false;
};

// Handle image loading errors
const handleImageError = (event) => {
    if (event.target.dataset.errorHandled) return;
    event.target.dataset.errorHandled = 'true';

    // Use inline SVG placeholder
    const svgPlaceholder = 'data:image/svg+xml;base64,' + btoa(`
        <svg xmlns="http://www.w3.org/2000/svg" width="200" height="200" viewBox="0 0 200 200">
            <rect width="200" height="200" fill="#f3f4f6"/>
            <circle cx="80" cy="70" r="12" fill="#d1d5db"/>
            <polygon points="40,140 80,100 120,120 160,80 180,140" fill="#d1d5db"/>
            <text x="100" y="170" text-anchor="middle" fill="#9ca3af" font-size="16">Menu Item</text>
        </svg>
    `);

    event.target.src = svgPlaceholder;
};
</script>

<template>
    <div class="menu-item-grid">
        <!-- Loading State -->
        <div v-if="loading" class="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
            <div 
                v-for="n in 8" 
                :key="n"
                class="bg-gray-200 dark:bg-gray-700 rounded-lg p-4 animate-pulse"
            >
                <div class="bg-gray-300 dark:bg-gray-600 h-32 rounded-lg mb-3"></div>
                <div class="bg-gray-300 dark:bg-gray-600 h-4 rounded mb-2"></div>
                <div class="bg-gray-300 dark:bg-gray-600 h-3 rounded w-3/4"></div>
            </div>
        </div>

        <!-- Menu Items Grid -->
        <div v-else-if="items.length > 0" class="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 gap-4">
            <div
                v-for="item in items"
                :key="item.id"
                @click="addItem(item)"
                :class="[
                    'bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden cursor-pointer transition-all duration-200 hover:shadow-lg hover:scale-105',
                    isItemAvailable(item)
                        ? 'hover:border-blue-300'
                        : 'opacity-50 cursor-not-allowed',
                    loading ? 'pointer-events-none' : ''
                ]"
            >
                <!-- Item Image -->
                <div class="relative">
                    <img
                        v-if="item.primary_image_url || item.image_url"
                        :src="item.primary_image_url || item.image_url"
                        :alt="item.name"
                        class="w-full h-32 object-cover"
                        @error="handleImageError"
                    />
                    <div
                        v-else
                        class="w-full h-32 bg-gray-100 dark:bg-gray-700 rounded-lg flex items-center justify-center"
                    >
                        <svg class="w-8 h-8 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                        </svg>
                    </div>

                    <!-- Availability Badge -->
                    <div
                        v-if="!isItemAvailable(item)"
                        class="absolute top-2 right-2 bg-red-500 text-white text-xs px-2 py-1 rounded-full"
                    >
                        {{ $t('Unavailable') }}
                    </div>

                    <!-- Combo Badge -->
                    <div
                        v-if="item.is_combo"
                        class="absolute top-2 left-2 bg-indigo-600 text-white text-xs px-2 py-1 rounded-full font-medium"
                    >
                        Combo
                    </div>

                    <!-- Dietary Badges -->
                    <div :class="[
                        'absolute flex flex-col space-y-1',
                        item.is_combo ? 'top-8 left-2' : 'top-2 left-2'
                    ]">
                        <span
                            v-if="item.is_vegetarian"
                            class="bg-green-500 text-white text-xs px-1.5 py-0.5 rounded-full"
                            title="Vegetarian"
                        >
                            🌱
                        </span>
                        <span
                            v-if="item.is_vegan"
                            class="bg-green-600 text-white text-xs px-1.5 py-0.5 rounded-full"
                            title="Vegan"
                        >
                            🌿
                        </span>
                        <span
                            v-if="item.is_spicy"
                            class="bg-red-500 text-white text-xs px-1.5 py-0.5 rounded-full"
                            title="Spicy"
                        >
                            🌶️
                        </span>
                        <span
                            v-if="item.is_gluten_free"
                            class="bg-blue-500 text-white text-xs px-1.5 py-0.5 rounded-full"
                            title="Gluten Free"
                        >
                            GF
                        </span>
                    </div>
                </div>

                <!-- Item Details -->
                <div class="p-3 space-y-2">
                    <h3 class="font-semibold text-gray-900 text-sm line-clamp-2">
                        {{ item.name }}
                    </h3>

                    <div class="flex items-center justify-between">
                        <span class="font-bold text-lg text-blue-600">
                            {{ formatPrice(item.price) }}
                        </span>

                        <button
                            v-if="isItemAvailable(item)"
                            @click.stop="addItem(item)"
                            class="p-1.5 bg-blue-600 text-white rounded-full hover:bg-blue-700 transition-colors"
                            :disabled="loading"
                        >
                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                            </svg>
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Empty State -->
        <div v-else class="text-center py-12">
            <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"></path>
            </svg>
            <h3 class="mt-2 text-sm font-medium text-gray-900 dark:text-white">{{ $t('No menu items') }}</h3>
            <p class="mt-1 text-sm text-gray-500 dark:text-gray-400">{{ $t('No menu items available in this category') }}</p>
        </div>
    </div>
</template>

<style scoped>
.line-clamp-2 {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.menu-item-grid {
    min-height: 400px;
}
</style>
