<template>
    <div class="fixed inset-0 z-50 overflow-y-auto">
        <div class="flex items-center justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
            <!-- Background overlay -->
            <div class="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" @click="$emit('close')"></div>

            <!-- Modal panel -->
            <div class="inline-block align-bottom bg-white dark:bg-gray-800 rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-3xl sm:w-full">
                <!-- Header -->
                <div class="bg-white dark:bg-gray-800 px-6 py-4 border-b border-gray-200 dark:border-gray-700">
                    <div class="flex items-center justify-between">
                        <h3 class="text-lg font-medium text-gray-900 dark:text-white">
                            🏍️ Select Delivery Rider
                        </h3>
                        <button
                            @click="$emit('close')"
                            class="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
                        >
                            <i class="fas fa-times text-xl"></i>
                        </button>
                    </div>
                </div>

                <!-- Filter Options -->
                <div class="bg-gray-50 dark:bg-gray-700 px-6 py-4">
                    <div class="flex space-x-4">
                        <div class="flex-1">
                            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                                Filter by Status
                            </label>
                            <select
                                v-model="statusFilter"
                                class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-800 dark:text-white"
                            >
                                <option value="">All Riders</option>
                                <option value="available">Available Only</option>
                                <option value="busy">Busy</option>
                                <option value="offline">Offline</option>
                            </select>
                        </div>
                        
                        <div class="flex-1">
                            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                                Sort by
                            </label>
                            <select
                                v-model="sortBy"
                                class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-800 dark:text-white"
                            >
                                <option value="rating">Rating</option>
                                <option value="deliveries">Total Deliveries</option>
                                <option value="current_load">Current Load</option>
                            </select>
                        </div>
                    </div>
                </div>

                <!-- Riders List -->
                <div class="bg-white dark:bg-gray-800 max-h-96 overflow-y-auto">
                    <div v-if="filteredRiders.length === 0" class="text-center py-8 text-gray-500 dark:text-gray-400">
                        <i class="fas fa-motorcycle text-4xl mb-3 opacity-50"></i>
                        <p>No riders available</p>
                        <p class="text-sm">Try adjusting your filters</p>
                    </div>

                    <div v-else class="divide-y divide-gray-200 dark:divide-gray-700">
                        <div
                            v-for="rider in filteredRiders"
                            :key="rider.id"
                            @click="selectRider(rider)"
                            :class="[
                                'p-4 cursor-pointer transition-colors',
                                rider.delivery_status === 'available'
                                    ? 'hover:bg-green-50 dark:hover:bg-green-900/20'
                                    : 'hover:bg-gray-50 dark:hover:bg-gray-700 opacity-75'
                            ]"
                        >
                            <div class="flex items-center justify-between">
                                <div class="flex items-center space-x-4">
                                    <!-- Rider Avatar -->
                                    <div class="relative">
                                        <div
                                            v-if="rider.profile_photo_url"
                                            class="w-12 h-12 rounded-full bg-cover bg-center"
                                            :style="{ backgroundImage: `url(${rider.profile_photo_url})` }"
                                        ></div>
                                        <div
                                            v-else
                                            class="w-12 h-12 bg-blue-500 rounded-full flex items-center justify-center"
                                        >
                                            <i class="fas fa-motorcycle text-white"></i>
                                        </div>
                                        
                                        <!-- Status Indicator -->
                                        <div
                                            :class="[
                                                'absolute -bottom-1 -right-1 w-4 h-4 rounded-full border-2 border-white',
                                                getStatusColor(rider.delivery_status)
                                            ]"
                                        ></div>
                                    </div>

                                    <!-- Rider Info -->
                                    <div>
                                        <div class="flex items-center space-x-2">
                                            <div class="font-medium text-gray-900 dark:text-white">
                                                {{ rider.name }}
                                            </div>
                                            <span
                                                :class="[
                                                    'px-2 py-1 rounded-full text-xs font-medium',
                                                    getStatusBadgeColor(rider.delivery_status)
                                                ]"
                                            >
                                                {{ getStatusText(rider.delivery_status) }}
                                            </span>
                                        </div>
                                        
                                        <div class="text-sm text-gray-600 dark:text-gray-400">
                                            📞 {{ rider.phone }}
                                        </div>
                                        
                                        <div class="text-sm text-gray-600 dark:text-gray-400">
                                            🚗 {{ getVehicleIcon(rider.vehicle_type) }} {{ rider.vehicle_type }}
                                        </div>
                                    </div>
                                </div>

                                <!-- Rider Stats -->
                                <div class="text-right">
                                    <div class="flex items-center space-x-1 mb-1">
                                        <i class="fas fa-star text-yellow-400 text-sm"></i>
                                        <span class="text-sm font-medium text-gray-900 dark:text-white">
                                            {{ rider.average_rating?.toFixed(1) || '5.0' }}
                                        </span>
                                        <span class="text-xs text-gray-500">
                                            ({{ rider.rating_count || 0 }})
                                        </span>
                                    </div>
                                    
                                    <div class="text-xs text-gray-500 dark:text-gray-400">
                                        {{ rider.total_deliveries || 0 }} deliveries
                                    </div>
                                    
                                    <div class="text-xs text-gray-500 dark:text-gray-400">
                                        {{ rider.current_delivery_count || 0 }}/{{ rider.max_concurrent_deliveries || 3 }} active
                                    </div>
                                    
                                    <div v-if="rider.last_active_at" class="text-xs text-gray-500 dark:text-gray-400">
                                        Last active: {{ formatTime(rider.last_active_at) }}
                                    </div>
                                </div>
                            </div>

                            <!-- Additional Info for Available Riders -->
                            <div v-if="rider.delivery_status === 'available'" class="mt-3 p-2 bg-green-50 dark:bg-green-900/20 rounded-md">
                                <div class="flex items-center justify-between text-sm">
                                    <span class="text-green-700 dark:text-green-300">
                                        ✅ Available for delivery
                                    </span>
                                    <span class="text-green-600 dark:text-green-400 font-medium">
                                        Can take {{ (rider.max_concurrent_deliveries || 3) - (rider.current_delivery_count || 0) }} more orders
                                    </span>
                                </div>
                            </div>

                            <!-- Warning for Busy Riders -->
                            <div v-else-if="rider.delivery_status === 'busy'" class="mt-3 p-2 bg-yellow-50 dark:bg-yellow-900/20 rounded-md">
                                <div class="flex items-center justify-between text-sm">
                                    <span class="text-yellow-700 dark:text-yellow-300">
                                        ⚠️ Currently busy
                                    </span>
                                    <span class="text-yellow-600 dark:text-yellow-400">
                                        {{ rider.current_delivery_count || 0 }} active deliveries
                                    </span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Footer -->
                <div class="bg-gray-50 dark:bg-gray-700 px-6 py-4">
                    <div class="flex justify-between items-center text-sm text-gray-600 dark:text-gray-400">
                        <span>{{ filteredRiders.length }} riders shown</span>
                        <span>{{ availableRidersCount }} available</span>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script setup>
import { ref, computed } from 'vue'

const props = defineProps({
    riders: Array,
})

const emit = defineEmits(['select-rider', 'close'])

// Reactive state
const statusFilter = ref('')
const sortBy = ref('rating')

// Computed
const filteredRiders = computed(() => {
    let filtered = props.riders

    // Filter by status
    if (statusFilter.value) {
        filtered = filtered.filter(rider => rider.delivery_status === statusFilter.value)
    }

    // Sort riders
    filtered = [...filtered].sort((a, b) => {
        switch (sortBy.value) {
            case 'rating':
                return (b.average_rating || 0) - (a.average_rating || 0)
            case 'deliveries':
                return (b.total_deliveries || 0) - (a.total_deliveries || 0)
            case 'current_load':
                return (a.current_delivery_count || 0) - (b.current_delivery_count || 0)
            default:
                return 0
        }
    })

    return filtered
})

const availableRidersCount = computed(() => {
    return props.riders.filter(rider => rider.delivery_status === 'available').length
})

// Methods
const selectRider = (rider) => {
    emit('select-rider', rider)
}

const getStatusColor = (status) => {
    const colors = {
        available: 'bg-green-500',
        busy: 'bg-yellow-500',
        offline: 'bg-gray-500',
        on_break: 'bg-blue-500',
    }
    return colors[status] || 'bg-gray-500'
}

const getStatusBadgeColor = (status) => {
    const colors = {
        available: 'bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400',
        busy: 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-400',
        offline: 'bg-gray-100 text-gray-800 dark:bg-gray-900/30 dark:text-gray-400',
        on_break: 'bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-400',
    }
    return colors[status] || colors.offline
}

const getStatusText = (status) => {
    const texts = {
        available: 'Available',
        busy: 'Busy',
        offline: 'Offline',
        on_break: 'On Break',
    }
    return texts[status] || 'Unknown'
}

const getVehicleIcon = (vehicleType) => {
    const icons = {
        bike: '🏍️',
        car: '🚗',
        scooter: '🛵',
        bicycle: '🚲',
    }
    return icons[vehicleType] || '🚗'
}

const formatTime = (timestamp) => {
    const now = new Date()
    const time = new Date(timestamp)
    const diffInMinutes = Math.floor((now - time) / (1000 * 60))

    if (diffInMinutes < 1) return 'Just now'
    if (diffInMinutes < 60) return `${diffInMinutes}m ago`
    if (diffInMinutes < 1440) return `${Math.floor(diffInMinutes / 60)}h ago`
    return time.toLocaleDateString()
}
</script>
