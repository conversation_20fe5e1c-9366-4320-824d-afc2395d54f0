<template>
    <DialogModal :show="show" @close="closeModal">
        <template #title>
            Add New Customer
        </template>

        <template #content>
            <form @submit.prevent="submit" class="space-y-4">
                <!-- Name -->
                <div>
                    <InputLabel for="customer_name" value="Name" />
                    <TextInput
                        id="customer_name"
                        ref="nameInput"
                        v-model="form.name"
                        type="text"
                        class="mt-1 block w-full"
                        :class="{ 'border-red-500': form.errors.name }"
                        autocomplete="name"
                        placeholder="Enter customer name"
                    />
                    <InputError :message="form.errors.name" class="mt-2" />
                </div>

                <!-- Phone -->
                <div>
                    <InputLabel for="customer_phone" value="Phone" />
                    <TextInput
                        id="customer_phone"
                        v-model="form.phone"
                        type="tel"
                        class="mt-1 block w-full"
                        :class="{ 'border-red-500': form.errors.phone }"
                        autocomplete="tel"
                        placeholder="Enter phone number"
                    />
                    <InputError :message="form.errors.phone" class="mt-2" />
                </div>

                <!-- Email -->
                <div>
                    <InputLabel for="customer_email" value="Email" />
                    <TextInput
                        id="customer_email"
                        v-model="form.email"
                        type="email"
                        class="mt-1 block w-full"
                        :class="{ 'border-red-500': form.errors.email }"
                        autocomplete="email"
                        placeholder="Enter email address"
                    />
                    <InputError :message="form.errors.email" class="mt-2" />
                </div>

                <!-- Address -->
                <div>
                    <InputLabel for="customer_address" value="Address" />
                    <textarea
                        id="customer_address"
                        v-model="form.address"
                        rows="2"
                        class="mt-1 block w-full border-gray-300 dark:border-gray-700 dark:bg-gray-900 dark:text-gray-300 focus:border-indigo-500 dark:focus:border-indigo-600 focus:ring-indigo-500 dark:focus:ring-indigo-600 rounded-md shadow-sm"
                        :class="{ 'border-red-500': form.errors.address }"
                        placeholder="Enter customer address (optional)"
                    ></textarea>
                    <InputError :message="form.errors.address" class="mt-2" />
                </div>

                <!-- Validation Error Message -->
                <div v-if="form.errors.general" class="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded">
                    {{ form.errors.general }}
                </div>

                <!-- Info Message -->
                <div class="bg-blue-50 border border-blue-200 text-blue-700 px-4 py-3 rounded text-sm">
                    <strong>Note:</strong> At least one of Name, Phone, or Email must be provided.
                </div>
            </form>
        </template>

        <template #footer>
            <SecondaryButton @click="closeModal" :disabled="form.processing">
                Cancel
            </SecondaryButton>

            <PrimaryButton
                class="ml-3"
                :class="{ 'opacity-25': form.processing }"
                :disabled="form.processing"
                @click="submit"
            >
                <svg v-if="form.processing" class="animate-spin -ml-1 mr-3 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                    <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                    <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
                {{ form.processing ? 'Creating...' : 'Create Customer' }}
            </PrimaryButton>
        </template>
    </DialogModal>
</template>

<script setup>
import { ref, nextTick } from 'vue';
import { useForm } from '@inertiajs/vue3';
import axios from 'axios';
import DialogModal from '@/Components/DialogModal.vue';
import InputLabel from '@/Components/InputLabel.vue';
import TextInput from '@/Components/TextInput.vue';
import InputError from '@/Components/InputError.vue';
import PrimaryButton from '@/Components/PrimaryButton.vue';
import SecondaryButton from '@/Components/SecondaryButton.vue';

const props = defineProps({
    show: {
        type: Boolean,
        default: false
    }
});

const emit = defineEmits(['close', 'customer-created']);

const nameInput = ref();

const form = useForm({
    name: '',
    phone: '',
    email: '',
    address: '',
    tier: 'regular'
});

const submit = () => {
    // Client-side validation
    if (!form.name && !form.phone && !form.email) {
        form.setError('general', 'At least one of Name, Phone, or Email must be provided.');
        return;
    }

    form.clearErrors();
    form.processing = true;

    // Use axios for API call instead of Inertia form
    axios.post(route('customers.api.store'), {
        name: form.name,
        phone: form.phone,
        email: form.email,
        address: form.address,
        tier: form.tier
    })
    .then(response => {
        if (response.data.success) {
            emit('customer-created', response.data.customer);
            closeModal();
        }
    })
    .catch(error => {
        if (error.response && error.response.data.errors) {
            // Handle validation errors
            Object.keys(error.response.data.errors).forEach(key => {
                form.setError(key, error.response.data.errors[key]);
            });
        } else {
            form.setError('general', 'An error occurred while creating the customer.');
        }

        if (nameInput.value) {
            nameInput.value.focus();
        }
    })
    .finally(() => {
        form.processing = false;
    });
};

const closeModal = () => {
    form.reset();
    form.clearErrors();
    emit('close');
};

// Focus on name input when modal opens
const focusNameInput = () => {
    nextTick(() => {
        if (nameInput.value) {
            nameInput.value.focus();
        }
    });
};

// Expose method to parent
defineExpose({
    focusNameInput
});
</script>
