<script setup>
import { ref, computed, watch } from 'vue';
import { router } from '@inertiajs/vue3';

const props = defineProps({
    branches: {
        type: Array,
        required: true
    },
    selectedBranchId: {
        type: Number,
        required: true
    },
    disabled: {
        type: Boolean,
        default: false
    },
    showLabel: {
        type: Boolean,
        default: true
    },
    size: {
        type: String,
        default: 'md', // sm, md, lg
        validator: (value) => ['sm', 'md', 'lg'].includes(value)
    }
});

const emit = defineEmits(['branch-changed']);

const isLoading = ref(false);
const selectedBranch = ref(props.selectedBranchId);

// Computed properties
const currentBranch = computed(() => {
    return props.branches.find(branch => branch.id === selectedBranch.value);
});

const sizeClasses = computed(() => {
    const sizes = {
        sm: 'px-2 py-1 text-sm',
        md: 'px-3 py-2 text-base',
        lg: 'px-4 py-3 text-lg'
    };
    return sizes[props.size];
});

// Watch for prop changes
watch(() => props.selectedBranchId, (newValue) => {
    selectedBranch.value = newValue;
});

// Methods
const handleBranchChange = async (event) => {
    const newBranchId = parseInt(event.target.value);
    
    if (newBranchId === selectedBranch.value) return;

    isLoading.value = true;
    
    try {
        const response = await axios.post(route('pos.switch-branch'), {
            branch_id: newBranchId
        });

        if (response.data.success) {
            selectedBranch.value = newBranchId;
            
            // Emit event for parent component
            emit('branch-changed', {
                branchId: newBranchId,
                branch: response.data.branch
            });

            // Show success feedback
            showSuccessMessage(response.data.message);

            // Reload the page to get updated data for the new branch
            setTimeout(() => {
                router.reload();
            }, 500);
        }
    } catch (error) {
        console.error('Failed to switch branch:', error);
        
        // Reset selection on error
        selectedBranch.value = props.selectedBranchId;
        
        // Show error message
        showErrorMessage('Failed to switch branch. Please try again.');
    } finally {
        isLoading.value = false;
    }
};

const showSuccessMessage = (message) => {
    // Create a temporary success notification
    const notification = document.createElement('div');
    notification.className = 'fixed top-4 right-4 bg-green-500 text-white px-4 py-2 rounded-lg shadow-lg z-50 transition-all duration-300';
    notification.textContent = message;
    document.body.appendChild(notification);
    
    setTimeout(() => {
        notification.style.opacity = '0';
        setTimeout(() => {
            document.body.removeChild(notification);
        }, 300);
    }, 2000);
};

const showErrorMessage = (message) => {
    // Create a temporary error notification
    const notification = document.createElement('div');
    notification.className = 'fixed top-4 right-4 bg-red-500 text-white px-4 py-2 rounded-lg shadow-lg z-50 transition-all duration-300';
    notification.textContent = message;
    document.body.appendChild(notification);
    
    setTimeout(() => {
        notification.style.opacity = '0';
        setTimeout(() => {
            document.body.removeChild(notification);
        }, 300);
    }, 3000);
};
</script>

<template>
    <div class="branch-selector">
        <!-- Label -->
        <label v-if="showLabel" for="branch-select" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
            Current Branch
        </label>
        
        <!-- Branch Selector -->
        <div class="relative">
            <select
                id="branch-select"
                v-model="selectedBranch"
                @change="handleBranchChange"
                :disabled="disabled || isLoading || branches.length <= 1"
                :class="[
                    'block w-full rounded-md border-gray-300 dark:border-gray-600 shadow-sm',
                    'focus:border-indigo-500 focus:ring-indigo-500 dark:bg-gray-700 dark:text-white',
                    'disabled:opacity-50 disabled:cursor-not-allowed',
                    sizeClasses,
                    isLoading ? 'cursor-wait' : 'cursor-pointer'
                ]"
            >
                <option v-for="branch in branches" :key="branch.id" :value="branch.id">
                    {{ branch.name }}
                    <span v-if="branch.is_main_branch" class="text-xs text-gray-500">(Main)</span>
                </option>
            </select>
            
            <!-- Loading indicator -->
            <div v-if="isLoading" class="absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none">
                <svg class="animate-spin h-4 w-4 text-gray-400" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                    <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                    <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
            </div>
        </div>
        
        <!-- Current branch info -->
        <div v-if="currentBranch && showLabel" class="mt-1 text-xs text-gray-500 dark:text-gray-400">
            <span class="inline-flex items-center">
                <span class="w-2 h-2 bg-green-400 rounded-full mr-1"></span>
                Active: {{ currentBranch.name }}
                <span v-if="currentBranch.is_main_branch" class="ml-1 text-blue-500">(Main Branch)</span>
            </span>
        </div>
        
        <!-- No branches message -->
        <div v-if="branches.length === 0" class="text-sm text-red-600 dark:text-red-400 mt-1">
            No branches available
        </div>
        
        <!-- Single branch message -->
        <div v-else-if="branches.length === 1" class="text-xs text-gray-500 dark:text-gray-400 mt-1">
            Only one branch available
        </div>
    </div>
</template>

<style scoped>
.branch-selector select {
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
    background-position: right 0.5rem center;
    background-repeat: no-repeat;
    background-size: 1.5em 1.5em;
    padding-right: 2.5rem;
}

.dark .branch-selector select {
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%9ca3af' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
}
</style>
