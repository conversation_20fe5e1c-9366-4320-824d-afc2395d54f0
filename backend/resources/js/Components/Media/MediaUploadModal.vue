<template>
    <Modal :show="show" @close="$emit('close')" max-width="2xl">
        <div class="p-6">
            <div class="flex items-center justify-between mb-4">
                <h3 class="text-lg font-medium text-gray-900">Upload Media Files</h3>
                <button @click="$emit('close')" class="text-gray-400 hover:text-gray-600">
                    <svg class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                    </svg>
                </button>
            </div>

            <!-- Upload Options -->
            <div class="grid grid-cols-2 gap-4 mb-6">
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">Folder</label>
                    <select v-model="uploadData.folder" class="w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500">
                        <option value="">No Folder</option>
                        <option v-for="folder in folders" :key="folder" :value="folder">{{ folder }}</option>
                    </select>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">Collection</label>
                    <input 
                        v-model="uploadData.collection" 
                        type="text" 
                        placeholder="Optional collection name"
                        class="w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500"
                    >
                </div>
            </div>

            <!-- Dropzone -->
            <div 
                ref="dropzone"
                @drop="handleDrop"
                @dragover="handleDragOver"
                @dragenter="handleDragEnter"
                @dragleave="handleDragLeave"
                @click="openFileDialog"
                :class="[
                    'border-2 border-dashed rounded-lg p-8 text-center cursor-pointer transition-colors',
                    isDragging ? 'border-blue-500 bg-blue-50' : 'border-gray-300 hover:border-gray-400'
                ]"
            >
                <input 
                    ref="fileInput"
                    type="file"
                    multiple
                    accept="image/*,video/*,.pdf,.doc,.docx"
                    @change="handleFileSelect"
                    class="hidden"
                >
                
                <div v-if="!uploading">
                    <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12" />
                    </svg>
                    <p class="mt-2 text-sm text-gray-600">
                        <span class="font-medium text-blue-600">Click to upload</span> or drag and drop
                    </p>
                    <p class="text-xs text-gray-500">PNG, JPG, GIF, SVG, MP4, PDF up to 10MB</p>
                </div>

                <div v-else class="space-y-4">
                    <div class="animate-spin mx-auto h-8 w-8 border-4 border-blue-500 border-t-transparent rounded-full"></div>
                    <p class="text-sm text-gray-600">Uploading {{ selectedFiles.length }} file(s)...</p>
                    <div class="w-full bg-gray-200 rounded-full h-2">
                        <div 
                            class="bg-blue-600 h-2 rounded-full transition-all duration-300"
                            :style="{ width: uploadProgress + '%' }"
                        ></div>
                    </div>
                </div>
            </div>

            <!-- Selected Files Preview -->
            <div v-if="selectedFiles.length > 0 && !uploading" class="mt-6">
                <h4 class="text-sm font-medium text-gray-900 mb-3">Selected Files ({{ selectedFiles.length }})</h4>
                <div class="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 gap-3 max-h-60 overflow-y-auto">
                    <div 
                        v-for="(file, index) in selectedFiles" 
                        :key="index"
                        class="relative group border rounded-lg overflow-hidden"
                    >
                        <div class="aspect-square bg-gray-100 flex items-center justify-center">
                            <img 
                                v-if="file.type.startsWith('image/')" 
                                :src="getFilePreview(file)" 
                                :alt="file.name"
                                class="w-full h-full object-cover"
                            >
                            <div v-else class="text-center p-2">
                                <svg class="mx-auto h-6 w-6 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                                </svg>
                                <p class="text-xs text-gray-500 mt-1">{{ getFileExtension(file.name) }}</p>
                            </div>
                        </div>
                        <div class="p-2">
                            <p class="text-xs font-medium text-gray-900 truncate" :title="file.name">
                                {{ file.name }}
                            </p>
                            <p class="text-xs text-gray-500">
                                {{ formatFileSize(file.size) }}
                            </p>
                        </div>
                        <button 
                            @click="removeFile(index)"
                            class="absolute top-1 right-1 bg-red-500 text-white rounded-full p-1 opacity-0 group-hover:opacity-100 transition-opacity"
                        >
                            <svg class="h-3 w-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                            </svg>
                        </button>
                    </div>
                </div>
            </div>

            <!-- Upload Results -->
            <div v-if="uploadResults.length > 0" class="mt-6">
                <h4 class="text-sm font-medium text-gray-900 mb-3">Upload Results</h4>
                <div class="space-y-2 max-h-40 overflow-y-auto">
                    <div 
                        v-for="result in uploadResults" 
                        :key="result.name"
                        class="flex items-center justify-between p-2 bg-gray-50 rounded"
                    >
                        <span class="text-sm text-gray-900">{{ result.name }}</span>
                        <span v-if="result.success" class="text-green-600 text-sm">✓ Uploaded</span>
                        <span v-else class="text-red-600 text-sm">✗ Failed</span>
                    </div>
                </div>
            </div>

            <!-- Actions -->
            <div class="flex justify-end space-x-3 mt-6">
                <button 
                    @click="$emit('close')" 
                    class="px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 rounded-md hover:bg-gray-200"
                    :disabled="uploading"
                >
                    Cancel
                </button>
                <button 
                    @click="uploadFiles" 
                    :disabled="selectedFiles.length === 0 || uploading"
                    class="px-4 py-2 text-sm font-medium text-white bg-blue-600 rounded-md hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                    {{ uploading ? 'Uploading...' : `Upload ${selectedFiles.length} File(s)` }}
                </button>
            </div>
        </div>
    </Modal>
</template>

<script setup>
import { ref, reactive } from 'vue'
import { router } from '@inertiajs/vue3'
import Modal from '@/Components/Modal.vue'

const props = defineProps({
    show: Boolean,
    folders: {
        type: Array,
        default: () => []
    }
})

const emit = defineEmits(['close', 'uploaded'])

const dropzone = ref(null)
const fileInput = ref(null)
const isDragging = ref(false)
const uploading = ref(false)
const uploadProgress = ref(0)
const selectedFiles = ref([])
const uploadResults = ref([])

const uploadData = reactive({
    folder: '',
    collection: ''
})

const handleDragEnter = (e) => {
    e.preventDefault()
    isDragging.value = true
}

const handleDragOver = (e) => {
    e.preventDefault()
}

const handleDragLeave = (e) => {
    e.preventDefault()
    if (!dropzone.value.contains(e.relatedTarget)) {
        isDragging.value = false
    }
}

const handleDrop = (e) => {
    e.preventDefault()
    isDragging.value = false
    
    const files = Array.from(e.dataTransfer.files)
    addFiles(files)
}

const openFileDialog = () => {
    if (!uploading.value) {
        fileInput.value.click()
    }
}

const handleFileSelect = (e) => {
    const files = Array.from(e.target.files)
    addFiles(files)
}

const addFiles = (files) => {
    const validFiles = files.filter(file => {
        // Check file size (10MB limit)
        if (file.size > 10 * 1024 * 1024) {
            alert(`File ${file.name} is too large. Maximum size is 10MB.`)
            return false
        }
        
        // Check file type
        const allowedTypes = [
            'image/jpeg', 'image/png', 'image/gif', 'image/svg+xml', 'image/webp',
            'video/mp4', 'video/mpeg', 'video/quicktime',
            'application/pdf', 'application/msword', 
            'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
        ]
        
        if (!allowedTypes.includes(file.type)) {
            alert(`File ${file.name} has an unsupported format.`)
            return false
        }
        
        return true
    })
    
    selectedFiles.value.push(...validFiles)
}

const removeFile = (index) => {
    selectedFiles.value.splice(index, 1)
}

const getFilePreview = (file) => {
    return URL.createObjectURL(file)
}

const getFileExtension = (filename) => {
    return filename.split('.').pop().toUpperCase()
}

const formatFileSize = (bytes) => {
    const units = ['B', 'KB', 'MB', 'GB']
    let size = bytes
    let unitIndex = 0
    
    while (size >= 1024 && unitIndex < units.length - 1) {
        size /= 1024
        unitIndex++
    }
    
    return `${size.toFixed(1)} ${units[unitIndex]}`
}

const uploadFiles = async () => {
    if (selectedFiles.value.length === 0) return
    
    uploading.value = true
    uploadProgress.value = 0
    uploadResults.value = []
    
    const formData = new FormData()
    
    selectedFiles.value.forEach(file => {
        formData.append('files[]', file)
    })
    
    if (uploadData.folder) {
        formData.append('folder', uploadData.folder)
    }
    
    if (uploadData.collection) {
        formData.append('collection', uploadData.collection)
    }
    
    try {
        const response = await fetch(route('media.upload'), {
            method: 'POST',
            body: formData,
            headers: {
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
                'X-Requested-With': 'XMLHttpRequest'
            }
        })
        
        const result = await response.json()
        
        if (result.success) {
            uploadResults.value = selectedFiles.value.map(file => ({
                name: file.name,
                success: true
            }))
            
            setTimeout(() => {
                emit('uploaded')
                resetForm()
            }, 1000)
        } else {
            throw new Error(result.message || 'Upload failed')
        }
        
    } catch (error) {
        uploadResults.value = selectedFiles.value.map(file => ({
            name: file.name,
            success: false
        }))
        
        alert('Upload failed: ' + error.message)
    } finally {
        uploading.value = false
        uploadProgress.value = 100
    }
}

const resetForm = () => {
    selectedFiles.value = []
    uploadResults.value = []
    uploadProgress.value = 0
    uploadData.folder = ''
    uploadData.collection = ''
    
    if (fileInput.value) {
        fileInput.value.value = ''
    }
}
</script>
