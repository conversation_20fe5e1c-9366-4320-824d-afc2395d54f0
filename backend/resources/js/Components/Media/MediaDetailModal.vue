<template>
    <Modal :show="show" @close="$emit('close')" max-width="4xl">
        <div class="p-6" v-if="media">
            <div class="flex justify-between items-start mb-6">
                <h2 class="text-lg font-medium text-gray-900 dark:text-gray-100">
                    Media Details
                </h2>
                <button @click="$emit('close')" class="text-gray-400 hover:text-gray-600">
                    <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                    </svg>
                </button>
            </div>

            <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                <!-- Media Preview -->
                <div class="space-y-4">
                    <div class="bg-gray-100 dark:bg-gray-800 rounded-lg p-4 flex items-center justify-center min-h-64">
                        <img 
                            v-if="isImage(media)"
                            :src="media.url" 
                            :alt="media.name"
                            class="max-w-full max-h-64 object-contain rounded"
                        />
                        <div v-else class="text-center">
                            <svg class="w-16 h-16 mx-auto text-gray-400 mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                            </svg>
                            <p class="text-sm text-gray-500">{{ media.mime_type }}</p>
                        </div>
                    </div>

                    <!-- Actions -->
                    <div class="flex space-x-2">
                        <a 
                            :href="media.url" 
                            target="_blank"
                            class="flex-1 bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md text-center text-sm"
                        >
                            View Full Size
                        </a>
                        <a 
                            :href="media.download_url || media.url" 
                            download
                            class="flex-1 bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded-md text-center text-sm"
                        >
                            Download
                        </a>
                    </div>
                </div>

                <!-- Media Information -->
                <div class="space-y-4">
                    <form @submit.prevent="updateMedia">
                        <div class="space-y-4">
                            <div>
                                <InputLabel for="media_name" value="Name" />
                                <TextInput
                                    id="media_name"
                                    v-model="form.name"
                                    type="text"
                                    class="mt-1 block w-full"
                                    :class="{ 'border-red-500': form.errors.name }"
                                />
                                <InputError :message="form.errors.name" class="mt-2" />
                            </div>

                            <div>
                                <InputLabel for="media_alt_text" value="Alt Text" />
                                <TextInput
                                    id="media_alt_text"
                                    v-model="form.alt_text"
                                    type="text"
                                    class="mt-1 block w-full"
                                    :class="{ 'border-red-500': form.errors.alt_text }"
                                />
                                <InputError :message="form.errors.alt_text" class="mt-2" />
                            </div>

                            <div>
                                <InputLabel for="media_description" value="Description" />
                                <textarea
                                    id="media_description"
                                    v-model="form.description"
                                    class="mt-1 block w-full border-gray-300 dark:border-gray-700 dark:bg-gray-900 dark:text-gray-300 focus:border-indigo-500 dark:focus:border-indigo-600 focus:ring-indigo-500 dark:focus:ring-indigo-600 rounded-md shadow-sm"
                                    rows="3"
                                ></textarea>
                                <InputError :message="form.errors.description" class="mt-2" />
                            </div>
                        </div>

                        <div class="flex items-center justify-between mt-6">
                            <button
                                type="button"
                                @click="deleteMedia"
                                class="bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-md text-sm"
                                :disabled="form.processing"
                            >
                                Delete
                            </button>
                            
                            <div class="flex space-x-3">
                                <SecondaryButton @click="$emit('close')">
                                    Cancel
                                </SecondaryButton>
                                <PrimaryButton 
                                    :class="{ 'opacity-25': form.processing }" 
                                    :disabled="form.processing"
                                >
                                    <span v-if="form.processing">Updating...</span>
                                    <span v-else>Update</span>
                                </PrimaryButton>
                            </div>
                        </div>
                    </form>

                    <!-- File Information -->
                    <div class="border-t pt-4 mt-6">
                        <h3 class="text-sm font-medium text-gray-900 dark:text-gray-100 mb-3">File Information</h3>
                        <dl class="space-y-2 text-sm">
                            <div class="flex justify-between">
                                <dt class="text-gray-500">File Size:</dt>
                                <dd class="text-gray-900 dark:text-gray-100">{{ formatFileSize(media.size) }}</dd>
                            </div>
                            <div class="flex justify-between">
                                <dt class="text-gray-500">Type:</dt>
                                <dd class="text-gray-900 dark:text-gray-100">{{ media.mime_type }}</dd>
                            </div>
                            <div class="flex justify-between" v-if="media.dimensions">
                                <dt class="text-gray-500">Dimensions:</dt>
                                <dd class="text-gray-900 dark:text-gray-100">{{ media.dimensions }}</dd>
                            </div>
                            <div class="flex justify-between">
                                <dt class="text-gray-500">Uploaded:</dt>
                                <dd class="text-gray-900 dark:text-gray-100">{{ formatDate(media.created_at) }}</dd>
                            </div>
                        </dl>
                    </div>
                </div>
            </div>
        </div>
    </Modal>
</template>

<script setup>
import { useForm } from '@inertiajs/vue3'
import { watch } from 'vue'
import Modal from '@/Components/Modal.vue'
import InputLabel from '@/Components/InputLabel.vue'
import TextInput from '@/Components/TextInput.vue'
import InputError from '@/Components/InputError.vue'
import PrimaryButton from '@/Components/PrimaryButton.vue'
import SecondaryButton from '@/Components/SecondaryButton.vue'

const props = defineProps({
    show: {
        type: Boolean,
        default: false,
    },
    media: {
        type: Object,
        default: null,
    },
})

const emit = defineEmits(['close', 'updated', 'deleted'])

const form = useForm({
    name: '',
    alt_text: '',
    description: '',
})

// Watch for media changes and update form
watch(() => props.media, (newMedia) => {
    if (newMedia) {
        form.name = newMedia.name || ''
        form.alt_text = newMedia.alt_text || ''
        form.description = newMedia.description || ''
    }
}, { immediate: true })

const isImage = (media) => {
    return media && media.mime_type && media.mime_type.startsWith('image/')
}

const formatFileSize = (bytes) => {
    if (!bytes) return '0 Bytes'
    const k = 1024
    const sizes = ['Bytes', 'KB', 'MB', 'GB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString()
}

const updateMedia = () => {
    if (!props.media) return
    
    form.put(route('media.update', props.media.id), {
        preserveScroll: true,
        onSuccess: () => {
            emit('updated')
        },
    })
}

const deleteMedia = () => {
    if (!props.media) return
    
    if (confirm('Are you sure you want to delete this media file? This action cannot be undone.')) {
        form.delete(route('media.destroy', props.media.id), {
            preserveScroll: true,
            onSuccess: () => {
                emit('deleted')
            },
        })
    }
}
</script>
