<template>
    <Modal :show="show" @close="$emit('close')" max-width="md">
        <div class="p-6">
            <h2 class="text-lg font-medium text-gray-900 dark:text-gray-100 mb-4">
                Create New Folder
            </h2>

            <form @submit.prevent="createFolder">
                <div class="mb-4">
                    <InputLabel for="folder_name" value="Folder Name" />
                    <TextInput
                        id="folder_name"
                        v-model="form.name"
                        type="text"
                        class="mt-1 block w-full"
                        :class="{ 'border-red-500': form.errors.name }"
                        placeholder="Enter folder name"
                        required
                        autofocus
                    />
                    <InputError :message="form.errors.name" class="mt-2" />
                </div>

                <div class="mb-4">
                    <InputLabel for="folder_description" value="Description (Optional)" />
                    <textarea
                        id="folder_description"
                        v-model="form.description"
                        class="mt-1 block w-full border-gray-300 dark:border-gray-700 dark:bg-gray-900 dark:text-gray-300 focus:border-indigo-500 dark:focus:border-indigo-600 focus:ring-indigo-500 dark:focus:ring-indigo-600 rounded-md shadow-sm"
                        rows="3"
                        placeholder="Enter folder description"
                    ></textarea>
                    <InputError :message="form.errors.description" class="mt-2" />
                </div>

                <div class="flex items-center justify-end space-x-3">
                    <SecondaryButton @click="$emit('close')">
                        Cancel
                    </SecondaryButton>
                    <PrimaryButton 
                        :class="{ 'opacity-25': form.processing }" 
                        :disabled="form.processing"
                    >
                        <span v-if="form.processing">Creating...</span>
                        <span v-else>Create Folder</span>
                    </PrimaryButton>
                </div>
            </form>
        </div>
    </Modal>
</template>

<script setup>
import { useForm } from '@inertiajs/vue3'
import Modal from '@/Components/Modal.vue'
import InputLabel from '@/Components/InputLabel.vue'
import TextInput from '@/Components/TextInput.vue'
import InputError from '@/Components/InputError.vue'
import PrimaryButton from '@/Components/PrimaryButton.vue'
import SecondaryButton from '@/Components/SecondaryButton.vue'

defineProps({
    show: {
        type: Boolean,
        default: false,
    },
})

const emit = defineEmits(['close', 'created'])

const form = useForm({
    name: '',
    description: '',
})

const createFolder = () => {
    form.post(route('media.folders.store'), {
        preserveScroll: true,
        onSuccess: () => {
            form.reset()
            emit('created')
        },
        onError: () => {
            // Form errors will be handled automatically by Inertia
        },
    })
}
</script>
