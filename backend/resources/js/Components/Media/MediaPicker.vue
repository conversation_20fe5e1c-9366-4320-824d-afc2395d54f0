<template>
    <div>
        <!-- Trigger <PERSON> -->
        <button 
            @click="showPicker = true"
            type="button"
            class="relative block w-full border-2 border-gray-300 border-dashed rounded-lg p-12 text-center hover:border-gray-400 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
        >
            <div v-if="!selectedMedia">
                <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
                </svg>
                <span class="mt-2 block text-sm font-medium text-gray-900">
                    {{ placeholder || 'Select media' }}
                </span>
            </div>
            <div v-else class="relative">
                <img 
                    v-if="selectedMedia.mime_type.startsWith('image/')"
                    :src="selectedMedia.thumbnail || selectedMedia.url" 
                    :alt="selectedMedia.alt_text || selectedMedia.name"
                    class="mx-auto max-h-32 rounded"
                >
                <div v-else class="text-center">
                    <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                    </svg>
                    <p class="text-sm text-gray-600 mt-2">{{ selectedMedia.name }}</p>
                </div>
                <button 
                    @click.stop="removeMedia"
                    class="absolute top-2 right-2 bg-red-500 text-white rounded-full p-1 hover:bg-red-600"
                >
                    <svg class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                    </svg>
                </button>
            </div>
        </button>

        <!-- Media Picker Modal -->
        <Modal :show="showPicker" @close="showPicker = false" max-width="4xl">
            <div class="p-6">
                <div class="flex items-center justify-between mb-4">
                    <h3 class="text-lg font-medium text-gray-900">Select Media</h3>
                    <button @click="showPicker = false" class="text-gray-400 hover:text-gray-600">
                        <svg class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                        </svg>
                    </button>
                </div>

                <!-- Search and Filters -->
                <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
                    <div>
                        <input 
                            v-model="searchQuery" 
                            @input="searchMedia"
                            type="text" 
                            placeholder="Search media..."
                            class="w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500"
                        >
                    </div>
                    <div>
                        <select 
                            v-model="selectedFolder" 
                            @change="filterMedia"
                            class="w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500"
                        >
                            <option value="">All Folders</option>
                            <option v-for="folder in folders" :key="folder" :value="folder">
                                {{ folder }}
                            </option>
                        </select>
                    </div>
                    <div>
                        <select 
                            v-model="selectedType" 
                            @change="filterMedia"
                            class="w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500"
                        >
                            <option value="">All Types</option>
                            <option value="images">Images</option>
                            <option value="videos">Videos</option>
                            <option value="documents">Documents</option>
                        </select>
                    </div>
                </div>

                <!-- Upload Button -->
                <div class="mb-6">
                    <button 
                        @click="showUploadModal = true"
                        class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md text-sm"
                    >
                        Upload New Media
                    </button>
                </div>

                <!-- Media Grid -->
                <div v-if="loading" class="text-center py-8">
                    <div class="animate-spin mx-auto h-8 w-8 border-4 border-blue-500 border-t-transparent rounded-full"></div>
                    <p class="mt-2 text-sm text-gray-600">Loading media...</p>
                </div>

                <div v-else-if="mediaItems.length === 0" class="text-center py-8">
                    <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
                    </svg>
                    <h3 class="mt-2 text-sm font-medium text-gray-900">No media found</h3>
                    <p class="mt-1 text-sm text-gray-500">Upload some files to get started.</p>
                </div>

                <div v-else class="grid grid-cols-3 sm:grid-cols-4 md:grid-cols-6 gap-4 max-h-96 overflow-y-auto">
                    <div 
                        v-for="item in mediaItems" 
                        :key="item.id"
                        @click="selectMediaItem(item)"
                        :class="[
                            'relative cursor-pointer border-2 rounded-lg overflow-hidden transition-colors',
                            tempSelectedMedia?.id === item.id ? 'border-blue-500 bg-blue-50' : 'border-gray-200 hover:border-gray-300'
                        ]"
                    >
                        <div class="aspect-square bg-gray-100 flex items-center justify-center">
                            <img 
                                v-if="item.mime_type.startsWith('image/')" 
                                :src="item.thumbnail || item.url" 
                                :alt="item.alt_text || item.name"
                                class="w-full h-full object-cover"
                            >
                            <div v-else class="text-center p-2">
                                <svg class="mx-auto h-6 w-6 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                                </svg>
                                <p class="text-xs text-gray-500 mt-1">{{ getFileExtension(item.file_name) }}</p>
                            </div>
                        </div>
                        <div class="p-2">
                            <p class="text-xs font-medium text-gray-900 truncate" :title="item.name">
                                {{ item.name }}
                            </p>
                        </div>
                        
                        <!-- Selection Indicator -->
                        <div v-if="tempSelectedMedia?.id === item.id" class="absolute top-2 right-2">
                            <div class="bg-blue-500 text-white rounded-full p-1">
                                <svg class="h-3 w-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
                                </svg>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Pagination -->
                <div v-if="hasMorePages" class="mt-4 text-center">
                    <button 
                        @click="loadMore"
                        :disabled="loadingMore"
                        class="bg-gray-500 hover:bg-gray-600 text-white px-4 py-2 rounded-md text-sm disabled:opacity-50"
                    >
                        {{ loadingMore ? 'Loading...' : 'Load More' }}
                    </button>
                </div>

                <!-- Actions -->
                <div class="flex justify-end space-x-3 mt-6">
                    <button 
                        @click="showPicker = false" 
                        class="px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 rounded-md hover:bg-gray-200"
                    >
                        Cancel
                    </button>
                    <button 
                        @click="confirmSelection" 
                        :disabled="!tempSelectedMedia"
                        class="px-4 py-2 text-sm font-medium text-white bg-blue-600 rounded-md hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed"
                    >
                        Select Media
                    </button>
                </div>
            </div>
        </Modal>

        <!-- Upload Modal -->
        <MediaUploadModal 
            :show="showUploadModal" 
            @close="showUploadModal = false"
            @uploaded="handleUploaded"
        />
    </div>
</template>

<script setup>
import { ref, watch, onMounted } from 'vue'
import Modal from '@/Components/Modal.vue'
import MediaUploadModal from '@/Components/Media/MediaUploadModal.vue'

const props = defineProps({
    modelValue: [Object, Number],
    placeholder: String,
    accept: {
        type: String,
        default: 'images' // images, videos, documents, all
    }
})

const emit = defineEmits(['update:modelValue'])

const showPicker = ref(false)
const showUploadModal = ref(false)
const loading = ref(false)
const loadingMore = ref(false)
const mediaItems = ref([])
const folders = ref([])
const selectedMedia = ref(null)
const tempSelectedMedia = ref(null)
const searchQuery = ref('')
const selectedFolder = ref('')
const selectedType = ref('')
const currentPage = ref(1)
const hasMorePages = ref(false)

// Load initial media when picker opens
watch(showPicker, (show) => {
    if (show) {
        loadMedia()
    }
})

// Load media from props
watch(() => props.modelValue, (value) => {
    if (value && typeof value === 'object') {
        selectedMedia.value = value
    } else if (value && typeof value === 'number') {
        // Load media by ID
        loadMediaById(value)
    } else {
        selectedMedia.value = null
    }
}, { immediate: true })

const loadMedia = async (page = 1) => {
    loading.value = page === 1
    loadingMore.value = page > 1
    
    try {
        const params = new URLSearchParams({
            page: page.toString(),
            search: searchQuery.value,
            folder: selectedFolder.value,
            type: selectedType.value || props.accept
        })
        
        const response = await fetch(`${route('media.picker')}?${params}`)
        const data = await response.json()
        
        if (page === 1) {
            mediaItems.value = data.data
        } else {
            mediaItems.value.push(...data.data)
        }
        
        currentPage.value = data.current_page
        hasMorePages.value = data.current_page < data.last_page
        
    } catch (error) {
        console.error('Failed to load media:', error)
    } finally {
        loading.value = false
        loadingMore.value = false
    }
}

const loadMediaById = async (id) => {
    try {
        const response = await fetch(route('media.show', id))
        const data = await response.json()
        selectedMedia.value = data
    } catch (error) {
        console.error('Failed to load media by ID:', error)
    }
}

const searchMedia = () => {
    currentPage.value = 1
    loadMedia()
}

const filterMedia = () => {
    currentPage.value = 1
    loadMedia()
}

const loadMore = () => {
    loadMedia(currentPage.value + 1)
}

const selectMediaItem = (item) => {
    tempSelectedMedia.value = item
}

const confirmSelection = () => {
    selectedMedia.value = tempSelectedMedia.value
    emit('update:modelValue', tempSelectedMedia.value)
    showPicker.value = false
    tempSelectedMedia.value = null
}

const removeMedia = () => {
    selectedMedia.value = null
    emit('update:modelValue', null)
}

const getFileExtension = (filename) => {
    return filename.split('.').pop().toUpperCase()
}

const handleUploaded = () => {
    showUploadModal.value = false
    loadMedia() // Refresh media list
}

onMounted(() => {
    // Load folders for filter
    fetch(route('media.index'))
        .then(response => response.json())
        .then(data => {
            if (data.folders) {
                folders.value = data.folders
            }
        })
        .catch(error => console.error('Failed to load folders:', error))
})
</script>
