<template>
    <!-- Toast Container -->
    <div class="fixed top-4 right-4 left-4 sm:left-auto z-[9999] space-y-2 max-w-sm sm:max-w-md mx-auto sm:mx-0">
        <TransitionGroup
            name="toast"
            tag="div"
            class="space-y-2"
        >
            <div
                v-for="toast in toasts"
                :key="toast.id"
                :class="[
                    'max-w-sm w-full bg-white shadow-lg rounded-lg pointer-events-auto ring-1 ring-black ring-opacity-5 overflow-hidden',
                    'transform transition-all duration-300 ease-in-out'
                ]"
            >
                <div class="p-4">
                    <div class="flex items-start">
                        <!-- Icon -->
                        <div class="flex-shrink-0">
                            <div
                                :class="[
                                    'w-6 h-6 rounded-full flex items-center justify-center',
                                    getToastStyles(toast.type).iconBg
                                ]"
                            >
                                <i
                                    :class="[
                                        'text-sm',
                                        getToastStyles(toast.type).icon,
                                        getToastStyles(toast.type).iconColor
                                    ]"
                                ></i>
                            </div>
                        </div>
                        
                        <!-- Content -->
                        <div class="ml-3 w-0 flex-1">
                            <p
                                :class="[
                                    'text-sm font-medium',
                                    getToastStyles(toast.type).textColor
                                ]"
                            >
                                {{ toast.message }}
                            </p>
                        </div>
                        
                        <!-- Close Button -->
                        <div class="ml-4 flex-shrink-0 flex">
                            <button
                                @click="removeToast(toast.id)"
                                class="bg-white rounded-md inline-flex text-gray-400 hover:text-gray-500 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-orange-500"
                            >
                                <span class="sr-only">Close</span>
                                <i class="fas fa-times text-sm"></i>
                            </button>
                        </div>
                    </div>
                </div>
                
                <!-- Progress Bar -->
                <div
                    v-if="toast.duration > 0"
                    :class="[
                        'h-1 transition-all ease-linear',
                        getToastStyles(toast.type).progressBg
                    ]"
                    :style="{
                        animationDuration: `${toast.duration}ms`,
                        animationName: 'toast-progress'
                    }"
                ></div>
            </div>
        </TransitionGroup>
    </div>
</template>

<script setup>
import { useToast } from '@/Composables/useToast'

// Toast functionality
const { toasts, removeToast } = useToast()

// Get toast styling based on type
const getToastStyles = (type) => {
    const styles = {
        success: {
            iconBg: 'bg-green-100',
            icon: 'fas fa-check',
            iconColor: 'text-green-600',
            textColor: 'text-gray-900',
            progressBg: 'bg-green-500'
        },
        error: {
            iconBg: 'bg-red-100',
            icon: 'fas fa-times',
            iconColor: 'text-red-600',
            textColor: 'text-gray-900',
            progressBg: 'bg-red-500'
        },
        warning: {
            iconBg: 'bg-yellow-100',
            icon: 'fas fa-exclamation-triangle',
            iconColor: 'text-yellow-600',
            textColor: 'text-gray-900',
            progressBg: 'bg-yellow-500'
        },
        info: {
            iconBg: 'bg-blue-100',
            icon: 'fas fa-info-circle',
            iconColor: 'text-blue-600',
            textColor: 'text-gray-900',
            progressBg: 'bg-blue-500'
        }
    }
    
    return styles[type] || styles.info
}
</script>

<style scoped>
/* Toast animations */
.toast-enter-active,
.toast-leave-active {
    transition: all 0.3s ease;
}

.toast-enter-from {
    opacity: 0;
    transform: translateX(100%);
}

.toast-leave-to {
    opacity: 0;
    transform: translateX(100%);
}

/* Progress bar animation */
@keyframes toast-progress {
    from {
        width: 100%;
    }
    to {
        width: 0%;
    }
}

.h-1 {
    animation-name: toast-progress;
    animation-timing-function: linear;
    animation-fill-mode: forwards;
}
</style>
