<template>
    <div class="fixed inset-0 z-50 overflow-y-auto" aria-labelledby="modal-title" role="dialog" aria-modal="true">
        <div class="flex items-end justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
            <div class="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" aria-hidden="true" @click="$emit('close')"></div>

            <!-- This element is to trick the browser into centering the modal contents. -->
            <span class="hidden sm:inline-block sm:align-middle sm:h-screen" aria-hidden="true">&#8203;</span>

            <div class="relative inline-block align-bottom bg-white dark:bg-gray-800 rounded-lg px-4 pt-5 pb-4 text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full sm:p-6">
                <div>
                    <div class="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-blue-100 dark:bg-blue-900">
                        <ClipboardDocumentCheckIcon class="h-6 w-6 text-blue-600 dark:text-blue-400" aria-hidden="true" />
                    </div>
                    <div class="mt-3 text-center sm:mt-5">
                        <h3 class="text-lg leading-6 font-medium text-gray-900 dark:text-gray-100" id="modal-title">
                            Update Order Status
                        </h3>
                        <div class="mt-2">
                            <p class="text-sm text-gray-500 dark:text-gray-400">
                                Update the status for Order #{{ order.id }}
                            </p>
                        </div>
                    </div>
                </div>

                <form @submit.prevent="updateStatus" class="mt-5 sm:mt-6">
                    <!-- Current Status -->
                    <div class="mb-4">
                        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                            Current Status
                        </label>
                        <div class="flex items-center">
                            <span
                                :class="[
                                    'inline-flex items-center px-3 py-1 rounded-full text-sm font-medium',
                                    getStatusColor(order.status)
                                ]"
                            >
                                {{ formatStatus(order.status) }}
                            </span>
                        </div>
                    </div>

                    <!-- New Status -->
                    <div class="mb-4">
                        <label for="status" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                            New Status
                        </label>
                        <select
                            id="status"
                            v-model="form.status"
                            required
                            class="w-full rounded-md border-gray-300 dark:border-gray-700 dark:bg-gray-900 dark:text-gray-300 focus:border-blue-500 focus:ring-blue-500"
                        >
                            <option value="">Select new status</option>
                            <option
                                v-for="status in availableStatuses"
                                :key="status.value"
                                :value="status.value"
                            >
                                {{ status.label }}
                            </option>
                        </select>
                        <p v-if="errors.status" class="mt-1 text-sm text-red-600 dark:text-red-400">
                            {{ errors.status }}
                        </p>
                    </div>

                    <!-- Notes -->
                    <div class="mb-6">
                        <label for="notes" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                            Notes (Optional)
                        </label>
                        <textarea
                            id="notes"
                            v-model="form.notes"
                            rows="3"
                            class="w-full rounded-md border-gray-300 dark:border-gray-700 dark:bg-gray-900 dark:text-gray-300 focus:border-blue-500 focus:ring-blue-500"
                            placeholder="Add any notes about this status change..."
                        ></textarea>
                        <p v-if="errors.notes" class="mt-1 text-sm text-red-600 dark:text-red-400">
                            {{ errors.notes }}
                        </p>
                    </div>

                    <!-- Action Buttons -->
                    <div class="mt-5 sm:mt-6 sm:grid sm:grid-cols-2 sm:gap-3 sm:grid-flow-row-dense">
                        <button
                            type="submit"
                            :disabled="!form.status || processing"
                            class="w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-blue-600 text-base font-medium text-white hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 sm:col-start-2 sm:text-sm disabled:bg-gray-400 disabled:cursor-not-allowed transition-colors duration-200"
                        >
                            {{ processing ? 'Updating...' : 'Update Status' }}
                        </button>
                        <button
                            type="button"
                            @click="$emit('close')"
                            class="mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 dark:border-gray-600 shadow-sm px-4 py-2 bg-white dark:bg-gray-700 text-base font-medium text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 sm:mt-0 sm:col-start-1 sm:text-sm transition-colors duration-200"
                        >
                            Cancel
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</template>

<script setup>
import { ref, reactive, computed } from 'vue'
import { ClipboardDocumentCheckIcon } from '@heroicons/vue/24/outline'

const props = defineProps({
    order: {
        type: Object,
        required: true
    }
})

const emit = defineEmits(['close', 'updated'])

const processing = ref(false)
const errors = ref({})

const form = reactive({
    status: '',
    notes: ''
})

// Available status transitions based on current status
const availableStatuses = computed(() => {
    const currentStatus = props.order.status
    const statusOptions = {
        pending: [
            { value: 'confirmed', label: 'Confirmed' },
            { value: 'cancelled', label: 'Cancelled' }
        ],
        confirmed: [
            { value: 'preparing', label: 'Preparing' },
            { value: 'cancelled', label: 'Cancelled' }
        ],
        preparing: [
            { value: 'ready', label: 'Ready' },
            { value: 'cancelled', label: 'Cancelled' }
        ],
        ready: [
            { value: 'served', label: 'Served' },
            { value: 'preparing', label: 'Back to Preparing' }
        ],
        served: [
            { value: 'completed', label: 'Completed' }
        ]
    }
    
    return statusOptions[currentStatus] || []
})

const formatStatus = (status) => {
    return status.charAt(0).toUpperCase() + status.slice(1)
}

const getStatusColor = (status) => {
    const colors = {
        pending: 'bg-yellow-100 text-yellow-800',
        confirmed: 'bg-blue-100 text-blue-800',
        preparing: 'bg-orange-100 text-orange-800',
        ready: 'bg-green-100 text-green-800',
        served: 'bg-purple-100 text-purple-800',
        completed: 'bg-gray-100 text-gray-800',
        cancelled: 'bg-red-100 text-red-800'
    }
    return colors[status] || 'bg-gray-100 text-gray-800'
}

const updateStatus = async () => {
    if (!form.status) return

    processing.value = true
    errors.value = {}

    try {
        const response = await axios.post(route('waiter.update-order-status', props.order.id), {
            status: form.status,
            notes: form.notes
        })

        if (response.data.message) {
            // Show success message (you can implement a toast notification here)
            console.log('Status updated successfully')
        }

        emit('updated')
    } catch (error) {
        if (error.response?.data?.errors) {
            errors.value = error.response.data.errors
        } else {
            console.error('Error updating status:', error)
            alert('Failed to update status. Please try again.')
        }
    } finally {
        processing.value = false
    }
}
</script>
