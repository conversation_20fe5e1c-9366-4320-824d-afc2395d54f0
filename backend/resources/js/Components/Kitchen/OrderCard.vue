<script setup>
import { computed } from 'vue';

const props = defineProps({
    order: Object,
    status: String,
    loading: Boolean
});

const emit = defineEmits([
    'start-preparing',
    'mark-ready',
    'mark-served',
    'update-status'
]);

// Computed properties
const orderItems = computed(() => {
    return props.order?.items?.filter(item => item.status !== 'cancelled') || [];
});

const timeElapsed = computed(() => {
    if (!props.order?.created_at) return '';
    const now = new Date();
    const orderTime = new Date(props.order.created_at);
    const diffMinutes = Math.floor((now - orderTime) / (1000 * 60));
    
    if (diffMinutes < 60) {
        return `${diffMinutes}m`;
    } else {
        const hours = Math.floor(diffMinutes / 60);
        const minutes = diffMinutes % 60;
        return `${hours}h ${minutes}m`;
    }
});

const isUrgent = computed(() => {
    if (!props.order?.created_at) return false;
    const now = new Date();
    const orderTime = new Date(props.order.created_at);
    const diffMinutes = Math.floor((now - orderTime) / (1000 * 60));
    return diffMinutes > 30; // Orders older than 30 minutes are urgent
});

const statusColor = computed(() => {
    switch (props.status) {
        case 'pending':
            return 'border-yellow-300 bg-yellow-50';
        case 'received':
            return 'border-blue-300 bg-blue-50';
        case 'preparing':
            return 'border-orange-300 bg-orange-50';
        case 'ready':
            return 'border-green-300 bg-green-50';
        default:
            return 'border-gray-300 bg-gray-50';
    }
});

// Format time
const formatTime = (dateString) => {
    if (!dateString) return '';
    return new Date(dateString).toLocaleTimeString();
};

// Action handlers
const startPreparing = () => {
    emit('start-preparing', props.order);
};

const markReady = () => {
    emit('mark-ready', props.order);
};

const markServed = () => {
    emit('mark-served', props.order);
};

const updateStatus = (status) => {
    emit('update-status', props.order, status);
};
</script>

<template>
    <div :class="[
        'border-2 rounded-lg p-4 transition-all duration-200',
        statusColor,
        isUrgent ? 'ring-2 ring-red-500 ring-opacity-50' : '',
        loading ? 'opacity-50 pointer-events-none' : ''
    ]">
        <!-- Order Header -->
        <div class="flex justify-between items-start mb-3">
            <div>
                <h3 class="font-bold text-lg text-gray-900">
                    {{ order.order_number }}
                </h3>
                <div class="flex items-center space-x-2 text-sm text-gray-600">
                    <span v-if="order.table">
                        🪑 {{ order.table.name }}
                    </span>
                    <span v-if="order.guest_count">
                        👥 {{ order.guest_count }}
                    </span>
                </div>
            </div>
            
            <div class="text-right">
                <div :class="[
                    'text-sm font-medium px-2 py-1 rounded-full',
                    isUrgent ? 'bg-red-100 text-red-800' : 'bg-gray-100 text-gray-600'
                ]">
                    ⏱️ {{ timeElapsed }}
                </div>
                <div class="text-xs text-gray-500 mt-1">
                    {{ formatTime(order.created_at) }}
                </div>
            </div>
        </div>

        <!-- Order Items -->
        <div class="space-y-2 mb-4">
            <div
                v-for="item in orderItems"
                :key="item.id"
                class="bg-white rounded-lg p-3 border border-gray-200"
            >
                <div class="flex justify-between items-start">
                    <div class="flex-1">
                        <h4 class="font-medium text-gray-900">
                            {{ item.food_name || item.menu_item?.name }}
                        </h4>
                        <div class="text-sm text-gray-600">
                            Qty: {{ item.quantity }}
                        </div>
                        
                        <!-- Special Instructions -->
                        <div v-if="item.special_instructions" class="mt-2 text-sm text-orange-700 bg-orange-100 rounded p-2">
                            📝 {{ item.special_instructions }}
                        </div>
                        
                        <!-- Modifiers -->
                        <div v-if="item.modifiers && Object.keys(item.modifiers).length > 0" class="mt-2">
                            <div class="text-xs text-gray-500 mb-1">Modifiers:</div>
                            <div class="flex flex-wrap gap-1">
                                <span
                                    v-for="(value, key) in item.modifiers"
                                    :key="key"
                                    class="text-xs bg-blue-100 text-blue-800 px-2 py-1 rounded-full"
                                >
                                    {{ key }}: {{ value }}
                                </span>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Item Status -->
                    <div class="ml-3">
                        <span :class="[
                            'inline-flex px-2 py-1 text-xs font-medium rounded-full',
                            item.kitchen_status === 'pending' ? 'bg-yellow-100 text-yellow-800' :
                            item.kitchen_status === 'received' ? 'bg-blue-100 text-blue-800' :
                            item.kitchen_status === 'preparing' ? 'bg-orange-100 text-orange-800' :
                            item.kitchen_status === 'ready' ? 'bg-green-100 text-green-800' :
                            'bg-gray-100 text-gray-800'
                        ]">
                            {{ $t(`kitchen.${item.kitchen_status}`) }}
                        </span>
                    </div>
                </div>
            </div>
        </div>

        <!-- Order Notes -->
        <div v-if="order.special_instructions || order.kitchen_notes" class="mb-4">
            <div v-if="order.special_instructions" class="text-sm text-gray-700 bg-yellow-100 rounded p-2 mb-2">
                <strong>{{ $t('kitchen.special_requests') }}:</strong> {{ order.special_instructions }}
            </div>
            <div v-if="order.kitchen_notes" class="text-sm text-gray-700 bg-blue-100 rounded p-2">
                <strong>{{ $t('kitchen.kitchen_notes') }}:</strong> {{ order.kitchen_notes }}
            </div>
        </div>

        <!-- Action Buttons -->
        <div class="flex flex-col space-y-2">
            <!-- Pending Status Actions -->
            <template v-if="status === 'pending'">
                <button
                    @click="updateStatus('received')"
                    :disabled="loading"
                    class="w-full px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 font-medium transition-colors"
                >
                    ✅ {{ $t('kitchen.order_received') }}
                </button>
            </template>

            <!-- Received Status Actions -->
            <template v-if="status === 'received'">
                <button
                    @click="startPreparing"
                    :disabled="loading"
                    class="w-full px-4 py-2 bg-orange-600 text-white rounded-lg hover:bg-orange-700 disabled:opacity-50 font-medium transition-colors"
                >
                    🍳 {{ $t('kitchen.start_preparing') }}
                </button>
            </template>

            <!-- Preparing Status Actions -->
            <template v-if="status === 'preparing'">
                <button
                    @click="markReady"
                    :disabled="loading"
                    class="w-full px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 disabled:opacity-50 font-medium transition-colors"
                >
                    🔔 {{ $t('kitchen.mark_ready') }}
                </button>
            </template>

            <!-- Ready Status Actions -->
            <template v-if="status === 'ready'">
                <button
                    @click="markServed"
                    :disabled="loading"
                    class="w-full px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 disabled:opacity-50 font-medium transition-colors"
                >
                    🚚 {{ $t('kitchen.mark_served') }}
                </button>
            </template>

            <!-- Estimated Prep Time -->
            <div v-if="order.estimated_prep_time" class="text-center text-sm text-gray-600">
                {{ $t('kitchen.estimated_time') }}: {{ order.estimated_prep_time }}min
            </div>
        </div>

        <!-- Urgent Order Indicator -->
        <div v-if="isUrgent" class="mt-3 text-center">
            <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-red-100 text-red-800">
                🚨 {{ $t('Urgent Order') }}
            </span>
        </div>
    </div>
</template>

<style scoped>
/* Pulse animation for urgent orders */
@keyframes pulse-urgent {
    0%, 100% {
        box-shadow: 0 0 0 0 rgba(239, 68, 68, 0.7);
    }
    50% {
        box-shadow: 0 0 0 10px rgba(239, 68, 68, 0);
    }
}

.ring-red-500 {
    animation: pulse-urgent 2s infinite;
}
</style>
