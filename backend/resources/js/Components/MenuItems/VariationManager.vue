<template>
    <div class="space-y-6">
        <div class="flex items-center justify-between">
            <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100">
                Menu Item Variations
            </h3>
            <button
                @click="addVariation"
                type="button"
                class="inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
            >
                <PlusIcon class="h-4 w-4 mr-1" />
                Add Variation
            </button>
        </div>

        <div v-if="variations.length === 0" class="text-center py-8 text-gray-500 dark:text-gray-400">
            No variations added yet. Click "Add Variation" to create size options, customizations, or other choices for this menu item.
        </div>

        <div v-else class="space-y-4">
            <div
                v-for="(variation, variationIndex) in variations"
                :key="variationIndex"
                class="border border-gray-200 dark:border-gray-700 rounded-lg p-4 bg-white dark:bg-gray-800"
            >
                <div class="flex items-center justify-between mb-4">
                    <h4 class="text-md font-medium text-gray-900 dark:text-gray-100">
                        Variation {{ variationIndex + 1 }}
                    </h4>
                    <button
                        @click="removeVariation(variationIndex)"
                        type="button"
                        class="text-red-600 hover:text-red-800 dark:text-red-400 dark:hover:text-red-300"
                    >
                        <TrashIcon class="h-5 w-5" />
                    </button>
                </div>

                <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                            Variation Name
                        </label>
                        <input
                            v-model="variation.name"
                            type="text"
                            placeholder="e.g., Size, Crust Type"
                            class="w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-gray-100 shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
                        />
                    </div>

                    <div>
                        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                            Type
                        </label>
                        <select
                            v-model="variation.type"
                            class="w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-gray-100 shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
                        >
                            <option value="size">Size</option>
                            <option value="option">Option</option>
                            <option value="customization">Customization</option>
                            <option value="addon">Add-on</option>
                        </select>
                    </div>
                </div>

                <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
                    <div class="flex items-center">
                        <input
                            v-model="variation.is_required"
                            type="checkbox"
                            class="rounded border-gray-300 text-indigo-600 shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
                        />
                        <label class="ml-2 text-sm text-gray-700 dark:text-gray-300">
                            Required
                        </label>
                    </div>

                    <div class="flex items-center">
                        <input
                            v-model="variation.is_multiple"
                            type="checkbox"
                            class="rounded border-gray-300 text-indigo-600 shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
                        />
                        <label class="ml-2 text-sm text-gray-700 dark:text-gray-300">
                            Multiple Selection
                        </label>
                    </div>

                    <div v-if="variation.is_multiple" class="grid grid-cols-2 gap-2">
                        <input
                            v-model.number="variation.min_selections"
                            type="number"
                            min="0"
                            placeholder="Min"
                            class="w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-gray-100 shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
                        />
                        <input
                            v-model.number="variation.max_selections"
                            type="number"
                            min="1"
                            placeholder="Max"
                            class="w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-gray-100 shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
                        />
                    </div>
                </div>

                <!-- Variation Options -->
                <div class="border-t border-gray-200 dark:border-gray-600 pt-4">
                    <div class="flex items-center justify-between mb-3">
                        <h5 class="text-sm font-medium text-gray-900 dark:text-gray-100">
                            Options
                        </h5>
                        <button
                            @click="addOption(variationIndex)"
                            type="button"
                            class="text-sm text-indigo-600 hover:text-indigo-800 dark:text-indigo-400 dark:hover:text-indigo-300"
                        >
                            + Add Option
                        </button>
                    </div>

                    <div class="space-y-2">
                        <div
                            v-for="(option, optionIndex) in variation.options"
                            :key="optionIndex"
                            class="flex items-center gap-3 p-3 bg-gray-50 dark:bg-gray-700 rounded-md"
                        >
                            <input
                                v-model="option.name"
                                type="text"
                                placeholder="Option name"
                                class="flex-1 rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-800 dark:text-gray-100 shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
                            />
                            <div class="flex items-center">
                                <span class="text-sm text-gray-500 dark:text-gray-400 mr-1">$</span>
                                <input
                                    v-model.number="option.price"
                                    type="number"
                                    step="0.01"
                                    min="0"
                                    placeholder="0.00"
                                    class="w-20 rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-800 dark:text-gray-100 shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
                                />
                            </div>
                            <div class="flex items-center">
                                <input
                                    v-model="option.is_default"
                                    type="checkbox"
                                    class="rounded border-gray-300 text-indigo-600 shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
                                />
                                <label class="ml-1 text-xs text-gray-600 dark:text-gray-400">
                                    Default
                                </label>
                            </div>
                            <button
                                @click="removeOption(variationIndex, optionIndex)"
                                type="button"
                                class="text-red-600 hover:text-red-800 dark:text-red-400 dark:hover:text-red-300"
                            >
                                <TrashIcon class="h-4 w-4" />
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script setup>
import { ref, watch } from 'vue'
import { PlusIcon, TrashIcon } from '@heroicons/vue/24/outline'

const props = defineProps({
    modelValue: {
        type: Array,
        default: () => []
    }
})

const emit = defineEmits(['update:modelValue'])

const variations = ref([...props.modelValue])

// Watch for changes and emit to parent
watch(variations, (newValue) => {
    emit('update:modelValue', newValue)
}, { deep: true })

// Watch for prop changes
watch(() => props.modelValue, (newValue) => {
    variations.value = [...newValue]
}, { deep: true })

const addVariation = () => {
    variations.value.push({
        name: '',
        type: 'size',
        is_required: false,
        is_multiple: false,
        min_selections: null,
        max_selections: null,
        options: [
            { name: '', price: 0, is_default: true }
        ]
    })
}

const removeVariation = (index) => {
    variations.value.splice(index, 1)
}

const addOption = (variationIndex) => {
    variations.value[variationIndex].options.push({
        name: '',
        price: 0,
        is_default: false
    })
}

const removeOption = (variationIndex, optionIndex) => {
    variations.value[variationIndex].options.splice(optionIndex, 1)
}
</script>
