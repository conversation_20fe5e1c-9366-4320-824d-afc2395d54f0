<template>
    <div class="space-y-6">
        <!-- Category Selection -->
        <div>
            <label for="category" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Category *
            </label>
            <div class="relative">
                <button
                    type="button"
                    @click="toggleCategoryDropdown"
                    class="relative w-full bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-lg shadow-sm pl-3 pr-10 py-3 text-left cursor-pointer focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 transition-all duration-200"
                    :class="{
                        'border-red-300 dark:border-red-600': categoryError,
                        'ring-2 ring-indigo-500 border-indigo-500': showCategoryDropdown
                    }"
                >
                    <span class="flex items-center">
                        <span v-if="selectedCategoryName" class="block truncate text-gray-900 dark:text-gray-100">
                            {{ selectedCategoryName }}
                        </span>
                        <span v-else class="block truncate text-gray-500 dark:text-gray-400">
                            Select a category
                        </span>
                    </span>
                    <span class="absolute inset-y-0 right-0 flex items-center pr-2 pointer-events-none">
                        <svg class="h-5 w-5 text-gray-400 transition-transform duration-200" :class="{ 'rotate-180': showCategoryDropdown }" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" />
                        </svg>
                    </span>
                </button>

                <!-- Category Dropdown -->
                <div v-if="showCategoryDropdown" class="absolute z-10 mt-1 w-full bg-white dark:bg-gray-700 shadow-lg max-h-60 rounded-lg py-1 text-base ring-1 ring-black ring-opacity-5 overflow-auto focus:outline-none border border-gray-200 dark:border-gray-600">
                    <div class="sticky top-0 bg-white dark:bg-gray-700 p-2 border-b border-gray-200 dark:border-gray-600">
                        <input
                            v-model="categorySearchQuery"
                            type="text"
                            placeholder="Search categories..."
                            class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 dark:bg-gray-600 dark:text-gray-100"
                        />
                    </div>
                    <div
                        v-for="category in filteredCategories"
                        :key="category.id"
                        @click="selectCategory(category)"
                        class="cursor-pointer select-none relative py-3 pl-3 pr-9 hover:bg-indigo-50 dark:hover:bg-indigo-900/20 transition-colors duration-150"
                        :class="{ 'bg-indigo-100 dark:bg-indigo-900/30': selectedCategory == category.id }"
                    >
                        <div class="flex items-center">
                            <span class="font-medium block truncate text-gray-900 dark:text-gray-100">
                                {{ category.name }}
                            </span>
                            <span v-if="category.description" class="ml-2 text-sm text-gray-500 dark:text-gray-400 truncate">
                                {{ category.description }}
                            </span>
                        </div>
                        <span v-if="selectedCategory == category.id" class="absolute inset-y-0 right-0 flex items-center pr-4">
                            <svg class="h-5 w-5 text-indigo-600 dark:text-indigo-400" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd" />
                            </svg>
                        </span>
                    </div>
                    <div v-if="filteredCategories.length === 0" class="py-3 px-3 text-sm text-gray-500 dark:text-gray-400">
                        No categories found
                    </div>
                </div>
            </div>
            <p v-if="categoryError" class="mt-2 text-sm text-red-600 dark:text-red-400">
                {{ categoryError }}
            </p>
        </div>

        <!-- Subcategory Selection -->
        <div v-if="availableSubcategories.length > 0">
            <label for="subcategory" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Subcategory
            </label>
            <div class="relative">
                <button
                    type="button"
                    @click="toggleSubcategoryDropdown"
                    class="relative w-full bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-lg shadow-sm pl-3 pr-10 py-3 text-left cursor-pointer focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 transition-all duration-200"
                    :class="{
                        'border-red-300 dark:border-red-600': subcategoryError,
                        'ring-2 ring-indigo-500 border-indigo-500': showSubcategoryDropdown
                    }"
                >
                    <span class="flex items-center">
                        <span v-if="selectedSubcategoryName" class="block truncate text-gray-900 dark:text-gray-100">
                            {{ selectedSubcategoryName }}
                        </span>
                        <span v-else class="block truncate text-gray-500 dark:text-gray-400">
                            Select a subcategory (optional)
                        </span>
                    </span>
                    <span class="absolute inset-y-0 right-0 flex items-center pr-2 pointer-events-none">
                        <svg class="h-5 w-5 text-gray-400 transition-transform duration-200" :class="{ 'rotate-180': showSubcategoryDropdown }" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" />
                        </svg>
                    </span>
                </button>

                <!-- Subcategory Dropdown -->
                <div v-if="showSubcategoryDropdown" class="absolute z-10 mt-1 w-full bg-white dark:bg-gray-700 shadow-lg max-h-60 rounded-lg py-1 text-base ring-1 ring-black ring-opacity-5 overflow-auto focus:outline-none border border-gray-200 dark:border-gray-600">
                    <div class="sticky top-0 bg-white dark:bg-gray-700 p-2 border-b border-gray-200 dark:border-gray-600">
                        <input
                            v-model="subcategorySearchQuery"
                            type="text"
                            placeholder="Search subcategories..."
                            class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 dark:bg-gray-600 dark:text-gray-100"
                        />
                    </div>
                    <div
                        @click="selectSubcategory(null)"
                        class="cursor-pointer select-none relative py-3 pl-3 pr-9 hover:bg-indigo-50 dark:hover:bg-indigo-900/20 transition-colors duration-150"
                        :class="{ 'bg-indigo-100 dark:bg-indigo-900/30': selectedSubcategory == null }"
                    >
                        <div class="flex items-center">
                            <span class="font-medium block truncate text-gray-500 dark:text-gray-400 italic">
                                None (No subcategory)
                            </span>
                        </div>
                        <span v-if="selectedSubcategory == null" class="absolute inset-y-0 right-0 flex items-center pr-4">
                            <svg class="h-5 w-5 text-indigo-600 dark:text-indigo-400" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd" />
                            </svg>
                        </span>
                    </div>
                    <div
                        v-for="subcategory in filteredSubcategories"
                        :key="subcategory.id"
                        @click="selectSubcategory(subcategory)"
                        class="cursor-pointer select-none relative py-3 pl-3 pr-9 hover:bg-indigo-50 dark:hover:bg-indigo-900/20 transition-colors duration-150"
                        :class="{ 'bg-indigo-100 dark:bg-indigo-900/30': selectedSubcategory == subcategory.id }"
                    >
                        <div class="flex items-center">
                            <span class="font-medium block truncate text-gray-900 dark:text-gray-100">
                                {{ subcategory.name }}
                            </span>
                            <span v-if="subcategory.description" class="ml-2 text-sm text-gray-500 dark:text-gray-400 truncate">
                                {{ subcategory.description }}
                            </span>
                        </div>
                        <span v-if="selectedSubcategory == subcategory.id" class="absolute inset-y-0 right-0 flex items-center pr-4">
                            <svg class="h-5 w-5 text-indigo-600 dark:text-indigo-400" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd" />
                            </svg>
                        </span>
                    </div>
                    <div v-if="filteredSubcategories.length === 0" class="py-3 px-3 text-sm text-gray-500 dark:text-gray-400">
                        No subcategories found
                    </div>
                </div>
            </div>
            <p v-if="subcategoryError" class="mt-2 text-sm text-red-600 dark:text-red-400">
                {{ subcategoryError }}
            </p>
        </div>

        <!-- Loading State -->
        <div v-if="loadingSubcategories" class="flex items-center space-x-2 text-sm text-gray-500">
            <svg class="animate-spin h-4 w-4" fill="none" viewBox="0 0 24 24">
                <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
            </svg>
            <span>Loading subcategories...</span>
        </div>

        <!-- No Subcategories Message -->
        <div v-if="selectedCategory && !loadingSubcategories && availableSubcategories.length === 0" class="text-sm text-gray-500">
            No subcategories available for this category.
        </div>
    </div>
</template>

<script setup>
import { ref, computed, watch, onMounted, onUnmounted } from 'vue'
import axios from 'axios'

const props = defineProps({
    categories: {
        type: Array,
        default: () => []
    },
    categoryId: {
        type: [String, Number],
        default: null
    },
    subcategoryId: {
        type: [String, Number],
        default: null
    },
    categoryError: {
        type: String,
        default: null
    },
    subcategoryError: {
        type: String,
        default: null
    }
})

const emit = defineEmits(['update:categoryId', 'update:subcategoryId'])

// Reactive data
const selectedCategory = ref(props.categoryId)
const selectedSubcategory = ref(props.subcategoryId)
const availableSubcategories = ref([])
const loadingSubcategories = ref(false)

// Dropdown states
const showCategoryDropdown = ref(false)
const showSubcategoryDropdown = ref(false)

// Search queries
const categorySearchQuery = ref('')
const subcategorySearchQuery = ref('')

// Computed properties
const selectedCategoryName = computed(() => {
    const category = props.categories.find(cat => cat.id == selectedCategory.value)
    return category ? category.name : ''
})

const selectedSubcategoryName = computed(() => {
    const subcategory = availableSubcategories.value.find(sub => sub.id == selectedSubcategory.value)
    return subcategory ? subcategory.name : ''
})

const filteredCategories = computed(() => {
    if (!categorySearchQuery.value) return props.categories
    return props.categories.filter(category =>
        category.name.toLowerCase().includes(categorySearchQuery.value.toLowerCase()) ||
        (category.description && category.description.toLowerCase().includes(categorySearchQuery.value.toLowerCase()))
    )
})

const filteredSubcategories = computed(() => {
    if (!subcategorySearchQuery.value) return availableSubcategories.value
    return availableSubcategories.value.filter(subcategory =>
        subcategory.name.toLowerCase().includes(subcategorySearchQuery.value.toLowerCase()) ||
        (subcategory.description && subcategory.description.toLowerCase().includes(subcategorySearchQuery.value.toLowerCase()))
    )
})

// Watch for external changes
watch(() => props.categoryId, (newValue) => {
    selectedCategory.value = newValue
    if (newValue) {
        loadSubcategories(newValue)
    } else {
        availableSubcategories.value = []
        selectedSubcategory.value = null
    }
})

watch(() => props.subcategoryId, (newValue) => {
    selectedSubcategory.value = newValue
})

// Load subcategories when component mounts if category is already selected
onMounted(() => {
    if (selectedCategory.value) {
        loadSubcategories(selectedCategory.value)
    }
    // Add click outside listener
    document.addEventListener('click', handleClickOutside)
})

onUnmounted(() => {
    // Remove click outside listener
    document.removeEventListener('click', handleClickOutside)
})

// Methods
const toggleCategoryDropdown = () => {
    showCategoryDropdown.value = !showCategoryDropdown.value
    showSubcategoryDropdown.value = false
    if (showCategoryDropdown.value) {
        categorySearchQuery.value = ''
    }
}

const toggleSubcategoryDropdown = () => {
    showSubcategoryDropdown.value = !showSubcategoryDropdown.value
    showCategoryDropdown.value = false
    if (showSubcategoryDropdown.value) {
        subcategorySearchQuery.value = ''
    }
}

const selectCategory = (category) => {
    selectedCategory.value = category.id
    showCategoryDropdown.value = false
    categorySearchQuery.value = ''

    emit('update:categoryId', selectedCategory.value)

    // Reset subcategory when category changes
    selectedSubcategory.value = null
    emit('update:subcategoryId', null)

    // Load subcategories for new category
    loadSubcategories(selectedCategory.value)
}

const selectSubcategory = (subcategory) => {
    selectedSubcategory.value = subcategory ? subcategory.id : null
    showSubcategoryDropdown.value = false
    subcategorySearchQuery.value = ''

    emit('update:subcategoryId', selectedSubcategory.value)
}

const handleClickOutside = (event) => {
    const categoryDropdown = event.target.closest('.relative')
    if (!categoryDropdown) {
        showCategoryDropdown.value = false
        showSubcategoryDropdown.value = false
    }
}

const onCategoryChange = () => {
    emit('update:categoryId', selectedCategory.value)

    // Reset subcategory when category changes
    selectedSubcategory.value = null
    emit('update:subcategoryId', null)

    // Load subcategories for new category
    if (selectedCategory.value) {
        loadSubcategories(selectedCategory.value)
    } else {
        availableSubcategories.value = []
    }
}

const onSubcategoryChange = () => {
    emit('update:subcategoryId', selectedSubcategory.value)
}

const loadSubcategories = async (categoryId) => {
    if (!categoryId) {
        availableSubcategories.value = []
        return
    }

    loadingSubcategories.value = true
    
    try {
        const response = await axios.get(`/categories/${categoryId}/subcategories`)
        availableSubcategories.value = response.data || []
    } catch (error) {
        console.error('Failed to load subcategories:', error)
        availableSubcategories.value = []
    } finally {
        loadingSubcategories.value = false
    }
}
</script>
