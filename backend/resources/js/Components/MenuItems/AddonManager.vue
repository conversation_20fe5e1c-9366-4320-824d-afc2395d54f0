<template>
    <div class="space-y-6">
        <div class="flex items-center justify-between">
            <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100">
                Menu Item Add-ons
            </h3>
            <div class="text-sm text-gray-500 dark:text-gray-400">
                Select add-ons that customers can choose with this menu item
            </div>
        </div>

        <div v-if="availableAddons.length === 0" class="text-center py-8 text-gray-500 dark:text-gray-400">
            No add-ons available. Create add-ons first to assign them to menu items.
        </div>

        <div v-else class="space-y-4">
            <!-- Filter by category -->
            <div class="flex flex-wrap gap-2">
                <button
                    v-for="(label, category) in addonCategories"
                    :key="category"
                    @click="selectedCategory = selectedCategory === category ? null : category"
                    :class="[
                        'px-3 py-1 text-sm rounded-full border transition-colors',
                        selectedCategory === category
                            ? 'bg-indigo-100 border-indigo-300 text-indigo-800 dark:bg-indigo-900 dark:border-indigo-600 dark:text-indigo-200'
                            : 'bg-gray-100 border-gray-300 text-gray-700 hover:bg-gray-200 dark:bg-gray-700 dark:border-gray-600 dark:text-gray-300 dark:hover:bg-gray-600'
                    ]"
                >
                    {{ label }}
                </button>
            </div>

            <!-- Addons list -->
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                <div
                    v-for="addon in filteredAddons"
                    :key="addon.id"
                    :class="[
                        'border rounded-lg p-4 cursor-pointer transition-all',
                        isSelected(addon.id)
                            ? 'border-indigo-500 bg-indigo-50 dark:bg-indigo-900/20 dark:border-indigo-400'
                            : 'border-gray-200 bg-white hover:border-gray-300 dark:border-gray-700 dark:bg-gray-800 dark:hover:border-gray-600'
                    ]"
                    @click="toggleAddon(addon)"
                >
                    <div class="flex items-start justify-between">
                        <div class="flex-1">
                            <div class="flex items-center">
                                <input
                                    :checked="isSelected(addon.id)"
                                    type="checkbox"
                                    class="rounded border-gray-300 text-indigo-600 shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
                                    @click.stop
                                    @change="toggleAddon(addon)"
                                />
                                <h4 class="ml-3 text-sm font-medium text-gray-900 dark:text-gray-100">
                                    {{ addon.name }}
                                </h4>
                            </div>
                            
                            <p v-if="addon.description" class="mt-1 text-xs text-gray-500 dark:text-gray-400">
                                {{ addon.description }}
                            </p>
                            
                            <div class="mt-2 flex items-center justify-between">
                                <span class="text-sm font-medium text-green-600 dark:text-green-400">
                                    ${{ addon.price }}
                                </span>
                                <span class="text-xs text-gray-500 dark:text-gray-400 capitalize">
                                    {{ addon.category }}
                                </span>
                            </div>
                        </div>
                    </div>

                    <!-- Addon settings (shown when selected) -->
                    <div v-if="isSelected(addon.id)" class="mt-4 pt-4 border-t border-gray-200 dark:border-gray-600">
                        <div class="space-y-3">
                            <div class="flex items-center justify-between">
                                <label class="flex items-center">
                                    <input
                                        v-model="getAddonSettings(addon.id).is_required"
                                        type="checkbox"
                                        class="rounded border-gray-300 text-indigo-600 shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
                                    />
                                    <span class="ml-2 text-sm text-gray-700 dark:text-gray-300">Required</span>
                                </label>
                            </div>

                            <div>
                                <label class="block text-sm text-gray-700 dark:text-gray-300 mb-1">
                                    Max Quantity
                                </label>
                                <input
                                    v-model.number="getAddonSettings(addon.id).max_quantity"
                                    type="number"
                                    min="1"
                                    max="10"
                                    class="w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-gray-100 shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
                                />
                            </div>

                            <div>
                                <label class="block text-sm text-gray-700 dark:text-gray-300 mb-1">
                                    Price Override (optional)
                                </label>
                                <div class="relative">
                                    <span class="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-500 dark:text-gray-400">$</span>
                                    <input
                                        v-model.number="getAddonSettings(addon.id).price_override"
                                        type="number"
                                        step="0.01"
                                        min="0"
                                        placeholder="Leave empty to use default price"
                                        class="w-full pl-8 rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-gray-100 shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
                                    />
                                </div>
                                <p class="mt-1 text-xs text-gray-500 dark:text-gray-400">
                                    Default: ${{ addon.price }}
                                </p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script setup>
import { ref, computed, watch } from 'vue'

const props = defineProps({
    availableAddons: {
        type: Array,
        default: () => []
    },
    addonCategories: {
        type: Object,
        default: () => ({})
    },
    selectedAddonIds: {
        type: Array,
        default: () => []
    },
    addonSettings: {
        type: Array,
        default: () => []
    }
})

const emit = defineEmits(['update:selectedAddonIds', 'update:addonSettings'])

const selectedCategory = ref(null)
const selectedIds = ref([...props.selectedAddonIds])
const settings = ref([...props.addonSettings])

// Watch for changes and emit to parent
watch(selectedIds, (newValue) => {
    emit('update:selectedAddonIds', newValue)
}, { deep: true })

watch(settings, (newValue) => {
    emit('update:addonSettings', newValue)
}, { deep: true })

// Watch for prop changes
watch(() => props.selectedAddonIds, (newValue) => {
    selectedIds.value = [...newValue]
})

watch(() => props.addonSettings, (newValue) => {
    settings.value = [...newValue]
})

const filteredAddons = computed(() => {
    if (!selectedCategory.value) {
        return props.availableAddons
    }
    return props.availableAddons.filter(addon => addon.category === selectedCategory.value)
})

const isSelected = (addonId) => {
    return selectedIds.value.includes(addonId)
}

const toggleAddon = (addon) => {
    const index = selectedIds.value.indexOf(addon.id)
    
    if (index > -1) {
        // Remove addon
        selectedIds.value.splice(index, 1)
        // Remove settings
        const settingsIndex = settings.value.findIndex(s => s.addon_id === addon.id)
        if (settingsIndex > -1) {
            settings.value.splice(settingsIndex, 1)
        }
    } else {
        // Add addon
        selectedIds.value.push(addon.id)
        // Add default settings
        settings.value.push({
            addon_id: addon.id,
            is_required: false,
            max_quantity: 1,
            price_override: null
        })
    }
}

const getAddonSettings = (addonId) => {
    let addonSettings = settings.value.find(s => s.addon_id === addonId)
    
    if (!addonSettings) {
        addonSettings = {
            addon_id: addonId,
            is_required: false,
            max_quantity: 1,
            price_override: null
        }
        settings.value.push(addonSettings)
    }
    
    return addonSettings
}
</script>
