<script setup>
import { ref, computed, watch, onMounted, onUnmounted } from 'vue'

const props = defineProps({
    modelValue: {
        type: Array,
        default: () => []
    },
    categories: {
        type: Array,
        required: true
    },
    placeholder: {
        type: String,
        default: 'Select categories...'
    },
    multiple: {
        type: Boolean,
        default: true
    },
    maxSelections: {
        type: Number,
        default: null
    },
    error: {
        type: String,
        default: null
    },
    required: {
        type: Boolean,
        default: false
    }
})

const emit = defineEmits(['update:modelValue'])

// Reactive data
const isOpen = ref(false)
const searchQuery = ref('')
const dropdownRef = ref(null)
const inputRef = ref(null)

// Computed properties
const hierarchicalCategories = computed(() => {
    const result = []
    
    // First, add all parent categories (categories without parent_id)
    const parentCategories = props.categories.filter(cat => !cat.parent_id)
    
    parentCategories.forEach(parent => {
        // Add parent category
        result.push({
            ...parent,
            level: 0,
            displayName: parent.name,
            isParent: true
        })
        
        // Add child categories
        const children = props.categories.filter(cat => cat.parent_id === parent.id)
        children.forEach(child => {
            result.push({
                ...child,
                level: 1,
                displayName: `${parent.name} → ${child.name}`,
                isParent: false,
                parentName: parent.name
            })
        })
    })
    
    return result
})

const filteredCategories = computed(() => {
    if (!searchQuery.value) {
        return hierarchicalCategories.value
    }
    
    const query = searchQuery.value.toLowerCase()
    return hierarchicalCategories.value.filter(category => {
        return category.name.toLowerCase().includes(query) ||
               category.displayName.toLowerCase().includes(query) ||
               (category.parentName && category.parentName.toLowerCase().includes(query))
    })
})

const selectedCategories = computed(() => {
    return props.categories.filter(cat => props.modelValue.includes(cat.id))
})

const canAddMore = computed(() => {
    if (!props.maxSelections) return true
    return props.modelValue.length < props.maxSelections
})

const displayText = computed(() => {
    if (props.modelValue.length === 0) {
        return props.placeholder
    }
    
    if (props.modelValue.length === 1) {
        const category = selectedCategories.value[0]
        if (category) {
            const parent = props.categories.find(c => c.id === category.parent_id)
            return parent ? `${parent.name} → ${category.name}` : category.name
        }
    }
    
    return `${props.modelValue.length} categories selected`
})

// Methods
const toggleDropdown = () => {
    if (!canAddMore.value && props.modelValue.length > 0) return
    isOpen.value = !isOpen.value
    if (isOpen.value) {
        searchQuery.value = ''
        setTimeout(() => {
            inputRef.value?.focus()
        }, 100)
    }
}

const selectCategory = (category) => {
    if (!props.multiple) {
        emit('update:modelValue', [category.id])
        isOpen.value = false
        return
    }
    
    const currentIds = [...props.modelValue]
    const index = currentIds.indexOf(category.id)
    
    if (index > -1) {
        // Remove if already selected
        currentIds.splice(index, 1)
    } else {
        // Add if not selected and under limit
        if (canAddMore.value) {
            currentIds.push(category.id)
        }
    }
    
    emit('update:modelValue', currentIds)
    
    if (!props.multiple) {
        isOpen.value = false
    }
}

const removeCategory = (categoryId) => {
    const currentIds = [...props.modelValue]
    const index = currentIds.indexOf(categoryId)
    if (index > -1) {
        currentIds.splice(index, 1)
        emit('update:modelValue', currentIds)
    }
}

const clearAll = () => {
    emit('update:modelValue', [])
}

const isSelected = (categoryId) => {
    return props.modelValue.includes(categoryId)
}

const handleClickOutside = (event) => {
    if (dropdownRef.value && !dropdownRef.value.contains(event.target)) {
        isOpen.value = false
    }
}

// Lifecycle
onMounted(() => {
    document.addEventListener('click', handleClickOutside)
})

onUnmounted(() => {
    document.removeEventListener('click', handleClickOutside)
})
</script>

<template>
    <div class="hierarchical-category-select" ref="dropdownRef">
        <!-- Label -->
        <label v-if="$slots.label || required" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            <slot name="label">Categories</slot>
            <span v-if="required" class="text-red-500 ml-1">*</span>
        </label>

        <!-- Selected Categories Tags -->
        <div v-if="selectedCategories.length > 0" class="mb-3">
            <div class="flex flex-wrap gap-2">
                <span
                    v-for="category in selectedCategories"
                    :key="category.id"
                    class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200"
                >
                    <span v-if="category.parent_id">
                        {{ categories.find(c => c.id === category.parent_id)?.name }} → {{ category.name }}
                    </span>
                    <span v-else>{{ category.name }}</span>
                    <button
                        @click.stop="removeCategory(category.id)"
                        type="button"
                        class="ml-2 inline-flex items-center justify-center w-4 h-4 rounded-full text-blue-600 hover:bg-blue-200 hover:text-blue-800 focus:outline-none dark:text-blue-300 dark:hover:bg-blue-800"
                    >
                        <svg class="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                        </svg>
                    </button>
                </span>
                <button
                    v-if="selectedCategories.length > 1"
                    @click="clearAll"
                    type="button"
                    class="inline-flex items-center px-2 py-1 text-xs text-red-600 hover:text-red-800 dark:text-red-400 dark:hover:text-red-300"
                >
                    Clear all
                </button>
            </div>
        </div>

        <!-- Dropdown Trigger -->
        <div class="relative">
            <button
                @click="toggleDropdown"
                type="button"
                :class="[
                    'relative w-full bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm pl-3 pr-10 py-2 text-left cursor-pointer focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500 sm:text-sm',
                    error ? 'border-red-300 focus:ring-red-500 focus:border-red-500' : '',
                    !canAddMore && modelValue.length > 0 ? 'opacity-50 cursor-not-allowed' : ''
                ]"
                :disabled="!canAddMore && modelValue.length > 0"
            >
                <span class="block truncate text-gray-900 dark:text-gray-100">
                    {{ displayText }}
                </span>
                <span class="absolute inset-y-0 right-0 flex items-center pr-2 pointer-events-none">
                    <svg
                        :class="['w-5 h-5 text-gray-400 transition-transform', isOpen ? 'rotate-180' : '']"
                        fill="none"
                        stroke="currentColor"
                        viewBox="0 0 24 24"
                    >
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" />
                    </svg>
                </span>
            </button>

            <!-- Dropdown Menu -->
            <div
                v-show="isOpen"
                class="absolute z-50 mt-1 w-full bg-white dark:bg-gray-800 shadow-lg max-h-60 rounded-md py-1 text-base ring-1 ring-black ring-opacity-5 overflow-auto focus:outline-none sm:text-sm"
            >
                <!-- Search Input -->
                <div class="sticky top-0 bg-white dark:bg-gray-800 px-3 py-2 border-b border-gray-200 dark:border-gray-600">
                    <input
                        ref="inputRef"
                        v-model="searchQuery"
                        type="text"
                        placeholder="Search categories..."
                        class="w-full px-3 py-1 border border-gray-300 dark:border-gray-600 rounded-md text-sm focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-gray-100"
                    >
                </div>

                <!-- Category Options -->
                <div v-if="filteredCategories.length === 0" class="px-3 py-2 text-sm text-gray-500 dark:text-gray-400">
                    No categories found
                </div>
                
                <div v-else>
                    <button
                        v-for="category in filteredCategories"
                        :key="category.id"
                        @click="selectCategory(category)"
                        type="button"
                        :class="[
                            'w-full text-left px-3 py-2 text-sm hover:bg-gray-100 dark:hover:bg-gray-700 flex items-center justify-between',
                            category.level === 1 ? 'pl-6' : '',
                            isSelected(category.id) ? 'bg-blue-50 text-blue-700 dark:bg-blue-900 dark:text-blue-200' : 'text-gray-900 dark:text-gray-100'
                        ]"
                    >
                        <span class="flex items-center">
                            <span v-if="category.level === 1" class="text-gray-400 mr-2">→</span>
                            <span :class="category.isParent ? 'font-medium' : ''">
                                {{ category.level === 1 ? category.name : category.displayName }}
                            </span>
                        </span>
                        <svg
                            v-if="isSelected(category.id)"
                            class="w-4 h-4 text-blue-600 dark:text-blue-400"
                            fill="currentColor"
                            viewBox="0 0 20 20"
                        >
                            <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd" />
                        </svg>
                    </button>
                </div>
            </div>
        </div>

        <!-- Error Message -->
        <div v-if="error" class="mt-1 text-sm text-red-600 dark:text-red-400">
            {{ error }}
        </div>

        <!-- Help Text -->
        <div v-if="!error && maxSelections" class="mt-1 text-sm text-gray-500 dark:text-gray-400">
            {{ multiple ? `Select up to ${maxSelections} categories` : 'Select a category' }}
        </div>
    </div>
</template>

<style scoped>
.hierarchical-category-select {
    @apply relative;
}
</style>
