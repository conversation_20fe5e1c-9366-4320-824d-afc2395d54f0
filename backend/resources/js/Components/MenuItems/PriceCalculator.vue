<template>
    <div class="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg p-4">
        <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100 mb-4">
            Price Calculator
        </h3>

        <div class="space-y-4">
            <!-- Base Price -->
            <div class="flex justify-between items-center">
                <span class="text-sm text-gray-600 dark:text-gray-400">Base Price:</span>
                <span class="font-medium text-gray-900 dark:text-gray-100">{{ formatPrice(basePrice) }}</span>
            </div>

            <!-- Variations -->
            <div v-if="selectedVariations.length > 0" class="space-y-2">
                <div class="text-sm font-medium text-gray-700 dark:text-gray-300">Variations:</div>
                <div
                    v-for="variation in selectedVariations"
                    :key="variation.id"
                    class="flex justify-between items-center text-sm pl-4"
                >
                    <span class="text-gray-600 dark:text-gray-400">
                        {{ variation.name }}: {{ variation.selectedOption?.name }}
                    </span>
                    <span class="text-gray-900 dark:text-gray-100">
                        +${{ (variation.selectedOption?.price || 0).toFixed(2) }}
                    </span>
                </div>
            </div>

            <!-- Add-ons -->
            <div v-if="selectedAddons.length > 0" class="space-y-2">
                <div class="text-sm font-medium text-gray-700 dark:text-gray-300">Add-ons:</div>
                <div
                    v-for="addon in selectedAddons"
                    :key="addon.id"
                    class="flex justify-between items-center text-sm pl-4"
                >
                    <span class="text-gray-600 dark:text-gray-400">
                        {{ addon.name }}
                        <span v-if="addon.quantity > 1" class="text-xs">({{ addon.quantity }}x)</span>
                    </span>
                    <span class="text-gray-900 dark:text-gray-100">
                        +${{ (addon.price * addon.quantity).toFixed(2) }}
                    </span>
                </div>
            </div>

            <!-- Divider -->
            <hr class="border-gray-200 dark:border-gray-600" />

            <!-- Total Price -->
            <div class="flex justify-between items-center">
                <span class="text-lg font-medium text-gray-900 dark:text-gray-100">Total:</span>
                <span class="text-xl font-bold text-green-600 dark:text-green-400">
                    {{ formatPrice(totalPrice) }}
                </span>
            </div>

            <!-- Price Range (if variations exist) -->
            <div v-if="priceRange.min !== priceRange.max" class="text-center">
                <div class="text-sm text-gray-500 dark:text-gray-400">
                    Price range: {{ formatPrice(priceRange.min) }} - {{ formatPrice(priceRange.max) }}
                </div>
            </div>

            <!-- Interactive Demo -->
            <div v-if="showDemo" class="mt-6 pt-4 border-t border-gray-200 dark:border-gray-600">
                <h4 class="text-md font-medium text-gray-900 dark:text-gray-100 mb-3">
                    Price Demo
                </h4>

                <!-- Variation Selection -->
                <div v-if="variations.length > 0" class="space-y-3">
                    <div
                        v-for="variation in variations"
                        :key="variation.id"
                        class="space-y-2"
                    >
                        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300">
                            {{ variation.name }}
                            <span v-if="variation.is_required" class="text-red-500">*</span>
                        </label>
                        <select
                            v-model="demoSelections.variations[variation.id]"
                            class="w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-gray-100 shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
                        >
                            <option value="">Select {{ variation.name }}</option>
                            <option
                                v-for="option in variation.options"
                                :key="option.id"
                                :value="option.id"
                            >
                                {{ option.name }} (+${{ option.price.toFixed(2) }})
                            </option>
                        </select>
                    </div>
                </div>

                <!-- Addon Selection -->
                <div v-if="addons.length > 0" class="mt-4 space-y-2">
                    <label class="block text-sm font-medium text-gray-700 dark:text-gray-300">
                        Add-ons
                    </label>
                    <div class="space-y-2">
                        <label
                            v-for="addon in addons"
                            :key="addon.id"
                            class="flex items-center justify-between p-2 border border-gray-200 dark:border-gray-600 rounded-md"
                        >
                            <div class="flex items-center">
                                <input
                                    v-model="demoSelections.addons[addon.id]"
                                    type="checkbox"
                                    class="rounded border-gray-300 text-indigo-600 shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
                                />
                                <span class="ml-2 text-sm text-gray-700 dark:text-gray-300">
                                    {{ addon.name }}
                                </span>
                            </div>
                            <span class="text-sm text-gray-500 dark:text-gray-400">
                                +${{ addon.price.toFixed(2) }}
                            </span>
                        </label>
                    </div>
                </div>

                <!-- Demo Total -->
                <div class="mt-4 p-3 bg-gray-50 dark:bg-gray-700 rounded-md">
                    <div class="flex justify-between items-center">
                        <span class="font-medium text-gray-900 dark:text-gray-100">Demo Total:</span>
                        <span class="text-lg font-bold text-green-600 dark:text-green-400">
                            {{ formatPrice(demoTotal) }}
                        </span>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script setup>
import { ref, computed, watch } from 'vue'
import { formatPrice } from '@/utils/currency'

const props = defineProps({
    basePrice: {
        type: Number,
        default: 0
    },
    variations: {
        type: Array,
        default: () => []
    },
    addons: {
        type: Array,
        default: () => []
    },
    selectedVariations: {
        type: Array,
        default: () => []
    },
    selectedAddons: {
        type: Array,
        default: () => []
    },
    showDemo: {
        type: Boolean,
        default: false
    }
})

const demoSelections = ref({
    variations: {},
    addons: {}
})

// Calculate total price based on selections
const totalPrice = computed(() => {
    let total = props.basePrice

    // Add variation prices
    props.selectedVariations.forEach(variation => {
        if (variation.selectedOption) {
            total += variation.selectedOption.price
        }
    })

    // Add addon prices
    props.selectedAddons.forEach(addon => {
        total += addon.price * (addon.quantity || 1)
    })

    return total
})

// Calculate price range
const priceRange = computed(() => {
    let min = props.basePrice
    let max = props.basePrice

    // Add minimum and maximum variation prices
    props.variations.forEach(variation => {
        if (variation.options && variation.options.length > 0) {
            const prices = variation.options.map(option => option.price)
            min += Math.min(...prices)
            max += Math.max(...prices)
        }
    })

    // Add addon prices (optional, so only affects max)
    props.addons.forEach(addon => {
        max += addon.price
    })

    return { min, max }
})

// Calculate demo total
const demoTotal = computed(() => {
    let total = props.basePrice

    // Add selected variation prices
    Object.entries(demoSelections.value.variations).forEach(([variationId, optionId]) => {
        if (optionId) {
            const variation = props.variations.find(v => v.id == variationId)
            if (variation) {
                const option = variation.options.find(o => o.id == optionId)
                if (option) {
                    total += option.price
                }
            }
        }
    })

    // Add selected addon prices
    Object.entries(demoSelections.value.addons).forEach(([addonId, selected]) => {
        if (selected) {
            const addon = props.addons.find(a => a.id == addonId)
            if (addon) {
                total += addon.price
            }
        }
    })

    return total
})

// Initialize demo selections with defaults
watch(() => props.variations, (newVariations) => {
    newVariations.forEach(variation => {
        if (variation.is_required && variation.options.length > 0) {
            const defaultOption = variation.options.find(o => o.is_default) || variation.options[0]
            demoSelections.value.variations[variation.id] = defaultOption.id
        }
    })
}, { immediate: true })
</script>
