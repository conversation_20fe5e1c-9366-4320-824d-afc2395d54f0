<template>
    <div class="space-y-4">
        <div class="flex items-center justify-between">
            <label class="block text-sm font-medium text-gray-700">
                Images (Max 5)
            </label>
            <button
                type="button"
                @click="openMediaLibrary"
                class="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
            >
                <svg class="-ml-0.5 mr-2 h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"/>
                </svg>
                Add Images
            </button>
        </div>

        <!-- Selected Images Display -->
        <div v-if="selectedImages.length > 0" class="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-5 gap-4">
            <div
                v-for="(image, index) in selectedImages"
                :key="image.id"
                class="relative group"
            >
                <div class="aspect-square rounded-lg overflow-hidden bg-gray-100 border-2"
                     :class="{ 'border-blue-500': index === 0, 'border-gray-200': index !== 0 }">
                    <img
                        :src="image.thumbnail_url || image.url"
                        :alt="image.alt_text || 'Menu item image'"
                        class="w-full h-full object-cover"
                        @error="handleImageError"
                    />
                </div>

                <!-- Primary Badge -->
                <div v-if="index === 0" class="absolute top-2 left-2">
                    <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                        Primary
                    </span>
                </div>

                <!-- Remove Button -->
                <button
                    type="button"
                    @click="removeImage(index)"
                    class="absolute top-2 right-2 p-1 bg-red-600 text-white rounded-full opacity-0 group-hover:opacity-100 transition-opacity duration-200 hover:bg-red-700"
                >
                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"/>
                    </svg>
                </button>

                <!-- Drag Handle -->
                <div class="absolute bottom-2 right-2 p-1 bg-gray-600 text-white rounded opacity-0 group-hover:opacity-100 transition-opacity duration-200 cursor-move">
                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 8h16M4 16h16"/>
                    </svg>
                </div>
            </div>
        </div>

        <!-- Empty State -->
        <div v-else class="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center">
            <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"/>
            </svg>
            <p class="mt-2 text-sm text-gray-600">No images selected</p>
            <p class="text-xs text-gray-500">Click "Add Images" to select up to 5 images</p>
        </div>

        <!-- Validation Error -->
        <div v-if="error" class="text-red-600 text-sm">
            {{ error }}
        </div>

        <!-- Media Library Modal -->
        <MediaLibraryModal
            v-if="showMediaLibrary"
            :show="showMediaLibrary"
            :multiple="true"
            :max-selection="maxImages - selectedImages.length"
            @close="showMediaLibrary = false"
            @select="handleMediaSelection"
        />
    </div>
</template>

<script setup>
import { ref, computed, watch, nextTick } from 'vue'
import MediaLibraryModal from '@/Components/MediaLibrary/MediaLibraryModal.vue'

const props = defineProps({
    modelValue: {
        type: Array,
        default: () => []
    },
    maxImages: {
        type: Number,
        default: 5
    },
    error: {
        type: String,
        default: null
    }
})

const emit = defineEmits(['update:modelValue'])

const selectedImages = ref([...props.modelValue])
const showMediaLibrary = ref(false)
const isUpdatingFromParent = ref(false)

// Watch for external changes to modelValue
watch(() => props.modelValue, (newValue) => {
    if (!isUpdatingFromParent.value) {
        isUpdatingFromParent.value = true
        selectedImages.value = [...newValue]
        nextTick(() => {
            isUpdatingFromParent.value = false
        })
    }
}, { deep: true })

// Emit changes to parent (only when not updating from parent)
watch(selectedImages, (newValue) => {
    if (!isUpdatingFromParent.value) {
        nextTick(() => {
            emit('update:modelValue', newValue)
        })
    }
}, { deep: true })

const openMediaLibrary = () => {
    if (selectedImages.value.length >= props.maxImages) {
        return
    }
    showMediaLibrary.value = true
}

const handleMediaSelection = (media) => {
    const newImages = Array.isArray(media) ? media : [media]

    // Filter out images that are already selected (prevent duplicates)
    const existingIds = selectedImages.value.map(img => img.id)
    const uniqueNewImages = newImages.filter(img => !existingIds.includes(img.id))

    // Add new images, ensuring we don't exceed max
    const availableSlots = props.maxImages - selectedImages.value.length
    const imagesToAdd = uniqueNewImages.slice(0, availableSlots)

    if (imagesToAdd.length > 0) {
        selectedImages.value.push(...imagesToAdd)
    }
    showMediaLibrary.value = false
}

const removeImage = (index) => {
    selectedImages.value.splice(index, 1)
}

const handleImageError = (event) => {
    if (event.target.dataset.errorHandled) return
    event.target.dataset.errorHandled = 'true'

    // Use inline SVG placeholder
    const svgPlaceholder = 'data:image/svg+xml;base64,' + btoa(`
        <svg xmlns="http://www.w3.org/2000/svg" width="150" height="150" viewBox="0 0 150 150">
            <rect width="150" height="150" fill="#f3f4f6"/>
            <circle cx="60" cy="50" r="8" fill="#d1d5db"/>
            <polygon points="30,100 50,70 70,85 90,60 120,100" fill="#d1d5db"/>
            <text x="75" y="130" text-anchor="middle" fill="#9ca3af" font-size="12">Image</text>
        </svg>
    `)

    event.target.src = svgPlaceholder
}
</script>
