<template>
    <div class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
        <div class="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
            <div class="mt-3">
                <!-- Header -->
                <div class="flex items-center justify-between mb-4">
                    <h3 class="text-lg font-medium text-gray-900">
                        {{ $t('loyalty.adjust_points.title') }}
                    </h3>
                    <button
                        @click="$emit('close')"
                        class="text-gray-400 hover:text-gray-600"
                    >
                        <i class="fas fa-times"></i>
                    </button>
                </div>

                <!-- Account Info -->
                <div class="bg-gray-50 rounded-lg p-4 mb-4">
                    <div class="flex items-center justify-between">
                        <div>
                            <h4 class="font-medium text-gray-900">{{ account.customer_name }}</h4>
                            <p class="text-sm text-gray-600">{{ account.phone_number }}</p>
                        </div>
                        <div class="text-right">
                            <div class="text-lg font-bold text-orange-600">
                                {{ formatNumber(account.total_points) }} {{ $t('loyalty.points') }}
                            </div>
                            <div class="text-sm text-gray-500">{{ $t('loyalty.current_balance') }}</div>
                        </div>
                    </div>
                </div>

                <!-- Form -->
                <form @submit.prevent="submitAdjustment">
                    <div class="space-y-4">
                        <!-- Points Input -->
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">
                                {{ $t('loyalty.adjust_points.points_amount') }}
                            </label>
                            <input
                                type="number"
                                v-model="form.points"
                                :placeholder="$t('loyalty.adjust_points.points_placeholder')"
                                class="w-full rounded-md border-gray-300 shadow-sm focus:border-orange-500 focus:ring-orange-500"
                                required
                            >
                            <p class="mt-1 text-xs text-gray-500">
                                {{ $t('loyalty.adjust_points.points_help') }}
                            </p>
                        </div>

                        <!-- Reason Input -->
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">
                                {{ $t('loyalty.adjust_points.reason') }}
                            </label>
                            <textarea
                                v-model="form.reason"
                                rows="3"
                                :placeholder="$t('loyalty.adjust_points.reason_placeholder')"
                                class="w-full rounded-md border-gray-300 shadow-sm focus:border-orange-500 focus:ring-orange-500"
                                required
                            ></textarea>
                        </div>

                        <!-- Preview -->
                        <div v-if="form.points" class="bg-blue-50 rounded-lg p-3">
                            <div class="flex items-center justify-between text-sm">
                                <span class="text-gray-600">{{ $t('loyalty.adjust_points.new_balance') }}:</span>
                                <span class="font-medium" :class="newBalance >= 0 ? 'text-green-600' : 'text-red-600'">
                                    {{ formatNumber(newBalance) }} {{ $t('loyalty.points') }}
                                </span>
                            </div>
                            <div v-if="newBalance < 0" class="mt-2 text-xs text-red-600">
                                <i class="fas fa-exclamation-triangle mr-1"></i>
                                {{ $t('loyalty.adjust_points.negative_balance_warning') }}
                            </div>
                        </div>

                        <!-- Error Message -->
                        <div v-if="error" class="bg-red-50 border border-red-200 rounded-md p-3">
                            <div class="flex">
                                <i class="fas fa-exclamation-circle text-red-400 mr-2 mt-0.5"></i>
                                <div class="text-sm text-red-700">{{ error }}</div>
                            </div>
                        </div>
                    </div>

                    <!-- Actions -->
                    <div class="flex justify-end space-x-3 mt-6 pt-4 border-t">
                        <button
                            type="button"
                            @click="$emit('close')"
                            class="px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-orange-500"
                        >
                            {{ $t('common.cancel') }}
                        </button>
                        <button
                            type="submit"
                            :disabled="processing || !form.points || !form.reason"
                            class="px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-orange-600 hover:bg-orange-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-orange-500 disabled:opacity-50"
                        >
                            <i v-if="processing" class="fas fa-spinner fa-spin mr-2"></i>
                            {{ $t('loyalty.adjust_points.submit') }}
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</template>

<script setup>
import { ref, reactive, computed } from 'vue'
import { useForm } from '@inertiajs/vue3'
import { useI18n } from 'vue-i18n'

const { t } = useI18n()

const props = defineProps({
    account: {
        type: Object,
        required: true,
    },
})

const emit = defineEmits(['close', 'success'])

const form = reactive({
    points: '',
    reason: '',
})

const processing = ref(false)
const error = ref('')

const newBalance = computed(() => {
    const points = parseInt(form.points) || 0
    return props.account.total_points + points
})

const submitAdjustment = async () => {
    if (!form.points || !form.reason) {
        return
    }

    processing.value = true
    error.value = ''

    try {
        const response = await axios.post(
            route('loyalty.accounts.adjust', props.account.id),
            {
                points: parseInt(form.points),
                reason: form.reason,
            }
        )

        if (response.data.success !== false) {
            emit('success')
        }
    } catch (err) {
        if (err.response?.data?.message) {
            error.value = err.response.data.message
        } else if (err.response?.data?.errors) {
            const errors = Object.values(err.response.data.errors).flat()
            error.value = errors[0] || 'An error occurred'
        } else {
            error.value = 'Failed to adjust points. Please try again.'
        }
    } finally {
        processing.value = false
    }
}

const formatNumber = (number) => {
    return new Intl.NumberFormat().format(number || 0)
}
</script>
