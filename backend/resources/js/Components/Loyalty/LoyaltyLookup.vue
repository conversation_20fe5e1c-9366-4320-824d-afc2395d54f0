<template>
    <div class="bg-white border border-gray-200 rounded-lg p-4">
        <div class="flex items-center justify-between mb-4">
            <h3 class="text-lg font-medium text-gray-900">
                <i class="fas fa-star text-orange-500 mr-2"></i>
                {{ $t('loyalty.lookup.title') }}
            </h3>
            <div v-if="!loyaltyEnabled" class="text-sm text-gray-500">
                {{ $t('loyalty.lookup.disabled') }}
            </div>
        </div>

        <div v-if="loyaltyEnabled">
            <!-- Phone Number Input -->
            <div v-if="!loyaltyAccount" class="space-y-4">
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">
                        {{ $t('loyalty.lookup.phone_number') }}
                    </label>
                    <div class="flex space-x-2">
                        <input
                            type="tel"
                            v-model="phoneNumber"
                            :placeholder="$t('loyalty.lookup.phone_placeholder')"
                            class="flex-1 rounded-md border-gray-300 shadow-sm focus:border-orange-500 focus:ring-orange-500"
                            @keyup.enter="lookupAccount"
                        >
                        <button
                            @click="lookupAccount"
                            :disabled="!phoneNumber || lookingUp"
                            class="px-4 py-2 bg-orange-600 text-white rounded-md hover:bg-orange-700 focus:outline-none focus:ring-2 focus:ring-orange-500 disabled:opacity-50"
                        >
                            <i v-if="lookingUp" class="fas fa-spinner fa-spin"></i>
                            <i v-else class="fas fa-search"></i>
                        </button>
                    </div>
                </div>

                <!-- Error Message -->
                <div v-if="error" class="bg-red-50 border border-red-200 rounded-md p-3">
                    <div class="flex">
                        <i class="fas fa-exclamation-circle text-red-400 mr-2 mt-0.5"></i>
                        <div class="text-sm text-red-700">{{ error }}</div>
                    </div>
                </div>
            </div>

            <!-- Account Found -->
            <div v-if="loyaltyAccount" class="space-y-4">
                <!-- Account Info -->
                <div class="bg-gradient-to-r from-orange-50 to-yellow-50 border border-orange-200 rounded-lg p-4">
                    <div class="flex items-center justify-between">
                        <div>
                            <h4 class="font-medium text-gray-900">{{ loyaltyAccount.customer_name }}</h4>
                            <p class="text-sm text-gray-600">{{ loyaltyAccount.phone_number }}</p>
                            <div class="flex items-center mt-1">
                                <span
                                    class="inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium"
                                    :style="{ backgroundColor: loyaltyAccount.tier_color + '20', color: loyaltyAccount.tier_color }"
                                >
                                    {{ $t(`loyalty.tiers.${loyaltyAccount.tier}`) }}
                                </span>
                            </div>
                        </div>
                        <div class="text-right">
                            <div class="text-2xl font-bold text-orange-600">
                                {{ formatNumber(loyaltyAccount.total_points) }}
                            </div>
                            <div class="text-sm text-gray-500">{{ $t('loyalty.available_points') }}</div>
                        </div>
                    </div>
                </div>

                <!-- Points Redemption -->
                <div v-if="orderAmount > 0" class="space-y-3">
                    <div class="flex items-center justify-between">
                        <label class="text-sm font-medium text-gray-700">
                            {{ $t('loyalty.redemption.points_to_use') }}
                        </label>
                        <button
                            @click="clearRedemption"
                            class="text-xs text-gray-500 hover:text-gray-700"
                        >
                            {{ $t('common.clear') }}
                        </button>
                    </div>

                    <div class="flex space-x-2">
                        <input
                            type="number"
                            v-model="pointsToUse"
                            :max="loyaltyAccount.total_points"
                            min="0"
                            step="1"
                            :placeholder="$t('loyalty.redemption.points_placeholder')"
                            class="flex-1 rounded-md border-gray-300 shadow-sm focus:border-orange-500 focus:ring-orange-500"
                            @input="calculateDiscount"
                        >
                        <button
                            @click="useMaxPoints"
                            class="px-3 py-2 text-sm bg-gray-100 text-gray-700 rounded-md hover:bg-gray-200"
                        >
                            {{ $t('loyalty.redemption.use_max') }}
                        </button>
                    </div>

                    <!-- Discount Preview -->
                    <div v-if="discountInfo && pointsToUse > 0" class="bg-green-50 border border-green-200 rounded-md p-3">
                        <div class="flex items-center justify-between text-sm">
                            <span class="text-gray-600">{{ $t('loyalty.redemption.discount_amount') }}:</span>
                            <span class="font-medium text-green-600">
                                -${{ formatCurrency(discountInfo.discount_amount) }}
                            </span>
                        </div>
                        <div class="flex items-center justify-between text-sm mt-1">
                            <span class="text-gray-600">{{ $t('loyalty.redemption.remaining_points') }}:</span>
                            <span class="font-medium text-gray-900">
                                {{ formatNumber(loyaltyAccount.total_points - pointsToUse) }}
                            </span>
                        </div>
                        <div v-if="discountInfo.max_discount < discountInfo.discount_amount" class="mt-2 text-xs text-orange-600">
                            <i class="fas fa-info-circle mr-1"></i>
                            {{ $t('loyalty.redemption.max_discount_notice', { max: formatCurrency(discountInfo.max_discount) }) }}
                        </div>
                    </div>

                    <!-- Quick Point Buttons -->
                    <div class="flex flex-wrap gap-2">
                        <button
                            v-for="amount in quickAmounts"
                            :key="amount"
                            @click="setPointsToUse(amount)"
                            :disabled="amount > loyaltyAccount.total_points"
                            class="px-3 py-1 text-xs bg-orange-100 text-orange-700 rounded-full hover:bg-orange-200 disabled:opacity-50 disabled:cursor-not-allowed"
                        >
                            {{ amount }} {{ $t('loyalty.points') }}
                        </button>
                    </div>
                </div>

                <!-- Actions -->
                <div class="flex justify-between items-center pt-3 border-t">
                    <button
                        @click="clearAccount"
                        class="text-sm text-gray-500 hover:text-gray-700"
                    >
                        <i class="fas fa-times mr-1"></i>
                        {{ $t('loyalty.lookup.clear_account') }}
                    </button>
                    
                    <button
                        v-if="pointsToUse > 0"
                        @click="applyRedemption"
                        :disabled="applying"
                        class="px-4 py-2 bg-green-600 text-white text-sm rounded-md hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-green-500 disabled:opacity-50"
                    >
                        <i v-if="applying" class="fas fa-spinner fa-spin mr-2"></i>
                        {{ $t('loyalty.redemption.apply') }}
                    </button>
                </div>
            </div>
        </div>
    </div>
</template>

<script setup>
import { ref, computed, watch } from 'vue'
import { useI18n } from 'vue-i18n'
import axios from 'axios'

const { t } = useI18n()

const props = defineProps({
    orderAmount: {
        type: Number,
        default: 0,
    },
    loyaltyEnabled: {
        type: Boolean,
        default: false,
    },
    initialPhoneNumber: {
        type: String,
        default: '',
    },
})

const emit = defineEmits(['account-found', 'redemption-applied', 'account-cleared'])

const phoneNumber = ref(props.initialPhoneNumber)
const loyaltyAccount = ref(null)
const pointsToUse = ref(0)
const discountInfo = ref(null)
const lookingUp = ref(false)
const applying = ref(false)
const error = ref('')

const quickAmounts = computed(() => {
    if (!loyaltyAccount.value) return []
    
    const maxPoints = loyaltyAccount.value.total_points
    const amounts = [50, 100, 200, 500, 1000]
    
    return amounts.filter(amount => amount <= maxPoints)
})

watch(() => props.initialPhoneNumber, (newPhone) => {
    if (newPhone && newPhone !== phoneNumber.value) {
        phoneNumber.value = newPhone
        lookupAccount()
    }
})

const lookupAccount = async () => {
    if (!phoneNumber.value.trim()) {
        error.value = t('loyalty.lookup.phone_required')
        return
    }

    lookingUp.value = true
    error.value = ''

    try {
        const response = await axios.post('/api/loyalty/lookup', {
            phone_number: phoneNumber.value.trim(),
        })

        if (response.data.found) {
            loyaltyAccount.value = response.data.account
            emit('account-found', response.data.account)
        } else {
            error.value = t('loyalty.lookup.not_found')
            loyaltyAccount.value = null
        }
    } catch (err) {
        error.value = err.response?.data?.message || t('loyalty.lookup.error')
        loyaltyAccount.value = null
    } finally {
        lookingUp.value = false
    }
}

const calculateDiscount = async () => {
    if (!loyaltyAccount.value || !pointsToUse.value || pointsToUse.value <= 0 || props.orderAmount <= 0) {
        discountInfo.value = null
        return
    }

    try {
        const response = await axios.post('/api/loyalty/calculate-discount', {
            phone_number: loyaltyAccount.value.phone_number,
            points_to_use: parseInt(pointsToUse.value),
            order_amount: props.orderAmount,
        })

        if (response.data.success) {
            discountInfo.value = response.data.discount_info
        }
    } catch (err) {
        console.error('Failed to calculate discount:', err)
        discountInfo.value = null
    }
}

const useMaxPoints = () => {
    if (!loyaltyAccount.value || props.orderAmount <= 0) return
    
    // Calculate maximum usable points based on order amount and max discount percentage
    const maxDiscountAmount = props.orderAmount * 0.2 // Assuming 20% max discount
    const maxUsablePoints = Math.min(
        loyaltyAccount.value.total_points,
        Math.floor(maxDiscountAmount * 100) // Assuming 100 points = $1
    )
    
    pointsToUse.value = maxUsablePoints
    calculateDiscount()
}

const setPointsToUse = (amount) => {
    pointsToUse.value = Math.min(amount, loyaltyAccount.value.total_points)
    calculateDiscount()
}

const clearRedemption = () => {
    pointsToUse.value = 0
    discountInfo.value = null
}

const clearAccount = () => {
    loyaltyAccount.value = null
    phoneNumber.value = ''
    pointsToUse.value = 0
    discountInfo.value = null
    error.value = ''
    emit('account-cleared')
}

const applyRedemption = () => {
    if (!discountInfo.value || pointsToUse.value <= 0) return
    
    applying.value = true
    
    emit('redemption-applied', {
        account: loyaltyAccount.value,
        points_to_redeem: pointsToUse.value,
        discount_amount: discountInfo.value.discount_amount,
    })
    
    // Reset applying state after a short delay
    setTimeout(() => {
        applying.value = false
    }, 1000)
}

const formatNumber = (number) => {
    return new Intl.NumberFormat().format(number || 0)
}

const formatCurrency = (amount) => {
    return new Intl.NumberFormat('en-US', {
        minimumFractionDigits: 2,
        maximumFractionDigits: 2,
    }).format(amount || 0)
}

// Watch for order amount changes to recalculate discount
watch(() => props.orderAmount, () => {
    if (pointsToUse.value > 0) {
        calculateDiscount()
    }
})
</script>
