<script setup>
import { ref, computed, onMounted } from 'vue';
import { router, usePage } from '@inertiajs/vue3';
import { useI18n } from 'vue-i18n';
import Dropdown from '@/Components/Dropdown.vue';
import DropdownLink from '@/Components/DropdownLink.vue';
import { changeLocale, getCurrentLocale, getAvailableLocales } from '@/i18n';

// Use Vue i18n
const { t, locale } = useI18n();

// Get page props
const page = usePage();

// Current locale from i18n
const currentLocale = ref(getCurrentLocale());

// Available languages
const availableLanguages = getAvailableLocales();

// Switch language
const switchLanguage = async (newLocale) => {
    try {
        console.log('Switching language to:', newLocale);

        // Change locale using our i18n helper
        changeLocale(newLocale);
        currentLocale.value = newLocale;

        // Update Vue i18n locale
        locale.value = newLocale;

        // Update user preference in database if user is authenticated
        if (page.props.auth?.user) {
            try {
                await router.put(route('user.language.update'), {
                    language: newLocale
                }, {
                    preserveState: true,
                    preserveScroll: true,
                    onSuccess: (page) => {
                        console.log('Language preference updated successfully');
                        // The backend now returns a proper Inertia response
                        if (page.props.flash?.success) {
                            console.log('Success message:', page.props.flash.success);
                        }
                    },
                    onError: (errors) => {
                        console.error('Failed to update language preference:', errors);
                        // Revert the language change if backend update failed
                        const previousLocale = currentLocale.value === 'en' ? 'bn' : 'en';
                        changeLocale(previousLocale);
                        currentLocale.value = previousLocale;
                        locale.value = previousLocale;
                    }
                });
            } catch (error) {
                console.error('Error updating language preference:', error);
                // Revert the language change if request failed
                const previousLocale = currentLocale.value === 'en' ? 'bn' : 'en';
                changeLocale(previousLocale);
                currentLocale.value = previousLocale;
                locale.value = previousLocale;
            }
        }

        console.log('Language switched successfully to:', newLocale);
    } catch (error) {
        console.error('Error switching language:', error);
    }
};

// Initialize
onMounted(() => {
    try {
        // Get preferred language from user, localStorage, or default
        const userLang = page.props.auth?.user?.preferred_language;
        const storedLang = localStorage.getItem('locale');
        const browserLang = navigator.language.split('-')[0];

        const preferredLang = userLang || storedLang || (availableLanguages.find(l => l.code === browserLang)?.code) || 'en';

        console.log('Initializing language switcher with:', {
            userLang,
            storedLang,
            browserLang,
            preferredLang,
            currentLocale: currentLocale.value
        });

        if (preferredLang !== currentLocale.value) {
            switchLanguage(preferredLang);
        }
    } catch (error) {
        console.error('Error initializing language switcher:', error);
    }
});

defineExpose({
    t,
    currentLocale,
    switchLanguage
});
</script>

<template>
    <Dropdown align="right" width="48">
        <template #trigger>
            <button 
                type="button" 
                class="inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-gray-500 dark:text-gray-400 bg-white dark:bg-gray-800 hover:text-gray-700 dark:hover:text-gray-300 focus:outline-none focus:bg-gray-50 dark:focus:bg-gray-700 active:bg-gray-50 dark:active:bg-gray-700 transition ease-in-out duration-150"
            >
                <span class="mr-2">
                    {{ availableLanguages.find(l => l.code === currentLocale.value)?.flag || '🌐' }}
                </span>
                {{ availableLanguages.find(l => l.code === currentLocale.value)?.name || 'Language' }}
                
                <svg class="ml-2 -mr-0.5 h-4 w-4" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" d="M19.5 8.25l-7.5 7.5-7.5-7.5" />
                </svg>
            </button>
        </template>

        <template #content>
            <div class="w-48">
                <div class="block px-4 py-2 text-xs text-gray-400">
                    {{ t('language.select_language', 'Select Language') }}
                </div>
                
                <template v-for="language in availableLanguages" :key="language.code">
                    <button
                        @click.prevent="switchLanguage(language.code)"
                        type="button"
                        class="w-full text-left block px-4 py-2 text-sm leading-5 text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-800 focus:outline-none focus:bg-gray-100 dark:focus:bg-gray-800 transition duration-150 ease-in-out"
                        :class="{ 'bg-gray-100 dark:bg-gray-800 font-medium': currentLocale === language.code }"
                    >
                        <div class="flex items-center">
                            <span class="mr-3 text-lg">{{ language.flag }}</span>
                            <span>{{ language.name }}</span>
                            <span v-if="language.native_name && language.native_name !== language.name" class="ml-1 text-gray-500 dark:text-gray-400">
                                ({{ language.native_name }})
                            </span>
                            <svg
                                v-if="currentLocale === language.code"
                                class="ml-auto h-4 w-4 text-green-500"
                                xmlns="http://www.w3.org/2000/svg"
                                fill="none"
                                viewBox="0 0 24 24"
                                stroke-width="1.5"
                                stroke="currentColor"
                            >
                                <path stroke-linecap="round" stroke-linejoin="round" d="M4.5 12.75l6 6 9-13.5" />
                            </svg>
                        </div>
                    </button>
                </template>
            </div>
        </template>
    </Dropdown>
</template>
