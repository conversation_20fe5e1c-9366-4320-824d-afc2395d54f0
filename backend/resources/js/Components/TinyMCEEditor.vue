<template>
    <div class="tinymce-wrapper">
        <label v-if="label" :for="editorId" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            {{ label }}
            <span v-if="required" class="text-red-500">*</span>
        </label>
        <div class="relative">
            <textarea
                :id="editorId"
                ref="editorRef"
                :name="name"
                :placeholder="placeholder"
                class="hidden"
                :value="modelValue"
            ></textarea>
            <div v-if="loading" class="absolute inset-0 bg-gray-50 dark:bg-gray-800 flex items-center justify-center rounded-md border border-gray-300 dark:border-gray-600">
                <div class="flex items-center space-x-2">
                    <div class="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600"></div>
                    <span class="text-sm text-gray-600 dark:text-gray-400">Loading editor...</span>
                </div>
            </div>
        </div>
        <p v-if="error" class="mt-1 text-sm text-red-600 dark:text-red-400">{{ error }}</p>
        <p v-if="help" class="mt-1 text-sm text-gray-500 dark:text-gray-400">{{ help }}</p>
    </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted, watch, nextTick } from 'vue'
import { usePage } from '@inertiajs/vue3'

const props = defineProps({
    modelValue: {
        type: String,
        default: ''
    },
    name: {
        type: String,
        default: 'content'
    },
    label: {
        type: String,
        default: ''
    },
    placeholder: {
        type: String,
        default: 'Start writing...'
    },
    height: {
        type: [String, Number],
        default: 400
    },
    required: {
        type: Boolean,
        default: false
    },
    error: {
        type: String,
        default: ''
    },
    help: {
        type: String,
        default: ''
    },
    config: {
        type: Object,
        default: () => ({})
    }
})

const emit = defineEmits(['update:modelValue', 'ready', 'change'])

const editorRef = ref(null)
const editorId = ref(`tinymce-${Math.random().toString(36).substr(2, 9)}`)
const loading = ref(true)
const editor = ref(null)
const page = usePage()

// Default TinyMCE configuration
const defaultConfig = {
    selector: `#${editorId.value}`,
    height: props.height,
    menubar: true,
    plugins: [
        'advlist', 'autolink', 'lists', 'link', 'image', 'charmap', 'preview',
        'anchor', 'searchreplace', 'visualblocks', 'code', 'fullscreen',
        'insertdatetime', 'media', 'table', 'help', 'wordcount', 'emoticons'
    ],
    toolbar: 'undo redo | blocks | bold italic forecolor | alignleft aligncenter alignright alignjustify | bullist numlist outdent indent | removeformat | help | fullscreen | code',
    content_style: 'body { font-family: -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif; font-size: 14px; }',
    skin: 'oxide',
    content_css: 'default',
    branding: false,
    promotion: false,
    license_key: 'gpl',
    setup: (ed) => {
        ed.on('init', () => {
            loading.value = false
            editor.value = ed
            emit('ready', ed)
        })
        
        ed.on('change keyup paste', () => {
            const content = ed.getContent()
            emit('update:modelValue', content)
            emit('change', content)
        })
    },
    // Image upload configuration
    images_upload_handler: (blobInfo, progress) => {
        return new Promise((resolve, reject) => {
            const formData = new FormData()
            formData.append('file', blobInfo.blob(), blobInfo.filename())
            
            // You can customize this endpoint
            fetch('/api/upload-image', {
                method: 'POST',
                body: formData,
                headers: {
                    'X-CSRF-TOKEN': page.props.csrf_token || document.querySelector('meta[name="csrf-token"]')?.getAttribute('content')
                }
            })
            .then(response => response.json())
            .then(result => {
                if (result.success) {
                    resolve(result.url)
                } else {
                    reject(result.message || 'Upload failed')
                }
            })
            .catch(error => {
                reject('Upload failed: ' + error.message)
            })
        })
    },
    // File picker for media library integration
    file_picker_callback: (callback, value, meta) => {
        // You can integrate with your media library here
        if (meta.filetype === 'image') {
            // Open media library modal
            // This is a placeholder - you can integrate with your existing media library
            console.log('Open media library for images')
        }
    }
}

// Load TinyMCE script
const loadTinyMCE = () => {
    return new Promise((resolve, reject) => {
        if (window.tinymce) {
            resolve()
            return
        }

        const script = document.createElement('script')
        script.src = '/tinymce/js/tinymce/tinymce.min.js'
        script.onload = resolve
        script.onerror = reject
        document.head.appendChild(script)
    })
}

// Initialize editor
const initEditor = async () => {
    try {
        await loadTinyMCE()
        
        const config = {
            ...defaultConfig,
            ...props.config,
            selector: `#${editorId.value}`
        }
        
        await window.tinymce.init(config)
        
        // Set initial content
        if (props.modelValue && editor.value) {
            editor.value.setContent(props.modelValue)
        }
    } catch (error) {
        console.error('Failed to initialize TinyMCE:', error)
        loading.value = false
    }
}

// Watch for content changes
watch(() => props.modelValue, (newValue) => {
    if (editor.value && editor.value.getContent() !== newValue) {
        editor.value.setContent(newValue || '')
    }
})

onMounted(async () => {
    await nextTick()
    await initEditor()
})

onUnmounted(() => {
    if (editor.value) {
        editor.value.destroy()
        editor.value = null
    }
})
</script>

<style scoped>
.tinymce-wrapper {
    @apply w-full;
}

/* Dark mode support */
:deep(.tox-tinymce) {
    @apply border-gray-300 dark:border-gray-600 rounded-md;
}

:deep(.tox-editor-header) {
    @apply dark:bg-gray-800 dark:border-gray-600;
}

:deep(.tox-toolbar__primary) {
    @apply dark:bg-gray-800;
}

:deep(.tox-tbtn) {
    @apply dark:text-gray-300 dark:hover:bg-gray-700;
}

:deep(.tox-edit-area__iframe) {
    @apply dark:bg-gray-900;
}
</style>
