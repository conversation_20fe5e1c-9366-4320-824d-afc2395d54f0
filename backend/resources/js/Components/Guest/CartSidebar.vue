<template>
    <!-- Cart Sidebar Overlay -->
    <div
        v-if="isCartOpen"
        class="fixed inset-0 bg-black bg-opacity-50 z-50"
        @click="closeCart"
    ></div>
    
    <!-- Cart Sidebar -->
    <div
        :class="[
            'fixed top-0 right-0 h-full w-full max-w-md bg-white shadow-xl z-50 transform transition-transform duration-300 ease-in-out',
            isCartOpen ? 'translate-x-0' : 'translate-x-full'
        ]"
    >
        <!-- Header -->
        <div class="flex items-center justify-between p-4 border-b">
            <h2 class="text-lg font-semibold">Your Cart ({{ cartCount }})</h2>
            <button
                @click="closeCart"
                class="p-2 hover:bg-gray-100 rounded-full transition-colors"
            >
                <i class="fas fa-times text-gray-600"></i>
            </button>
        </div>
        
        <!-- Cart Content -->
        <div class="flex flex-col h-full">
            <!-- Empty Cart -->
            <div v-if="cartItems.length === 0" class="flex-1 flex items-center justify-center p-8">
                <div class="text-center">
                    <i class="fas fa-shopping-cart text-6xl text-gray-300 mb-4"></i>
                    <h3 class="text-lg font-semibold text-gray-600 mb-2">Your cart is empty</h3>
                    <p class="text-gray-500 mb-4">Add some delicious items to get started!</p>
                    <Link
                        :href="route('guest.menu')"
                        @click="closeCart"
                        class="bg-orange-600 text-white px-6 py-2 rounded-lg hover:bg-orange-700 transition-colors"
                    >
                        Browse Menu
                    </Link>
                </div>
            </div>
            
            <!-- Cart Items -->
            <div v-else class="flex-1 overflow-y-auto p-4 space-y-4">
                <div
                    v-for="item in cartItems"
                    :key="item.id"
                    class="bg-gray-50 rounded-lg p-4"
                >
                    <div class="flex items-start space-x-3">
                        <!-- Item Image -->
                        <div class="w-16 h-16 bg-gray-200 rounded-lg overflow-hidden flex-shrink-0">
                            <img
                                v-if="item.image"
                                :src="item.image"
                                :alt="item.name"
                                class="w-full h-full object-cover"
                            />
                            <div v-else class="w-full h-full flex items-center justify-center text-gray-400">
                                <i class="fas fa-utensils"></i>
                            </div>
                        </div>
                        
                        <!-- Item Details -->
                        <div class="flex-1 min-w-0">
                            <h4 class="font-semibold text-gray-800 truncate">{{ item.name }}</h4>
                            <p class="text-sm text-gray-600">${{ (item.basePrice || item.totalPrice / item.quantity).toFixed(2) }} each</p>
                            
                            <!-- Customizations -->
                            <div v-if="hasCustomizations(item)" class="mt-2 text-xs text-gray-500">
                                <div v-if="Object.keys(item.variations || {}).length > 0">
                                    <span class="font-medium">Options:</span> {{ formatVariations(item.variations) }}
                                </div>
                                <div v-if="(item.addons || []).length > 0">
                                    <span class="font-medium">Add-ons:</span> {{ formatAddons(item.addons) }}
                                </div>
                                <div v-if="item.specialInstructions">
                                    <span class="font-medium">Note:</span> {{ item.specialInstructions }}
                                </div>
                            </div>
                            
                            <!-- Quantity Controls -->
                            <div class="flex items-center justify-between mt-3">
                                <div class="flex items-center border border-gray-300 rounded">
                                    <button
                                        @click="updateItemQuantity(item.id, item.quantity - 1)"
                                        class="px-2 py-1 text-gray-600 hover:text-gray-800"
                                    >
                                        <i class="fas fa-minus text-xs"></i>
                                    </button>
                                    <span class="px-3 py-1 border-x border-gray-300 text-sm">{{ item.quantity }}</span>
                                    <button
                                        @click="updateItemQuantity(item.id, item.quantity + 1)"
                                        class="px-2 py-1 text-gray-600 hover:text-gray-800"
                                    >
                                        <i class="fas fa-plus text-xs"></i>
                                    </button>
                                </div>
                                
                                <div class="text-right">
                                    <div class="font-semibold text-orange-600">${{ item.totalPrice.toFixed(2) }}</div>
                                    <button
                                        @click="removeItemFromCart(item.id)"
                                        class="text-xs text-red-600 hover:text-red-800"
                                    >
                                        Remove
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Cart Footer -->
            <div v-if="cartItems.length > 0" class="border-t p-4 space-y-4">
                <!-- Totals -->
                <div class="space-y-2">
                    <div class="flex justify-between text-sm">
                        <span>Subtotal:</span>
                        <span>${{ cartSubtotal.toFixed(2) }}</span>
                    </div>
                    <div v-if="cartExtrasTotal > 0" class="flex justify-between text-sm">
                        <span>Extras:</span>
                        <span>${{ cartExtrasTotal.toFixed(2) }}</span>
                    </div>
                    <div class="flex justify-between font-semibold text-lg border-t pt-2">
                        <span>Total:</span>
                        <span class="text-orange-600">${{ cartTotal.toFixed(2) }}</span>
                    </div>
                </div>
                
                <!-- Action Buttons -->
                <div class="space-y-2">
                    <button
                        @click="proceedToCheckout"
                        class="w-full bg-orange-600 text-white py-3 rounded-lg font-semibold hover:bg-orange-700 transition-colors"
                    >
                        Proceed to Checkout
                    </button>
                    <button
                        @click="clearCart"
                        class="w-full border border-gray-300 text-gray-600 py-2 rounded-lg hover:bg-gray-50 transition-colors"
                    >
                        Clear Cart
                    </button>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Checkout Modal -->
    <CheckoutModal 
        v-if="showCheckout"
        :cart-data="prepareOrderData()"
        @close="showCheckout = false"
        @order-placed="handleOrderPlaced"
    />
</template>

<script setup>
import { ref } from 'vue'
import { Link } from '@inertiajs/vue3'
import { useCart } from '@/Composables/useCart'
import CheckoutModal from '@/Components/Guest/CheckoutModal.vue'

// Cart functionality
const {
    cartItems,
    cartCount,
    cartTotal,
    cartSubtotal,
    cartExtrasTotal,
    isCartOpen,
    closeCart,
    updateItemQuantity,
    removeItemFromCart,
    clearCart,
    prepareOrderData
} = useCart()

// Component state
const showCheckout = ref(false)

// Methods
const hasCustomizations = (item) => {
    return Object.keys(item.variations || {}).length > 0 ||
           (item.addons || []).length > 0 ||
           (item.specialInstructions || '').trim().length > 0
}

const formatVariations = (variations) => {
    // This would need to be enhanced with actual variation names
    // For now, just show the count
    const count = Object.keys(variations || {}).length
    return count > 0 ? `${count} selected` : ''
}

const formatAddons = (addons) => {
    // This would need to be enhanced with actual addon names
    // For now, just show the count
    const count = (addons || []).length
    return count > 0 ? `${count} selected` : ''
}

const proceedToCheckout = () => {
    showCheckout.value = true
}

const handleOrderPlaced = (order) => {
    showCheckout.value = false
    closeCart()
    clearCart()
    
    // Redirect to order tracking or show success message
    alert(`Order placed successfully! Order number: ${order.order_number}`)
}
</script>
