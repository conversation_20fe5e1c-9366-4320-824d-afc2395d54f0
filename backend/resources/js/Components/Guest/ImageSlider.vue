<template>
    <div class="relative">
        <!-- Main Image Display -->
        <div class="relative aspect-square bg-gray-200 rounded-lg overflow-hidden group">
            <img
                v-if="currentImage"
                :src="currentImage.url"
                :alt="currentImage.alt"
                class="w-full h-full object-cover transition-transform duration-300 group-hover:scale-105"
                @error="handleImageError"
                @click="openZoom"
            />
            <div v-else class="flex items-center justify-center h-full text-gray-400">
                <i class="fas fa-utensils text-6xl"></i>
            </div>
            
            <!-- Zoom Icon -->
            <div 
                v-if="currentImage && !currentImage.isPlaceholder"
                class="absolute top-4 right-4 bg-black bg-opacity-50 text-white p-2 rounded-full opacity-0 group-hover:opacity-100 transition-opacity cursor-pointer"
                @click="openZoom"
            >
                <i class="fas fa-search-plus"></i>
            </div>
            
            <!-- Navigation Arrows -->
            <div v-if="images.length > 1" class="absolute inset-y-0 left-0 flex items-center">
                <button
                    @click="previousImage"
                    class="ml-2 bg-black bg-opacity-50 text-white p-2 rounded-full opacity-0 group-hover:opacity-100 transition-opacity hover:bg-opacity-75"
                >
                    <i class="fas fa-chevron-left"></i>
                </button>
            </div>
            <div v-if="images.length > 1" class="absolute inset-y-0 right-0 flex items-center">
                <button
                    @click="nextImage"
                    class="mr-2 bg-black bg-opacity-50 text-white p-2 rounded-full opacity-0 group-hover:opacity-100 transition-opacity hover:bg-opacity-75"
                >
                    <i class="fas fa-chevron-right"></i>
                </button>
            </div>
            
            <!-- Image Counter -->
            <div v-if="images.length > 1" class="absolute bottom-4 left-4 bg-black bg-opacity-50 text-white px-3 py-1 rounded-full text-sm">
                {{ currentIndex + 1 }} / {{ images.length }}
            </div>
        </div>
        
        <!-- Thumbnail Navigation -->
        <div v-if="images.length > 1" class="mt-4 flex space-x-2 overflow-x-auto pb-2">
            <button
                v-for="(image, index) in images"
                :key="index"
                @click="setCurrentImage(index)"
                class="flex-shrink-0 w-16 h-16 rounded-lg overflow-hidden border-2 transition-all"
                :class="[
                    currentIndex === index 
                        ? 'border-orange-600 ring-2 ring-orange-200' 
                        : 'border-gray-300 hover:border-gray-400'
                ]"
            >
                <img
                    :src="image.url"
                    :alt="image.alt"
                    class="w-full h-full object-cover"
                    @error="handleImageError"
                />
            </button>
        </div>
        
        <!-- Zoom Modal -->
        <div
            v-if="showZoom"
            class="fixed inset-0 bg-black bg-opacity-90 z-50 flex items-center justify-center p-4"
            @click="closeZoom"
        >
            <div class="relative max-w-4xl max-h-full">
                <img
                    :src="currentImage.url"
                    :alt="currentImage.alt"
                    class="max-w-full max-h-full object-contain"
                    @error="handleImageError"
                />
                <button
                    @click="closeZoom"
                    class="absolute top-4 right-4 bg-black bg-opacity-50 text-white p-2 rounded-full hover:bg-opacity-75"
                >
                    <i class="fas fa-times"></i>
                </button>
                
                <!-- Zoom Navigation -->
                <div v-if="images.length > 1" class="absolute inset-y-0 left-0 flex items-center">
                    <button
                        @click.stop="previousImage"
                        class="ml-4 bg-black bg-opacity-50 text-white p-3 rounded-full hover:bg-opacity-75"
                    >
                        <i class="fas fa-chevron-left"></i>
                    </button>
                </div>
                <div v-if="images.length > 1" class="absolute inset-y-0 right-0 flex items-center">
                    <button
                        @click.stop="nextImage"
                        class="mr-4 bg-black bg-opacity-50 text-white p-3 rounded-full hover:bg-opacity-75"
                    >
                        <i class="fas fa-chevron-right"></i>
                    </button>
                </div>
            </div>
        </div>
    </div>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted } from 'vue'

const props = defineProps({
    images: {
        type: Array,
        default: () => []
    },
    alt: {
        type: String,
        default: ''
    }
})

const emit = defineEmits(['image-error'])

// Component state
const currentIndex = ref(0)
const showZoom = ref(false)

// Computed properties
const currentImage = computed(() => {
    return props.images[currentIndex.value] || null
})

// Methods
const setCurrentImage = (index) => {
    if (index >= 0 && index < props.images.length) {
        currentIndex.value = index
    }
}

const nextImage = () => {
    const nextIndex = (currentIndex.value + 1) % props.images.length
    setCurrentImage(nextIndex)
}

const previousImage = () => {
    const prevIndex = currentIndex.value === 0 
        ? props.images.length - 1 
        : currentIndex.value - 1
    setCurrentImage(prevIndex)
}

const openZoom = () => {
    if (currentImage.value && !currentImage.value.isPlaceholder) {
        showZoom.value = true
        document.body.style.overflow = 'hidden'
    }
}

const closeZoom = () => {
    showZoom.value = false
    document.body.style.overflow = ''
}

const handleImageError = (event) => {
    emit('image-error', event)
}

const handleKeydown = (event) => {
    if (!showZoom.value) return
    
    switch (event.key) {
        case 'Escape':
            closeZoom()
            break
        case 'ArrowLeft':
            previousImage()
            break
        case 'ArrowRight':
            nextImage()
            break
    }
}

// Touch handling for mobile
let touchStartX = 0
let touchEndX = 0

const handleTouchStart = (event) => {
    touchStartX = event.changedTouches[0].screenX
}

const handleTouchEnd = (event) => {
    touchEndX = event.changedTouches[0].screenX
    handleSwipe()
}

const handleSwipe = () => {
    const swipeThreshold = 50
    const diff = touchStartX - touchEndX
    
    if (Math.abs(diff) > swipeThreshold) {
        if (diff > 0) {
            nextImage()
        } else {
            previousImage()
        }
    }
}

// Lifecycle
onMounted(() => {
    document.addEventListener('keydown', handleKeydown)
    document.addEventListener('touchstart', handleTouchStart)
    document.addEventListener('touchend', handleTouchEnd)
})

onUnmounted(() => {
    document.removeEventListener('keydown', handleKeydown)
    document.removeEventListener('touchstart', handleTouchStart)
    document.removeEventListener('touchend', handleTouchEnd)
    document.body.style.overflow = ''
})
</script>

<style scoped>
/* Custom scrollbar for thumbnails */
.overflow-x-auto::-webkit-scrollbar {
    height: 4px;
}

.overflow-x-auto::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 2px;
}

.overflow-x-auto::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 2px;
}

.overflow-x-auto::-webkit-scrollbar-thumb:hover {
    background: #a1a1a1;
}
</style>
