<template>
    <div class="space-y-6">
        <!-- Variations Selection -->
        <div v-if="menuItem.variations && menuItem.variations.length > 0" class="space-y-4">
            <h3 class="text-lg font-semibold">Customize Your Order</h3>
            
            <div
                v-for="variation in menuItem.variations"
                :key="variation.id"
                class="border border-gray-200 rounded-lg p-4"
            >
                <h4 class="font-semibold mb-3 flex items-center">
                    {{ variation.name }}
                    <span v-if="variation.is_required" class="ml-2 text-red-500 text-sm">*Required</span>
                </h4>
                
                <div class="space-y-2">
                    <label
                        v-for="option in variation.options"
                        :key="option.id"
                        class="flex items-center justify-between p-3 border border-gray-200 rounded-lg cursor-pointer hover:bg-gray-50 transition-colors"
                        :class="{ 'border-orange-500 bg-orange-50': selectedVariations[variation.id] === option.id }"
                    >
                        <div class="flex items-center">
                            <input
                                :type="variation.selection_type === 'multiple' ? 'checkbox' : 'radio'"
                                :name="`variation_${variation.id}`"
                                :value="option.id"
                                :checked="isVariationSelected(variation.id, option.id)"
                                @change="handleVariationChange(variation, option, $event)"
                                class="mr-3 text-orange-600 focus:ring-orange-500"
                            />
                            <div>
                                <div class="font-medium">{{ option.name }}</div>
                                <div v-if="option.description" class="text-sm text-gray-600">{{ option.description }}</div>
                            </div>
                        </div>
                        <span class="text-sm font-semibold" :class="option.price > 0 ? 'text-orange-600' : 'text-green-600'">
                            {{ option.price > 0 ? `+$${option.price.toFixed(2)}` : 'Free' }}
                        </span>
                    </label>
                </div>
            </div>
        </div>
        
        <!-- Add-ons Selection -->
        <div v-if="menuItem.addons && menuItem.addons.length > 0" class="space-y-4">
            <h3 class="text-lg font-semibold">Add Extra Items</h3>
            
            <div class="grid grid-cols-1 sm:grid-cols-2 gap-3">
                <label
                    v-for="addon in menuItem.addons"
                    :key="addon.id"
                    class="flex items-center justify-between p-3 border border-gray-200 rounded-lg cursor-pointer hover:bg-gray-50 transition-colors"
                    :class="{ 'border-orange-500 bg-orange-50': selectedAddons.includes(addon.id) }"
                >
                    <div class="flex items-center">
                        <input
                            type="checkbox"
                            :value="addon.id"
                            :checked="selectedAddons.includes(addon.id)"
                            @change="handleAddonChange(addon, $event)"
                            class="mr-3 text-orange-600 focus:ring-orange-500"
                        />
                        <div>
                            <div class="font-medium">{{ addon.name }}</div>
                            <div v-if="addon.description" class="text-sm text-gray-600">{{ addon.description }}</div>
                        </div>
                    </div>
                    <span class="text-sm font-semibold text-orange-600">+${{ addon.price.toFixed(2) }}</span>
                </label>
            </div>
        </div>
        
        <!-- Special Instructions -->
        <div>
            <label for="special_instructions" class="block text-sm font-medium text-gray-700 mb-2">
                Special Instructions (Optional)
            </label>
            <textarea
                id="special_instructions"
                v-model="specialInstructions"
                rows="3"
                class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-orange-500 focus:border-orange-500"
                placeholder="Any special requests or dietary requirements..."
            ></textarea>
        </div>
        
        <!-- Quantity and Price -->
        <div class="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
            <div class="flex items-center space-x-4">
                <label class="text-sm font-medium text-gray-700">Quantity:</label>
                <div class="flex items-center border border-gray-300 rounded-lg">
                    <button
                        type="button"
                        @click="decreaseQuantity"
                        :disabled="quantity <= 1"
                        class="px-3 py-2 text-gray-600 hover:text-gray-800 disabled:opacity-50 disabled:cursor-not-allowed"
                    >
                        <i class="fas fa-minus"></i>
                    </button>
                    <span class="px-4 py-2 border-x border-gray-300 min-w-[3rem] text-center">{{ quantity }}</span>
                    <button
                        type="button"
                        @click="increaseQuantity"
                        class="px-3 py-2 text-gray-600 hover:text-gray-800"
                    >
                        <i class="fas fa-plus"></i>
                    </button>
                </div>
            </div>
            
            <div class="text-right">
                <div class="text-sm text-gray-600">Total Price</div>
                <div class="text-2xl font-bold text-orange-600">${{ totalPrice.toFixed(2) }}</div>
            </div>
        </div>
        
        <!-- Add to Cart Button -->
        <button
            @click="handleAddToCart"
            :disabled="!canAddToCart"
            class="w-full bg-orange-600 text-white py-4 px-6 rounded-lg font-semibold text-lg hover:bg-orange-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
        >
            <i class="fas fa-shopping-cart mr-2"></i>
            Add to Cart - ${{ totalPrice.toFixed(2) }}
        </button>
        
        <!-- Validation Messages -->
        <div v-if="validationErrors.length > 0" class="bg-red-50 border border-red-200 rounded-lg p-4">
            <h4 class="text-red-800 font-semibold mb-2">Please complete the following:</h4>
            <ul class="text-red-700 text-sm space-y-1">
                <li v-for="error in validationErrors" :key="error" class="flex items-center">
                    <i class="fas fa-exclamation-circle mr-2"></i>
                    {{ error }}
                </li>
            </ul>
        </div>
    </div>
</template>

<script setup>
import { ref, computed, watch } from 'vue'

const props = defineProps({
    menuItem: {
        type: Object,
        required: true
    },
    selectedVariations: {
        type: Object,
        default: () => ({})
    },
    selectedAddons: {
        type: Array,
        default: () => []
    },
    quantity: {
        type: Number,
        default: 1
    }
})

const emit = defineEmits([
    'update-variations',
    'update-addons', 
    'update-quantity',
    'add-to-cart'
])

// Component state
const specialInstructions = ref('')
const localVariations = ref({ ...props.selectedVariations })
const localAddons = ref([...props.selectedAddons])
const localQuantity = ref(props.quantity)

// Computed properties
const totalPrice = computed(() => {
    let price = parseFloat(props.menuItem.effective_price || 0)
    
    // Add variation prices
    Object.entries(localVariations.value).forEach(([variationId, optionId]) => {
        const variation = props.menuItem.variations?.find(v => v.id == variationId)
        const option = variation?.options?.find(o => o.id == optionId)
        if (option) {
            price += parseFloat(option.price || 0)
        }
    })
    
    // Add addon prices
    localAddons.value.forEach(addonId => {
        const addon = props.menuItem.addons?.find(a => a.id == addonId)
        if (addon) {
            price += parseFloat(addon.price || 0)
        }
    })
    
    return price * localQuantity.value
})

const validationErrors = computed(() => {
    const errors = []
    
    // Check required variations
    if (props.menuItem.variations) {
        props.menuItem.variations.forEach(variation => {
            if (variation.is_required && !localVariations.value[variation.id]) {
                errors.push(`Please select an option for ${variation.name}`)
            }
        })
    }
    
    return errors
})

const canAddToCart = computed(() => {
    return validationErrors.value.length === 0 && localQuantity.value > 0
})

// Methods
const isVariationSelected = (variationId, optionId) => {
    return localVariations.value[variationId] === optionId
}

const handleVariationChange = (variation, option, event) => {
    if (variation.selection_type === 'multiple') {
        // Handle multiple selection (checkboxes)
        if (event.target.checked) {
            if (!Array.isArray(localVariations.value[variation.id])) {
                localVariations.value[variation.id] = []
            }
            localVariations.value[variation.id].push(option.id)
        } else {
            const index = localVariations.value[variation.id]?.indexOf(option.id)
            if (index > -1) {
                localVariations.value[variation.id].splice(index, 1)
            }
        }
    } else {
        // Handle single selection (radio buttons)
        localVariations.value[variation.id] = option.id
    }
    
    emit('update-variations', localVariations.value)
}

const handleAddonChange = (addon, event) => {
    if (event.target.checked) {
        localAddons.value.push(addon.id)
    } else {
        const index = localAddons.value.indexOf(addon.id)
        if (index > -1) {
            localAddons.value.splice(index, 1)
        }
    }
    
    emit('update-addons', localAddons.value)
}

const increaseQuantity = () => {
    localQuantity.value++
    emit('update-quantity', localQuantity.value)
}

const decreaseQuantity = () => {
    if (localQuantity.value > 1) {
        localQuantity.value--
        emit('update-quantity', localQuantity.value)
    }
}

const handleAddToCart = () => {
    if (!canAddToCart.value) return
    
    const cartItem = {
        menuItemId: props.menuItem.id,
        name: props.menuItem.name,
        basePrice: parseFloat(props.menuItem.effective_price || 0),
        quantity: localQuantity.value,
        variations: { ...localVariations.value },
        addons: [...localAddons.value],
        specialInstructions: specialInstructions.value,
        totalPrice: totalPrice.value,
        image: props.menuItem.primary_image_url
    }
    
    emit('add-to-cart', cartItem)
}

// Watch for prop changes
watch(() => props.selectedVariations, (newVal) => {
    localVariations.value = { ...newVal }
}, { deep: true })

watch(() => props.selectedAddons, (newVal) => {
    localAddons.value = [...newVal]
})

watch(() => props.quantity, (newVal) => {
    localQuantity.value = newVal
})
</script>
