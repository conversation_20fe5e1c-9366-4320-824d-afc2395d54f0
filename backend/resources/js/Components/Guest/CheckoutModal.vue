<template>
    <!-- Modal Overlay -->
    <div class="fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center p-4">
        <div class="bg-white rounded-lg max-w-2xl w-full max-h-[90vh] overflow-y-auto">
            <!-- Header -->
            <div class="flex items-center justify-between p-6 border-b">
                <h2 class="text-xl font-semibold">Complete Your Order</h2>
                <button
                    @click="$emit('close')"
                    class="p-2 hover:bg-gray-100 rounded-full transition-colors"
                >
                    <i class="fas fa-times text-gray-600"></i>
                </button>
            </div>
            
            <!-- Form -->
            <form @submit.prevent="submitOrder" class="p-6 space-y-6">
                <!-- Customer Information -->
                <div class="space-y-4">
                    <h3 class="text-lg font-semibold">Contact Information</h3>
                    
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                            <label for="customer_name" class="block text-sm font-medium text-gray-700 mb-1">
                                Full Name *
                            </label>
                            <input
                                id="customer_name"
                                v-model="form.customer_name"
                                type="text"
                                required
                                class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-orange-500 focus:border-orange-500"
                                :class="{ 'border-red-500': errors.customer_name }"
                            />
                            <p v-if="errors.customer_name" class="text-red-500 text-sm mt-1">{{ errors.customer_name }}</p>
                        </div>
                        
                        <div>
                            <label for="customer_phone" class="block text-sm font-medium text-gray-700 mb-1">
                                Phone Number *
                            </label>
                            <input
                                id="customer_phone"
                                v-model="form.customer_phone"
                                type="tel"
                                required
                                class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-orange-500 focus:border-orange-500"
                                :class="{ 'border-red-500': errors.customer_phone }"
                            />
                            <p v-if="errors.customer_phone" class="text-red-500 text-sm mt-1">{{ errors.customer_phone }}</p>
                        </div>
                        
                        <div class="md:col-span-2">
                            <label for="customer_email" class="block text-sm font-medium text-gray-700 mb-1">
                                Email Address
                            </label>
                            <input
                                id="customer_email"
                                v-model="form.customer_email"
                                type="email"
                                class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-orange-500 focus:border-orange-500"
                                :class="{ 'border-red-500': errors.customer_email }"
                            />
                            <p v-if="errors.customer_email" class="text-red-500 text-sm mt-1">{{ errors.customer_email }}</p>
                        </div>
                    </div>
                </div>
                
                <!-- Delivery Information -->
                <div class="space-y-4">
                    <h3 class="text-lg font-semibold">Delivery Information</h3>
                    
                    <div class="space-y-4">
                        <div>
                            <label for="delivery_address" class="block text-sm font-medium text-gray-700 mb-1">
                                Street Address *
                            </label>
                            <textarea
                                id="delivery_address"
                                v-model="form.delivery_address"
                                rows="2"
                                required
                                class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-orange-500 focus:border-orange-500"
                                :class="{ 'border-red-500': errors.delivery_address }"
                                placeholder="Enter your full delivery address"
                            ></textarea>
                            <p v-if="errors.delivery_address" class="text-red-500 text-sm mt-1">{{ errors.delivery_address }}</p>
                        </div>
                        
                        <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                            <div>
                                <label for="delivery_city" class="block text-sm font-medium text-gray-700 mb-1">
                                    City *
                                </label>
                                <input
                                    id="delivery_city"
                                    v-model="form.delivery_city"
                                    type="text"
                                    required
                                    class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-orange-500 focus:border-orange-500"
                                    :class="{ 'border-red-500': errors.delivery_city }"
                                />
                                <p v-if="errors.delivery_city" class="text-red-500 text-sm mt-1">{{ errors.delivery_city }}</p>
                            </div>
                            
                            <div>
                                <label for="delivery_state" class="block text-sm font-medium text-gray-700 mb-1">
                                    State/Province
                                </label>
                                <input
                                    id="delivery_state"
                                    v-model="form.delivery_state"
                                    type="text"
                                    class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-orange-500 focus:border-orange-500"
                                />
                            </div>
                            
                            <div>
                                <label for="delivery_postal_code" class="block text-sm font-medium text-gray-700 mb-1">
                                    Postal Code *
                                </label>
                                <input
                                    id="delivery_postal_code"
                                    v-model="form.delivery_postal_code"
                                    type="text"
                                    required
                                    class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-orange-500 focus:border-orange-500"
                                    :class="{ 'border-red-500': errors.delivery_postal_code }"
                                />
                                <p v-if="errors.delivery_postal_code" class="text-red-500 text-sm mt-1">{{ errors.delivery_postal_code }}</p>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Order Notes -->
                <div>
                    <label for="order_notes" class="block text-sm font-medium text-gray-700 mb-1">
                        Special Instructions (Optional)
                    </label>
                    <textarea
                        id="order_notes"
                        v-model="form.order_notes"
                        rows="3"
                        class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-orange-500 focus:border-orange-500"
                        placeholder="Any special requests, delivery instructions, or dietary requirements..."
                    ></textarea>
                </div>
                
                <!-- Payment Method -->
                <div class="space-y-4">
                    <h3 class="text-lg font-semibold">Payment Method</h3>
                    
                    <div class="space-y-2">
                        <label class="flex items-center p-3 border border-gray-300 rounded-lg cursor-pointer hover:bg-gray-50">
                            <input
                                v-model="form.payment_method"
                                type="radio"
                                value="cash_on_delivery"
                                class="mr-3 text-orange-600 focus:ring-orange-500"
                            />
                            <div>
                                <div class="font-medium">Cash on Delivery</div>
                                <div class="text-sm text-gray-600">Pay when your order arrives</div>
                            </div>
                        </label>
                        
                        <label class="flex items-center p-3 border border-gray-300 rounded-lg cursor-pointer hover:bg-gray-50">
                            <input
                                v-model="form.payment_method"
                                type="radio"
                                value="card_on_delivery"
                                class="mr-3 text-orange-600 focus:ring-orange-500"
                            />
                            <div>
                                <div class="font-medium">Card on Delivery</div>
                                <div class="text-sm text-gray-600">Pay with card when your order arrives</div>
                            </div>
                        </label>
                    </div>
                </div>
                
                <!-- Order Summary -->
                <div class="bg-gray-50 rounded-lg p-4">
                    <h3 class="font-semibold mb-3">Order Summary</h3>
                    <div class="space-y-2 text-sm">
                        <div class="flex justify-between">
                            <span>Items ({{ cartData.item_count }}):</span>
                            <span>${{ cartData.subtotal.toFixed(2) }}</span>
                        </div>
                        <div class="flex justify-between">
                            <span>Delivery Fee:</span>
                            <span>${{ deliveryFee.toFixed(2) }}</span>
                        </div>
                        <div class="flex justify-between font-semibold text-lg border-t pt-2">
                            <span>Total:</span>
                            <span class="text-orange-600">${{ orderTotal.toFixed(2) }}</span>
                        </div>
                    </div>
                </div>
                
                <!-- Submit Button -->
                <div class="flex space-x-4">
                    <button
                        type="button"
                        @click="$emit('close')"
                        class="flex-1 border border-gray-300 text-gray-600 py-3 rounded-lg hover:bg-gray-50 transition-colors"
                    >
                        Cancel
                    </button>
                    <button
                        type="submit"
                        :disabled="isSubmitting"
                        class="flex-1 bg-orange-600 text-white py-3 rounded-lg font-semibold hover:bg-orange-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
                    >
                        <span v-if="isSubmitting">
                            <i class="fas fa-spinner fa-spin mr-2"></i>
                            Placing Order...
                        </span>
                        <span v-else>
                            Place Order - ${{ orderTotal.toFixed(2) }}
                        </span>
                    </button>
                </div>
            </form>
        </div>
    </div>
</template>

<script setup>
import { ref, computed } from 'vue'
import { router } from '@inertiajs/vue3'

const props = defineProps({
    cartData: {
        type: Object,
        required: true
    }
})

const emit = defineEmits(['close', 'order-placed'])

// Form data
const form = ref({
    customer_name: '',
    customer_phone: '',
    customer_email: '',
    delivery_address: '',
    delivery_city: '',
    delivery_state: '',
    delivery_postal_code: '',
    order_notes: '',
    payment_method: 'cash_on_delivery'
})

// Component state
const isSubmitting = ref(false)
const errors = ref({})

// Computed properties
const deliveryFee = computed(() => 5.00) // Fixed delivery fee for now
const orderTotal = computed(() => props.cartData.total + deliveryFee.value)

// Methods
const validateForm = () => {
    errors.value = {}
    
    if (!form.value.customer_name.trim()) {
        errors.value.customer_name = 'Name is required'
    }
    
    if (!form.value.customer_phone.trim()) {
        errors.value.customer_phone = 'Phone number is required'
    }
    
    if (form.value.customer_email && !isValidEmail(form.value.customer_email)) {
        errors.value.customer_email = 'Please enter a valid email address'
    }
    
    if (!form.value.delivery_address.trim()) {
        errors.value.delivery_address = 'Delivery address is required'
    }
    
    if (!form.value.delivery_city.trim()) {
        errors.value.delivery_city = 'City is required'
    }
    
    if (!form.value.delivery_postal_code.trim()) {
        errors.value.delivery_postal_code = 'Postal code is required'
    }
    
    return Object.keys(errors.value).length === 0
}

const isValidEmail = (email) => {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
    return emailRegex.test(email)
}

const submitOrder = async () => {
    if (!validateForm()) {
        return
    }
    
    isSubmitting.value = true
    
    try {
        const orderData = {
            ...form.value,
            items: props.cartData.items,
            subtotal: props.cartData.subtotal,
            delivery_fee: deliveryFee.value,
            total: orderTotal.value
        }
        
        router.post(route('guest.orders.store'), orderData, {
            onSuccess: (page) => {
                const order = page.props.order
                emit('order-placed', order)
            },
            onError: (errors) => {
                errors.value = errors
            },
            onFinish: () => {
                isSubmitting.value = false
            }
        })
    } catch (error) {
        console.error('Order submission error:', error)
        isSubmitting.value = false
    }
}
</script>
