<script setup>
import { ref, computed } from 'vue';
import MediaLibraryModal from './MediaLibraryModal.vue';

const props = defineProps({
    modelValue: {
        type: Object,
        default: null,
    },
    label: {
        type: String,
        default: 'Media',
    },
    placeholder: {
        type: String,
        default: 'Select media...',
    },
    required: {
        type: Boolean,
        default: false,
    },
    error: {
        type: String,
        default: null,
    },
});

const emit = defineEmits(['update:modelValue']);

// Reactive data
const showModal = ref(false);

// Computed
const hasSelection = computed(() => {
    return props.modelValue !== null && props.modelValue !== undefined;
});

const displayText = computed(() => {
    if (!hasSelection.value) {
        return props.placeholder;
    }

    return props.modelValue.name || props.modelValue.original_name || 'Selected file';
});

const previewImage = computed(() => {
    if (!hasSelection.value) return null;

    if (props.modelValue.mime_type?.startsWith('image/')) {
        return props.modelValue.thumbnail_url || props.modelValue.url;
    }

    return null;
});

// Methods
const openModal = () => {
    showModal.value = true;
};

const closeModal = () => {
    showModal.value = false;
};

const handleSelection = (media) => {
    emit('update:modelValue', media);
    closeModal();
};

const clearSelection = () => {
    emit('update:modelValue', null);
};

const formatFileSize = (bytes) => {
    if (!bytes) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
};
</script>

<template>
    <div class="media-picker">
        <!-- Label -->
        <label v-if="label" class="block text-sm font-medium text-gray-700 mb-2">
            {{ label }}
            <span v-if="required" class="text-red-500">*</span>
        </label>

        <!-- Picker Button -->
        <div
            @click.stop="openModal"
            :class="[
                'relative border-2 border-dashed rounded-lg p-4 cursor-pointer transition-colors hover:border-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500',
                error ? 'border-red-300' : 'border-gray-300',
                hasSelection ? 'bg-gray-50' : 'bg-white'
            ]"
        >
            <!-- Preview Area -->
            <div class="flex items-center space-x-4">
                <!-- Image Preview -->
                <div class="flex-shrink-0">
                    <div v-if="previewImage" class="w-16 h-16 rounded-lg overflow-hidden">
                        <img
                            :src="previewImage"
                            :alt="displayText"
                            class="w-full h-full object-cover"
                        >
                    </div>
                    <div v-else class="w-16 h-16 bg-gray-200 rounded-lg flex items-center justify-center">
                        <svg class="w-8 h-8 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
                        </svg>
                    </div>
                </div>

                <!-- Content -->
                <div class="flex-1 min-w-0">
                    <div class="text-sm font-medium text-gray-900">
                        {{ displayText }}
                    </div>
                    <div v-if="hasSelection" class="text-xs text-gray-500 mt-1">
                        {{ formatFileSize(props.modelValue.size) }}
                    </div>
                    <div v-else class="text-xs text-gray-500 mt-1">
                        Click to select an image from your media library
                    </div>
                </div>

                <!-- Actions -->
                <div class="flex-shrink-0 flex items-center space-x-2">
                    <button
                        type="button"
                        v-if="hasSelection"
                        @click.stop="clearSelection"
                        class="p-1 text-gray-400 hover:text-red-600 transition-colors"
                        title="Clear selection"
                    >
                        <svg class="w-5 h-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                        </svg>
                    </button>
                    <div class="text-gray-400">
                        <svg class="w-5 h-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                        </svg>
                    </div>
                </div>
            </div>
        </div>

        <!-- Error Message -->
        <div v-if="error" class="mt-1 text-sm text-red-600">
            {{ error }}
        </div>

        <!-- Help Text -->
        <div v-else class="mt-1 text-sm text-gray-500">
            Select an image from your media library
        </div>

        <!-- Media Library Modal -->
        <MediaLibraryModal
            :show="showModal"
            :onSelect="handleSelection"
            @close="closeModal"
        />
    </div>
</template>
