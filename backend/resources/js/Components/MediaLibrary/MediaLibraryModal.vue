<script setup>
import { ref, computed, onMounted, onUnmounted, watch } from 'vue';
import { router } from '@inertiajs/vue3';

const props = defineProps({
    show: {
        type: Boolean,
        default: false,
    },
    multiple: {
        type: Boolean,
        default: false,
    },
    maxSelection: {
        type: Number,
        default: 1,
    },
    onSelect: {
        type: Function,
        default: () => {}
    }
});

const emit = defineEmits(['close', 'select']);

// Reactive data
const search = ref('');
const items = ref([]); // Initialize as empty array to prevent undefined errors
const loading = ref(true); // Start with loading state
const searchTimeout = ref(null);
const selectedItem = ref(null);
const selectedItems = ref([]); // For multiple selection
const pagination = ref({
    current_page: 1,
    last_page: 1,
    per_page: 12
});

// Upload modal
const showUploadModal = ref(false);
const uploadFiles = ref([]);
const uploading = ref(false);
const isDragging = ref(false);

// Paste functionality
const pasteIndicator = ref(false);
const pasteTimeout = ref(null);

// Methods
const loadMedia = async () => {
    loading.value = true;
    try {
        console.log('Fetching media with params:', {
            search: search.value,
            page: pagination.value.current_page,
            per_page: pagination.value.per_page
        });

        const response = await fetch('/media?' + new URLSearchParams({
            search: search.value,
            page: pagination.value.current_page,
            per_page: pagination.value.per_page
        }));

        const data = await response.json();
        console.log('Media API response:', data);

        // The response should be the data array directly from the API
        if (data.data && Array.isArray(data.data)) {
            items.value = data.data;

            // Handle pagination - try both formats for compatibility
            if (data.meta && data.meta.pagination) {
                pagination.value = data.meta.pagination;
            } else {
                // Fallback to direct pagination properties
                pagination.value = {
                    current_page: data.current_page || 1,
                    last_page: data.last_page || 1,
                    per_page: data.per_page || 12,
                    total: data.total || 0
                };
            }
        } else {
            console.error('Expected data.data array but got:', typeof data.data);
            items.value = [];
        }

        console.log('Items after processing:', items.value);

        // Check if items have the expected properties
        if (items.value.length > 0) {
            console.log('First item properties:', {
                id: items.value[0].id,
                name: items.value[0].name,
                thumbnail_url: items.value[0].thumbnail_url,
                url: items.value[0].url
            });

            // Check if thumbnail_url is valid
            if (!items.value[0].thumbnail_url) {
                console.error('Missing thumbnail_url in item:', items.value[0]);
            }
        }

        loading.value = false;
    } catch (error) {
        console.error('Failed to load media:', error);
        // Ensure items is always a valid array even on error
        items.value = [];
        loading.value = false;

        // Handle authentication errors
        if (error.response && error.response.status === 401) {
            alert('Your session has expired. Please log in again.');
            // Redirect to login page or trigger auth refresh
        }
    }
};

const changePage = (page) => {
    if (page < 1 || page > pagination.value.last_page) return;
    pagination.value.current_page = page;
    loadMedia();
};

const searchMedia = () => {
    // Debounce search
    if (searchTimeout.value) {
        clearTimeout(searchTimeout.value);
    }

    searchTimeout.value = setTimeout(() => {
        loadMedia();
    }, 300);
};

const selectMedia = (item) => {
    if (props.multiple) {
        // Handle multiple selection
        const index = selectedItems.value.findIndex(selected => selected.id === item.id);

        if (index > -1) {
            // Item is already selected, remove it
            selectedItems.value.splice(index, 1);
        } else {
            // Item is not selected, add it if we haven't reached max
            if (selectedItems.value.length < props.maxSelection) {
                selectedItems.value.push(item);
            }
        }
    } else {
        // Handle single selection
        selectedItem.value = item;
        props.onSelect(item);
        emit('select', item);
        emit('close');
    }
};

const isItemSelected = (item) => {
    if (props.multiple) {
        return selectedItems.value.some(selected => selected.id === item.id);
    }
    return selectedItem.value && selectedItem.value.id === item.id;
};

const confirmSelection = () => {
    if (props.multiple && selectedItems.value.length > 0) {
        props.onSelect(selectedItems.value);
        emit('select', selectedItems.value);
        emit('close');
    }
};

const clearSelection = () => {
    selectedItems.value = [];
};

const truncateName = (text, length) => {
    if (!text) return '';
    if (text.length <= length) return text;

    const extension = text.lastIndexOf('.') > -1 ? text.substring(text.lastIndexOf('.')) : '';
    const name = text.substring(0, text.lastIndexOf('.') > -1 ? text.lastIndexOf('.') : text.length);

    if (name.length <= length - 3) return text;
    return name.substring(0, length - 3) + '...' + extension;
};

const formatFileSize = (bytes) => {
    if (!bytes) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
};

const handleImageError = (event) => {
    // Prevent infinite loop by checking if we've already tried to set a fallback
    if (event.target.dataset.errorHandled) {
        return;
    }

    // Mark as handled to prevent infinite loop
    event.target.dataset.errorHandled = 'true';

    // Create a simple SVG placeholder instead of trying to load another image
    const svgPlaceholder = 'data:image/svg+xml;base64,' + btoa(`
        <svg xmlns="http://www.w3.org/2000/svg" width="100" height="100" viewBox="0 0 100 100">
            <rect width="100" height="100" fill="#f3f4f6"/>
            <path d="M30 25h40a5 5 0 015 5v40a5 5 0 01-5 5H30a5 5 0 01-5-5V30a5 5 0 015-5z" fill="#d1d5db"/>
            <circle cx="40" cy="40" r="5" fill="#9ca3af"/>
            <path d="M30 60l10-10 5 5 15-15 15 15v10H30z" fill="#9ca3af"/>
        </svg>
    `);

    event.target.src = svgPlaceholder;
};

const openUploadModal = () => {
    showUploadModal.value = true;
    uploadFiles.value = [];
    isDragging.value = false;
};

const handleFileSelect = (event) => {
    const files = event.target.files;
    if (files && files.length > 0) {
        addFilesToUpload(files);
    }
};

const handleFileDrop = (event) => {
    isDragging.value = false;
    const files = event.dataTransfer.files;
    if (files && files.length > 0) {
        addFilesToUpload(files);
    }
};

const addFilesToUpload = (files) => {
    for (let i = 0; i < files.length; i++) {
        const file = files[i];
        if (file && file.type.startsWith('image/')) {
            // Check if file is already in the list
            const isDuplicate = uploadFiles.value.some(f =>
                f.name === file.name && f.size === file.size && f.lastModified === file.lastModified
            );

            if (!isDuplicate) {
                uploadFiles.value.push({
                    file: file,
                    name: file.name,
                    size: file.size,
                    preview: URL.createObjectURL(file)
                });
            }
        }
    }
};

// Paste functionality
const handlePaste = async (event) => {
    // Only handle paste when upload modal is open or main modal is focused
    if (!showUploadModal.value && !props.show) return;

    const clipboardItems = event.clipboardData?.items;
    if (!clipboardItems) return;

    const imageFiles = [];

    // Process clipboard items
    for (let i = 0; i < clipboardItems.length; i++) {
        const item = clipboardItems[i];

        // Check if item is an image
        if (item.type.startsWith('image/')) {
            const file = item.getAsFile();
            if (file) {
                // Generate a meaningful filename with timestamp
                const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
                const extension = getFileExtension(item.type);
                const filename = `pasted-image-${timestamp}.${extension}`;

                // Create a new File object with a proper name
                const namedFile = new File([file], filename, {
                    type: file.type,
                    lastModified: Date.now()
                });

                imageFiles.push(namedFile);
            }
        }
    }

    if (imageFiles.length > 0) {
        // Show paste indicator
        showPasteIndicator();

        // If upload modal is not open, open it
        if (!showUploadModal.value) {
            openUploadModal();
        }

        // Add pasted files to upload queue
        addFilesToUpload(imageFiles);

        // Prevent default paste behavior
        event.preventDefault();
    }
};

const getFileExtension = (mimeType) => {
    const extensions = {
        'image/png': 'png',
        'image/jpeg': 'jpg',
        'image/jpg': 'jpg',
        'image/gif': 'gif',
        'image/webp': 'webp',
        'image/bmp': 'bmp',
        'image/svg+xml': 'svg'
    };
    return extensions[mimeType] || 'png';
};

const showPasteIndicator = () => {
    pasteIndicator.value = true;

    // Clear existing timeout
    if (pasteTimeout.value) {
        clearTimeout(pasteTimeout.value);
    }

    // Hide indicator after 2 seconds
    pasteTimeout.value = setTimeout(() => {
        pasteIndicator.value = false;
    }, 2000);
};

const setupPasteListener = () => {
    document.addEventListener('paste', handlePaste);
};

const removePasteListener = () => {
    document.removeEventListener('paste', handlePaste);
    if (pasteTimeout.value) {
        clearTimeout(pasteTimeout.value);
    }
};

const removeFile = (index) => {
    if (uploadFiles.value[index] && uploadFiles.value[index].preview) {
        URL.revokeObjectURL(uploadFiles.value[index].preview);
    }
    uploadFiles.value.splice(index, 1);
};

// Helper function to get CSRF token
const getCsrfToken = () => {
    // First try meta tag
    const metaTag = document.querySelector('meta[name="csrf-token"]');
    if (metaTag && metaTag.getAttribute('content')) {
        return metaTag.getAttribute('content');
    }

    // Try to get from window object (if set by Laravel)
    if (window.Laravel && window.Laravel.csrfToken) {
        return window.Laravel.csrfToken;
    }

    // Try XSRF-TOKEN cookie (Laravel's default for SPA)
    const cookies = document.cookie.split(';');
    for (let cookie of cookies) {
        const [name, value] = cookie.trim().split('=');
        if (name === 'XSRF-TOKEN') {
            return decodeURIComponent(value);
        }
    }

    console.error('CSRF token not found. Please ensure the page includes the CSRF meta tag.');
    return null;
};

const uploadMedia = async () => {
    if (uploadFiles.value.length === 0) return;

    uploading.value = true;

    try {
        // Use fetch for consistent JSON API handling
        const formData = new FormData();

        // Append all files to the form data
        uploadFiles.value.forEach((fileObj, index) => {
            formData.append(`files[]`, fileObj.file);
        });

        // Get CSRF token safely
        const csrfToken = getCsrfToken();

        if (!csrfToken) {
            throw new Error('CSRF token not found. Please refresh the page and try again.');
        }

        const headers = {
            'X-CSRF-TOKEN': csrfToken,
            'X-Requested-With': 'XMLHttpRequest'
        };

        console.log('Uploading files:', uploadFiles.value.length);

        const response = await fetch('/media/upload', {
            method: 'POST',
            body: formData,
            headers: headers,
            credentials: 'same-origin'
        });

        if (!response.ok) {
            const errorData = await response.json().catch(() => ({ message: 'Upload failed' }));
            throw new Error(errorData.message || 'Upload failed');
        }

        const result = await response.json();
        console.log('Upload successful:', result);

        // Refresh media library to show new uploads
        await loadMedia();

        // Clean up and close modal
        uploadFiles.value.forEach(fileObj => {
            if (fileObj.preview) {
                URL.revokeObjectURL(fileObj.preview);
            }
        });

        showUploadModal.value = false;
        uploadFiles.value = [];

    } catch (error) {
        console.error('Failed to upload media:', error);
        alert(error.message || 'Failed to upload media. Please try again.');
    } finally {
        uploading.value = false;
    }
};

// Lifecycle
onMounted(() => {
    if (props.show) {
        loadMedia();
    }
    // Set up paste listener when component mounts
    setupPasteListener();
});

onUnmounted(() => {
    // Clean up paste listener when component unmounts
    removePasteListener();
    cleanup();
});

// Cleanup on unmount
const cleanup = () => {
    // Clean up all preview URLs
    uploadFiles.value.forEach(fileObj => {
        if (fileObj.preview) {
            URL.revokeObjectURL(fileObj.preview);
        }
    });
};

// Watch for show prop changes
watch(() => props.show, (newValue) => {
    if (newValue) {
        loadMedia();
    }
});
</script>

<template>
    <!-- Media Library Modal -->
    <div v-if="show" class="fixed inset-0 z-50 overflow-y-auto">
        <div class="flex items-end justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
            <!-- Background overlay -->
            <div class="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" @click="$emit('close')"></div>

            <!-- Modal panel -->
            <div class="inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-6xl sm:w-full" @click.stop>
                <!-- Header -->
                <div class="bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4 border-b border-gray-200">
                    <div class="flex justify-between items-center">
                        <h3 class="text-lg leading-6 font-medium text-gray-900">Media Library</h3>
                        <button type="button" @click="$emit('close')" class="text-gray-400 hover:text-gray-600">
                            <svg class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                            </svg>
                        </button>
                    </div>

                    <!-- Search and Upload -->
                    <div class="mt-4 flex justify-between items-center">
                        <div class="flex-1 max-w-md">
                            <input
                                v-model="search"
                                @input="searchMedia"
                                type="text"
                                placeholder="Search media..."
                                class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                            />
                        </div>
                        <div class="flex items-center space-x-2">
                            <!-- Paste Indicator for main modal -->
                            <div v-if="pasteIndicator && !showUploadModal" class="flex items-center px-3 py-1 bg-green-50 border border-green-200 rounded-md">
                                <svg class="w-4 h-4 text-green-500 mr-1" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd" />
                                </svg>
                                <span class="text-xs font-medium text-green-800">Pasted!</span>
                            </div>
                            <!-- Multiple selection info -->
                            <div v-if="multiple && selectedItems.length > 0" class="text-sm text-gray-600">
                                {{ selectedItems.length }} of {{ maxSelection }} selected
                            </div>
                            <button
                                type="button"
                                @click="openUploadModal"
                                class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500"
                            >
                                Upload Media
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Content -->
                <div class="bg-white px-4 pt-5 pb-4 sm:p-6" style="min-height: 500px; max-height: 600px; overflow-y: auto;">
                    <!-- Loading State -->
                    <div v-if="loading" class="flex justify-center items-center h-64">
                        <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
                    </div>

                    <!-- Media Grid -->
                    <div v-else-if="items && items.length > 0" class="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-6 gap-4">
                        <div
                            v-for="item in items"
                            :key="item.id"
                            @click="selectMedia(item)"
                            :class="[
                                'relative group cursor-pointer bg-gray-100 rounded-lg overflow-hidden hover:shadow-lg transition-all duration-200',
                                isItemSelected(item) ? 'ring-2 ring-blue-500 shadow-lg' : ''
                            ]"
                        >
                            <!-- Image -->
                            <div class="aspect-square">
                                <img
                                    :src="item.thumbnail_url || item.url"
                                    :alt="item.name"
                                    class="w-full h-full object-cover"
                                    loading="lazy"
                                    @error="handleImageError"
                                />
                            </div>

                            <!-- Selection indicator -->
                            <div v-if="multiple" class="absolute top-2 right-2">
                                <div :class="[
                                    'w-6 h-6 rounded-full border-2 flex items-center justify-center transition-all duration-200',
                                    isItemSelected(item)
                                        ? 'bg-blue-500 border-blue-500'
                                        : 'bg-white border-gray-300 group-hover:border-blue-400'
                                ]">
                                    <svg v-if="isItemSelected(item)" class="w-4 h-4 text-white" fill="currentColor" viewBox="0 0 20 20">
                                        <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd" />
                                    </svg>
                                </div>
                            </div>

                            <!-- Overlay -->
                            <div :class="[
                                'absolute inset-0 bg-black transition-opacity flex items-center justify-center',
                                isItemSelected(item) ? 'bg-opacity-20' : 'bg-opacity-0 group-hover:bg-opacity-30'
                            ]">
                                <div :class="[
                                    'transition-opacity',
                                    isItemSelected(item) ? 'opacity-100' : 'opacity-0 group-hover:opacity-100'
                                ]">
                                    <svg class="w-8 h-8 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                                    </svg>
                                </div>
                            </div>

                            <!-- Info -->
                            <div class="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black to-transparent p-2">
                                <p class="text-white text-xs font-medium truncate">{{ truncateName(item.name, 20) }}</p>
                                <p class="text-gray-300 text-xs">{{ formatFileSize(item.size) }}</p>
                            </div>
                        </div>
                    </div>

                    <!-- Empty State -->
                    <div v-else class="flex flex-col items-center justify-center h-64 text-gray-500">
                        <svg class="w-16 h-16 mb-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
                        </svg>
                        <p class="text-lg font-medium">No media found</p>
                        <p class="text-sm">Upload some images to get started</p>
                        <p class="text-xs text-gray-400 mt-2">💡 Tip: You can paste images directly with Ctrl+V / Cmd+V</p>
                    </div>

                    <!-- Pagination -->
                    <div v-if="pagination.last_page > 1" class="mt-6 flex justify-center">
                        <nav class="flex items-center space-x-2">
                            <button
                                type="button"
                                @click="changePage(pagination.current_page - 1)"
                                :disabled="pagination.current_page <= 1"
                                class="px-3 py-1 text-sm bg-gray-200 text-gray-700 rounded disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-300"
                            >
                                Previous
                            </button>

                            <span class="px-3 py-1 text-sm text-gray-700">
                                Page {{ pagination.current_page }} of {{ pagination.last_page }}
                            </span>

                            <button
                                type="button"
                                @click="changePage(pagination.current_page + 1)"
                                :disabled="pagination.current_page >= pagination.last_page"
                                class="px-3 py-1 text-sm bg-gray-200 text-gray-700 rounded disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-300"
                            >
                                Next
                            </button>
                        </nav>
                    </div>
                </div>

                <!-- Footer for multiple selection -->
                <div v-if="multiple" class="bg-gray-50 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse border-t border-gray-200">
                    <button
                        type="button"
                        @click="confirmSelection"
                        :disabled="selectedItems.length === 0"
                        :class="[
                            'w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 text-base font-medium text-white focus:outline-none focus:ring-2 focus:ring-offset-2 sm:ml-3 sm:w-auto sm:text-sm',
                            selectedItems.length > 0
                                ? 'bg-blue-600 hover:bg-blue-700 focus:ring-blue-500'
                                : 'bg-gray-300 cursor-not-allowed'
                        ]"
                    >
                        Select {{ selectedItems.length }} Image{{ selectedItems.length !== 1 ? 's' : '' }}
                    </button>
                    <button
                        type="button"
                        @click="clearSelection"
                        v-if="selectedItems.length > 0"
                        class="mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 shadow-sm px-4 py-2 bg-white text-base font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 sm:mt-0 sm:mr-3 sm:w-auto sm:text-sm"
                    >
                        Clear Selection
                    </button>
                    <button
                        type="button"
                        @click="$emit('close')"
                        class="mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 shadow-sm px-4 py-2 bg-white text-base font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 sm:mt-0 sm:w-auto sm:text-sm"
                    >
                        Cancel
                    </button>
                </div>

            </div>
        </div>
    </div>

    <!-- Upload Modal -->
    <div v-if="showUploadModal" class="fixed inset-0 overflow-y-auto" style="z-index: 9999;">
        <div class="flex items-end justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
            <!-- Background overlay -->
            <div class="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" @click="showUploadModal = false"></div>

            <!-- Modal panel -->
            <div class="inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full" @click.stop>
                <!-- Header -->
                <div class="bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4 border-b border-gray-200">
                    <div class="flex justify-between items-center">
                        <h3 class="text-lg leading-6 font-medium text-gray-900">Upload Media</h3>
                        <button type="button" @click="showUploadModal = false" class="text-gray-400 hover:text-gray-600">
                            <svg class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                            </svg>
                        </button>
                    </div>
                </div>

                <!-- Content -->
                <div class="bg-white px-4 pt-5 pb-4 sm:p-6">
                    <!-- Paste Indicator -->
                    <div v-if="pasteIndicator" class="mb-4 p-3 bg-green-50 border border-green-200 rounded-lg">
                        <div class="flex items-center">
                            <svg class="w-5 h-5 text-green-500 mr-2" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd" />
                            </svg>
                            <span class="text-sm font-medium text-green-800">Image pasted successfully!</span>
                        </div>
                    </div>

                    <!-- File Drop Zone -->
                    <div
                        @drop.prevent="handleFileDrop"
                        @dragover.prevent="isDragging = true"
                        @dragleave.prevent="isDragging = false"
                        :class="[
                            'border-2 border-dashed rounded-lg p-6 text-center transition-colors',
                            isDragging ? 'border-blue-500 bg-blue-50' : 'border-gray-300'
                        ]"
                    >
                        <svg class="mx-auto h-12 w-12 text-gray-400" stroke="currentColor" fill="none" viewBox="0 0 48 48">
                            <path d="M28 8H12a4 4 0 00-4 4v20m32-12v8m0 0v8a4 4 0 01-4 4H12a4 4 0 01-4-4v-4m32-4l-3.172-3.172a4 4 0 00-5.656 0L28 28M8 32l9.172-9.172a4 4 0 015.656 0L28 28m0 0l4 4m4-24h8m-4-4v8m-12 4h.02" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" />
                        </svg>
                        <div class="mt-4">
                            <label for="file-upload" class="cursor-pointer">
                                <span class="mt-2 block text-sm font-medium text-gray-900">
                                    Drop files here or click to browse
                                </span>
                                <input
                                    id="file-upload"
                                    name="file-upload"
                                    type="file"
                                    class="sr-only"
                                    multiple
                                    accept="image/*"
                                    @change="handleFileSelect"
                                />
                            </label>
                            <p class="mt-1 text-xs text-gray-500">PNG, JPG, GIF up to 10MB</p>
                            <p class="mt-1 text-xs text-gray-400">💡 Tip: You can also paste images directly (Ctrl+V / Cmd+V)</p>
                        </div>
                    </div>

                    <!-- Selected Files -->
                    <div v-if="uploadFiles.length > 0" class="mt-4">
                        <h4 class="text-sm font-medium text-gray-900 mb-2">Selected Files ({{ uploadFiles.length }})</h4>
                        <div class="space-y-2 max-h-40 overflow-y-auto">
                            <div
                                v-for="(fileObj, index) in uploadFiles"
                                :key="index"
                                class="flex items-center justify-between p-2 bg-gray-50 rounded"
                            >
                                <div class="flex items-center space-x-3">
                                    <img
                                        :src="fileObj.preview"
                                        :alt="fileObj.name"
                                        class="w-10 h-10 object-cover rounded"
                                    />
                                    <div>
                                        <p class="text-sm font-medium text-gray-900 truncate max-w-xs">{{ fileObj.name }}</p>
                                        <p class="text-xs text-gray-500">{{ formatFileSize(fileObj.size) }}</p>
                                    </div>
                                </div>
                                <button
                                    type="button"
                                    @click="removeFile(index)"
                                    class="text-red-600 hover:text-red-800"
                                >
                                    <svg class="w-4 h-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                                    </svg>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Footer -->
                <div class="bg-gray-50 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse">
                    <button
                        type="button"
                        @click="uploadMedia"
                        :disabled="uploadFiles.length === 0 || uploading"
                        :class="[
                            'w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 text-base font-medium text-white focus:outline-none focus:ring-2 focus:ring-offset-2 sm:ml-3 sm:w-auto sm:text-sm',
                            uploadFiles.length > 0 && !uploading
                                ? 'bg-blue-600 hover:bg-blue-700 focus:ring-blue-500'
                                : 'bg-gray-300 cursor-not-allowed'
                        ]"
                    >
                        <svg v-if="uploading" class="animate-spin -ml-1 mr-3 h-5 w-5 text-white" fill="none" viewBox="0 0 24 24">
                            <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                            <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                        </svg>
                        {{ uploading ? 'Uploading...' : 'Upload' }}
                    </button>
                    <button
                        type="button"
                        @click="showUploadModal = false"
                        :disabled="uploading"
                        class="mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 shadow-sm px-4 py-2 bg-white text-base font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm disabled:opacity-50 disabled:cursor-not-allowed"
                    >
                        Cancel
                    </button>
                </div>
            </div>
        </div>
    </div>
</template>
