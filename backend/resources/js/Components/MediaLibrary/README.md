# Media Library Components

This directory contains Vue.js components for managing media files in the application.

## Components

### MediaLibraryModal.vue
The main modal component for browsing, selecting, and uploading media files.

**Features:**
- Browse existing media files with pagination
- Search functionality
- Single and multiple selection modes
- Drag & drop file upload
- **Paste functionality for images** (NEW)
- File preview and metadata display

**Paste Functionality:**
The component now supports pasting images directly from the clipboard:

1. **How it works:**
   - Listens for paste events (Ctrl+V / Cmd+V) when the modal is open
   - Detects image data in clipboard
   - Automatically converts pasted images to files
   - Integrates with existing upload workflow

2. **Supported formats:**
   - PNG, JPEG, GIF, WebP, BMP, SVG
   - Any image format supported by the browser's clipboard API

3. **User experience:**
   - Visual feedback with green success indicator
   - Automatic filename generation with timestamp
   - Opens upload modal if not already open
   - Seamless integration with drag & drop workflow

4. **Usage:**
   - Users can paste screenshots, copied images, or any clipboard image
   - Works from anywhere in the application when modal is focused
   - Provides helpful tips in the UI about paste functionality

### MediaPicker.vue
A form input component that opens the MediaLibraryModal for selecting a single media file.

**Features:**
- Form-friendly interface
- Preview selected media
- Clear selection option
- Validation support
- Inherits paste functionality from MediaLibraryModal

## Usage Examples

### Basic Media Picker
```vue
<template>
  <MediaPicker
    v-model="selectedMedia"
    label="Featured Image"
    placeholder="Select an image..."
    :required="true"
  />
</template>

<script setup>
import { ref } from 'vue'
import MediaPicker from '@/Components/MediaLibrary/MediaPicker.vue'

const selectedMedia = ref(null)
</script>
```

### Media Library Modal
```vue
<template>
  <MediaLibraryModal
    :show="showModal"
    :multiple="true"
    :maxSelection="5"
    @select="handleSelection"
    @close="showModal = false"
  />
</template>

<script setup>
import { ref } from 'vue'
import MediaLibraryModal from '@/Components/MediaLibrary/MediaLibraryModal.vue'

const showModal = ref(false)

const handleSelection = (media) => {
  console.log('Selected media:', media)
}
</script>
```

## API Integration

The components integrate with the following backend endpoints:

- `GET /media` - Fetch media files with pagination and search
- `POST /media/upload` - Upload new media files
- `GET /media/{id}` - Get specific media file details

## File Structure

```
MediaLibrary/
├── MediaLibraryModal.vue    # Main modal component
├── MediaPicker.vue          # Form input component
└── README.md               # This documentation
```

## Recent Updates

### Paste Functionality (Latest)
- Added clipboard paste support for images
- Automatic file naming with timestamps
- Visual feedback for successful paste operations
- Seamless integration with existing upload workflow
- Cross-platform support (Windows, Mac, Linux)

The paste functionality enhances user productivity by allowing quick addition of screenshots, copied images, and other clipboard content without needing to save files first.
