<template>
    <div v-if="isOpen" class="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50" @click="closeCalculator">
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-xl p-6 w-80 max-w-sm mx-4" @click.stop>
            <!-- Header -->
            <div class="flex items-center justify-between mb-4">
                <h3 class="text-lg font-semibold text-gray-900 dark:text-white">Calculator</h3>
                <button
                    @click="closeCalculator"
                    class="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 transition-colors"
                >
                    <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                    </svg>
                </button>
            </div>

            <!-- Display -->
            <div class="mb-4">
                <div class="bg-gray-100 dark:bg-gray-700 rounded-lg p-4 text-right">
                    <div class="text-sm text-gray-500 dark:text-gray-400 h-5">{{ previousValue }}</div>
                    <div class="text-2xl font-mono text-gray-900 dark:text-white">{{ display }}</div>
                </div>
            </div>

            <!-- Buttons -->
            <div class="grid grid-cols-4 gap-2">
                <!-- Row 1 -->
                <button @click="clear" class="calc-btn bg-red-500 hover:bg-red-600 text-white">C</button>
                <button @click="clearEntry" class="calc-btn bg-orange-500 hover:bg-orange-600 text-white">CE</button>
                <button @click="backspace" class="calc-btn bg-gray-500 hover:bg-gray-600 text-white">⌫</button>
                <button @click="inputOperator('/')" class="calc-btn bg-blue-500 hover:bg-blue-600 text-white">÷</button>

                <!-- Row 2 -->
                <button @click="inputNumber('7')" class="calc-btn">7</button>
                <button @click="inputNumber('8')" class="calc-btn">8</button>
                <button @click="inputNumber('9')" class="calc-btn">9</button>
                <button @click="inputOperator('*')" class="calc-btn bg-blue-500 hover:bg-blue-600 text-white">×</button>

                <!-- Row 3 -->
                <button @click="inputNumber('4')" class="calc-btn">4</button>
                <button @click="inputNumber('5')" class="calc-btn">5</button>
                <button @click="inputNumber('6')" class="calc-btn">6</button>
                <button @click="inputOperator('-')" class="calc-btn bg-blue-500 hover:bg-blue-600 text-white">−</button>

                <!-- Row 4 -->
                <button @click="inputNumber('1')" class="calc-btn">1</button>
                <button @click="inputNumber('2')" class="calc-btn">2</button>
                <button @click="inputNumber('3')" class="calc-btn">3</button>
                <button @click="inputOperator('+')" class="calc-btn bg-blue-500 hover:bg-blue-600 text-white row-span-2 h-auto">+</button>

                <!-- Row 5 -->
                <button @click="inputNumber('0')" class="calc-btn col-span-2">0</button>
                <button @click="inputDecimal" class="calc-btn">.</button>
                <button @click="calculate" class="calc-btn bg-green-500 hover:bg-green-600 text-white">=</button>
            </div>
        </div>
    </div>
</template>

<script setup>
import { ref, watch } from 'vue'

const props = defineProps({
    isOpen: {
        type: Boolean,
        default: false
    }
})

const emit = defineEmits(['close'])

// Calculator state
const display = ref('0')
const previousValue = ref('')
const operator = ref('')
const waitingForOperand = ref(false)
const hasDecimal = ref(false)

// Methods
const closeCalculator = () => {
    emit('close')
}

const inputNumber = (num) => {
    if (waitingForOperand.value) {
        display.value = num
        waitingForOperand.value = false
        hasDecimal.value = false
    } else {
        display.value = display.value === '0' ? num : display.value + num
    }
}

const inputDecimal = () => {
    if (waitingForOperand.value) {
        display.value = '0.'
        waitingForOperand.value = false
        hasDecimal.value = true
    } else if (!hasDecimal.value) {
        display.value += '.'
        hasDecimal.value = true
    }
}

const inputOperator = (nextOperator) => {
    const inputValue = parseFloat(display.value)

    if (previousValue.value === '') {
        previousValue.value = inputValue
    } else if (operator.value) {
        const currentValue = previousValue.value || 0
        const newValue = performCalculation(currentValue, inputValue, operator.value)

        display.value = String(newValue)
        previousValue.value = newValue
    }

    waitingForOperand.value = true
    operator.value = nextOperator
    hasDecimal.value = false
}

const calculate = () => {
    const inputValue = parseFloat(display.value)

    if (previousValue.value !== '' && operator.value) {
        const currentValue = previousValue.value || 0
        const newValue = performCalculation(currentValue, inputValue, operator.value)

        display.value = String(newValue)
        previousValue.value = ''
        operator.value = ''
        waitingForOperand.value = true
        hasDecimal.value = false
    }
}

const performCalculation = (firstValue, secondValue, operation) => {
    switch (operation) {
        case '+':
            return firstValue + secondValue
        case '-':
            return firstValue - secondValue
        case '*':
            return firstValue * secondValue
        case '/':
            return secondValue !== 0 ? firstValue / secondValue : 0
        default:
            return secondValue
    }
}

const clear = () => {
    display.value = '0'
    previousValue.value = ''
    operator.value = ''
    waitingForOperand.value = false
    hasDecimal.value = false
}

const clearEntry = () => {
    display.value = '0'
    hasDecimal.value = false
}

const backspace = () => {
    if (display.value.length > 1) {
        const newValue = display.value.slice(0, -1)
        display.value = newValue
        hasDecimal.value = newValue.includes('.')
    } else {
        display.value = '0'
        hasDecimal.value = false
    }
}

// Keyboard support
const handleKeydown = (event) => {
    const { key } = event
    
    if (key >= '0' && key <= '9') {
        event.preventDefault()
        inputNumber(key)
    } else if (key === '.') {
        event.preventDefault()
        inputDecimal()
    } else if (['+', '-', '*', '/'].includes(key)) {
        event.preventDefault()
        inputOperator(key)
    } else if (key === 'Enter' || key === '=') {
        event.preventDefault()
        calculate()
    } else if (key === 'Escape') {
        event.preventDefault()
        closeCalculator()
    } else if (key === 'Backspace') {
        event.preventDefault()
        backspace()
    } else if (key === 'Delete') {
        event.preventDefault()
        clear()
    }
}

// Watch for open state to add/remove keyboard listeners
watch(() => props.isOpen, (isOpen) => {
    if (isOpen) {
        document.addEventListener('keydown', handleKeydown)
    } else {
        document.removeEventListener('keydown', handleKeydown)
    }
})
</script>

<style scoped>
.calc-btn {
    @apply h-12 bg-gray-200 dark:bg-gray-600 text-gray-900 dark:text-white rounded-lg font-semibold text-lg hover:bg-gray-300 dark:hover:bg-gray-500 transition-colors focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2;
}
</style>
