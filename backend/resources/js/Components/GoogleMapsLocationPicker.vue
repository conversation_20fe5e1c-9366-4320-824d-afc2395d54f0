<template>
    <div class="space-y-4">
        <!-- Address Search Input -->
        <div>
            <label for="address-search" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                Search Location
            </label>
            <input
                id="address-search"
                ref="searchInput"
                v-model="searchQuery"
                type="text"
                :placeholder="placeholder"
                class="w-full rounded-md border-gray-300 dark:border-gray-700 dark:bg-gray-900 dark:text-gray-300 focus:border-indigo-500 focus:ring-indigo-500"
                @input="onSearchInput"
            />
            <p class="mt-1 text-xs text-gray-500 dark:text-gray-400">
                Search for an address or click on the map to set the location
            </p>
        </div>

        <!-- Map Container -->
        <div class="relative">
            <!-- Loading State -->
            <div v-if="isLoading" id="map-loading" class="flex items-center justify-center h-64 bg-gray-100 dark:bg-gray-700 rounded-lg">
                <div class="text-center">
                    <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-indigo-600 mx-auto mb-2"></div>
                    <p class="text-sm text-gray-600 dark:text-gray-400">Loading map...</p>
                </div>
            </div>

            <!-- Map -->
            <div
                id="google-map"
                ref="mapContainer"
                class="h-64 w-full rounded-lg border border-gray-300 dark:border-gray-600"
                :class="{ 'hidden': isLoading }"
            ></div>

            <!-- Error State -->
            <div v-if="mapError" class="absolute inset-0 flex items-center justify-center bg-red-50 dark:bg-red-900/20 rounded-lg border border-red-200 dark:border-red-800">
                <div class="text-center p-4">
                    <svg class="h-8 w-8 text-red-400 mx-auto mb-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
                    </svg>
                    <p class="text-sm text-red-600 dark:text-red-400">{{ mapError }}</p>
                </div>
            </div>
        </div>

        <!-- Selected Location Display -->
        <div v-if="selectedLocation" class="bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg p-3">
            <div class="flex items-start">
                <svg class="h-5 w-5 text-green-400 mt-0.5 mr-2" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
                </svg>
                <div class="flex-1">
                    <h4 class="text-sm font-medium text-green-800 dark:text-green-200">Location Selected</h4>
                    <p class="text-sm text-green-700 dark:text-green-300 mt-1">
                        {{ selectedLocation.address || `${selectedLocation.lat.toFixed(6)}, ${selectedLocation.lng.toFixed(6)}` }}
                    </p>
                    <p class="text-xs text-green-600 dark:text-green-400 mt-1">
                        Coordinates: {{ selectedLocation.lat.toFixed(6) }}, {{ selectedLocation.lng.toFixed(6) }}
                    </p>
                </div>
            </div>
        </div>

        <!-- Error Messages -->
        <div v-if="errors.latitude || errors.longitude" class="text-sm text-red-600 dark:text-red-400">
            {{ errors.latitude || errors.longitude }}
        </div>
    </div>
</template>

<script setup>
import { ref, onMounted, watch, nextTick } from 'vue'

const props = defineProps({
    latitude: {
        type: [Number, String],
        default: null
    },
    longitude: {
        type: [Number, String],
        default: null
    },
    formattedAddress: {
        type: String,
        default: ''
    },
    initialAddress: {
        type: String,
        default: ''
    },
    placeholder: {
        type: String,
        default: 'Search for a location or click on the map'
    },
    errors: {
        type: Object,
        default: () => ({})
    }
})

const emit = defineEmits(['update:latitude', 'update:longitude', 'update:formatted-address'])

// Component state
const isLoading = ref(true)
const mapError = ref('')
const searchQuery = ref('')
const selectedLocation = ref(null)
const searchInput = ref(null)
const mapContainer = ref(null)

// Google Maps objects
let map = null
let marker = null
let geocoder = null
let autocomplete = null

// Default location (Dhaka, Bangladesh)
const defaultLocation = {
    lat: 23.8103,
    lng: 90.4125
}

// Initialize component
onMounted(async () => {
    searchQuery.value = props.initialAddress
    
    // Initialize selected location if coordinates are provided
    if (props.latitude && props.longitude) {
        selectedLocation.value = {
            lat: parseFloat(props.latitude),
            lng: parseFloat(props.longitude),
            address: props.formattedAddress
        }
    }
    
    await loadGoogleMaps()
})

// Watch for prop changes
watch(() => [props.latitude, props.longitude], ([newLat, newLng]) => {
    if (newLat && newLng && map && marker) {
        const position = new google.maps.LatLng(parseFloat(newLat), parseFloat(newLng))
        marker.setPosition(position)
        map.setCenter(position)
        selectedLocation.value = {
            lat: parseFloat(newLat),
            lng: parseFloat(newLng),
            address: props.formattedAddress
        }
    }
})

// Load Google Maps
const loadGoogleMaps = async () => {
    try {
        // Check if Google Maps is already loaded
        if (typeof google !== 'undefined' && google.maps) {
            await initializeMap()
            return
        }

        // Load Google Maps script
        const apiKey = window.googleMapsApiKey || ''
        if (!apiKey) {
            throw new Error('Google Maps API key not configured')
        }

        const script = document.createElement('script')
        script.src = `https://maps.googleapis.com/maps/api/js?key=${apiKey}&libraries=places&callback=initGoogleMapsLocationPicker`
        script.async = true
        script.defer = true

        // Set up global callback
        window.initGoogleMapsLocationPicker = async () => {
            await initializeMap()
        }

        document.head.appendChild(script)
    } catch (error) {
        console.error('Error loading Google Maps:', error)
        mapError.value = error.message
        isLoading.value = false
    }
}

// Initialize map
const initializeMap = async () => {
    try {
        await nextTick()
        
        if (!mapContainer.value) {
            throw new Error('Map container not found')
        }

        geocoder = new google.maps.Geocoder()

        // Determine initial center
        const initialCenter = selectedLocation.value || defaultLocation

        // Create map
        map = new google.maps.Map(mapContainer.value, {
            zoom: 15,
            center: initialCenter,
            mapTypeControl: false,
            streetViewControl: false,
            fullscreenControl: false
        })

        // Create marker
        marker = new google.maps.Marker({
            position: initialCenter,
            map: map,
            draggable: true,
            title: 'Branch Location'
        })

        // Add map click listener
        map.addListener('click', (event) => {
            updateMarkerPosition(event.latLng)
        })

        // Add marker drag listener
        marker.addListener('dragend', (event) => {
            updateMarkerPosition(event.latLng)
        })

        // Initialize autocomplete
        if (searchInput.value && google.maps.places) {
            autocomplete = new google.maps.places.Autocomplete(searchInput.value, {
                fields: ['place_id', 'geometry', 'name', 'formatted_address']
            })

            autocomplete.addListener('place_changed', () => {
                const place = autocomplete.getPlace()
                if (place.geometry) {
                    updateMarkerPosition(place.geometry.location)
                    map.setCenter(place.geometry.location)
                }
            })
        }

        isLoading.value = false
    } catch (error) {
        console.error('Error initializing map:', error)
        mapError.value = 'Failed to initialize map. Please try refreshing the page.'
        isLoading.value = false
    }
}

// Update marker position and emit coordinates
const updateMarkerPosition = (latLng) => {
    try {
        marker.setPosition(latLng)
        
        const lat = latLng.lat()
        const lng = latLng.lng()
        
        selectedLocation.value = {
            lat: lat,
            lng: lng,
            address: ''
        }

        // Emit coordinates
        emit('update:latitude', lat)
        emit('update:longitude', lng)

        // Reverse geocode to get address
        geocoder.geocode({ location: latLng }, (results, status) => {
            if (status === 'OK' && results[0]) {
                const address = results[0].formatted_address
                selectedLocation.value.address = address
                emit('update:formatted-address', address)
            } else {
                emit('update:formatted-address', `${lat.toFixed(6)}, ${lng.toFixed(6)}`)
            }
        })
    } catch (error) {
        console.error('Error updating marker position:', error)
    }
}

// Handle search input
const onSearchInput = () => {
    // The autocomplete will handle this automatically
}
</script>

<style scoped>
.animate-spin {
    animation: spin 1s linear infinite;
}

@keyframes spin {
    from {
        transform: rotate(0deg);
    }
    to {
        transform: rotate(360deg);
    }
}
</style>
