import { createI18n } from 'vue-i18n'

// Import translation files
import en from '../lang/en.json'
import bn from '../lang/bn.json'

// Get the current locale from the HTML lang attribute or default to 'en'
const getLocale = () => {
    // Check if we're in a browser environment
    if (typeof window !== 'undefined') {
        // Try to get locale from HTML lang attribute
        const htmlLang = document.documentElement.lang
        if (htmlLang && (htmlLang === 'en' || htmlLang === 'bn')) {
            return htmlLang
        }
        
        // Try to get from localStorage
        const savedLocale = localStorage.getItem('locale')
        if (savedLocale && (savedLocale === 'en' || savedLocale === 'bn')) {
            return savedLocale
        }
    }
    
    // Default to English
    return 'en'
}

// Create i18n instance
const i18n = createI18n({
    legacy: false, // Use Composition API mode
    locale: getLocale(),
    fallbackLocale: 'en',
    messages: {
        en,
        bn
    },
    // Enable missing translation warnings in development
    missingWarn: import.meta.env.DEV,
    fallbackWarn: import.meta.env.DEV,
})

// Function to change locale
export const changeLocale = (locale) => {
    if (locale === 'en' || locale === 'bn') {
        i18n.global.locale.value = locale
        
        // Save to localStorage
        if (typeof window !== 'undefined') {
            localStorage.setItem('locale', locale)
            
            // Update HTML lang attribute
            document.documentElement.lang = locale
            
            // Update page direction for RTL languages if needed
            document.documentElement.dir = locale === 'ar' ? 'rtl' : 'ltr'
        }
    }
}

// Function to get current locale
export const getCurrentLocale = () => {
    return i18n.global.locale.value
}

// Function to get available locales
export const getAvailableLocales = () => {
    return [
        {
            code: 'en',
            name: 'English',
            native_name: 'English',
            flag: '🇺🇸'
        },
        {
            code: 'bn',
            name: 'Bengali',
            native_name: 'বাংলা',
            flag: '🇧🇩'
        }
    ]
}

export default i18n
