<template>
    <GuestLayout>
        <Head :title="page.meta_title || page.title" />
        
        <!-- SEO Meta Tags -->
        <Head>
            <meta name="description" :content="page.meta_description || page.excerpt" />
            <meta name="keywords" :content="page.meta_keywords ? page.meta_keywords.join(', ') : ''" />
            <meta property="og:title" :content="page.meta_title || page.title" />
            <meta property="og:description" :content="page.meta_description || page.excerpt" />
            <meta property="og:image" :content="page.banner_image_url" v-if="page.banner_image_url" />
            <meta property="og:type" content="article" />
            <meta name="twitter:card" content="summary_large_image" />
            <meta name="twitter:title" :content="page.meta_title || page.title" />
            <meta name="twitter:description" :content="page.meta_description || page.excerpt" />
            <meta name="twitter:image" :content="page.banner_image_url" v-if="page.banner_image_url" />
        </Head>

        <!-- Banner Image -->
        <div v-if="page.banner_image_url" class="relative h-64 md:h-80 lg:h-96 overflow-hidden">
            <img 
                :src="page.banner_image_url" 
                :alt="page.title"
                class="w-full h-full object-cover"
            />
            <div class="absolute inset-0 bg-black bg-opacity-40 flex items-center justify-center">
                <div class="text-center text-white">
                    <h1 class="text-4xl md:text-5xl lg:text-6xl font-bold mb-4">
                        {{ page.title }}
                    </h1>
                    <p v-if="page.excerpt" class="text-lg md:text-xl max-w-2xl mx-auto px-4">
                        {{ page.excerpt }}
                    </p>
                </div>
            </div>
        </div>

        <!-- Page Content -->
        <div class="py-12">
            <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
                <!-- Page Title (if no banner) -->
                <div v-if="!page.banner_image_url" class="text-center mb-12">
                    <h1 class="text-4xl md:text-5xl font-bold text-gray-900 dark:text-white mb-4">
                        {{ page.title }}
                    </h1>
                    <p v-if="page.excerpt" class="text-lg text-gray-600 dark:text-gray-400 max-w-2xl mx-auto">
                        {{ page.excerpt }}
                    </p>
                </div>

                <!-- Page Content -->
                <div class="prose prose-lg max-w-none dark:prose-invert">
                    <div v-html="page.content"></div>
                </div>

                <!-- Page Meta Information -->
                <div class="mt-12 pt-8 border-t border-gray-200 dark:border-gray-700">
                    <div class="flex items-center justify-between text-sm text-gray-500 dark:text-gray-400">
                        <div>
                            <span>Published on {{ formatDate(page.published_at) }}</span>
                        </div>
                        <div v-if="page.updated_at !== page.created_at">
                            <span>Last updated {{ formatDate(page.updated_at) }}</span>
                        </div>
                    </div>
                </div>

                <!-- Back to Home -->
                <div class="mt-8 text-center">
                    <Link 
                        :href="route('guest.home')"
                        class="inline-flex items-center px-6 py-3 bg-orange-600 hover:bg-orange-700 text-white font-medium rounded-lg transition-colors duration-200"
                    >
                        <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
                        </svg>
                        Back to Home
                    </Link>
                </div>
            </div>
        </div>
    </GuestLayout>
</template>

<script setup>
import { Head, Link } from '@inertiajs/vue3'
import GuestLayout from '@/Layouts/GuestLayout.vue'

const props = defineProps({
    restaurant: Object,
    page: Object,
})

const formatDate = (dateString) => {
    if (!dateString) return ''
    
    const date = new Date(dateString)
    return date.toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'long',
        day: 'numeric'
    })
}
</script>

<style scoped>
/* Custom styles for page content */
.prose {
    color: inherit;
}

.prose h1,
.prose h2,
.prose h3,
.prose h4,
.prose h5,
.prose h6 {
    color: inherit;
}

.prose a {
    color: #ea580c;
    text-decoration: none;
}

.prose a:hover {
    color: #c2410c;
    text-decoration: underline;
}

.prose strong {
    color: inherit;
    font-weight: 600;
}

.prose blockquote {
    border-left: 4px solid #ea580c;
    background-color: rgba(234, 88, 12, 0.05);
    padding: 1rem 1.5rem;
    margin: 1.5rem 0;
    font-style: italic;
}

.prose code {
    background-color: rgba(234, 88, 12, 0.1);
    color: #ea580c;
    padding: 0.25rem 0.5rem;
    border-radius: 0.25rem;
    font-size: 0.875em;
}

.prose pre {
    background-color: #1f2937;
    color: #f9fafb;
    padding: 1rem;
    border-radius: 0.5rem;
    overflow-x: auto;
}

.prose pre code {
    background-color: transparent;
    color: inherit;
    padding: 0;
}

.prose img {
    border-radius: 0.5rem;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
}

.prose table {
    border-collapse: collapse;
    width: 100%;
}

.prose th,
.prose td {
    border: 1px solid #d1d5db;
    padding: 0.75rem;
    text-align: left;
}

.prose th {
    background-color: #f9fafb;
    font-weight: 600;
}

.prose ul,
.prose ol {
    padding-left: 1.5rem;
}

.prose li {
    margin: 0.5rem 0;
}

/* Dark mode adjustments */
.dark .prose blockquote {
    background-color: rgba(234, 88, 12, 0.1);
    border-left-color: #ea580c;
}

.dark .prose code {
    background-color: rgba(234, 88, 12, 0.2);
}

.dark .prose th {
    background-color: #374151;
    border-color: #4b5563;
}

.dark .prose td {
    border-color: #4b5563;
}
</style>
