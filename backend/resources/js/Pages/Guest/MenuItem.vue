<template>
    <GuestLayout>
        <Head :title="`${menuItem?.name} - ${restaurant?.name || 'Restaurant'}`" />

        <!-- Breadcrumb -->
        <section class="bg-gray-50 py-4">
            <div class="container mx-auto px-4">
                <nav class="flex items-center space-x-2 text-sm">
                    <Link :href="route('guest.home')" class="text-gray-600 hover:text-orange-600">
                        Home
                    </Link>
                    <span class="text-gray-400">/</span>
                    <Link :href="route('guest.menu')" class="text-gray-600 hover:text-orange-600">
                        Menu
                    </Link>
                    <span class="text-gray-400">/</span>
                    <span class="text-gray-800 font-semibold">{{ menuItem?.name }}</span>
                </nav>
            </div>
        </section>

        <!-- Menu Item Details -->
        <section class="py-12">
            <div class="container mx-auto px-4">
                <div class="grid grid-cols-1 lg:grid-cols-2 gap-12">
                    <!-- Image Slider -->
                    <div class="space-y-4">
                        <ImageSlider
                            :images="menuItemImages"
                            :alt="menuItem?.name"
                            @image-error="handleImageError"
                        />
                    </div>

                    <!-- Details -->
                    <div class="space-y-6">
                        <div>
                            <h1 class="text-3xl font-bold text-gray-800 mb-2">{{ menuItem?.name }}</h1>
                            <p class="text-gray-600 text-lg">{{ menuItem?.description }}</p>
                        </div>

                        <!-- Price -->
                        <div class="text-3xl font-bold text-orange-600">
                            ${{ formatPrice(menuItem?.effective_price) }}
                        </div>

                        <!-- Tags -->
                        <div class="flex flex-wrap gap-2">
                            <span v-if="menuItem?.is_vegetarian" class="bg-green-100 text-green-800 px-3 py-1 rounded-full text-sm">
                                <i class="fas fa-leaf mr-1"></i>Vegetarian
                            </span>
                            <span v-if="menuItem?.is_vegan" class="bg-green-100 text-green-800 px-3 py-1 rounded-full text-sm">
                                <i class="fas fa-seedling mr-1"></i>Vegan
                            </span>
                            <span v-if="menuItem?.is_spicy" class="bg-red-100 text-red-800 px-3 py-1 rounded-full text-sm">
                                <i class="fas fa-pepper-hot mr-1"></i>Spicy
                            </span>
                            <span v-if="menuItem?.is_gluten_free" class="bg-blue-100 text-blue-800 px-3 py-1 rounded-full text-sm">
                                Gluten Free
                            </span>
                        </div>

                        <!-- Additional Info -->
                        <div class="grid grid-cols-2 gap-4 text-sm">
                            <div v-if="menuItem?.preparation_time" class="flex items-center text-gray-600">
                                <i class="fas fa-clock mr-2 text-orange-600"></i>
                                {{ menuItem.preparation_time }} minutes
                            </div>
                            <div v-if="menuItem?.calories" class="flex items-center text-gray-600">
                                <i class="fas fa-fire mr-2 text-orange-600"></i>
                                {{ menuItem.calories }} calories
                            </div>
                        </div>

                        <!-- Ingredients -->
                        <div v-if="menuItem?.ingredients">
                            <h3 class="text-lg font-semibold mb-2">Ingredients</h3>
                            <p class="text-gray-600">{{ menuItem.ingredients }}</p>
                        </div>

                        <!-- Variations -->
                        <div v-if="menuItem?.variations && menuItem.variations.length > 0">
                            <h3 class="text-lg font-semibold mb-4">Available Variations</h3>
                            <div class="space-y-2">
                                <div
                                    v-for="variation in menuItem.variations"
                                    :key="variation.id"
                                    class="flex items-center justify-between p-3 bg-gray-50 rounded-lg"
                                >
                                    <div>
                                        <span class="font-medium">{{ variation.name }}</span>
                                        <p v-if="variation.description" class="text-sm text-gray-600">{{ variation.description }}</p>
                                    </div>
                                    <span class="text-sm font-medium">
                                        {{ variation.price_modifier > 0 ? `+$${variation.price_modifier}` :
                                           variation.price_modifier < 0 ? `-$${Math.abs(variation.price_modifier)}` : 'No extra charge' }}
                                    </span>
                                </div>
                            </div>
                        </div>

                        <!-- Add-ons -->
                        <div v-if="menuItem?.addons && menuItem.addons.length > 0">
                            <h3 class="text-lg font-semibold mb-4">Available Add-ons</h3>
                            <div class="grid grid-cols-1 sm:grid-cols-2 gap-3">
                                <div
                                    v-for="addon in menuItem.addons"
                                    :key="addon.id"
                                    class="flex items-center justify-between p-3 border border-gray-200 rounded-lg"
                                >
                                    <div>
                                        <div class="font-medium">{{ addon.name }}</div>
                                        <div v-if="addon.description" class="text-sm text-gray-600">{{ addon.description }}</div>
                                    </div>
                                    <span class="text-sm font-semibold text-orange-600">+${{ addon.price }}</span>
                                </div>
                            </div>
                        </div>

                        <!-- Availability Status -->
                        <div class="p-4 rounded-lg" :class="menuItem?.is_available ? 'bg-green-50 border border-green-200' : 'bg-red-50 border border-red-200'">
                            <div class="flex items-center">
                                <i :class="menuItem?.is_available ? 'fas fa-check-circle text-green-600' : 'fas fa-times-circle text-red-600'" class="mr-2"></i>
                                <span :class="menuItem?.is_available ? 'text-green-800' : 'text-red-800'" class="font-semibold">
                                    {{ menuItem?.is_available ? 'Available Now' : 'Currently Unavailable' }}
                                </span>
                            </div>
                        </div>

                        <!-- Add to Cart Section -->
                        <div v-if="menuItem?.is_available" class="space-y-6 border-t pt-6">
                            <AddToCartForm
                                :menu-item="menuItem"
                                :selected-variations="selectedVariations"
                                :selected-addons="selectedAddons"
                                :quantity="quantity"
                                @update-variations="updateVariations"
                                @update-addons="updateAddons"
                                @update-quantity="updateQuantity"
                                @add-to-cart="addToCart"
                            />
                        </div>

                        <!-- Alternative Actions -->
                        <div class="space-y-4 border-t pt-6">
                            <div class="flex flex-col sm:flex-row gap-4">
                                <a
                                    v-if="siteSettings?.contact?.phone"
                                    :href="`tel:${siteSettings.contact.phone}`"
                                    class="flex-1 border border-orange-600 text-orange-600 text-center px-6 py-3 rounded-lg font-semibold hover:bg-orange-50 transition-colors"
                                >
                                    <i class="fas fa-phone mr-2"></i>
                                    Call to Order
                                </a>
                                <Link
                                    :href="route('guest.menu')"
                                    class="flex-1 border border-gray-300 text-gray-600 text-center px-6 py-3 rounded-lg font-semibold hover:bg-gray-50 transition-colors"
                                >
                                    Back to Menu
                                </Link>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Related Items -->
        <section v-if="relatedItems && relatedItems.length > 0" class="py-12 bg-gray-50">
            <div class="container mx-auto px-4">
                <h2 class="text-2xl font-bold text-center mb-8">You Might Also Like</h2>
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                    <div
                        v-for="item in relatedItems"
                        :key="item.id"
                        class="bg-white rounded-lg shadow-md overflow-hidden hover:shadow-lg transition-shadow"
                    >
                        <div class="h-32 bg-gray-200 relative">
                            <img
                                v-if="item.primary_image_url"
                                :src="item.primary_image_url"
                                :alt="item.name"
                                class="w-full h-full object-cover"
                                @error="handleImageError"
                            />
                            <div v-else class="flex items-center justify-center h-full text-gray-400">
                                <i class="fas fa-utensils text-2xl"></i>
                            </div>
                            <div class="absolute top-2 right-2">
                                <span class="bg-orange-600 text-white px-2 py-1 rounded text-xs font-semibold">
                                    ${{ formatPrice(item.effective_price) }}
                                </span>
                            </div>
                        </div>
                        <div class="p-4">
                            <h3 class="font-semibold mb-2">{{ item.name }}</h3>
                            <Link
                                :href="route('guest.menu.item', item.slug)"
                                class="text-orange-600 hover:text-orange-700 text-sm font-semibold"
                            >
                                View Details →
                            </Link>
                        </div>
                    </div>
                </div>
            </div>
        </section>
    </GuestLayout>
</template>

<script setup>
import { Head, Link } from '@inertiajs/vue3'
import { ref, computed, onMounted } from 'vue'
import GuestLayout from '@/Layouts/GuestLayout.vue'
import ImageSlider from '@/Components/Guest/ImageSlider.vue'
import AddToCartForm from '@/Components/Guest/AddToCartForm.vue'
import { useCart } from '@/Composables/useCart'

const props = defineProps({
    restaurant: Object,
    menuItem: Object,
    relatedItems: Array,
    siteSettings: Object,
})

// Cart functionality
const { addItemToCart } = useCart()

// Component state
const selectedVariations = ref({})
const selectedAddons = ref([])
const quantity = ref(1)

// Computed properties
const menuItemImages = computed(() => {
    if (!props.menuItem) return []

    const images = []

    // Add primary image first
    if (props.menuItem.primary_image_url) {
        images.push({
            url: props.menuItem.primary_image_url,
            alt: props.menuItem.name,
            isPrimary: true
        })
    }

    // Add additional images from mediaItems
    if (props.menuItem.mediaItems) {
        props.menuItem.mediaItems.forEach(media => {
            // Skip if it's already the primary image
            if (media.url !== props.menuItem.primary_image_url) {
                images.push({
                    url: media.url,
                    alt: media.name || props.menuItem.name,
                    isPrimary: false
                })
            }
        })
    }

    // If no images, return placeholder
    if (images.length === 0) {
        images.push({
            url: '/images/placeholder-food.jpg',
            alt: props.menuItem.name,
            isPrimary: true,
            isPlaceholder: true
        })
    }

    return images
})

// Methods
const updateVariations = (variations) => {
    selectedVariations.value = variations
}

const updateAddons = (addons) => {
    selectedAddons.value = addons
}

const updateQuantity = (newQuantity) => {
    quantity.value = newQuantity
}

const addToCart = (cartItem) => {
    addItemToCart(cartItem)

    // Show success message
    alert('Item added to cart successfully!')
}

const formatPrice = (price) => {
    if (!price) return '0.00'
    return parseFloat(price).toFixed(2)
}

const handleImageError = (event) => {
    if (event.target.dataset.errorHandled) return;
    event.target.dataset.errorHandled = 'true';

    // Use inline SVG placeholder
    const svgPlaceholder = 'data:image/svg+xml;base64,' + btoa(`
        <svg xmlns="http://www.w3.org/2000/svg" width="200" height="200" viewBox="0 0 200 200">
            <rect width="200" height="200" fill="#f3f4f6"/>
            <circle cx="80" cy="70" r="12" fill="#d1d5db"/>
            <polygon points="40,140 80,100 120,120 160,80 180,140" fill="#d1d5db"/>
            <text x="100" y="170" text-anchor="middle" fill="#9ca3af" font-size="16">Menu Item</text>
        </svg>
    `);

    event.target.src = svgPlaceholder;
};

// Initialize component
onMounted(() => {
    // Debug: Log menu item data
    console.log('MenuItem data:', props.menuItem)
    console.log('Primary image URL:', props.menuItem?.primary_image_url)
    console.log('Media items:', props.menuItem?.mediaItems)
    console.log('Computed images:', menuItemImages.value)

    // Initialize required variations
    if (props.menuItem?.variations) {
        props.menuItem.variations.forEach(variation => {
            if (variation.is_required && variation.options?.length > 0) {
                selectedVariations.value[variation.id] = variation.options[0].id
            }
        })
    }
})
</script>
