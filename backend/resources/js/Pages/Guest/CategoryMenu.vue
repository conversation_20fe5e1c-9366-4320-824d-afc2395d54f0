<template>
    <GuestLayout>
        <Head :title="`${category?.name || 'Category'} - ${restaurant?.name || 'Restaurant'}`" />

        <!-- Breadcrumb -->
        <section class="bg-gray-50 py-4">
            <div class="container mx-auto px-4">
                <nav class="flex items-center space-x-2 text-sm">
                    <Link :href="route('guest.home')" class="text-gray-600 hover:text-orange-600">
                        Home
                    </Link>
                    <span class="text-gray-400">/</span>
                    <Link :href="route('guest.menu')" class="text-gray-600 hover:text-orange-600">
                        Menu
                    </Link>
                    <span class="text-gray-400">/</span>
                    <span class="text-gray-800 font-semibold">{{ category?.name || 'Category' }}</span>
                </nav>
            </div>
        </section>

        <!-- Category Header -->
        <section class="bg-gray-50 py-12">
            <div class="container mx-auto px-4">
                <div class="text-center">
                    <h1 class="text-4xl font-bold text-gray-800 mb-4">{{ category?.name }}</h1>
                    <p v-if="category?.description" class="text-xl text-gray-600 mb-6">
                        {{ category.description }}
                    </p>
                    <div class="flex items-center justify-center space-x-4 text-sm text-gray-500">
                        <span v-if="menuItems?.data?.length">
                            {{ menuItems.data.length }} {{ menuItems.data.length === 1 ? 'item' : 'items' }} available
                        </span>
                        <span v-if="menuItems?.total && menuItems.total > menuItems.data?.length">
                            {{ menuItems.total }} total items
                        </span>
                    </div>
                </div>
            </div>
        </section>

        <!-- Menu Items -->
        <section class="py-12">
            <div class="container mx-auto px-4">
                <!-- Category Not Found -->
                <div v-if="!category" class="text-center py-16">
                    <div class="text-gray-400 text-6xl mb-6">
                        <i class="fas fa-search"></i>
                    </div>
                    <h3 class="text-2xl font-semibold text-gray-600 mb-2">Category Not Found</h3>
                    <p class="text-gray-500 mb-8">
                        The category you're looking for doesn't exist or has been removed.
                    </p>
                    <Link
                        :href="route('guest.menu')"
                        class="inline-block bg-orange-600 text-white px-6 py-3 rounded-lg font-semibold hover:bg-orange-700 transition-colors"
                    >
                        Browse All Categories
                    </Link>
                </div>

                <!-- No Items Message -->
                <div v-else-if="!menuItems?.data?.length" class="text-center py-16">
                    <div class="text-gray-400 text-6xl mb-6">
                        <i class="fas fa-utensils"></i>
                    </div>
                    <h3 class="text-2xl font-semibold text-gray-600 mb-2">No Items Available</h3>
                    <p class="text-gray-500 mb-8">
                        There are currently no items available in this category.
                    </p>
                    <Link
                        :href="route('guest.menu')"
                        class="inline-block bg-orange-600 text-white px-6 py-3 rounded-lg font-semibold hover:bg-orange-700 transition-colors"
                    >
                        Browse All Categories
                    </Link>
                </div>

                <!-- Menu Items Grid -->
                <div v-else class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-8">
                    <div
                        v-for="item in menuItems.data"
                        :key="item.id"
                        class="bg-white rounded-lg shadow-md overflow-hidden hover:shadow-lg transition-shadow"
                    >
                        <!-- Item Image -->
                        <div class="h-48 bg-gray-200 relative">
                            <img
                                v-if="item.primary_image_url"
                                :src="item.primary_image_url"
                                :alt="item.name"
                                class="w-full h-full object-cover"
                                @error="handleImageError"
                            />
                            <div v-else class="flex items-center justify-center h-full text-gray-400">
                                <i class="fas fa-utensils text-4xl"></i>
                            </div>
                            
                            <!-- Price Badge -->
                            <div class="absolute top-2 right-2">
                                <span class="bg-orange-600 text-white px-3 py-1 rounded-full text-sm font-semibold">
                                    ${{ item.effective_price }}
                                </span>
                            </div>

                            <!-- Availability Badge -->
                            <div v-if="!item.is_available" class="absolute inset-0 bg-black bg-opacity-50 flex items-center justify-center">
                                <span class="bg-red-600 text-white px-3 py-1 rounded text-sm font-semibold">
                                    Not Available
                                </span>
                            </div>
                        </div>

                        <!-- Item Details -->
                        <div class="p-6">
                            <h3 class="text-xl font-semibold mb-2">{{ item.name }}</h3>
                            <p class="text-gray-600 mb-4 line-clamp-2">{{ item.description }}</p>
                            
                            <!-- Item Tags -->
                            <div class="flex items-center flex-wrap gap-2 mb-4">
                                <span v-if="item.is_vegetarian" class="bg-green-100 text-green-800 text-xs px-2 py-1 rounded-full">
                                    <i class="fas fa-leaf mr-1"></i>Vegetarian
                                </span>
                                <span v-if="item.is_vegan" class="bg-green-100 text-green-800 text-xs px-2 py-1 rounded-full">
                                    <i class="fas fa-seedling mr-1"></i>Vegan
                                </span>
                                <span v-if="item.is_spicy" class="bg-red-100 text-red-800 text-xs px-2 py-1 rounded-full">
                                    <i class="fas fa-pepper-hot mr-1"></i>Spicy
                                </span>
                                <span v-if="item.is_gluten_free" class="bg-blue-100 text-blue-800 text-xs px-2 py-1 rounded-full">
                                    Gluten Free
                                </span>
                            </div>

                            <!-- Additional Info -->
                            <div class="flex items-center justify-between text-sm text-gray-500 mb-4">
                                <div v-if="item.preparation_time" class="flex items-center">
                                    <i class="fas fa-clock mr-1"></i>
                                    {{ item.preparation_time }} mins
                                </div>
                                <div v-if="item.calories" class="flex items-center">
                                    <i class="fas fa-fire mr-1"></i>
                                    {{ item.calories }} cal
                                </div>
                            </div>

                            <!-- Action Button -->
                            <div class="flex items-center justify-between">
                                <div class="text-lg font-bold text-orange-600">
                                    ${{ item.effective_price }}
                                </div>
                                <Link
                                    :href="route('guest.menu.item', item.slug)"
                                    :class="[
                                        'px-4 py-2 rounded-lg font-semibold transition-colors',
                                        item.is_available
                                            ? 'bg-orange-600 text-white hover:bg-orange-700'
                                            : 'bg-gray-300 text-gray-500 cursor-not-allowed'
                                    ]"
                                    :disabled="!item.is_available"
                                >
                                    {{ item.is_available ? 'View Details' : 'Not Available' }}
                                </Link>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Pagination -->
                <div v-if="menuItems?.links && menuItems.links.length > 3" class="mt-12">
                    <nav class="flex items-center justify-center">
                        <div class="flex flex-wrap items-center justify-center gap-2">
                            <Link
                                v-for="link in menuItems.links"
                                :key="link.label"
                                :href="link.url"
                                :class="[
                                    'px-3 py-2 rounded-lg text-sm font-medium transition-colors',
                                    link.active
                                        ? 'bg-orange-600 text-white shadow-md'
                                        : link.url
                                            ? 'bg-white text-gray-700 hover:bg-gray-50 border border-gray-300 hover:border-orange-300'
                                            : 'bg-gray-100 text-gray-400 cursor-not-allowed'
                                ]"
                                :disabled="!link.url"
                                v-html="link.label"
                            />
                        </div>
                    </nav>

                    <!-- Pagination Info -->
                    <div v-if="menuItems?.from && menuItems?.to && menuItems?.total" class="text-center mt-4 text-sm text-gray-600">
                        Showing {{ menuItems.from }} to {{ menuItems.to }} of {{ menuItems.total }} items
                    </div>
                </div>
            </div>
        </section>

        <!-- Call to Action -->
        <section class="py-16 bg-orange-600 text-white">
            <div class="container mx-auto px-4 text-center">
                <h2 class="text-3xl font-bold mb-4">Explore More Categories</h2>
                <p class="text-xl mb-8 opacity-90">
                    Discover our full menu with all available categories
                </p>
                <div class="space-x-4">
                    <Link
                        :href="route('guest.menu')"
                        class="inline-block bg-white text-orange-600 px-8 py-3 rounded-lg font-semibold hover:bg-gray-100 transition-colors"
                    >
                        <i class="fas fa-utensils mr-2"></i>
                        View Full Menu
                    </Link>
                    <Link
                        :href="route('guest.home')"
                        class="inline-block border-2 border-white text-white px-8 py-3 rounded-lg font-semibold hover:bg-white hover:text-orange-600 transition-colors"
                    >
                        Back to Home
                    </Link>
                </div>
            </div>
        </section>
    </GuestLayout>
</template>

<script setup>
import { Head, Link } from '@inertiajs/vue3'
import GuestLayout from '@/Layouts/GuestLayout.vue'

defineProps({
    restaurant: Object,
    category: Object,
    menuItems: Object, // Paginated collection
})

const handleImageError = (event) => {
    if (event.target.dataset.errorHandled) return;
    event.target.dataset.errorHandled = 'true';

    // Use inline SVG placeholder
    const svgPlaceholder = 'data:image/svg+xml;base64,' + btoa(`
        <svg xmlns="http://www.w3.org/2000/svg" width="200" height="200" viewBox="0 0 200 200">
            <rect width="200" height="200" fill="#f3f4f6"/>
            <circle cx="80" cy="70" r="12" fill="#d1d5db"/>
            <polygon points="40,140 80,100 120,120 160,80 180,140" fill="#d1d5db"/>
            <text x="100" y="170" text-anchor="middle" fill="#9ca3af" font-size="16">Menu Item</text>
        </svg>
    `);

    event.target.src = svgPlaceholder;
};
</script>

<style scoped>
.line-clamp-2 {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}
</style>
