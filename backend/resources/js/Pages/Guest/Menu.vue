<template>
    <GuestLayout>
        <Head :title="`Menu - ${restaurant?.name || 'Restaurant'}`" />

        <!-- Header -->
        <section class="bg-gray-50 py-12">
            <div class="container mx-auto px-4">
                <h1 class="text-4xl font-bold text-center mb-4">Our Menu</h1>
                <p class="text-xl text-gray-600 text-center">
                    Discover our delicious selection of dishes
                </p>
            </div>
        </section>

        <!-- Menu Categories -->
        <section class="py-12">
            <div class="container mx-auto px-4">
                <div v-if="categories.length === 0" class="text-center py-12">
                    <div class="text-gray-400 text-6xl mb-4">
                        <i class="fas fa-utensils"></i>
                    </div>
                    <h3 class="text-2xl font-semibold text-gray-600 mb-2">Menu Coming Soon</h3>
                    <p class="text-gray-500">We're working on our menu. Please check back later!</p>
                </div>

                <div v-else class="space-y-16">
                    <div
                        v-for="category in categories"
                        :key="category.id"
                        class="category-section"
                    >
                        <!-- Category Header -->
                        <div class="text-center mb-8">
                            <h2 class="text-3xl font-bold text-gray-800 mb-2">{{ category.name }}</h2>
                            <p v-if="category.description" class="text-gray-600">{{ category.description }}</p>
                        </div>

                        <!-- Menu Items -->
                        <div v-if="category.active_menu_items && category.active_menu_items.length > 0" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                            <div
                                v-for="item in category.active_menu_items"
                                :key="item.id"
                                class="bg-white rounded-lg shadow-md overflow-hidden hover:shadow-lg transition-shadow"
                            >
                                <!-- Item Image -->
                                <div class="h-48 bg-gray-200 relative">
                                    <img
                                        v-if="item.primary_image_url"
                                        :src="item.primary_image_url"
                                        :alt="item.name"
                                        class="w-full h-full object-cover"
                                        @error="handleImageError"
                                    />
                                    <div v-else class="flex items-center justify-center h-full text-gray-400">
                                        <i class="fas fa-utensils text-4xl"></i>
                                    </div>
                                    
                                    <!-- Price Badge -->
                                    <div class="absolute top-2 right-2">
                                        <span class="bg-orange-600 text-white px-3 py-1 rounded-full text-sm font-semibold">
                                            ${{ item.effective_price }}
                                        </span>
                                    </div>

                                    <!-- Availability Badge -->
                                    <div v-if="!item.is_available" class="absolute inset-0 bg-black bg-opacity-50 flex items-center justify-center">
                                        <span class="bg-red-600 text-white px-3 py-1 rounded text-sm font-semibold">
                                            Not Available
                                        </span>
                                    </div>
                                </div>

                                <!-- Item Details -->
                                <div class="p-6">
                                    <h3 class="text-xl font-semibold mb-2">{{ item.name }}</h3>
                                    <p class="text-gray-600 mb-4 line-clamp-2">{{ item.description }}</p>
                                    
                                    <!-- Item Tags -->
                                    <div class="flex items-center space-x-2 mb-4">
                                        <span v-if="item.is_vegetarian" class="bg-green-100 text-green-800 text-xs px-2 py-1 rounded-full">
                                            <i class="fas fa-leaf mr-1"></i>Vegetarian
                                        </span>
                                        <span v-if="item.is_vegan" class="bg-green-100 text-green-800 text-xs px-2 py-1 rounded-full">
                                            <i class="fas fa-seedling mr-1"></i>Vegan
                                        </span>
                                        <span v-if="item.is_spicy" class="bg-red-100 text-red-800 text-xs px-2 py-1 rounded-full">
                                            <i class="fas fa-pepper-hot mr-1"></i>Spicy
                                        </span>
                                        <span v-if="item.is_gluten_free" class="bg-blue-100 text-blue-800 text-xs px-2 py-1 rounded-full">
                                            Gluten Free
                                        </span>
                                    </div>

                                    <!-- Preparation Time -->
                                    <div v-if="item.preparation_time" class="flex items-center text-gray-500 text-sm mb-4">
                                        <i class="fas fa-clock mr-2"></i>
                                        {{ item.preparation_time }} mins
                                    </div>

                                    <!-- Action Button -->
                                    <div class="flex items-center justify-between">
                                        <div class="text-lg font-bold text-orange-600">
                                            ${{ item.effective_price }}
                                        </div>
                                        <Link
                                            :href="route('guest.menu.item', item.slug)"
                                            :class="[
                                                'px-4 py-2 rounded-lg font-semibold transition-colors',
                                                item.is_available
                                                    ? 'bg-orange-600 text-white hover:bg-orange-700'
                                                    : 'bg-gray-300 text-gray-500 cursor-not-allowed'
                                            ]"
                                            :disabled="!item.is_available"
                                        >
                                            {{ item.is_available ? 'View Details' : 'Not Available' }}
                                        </Link>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- No Items in Category -->
                        <div v-else class="text-center py-8">
                            <div class="text-gray-400 text-4xl mb-4">
                                <i class="fas fa-utensils"></i>
                            </div>
                            <p class="text-gray-500">No items available in this category</p>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Call to Action -->
        <section class="py-16 bg-orange-600 text-white">
            <div class="container mx-auto px-4 text-center">
                <h2 class="text-3xl font-bold mb-4">Ready to Order?</h2>
                <p class="text-xl mb-8 opacity-90">
                    Call us to place your order or visit our restaurant
                </p>
                <div class="space-x-4">
                    <a
                        v-if="restaurant?.phone"
                        :href="`tel:${restaurant.phone}`"
                        class="inline-block bg-white text-orange-600 px-8 py-3 rounded-lg font-semibold hover:bg-gray-100 transition-colors"
                    >
                        <i class="fas fa-phone mr-2"></i>
                        Call {{ restaurant.phone }}
                    </a>
                    <Link
                        :href="route('guest.home')"
                        class="inline-block border-2 border-white text-white px-8 py-3 rounded-lg font-semibold hover:bg-white hover:text-orange-600 transition-colors"
                    >
                        Back to Home
                    </Link>
                </div>
            </div>
        </section>
    </GuestLayout>
</template>

<script setup>
import { Head, Link } from '@inertiajs/vue3'
import GuestLayout from '@/Layouts/GuestLayout.vue'

defineProps({
    restaurant: Object,
    categories: Array,
})

const handleImageError = (event) => {
    if (event.target.dataset.errorHandled) return;
    event.target.dataset.errorHandled = 'true';

    // Use inline SVG placeholder
    const svgPlaceholder = 'data:image/svg+xml;base64,' + btoa(`
        <svg xmlns="http://www.w3.org/2000/svg" width="200" height="200" viewBox="0 0 200 200">
            <rect width="200" height="200" fill="#f3f4f6"/>
            <circle cx="80" cy="70" r="12" fill="#d1d5db"/>
            <polygon points="40,140 80,100 120,120 160,80 180,140" fill="#d1d5db"/>
            <text x="100" y="170" text-anchor="middle" fill="#9ca3af" font-size="16">Menu Item</text>
        </svg>
    `);

    event.target.src = svgPlaceholder;
};
</script>

<style scoped>
.line-clamp-2 {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}
</style>
