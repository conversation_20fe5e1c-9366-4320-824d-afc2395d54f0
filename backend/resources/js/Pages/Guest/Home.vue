<template>
    <GuestLayout>
        <Head :title="restaurant?.name || 'Restaurant'" />

        <!-- Hero Section -->
        <section class="relative bg-gradient-to-r from-orange-500 to-red-600 text-white py-20">
            <div class="container mx-auto px-4 text-center">
                <h1 class="text-4xl md:text-6xl font-bold mb-4">
                    {{ restaurant?.name || 'Welcome to Our Restaurant' }}
                </h1>
                <p class="text-xl md:text-2xl mb-8 opacity-90">
                    {{ restaurant?.description || 'Delicious food, great atmosphere, unforgettable experience' }}
                </p>
                <div class="space-x-4">
                    <Link
                        :href="route('guest.menu')"
                        class="inline-block bg-white text-orange-600 px-8 py-3 rounded-lg font-semibold hover:bg-gray-100 transition-colors"
                    >
                        View Menu
                    </Link>
                    <a
                        :href="`tel:${restaurant?.phone}`"
                        class="inline-block border-2 border-white text-white px-8 py-3 rounded-lg font-semibold hover:bg-white hover:text-orange-600 transition-colors"
                    >
                        Call Now
                    </a>
                </div>
            </div>
        </section>

        <!-- Restaurant Info -->
        <section v-if="restaurant" class="py-16 bg-gray-50">
            <div class="container mx-auto px-4">
                <div class="grid grid-cols-1 md:grid-cols-3 gap-8 text-center">
                    <div class="bg-white p-6 rounded-lg shadow-md">
                        <div class="text-orange-600 text-3xl mb-4">
                            <i class="fas fa-clock"></i>
                        </div>
                        <h3 class="text-xl font-semibold mb-2">Opening Hours</h3>
                        <p class="text-gray-600">
                            {{ restaurant.opening_time }} - {{ restaurant.closing_time }}
                        </p>
                        <p class="text-sm text-gray-500 mt-1">
                            {{ restaurant.is_open ? 'Currently Open' : 'Currently Closed' }}
                        </p>
                    </div>

                    <div class="bg-white p-6 rounded-lg shadow-md">
                        <div class="text-orange-600 text-3xl mb-4">
                            <i class="fas fa-map-marker-alt"></i>
                        </div>
                        <h3 class="text-xl font-semibold mb-2">Location</h3>
                        <p class="text-gray-600">
                            {{ restaurant.address }}<br>
                            {{ restaurant.city }}, {{ restaurant.state }}
                        </p>
                    </div>

                    <div class="bg-white p-6 rounded-lg shadow-md">
                        <div class="text-orange-600 text-3xl mb-4">
                            <i class="fas fa-phone"></i>
                        </div>
                        <h3 class="text-xl font-semibold mb-2">Contact</h3>
                        <p class="text-gray-600">
                            {{ restaurant.phone }}<br>
                            {{ restaurant.email }}
                        </p>
                    </div>
                </div>
            </div>
        </section>

        <!-- Featured Items -->
        <section v-if="featuredItems.length > 0" class="py-16">
            <div class="container mx-auto px-4">
                <h2 class="text-3xl font-bold text-center mb-12">Featured Items</h2>
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                    <div
                        v-for="item in featuredItems"
                        :key="item.id"
                        class="bg-white rounded-lg shadow-md overflow-hidden hover:shadow-lg transition-shadow"
                    >
                        <div class="h-48 bg-gray-200 relative">
                            <img
                                v-if="item.primary_image_url"
                                :src="item.primary_image_url"
                                :alt="item.name"
                                class="w-full h-full object-cover"
                                @error="handleImageError"
                            />
                            <div v-else class="flex items-center justify-center h-full text-gray-400">
                                <i class="fas fa-utensils text-4xl"></i>
                            </div>
                            <div class="absolute top-2 right-2">
                                <span class="bg-orange-600 text-white px-2 py-1 rounded text-sm font-semibold">
                                    ${{ item.effective_price }}
                                </span>
                            </div>
                        </div>
                        <div class="p-6">
                            <h3 class="text-xl font-semibold mb-2">{{ item.name }}</h3>
                            <p class="text-gray-600 mb-4">{{ item.description }}</p>
                            <div class="flex items-center justify-between">
                                <div class="flex items-center space-x-2">
                                    <span v-if="item.is_vegetarian" class="text-green-600 text-sm">
                                        <i class="fas fa-leaf"></i> Vegetarian
                                    </span>
                                    <span v-if="item.is_spicy" class="text-red-600 text-sm">
                                        <i class="fas fa-pepper-hot"></i> Spicy
                                    </span>
                                </div>
                                <Link
                                    :href="route('guest.menu.item', item.slug)"
                                    class="bg-orange-600 text-white px-4 py-2 rounded hover:bg-orange-700 transition-colors"
                                >
                                    View Details
                                </Link>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Categories -->
        <section v-if="categories.length > 0" class="py-16 bg-gray-50">
            <div class="container mx-auto px-4">
                <h2 class="text-3xl font-bold text-center mb-12">Menu Categories</h2>
                <div class="grid grid-cols-2 md:grid-cols-4 gap-6">
                    <Link
                        v-for="category in categories"
                        :key="category.id"
                        :href="route('guest.menu.category', category.slug)"
                        class="bg-white rounded-lg p-6 text-center hover:shadow-lg transition-shadow group"
                    >
                        <div class="text-orange-600 text-4xl mb-4 group-hover:scale-110 transition-transform">
                            <i class="fas fa-utensils"></i>
                        </div>
                        <h3 class="text-lg font-semibold">{{ category.name }}</h3>
                        <p class="text-sm text-gray-600 mt-1">{{ category.description }}</p>
                    </Link>
                </div>
            </div>
        </section>

        <!-- Call to Action -->
        <section class="py-16 bg-orange-600 text-white">
            <div class="container mx-auto px-4 text-center">
                <h2 class="text-3xl font-bold mb-4">Ready to Order?</h2>
                <p class="text-xl mb-8 opacity-90">
                    Browse our full menu and place your order online
                </p>
                <Link
                    :href="route('guest.menu')"
                    class="inline-block bg-white text-orange-600 px-8 py-3 rounded-lg font-semibold hover:bg-gray-100 transition-colors"
                >
                    Order Now
                </Link>
            </div>
        </section>
    </GuestLayout>
</template>

<script setup>
import { Head, Link } from '@inertiajs/vue3'
import GuestLayout from '@/Layouts/GuestLayout.vue'

defineProps({
    restaurant: Object,
    featuredItems: Array,
    categories: Array,
})

const handleImageError = (event) => {
    if (event.target.dataset.errorHandled) return;
    event.target.dataset.errorHandled = 'true';

    // Use inline SVG placeholder
    const svgPlaceholder = 'data:image/svg+xml;base64,' + btoa(`
        <svg xmlns="http://www.w3.org/2000/svg" width="200" height="200" viewBox="0 0 200 200">
            <rect width="200" height="200" fill="#f3f4f6"/>
            <circle cx="80" cy="70" r="12" fill="#d1d5db"/>
            <polygon points="40,140 80,100 120,120 160,80 180,140" fill="#d1d5db"/>
            <text x="100" y="170" text-anchor="middle" fill="#9ca3af" font-size="16">Featured Item</text>
        </svg>
    `);

    event.target.src = svgPlaceholder;
};
</script>
