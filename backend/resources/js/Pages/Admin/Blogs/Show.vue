<template>
    <AdminLayout title="Blog Post Details">
        <template #header>
            <div class="flex justify-between items-center">
                <h2 class="font-semibold text-xl text-gray-800 dark:text-gray-200 leading-tight">
                    {{ $t('blogs.show.title') }}: {{ blog.title }}
                </h2>
                <div class="flex space-x-2">
                    <Link
                        :href="route('admin.blogs.edit', blog.id)"
                        class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded"
                    >
                        <i class="fas fa-edit mr-2"></i>
                        {{ $t('common.edit') }}
                    </Link>
                    <Link
                        :href="route('admin.blogs.index')"
                        class="bg-gray-500 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded"
                    >
                        <i class="fas fa-arrow-left mr-2"></i>
                        {{ $t('common.back') }}
                    </Link>
                </div>
            </div>
        </template>

        <div class="py-12">
            <div class="max-w-4xl mx-auto sm:px-6 lg:px-8">
                <!-- Blog Overview -->
                <div class="bg-white dark:bg-gray-800 overflow-hidden shadow-xl sm:rounded-lg mb-6">
                    <div class="p-6">
                        <!-- Featured Image -->
                        <div v-if="blog.featured_image_url" class="mb-6">
                            <img 
                                :src="blog.featured_image_url" 
                                :alt="blog.title"
                                class="w-full h-64 object-cover rounded-lg"
                            />
                        </div>

                        <!-- Blog Header -->
                        <div class="mb-6">
                            <h1 class="text-3xl font-bold text-gray-900 dark:text-white mb-4">
                                {{ blog.title }}
                            </h1>
                            
                            <div class="flex flex-wrap items-center gap-4 text-sm text-gray-600 dark:text-gray-400">
                                <div class="flex items-center">
                                    <i class="fas fa-user mr-2"></i>
                                    <span>{{ blog.author?.name || $t('blogs.no_author') }}</span>
                                </div>
                                
                                <div class="flex items-center">
                                    <i class="fas fa-calendar mr-2"></i>
                                    <span>{{ formatDate(blog.created_at) }}</span>
                                </div>
                                
                                <div v-if="blog.published_at" class="flex items-center">
                                    <i class="fas fa-globe mr-2"></i>
                                    <span>{{ $t('blogs.published') }}: {{ formatDate(blog.published_at) }}</span>
                                </div>
                                
                                <div class="flex items-center">
                                    <span :class="getStatusClass(blog.status)" class="inline-flex px-2 py-1 text-xs font-semibold rounded-full">
                                        {{ $t(`blogs.${blog.status}`) }}
                                    </span>
                                </div>
                            </div>

                            <!-- Excerpt -->
                            <div v-if="blog.excerpt" class="mt-4">
                                <p class="text-lg text-gray-600 dark:text-gray-400 italic">
                                    {{ blog.excerpt }}
                                </p>
                            </div>
                        </div>

                        <!-- Blog Content -->
                        <div class="prose prose-lg max-w-none dark:prose-invert">
                            <div v-html="blog.content"></div>
                        </div>

                        <!-- Tags and Categories -->
                        <div v-if="blog.tags || blog.categories" class="mt-8 pt-6 border-t border-gray-200 dark:border-gray-700">
                            <div class="flex flex-wrap gap-6">
                                <div v-if="blog.tags && blog.tags.length > 0">
                                    <h4 class="text-sm font-medium text-gray-900 dark:text-white mb-2">
                                        {{ $t('blogs.tags') }}:
                                    </h4>
                                    <div class="flex flex-wrap gap-2">
                                        <span 
                                            v-for="tag in blog.tags" 
                                            :key="tag"
                                            class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200"
                                        >
                                            <i class="fas fa-tag mr-1"></i>
                                            {{ tag }}
                                        </span>
                                    </div>
                                </div>

                                <div v-if="blog.categories && blog.categories.length > 0">
                                    <h4 class="text-sm font-medium text-gray-900 dark:text-white mb-2">
                                        {{ $t('blogs.categories') }}:
                                    </h4>
                                    <div class="flex flex-wrap gap-2">
                                        <span 
                                            v-for="category in blog.categories" 
                                            :key="category"
                                            class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200"
                                        >
                                            <i class="fas fa-folder mr-1"></i>
                                            {{ category }}
                                        </span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Blog Details -->
                <div class="bg-white dark:bg-gray-800 overflow-hidden shadow-xl sm:rounded-lg mb-6">
                    <div class="p-6">
                        <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-4">
                            {{ $t('blogs.blog_details') }}
                        </h3>
                        
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <div>
                                <label class="block text-sm font-medium text-gray-500 dark:text-gray-400">
                                    {{ $t('blogs.slug') }}
                                </label>
                                <p class="mt-1 text-sm text-gray-900 dark:text-white">
                                    <code class="bg-gray-100 dark:bg-gray-700 px-2 py-1 rounded text-xs">
                                        /blog/{{ blog.slug }}
                                    </code>
                                </p>
                            </div>

                            <div>
                                <label class="block text-sm font-medium text-gray-500 dark:text-gray-400">
                                    {{ $t('blogs.status') }}
                                </label>
                                <span :class="getStatusClass(blog.status)" class="inline-flex px-2 py-1 text-xs font-semibold rounded-full mt-1">
                                    {{ $t(`blogs.${blog.status}`) }}
                                </span>
                            </div>

                            <div>
                                <label class="block text-sm font-medium text-gray-500 dark:text-gray-400">
                                    {{ $t('blogs.author') }}
                                </label>
                                <p class="mt-1 text-sm text-gray-900 dark:text-white">
                                    {{ blog.author?.name || $t('blogs.no_author') }}
                                </p>
                            </div>

                            <div>
                                <label class="block text-sm font-medium text-gray-500 dark:text-gray-400">
                                    {{ $t('blogs.sort_order') }}
                                </label>
                                <p class="mt-1 text-sm text-gray-900 dark:text-white">
                                    {{ blog.sort_order || 0 }}
                                </p>
                            </div>

                            <div>
                                <label class="block text-sm font-medium text-gray-500 dark:text-gray-400">
                                    {{ $t('blogs.created_at') }}
                                </label>
                                <p class="mt-1 text-sm text-gray-900 dark:text-white">
                                    {{ formatDateTime(blog.created_at) }}
                                </p>
                            </div>

                            <div>
                                <label class="block text-sm font-medium text-gray-500 dark:text-gray-400">
                                    {{ $t('blogs.updated_at') }}
                                </label>
                                <p class="mt-1 text-sm text-gray-900 dark:text-white">
                                    {{ formatDateTime(blog.updated_at) }}
                                </p>
                            </div>
                        </div>

                        <!-- Meta Description -->
                        <div v-if="blog.meta_description" class="mt-6">
                            <label class="block text-sm font-medium text-gray-500 dark:text-gray-400">
                                {{ $t('blogs.meta_description') }}
                            </label>
                            <p class="mt-1 text-sm text-gray-900 dark:text-white">
                                {{ blog.meta_description }}
                            </p>
                        </div>
                    </div>
                </div>

                <!-- Actions -->
                <div class="bg-white dark:bg-gray-800 overflow-hidden shadow-xl sm:rounded-lg">
                    <div class="p-6">
                        <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-4">
                            {{ $t('blogs.actions') }}
                        </h3>
                        
                        <div class="flex flex-wrap gap-4">
                            <Link
                                :href="route('admin.blogs.edit', blog.id)"
                                class="inline-flex items-center px-4 py-2 bg-blue-500 hover:bg-blue-700 text-white font-bold rounded"
                            >
                                <i class="fas fa-edit mr-2"></i>
                                {{ $t('common.edit') }}
                            </Link>

                            <button
                                @click="toggleStatus"
                                :class="blog.status === 'published' ? 'bg-yellow-500 hover:bg-yellow-700' : 'bg-green-500 hover:bg-green-700'"
                                class="inline-flex items-center px-4 py-2 text-white font-bold rounded"
                            >
                                <i :class="blog.status === 'published' ? 'fas fa-eye-slash' : 'fas fa-eye'" class="mr-2"></i>
                                {{ blog.status === 'published' ? $t('blogs.unpublish') : $t('blogs.publish') }}
                            </button>

                            <button
                                @click="duplicateBlog"
                                class="inline-flex items-center px-4 py-2 bg-purple-500 hover:bg-purple-700 text-white font-bold rounded"
                            >
                                <i class="fas fa-copy mr-2"></i>
                                {{ $t('blogs.duplicate') }}
                            </button>

                            <button
                                @click="deleteBlog"
                                class="inline-flex items-center px-4 py-2 bg-red-500 hover:bg-red-700 text-white font-bold rounded"
                            >
                                <i class="fas fa-trash mr-2"></i>
                                {{ $t('common.delete') }}
                            </button>

                            <a
                                v-if="blog.status === 'published'"
                                :href="getBlogUrl()"
                                target="_blank"
                                class="inline-flex items-center px-4 py-2 bg-gray-500 hover:bg-gray-700 text-white font-bold rounded"
                            >
                                <i class="fas fa-external-link-alt mr-2"></i>
                                {{ $t('blogs.view_public') }}
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </AdminLayout>
</template>

<script setup>
import { Link, router } from '@inertiajs/vue3'
import AdminLayout from '@/Layouts/AdminLayout.vue'
import { useI18n } from 'vue-i18n'

const { t } = useI18n()

const props = defineProps({
    blog: Object,
})

const formatDate = (date) => {
    return new Date(date).toLocaleDateString()
}

const formatDateTime = (date) => {
    return new Date(date).toLocaleString()
}

const getStatusClass = (status) => {
    const classes = {
        published: 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200',
        draft: 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200',
    }
    return classes[status] || classes.draft
}

const getBlogUrl = () => {
    // This would be the public URL for the blog post
    return `/blog/${props.blog.slug}`
}

const toggleStatus = () => {
    const newStatus = props.blog.status === 'published' ? 'draft' : 'published'
    const confirmMessage = newStatus === 'published' 
        ? t('blogs.confirm_publish')
        : t('blogs.confirm_unpublish')

    if (confirm(confirmMessage)) {
        router.patch(route('admin.blogs.update', props.blog.id), {
            status: newStatus,
            published_at: newStatus === 'published' ? new Date().toISOString() : null
        }, {
            preserveScroll: true,
        })
    }
}

const duplicateBlog = () => {
    if (confirm(t('blogs.confirm_duplicate'))) {
        router.post(route('admin.blogs.store'), {
            ...props.blog,
            title_en: `${props.blog.title} (Copy)`,
            slug: `${props.blog.slug}-copy`,
            status: 'draft',
            published_at: null,
        }, {
            preserveScroll: true,
        })
    }
}

const deleteBlog = () => {
    if (confirm(t('blogs.confirm_delete'))) {
        router.delete(route('admin.blogs.destroy', props.blog.id))
    }
}
</script>
