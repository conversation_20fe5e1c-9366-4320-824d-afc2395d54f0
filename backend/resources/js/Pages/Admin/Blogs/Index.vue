<template>
    <AdminLayout title="Blog Management">
        <template #header>
            <div class="flex justify-between items-center">
                <h2 class="font-semibold text-xl text-gray-800 dark:text-gray-200 leading-tight">
                    {{ $t('blogs.management') }}
                </h2>
                <Link
                    :href="route('admin.blogs.create')"
                    class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded"
                >
                    <i class="fas fa-plus mr-2"></i>
                    {{ $t('blogs.add_blog') }}
                </Link>
            </div>
        </template>

        <div class="py-12">
            <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
                <!-- Stats Cards -->
                <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
                    <div class="bg-white dark:bg-gray-800 overflow-hidden shadow-xl sm:rounded-lg p-6">
                        <div class="flex items-center">
                            <div class="flex-shrink-0">
                                <div class="w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center">
                                    <i class="fas fa-blog text-white text-sm"></i>
                                </div>
                            </div>
                            <div class="ml-4">
                                <div class="text-sm font-medium text-gray-500 dark:text-gray-400">
                                    {{ $t('blogs.total_blogs') }}
                                </div>
                                <div class="text-2xl font-bold text-gray-900 dark:text-white">
                                    {{ stats.total_blogs || 0 }}
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="bg-white dark:bg-gray-800 overflow-hidden shadow-xl sm:rounded-lg p-6">
                        <div class="flex items-center">
                            <div class="flex-shrink-0">
                                <div class="w-8 h-8 bg-green-500 rounded-full flex items-center justify-center">
                                    <i class="fas fa-check-circle text-white text-sm"></i>
                                </div>
                            </div>
                            <div class="ml-4">
                                <div class="text-sm font-medium text-gray-500 dark:text-gray-400">
                                    {{ $t('blogs.published_blogs') }}
                                </div>
                                <div class="text-2xl font-bold text-gray-900 dark:text-white">
                                    {{ stats.published_blogs || 0 }}
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="bg-white dark:bg-gray-800 overflow-hidden shadow-xl sm:rounded-lg p-6">
                        <div class="flex items-center">
                            <div class="flex-shrink-0">
                                <div class="w-8 h-8 bg-yellow-500 rounded-full flex items-center justify-center">
                                    <i class="fas fa-edit text-white text-sm"></i>
                                </div>
                            </div>
                            <div class="ml-4">
                                <div class="text-sm font-medium text-gray-500 dark:text-gray-400">
                                    {{ $t('blogs.draft_blogs') }}
                                </div>
                                <div class="text-2xl font-bold text-gray-900 dark:text-white">
                                    {{ stats.draft_blogs || 0 }}
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="bg-white dark:bg-gray-800 overflow-hidden shadow-xl sm:rounded-lg p-6">
                        <div class="flex items-center">
                            <div class="flex-shrink-0">
                                <div class="w-8 h-8 bg-purple-500 rounded-full flex items-center justify-center">
                                    <i class="fas fa-users text-white text-sm"></i>
                                </div>
                            </div>
                            <div class="ml-4">
                                <div class="text-sm font-medium text-gray-500 dark:text-gray-400">
                                    {{ $t('blogs.total_authors') }}
                                </div>
                                <div class="text-2xl font-bold text-gray-900 dark:text-white">
                                    {{ stats.total_authors || 0 }}
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Filters -->
                <div class="bg-white dark:bg-gray-800 overflow-hidden shadow-xl sm:rounded-lg mb-6">
                    <div class="p-6">
                        <div class="grid grid-cols-1 md:grid-cols-5 gap-4">
                            <div>
                                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                                    {{ $t('forms.search') }}
                                </label>
                                <input
                                    v-model="filters.search"
                                    type="text"
                                    :placeholder="$t('blogs.search_placeholder')"
                                    class="w-full rounded-md border-gray-300 dark:border-gray-700 dark:bg-gray-900 dark:text-gray-300 focus:border-indigo-500 dark:focus:border-indigo-600 focus:ring-indigo-500 dark:focus:ring-indigo-600"
                                    @input="search"
                                />
                            </div>

                            <div>
                                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                                    {{ $t('blogs.status') }}
                                </label>
                                <select
                                    v-model="filters.status"
                                    class="w-full rounded-md border-gray-300 dark:border-gray-700 dark:bg-gray-900 dark:text-gray-300 focus:border-indigo-500 dark:focus:border-indigo-600 focus:ring-indigo-500 dark:focus:ring-indigo-600"
                                    @change="search"
                                >
                                    <option value="">{{ $t('blogs.all_status') }}</option>
                                    <option value="published">{{ $t('blogs.published') }}</option>
                                    <option value="draft">{{ $t('blogs.draft') }}</option>
                                </select>
                            </div>

                            <div>
                                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                                    {{ $t('blogs.author') }}
                                </label>
                                <select
                                    v-model="filters.author_id"
                                    class="w-full rounded-md border-gray-300 dark:border-gray-700 dark:bg-gray-900 dark:text-gray-300 focus:border-indigo-500 dark:focus:border-indigo-600 focus:ring-indigo-500 dark:focus:ring-indigo-600"
                                    @change="search"
                                >
                                    <option value="">{{ $t('blogs.all_authors') }}</option>
                                    <option v-for="author in authors" :key="author.id" :value="author.id">
                                        {{ author.name }}
                                    </option>
                                </select>
                            </div>

                            <div>
                                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                                    {{ $t('blogs.sort_by') }}
                                </label>
                                <select
                                    v-model="filters.sort"
                                    class="w-full rounded-md border-gray-300 dark:border-gray-700 dark:bg-gray-900 dark:text-gray-300 focus:border-indigo-500 dark:focus:border-indigo-600 focus:ring-indigo-500 dark:focus:ring-indigo-600"
                                    @change="search"
                                >
                                    <option value="created_at">{{ $t('blogs.newest_first') }}</option>
                                    <option value="title">{{ $t('blogs.title_az') }}</option>
                                    <option value="published_at">{{ $t('blogs.published_date') }}</option>
                                </select>
                            </div>

                            <div class="flex items-end">
                                <button
                                    @click="resetFilters"
                                    class="w-full px-4 py-2 bg-gray-500 hover:bg-gray-700 text-white font-bold rounded"
                                >
                                    {{ $t('forms.reset') }}
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Bulk Actions -->
                <div v-if="selectedBlogs.length > 0" class="bg-blue-50 dark:bg-blue-900 border border-blue-200 dark:border-blue-700 rounded-lg p-4 mb-6">
                    <div class="flex items-center justify-between">
                        <div class="flex items-center">
                            <span class="text-sm text-blue-800 dark:text-blue-200">
                                {{ selectedBlogs.length }} {{ $t('blogs.selected') }}
                            </span>
                        </div>
                        <div class="flex space-x-2">
                            <button
                                @click="bulkAction('publish')"
                                class="px-3 py-1 bg-green-500 hover:bg-green-700 text-white text-sm rounded"
                            >
                                {{ $t('blogs.publish') }}
                            </button>
                            <button
                                @click="bulkAction('draft')"
                                class="px-3 py-1 bg-yellow-500 hover:bg-yellow-700 text-white text-sm rounded"
                            >
                                {{ $t('blogs.draft') }}
                            </button>
                            <button
                                @click="bulkAction('delete')"
                                class="px-3 py-1 bg-red-500 hover:bg-red-700 text-white text-sm rounded"
                            >
                                {{ $t('blogs.delete') }}
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Blogs Table -->
                <div class="bg-white dark:bg-gray-800 overflow-hidden shadow-xl sm:rounded-lg">
                    <div class="overflow-x-auto">
                        <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                            <thead class="bg-gray-50 dark:bg-gray-700">
                                <tr>
                                    <th class="px-6 py-3 text-left">
                                        <input
                                            type="checkbox"
                                            @change="toggleSelectAll"
                                            :checked="selectedBlogs.length === blogs.data.length && blogs.data.length > 0"
                                            class="rounded border-gray-300 text-indigo-600 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50"
                                        />
                                    </th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                                        {{ $t('blogs.title') }}
                                    </th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                                        {{ $t('blogs.author') }}
                                    </th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                                        {{ $t('blogs.status') }}
                                    </th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                                        {{ $t('blogs.published_at') }}
                                    </th>
                                    <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                                        {{ $t('blogs.actions') }}
                                    </th>
                                </tr>
                            </thead>
                            <tbody class="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                                <tr v-for="blog in blogs.data" :key="blog.id" class="hover:bg-gray-50 dark:hover:bg-gray-700">
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <input
                                            type="checkbox"
                                            :value="blog.id"
                                            v-model="selectedBlogs"
                                            class="rounded border-gray-300 text-indigo-600 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50"
                                        />
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="flex items-center">
                                            <div class="flex-shrink-0 h-10 w-10" v-if="blog.featured_image_url">
                                                <img class="h-10 w-10 rounded-lg object-cover" :src="blog.featured_image_url" :alt="blog.title" />
                                            </div>
                                            <div class="flex-shrink-0 h-10 w-10 bg-gray-300 rounded-lg flex items-center justify-center" v-else>
                                                <i class="fas fa-image text-gray-500"></i>
                                            </div>
                                            <div class="ml-4">
                                                <div class="text-sm font-medium text-gray-900 dark:text-white">
                                                    {{ blog.title }}
                                                </div>
                                                <div class="text-sm text-gray-500 dark:text-gray-400">
                                                    {{ blog.excerpt }}
                                                </div>
                                            </div>
                                        </div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white">
                                        {{ blog.author?.name || $t('blogs.no_author') }}
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <span :class="getStatusClass(blog.status)" class="inline-flex px-2 py-1 text-xs font-semibold rounded-full">
                                            {{ $t(`blogs.${blog.status}`) }}
                                        </span>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white">
                                        {{ blog.published_at ? formatDate(blog.published_at) : '-' }}
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                                        <div class="flex items-center justify-end space-x-2">
                                            <Link
                                                :href="route('admin.blogs.show', blog.id)"
                                                class="text-blue-600 hover:text-blue-900 dark:text-blue-400 dark:hover:text-blue-300"
                                                :title="$t('common.view')"
                                            >
                                                <i class="fas fa-eye"></i>
                                            </Link>
                                            <Link
                                                :href="route('admin.blogs.edit', blog.id)"
                                                class="text-indigo-600 hover:text-indigo-900 dark:text-indigo-400 dark:hover:text-indigo-300"
                                                :title="$t('common.edit')"
                                            >
                                                <i class="fas fa-edit"></i>
                                            </Link>
                                            <button
                                                @click="deleteBlog(blog)"
                                                class="text-red-600 hover:text-red-900 dark:text-red-400 dark:hover:text-red-300"
                                                :title="$t('common.delete')"
                                            >
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>

                    <!-- Pagination -->
                    <div class="px-6 py-4 border-t border-gray-200 dark:border-gray-700">
                        <Pagination :links="blogs.links" />
                    </div>
                </div>
            </div>
        </div>
    </AdminLayout>
</template>

<script setup>
import { ref, reactive } from 'vue'
import { Link, router } from '@inertiajs/vue3'
import AdminLayout from '@/Layouts/AdminLayout.vue'
import Pagination from '@/Components/Pagination.vue'
import { useI18n } from 'vue-i18n'

const { t } = useI18n()

// Props
const props = defineProps({
    blogs: Object,
    authors: Array,
    stats: Object,
    filters: Object,
})

// Reactive data
const filters = reactive({
    search: props.filters.search || '',
    status: props.filters.status || '',
    author_id: props.filters.author_id || '',
    sort: props.filters.sort || 'created_at',
})

const selectedBlogs = ref([])

// Methods
const search = () => {
    router.get(route('admin.blogs.index'), filters, {
        preserveState: true,
        replace: true,
    })
}

const resetFilters = () => {
    filters.search = ''
    filters.status = ''
    filters.author_id = ''
    filters.sort = 'created_at'
    search()
}

const formatDate = (date) => {
    return new Date(date).toLocaleDateString()
}

const getStatusClass = (status) => {
    const classes = {
        published: 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200',
        draft: 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200',
    }
    return classes[status] || classes.draft
}

const toggleSelectAll = () => {
    if (selectedBlogs.value.length === props.blogs.data.length) {
        selectedBlogs.value = []
    } else {
        selectedBlogs.value = props.blogs.data.map(blog => blog.id)
    }
}

const bulkAction = (action) => {
    if (selectedBlogs.value.length === 0) return

    const confirmMessage = action === 'delete' 
        ? t('blogs.confirm_bulk_delete')
        : t('blogs.confirm_bulk_action', { action: t(`blogs.${action}`) })

    if (confirm(confirmMessage)) {
        router.post(route('admin.blogs.bulk-action'), {
            action: action,
            ids: selectedBlogs.value
        }, {
            preserveScroll: true,
            onSuccess: () => {
                selectedBlogs.value = []
            }
        })
    }
}

const deleteBlog = (blog) => {
    if (confirm(t('blogs.confirm_delete'))) {
        router.delete(route('admin.blogs.destroy', blog.id), {
            preserveScroll: true,
        })
    }
}
</script>
