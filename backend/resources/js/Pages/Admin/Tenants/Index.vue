<template>
    <AdminLayout title="Tenant Management">
        <template #header>
            <h2 class="font-semibold text-xl text-gray-800 dark:text-gray-200 leading-tight">
                {{ $t('tenants.management') }}
            </h2>
        </template>

        <div class="py-12">
            <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
                <!-- Header Actions -->
                <div class="bg-white dark:bg-gray-800 overflow-hidden shadow-xl sm:rounded-lg mb-6">
                    <div class="p-6">
                        <div class="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
                            <div>
                                <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100">
                                    {{ $t('tenants.title') }}
                                </h3>
                                <p class="mt-1 text-sm text-gray-600 dark:text-gray-400">
                                    {{ $t('tenants.manage_description') }}
                                </p>
                            </div>
                            <div class="flex items-center gap-3">
                                <!-- Bulk Actions -->
                                <div v-if="selectedTenants.length > 0" class="flex items-center gap-2">
                                    <span class="text-sm text-gray-600 dark:text-gray-400">
                                        {{ selectedTenants.length }} selected
                                    </span>
                                    <button
                                        @click="showBulkDeleteModal = true"
                                        class="inline-flex items-center px-3 py-2 bg-red-600 border border-transparent rounded-md font-semibold text-xs text-white uppercase tracking-widest hover:bg-red-700 focus:bg-red-700 active:bg-red-900 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2 dark:focus:ring-offset-gray-800 transition ease-in-out duration-150"
                                        :title="$t('tenants.delete_selected')"
                                    >
                                        <TrashIcon class="w-4 h-4 mr-1" />
                                        Delete Selected
                                    </button>
                                </div>

                                <!-- Add Tenant Button -->
                                <Link
                                    :href="route('admin.tenants.create')"
                                    class="inline-flex items-center px-4 py-2 bg-blue-600 border border-transparent rounded-md font-semibold text-xs text-white uppercase tracking-widest hover:bg-blue-700 focus:bg-blue-700 active:bg-blue-900 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 dark:focus:ring-offset-gray-800 transition ease-in-out duration-150"
                                >
                                    <PlusIcon class="w-4 h-4 mr-2" />
                                    {{ $t('tenants.add_tenant') }}
                                </Link>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Filters -->
                <div class="bg-white dark:bg-gray-800 overflow-hidden shadow-xl sm:rounded-lg mb-6">
                    <div class="p-6">
                        <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
                            <!-- Search -->
                            <div>
                                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                                    {{ $t('common.search') }}
                                </label>
                                <input
                                    v-model="filters.search"
                                    type="text"
                                    :placeholder="$t('tenants.search_placeholder')"
                                    class="w-full border-gray-300 dark:border-gray-700 dark:bg-gray-900 dark:text-gray-300 focus:border-blue-500 dark:focus:border-blue-600 focus:ring-blue-500 dark:focus:ring-blue-600 rounded-md shadow-sm"
                                    @input="search"
                                />
                            </div>

                            <!-- Status Filter -->
                            <div>
                                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                                    {{ $t('tenants.status') }}
                                </label>
                                <select
                                    v-model="filters.status"
                                    class="w-full border-gray-300 dark:border-gray-700 dark:bg-gray-900 dark:text-gray-300 focus:border-blue-500 dark:focus:border-blue-600 focus:ring-blue-500 dark:focus:ring-blue-600 rounded-md shadow-sm"
                                    @change="search"
                                >
                                    <option value="">{{ $t('tenants.all_status') }}</option>
                                    <option value="trial">{{ $t('tenants.trial') }}</option>
                                    <option value="active">{{ $t('tenants.active') }}</option>
                                    <option value="inactive">{{ $t('tenants.inactive') }}</option>
                                    <option value="cancelled">{{ $t('tenants.cancelled') }}</option>
                                    <option value="expired">{{ $t('tenants.expired') }}</option>
                                </select>
                            </div>

                            <!-- Plan Filter -->
                            <div>
                                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                                    {{ $t('tenants.subscription_plan') }}
                                </label>
                                <select
                                    v-model="filters.plan"
                                    class="w-full border-gray-300 dark:border-gray-700 dark:bg-gray-900 dark:text-gray-300 focus:border-blue-500 dark:focus:border-blue-600 focus:ring-blue-500 dark:focus:ring-blue-600 rounded-md shadow-sm"
                                    @change="search"
                                >
                                    <option value="">{{ $t('tenants.all_plans') }}</option>
                                    <option v-for="plan in plans" :key="plan.id" :value="plan.id">
                                        {{ plan.name }}
                                    </option>
                                </select>
                            </div>

                            <!-- Actions -->
                            <div class="flex items-end">
                                <button
                                    @click="clearFilters"
                                    class="w-full px-4 py-2 bg-gray-600 border border-transparent rounded-md font-semibold text-xs text-white uppercase tracking-widest hover:bg-gray-700 focus:bg-gray-700 active:bg-gray-900 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2 dark:focus:ring-offset-gray-800 transition ease-in-out duration-150"
                                >
                                    {{ $t('common.clear_filters') }}
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Tenants Table -->
                <div class="bg-white dark:bg-gray-800 overflow-hidden shadow-xl sm:rounded-lg">
                    <div class="overflow-x-auto">
                        <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                            <thead class="bg-gray-50 dark:bg-gray-700">
                                <tr>
                                    <th class="px-6 py-3 text-left">
                                        <input
                                            type="checkbox"
                                            :checked="isAllSelected"
                                            @change="toggleSelectAll"
                                            class="rounded border-gray-300 text-blue-600 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50"
                                        />
                                    </th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                                        {{ $t('tenants.tenant_info') }}
                                    </th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                                        {{ $t('tenants.subscription') }}
                                    </th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                                        {{ $t('tenants.status') }}
                                    </th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                                        {{ $t('tenants.created_at') }}
                                    </th>
                                    <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                                        {{ $t('common.actions') }}
                                    </th>
                                </tr>
                            </thead>
                            <tbody class="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                                <tr v-for="tenant in tenants.data" :key="tenant.id" class="hover:bg-gray-50 dark:hover:bg-gray-700">
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <input
                                            type="checkbox"
                                            :value="tenant.id"
                                            v-model="selectedTenants"
                                            class="rounded border-gray-300 text-blue-600 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50"
                                        />
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="flex items-center">
                                            <div class="flex-shrink-0 h-10 w-10">
                                                <div class="h-10 w-10 rounded-full bg-blue-500 flex items-center justify-center">
                                                    <span class="text-white font-medium text-sm">
                                                        {{ tenant.name.charAt(0).toUpperCase() }}
                                                    </span>
                                                </div>
                                            </div>
                                            <div class="ml-4">
                                                <div class="text-sm font-medium text-gray-900 dark:text-gray-100">
                                                    {{ tenant.name }}
                                                </div>
                                                <div class="text-sm text-gray-500 dark:text-gray-400">
                                                    {{ tenant.email }}
                                                </div>
                                                <div class="text-xs text-gray-400 dark:text-gray-500">
                                                    {{ tenant.domains?.[0]?.domain }}
                                                </div>
                                            </div>
                                        </div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="text-sm text-gray-900 dark:text-gray-100">
                                            {{ tenant.subscription_plan?.name }}
                                        </div>
                                        <div class="text-sm text-gray-500 dark:text-gray-400">
                                            {{ formatCurrency(tenant.subscription_plan?.price) }}/{{ $t('common.month') }}
                                        </div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <span :class="getStatusClass(tenant.subscription_status)" class="inline-flex px-2 py-1 text-xs font-semibold rounded-full">
                                            {{ $t(`tenants.${tenant.subscription_status}`) }}
                                        </span>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                                        {{ formatDate(tenant.created_at) }}
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                                        <div class="flex items-center justify-end space-x-2">
                                            <!-- Subscription Button -->
                                            <button
                                                v-if="getSubscriptionButtonType(tenant) !== 'none'"
                                                @click="handleSubscriptionAction(tenant)"
                                                :class="getSubscriptionButtonClass(tenant)"
                                                :title="getSubscriptionButtonText(tenant)"
                                                :disabled="tenant.subscription_status === 'active' && !isSubscriptionExpired(tenant)"
                                            >
                                                <CreditCardIcon class="w-4 h-4" />
                                            </button>

                                            <!-- View Button -->
                                            <Link
                                                :href="route('admin.tenants.show', tenant.id)"
                                                class="text-blue-600 hover:text-blue-900 dark:text-blue-400 dark:hover:text-blue-300"
                                                :title="$t('common.view')"
                                            >
                                                <EyeIcon class="w-4 h-4" />
                                            </Link>

                                            <!-- Edit Button -->
                                            <Link
                                                :href="route('admin.tenants.edit', tenant.id)"
                                                class="text-yellow-600 hover:text-yellow-900 dark:text-yellow-400 dark:hover:text-yellow-300"
                                                :title="$t('common.edit')"
                                            >
                                                <PencilIcon class="w-4 h-4" />
                                            </Link>

                                            <!-- Suspend/Activate Button -->
                                            <button
                                                v-if="tenant.subscription_status === 'active'"
                                                @click="suspendTenant(tenant)"
                                                class="text-orange-600 hover:text-orange-900 dark:text-orange-400 dark:hover:text-orange-300"
                                                :title="$t('tenants.suspend')"
                                            >
                                                <PauseIcon class="w-4 h-4" />
                                            </button>
                                            <button
                                                v-else-if="tenant.subscription_status === 'inactive'"
                                                @click="activateTenant(tenant)"
                                                class="text-green-600 hover:text-green-900 dark:text-green-400 dark:hover:text-green-300"
                                                :title="$t('tenants.activate')"
                                            >
                                                <PlayIcon class="w-4 h-4" />
                                            </button>

                                            <!-- Delete Button -->
                                            <button
                                                @click="deleteTenant(tenant)"
                                                class="text-red-600 hover:text-red-900 dark:text-red-400 dark:hover:text-red-300"
                                                :title="$t('common.delete')"
                                            >
                                                <TrashIcon class="w-4 h-4" />
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>

                    <!-- Pagination -->
                    <div class="bg-white dark:bg-gray-800 px-4 py-3 border-t border-gray-200 dark:border-gray-700 sm:px-6">
                        <Pagination :links="tenants.links" />
                    </div>
                </div>
            </div>
        </div>

        <!-- Bulk Delete Confirmation Modal -->
        <div v-if="showBulkDeleteModal" class="fixed inset-0 z-50 overflow-y-auto" aria-labelledby="modal-title" role="dialog" aria-modal="true">
            <div class="flex items-end justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
                <div class="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" aria-hidden="true" @click="showBulkDeleteModal = false"></div>
                <span class="hidden sm:inline-block sm:align-middle sm:h-screen" aria-hidden="true">&#8203;</span>
                <div class="inline-block align-bottom bg-white dark:bg-gray-800 rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full">
                    <div class="bg-white dark:bg-gray-800 px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
                        <div class="sm:flex sm:items-start">
                            <div class="mx-auto flex-shrink-0 flex items-center justify-center h-12 w-12 rounded-full bg-red-100 sm:mx-0 sm:h-10 sm:w-10">
                                <ExclamationTriangleIcon class="h-6 w-6 text-red-600" aria-hidden="true" />
                            </div>
                            <div class="mt-3 text-center sm:mt-0 sm:ml-4 sm:text-left">
                                <h3 class="text-lg leading-6 font-medium text-gray-900 dark:text-gray-100" id="modal-title">
                                    Delete Selected Tenants
                                </h3>
                                <div class="mt-2">
                                    <p class="text-sm text-gray-500 dark:text-gray-400">
                                        Are you sure you want to delete {{ selectedTenants.length }} tenant(s)? This action cannot be undone and will permanently remove all tenant data including databases, files, and configurations.
                                    </p>
                                    <div class="mt-3 p-3 bg-red-50 dark:bg-red-900/20 rounded-md">
                                        <p class="text-sm text-red-800 dark:text-red-200 font-medium">
                                            ⚠️ Warning: This will permanently delete:
                                        </p>
                                        <ul class="mt-1 text-sm text-red-700 dark:text-red-300 list-disc list-inside">
                                            <li>All tenant databases and data</li>
                                            <li>Uploaded files and assets</li>
                                            <li>User accounts and configurations</li>
                                            <li>Order history and analytics</li>
                                        </ul>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="bg-gray-50 dark:bg-gray-700 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse">
                        <button
                            type="button"
                            @click="confirmBulkDelete"
                            :disabled="bulkDeleteLoading"
                            class="w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-red-600 text-base font-medium text-white hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 sm:ml-3 sm:w-auto sm:text-sm disabled:opacity-50 disabled:cursor-not-allowed"
                        >
                            <span v-if="bulkDeleteLoading" class="flex items-center">
                                <svg class="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                                    <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                                    <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                                </svg>
                                Deleting...
                            </span>
                            <span v-else>Delete {{ selectedTenants.length }} Tenant(s)</span>
                        </button>
                        <button
                            type="button"
                            @click="showBulkDeleteModal = false"
                            :disabled="bulkDeleteLoading"
                            class="mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 shadow-sm px-4 py-2 bg-white text-base font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm dark:bg-gray-600 dark:text-gray-200 dark:border-gray-500 dark:hover:bg-gray-700 disabled:opacity-50 disabled:cursor-not-allowed"
                        >
                            Cancel
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Subscription Modal -->
        <div v-if="showSubscriptionModal" class="fixed inset-0 z-50 overflow-y-auto" aria-labelledby="modal-title" role="dialog" aria-modal="true">
            <div class="flex items-end justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
                <div class="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" aria-hidden="true" @click="showSubscriptionModal = false"></div>
                <span class="hidden sm:inline-block sm:align-middle sm:h-screen" aria-hidden="true">&#8203;</span>
                <div class="inline-block align-bottom bg-white dark:bg-gray-800 rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full">
                    <div class="bg-white dark:bg-gray-800 px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
                        <div class="sm:flex sm:items-start">
                            <div class="mx-auto flex-shrink-0 flex items-center justify-center h-12 w-12 rounded-full bg-blue-100 sm:mx-0 sm:h-10 sm:w-10">
                                <CreditCardIcon class="h-6 w-6 text-blue-600" aria-hidden="true" />
                            </div>
                            <div class="mt-3 text-center sm:mt-0 sm:ml-4 sm:text-left w-full">
                                <h3 class="text-lg leading-6 font-medium text-gray-900 dark:text-gray-100" id="modal-title">
                                    {{ getSubscriptionModalTitle() }}
                                </h3>
                                <div class="mt-2">
                                    <p class="text-sm text-gray-500 dark:text-gray-400">
                                        {{ getSubscriptionModalDescription() }}
                                    </p>
                                    <div v-if="subscriptionAction === 'subscribe'" class="mt-4">
                                        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                                            Select Subscription Plan
                                        </label>
                                        <select
                                            v-model="selectedPlanId"
                                            class="w-full border-gray-300 dark:border-gray-700 dark:bg-gray-900 dark:text-gray-300 focus:border-blue-500 dark:focus:border-blue-600 focus:ring-blue-500 dark:focus:ring-blue-600 rounded-md shadow-sm"
                                        >
                                            <option value="">Select a plan...</option>
                                            <option v-for="plan in plans" :key="plan.id" :value="plan.id">
                                                {{ plan.name }} - {{ formatCurrency(plan.price) }}/month
                                            </option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="bg-gray-50 dark:bg-gray-700 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse">
                        <button
                            type="button"
                            @click="confirmSubscriptionAction"
                            :disabled="subscriptionLoading || (subscriptionAction === 'subscribe' && !selectedPlanId)"
                            class="w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-blue-600 text-base font-medium text-white hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 sm:ml-3 sm:w-auto sm:text-sm disabled:opacity-50 disabled:cursor-not-allowed"
                        >
                            <span v-if="subscriptionLoading" class="flex items-center">
                                <svg class="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                                    <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                                    <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                                </svg>
                                Processing...
                            </span>
                            <span v-else>{{ getSubscriptionButtonText(selectedTenant) }}</span>
                        </button>
                        <button
                            type="button"
                            @click="showSubscriptionModal = false"
                            :disabled="subscriptionLoading"
                            class="mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 shadow-sm px-4 py-2 bg-white text-base font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm dark:bg-gray-600 dark:text-gray-200 dark:border-gray-500 dark:hover:bg-gray-700 disabled:opacity-50 disabled:cursor-not-allowed"
                        >
                            Cancel
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </AdminLayout>
</template>

<script setup>
import { ref, reactive, computed } from 'vue'
import { Link, router } from '@inertiajs/vue3'
import AdminLayout from '@/Layouts/AdminLayout.vue'
import Pagination from '@/Components/Pagination.vue'
import {
    PlusIcon,
    EyeIcon,
    PencilIcon,
    TrashIcon,
    PauseIcon,
    PlayIcon,
    CreditCardIcon,
    ExclamationTriangleIcon,
    MagnifyingGlassIcon
} from '@heroicons/vue/24/outline'
import { useI18n } from 'vue-i18n'

const { t } = useI18n()

// Props
const props = defineProps({
    tenants: Object,
    plans: Array,
    filters: Object,
})

// Reactive data
const filters = reactive({
    search: props.filters.search || '',
    status: props.filters.status || '',
    plan: props.filters.plan || '',
})

// Bulk operations
const selectedTenants = ref([])
const showBulkDeleteModal = ref(false)
const bulkDeleteLoading = ref(false)

// Subscription management
const showSubscriptionModal = ref(false)
const selectedTenant = ref(null)
const subscriptionAction = ref('')
const selectedPlanId = ref('')
const subscriptionLoading = ref(false)

// Computed properties
const isAllSelected = computed(() => {
    return props.tenants.data.length > 0 && selectedTenants.value.length === props.tenants.data.length
})

// Methods
const search = () => {
    router.get(route('admin.tenants.index'), filters, {
        preserveState: true,
        replace: true,
    })
}

const clearFilters = () => {
    filters.search = ''
    filters.status = ''
    filters.plan = ''
    search()
}

const getStatusClass = (status) => {
    const classes = {
        trial: 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300',
        active: 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300',
        inactive: 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-300',
        cancelled: 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300',
        expired: 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300',
    }
    return classes[status] || classes.inactive
}

const formatDate = (date) => {
    return new Date(date).toLocaleDateString()
}

const formatCurrency = (amount) => {
    return new Intl.NumberFormat('en-US', {
        style: 'currency',
        currency: 'USD'
    }).format(amount)
}

const suspendTenant = (tenant) => {
    if (confirm(t('tenants.confirm_suspend'))) {
        router.post(route('admin.tenants.suspend', tenant.id))
    }
}

const activateTenant = (tenant) => {
    if (confirm(t('tenants.confirm_activate'))) {
        router.post(route('admin.tenants.activate', tenant.id))
    }
}

const deleteTenant = (tenant) => {
    if (confirm(t('tenants.confirm_delete'))) {
        router.delete(route('admin.tenants.destroy', tenant.id))
    }
}

// Bulk operations methods
const toggleSelectAll = () => {
    if (isAllSelected.value) {
        selectedTenants.value = []
    } else {
        selectedTenants.value = props.tenants.data.map(tenant => tenant.id)
    }
}

const confirmBulkDelete = () => {
    bulkDeleteLoading.value = true

    router.post(route('admin.tenants.bulk-delete'), {
        tenant_ids: selectedTenants.value
    }, {
        onSuccess: () => {
            selectedTenants.value = []
            showBulkDeleteModal.value = false
            bulkDeleteLoading.value = false
        },
        onError: () => {
            bulkDeleteLoading.value = false
        }
    })
}

// Subscription management methods
const getSubscriptionButtonType = (tenant) => {
    if (tenant.subscription_status === 'inactive' || tenant.subscription_status === 'cancelled') {
        return 'subscribe'
    } else if (tenant.subscription_status === 'expired' || isSubscriptionExpired(tenant)) {
        return 'renew'
    } else if (tenant.subscription_status === 'active') {
        return 'manage'
    }
    return 'none'
}

const getSubscriptionButtonClass = (tenant) => {
    const type = getSubscriptionButtonType(tenant)
    const baseClass = "inline-flex items-center justify-center w-8 h-8 rounded-full transition-colors duration-200"

    if (type === 'subscribe') {
        return `${baseClass} text-green-600 hover:text-green-900 hover:bg-green-100 dark:text-green-400 dark:hover:text-green-300 dark:hover:bg-green-900/20`
    } else if (type === 'renew') {
        return `${baseClass} text-yellow-600 hover:text-yellow-900 hover:bg-yellow-100 dark:text-yellow-400 dark:hover:text-yellow-300 dark:hover:bg-yellow-900/20`
    } else if (type === 'manage') {
        return `${baseClass} text-blue-600 hover:text-blue-900 hover:bg-blue-100 dark:text-blue-400 dark:hover:text-blue-300 dark:hover:bg-blue-900/20`
    }
    return `${baseClass} text-gray-400 cursor-not-allowed`
}

const getSubscriptionButtonText = (tenant) => {
    const type = getSubscriptionButtonType(tenant)

    if (type === 'subscribe') {
        return 'Subscribe'
    } else if (type === 'renew') {
        return 'Renew Subscription'
    } else if (type === 'manage') {
        return 'Manage Subscription'
    }
    return ''
}

const isSubscriptionExpired = (tenant) => {
    if (!tenant.subscription_ends_at) return false
    return new Date(tenant.subscription_ends_at) < new Date()
}

const handleSubscriptionAction = (tenant) => {
    selectedTenant.value = tenant
    subscriptionAction.value = getSubscriptionButtonType(tenant)
    selectedPlanId.value = tenant.subscription_plan_id || ''
    showSubscriptionModal.value = true
}

const getSubscriptionModalTitle = () => {
    if (subscriptionAction.value === 'subscribe') {
        return 'Subscribe Tenant'
    } else if (subscriptionAction.value === 'renew') {
        return 'Renew Subscription'
    } else if (subscriptionAction.value === 'manage') {
        return 'Manage Subscription'
    }
    return 'Subscription'
}

const getSubscriptionModalDescription = () => {
    if (subscriptionAction.value === 'subscribe') {
        return `Subscribe ${selectedTenant.value?.name} to a subscription plan.`
    } else if (subscriptionAction.value === 'renew') {
        return `Renew the subscription for ${selectedTenant.value?.name}.`
    } else if (subscriptionAction.value === 'manage') {
        return `Manage the subscription for ${selectedTenant.value?.name}.`
    }
    return ''
}

const confirmSubscriptionAction = () => {
    if (!selectedTenant.value) return

    subscriptionLoading.value = true

    if (subscriptionAction.value === 'subscribe') {
        router.post(route('admin.tenants.subscribe', selectedTenant.value.id), {
            subscription_plan_id: selectedPlanId.value
        }, {
            onSuccess: () => {
                showSubscriptionModal.value = false
                subscriptionLoading.value = false
                selectedTenant.value = null
                selectedPlanId.value = ''
            },
            onError: () => {
                subscriptionLoading.value = false
            }
        })
    } else if (subscriptionAction.value === 'renew') {
        router.post(route('admin.tenants.renew-subscription', selectedTenant.value.id), {}, {
            onSuccess: () => {
                showSubscriptionModal.value = false
                subscriptionLoading.value = false
                selectedTenant.value = null
            },
            onError: () => {
                subscriptionLoading.value = false
            }
        })
    }
}
</script>
