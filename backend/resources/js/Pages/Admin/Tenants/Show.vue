<template>
    <AdminLayout title="Tenant Details">
        <template #header>
            <div class="flex items-center justify-between">
                <h2 class="font-semibold text-xl text-gray-800 dark:text-gray-200 leading-tight">
                    {{ $t('tenants.show.title') }} - {{ tenant.name }}
                </h2>
                <div class="flex space-x-2">
                    <Link
                        :href="route('admin.tenants.edit', tenant.id)"
                        class="inline-flex items-center px-4 py-2 bg-blue-600 border border-transparent rounded-md font-semibold text-xs text-white uppercase tracking-widest hover:bg-blue-700 focus:bg-blue-700 active:bg-blue-900 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 dark:focus:ring-offset-gray-800 transition ease-in-out duration-150"
                    >
                        {{ $t('common.edit') }}
                    </Link>
                    <Link
                        :href="route('admin.tenants.index')"
                        class="inline-flex items-center px-4 py-2 bg-gray-600 border border-transparent rounded-md font-semibold text-xs text-white uppercase tracking-widest hover:bg-gray-700 focus:bg-gray-700 active:bg-gray-900 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2 dark:focus:ring-offset-gray-800 transition ease-in-out duration-150"
                    >
                        {{ $t('common.back') }}
                    </Link>
                </div>
            </div>
        </template>

        <div class="py-12">
            <div class="max-w-7xl mx-auto sm:px-6 lg:px-8 space-y-6">
                <!-- Overview Cards -->
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                    <!-- Total Orders -->
                    <div class="bg-white dark:bg-gray-800 overflow-hidden shadow rounded-lg">
                        <div class="p-5">
                            <div class="flex items-center">
                                <div class="flex-shrink-0">
                                    <svg class="h-6 w-6 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"></path>
                                    </svg>
                                </div>
                                <div class="ml-5 w-0 flex-1">
                                    <dl>
                                        <dt class="text-sm font-medium text-gray-500 dark:text-gray-400 truncate">Total Orders</dt>
                                        <dd class="text-lg font-medium text-gray-900 dark:text-gray-100">
                                            {{ analytics?.orders?.total_orders || 0 }}
                                        </dd>
                                    </dl>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Total Revenue -->
                    <div class="bg-white dark:bg-gray-800 overflow-hidden shadow rounded-lg">
                        <div class="p-5">
                            <div class="flex items-center">
                                <div class="flex-shrink-0">
                                    <svg class="h-6 w-6 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"></path>
                                    </svg>
                                </div>
                                <div class="ml-5 w-0 flex-1">
                                    <dl>
                                        <dt class="text-sm font-medium text-gray-500 dark:text-gray-400 truncate">Total Revenue</dt>
                                        <dd class="text-lg font-medium text-gray-900 dark:text-gray-100">
                                            {{ formatCurrency(analytics?.revenue?.total_revenue || 0) }}
                                        </dd>
                                    </dl>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Menu Items -->
                    <div class="bg-white dark:bg-gray-800 overflow-hidden shadow rounded-lg">
                        <div class="p-5">
                            <div class="flex items-center">
                                <div class="flex-shrink-0">
                                    <svg class="h-6 w-6 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 10h16M4 14h16M4 18h16"></path>
                                    </svg>
                                </div>
                                <div class="ml-5 w-0 flex-1">
                                    <dl>
                                        <dt class="text-sm font-medium text-gray-500 dark:text-gray-400 truncate">Menu Items</dt>
                                        <dd class="text-lg font-medium text-gray-900 dark:text-gray-100">
                                            {{ analytics?.menu?.active_menu_items || 0 }}
                                        </dd>
                                    </dl>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Staff Members -->
                    <div class="bg-white dark:bg-gray-800 overflow-hidden shadow rounded-lg">
                        <div class="p-5">
                            <div class="flex items-center">
                                <div class="flex-shrink-0">
                                    <svg class="h-6 w-6 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z"></path>
                                    </svg>
                                </div>
                                <div class="ml-5 w-0 flex-1">
                                    <dl>
                                        <dt class="text-sm font-medium text-gray-500 dark:text-gray-400 truncate">Staff Members</dt>
                                        <dd class="text-lg font-medium text-gray-900 dark:text-gray-100">
                                            {{ analytics?.staff?.active_staff || 0 }}
                                        </dd>
                                    </dl>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Usage Statistics -->
                <div v-if="usageStats" class="bg-white dark:bg-gray-800 overflow-hidden shadow-xl sm:rounded-lg">
                    <div class="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
                        <button @click="toggleSection('usage')" class="flex items-center justify-between w-full text-left">
                            <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100">
                                Plan Usage & Limits
                            </h3>
                            <svg class="h-5 w-5 transform transition-transform" :class="{ 'rotate-180': expandedSections.usage }" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                            </svg>
                        </button>
                    </div>
                    <div v-show="expandedSections.usage" class="p-6">
                        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                            <div v-for="(usage, key) in usageStats.current_usage" :key="key" class="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
                                <div class="flex items-center justify-between mb-2">
                                    <span class="text-sm font-medium text-gray-900 dark:text-gray-100 capitalize">{{ key.replace('_', ' ') }}</span>
                                    <span class="text-sm text-gray-500 dark:text-gray-400">
                                        {{ usage }} / {{ usageStats.plan_limits[key] || 'unlimited' }}
                                    </span>
                                </div>
                                <div class="w-full bg-gray-200 dark:bg-gray-600 rounded-full h-2">
                                    <div
                                        class="h-2 rounded-full transition-all duration-300"
                                        :class="getUsageColor(usageStats.usage_percentages[key] || 0)"
                                        :style="{ width: `${Math.min(usageStats.usage_percentages[key] || 0, 100)}%` }"
                                    ></div>
                                </div>
                                <div class="mt-1 text-xs text-gray-500 dark:text-gray-400">
                                    {{ formatPercentage(usageStats.usage_percentages[key] || 0) }} used
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Analytics Dashboard -->
                <div v-if="analytics" class="bg-white dark:bg-gray-800 overflow-hidden shadow-xl sm:rounded-lg">
                    <div class="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
                        <button @click="toggleSection('analytics')" class="flex items-center justify-between w-full text-left">
                            <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100">
                                Analytics Dashboard
                            </h3>
                            <svg class="h-5 w-5 transform transition-transform" :class="{ 'rotate-180': expandedSections.analytics }" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                            </svg>
                        </button>
                    </div>
                    <div v-show="expandedSections.analytics" class="p-6">
                        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                            <!-- Order Analytics -->
                            <div class="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
                                <h4 class="text-md font-medium text-gray-900 dark:text-gray-100 mb-3">Order Statistics</h4>
                                <div class="space-y-3">
                                    <div class="flex justify-between">
                                        <span class="text-sm text-gray-600 dark:text-gray-400">This Month:</span>
                                        <span class="text-sm font-medium text-gray-900 dark:text-gray-100">{{ analytics.orders?.orders_this_month || 0 }}</span>
                                    </div>
                                    <div class="flex justify-between">
                                        <span class="text-sm text-gray-600 dark:text-gray-400">Last Month:</span>
                                        <span class="text-sm font-medium text-gray-900 dark:text-gray-100">{{ analytics.orders?.orders_last_month || 0 }}</span>
                                    </div>
                                    <div class="flex justify-between">
                                        <span class="text-sm text-gray-600 dark:text-gray-400">Avg/Day:</span>
                                        <span class="text-sm font-medium text-gray-900 dark:text-gray-100">{{ analytics.orders?.average_orders_per_day || 0 }}</span>
                                    </div>
                                    <div class="flex justify-between">
                                        <span class="text-sm text-gray-600 dark:text-gray-400">Growth:</span>
                                        <span class="text-sm font-medium" :class="analytics.orders?.month_over_month_change >= 0 ? 'text-green-600' : 'text-red-600'">
                                            {{ analytics.orders?.month_over_month_change || 0 }}%
                                        </span>
                                    </div>
                                </div>
                            </div>

                            <!-- Revenue Analytics -->
                            <div class="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
                                <h4 class="text-md font-medium text-gray-900 dark:text-gray-100 mb-3">Revenue Statistics</h4>
                                <div class="space-y-3">
                                    <div class="flex justify-between">
                                        <span class="text-sm text-gray-600 dark:text-gray-400">This Month:</span>
                                        <span class="text-sm font-medium text-gray-900 dark:text-gray-100">{{ formatCurrency(analytics.revenue?.revenue_this_month || 0) }}</span>
                                    </div>
                                    <div class="flex justify-between">
                                        <span class="text-sm text-gray-600 dark:text-gray-400">Last Month:</span>
                                        <span class="text-sm font-medium text-gray-900 dark:text-gray-100">{{ formatCurrency(analytics.revenue?.revenue_last_month || 0) }}</span>
                                    </div>
                                    <div class="flex justify-between">
                                        <span class="text-sm text-gray-600 dark:text-gray-400">Avg Order Value:</span>
                                        <span class="text-sm font-medium text-gray-900 dark:text-gray-100">{{ formatCurrency(analytics.revenue?.average_order_value || 0) }}</span>
                                    </div>
                                    <div class="flex justify-between">
                                        <span class="text-sm text-gray-600 dark:text-gray-400">Growth:</span>
                                        <span class="text-sm font-medium" :class="analytics.revenue?.month_over_month_change >= 0 ? 'text-green-600' : 'text-red-600'">
                                            {{ analytics.revenue?.month_over_month_change || 0 }}%
                                        </span>
                                    </div>
                                </div>
                            </div>

                            <!-- Menu Analytics -->
                            <div class="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
                                <h4 class="text-md font-medium text-gray-900 dark:text-gray-100 mb-3">Menu Statistics</h4>
                                <div class="space-y-3">
                                    <div class="flex justify-between">
                                        <span class="text-sm text-gray-600 dark:text-gray-400">Active Items:</span>
                                        <span class="text-sm font-medium text-gray-900 dark:text-gray-100">{{ analytics.menu?.active_menu_items || 0 }}</span>
                                    </div>
                                    <div class="flex justify-between">
                                        <span class="text-sm text-gray-600 dark:text-gray-400">Inactive Items:</span>
                                        <span class="text-sm font-medium text-gray-900 dark:text-gray-100">{{ analytics.menu?.inactive_menu_items || 0 }}</span>
                                    </div>
                                    <div class="flex justify-between">
                                        <span class="text-sm text-gray-600 dark:text-gray-400">Categories:</span>
                                        <span class="text-sm font-medium text-gray-900 dark:text-gray-100">{{ analytics.menu?.active_categories || 0 }}</span>
                                    </div>
                                </div>
                                <div v-if="analytics.menu?.popular_items?.length" class="mt-4">
                                    <h5 class="text-sm font-medium text-gray-900 dark:text-gray-100 mb-2">Popular Items:</h5>
                                    <div class="space-y-1">
                                        <div v-for="item in analytics.menu.popular_items.slice(0, 3)" :key="item.name" class="flex justify-between text-xs">
                                            <span class="text-gray-600 dark:text-gray-400 truncate">{{ item.name }}</span>
                                            <span class="text-gray-900 dark:text-gray-100">{{ item.order_count }} orders</span>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Staff Analytics -->
                            <div class="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
                                <h4 class="text-md font-medium text-gray-900 dark:text-gray-100 mb-3">Staff Statistics</h4>
                                <div class="space-y-3">
                                    <div class="flex justify-between">
                                        <span class="text-sm text-gray-600 dark:text-gray-400">Active Staff:</span>
                                        <span class="text-sm font-medium text-gray-900 dark:text-gray-100">{{ analytics.staff?.active_staff || 0 }}</span>
                                    </div>
                                    <div class="flex justify-between">
                                        <span class="text-sm text-gray-600 dark:text-gray-400">Inactive Staff:</span>
                                        <span class="text-sm font-medium text-gray-900 dark:text-gray-100">{{ analytics.staff?.inactive_staff || 0 }}</span>
                                    </div>
                                </div>
                                <div v-if="analytics.staff?.role_breakdown" class="mt-4">
                                    <h5 class="text-sm font-medium text-gray-900 dark:text-gray-100 mb-2">By Role:</h5>
                                    <div class="space-y-1">
                                        <div v-for="(count, role) in analytics.staff.role_breakdown" :key="role" class="flex justify-between text-xs">
                                            <span class="text-gray-600 dark:text-gray-400 capitalize">{{ role }}:</span>
                                            <span class="text-gray-900 dark:text-gray-100">{{ count }}</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Payment History -->
                <div v-if="paymentHistory" class="bg-white dark:bg-gray-800 overflow-hidden shadow-xl sm:rounded-lg">
                    <div class="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
                        <button @click="toggleSection('payments')" class="flex items-center justify-between w-full text-left">
                            <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100">
                                Payment History
                            </h3>
                            <svg class="h-5 w-5 transform transition-transform" :class="{ 'rotate-180': expandedSections.payments }" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                            </svg>
                        </button>
                    </div>
                    <div v-show="expandedSections.payments" class="p-6">
                        <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
                            <div class="bg-gray-50 dark:bg-gray-700 rounded-lg p-4 text-center">
                                <div class="text-2xl font-bold text-gray-900 dark:text-gray-100">{{ formatCurrency(paymentHistory.total_revenue || 0) }}</div>
                                <div class="text-sm text-gray-600 dark:text-gray-400">Total Revenue</div>
                            </div>
                            <div class="bg-gray-50 dark:bg-gray-700 rounded-lg p-4 text-center">
                                <div class="text-2xl font-bold text-gray-900 dark:text-gray-100">{{ paymentHistory.payment_count || 0 }}</div>
                                <div class="text-sm text-gray-600 dark:text-gray-400">Total Payments</div>
                            </div>
                            <div class="bg-gray-50 dark:bg-gray-700 rounded-lg p-4 text-center">
                                <div class="text-2xl font-bold text-gray-900 dark:text-gray-100">{{ paymentHistory.successful_payments || 0 }}</div>
                                <div class="text-sm text-gray-600 dark:text-gray-400">Successful Payments</div>
                            </div>
                        </div>

                        <div v-if="paymentHistory.payments?.data?.length" class="overflow-x-auto">
                            <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                                <thead class="bg-gray-50 dark:bg-gray-700">
                                    <tr>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Date</th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Amount</th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Plan</th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Status</th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Method</th>
                                    </tr>
                                </thead>
                                <tbody class="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                                    <tr v-for="payment in paymentHistory.payments.data" :key="payment.id">
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100">
                                            {{ formatDate(payment.created_at) }}
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100">
                                            {{ formatCurrency(payment.amount) }}
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100">
                                            {{ payment.subscription_plan?.name || 'N/A' }}
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <span :class="getPaymentStatusClass(payment.status)" class="inline-flex px-2 py-1 text-xs font-semibold rounded-full">
                                                {{ payment.status }}
                                            </span>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100">
                                            {{ payment.payment_method || 'N/A' }}
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                        <div v-else class="text-center py-8">
                            <p class="text-gray-500 dark:text-gray-400">No payment history available</p>
                        </div>
                    </div>
                </div>

                <!-- Basic Information -->
                <div class="bg-white dark:bg-gray-800 overflow-hidden shadow-xl sm:rounded-lg">
                    <div class="p-6">
                        <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100 mb-4">
                            {{ $t('tenants.basic_info') }}
                        </h3>
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <div>
                                <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">{{ $t('tenants.restaurant_name') }}</dt>
                                <dd class="mt-1 text-sm text-gray-900 dark:text-gray-100">{{ tenant.name }}</dd>
                            </div>
                            <div>
                                <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">{{ $t('tenants.email') }}</dt>
                                <dd class="mt-1 text-sm text-gray-900 dark:text-gray-100">{{ tenant.email }}</dd>
                            </div>
                            <div>
                                <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">{{ $t('tenants.phone') }}</dt>
                                <dd class="mt-1 text-sm text-gray-900 dark:text-gray-100">{{ tenant.phone || 'N/A' }}</dd>
                            </div>
                            <div>
                                <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">{{ $t('tenants.domain') }}</dt>
                                <dd class="mt-1 text-sm text-gray-900 dark:text-gray-100">
                                    <a :href="`http://${tenant.domains?.[0]?.domain}`" target="_blank" class="text-blue-600 hover:text-blue-800">
                                        {{ tenant.domains?.[0]?.domain }}
                                    </a>
                                </dd>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Subscription Information -->
                <div class="bg-white dark:bg-gray-800 overflow-hidden shadow-xl sm:rounded-lg">
                    <div class="p-6">
                        <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100 mb-4">
                            {{ $t('tenants.subscription_info') }}
                        </h3>
                        <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                            <div>
                                <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">{{ $t('tenants.subscription_plan') }}</dt>
                                <dd class="mt-1 text-sm text-gray-900 dark:text-gray-100">{{ tenant.subscription_plan?.name }}</dd>
                            </div>
                            <div>
                                <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">{{ $t('tenants.subscription_status') }}</dt>
                                <dd class="mt-1">
                                    <span :class="getStatusClass(tenant.subscription_status)" class="inline-flex px-2 py-1 text-xs font-semibold rounded-full">
                                        {{ $t(`tenants.${tenant.subscription_status}`) }}
                                    </span>
                                </dd>
                            </div>
                            <div>
                                <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">{{ $t('tenants.monthly_price') }}</dt>
                                <dd class="mt-1 text-sm text-gray-900 dark:text-gray-100">{{ formatCurrency(tenant.subscription_plan?.price) }}</dd>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Address Information -->
                <div class="bg-white dark:bg-gray-800 overflow-hidden shadow-xl sm:rounded-lg">
                    <div class="p-6">
                        <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100 mb-4">
                            {{ $t('tenants.address_info') }}
                        </h3>
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <div>
                                <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">{{ $t('tenants.address') }}</dt>
                                <dd class="mt-1 text-sm text-gray-900 dark:text-gray-100">{{ tenant.address || 'N/A' }}</dd>
                            </div>
                            <div>
                                <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">{{ $t('tenants.city') }}</dt>
                                <dd class="mt-1 text-sm text-gray-900 dark:text-gray-100">{{ tenant.city || 'N/A' }}</dd>
                            </div>
                            <div>
                                <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">{{ $t('tenants.state') }}</dt>
                                <dd class="mt-1 text-sm text-gray-900 dark:text-gray-100">{{ tenant.state || 'N/A' }}</dd>
                            </div>
                            <div>
                                <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">{{ $t('tenants.country') }}</dt>
                                <dd class="mt-1 text-sm text-gray-900 dark:text-gray-100">{{ tenant.country || 'N/A' }}</dd>
                            </div>
                            <div>
                                <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">{{ $t('tenants.postal_code') }}</dt>
                                <dd class="mt-1 text-sm text-gray-900 dark:text-gray-100">{{ tenant.postal_code || 'N/A' }}</dd>
                            </div>
                            <div>
                                <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">{{ $t('tenants.timezone') }}</dt>
                                <dd class="mt-1 text-sm text-gray-900 dark:text-gray-100">{{ tenant.timezone || 'UTC' }}</dd>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Actions -->
                <div class="bg-white dark:bg-gray-800 overflow-hidden shadow-xl sm:rounded-lg">
                    <div class="p-6">
                        <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100 mb-4">
                            {{ $t('common.actions') }}
                        </h3>
                        <div class="flex flex-wrap gap-4">
                            <button
                                v-if="tenant.subscription_status === 'active'"
                                @click="suspendTenant"
                                class="inline-flex items-center px-4 py-2 bg-orange-600 border border-transparent rounded-md font-semibold text-xs text-white uppercase tracking-widest hover:bg-orange-700 focus:bg-orange-700 active:bg-orange-900 focus:outline-none focus:ring-2 focus:ring-orange-500 focus:ring-offset-2 dark:focus:ring-offset-gray-800 transition ease-in-out duration-150"
                            >
                                {{ $t('tenants.suspend') }}
                            </button>
                            <button
                                v-else-if="tenant.subscription_status === 'inactive'"
                                @click="activateTenant"
                                class="inline-flex items-center px-4 py-2 bg-green-600 border border-transparent rounded-md font-semibold text-xs text-white uppercase tracking-widest hover:bg-green-700 focus:bg-green-700 active:bg-green-900 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2 dark:focus:ring-offset-gray-800 transition ease-in-out duration-150"
                            >
                                {{ $t('tenants.activate') }}
                            </button>
                            <a
                                :href="`http://${tenant.domains?.[0]?.domain}`"
                                target="_blank"
                                class="inline-flex items-center px-4 py-2 bg-blue-600 border border-transparent rounded-md font-semibold text-xs text-white uppercase tracking-widest hover:bg-blue-700 focus:bg-blue-700 active:bg-blue-900 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 dark:focus:ring-offset-gray-800 transition ease-in-out duration-150"
                            >
                                {{ $t('tenants.visit_site') }}
                            </a>
                            <button
                                @click="deleteTenant"
                                class="inline-flex items-center px-4 py-2 bg-red-600 border border-transparent rounded-md font-semibold text-xs text-white uppercase tracking-widest hover:bg-red-700 focus:bg-red-700 active:bg-red-900 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2 dark:focus:ring-offset-gray-800 transition ease-in-out duration-150"
                            >
                                {{ $t('common.delete') }}
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Payment History -->
                <div v-if="tenant.payments && tenant.payments.length > 0" class="bg-white dark:bg-gray-800 overflow-hidden shadow-xl sm:rounded-lg">
                    <div class="p-6">
                        <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100 mb-4">
                            {{ $t('tenants.payment_history') }}
                        </h3>
                        <div class="overflow-x-auto">
                            <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                                <thead class="bg-gray-50 dark:bg-gray-700">
                                    <tr>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                                            {{ $t('payments.date') }}
                                        </th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                                            {{ $t('payments.amount') }}
                                        </th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                                            {{ $t('payments.status') }}
                                        </th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                                            {{ $t('payments.method') }}
                                        </th>
                                    </tr>
                                </thead>
                                <tbody class="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                                    <tr v-for="payment in tenant.payments" :key="payment.id">
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100">
                                            {{ formatDate(payment.created_at) }}
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100">
                                            {{ formatCurrency(payment.amount) }}
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <span :class="getPaymentStatusClass(payment.status)" class="inline-flex px-2 py-1 text-xs font-semibold rounded-full">
                                                {{ payment.status }}
                                            </span>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100">
                                            {{ payment.payment_method }}
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </AdminLayout>
</template>

<script setup>
import { ref, computed } from 'vue'
import { Link, router } from '@inertiajs/vue3'
import AdminLayout from '@/Layouts/AdminLayout.vue'
import { useI18n } from 'vue-i18n'

const { t } = useI18n()

// Props
const props = defineProps({
    tenant: Object,
    analytics: Object,
    paymentHistory: Object,
    usageStats: Object,
})

// Reactive state for collapsible sections
const expandedSections = ref({
    overview: true,
    analytics: true,
    payments: false,
    usage: true,
    subscription: true,
})

// Methods
const getStatusClass = (status) => {
    const classes = {
        trial: 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300',
        active: 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300',
        inactive: 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-300',
        cancelled: 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300',
        expired: 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300',
    }
    return classes[status] || classes.inactive
}

const getPaymentStatusClass = (status) => {
    const classes = {
        completed: 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300',
        pending: 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300',
        failed: 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300',
        refunded: 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-300',
    }
    return classes[status] || classes.pending
}

const formatDate = (date) => {
    return new Date(date).toLocaleDateString()
}

const formatCurrency = (amount) => {
    return new Intl.NumberFormat('en-US', {
        style: 'currency',
        currency: 'USD'
    }).format(amount)
}

const getUsageColor = (percentage) => {
    if (percentage >= 90) return 'bg-red-500'
    if (percentage >= 75) return 'bg-yellow-500'
    return 'bg-green-500'
}

const formatPercentage = (value) => {
    return `${value}%`
}

const toggleSection = (section) => {
    expandedSections.value[section] = !expandedSections.value[section]
}

// Computed properties for chart data
const orderTrendData = computed(() => {
    if (!props.analytics?.orders?.recent_trend) return []
    return props.analytics.orders.recent_trend.map(item => ({
        date: new Date(item.date).toLocaleDateString('en-US', { month: 'short', day: 'numeric' }),
        orders: item.count
    }))
})

const revenueTrendData = computed(() => {
    if (!props.analytics?.revenue?.revenue_trend) return []
    return props.analytics.revenue.revenue_trend.map(item => ({
        date: new Date(item.date).toLocaleDateString('en-US', { month: 'short', day: 'numeric' }),
        revenue: item.revenue
    }))
})

const suspendTenant = () => {
    if (confirm(t('tenants.confirm_suspend'))) {
        router.post(route('admin.tenants.suspend', props.tenant.id))
    }
}

const activateTenant = () => {
    if (confirm(t('tenants.confirm_activate'))) {
        router.post(route('admin.tenants.activate', props.tenant.id))
    }
}

const deleteTenant = () => {
    if (confirm(t('tenants.confirm_delete'))) {
        router.delete(route('admin.tenants.destroy', props.tenant.id))
    }
}
</script>
