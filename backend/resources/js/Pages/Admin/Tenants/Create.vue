<template>
    <AdminLayout title="Create Tenant">
        <template #header>
            <div class="flex items-center justify-between">
                <h2 class="font-semibold text-xl text-gray-800 dark:text-gray-200 leading-tight">
                    {{ $t('tenants.create.title') }}
                </h2>
                <Link
                    :href="route('admin.tenants.index')"
                    class="inline-flex items-center px-4 py-2 bg-gray-600 border border-transparent rounded-md font-semibold text-xs text-white uppercase tracking-widest hover:bg-gray-700 focus:bg-gray-700 active:bg-gray-900 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2 dark:focus:ring-offset-gray-800 transition ease-in-out duration-150"
                >
                    {{ $t('common.back') }}
                </Link>
            </div>
        </template>

        <div class="py-12">
            <div class="max-w-4xl mx-auto sm:px-6 lg:px-8">
                <div class="bg-white dark:bg-gray-800 overflow-hidden shadow-xl sm:rounded-lg">
                    <form @submit.prevent="submit" class="p-6 space-y-6">
                        <!-- Basic Information -->
                        <div>
                            <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100 mb-4">
                                {{ $t('tenants.basic_info') }}
                            </h3>
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                                <!-- Restaurant Name -->
                                <div>
                                    <InputLabel for="name" :value="$t('tenants.restaurant_name')" />
                                    <TextInput
                                        id="name"
                                        v-model="form.name"
                                        type="text"
                                        class="mt-1 block w-full"
                                        required
                                        autofocus
                                    />
                                    <InputError class="mt-2" :message="form.errors.name" />
                                </div>

                                <!-- Email -->
                                <div>
                                    <InputLabel for="email" :value="$t('tenants.email')" />
                                    <TextInput
                                        id="email"
                                        v-model="form.email"
                                        type="email"
                                        class="mt-1 block w-full"
                                        required
                                    />
                                    <InputError class="mt-2" :message="form.errors.email" />
                                </div>

                                <!-- Manager Password -->
                                <div>
                                    <InputLabel for="manager_password" :value="$t('tenants.manager_password')" />
                                    <TextInput
                                        id="manager_password"
                                        v-model="form.manager_password"
                                        type="password"
                                        class="mt-1 block w-full"
                                        required
                                        autocomplete="new-password"
                                    />
                                    <InputError class="mt-2" :message="form.errors.manager_password" />
                                    <p class="mt-1 text-sm text-gray-600 dark:text-gray-400">
                                        {{ $t('tenants.manager_password_help') }}
                                    </p>
                                </div>

                                <!-- Phone -->
                                <div>
                                    <InputLabel for="phone" :value="$t('tenants.phone')" />
                                    <TextInput
                                        id="phone"
                                        v-model="form.phone"
                                        type="text"
                                        class="mt-1 block w-full"
                                    />
                                    <InputError class="mt-2" :message="form.errors.phone" />
                                </div>

                                <!-- Subdomain -->
                                <div>
                                    <InputLabel for="subdomain" :value="$t('tenants.subdomain')" />
                                    <div class="mt-1 flex rounded-md shadow-sm">
                                        <TextInput
                                            id="subdomain"
                                            v-model="form.subdomain"
                                            type="text"
                                            class="flex-1 rounded-none rounded-l-md"
                                            required
                                            @input="generateSubdomain"
                                        />
                                        <span class="inline-flex items-center px-3 rounded-r-md border border-l-0 border-gray-300 bg-gray-50 text-gray-500 text-sm">
                                            .localhost:8000
                                        </span>
                                    </div>
                                    <InputError class="mt-2" :message="form.errors.subdomain" />
                                    <p class="mt-1 text-sm text-gray-500">
                                        {{ $t('tenants.subdomain_help') }}
                                    </p>
                                </div>
                            </div>
                        </div>

                        <!-- Subscription Information -->
                        <div>
                            <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100 mb-4">
                                {{ $t('tenants.subscription_info') }}
                            </h3>
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                                <!-- Subscription Plan -->
                                <div>
                                    <InputLabel for="subscription_plan_id" :value="$t('tenants.subscription_plan')" />
                                    <select
                                        id="subscription_plan_id"
                                        v-model="form.subscription_plan_id"
                                        class="mt-1 block w-full border-gray-300 dark:border-gray-700 dark:bg-gray-900 dark:text-gray-300 focus:border-blue-500 dark:focus:border-blue-600 focus:ring-blue-500 dark:focus:ring-blue-600 rounded-md shadow-sm"
                                        required
                                    >
                                        <option value="">{{ $t('tenants.select_plan') }}</option>
                                        <option v-for="plan in plans" :key="plan.id" :value="plan.id">
                                            {{ plan.name }} - {{ formatCurrency(plan.price) }}/{{ $t('common.month') }}
                                        </option>
                                    </select>
                                    <InputError class="mt-2" :message="form.errors.subscription_plan_id" />
                                </div>

                                <!-- Subscription Status -->
                                <div>
                                    <InputLabel for="subscription_status" :value="$t('tenants.subscription_status')" />
                                    <select
                                        id="subscription_status"
                                        v-model="form.subscription_status"
                                        class="mt-1 block w-full border-gray-300 dark:border-gray-700 dark:bg-gray-900 dark:text-gray-300 focus:border-blue-500 dark:focus:border-blue-600 focus:ring-blue-500 dark:focus:ring-blue-600 rounded-md shadow-sm"
                                        required
                                    >
                                        <option value="trial">{{ $t('tenants.trial') }}</option>
                                        <option value="active">{{ $t('tenants.active') }}</option>
                                        <option value="inactive">{{ $t('tenants.inactive') }}</option>
                                    </select>
                                    <InputError class="mt-2" :message="form.errors.subscription_status" />
                                </div>
                            </div>
                        </div>

                        <!-- Address Information -->
                        <div>
                            <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100 mb-4">
                                {{ $t('tenants.address_info') }}
                            </h3>
                            <div class="grid grid-cols-1 gap-6">
                                <!-- Address -->
                                <div>
                                    <InputLabel for="address" :value="$t('tenants.address')" />
                                    <textarea
                                        id="address"
                                        v-model="form.address"
                                        rows="3"
                                        class="mt-1 block w-full border-gray-300 dark:border-gray-700 dark:bg-gray-900 dark:text-gray-300 focus:border-blue-500 dark:focus:border-blue-600 focus:ring-blue-500 dark:focus:ring-blue-600 rounded-md shadow-sm"
                                    ></textarea>
                                    <InputError class="mt-2" :message="form.errors.address" />
                                </div>

                                <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                                    <!-- City -->
                                    <div>
                                        <InputLabel for="city" :value="$t('tenants.city')" />
                                        <TextInput
                                            id="city"
                                            v-model="form.city"
                                            type="text"
                                            class="mt-1 block w-full"
                                        />
                                        <InputError class="mt-2" :message="form.errors.city" />
                                    </div>

                                    <!-- State -->
                                    <div>
                                        <InputLabel for="state" :value="$t('tenants.state')" />
                                        <TextInput
                                            id="state"
                                            v-model="form.state"
                                            type="text"
                                            class="mt-1 block w-full"
                                        />
                                        <InputError class="mt-2" :message="form.errors.state" />
                                    </div>

                                    <!-- Postal Code -->
                                    <div>
                                        <InputLabel for="postal_code" :value="$t('tenants.postal_code')" />
                                        <TextInput
                                            id="postal_code"
                                            v-model="form.postal_code"
                                            type="text"
                                            class="mt-1 block w-full"
                                        />
                                        <InputError class="mt-2" :message="form.errors.postal_code" />
                                    </div>
                                </div>

                                <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                                    <!-- Country -->
                                    <div>
                                        <InputLabel for="country" :value="$t('tenants.country')" />
                                        <TextInput
                                            id="country"
                                            v-model="form.country"
                                            type="text"
                                            class="mt-1 block w-full"
                                        />
                                        <InputError class="mt-2" :message="form.errors.country" />
                                    </div>

                                    <!-- Timezone -->
                                    <div>
                                        <InputLabel for="timezone" :value="$t('tenants.timezone')" />
                                        <select
                                            id="timezone"
                                            v-model="form.timezone"
                                            class="mt-1 block w-full border-gray-300 dark:border-gray-700 dark:bg-gray-900 dark:text-gray-300 focus:border-blue-500 dark:focus:border-blue-600 focus:ring-blue-500 dark:focus:ring-blue-600 rounded-md shadow-sm"
                                        >
                                            <option value="UTC">UTC</option>
                                            <option value="Asia/Dhaka">Asia/Dhaka</option>
                                            <option value="America/New_York">America/New_York</option>
                                            <option value="Europe/London">Europe/London</option>
                                        </select>
                                        <InputError class="mt-2" :message="form.errors.timezone" />
                                    </div>

                                    <!-- Currency -->
                                    <div>
                                        <InputLabel for="currency" :value="$t('tenants.currency')" />
                                        <select
                                            id="currency"
                                            v-model="form.currency"
                                            class="mt-1 block w-full border-gray-300 dark:border-gray-700 dark:bg-gray-900 dark:text-gray-300 focus:border-blue-500 dark:focus:border-blue-600 focus:ring-blue-500 dark:focus:ring-blue-600 rounded-md shadow-sm"
                                        >
                                            <option value="USD">USD</option>
                                            <option value="BDT">BDT</option>
                                            <option value="EUR">EUR</option>
                                            <option value="GBP">GBP</option>
                                        </select>
                                        <InputError class="mt-2" :message="form.errors.currency" />
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Submit Button -->
                        <div class="flex items-center justify-end pt-6 border-t border-gray-200 dark:border-gray-700">
                            <PrimaryButton :class="{ 'opacity-25': form.processing }" :disabled="form.processing">
                                {{ $t('tenants.create_tenant') }}
                            </PrimaryButton>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </AdminLayout>
</template>

<script setup>
import { useForm } from '@inertiajs/vue3'
import { Link } from '@inertiajs/vue3'
import AdminLayout from '@/Layouts/AdminLayout.vue'
import InputError from '@/Components/InputError.vue'
import InputLabel from '@/Components/InputLabel.vue'
import PrimaryButton from '@/Components/PrimaryButton.vue'
import TextInput from '@/Components/TextInput.vue'
import { useI18n } from 'vue-i18n'

const { t } = useI18n()

// Props
const props = defineProps({
    plans: Array,
})

// Form
const form = useForm({
    name: '',
    email: '',
    manager_password: '',
    phone: '',
    subdomain: '',
    subscription_plan_id: '',
    subscription_status: 'trial',
    address: '',
    city: '',
    state: '',
    country: '',
    postal_code: '',
    timezone: 'UTC',
    currency: 'USD',
    language: 'en',
})

// Methods
const generateSubdomain = () => {
    if (form.name && !form.subdomain) {
        form.subdomain = form.name
            .toLowerCase()
            .replace(/[^a-z0-9]/g, '-')
            .replace(/-+/g, '-')
            .replace(/^-|-$/g, '')
    }
}

const formatCurrency = (amount) => {
    return new Intl.NumberFormat('en-US', {
        style: 'currency',
        currency: 'USD'
    }).format(amount)
}

const submit = () => {
    form.post(route('admin.tenants.store'))
}
</script>
