<template>
    <AdminLayout title="Admin Dashboard">
        <template #header>
            <div class="flex justify-between items-center">
                <h2 class="font-semibold text-xl text-gray-800 dark:text-gray-200 leading-tight">
                    {{ $t('admin.dashboard.title') }}
                </h2>
                <div class="flex items-center space-x-4">
                    <div class="text-sm text-gray-600 dark:text-gray-400">
                        {{ $t('admin.dashboard.last_updated') }}: {{ lastUpdated }}
                    </div>
                    <button
                        @click="refreshData"
                        class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded"
                        :disabled="loading"
                    >
                        <i class="fas fa-sync-alt" :class="{ 'fa-spin': loading }"></i>
                        {{ $t('common.refresh') }}
                    </button>
                </div>
            </div>
        </template>

        <div class="py-12">
            <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
                <!-- Stats Cards -->
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
                    <div class="bg-white dark:bg-gray-800 overflow-hidden shadow-xl sm:rounded-lg p-6">
                        <div class="flex items-center">
                            <div class="flex-shrink-0">
                                <div class="w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center">
                                    <i class="fas fa-building text-white text-sm"></i>
                                </div>
                            </div>
                            <div class="ml-4">
                                <div class="text-sm font-medium text-gray-500 dark:text-gray-400">
                                    {{ $t('admin.dashboard.total_tenants') }}
                                </div>
                                <div class="text-2xl font-bold text-gray-900 dark:text-white">
                                    {{ stats.total_tenants || 0 }}
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="bg-white dark:bg-gray-800 overflow-hidden shadow-xl sm:rounded-lg p-6">
                        <div class="flex items-center">
                            <div class="flex-shrink-0">
                                <div class="w-8 h-8 bg-green-500 rounded-full flex items-center justify-center">
                                    <i class="fas fa-check-circle text-white text-sm"></i>
                                </div>
                            </div>
                            <div class="ml-4">
                                <div class="text-sm font-medium text-gray-500 dark:text-gray-400">
                                    {{ $t('admin.dashboard.active_tenants') }}
                                </div>
                                <div class="text-2xl font-bold text-gray-900 dark:text-white">
                                    {{ stats.active_tenants || 0 }}
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="bg-white dark:bg-gray-800 overflow-hidden shadow-xl sm:rounded-lg p-6">
                        <div class="flex items-center">
                            <div class="flex-shrink-0">
                                <div class="w-8 h-8 bg-yellow-500 rounded-full flex items-center justify-center">
                                    <i class="fas fa-clock text-white text-sm"></i>
                                </div>
                            </div>
                            <div class="ml-4">
                                <div class="text-sm font-medium text-gray-500 dark:text-gray-400">
                                    {{ $t('admin.dashboard.trial_tenants') }}
                                </div>
                                <div class="text-2xl font-bold text-gray-900 dark:text-white">
                                    {{ stats.trial_tenants || 0 }}
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="bg-white dark:bg-gray-800 overflow-hidden shadow-xl sm:rounded-lg p-6">
                        <div class="flex items-center">
                            <div class="flex-shrink-0">
                                <div class="w-8 h-8 bg-purple-500 rounded-full flex items-center justify-center">
                                    <i class="fas fa-dollar-sign text-white text-sm"></i>
                                </div>
                            </div>
                            <div class="ml-4">
                                <div class="text-sm font-medium text-gray-500 dark:text-gray-400">
                                    {{ $t('admin.dashboard.monthly_revenue') }}
                                </div>
                                <div class="text-2xl font-bold text-gray-900 dark:text-white">
                                    ${{ formatCurrency(stats.monthly_revenue || 0) }}
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Charts and Tables Row -->
                <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
                    <!-- Monthly Revenue Chart -->
                    <div class="bg-white dark:bg-gray-800 overflow-hidden shadow-xl sm:rounded-lg p-6">
                        <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-4">
                            {{ $t('admin.dashboard.monthly_revenue_chart') }}
                        </h3>
                        <div class="h-64 flex items-center justify-center text-gray-500 dark:text-gray-400">
                            <div v-if="monthlyRevenue && monthlyRevenue.length > 0">
                                <!-- Chart component would go here -->
                                <div class="text-center">
                                    <i class="fas fa-chart-line text-4xl mb-2"></i>
                                    <p>{{ $t('admin.dashboard.revenue_trend') }}</p>
                                </div>
                            </div>
                            <div v-else>
                                <i class="fas fa-chart-line text-4xl mb-2"></i>
                                <p>{{ $t('admin.dashboard.no_revenue_data') }}</p>
                            </div>
                        </div>
                    </div>

                    <!-- Recent Tenants -->
                    <div class="bg-white dark:bg-gray-800 overflow-hidden shadow-xl sm:rounded-lg p-6">
                        <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-4">
                            {{ $t('admin.dashboard.recent_tenants') }}
                        </h3>
                        <div class="space-y-3">
                            <div
                                v-for="tenant in recentTenants"
                                :key="tenant.id"
                                class="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-700 rounded-lg"
                            >
                                <div class="flex items-center">
                                    <div class="w-10 h-10 bg-blue-500 rounded-full flex items-center justify-center">
                                        <span class="text-white font-bold text-sm">
                                            {{ tenant.name.charAt(0).toUpperCase() }}
                                        </span>
                                    </div>
                                    <div class="ml-3">
                                        <p class="text-sm font-medium text-gray-900 dark:text-white">
                                            {{ tenant.name }}
                                        </p>
                                        <p class="text-xs text-gray-500 dark:text-gray-400">
                                            {{ tenant.email }}
                                        </p>
                                    </div>
                                </div>
                                <div class="text-right">
                                    <span
                                        class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium"
                                        :class="getStatusClass(tenant.subscription_status)"
                                    >
                                        {{ tenant.subscription_status }}
                                    </span>
                                    <p class="text-xs text-gray-500 dark:text-gray-400 mt-1">
                                        {{ formatDate(tenant.created_at) }}
                                    </p>
                                </div>
                            </div>
                            <div v-if="!recentTenants || recentTenants.length === 0" class="text-center py-8">
                                <i class="fas fa-building text-gray-400 text-3xl mb-2"></i>
                                <p class="text-gray-500 dark:text-gray-400">{{ $t('admin.dashboard.no_recent_tenants') }}</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Recent Payments -->
                <div class="bg-white dark:bg-gray-800 overflow-hidden shadow-xl sm:rounded-lg p-6">
                    <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-4">
                        {{ $t('admin.dashboard.recent_payments') }}
                    </h3>
                    <div class="overflow-x-auto">
                        <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                            <thead class="bg-gray-50 dark:bg-gray-700">
                                <tr>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                                        {{ $t('admin.dashboard.tenant') }}
                                    </th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                                        {{ $t('admin.dashboard.amount') }}
                                    </th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                                        {{ $t('admin.dashboard.status') }}
                                    </th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                                        {{ $t('admin.dashboard.date') }}
                                    </th>
                                </tr>
                            </thead>
                            <tbody class="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                                <tr v-for="payment in recentPayments" :key="payment.id">
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white">
                                        {{ payment.tenant_name }}
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white">
                                        ${{ formatCurrency(payment.amount) }}
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <span
                                            class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium"
                                            :class="getPaymentStatusClass(payment.status)"
                                        >
                                            {{ payment.status }}
                                        </span>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                                        {{ formatDate(payment.created_at) }}
                                    </td>
                                </tr>
                                <tr v-if="!recentPayments || recentPayments.length === 0">
                                    <td colspan="4" class="px-6 py-8 text-center text-gray-500 dark:text-gray-400">
                                        <i class="fas fa-credit-card text-3xl mb-2"></i>
                                        <p>{{ $t('admin.dashboard.no_recent_payments') }}</p>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </AdminLayout>
</template>

<script>
import { defineComponent } from 'vue'
import AdminLayout from '@/Layouts/AdminLayout.vue'

export default defineComponent({
    name: 'AdminDashboard',
    components: {
        AdminLayout,
    },
    props: {
        stats: {
            type: Object,
            default: () => ({})
        },
        recentTenants: {
            type: Array,
            default: () => []
        },
        recentPayments: {
            type: Array,
            default: () => []
        },
        monthlyRevenue: {
            type: Array,
            default: () => []
        }
    },
    data() {
        return {
            loading: false,
            lastUpdated: new Date().toLocaleTimeString()
        }
    },
    methods: {
        refreshData() {
            this.loading = true
            // Simulate API call
            setTimeout(() => {
                this.loading = false
                this.lastUpdated = new Date().toLocaleTimeString()
                this.$inertia.reload({ only: ['stats', 'recentTenants', 'recentPayments', 'monthlyRevenue'] })
            }, 1000)
        },
        formatCurrency(amount) {
            return new Intl.NumberFormat('en-US', {
                minimumFractionDigits: 2,
                maximumFractionDigits: 2
            }).format(amount)
        },
        formatDate(date) {
            return new Date(date).toLocaleDateString()
        },
        getStatusClass(status) {
            const classes = {
                'active': 'bg-green-100 text-green-800',
                'trial': 'bg-yellow-100 text-yellow-800',
                'inactive': 'bg-gray-100 text-gray-800',
                'cancelled': 'bg-red-100 text-red-800',
                'expired': 'bg-red-100 text-red-800'
            }
            return classes[status] || 'bg-gray-100 text-gray-800'
        },
        getPaymentStatusClass(status) {
            const classes = {
                'completed': 'bg-green-100 text-green-800',
                'pending': 'bg-yellow-100 text-yellow-800',
                'failed': 'bg-red-100 text-red-800',
                'cancelled': 'bg-gray-100 text-gray-800'
            }
            return classes[status] || 'bg-gray-100 text-gray-800'
        }
    }
})
</script>
