<template>
    <AdminLayout title="Subscription Plans">
        <template #header>
            <div class="flex justify-between items-center">
                <h2 class="font-semibold text-xl text-gray-800 dark:text-gray-200 leading-tight">
                    {{ $t('subscription_plans.management') }}
                </h2>
                <Link
                    :href="route('admin.subscription-plans.create')"
                    class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded"
                >
                    <i class="fas fa-plus mr-2"></i>
                    {{ $t('subscription_plans.add_plan') }}
                </Link>
            </div>
        </template>

        <div class="py-12">
            <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
                <!-- Stats Cards -->
                <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
                    <div class="bg-white dark:bg-gray-800 overflow-hidden shadow-xl sm:rounded-lg p-6">
                        <div class="flex items-center">
                            <div class="flex-shrink-0">
                                <div class="w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center">
                                    <i class="fas fa-layer-group text-white text-sm"></i>
                                </div>
                            </div>
                            <div class="ml-4">
                                <div class="text-sm font-medium text-gray-500 dark:text-gray-400">
                                    {{ $t('subscription_plans.total_plans') }}
                                </div>
                                <div class="text-2xl font-bold text-gray-900 dark:text-white">
                                    {{ stats.total_plans || 0 }}
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="bg-white dark:bg-gray-800 overflow-hidden shadow-xl sm:rounded-lg p-6">
                        <div class="flex items-center">
                            <div class="flex-shrink-0">
                                <div class="w-8 h-8 bg-green-500 rounded-full flex items-center justify-center">
                                    <i class="fas fa-check-circle text-white text-sm"></i>
                                </div>
                            </div>
                            <div class="ml-4">
                                <div class="text-sm font-medium text-gray-500 dark:text-gray-400">
                                    {{ $t('subscription_plans.active_plans') }}
                                </div>
                                <div class="text-2xl font-bold text-gray-900 dark:text-white">
                                    {{ stats.active_plans || 0 }}
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="bg-white dark:bg-gray-800 overflow-hidden shadow-xl sm:rounded-lg p-6">
                        <div class="flex items-center">
                            <div class="flex-shrink-0">
                                <div class="w-8 h-8 bg-purple-500 rounded-full flex items-center justify-center">
                                    <i class="fas fa-users text-white text-sm"></i>
                                </div>
                            </div>
                            <div class="ml-4">
                                <div class="text-sm font-medium text-gray-500 dark:text-gray-400">
                                    {{ $t('subscription_plans.total_subscriptions') }}
                                </div>
                                <div class="text-2xl font-bold text-gray-900 dark:text-white">
                                    {{ stats.total_subscriptions || 0 }}
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Filters -->
                <div class="bg-white dark:bg-gray-800 overflow-hidden shadow-xl sm:rounded-lg mb-6">
                    <div class="p-6">
                        <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
                            <div>
                                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                                    {{ $t('forms.search') }}
                                </label>
                                <input
                                    v-model="filters.search"
                                    type="text"
                                    :placeholder="$t('subscription_plans.search_placeholder')"
                                    class="w-full rounded-md border-gray-300 dark:border-gray-700 dark:bg-gray-900 dark:text-gray-300 focus:border-indigo-500 dark:focus:border-indigo-600 focus:ring-indigo-500 dark:focus:ring-indigo-600"
                                    @input="search"
                                />
                            </div>

                            <div>
                                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                                    {{ $t('subscription_plans.status') }}
                                </label>
                                <select
                                    v-model="filters.status"
                                    class="w-full rounded-md border-gray-300 dark:border-gray-700 dark:bg-gray-900 dark:text-gray-300 focus:border-indigo-500 dark:focus:border-indigo-600 focus:ring-indigo-500 dark:focus:ring-indigo-600"
                                    @change="search"
                                >
                                    <option value="">{{ $t('subscription_plans.all_status') }}</option>
                                    <option value="active">{{ $t('subscription_plans.active') }}</option>
                                    <option value="inactive">{{ $t('subscription_plans.inactive') }}</option>
                                </select>
                            </div>

                            <div>
                                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                                    {{ $t('subscription_plans.billing_cycle') }}
                                </label>
                                <select
                                    v-model="filters.billing_cycle"
                                    class="w-full rounded-md border-gray-300 dark:border-gray-700 dark:bg-gray-900 dark:text-gray-300 focus:border-indigo-500 dark:focus:border-indigo-600 focus:ring-indigo-500 dark:focus:ring-indigo-600"
                                    @change="search"
                                >
                                    <option value="">{{ $t('subscription_plans.all_cycles') }}</option>
                                    <option value="monthly">{{ $t('subscription_plans.monthly') }}</option>
                                    <option value="yearly">{{ $t('subscription_plans.yearly') }}</option>
                                </select>
                            </div>

                            <div class="flex items-end">
                                <button
                                    @click="resetFilters"
                                    class="w-full px-4 py-2 bg-gray-500 hover:bg-gray-700 text-white font-bold rounded"
                                >
                                    {{ $t('forms.reset') }}
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Plans Table -->
                <div class="bg-white dark:bg-gray-800 overflow-hidden shadow-xl sm:rounded-lg">
                    <div class="overflow-x-auto">
                        <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                            <thead class="bg-gray-50 dark:bg-gray-700">
                                <tr>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                                        {{ $t('subscription_plans.plan_name') }}
                                    </th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                                        {{ $t('subscription_plans.price') }}
                                    </th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                                        {{ $t('subscription_plans.billing_cycle') }}
                                    </th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                                        {{ $t('subscription_plans.subscribers') }}
                                    </th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                                        {{ $t('subscription_plans.status') }}
                                    </th>
                                    <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                                        {{ $t('subscription_plans.actions') }}
                                    </th>
                                </tr>
                            </thead>
                            <tbody class="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                                <tr v-for="plan in plans.data" :key="plan.id" class="hover:bg-gray-50 dark:hover:bg-gray-700">
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="flex items-center">
                                            <div>
                                                <div class="text-sm font-medium text-gray-900 dark:text-white">
                                                    {{ plan.name }}
                                                </div>
                                                <div class="text-sm text-gray-500 dark:text-gray-400">
                                                    {{ plan.description }}
                                                </div>
                                            </div>
                                        </div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white">
                                        {{ formatCurrency(plan.price) }}
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white">
                                        {{ $t(`subscription_plans.${plan.billing_cycle}`) }}
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white">
                                        {{ plan.tenants_count || 0 }}
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <span :class="getStatusClass(plan.is_active)" class="inline-flex px-2 py-1 text-xs font-semibold rounded-full">
                                            {{ plan.is_active ? $t('subscription_plans.active') : $t('subscription_plans.inactive') }}
                                        </span>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                                        <div class="relative inline-block text-left">
                                            <button
                                                @click="toggleDropdown(plan.id)"
                                                class="inline-flex items-center justify-center w-8 h-8 bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm hover:bg-gray-50 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                                                :id="`dropdown-button-${plan.id}`"
                                            >
                                                <i class="fas fa-ellipsis-v text-gray-400"></i>
                                            </button>

                                            <!-- Dropdown Menu -->
                                            <div
                                                v-show="dropdownOpen === plan.id"
                                                class="absolute right-0 z-10 mt-2 w-48 bg-white dark:bg-gray-800 rounded-md shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none"
                                                role="menu"
                                            >
                                                <div class="py-1" role="none">
                                                    <Link
                                                        :href="route('admin.subscription-plans.show', plan.id)"
                                                        class="flex items-center px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700"
                                                        role="menuitem"
                                                        @click="closeDropdown"
                                                    >
                                                        <i class="fas fa-eye mr-3 text-blue-500"></i>
                                                        {{ $t('common.view') }}
                                                    </Link>
                                                    <Link
                                                        :href="route('admin.subscription-plans.edit', plan.id)"
                                                        class="flex items-center px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700"
                                                        role="menuitem"
                                                        @click="closeDropdown"
                                                    >
                                                        <i class="fas fa-edit mr-3 text-indigo-500"></i>
                                                        {{ $t('common.edit') }}
                                                    </Link>
                                                    <button
                                                        @click="toggleStatus(plan)"
                                                        class="flex items-center w-full px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700"
                                                        role="menuitem"
                                                    >
                                                        <i :class="plan.is_active ? 'fas fa-pause mr-3 text-red-500' : 'fas fa-play mr-3 text-green-500'"></i>
                                                        {{ plan.is_active ? $t('subscription_plans.deactivate') : $t('subscription_plans.activate') }}
                                                    </button>
                                                    <hr class="my-1 border-gray-200 dark:border-gray-600">
                                                    <button
                                                        @click="deletePlan(plan)"
                                                        class="flex items-center w-full px-4 py-2 text-sm text-red-600 dark:text-red-400 hover:bg-gray-100 dark:hover:bg-gray-700"
                                                        role="menuitem"
                                                    >
                                                        <i class="fas fa-trash mr-3"></i>
                                                        {{ $t('common.delete') }}
                                                    </button>
                                                </div>
                                            </div>
                                        </div>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>

                    <!-- Pagination -->
                    <div class="px-6 py-4 border-t border-gray-200 dark:border-gray-700">
                        <Pagination :links="plans.links" />
                    </div>
                </div>
            </div>
        </div>

        <!-- Delete Confirmation Modal -->
        <div v-if="showDeleteModal" class="fixed inset-0 z-50 overflow-y-auto" aria-labelledby="modal-title" role="dialog" aria-modal="true">
            <div class="flex items-end justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
                <!-- Background overlay -->
                <div class="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" aria-hidden="true" @click="cancelDelete"></div>

                <!-- Modal panel -->
                <div class="inline-block align-bottom bg-white dark:bg-gray-800 rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full">
                    <div class="bg-white dark:bg-gray-800 px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
                        <div class="sm:flex sm:items-start">
                            <div class="mx-auto flex-shrink-0 flex items-center justify-center h-12 w-12 rounded-full bg-red-100 dark:bg-red-900 sm:mx-0 sm:h-10 sm:w-10">
                                <i class="fas fa-exclamation-triangle text-red-600 dark:text-red-400"></i>
                            </div>
                            <div class="mt-3 text-center sm:mt-0 sm:ml-4 sm:text-left">
                                <h3 class="text-lg leading-6 font-medium text-gray-900 dark:text-gray-100" id="modal-title">
                                    {{ $t('subscription_plans.confirm_delete') }}
                                </h3>
                                <div class="mt-2">
                                    <p class="text-sm text-gray-500 dark:text-gray-400">
                                        Are you sure you want to delete the plan "{{ planToDelete?.name }}"? This action cannot be undone.
                                    </p>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="bg-gray-50 dark:bg-gray-700 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse">
                        <button
                            @click="confirmDelete"
                            type="button"
                            class="w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-red-600 text-base font-medium text-white hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 sm:ml-3 sm:w-auto sm:text-sm"
                        >
                            {{ $t('common.delete') }}
                        </button>
                        <button
                            @click="cancelDelete"
                            type="button"
                            class="mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 dark:border-gray-600 shadow-sm px-4 py-2 bg-white dark:bg-gray-800 text-base font-medium text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm"
                        >
                            {{ $t('common.cancel') }}
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </AdminLayout>
</template>

<script setup>
import { ref, reactive, onMounted, onUnmounted } from 'vue'
import { Link, router } from '@inertiajs/vue3'
import AdminLayout from '@/Layouts/AdminLayout.vue'
import Pagination from '@/Components/Pagination.vue'
import { useI18n } from 'vue-i18n'

const { t } = useI18n()

// Props
const props = defineProps({
    plans: Object,
    stats: Object,
    filters: Object,
})

// Reactive data
const filters = reactive({
    search: props.filters.search || '',
    status: props.filters.status || '',
    billing_cycle: props.filters.billing_cycle || '',
})

// Dropdown state
const dropdownOpen = ref(null)

// Confirmation modal state
const showDeleteModal = ref(false)
const planToDelete = ref(null)

// Bulk actions state
const selectedPlans = ref([])
const selectAll = ref(false)

// Methods
const search = () => {
    router.get(route('admin.subscription-plans.index'), filters, {
        preserveState: true,
        replace: true,
    })
}

const resetFilters = () => {
    filters.search = ''
    filters.status = ''
    filters.billing_cycle = ''
    search()
}

const formatCurrency = (amount) => {
    return new Intl.NumberFormat('en-US', {
        style: 'currency',
        currency: 'BDT',
        minimumFractionDigits: 0,
        maximumFractionDigits: 0
    }).format(amount).replace('BDT', '৳')
}

const getStatusClass = (isActive) => {
    return isActive
        ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200'
        : 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200'
}

const toggleStatus = (plan) => {
    closeDropdown()
    if (confirm(t('subscription_plans.confirm_toggle_status'))) {
        router.post(route('admin.subscription-plans.toggle-status', plan.id), {}, {
            preserveScroll: true,
        })
    }
}

const deletePlan = (plan) => {
    closeDropdown()
    planToDelete.value = plan
    showDeleteModal.value = true
}

const confirmDelete = () => {
    if (planToDelete.value) {
        router.delete(route('admin.subscription-plans.destroy', planToDelete.value.id), {
            preserveScroll: true,
            onSuccess: () => {
                showDeleteModal.value = false
                planToDelete.value = null
            },
            onError: () => {
                showDeleteModal.value = false
                planToDelete.value = null
            }
        })
    }
}

const cancelDelete = () => {
    showDeleteModal.value = false
    planToDelete.value = null
}

// Dropdown methods
const toggleDropdown = (planId) => {
    dropdownOpen.value = dropdownOpen.value === planId ? null : planId
}

const closeDropdown = () => {
    dropdownOpen.value = null
}

// Click outside to close dropdown
const handleClickOutside = (event) => {
    if (!event.target.closest('.relative')) {
        closeDropdown()
    }
}

// Lifecycle hooks
onMounted(() => {
    document.addEventListener('click', handleClickOutside)
})

onUnmounted(() => {
    document.removeEventListener('click', handleClickOutside)
})

// Bulk actions methods
const toggleSelectAll = () => {
    if (selectAll.value) {
        selectedPlans.value = props.plans.data.map(plan => plan.id)
    } else {
        selectedPlans.value = []
    }
}

const togglePlanSelection = (planId) => {
    const index = selectedPlans.value.indexOf(planId)
    if (index > -1) {
        selectedPlans.value.splice(index, 1)
    } else {
        selectedPlans.value.push(planId)
    }

    // Update select all checkbox
    selectAll.value = selectedPlans.value.length === props.plans.data.length
}

const bulkActivate = () => {
    if (selectedPlans.value.length === 0) return

    if (confirm(`Activate ${selectedPlans.value.length} selected plans?`)) {
        router.post(route('admin.subscription-plans.bulk-action'), {
            action: 'activate',
            plan_ids: selectedPlans.value
        }, {
            preserveScroll: true,
            onSuccess: () => {
                selectedPlans.value = []
                selectAll.value = false
            }
        })
    }
}

const bulkDeactivate = () => {
    if (selectedPlans.value.length === 0) return

    if (confirm(`Deactivate ${selectedPlans.value.length} selected plans?`)) {
        router.post(route('admin.subscription-plans.bulk-action'), {
            action: 'deactivate',
            plan_ids: selectedPlans.value
        }, {
            preserveScroll: true,
            onSuccess: () => {
                selectedPlans.value = []
                selectAll.value = false
            }
        })
    }
}
</script>
