<template>
    <AdminLayout title="Create Subscription Plan">
        <template #header>
            <div class="flex justify-between items-center">
                <h2 class="font-semibold text-xl text-gray-800 dark:text-gray-200 leading-tight">
                    {{ $t('subscription_plans.create.title') }}
                </h2>
                <Link
                    :href="route('admin.subscription-plans.index')"
                    class="bg-gray-500 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded"
                >
                    <i class="fas fa-arrow-left mr-2"></i>
                    {{ $t('common.back') }}
                </Link>
            </div>
        </template>

        <div class="py-12">
            <div class="max-w-4xl mx-auto sm:px-6 lg:px-8">
                <form @submit.prevent="submit">
                    <div class="bg-white dark:bg-gray-800 overflow-hidden shadow-xl sm:rounded-lg">
                        <div class="p-6">
                            <!-- Basic Information -->
                            <div class="mb-8">
                                <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-4">
                                    {{ $t('subscription_plans.basic_information') }}
                                </h3>
                                
                                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                                            {{ $t('subscription_plans.plan_name') }} <span class="text-red-500">*</span>
                                        </label>
                                        <input
                                            v-model="form.name"
                                            type="text"
                                            :placeholder="$t('subscription_plans.plan_name_placeholder')"
                                            class="w-full rounded-md border-gray-300 dark:border-gray-700 dark:bg-gray-900 dark:text-gray-300 focus:border-indigo-500 dark:focus:border-indigo-600 focus:ring-indigo-500 dark:focus:ring-indigo-600"
                                            :class="{ 'border-red-500': form.errors.name }"
                                        />
                                        <div v-if="form.errors.name" class="text-red-500 text-sm mt-1">
                                            {{ form.errors.name }}
                                        </div>
                                    </div>

                                    <div>
                                        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                                            {{ $t('subscription_plans.price') }} <span class="text-red-500">*</span>
                                        </label>
                                        <div class="relative">
                                            <span class="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-500">৳</span>
                                            <input
                                                v-model="form.price"
                                                type="number"
                                                step="0.01"
                                                min="0"
                                                :placeholder="$t('subscription_plans.price_placeholder')"
                                                class="w-full pl-8 rounded-md border-gray-300 dark:border-gray-700 dark:bg-gray-900 dark:text-gray-300 focus:border-indigo-500 dark:focus:border-indigo-600 focus:ring-indigo-500 dark:focus:ring-indigo-600"
                                                :class="{ 'border-red-500': form.errors.price }"
                                            />
                                        </div>
                                        <div v-if="form.errors.price" class="text-red-500 text-sm mt-1">
                                            {{ form.errors.price }}
                                        </div>
                                    </div>

                                    <div>
                                        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                                            {{ $t('subscription_plans.billing_cycle') }} <span class="text-red-500">*</span>
                                        </label>
                                        <select
                                            v-model="form.billing_cycle"
                                            class="w-full rounded-md border-gray-300 dark:border-gray-700 dark:bg-gray-900 dark:text-gray-300 focus:border-indigo-500 dark:focus:border-indigo-600 focus:ring-indigo-500 dark:focus:ring-indigo-600"
                                            :class="{ 'border-red-500': form.errors.billing_cycle }"
                                        >
                                            <option value="">{{ $t('subscription_plans.select_billing_cycle') }}</option>
                                            <option value="monthly">{{ $t('subscription_plans.monthly') }}</option>
                                            <option value="yearly">{{ $t('subscription_plans.yearly') }}</option>
                                        </select>
                                        <div v-if="form.errors.billing_cycle" class="text-red-500 text-sm mt-1">
                                            {{ form.errors.billing_cycle }}
                                        </div>
                                    </div>

                                    <div>
                                        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                                            {{ $t('subscription_plans.trial_days') }} <span class="text-red-500">*</span>
                                        </label>
                                        <input
                                            v-model="form.trial_days"
                                            type="number"
                                            min="0"
                                            max="365"
                                            :placeholder="$t('subscription_plans.trial_days_placeholder')"
                                            class="w-full rounded-md border-gray-300 dark:border-gray-700 dark:bg-gray-900 dark:text-gray-300 focus:border-indigo-500 dark:focus:border-indigo-600 focus:ring-indigo-500 dark:focus:ring-indigo-600"
                                            :class="{ 'border-red-500': form.errors.trial_days }"
                                        />
                                        <div v-if="form.errors.trial_days" class="text-red-500 text-sm mt-1">
                                            {{ form.errors.trial_days }}
                                        </div>
                                    </div>
                                </div>

                                <div class="mt-6">
                                    <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                                        {{ $t('subscription_plans.description') }} <span class="text-red-500">*</span>
                                    </label>
                                    <textarea
                                        v-model="form.description"
                                        rows="3"
                                        :placeholder="$t('subscription_plans.description_placeholder')"
                                        class="w-full rounded-md border-gray-300 dark:border-gray-700 dark:bg-gray-900 dark:text-gray-300 focus:border-indigo-500 dark:focus:border-indigo-600 focus:ring-indigo-500 dark:focus:ring-indigo-600"
                                        :class="{ 'border-red-500': form.errors.description }"
                                    ></textarea>
                                    <div v-if="form.errors.description" class="text-red-500 text-sm mt-1">
                                        {{ form.errors.description }}
                                    </div>
                                </div>
                            </div>

                            <!-- Features -->
                            <div class="mb-8">
                                <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-4">
                                    {{ $t('subscription_plans.features') }}
                                </h3>
                                
                                <div class="space-y-3">
                                    <div v-for="(feature, index) in form.features" :key="index" class="flex items-center space-x-3">
                                        <input
                                            v-model="form.features[index]"
                                            type="text"
                                            :placeholder="$t('subscription_plans.feature_placeholder')"
                                            class="flex-1 rounded-md border-gray-300 dark:border-gray-700 dark:bg-gray-900 dark:text-gray-300 focus:border-indigo-500 dark:focus:border-indigo-600 focus:ring-indigo-500 dark:focus:ring-indigo-600"
                                        />
                                        <button
                                            type="button"
                                            @click="removeFeature(index)"
                                            class="text-red-600 hover:text-red-900"
                                            :disabled="form.features.length <= 1"
                                        >
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </div>
                                    <button
                                        type="button"
                                        @click="addFeature"
                                        class="text-blue-600 hover:text-blue-900 text-sm"
                                    >
                                        <i class="fas fa-plus mr-1"></i>
                                        {{ $t('subscription_plans.add_feature') }}
                                    </button>
                                </div>
                                <div v-if="form.errors.features" class="text-red-500 text-sm mt-1">
                                    {{ form.errors.features }}
                                </div>
                            </div>

                            <!-- Limits -->
                            <div class="mb-8">
                                <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-4">
                                    {{ $t('subscription_plans.limits') }}
                                </h3>
                                
                                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                                            {{ $t('subscription_plans.max_orders_per_month') }}
                                        </label>
                                        <input
                                            v-model="form.max_orders_per_month"
                                            type="number"
                                            min="1"
                                            :placeholder="$t('subscription_plans.unlimited')"
                                            class="w-full rounded-md border-gray-300 dark:border-gray-700 dark:bg-gray-900 dark:text-gray-300 focus:border-indigo-500 dark:focus:border-indigo-600 focus:ring-indigo-500 dark:focus:ring-indigo-600"
                                        />
                                    </div>

                                    <div>
                                        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                                            {{ $t('subscription_plans.max_menu_items') }}
                                        </label>
                                        <input
                                            v-model="form.max_menu_items"
                                            type="number"
                                            min="1"
                                            :placeholder="$t('subscription_plans.unlimited')"
                                            class="w-full rounded-md border-gray-300 dark:border-gray-700 dark:bg-gray-900 dark:text-gray-300 focus:border-indigo-500 dark:focus:border-indigo-600 focus:ring-indigo-500 dark:focus:ring-indigo-600"
                                        />
                                    </div>

                                    <div>
                                        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                                            {{ $t('subscription_plans.max_tables') }}
                                        </label>
                                        <input
                                            v-model="form.max_tables"
                                            type="number"
                                            min="1"
                                            :placeholder="$t('subscription_plans.unlimited')"
                                            class="w-full rounded-md border-gray-300 dark:border-gray-700 dark:bg-gray-900 dark:text-gray-300 focus:border-indigo-500 dark:focus:border-indigo-600 focus:ring-indigo-500 dark:focus:ring-indigo-600"
                                        />
                                    </div>

                                    <div>
                                        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                                            {{ $t('subscription_plans.max_staff') }}
                                        </label>
                                        <input
                                            v-model="form.max_staff"
                                            type="number"
                                            min="1"
                                            :placeholder="$t('subscription_plans.unlimited')"
                                            class="w-full rounded-md border-gray-300 dark:border-gray-700 dark:bg-gray-900 dark:text-gray-300 focus:border-indigo-500 dark:focus:border-indigo-600 focus:ring-indigo-500 dark:focus:ring-indigo-600"
                                        />
                                    </div>
                                </div>
                            </div>

                            <!-- Features Toggles -->
                            <div class="mb-8">
                                <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-4">
                                    {{ $t('subscription_plans.advanced_features') }}
                                </h3>
                                
                                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                                    <div class="flex items-center">
                                        <input
                                            v-model="form.has_delivery"
                                            type="checkbox"
                                            class="rounded border-gray-300 text-indigo-600 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50"
                                        />
                                        <label class="ml-2 text-sm text-gray-700 dark:text-gray-300">
                                            {{ $t('subscription_plans.has_delivery') }}
                                        </label>
                                    </div>

                                    <div class="flex items-center">
                                        <input
                                            v-model="form.has_analytics"
                                            type="checkbox"
                                            class="rounded border-gray-300 text-indigo-600 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50"
                                        />
                                        <label class="ml-2 text-sm text-gray-700 dark:text-gray-300">
                                            {{ $t('subscription_plans.has_analytics') }}
                                        </label>
                                    </div>

                                    <div class="flex items-center">
                                        <input
                                            v-model="form.has_multi_location"
                                            type="checkbox"
                                            class="rounded border-gray-300 text-indigo-600 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50"
                                        />
                                        <label class="ml-2 text-sm text-gray-700 dark:text-gray-300">
                                            {{ $t('subscription_plans.has_multi_location') }}
                                        </label>
                                    </div>

                                    <div class="flex items-center">
                                        <input
                                            v-model="form.has_api_access"
                                            type="checkbox"
                                            class="rounded border-gray-300 text-indigo-600 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50"
                                        />
                                        <label class="ml-2 text-sm text-gray-700 dark:text-gray-300">
                                            {{ $t('subscription_plans.has_api_access') }}
                                        </label>
                                    </div>

                                    <div class="flex items-center">
                                        <input
                                            v-model="form.is_active"
                                            type="checkbox"
                                            class="rounded border-gray-300 text-indigo-600 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50"
                                        />
                                        <label class="ml-2 text-sm text-gray-700 dark:text-gray-300">
                                            {{ $t('subscription_plans.is_active') }}
                                        </label>
                                    </div>
                                </div>
                            </div>

                            <!-- Actions -->
                            <div class="flex justify-end space-x-4">
                                <Link
                                    :href="route('admin.subscription-plans.index')"
                                    class="px-4 py-2 bg-gray-500 hover:bg-gray-700 text-white font-bold rounded"
                                >
                                    {{ $t('forms.cancel') }}
                                </Link>
                                <button
                                    type="submit"
                                    :disabled="form.processing"
                                    class="px-4 py-2 bg-blue-500 hover:bg-blue-700 text-white font-bold rounded disabled:opacity-50"
                                >
                                    <i class="fas fa-save mr-2"></i>
                                    {{ form.processing ? $t('forms.saving') : $t('forms.save') }}
                                </button>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </AdminLayout>
</template>

<script setup>
import { useForm, Link } from '@inertiajs/vue3'
import AdminLayout from '@/Layouts/AdminLayout.vue'
import { useI18n } from 'vue-i18n'

const { t } = useI18n()

const form = useForm({
    name: '',
    description: '',
    price: '',
    billing_cycle: '',
    trial_days: 14,
    features: [''],
    max_orders_per_month: null,
    max_menu_items: null,
    max_tables: null,
    max_staff: null,
    has_delivery: false,
    has_analytics: false,
    has_multi_location: false,
    has_api_access: false,
    is_active: true,
})

const submit = () => {
    form.post(route('admin.subscription-plans.store'))
}

const addFeature = () => {
    form.features.push('')
}

const removeFeature = (index) => {
    if (form.features.length > 1) {
        form.features.splice(index, 1)
    }
}
</script>
