<template>
    <AdminLayout title="Subscription Plan Details">
        <template #header>
            <div class="flex justify-between items-center">
                <h2 class="font-semibold text-xl text-gray-800 dark:text-gray-200 leading-tight">
                    {{ $t('subscription_plans.show.title') }}: {{ plan.name }}
                </h2>
                <div class="flex space-x-2">
                    <Link
                        :href="route('admin.subscription-plans.edit', plan.id)"
                        class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded"
                    >
                        <i class="fas fa-edit mr-2"></i>
                        {{ $t('common.edit') }}
                    </Link>
                    <Link
                        :href="route('admin.subscription-plans.index')"
                        class="bg-gray-500 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded"
                    >
                        <i class="fas fa-arrow-left mr-2"></i>
                        {{ $t('common.back') }}
                    </Link>
                </div>
            </div>
        </template>

        <div class="py-12">
            <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
                <!-- Plan Overview -->
                <div class="bg-white dark:bg-gray-800 overflow-hidden shadow-xl sm:rounded-lg mb-6">
                    <div class="p-6">
                        <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
                            <!-- Plan Details -->
                            <div class="lg:col-span-2">
                                <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-4">
                                    {{ $t('subscription_plans.plan_details') }}
                                </h3>
                                
                                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                    <div>
                                        <label class="block text-sm font-medium text-gray-500 dark:text-gray-400">
                                            {{ $t('subscription_plans.plan_name') }}
                                        </label>
                                        <p class="mt-1 text-sm text-gray-900 dark:text-white">{{ plan.name }}</p>
                                    </div>

                                    <div>
                                        <label class="block text-sm font-medium text-gray-500 dark:text-gray-400">
                                            {{ $t('subscription_plans.price') }}
                                        </label>
                                        <p class="mt-1 text-sm text-gray-900 dark:text-white">{{ formatCurrency(plan.price) }}</p>
                                    </div>

                                    <div>
                                        <label class="block text-sm font-medium text-gray-500 dark:text-gray-400">
                                            {{ $t('subscription_plans.billing_cycle') }}
                                        </label>
                                        <p class="mt-1 text-sm text-gray-900 dark:text-white">{{ $t(`subscription_plans.${plan.billing_cycle}`) }}</p>
                                    </div>

                                    <div>
                                        <label class="block text-sm font-medium text-gray-500 dark:text-gray-400">
                                            {{ $t('subscription_plans.trial_days') }}
                                        </label>
                                        <p class="mt-1 text-sm text-gray-900 dark:text-white">{{ plan.trial_days }} {{ $t('subscription_plans.days') }}</p>
                                    </div>

                                    <div>
                                        <label class="block text-sm font-medium text-gray-500 dark:text-gray-400">
                                            {{ $t('subscription_plans.status') }}
                                        </label>
                                        <span :class="getStatusClass(plan.is_active)" class="inline-flex px-2 py-1 text-xs font-semibold rounded-full mt-1">
                                            {{ plan.is_active ? $t('subscription_plans.active') : $t('subscription_plans.inactive') }}
                                        </span>
                                    </div>

                                    <div>
                                        <label class="block text-sm font-medium text-gray-500 dark:text-gray-400">
                                            {{ $t('subscription_plans.created_at') }}
                                        </label>
                                        <p class="mt-1 text-sm text-gray-900 dark:text-white">{{ formatDate(plan.created_at) }}</p>
                                    </div>
                                </div>

                                <div class="mt-6">
                                    <label class="block text-sm font-medium text-gray-500 dark:text-gray-400">
                                        {{ $t('subscription_plans.description') }}
                                    </label>
                                    <p class="mt-1 text-sm text-gray-900 dark:text-white">{{ plan.description }}</p>
                                </div>
                            </div>

                            <!-- Statistics -->
                            <div>
                                <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-4">
                                    {{ $t('subscription_plans.statistics') }}
                                </h3>
                                
                                <div class="space-y-4">
                                    <div class="bg-blue-50 dark:bg-blue-900 p-4 rounded-lg">
                                        <div class="flex items-center">
                                            <div class="flex-shrink-0">
                                                <i class="fas fa-users text-blue-600 dark:text-blue-400"></i>
                                            </div>
                                            <div class="ml-3">
                                                <p class="text-sm font-medium text-blue-900 dark:text-blue-100">
                                                    {{ $t('subscription_plans.total_subscribers') }}
                                                </p>
                                                <p class="text-lg font-bold text-blue-600 dark:text-blue-400">
                                                    {{ stats.total_tenants || 0 }}
                                                </p>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="bg-green-50 dark:bg-green-900 p-4 rounded-lg">
                                        <div class="flex items-center">
                                            <div class="flex-shrink-0">
                                                <i class="fas fa-check-circle text-green-600 dark:text-green-400"></i>
                                            </div>
                                            <div class="ml-3">
                                                <p class="text-sm font-medium text-green-900 dark:text-green-100">
                                                    {{ $t('subscription_plans.active_subscribers') }}
                                                </p>
                                                <p class="text-lg font-bold text-green-600 dark:text-green-400">
                                                    {{ stats.active_tenants || 0 }}
                                                </p>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="bg-yellow-50 dark:bg-yellow-900 p-4 rounded-lg">
                                        <div class="flex items-center">
                                            <div class="flex-shrink-0">
                                                <i class="fas fa-clock text-yellow-600 dark:text-yellow-400"></i>
                                            </div>
                                            <div class="ml-3">
                                                <p class="text-sm font-medium text-yellow-900 dark:text-yellow-100">
                                                    {{ $t('subscription_plans.trial_subscribers') }}
                                                </p>
                                                <p class="text-lg font-bold text-yellow-600 dark:text-yellow-400">
                                                    {{ stats.trial_tenants || 0 }}
                                                </p>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="bg-purple-50 dark:bg-purple-900 p-4 rounded-lg">
                                        <div class="flex items-center">
                                            <div class="flex-shrink-0">
                                                <i class="fas fa-dollar-sign text-purple-600 dark:text-purple-400"></i>
                                            </div>
                                            <div class="ml-3">
                                                <p class="text-sm font-medium text-purple-900 dark:text-purple-100">
                                                    {{ $t('subscription_plans.monthly_revenue') }}
                                                </p>
                                                <p class="text-lg font-bold text-purple-600 dark:text-purple-400">
                                                    {{ formatCurrency(stats.monthly_revenue || 0) }}
                                                </p>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Features -->
                <div class="bg-white dark:bg-gray-800 overflow-hidden shadow-xl sm:rounded-lg mb-6">
                    <div class="p-6">
                        <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-4">
                            {{ $t('subscription_plans.features') }}
                        </h3>
                        
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div v-for="feature in plan.features" :key="feature" class="flex items-center">
                                <i class="fas fa-check text-green-500 mr-2"></i>
                                <span class="text-sm text-gray-900 dark:text-white">{{ feature }}</span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Limits & Advanced Features -->
                <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
                    <!-- Limits -->
                    <div class="bg-white dark:bg-gray-800 overflow-hidden shadow-xl sm:rounded-lg">
                        <div class="p-6">
                            <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-4">
                                {{ $t('subscription_plans.limits') }}
                            </h3>
                            
                            <div class="space-y-3">
                                <div class="flex justify-between">
                                    <span class="text-sm text-gray-500 dark:text-gray-400">{{ $t('subscription_plans.max_orders_per_month') }}</span>
                                    <span class="text-sm text-gray-900 dark:text-white">
                                        {{ plan.max_orders_per_month || $t('subscription_plans.unlimited') }}
                                    </span>
                                </div>
                                <div class="flex justify-between">
                                    <span class="text-sm text-gray-500 dark:text-gray-400">{{ $t('subscription_plans.max_menu_items') }}</span>
                                    <span class="text-sm text-gray-900 dark:text-white">
                                        {{ plan.max_menu_items || $t('subscription_plans.unlimited') }}
                                    </span>
                                </div>
                                <div class="flex justify-between">
                                    <span class="text-sm text-gray-500 dark:text-gray-400">{{ $t('subscription_plans.max_tables') }}</span>
                                    <span class="text-sm text-gray-900 dark:text-white">
                                        {{ plan.max_tables || $t('subscription_plans.unlimited') }}
                                    </span>
                                </div>
                                <div class="flex justify-between">
                                    <span class="text-sm text-gray-500 dark:text-gray-400">{{ $t('subscription_plans.max_staff') }}</span>
                                    <span class="text-sm text-gray-900 dark:text-white">
                                        {{ plan.max_staff || $t('subscription_plans.unlimited') }}
                                    </span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Advanced Features -->
                    <div class="bg-white dark:bg-gray-800 overflow-hidden shadow-xl sm:rounded-lg">
                        <div class="p-6">
                            <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-4">
                                {{ $t('subscription_plans.advanced_features') }}
                            </h3>
                            
                            <div class="space-y-3">
                                <div class="flex justify-between items-center">
                                    <span class="text-sm text-gray-500 dark:text-gray-400">{{ $t('subscription_plans.has_delivery') }}</span>
                                    <span :class="plan.has_delivery ? 'text-green-600' : 'text-red-600'">
                                        <i :class="plan.has_delivery ? 'fas fa-check' : 'fas fa-times'"></i>
                                    </span>
                                </div>
                                <div class="flex justify-between items-center">
                                    <span class="text-sm text-gray-500 dark:text-gray-400">{{ $t('subscription_plans.has_analytics') }}</span>
                                    <span :class="plan.has_analytics ? 'text-green-600' : 'text-red-600'">
                                        <i :class="plan.has_analytics ? 'fas fa-check' : 'fas fa-times'"></i>
                                    </span>
                                </div>
                                <div class="flex justify-between items-center">
                                    <span class="text-sm text-gray-500 dark:text-gray-400">{{ $t('subscription_plans.has_multi_location') }}</span>
                                    <span :class="plan.has_multi_location ? 'text-green-600' : 'text-red-600'">
                                        <i :class="plan.has_multi_location ? 'fas fa-check' : 'fas fa-times'"></i>
                                    </span>
                                </div>
                                <div class="flex justify-between items-center">
                                    <span class="text-sm text-gray-500 dark:text-gray-400">{{ $t('subscription_plans.has_api_access') }}</span>
                                    <span :class="plan.has_api_access ? 'text-green-600' : 'text-red-600'">
                                        <i :class="plan.has_api_access ? 'fas fa-check' : 'fas fa-times'"></i>
                                    </span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Recent Subscribers -->
                <div class="bg-white dark:bg-gray-800 overflow-hidden shadow-xl sm:rounded-lg">
                    <div class="p-6">
                        <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-4">
                            {{ $t('subscription_plans.recent_subscribers') }}
                        </h3>
                        
                        <div v-if="plan.tenants && plan.tenants.length > 0" class="overflow-x-auto">
                            <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                                <thead class="bg-gray-50 dark:bg-gray-700">
                                    <tr>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                                            {{ $t('tenants.tenant_name') }}
                                        </th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                                            {{ $t('tenants.status') }}
                                        </th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                                            {{ $t('tenants.subscribed_at') }}
                                        </th>
                                        <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                                            {{ $t('tenants.actions') }}
                                        </th>
                                    </tr>
                                </thead>
                                <tbody class="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                                    <tr v-for="tenant in plan.tenants" :key="tenant.id" class="hover:bg-gray-50 dark:hover:bg-gray-700">
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white">
                                            {{ tenant.name }}
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <span :class="getTenantStatusClass(tenant.status)" class="inline-flex px-2 py-1 text-xs font-semibold rounded-full">
                                                {{ $t(`tenants.${tenant.status}`) }}
                                            </span>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white">
                                            {{ formatDate(tenant.created_at) }}
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                                            <Link
                                                :href="route('admin.tenants.show', tenant.id)"
                                                class="text-blue-600 hover:text-blue-900 dark:text-blue-400 dark:hover:text-blue-300"
                                            >
                                                {{ $t('common.view') }}
                                            </Link>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                        <div v-else class="text-center py-8">
                            <p class="text-gray-500 dark:text-gray-400">{{ $t('subscription_plans.no_subscribers') }}</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </AdminLayout>
</template>

<script setup>
import { Link } from '@inertiajs/vue3'
import AdminLayout from '@/Layouts/AdminLayout.vue'
import { useI18n } from 'vue-i18n'

const { t } = useI18n()

const props = defineProps({
    plan: Object,
    stats: Object,
})

const formatCurrency = (amount) => {
    return new Intl.NumberFormat('en-US', {
        style: 'currency',
        currency: 'BDT',
        minimumFractionDigits: 0,
        maximumFractionDigits: 0
    }).format(amount).replace('BDT', '৳')
}

const formatDate = (date) => {
    return new Date(date).toLocaleDateString()
}

const getStatusClass = (isActive) => {
    return isActive
        ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200'
        : 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200'
}

const getTenantStatusClass = (status) => {
    const classes = {
        active: 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200',
        trial: 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200',
        suspended: 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200',
        expired: 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200',
    }
    return classes[status] || classes.active
}
</script>
