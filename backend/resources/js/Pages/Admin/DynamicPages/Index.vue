<template>
    <AdminLayout title="Page Management">
        <template #header>
            <div class="flex justify-between items-center">
                <h2 class="font-semibold text-xl text-gray-800 dark:text-gray-200 leading-tight">
                    {{ $t('pages.management') }}
                </h2>
                <Link
                    :href="route('admin.dynamic-pages.create')"
                    class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded"
                >
                    <i class="fas fa-plus mr-2"></i>
                    {{ $t('pages.add_page') }}
                </Link>
            </div>
        </template>

        <div class="py-12">
            <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
                <!-- Stats Cards -->
                <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
                    <div class="bg-white dark:bg-gray-800 overflow-hidden shadow-xl sm:rounded-lg p-6">
                        <div class="flex items-center">
                            <div class="flex-shrink-0">
                                <div class="w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center">
                                    <i class="fas fa-file-alt text-white text-sm"></i>
                                </div>
                            </div>
                            <div class="ml-4">
                                <div class="text-sm font-medium text-gray-500 dark:text-gray-400">
                                    {{ $t('pages.total_pages') }}
                                </div>
                                <div class="text-2xl font-bold text-gray-900 dark:text-white">
                                    {{ stats.total_pages || 0 }}
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="bg-white dark:bg-gray-800 overflow-hidden shadow-xl sm:rounded-lg p-6">
                        <div class="flex items-center">
                            <div class="flex-shrink-0">
                                <div class="w-8 h-8 bg-green-500 rounded-full flex items-center justify-center">
                                    <i class="fas fa-check-circle text-white text-sm"></i>
                                </div>
                            </div>
                            <div class="ml-4">
                                <div class="text-sm font-medium text-gray-500 dark:text-gray-400">
                                    {{ $t('pages.active_pages') }}
                                </div>
                                <div class="text-2xl font-bold text-gray-900 dark:text-white">
                                    {{ stats.active_pages || 0 }}
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="bg-white dark:bg-gray-800 overflow-hidden shadow-xl sm:rounded-lg p-6">
                        <div class="flex items-center">
                            <div class="flex-shrink-0">
                                <div class="w-8 h-8 bg-red-500 rounded-full flex items-center justify-center">
                                    <i class="fas fa-pause-circle text-white text-sm"></i>
                                </div>
                            </div>
                            <div class="ml-4">
                                <div class="text-sm font-medium text-gray-500 dark:text-gray-400">
                                    {{ $t('pages.inactive_pages') }}
                                </div>
                                <div class="text-2xl font-bold text-gray-900 dark:text-white">
                                    {{ stats.inactive_pages || 0 }}
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Filters -->
                <div class="bg-white dark:bg-gray-800 overflow-hidden shadow-xl sm:rounded-lg mb-6">
                    <div class="p-6">
                        <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
                            <div>
                                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                                    {{ $t('forms.search') }}
                                </label>
                                <input
                                    v-model="filters.search"
                                    type="text"
                                    :placeholder="$t('pages.search_placeholder')"
                                    class="w-full rounded-md border-gray-300 dark:border-gray-700 dark:bg-gray-900 dark:text-gray-300 focus:border-indigo-500 dark:focus:border-indigo-600 focus:ring-indigo-500 dark:focus:ring-indigo-600"
                                    @input="search"
                                />
                            </div>

                            <div>
                                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                                    {{ $t('pages.status') }}
                                </label>
                                <select
                                    v-model="filters.status"
                                    class="w-full rounded-md border-gray-300 dark:border-gray-700 dark:bg-gray-900 dark:text-gray-300 focus:border-indigo-500 dark:focus:border-indigo-600 focus:ring-indigo-500 dark:focus:ring-indigo-600"
                                    @change="search"
                                >
                                    <option value="">{{ $t('pages.all_status') }}</option>
                                    <option value="active">{{ $t('pages.active') }}</option>
                                    <option value="inactive">{{ $t('pages.inactive') }}</option>
                                </select>
                            </div>

                            <div>
                                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                                    {{ $t('pages.sort_by') }}
                                </label>
                                <select
                                    v-model="filters.sort"
                                    class="w-full rounded-md border-gray-300 dark:border-gray-700 dark:bg-gray-900 dark:text-gray-300 focus:border-indigo-500 dark:focus:border-indigo-600 focus:ring-indigo-500 dark:focus:ring-indigo-600"
                                    @change="search"
                                >
                                    <option value="created_at">{{ $t('pages.newest_first') }}</option>
                                    <option value="title">{{ $t('pages.title_az') }}</option>
                                    <option value="sort_order">{{ $t('pages.sort_order') }}</option>
                                </select>
                            </div>

                            <div class="flex items-end">
                                <button
                                    @click="resetFilters"
                                    class="w-full px-4 py-2 bg-gray-500 hover:bg-gray-700 text-white font-bold rounded"
                                >
                                    {{ $t('forms.reset') }}
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Bulk Actions -->
                <div v-if="selectedPages.length > 0" class="bg-blue-50 dark:bg-blue-900 border border-blue-200 dark:border-blue-700 rounded-lg p-4 mb-6">
                    <div class="flex items-center justify-between">
                        <div class="flex items-center">
                            <span class="text-sm text-blue-800 dark:text-blue-200">
                                {{ selectedPages.length }} {{ $t('pages.selected') }}
                            </span>
                        </div>
                        <div class="flex space-x-2">
                            <button
                                @click="bulkAction('activate')"
                                class="px-3 py-1 bg-green-500 hover:bg-green-700 text-white text-sm rounded"
                            >
                                {{ $t('pages.activate') }}
                            </button>
                            <button
                                @click="bulkAction('deactivate')"
                                class="px-3 py-1 bg-yellow-500 hover:bg-yellow-700 text-white text-sm rounded"
                            >
                                {{ $t('pages.deactivate') }}
                            </button>
                            <button
                                @click="bulkAction('delete')"
                                class="px-3 py-1 bg-red-500 hover:bg-red-700 text-white text-sm rounded"
                            >
                                {{ $t('pages.delete') }}
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Pages Table -->
                <div class="bg-white dark:bg-gray-800 overflow-hidden shadow-xl sm:rounded-lg">
                    <div class="overflow-x-auto">
                        <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                            <thead class="bg-gray-50 dark:bg-gray-700">
                                <tr>
                                    <th class="px-6 py-3 text-left">
                                        <input
                                            type="checkbox"
                                            @change="toggleSelectAll"
                                            :checked="selectedPages.length === pages.data.length && pages.data.length > 0"
                                            class="rounded border-gray-300 text-indigo-600 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50"
                                        />
                                    </th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                                        {{ $t('pages.title') }}
                                    </th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                                        {{ $t('pages.slug') }}
                                    </th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                                        {{ $t('pages.status') }}
                                    </th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                                        {{ $t('pages.sort_order') }}
                                    </th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                                        {{ $t('pages.created_at') }}
                                    </th>
                                    <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                                        {{ $t('pages.actions') }}
                                    </th>
                                </tr>
                            </thead>
                            <tbody class="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                                <tr v-for="page in pages.data" :key="page.id" class="hover:bg-gray-50 dark:hover:bg-gray-700">
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <input
                                            type="checkbox"
                                            :value="page.id"
                                            v-model="selectedPages"
                                            class="rounded border-gray-300 text-indigo-600 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50"
                                        />
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="flex items-center">
                                            <div class="flex-shrink-0 h-10 w-10" v-if="page.banner_image_url">
                                                <img class="h-10 w-10 rounded-lg object-cover" :src="page.banner_image_url" :alt="page.title" />
                                            </div>
                                            <div class="flex-shrink-0 h-10 w-10 bg-gray-300 rounded-lg flex items-center justify-center" v-else>
                                                <i class="fas fa-file-alt text-gray-500"></i>
                                            </div>
                                            <div class="ml-4">
                                                <div class="text-sm font-medium text-gray-900 dark:text-white">
                                                    {{ page.title }}
                                                </div>
                                                <div class="text-sm text-gray-500 dark:text-gray-400">
                                                    {{ page.meta_description }}
                                                </div>
                                            </div>
                                        </div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white">
                                        <code class="bg-gray-100 dark:bg-gray-700 px-2 py-1 rounded text-xs">
                                            /{{ page.slug }}
                                        </code>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <span :class="getStatusClass(page.status)" class="inline-flex px-2 py-1 text-xs font-semibold rounded-full">
                                            {{ $t(`pages.${page.status}`) }}
                                        </span>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white">
                                        {{ page.sort_order || 0 }}
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white">
                                        {{ formatDate(page.created_at) }}
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                                        <div class="flex items-center justify-end space-x-2">
                                            <Link
                                                :href="route('admin.dynamic-pages.show', page.id)"
                                                class="text-blue-600 hover:text-blue-900 dark:text-blue-400 dark:hover:text-blue-300"
                                                :title="$t('common.view')"
                                            >
                                                <i class="fas fa-eye"></i>
                                            </Link>
                                            <Link
                                                :href="route('admin.dynamic-pages.edit', page.id)"
                                                class="text-indigo-600 hover:text-indigo-900 dark:text-indigo-400 dark:hover:text-indigo-300"
                                                :title="$t('common.edit')"
                                            >
                                                <i class="fas fa-edit"></i>
                                            </Link>
                                            <button
                                                @click="togglePageStatus(page)"
                                                :class="page.status === 'active' ? 'text-red-600 hover:text-red-900' : 'text-green-600 hover:text-green-900'"
                                                :title="page.status === 'active' ? $t('pages.deactivate') : $t('pages.activate')"
                                            >
                                                <i :class="page.status === 'active' ? 'fas fa-pause' : 'fas fa-play'"></i>
                                            </button>
                                            <button
                                                @click="deletePage(page)"
                                                class="text-red-600 hover:text-red-900 dark:text-red-400 dark:hover:text-red-300"
                                                :title="$t('common.delete')"
                                            >
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>

                    <!-- Pagination -->
                    <div class="px-6 py-4 border-t border-gray-200 dark:border-gray-700">
                        <Pagination :links="pages.links" />
                    </div>
                </div>
            </div>
        </div>
    </AdminLayout>
</template>

<script setup>
import { ref, reactive } from 'vue'
import { Link, router } from '@inertiajs/vue3'
import AdminLayout from '@/Layouts/AdminLayout.vue'
import Pagination from '@/Components/Pagination.vue'
import { useI18n } from 'vue-i18n'

const { t } = useI18n()

// Props
const props = defineProps({
    pages: Object,
    stats: Object,
    filters: Object,
})

// Reactive data
const filters = reactive({
    search: props.filters.search || '',
    status: props.filters.status || '',
    sort: props.filters.sort || 'created_at',
})

const selectedPages = ref([])

// Methods
const search = () => {
    router.get(route('admin.dynamic-pages.index'), filters, {
        preserveState: true,
        replace: true,
    })
}

const resetFilters = () => {
    filters.search = ''
    filters.status = ''
    filters.sort = 'created_at'
    search()
}

const formatDate = (date) => {
    return new Date(date).toLocaleDateString()
}

const getStatusClass = (status) => {
    const classes = {
        active: 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200',
        inactive: 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200',
    }
    return classes[status] || classes.inactive
}

const toggleSelectAll = () => {
    if (selectedPages.value.length === props.pages.data.length) {
        selectedPages.value = []
    } else {
        selectedPages.value = props.pages.data.map(page => page.id)
    }
}

const bulkAction = (action) => {
    if (selectedPages.value.length === 0) return

    const confirmMessage = action === 'delete' 
        ? t('pages.confirm_bulk_delete')
        : t('pages.confirm_bulk_action', { action: t(`pages.${action}`) })

    if (confirm(confirmMessage)) {
        router.post(route('admin.dynamic-pages.bulk-action'), {
            action: action,
            ids: selectedPages.value
        }, {
            preserveScroll: true,
            onSuccess: () => {
                selectedPages.value = []
            }
        })
    }
}

const togglePageStatus = (page) => {
    const newStatus = page.status === 'active' ? 'inactive' : 'active'
    if (confirm(t('pages.confirm_toggle_status'))) {
        router.patch(route('admin.dynamic-pages.update', page.id), {
            status: newStatus
        }, {
            preserveScroll: true,
        })
    }
}

const deletePage = (page) => {
    if (confirm(t('pages.confirm_delete'))) {
        router.delete(route('admin.dynamic-pages.destroy', page.id), {
            preserveScroll: true,
        })
    }
}
</script>
