<template>
    <AdminLayout title="Edit Page">
        <template #header>
            <div class="flex justify-between items-center">
                <h2 class="font-semibold text-xl text-gray-800 dark:text-gray-200 leading-tight">
                    {{ $t('pages.edit.title') }}: {{ page.title }}
                </h2>
                <div class="flex space-x-2">
                    <Link
                        :href="route('admin.dynamic-pages.show', page.id)"
                        class="bg-green-500 hover:bg-green-700 text-white font-bold py-2 px-4 rounded"
                    >
                        <i class="fas fa-eye mr-2"></i>
                        {{ $t('common.view') }}
                    </Link>
                    <Link
                        :href="route('admin.dynamic-pages.index')"
                        class="bg-gray-500 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded"
                    >
                        <i class="fas fa-arrow-left mr-2"></i>
                        {{ $t('common.back') }}
                    </Link>
                </div>
            </div>
        </template>

        <div class="py-12">
            <div class="max-w-4xl mx-auto sm:px-6 lg:px-8">
                <form @submit.prevent="submit">
                    <div class="bg-white dark:bg-gray-800 overflow-hidden shadow-xl sm:rounded-lg">
                        <div class="p-6">
                            <!-- Basic Information -->
                            <div class="mb-8">
                                <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-4">
                                    {{ $t('pages.basic_information') }}
                                </h3>
                                
                                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                                            {{ $t('pages.title') }} (English) <span class="text-red-500">*</span>
                                        </label>
                                        <input
                                            v-model="form.title_en"
                                            type="text"
                                            :placeholder="$t('pages.title_placeholder')"
                                            class="w-full rounded-md border-gray-300 dark:border-gray-700 dark:bg-gray-900 dark:text-gray-300 focus:border-indigo-500 dark:focus:border-indigo-600 focus:ring-indigo-500 dark:focus:ring-indigo-600"
                                            :class="{ 'border-red-500': form.errors.title_en }"
                                        />
                                        <div v-if="form.errors.title_en" class="text-red-500 text-sm mt-1">
                                            {{ form.errors.title_en }}
                                        </div>
                                    </div>

                                    <div>
                                        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                                            {{ $t('pages.title') }} (Bengali) <span class="text-red-500">*</span>
                                        </label>
                                        <input
                                            v-model="form.title_bn"
                                            type="text"
                                            :placeholder="$t('pages.title_placeholder')"
                                            class="w-full rounded-md border-gray-300 dark:border-gray-700 dark:bg-gray-900 dark:text-gray-300 focus:border-indigo-500 dark:focus:border-indigo-600 focus:ring-indigo-500 dark:focus:ring-indigo-600"
                                            :class="{ 'border-red-500': form.errors.title_bn }"
                                        />
                                        <div v-if="form.errors.title_bn" class="text-red-500 text-sm mt-1">
                                            {{ form.errors.title_bn }}
                                        </div>
                                    </div>

                                    <div>
                                        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                                            {{ $t('pages.slug') }}
                                        </label>
                                        <input
                                            v-model="form.slug"
                                            type="text"
                                            :placeholder="$t('pages.slug_placeholder')"
                                            class="w-full rounded-md border-gray-300 dark:border-gray-700 dark:bg-gray-900 dark:text-gray-300 focus:border-indigo-500 dark:focus:border-indigo-600 focus:ring-indigo-500 dark:focus:ring-indigo-600"
                                            :class="{ 'border-red-500': form.errors.slug }"
                                        />
                                        <div v-if="form.errors.slug" class="text-red-500 text-sm mt-1">
                                            {{ form.errors.slug }}
                                        </div>
                                        <p class="text-xs text-gray-500 mt-1">{{ $t('pages.slug_help') }}</p>
                                    </div>

                                    <div>
                                        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                                            {{ $t('pages.status') }} <span class="text-red-500">*</span>
                                        </label>
                                        <select
                                            v-model="form.status"
                                            class="w-full rounded-md border-gray-300 dark:border-gray-700 dark:bg-gray-900 dark:text-gray-300 focus:border-indigo-500 dark:focus:border-indigo-600 focus:ring-indigo-500 dark:focus:ring-indigo-600"
                                            :class="{ 'border-red-500': form.errors.status }"
                                        >
                                            <option value="active">{{ $t('pages.active') }}</option>
                                            <option value="inactive">{{ $t('pages.inactive') }}</option>
                                        </select>
                                        <div v-if="form.errors.status" class="text-red-500 text-sm mt-1">
                                            {{ form.errors.status }}
                                        </div>
                                    </div>

                                    <div>
                                        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                                            {{ $t('pages.sort_order') }}
                                        </label>
                                        <input
                                            v-model="form.sort_order"
                                            type="number"
                                            min="0"
                                            :placeholder="$t('pages.sort_order_placeholder')"
                                            class="w-full rounded-md border-gray-300 dark:border-gray-700 dark:bg-gray-900 dark:text-gray-300 focus:border-indigo-500 dark:focus:border-indigo-600 focus:ring-indigo-500 dark:focus:ring-indigo-600"
                                        />
                                    </div>
                                </div>
                            </div>

                            <!-- Banner Image -->
                            <div class="mb-8">
                                <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-4">
                                    {{ $t('pages.banner_image') }}
                                </h3>
                                
                                <div class="border-2 border-dashed border-gray-300 dark:border-gray-600 rounded-lg p-6">
                                    <div v-if="form.banner_image_id || page.banner_image_url" class="text-center">
                                        <img 
                                            :src="selectedImageUrl || page.banner_image_url" 
                                            :alt="$t('pages.banner_image')"
                                            class="mx-auto h-32 w-auto rounded-lg object-cover"
                                        />
                                        <div class="mt-4 flex justify-center space-x-2">
                                            <button
                                                type="button"
                                                @click="selectImage"
                                                class="px-3 py-1 bg-blue-500 hover:bg-blue-700 text-white text-sm rounded"
                                            >
                                                {{ $t('pages.change_image') }}
                                            </button>
                                            <button
                                                type="button"
                                                @click="removeImage"
                                                class="px-3 py-1 bg-red-500 hover:bg-red-700 text-white text-sm rounded"
                                            >
                                                {{ $t('pages.remove_image') }}
                                            </button>
                                        </div>
                                    </div>
                                    <div v-else class="text-center">
                                        <i class="fas fa-image text-gray-400 text-4xl mb-4"></i>
                                        <p class="text-gray-500 dark:text-gray-400 mb-4">{{ $t('pages.no_image') }}</p>
                                        <button
                                            type="button"
                                            @click="selectImage"
                                            class="px-4 py-2 bg-blue-500 hover:bg-blue-700 text-white rounded"
                                        >
                                            {{ $t('pages.select_image') }}
                                        </button>
                                    </div>
                                </div>
                            </div>

                            <!-- Content -->
                            <div class="mb-8">
                                <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-4">
                                    {{ $t('pages.content') }} <span class="text-red-500">*</span>
                                </h3>
                                
                                <TinyMCEEditor
                                    v-model="form.content"
                                    :placeholder="$t('pages.content_placeholder')"
                                    :error="form.errors.content"
                                />
                                <div v-if="form.errors.content" class="text-red-500 text-sm mt-1">
                                    {{ form.errors.content }}
                                </div>
                            </div>

                            <!-- SEO & Meta -->
                            <div class="mb-8">
                                <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-4">
                                    {{ $t('pages.seo_meta') }}
                                </h3>
                                
                                <div class="grid grid-cols-1 gap-6">
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                                            {{ $t('pages.meta_description') }}
                                        </label>
                                        <textarea
                                            v-model="form.meta_description"
                                            rows="3"
                                            :placeholder="$t('pages.meta_description_placeholder')"
                                            class="w-full rounded-md border-gray-300 dark:border-gray-700 dark:bg-gray-900 dark:text-gray-300 focus:border-indigo-500 dark:focus:border-indigo-600 focus:ring-indigo-500 dark:focus:ring-indigo-600"
                                        ></textarea>
                                        <p class="text-xs text-gray-500 mt-1">{{ $t('pages.meta_description_help') }}</p>
                                    </div>
                                </div>
                            </div>

                            <!-- Actions -->
                            <div class="flex justify-end space-x-4">
                                <Link
                                    :href="route('admin.dynamic-pages.index')"
                                    class="px-4 py-2 bg-gray-500 hover:bg-gray-700 text-white font-bold rounded"
                                >
                                    {{ $t('forms.cancel') }}
                                </Link>
                                <button
                                    type="button"
                                    @click="saveDraft"
                                    :disabled="form.processing"
                                    class="px-4 py-2 bg-yellow-500 hover:bg-yellow-700 text-white font-bold rounded disabled:opacity-50"
                                >
                                    <i class="fas fa-save mr-2"></i>
                                    {{ $t('pages.save_draft') }}
                                </button>
                                <button
                                    type="submit"
                                    :disabled="form.processing"
                                    class="px-4 py-2 bg-blue-500 hover:bg-blue-700 text-white font-bold rounded disabled:opacity-50"
                                >
                                    <i class="fas fa-save mr-2"></i>
                                    {{ form.processing ? $t('forms.updating') : $t('forms.update') }}
                                </button>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
        </div>

        <!-- Media Selection Modal (placeholder) -->
        <div v-if="showMediaModal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
            <div class="bg-white dark:bg-gray-800 rounded-lg p-6 max-w-md w-full mx-4">
                <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-4">
                    {{ $t('pages.select_image') }}
                </h3>
                <p class="text-gray-600 dark:text-gray-400 mb-4">
                    Media selection functionality will be implemented here.
                </p>
                <div class="flex justify-end space-x-2">
                    <button
                        @click="showMediaModal = false"
                        class="px-4 py-2 bg-gray-500 hover:bg-gray-700 text-white rounded"
                    >
                        {{ $t('forms.cancel') }}
                    </button>
                </div>
            </div>
        </div>
    </AdminLayout>
</template>

<script setup>
import { ref } from 'vue'
import { useForm, Link } from '@inertiajs/vue3'
import AdminLayout from '@/Layouts/AdminLayout.vue'
import TinyMCEEditor from '@/Components/TinyMCEEditor.vue'
import { useI18n } from 'vue-i18n'

const { t } = useI18n()

const props = defineProps({
    page: Object,
})

const showMediaModal = ref(false)
const selectedImageUrl = ref('')

const form = useForm({
    title_en: props.page.title?.en || props.page.title || '',
    title_bn: props.page.title?.bn || '',
    slug: props.page.slug || '',
    content: props.page.content || '',
    banner_image_id: props.page.banner_image_id || null,
    meta_description: props.page.meta_description || '',
    status: props.page.status || 'active',
    sort_order: props.page.sort_order || 0,
})

const submit = () => {
    form.put(route('admin.dynamic-pages.update', props.page.id))
}

const saveDraft = () => {
    form.status = 'inactive'
    submit()
}

const selectImage = () => {
    showMediaModal.value = true
    // TODO: Implement media selection
}

const removeImage = () => {
    form.banner_image_id = null
    selectedImageUrl.value = ''
}
</script>
