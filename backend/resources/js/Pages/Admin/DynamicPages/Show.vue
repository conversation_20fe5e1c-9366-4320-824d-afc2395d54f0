<template>
    <AdminLayout title="Page Details">
        <template #header>
            <div class="flex justify-between items-center">
                <h2 class="font-semibold text-xl text-gray-800 dark:text-gray-200 leading-tight">
                    {{ $t('pages.show.title') }}: {{ page.title }}
                </h2>
                <div class="flex space-x-2">
                    <Link
                        :href="route('admin.dynamic-pages.edit', page.id)"
                        class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded"
                    >
                        <i class="fas fa-edit mr-2"></i>
                        {{ $t('common.edit') }}
                    </Link>
                    <Link
                        :href="route('admin.dynamic-pages.index')"
                        class="bg-gray-500 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded"
                    >
                        <i class="fas fa-arrow-left mr-2"></i>
                        {{ $t('common.back') }}
                    </Link>
                </div>
            </div>
        </template>

        <div class="py-12">
            <div class="max-w-4xl mx-auto sm:px-6 lg:px-8">
                <!-- Page Overview -->
                <div class="bg-white dark:bg-gray-800 overflow-hidden shadow-xl sm:rounded-lg mb-6">
                    <div class="p-6">
                        <!-- Banner Image -->
                        <div v-if="page.banner_image_url" class="mb-6">
                            <img 
                                :src="page.banner_image_url" 
                                :alt="page.title"
                                class="w-full h-64 object-cover rounded-lg"
                            />
                        </div>

                        <!-- Page Header -->
                        <div class="mb-6">
                            <h1 class="text-3xl font-bold text-gray-900 dark:text-white mb-4">
                                {{ page.title }}
                            </h1>
                            
                            <div class="flex flex-wrap items-center gap-4 text-sm text-gray-600 dark:text-gray-400">
                                <div class="flex items-center">
                                    <i class="fas fa-calendar mr-2"></i>
                                    <span>{{ $t('pages.created') }}: {{ formatDate(page.created_at) }}</span>
                                </div>
                                
                                <div class="flex items-center">
                                    <i class="fas fa-edit mr-2"></i>
                                    <span>{{ $t('pages.updated') }}: {{ formatDate(page.updated_at) }}</span>
                                </div>
                                
                                <div class="flex items-center">
                                    <span :class="getStatusClass(page.status)" class="inline-flex px-2 py-1 text-xs font-semibold rounded-full">
                                        {{ $t(`pages.${page.status}`) }}
                                    </span>
                                </div>
                            </div>
                        </div>

                        <!-- Page Content -->
                        <div class="prose prose-lg max-w-none dark:prose-invert">
                            <div v-html="page.content"></div>
                        </div>
                    </div>
                </div>

                <!-- Page Details -->
                <div class="bg-white dark:bg-gray-800 overflow-hidden shadow-xl sm:rounded-lg mb-6">
                    <div class="p-6">
                        <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-4">
                            {{ $t('pages.page_details') }}
                        </h3>
                        
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <div>
                                <label class="block text-sm font-medium text-gray-500 dark:text-gray-400">
                                    {{ $t('pages.slug') }}
                                </label>
                                <p class="mt-1 text-sm text-gray-900 dark:text-white">
                                    <code class="bg-gray-100 dark:bg-gray-700 px-2 py-1 rounded text-xs">
                                        /{{ page.slug }}
                                    </code>
                                </p>
                            </div>

                            <div>
                                <label class="block text-sm font-medium text-gray-500 dark:text-gray-400">
                                    {{ $t('pages.status') }}
                                </label>
                                <span :class="getStatusClass(page.status)" class="inline-flex px-2 py-1 text-xs font-semibold rounded-full mt-1">
                                    {{ $t(`pages.${page.status}`) }}
                                </span>
                            </div>

                            <div>
                                <label class="block text-sm font-medium text-gray-500 dark:text-gray-400">
                                    {{ $t('pages.sort_order') }}
                                </label>
                                <p class="mt-1 text-sm text-gray-900 dark:text-white">
                                    {{ page.sort_order || 0 }}
                                </p>
                            </div>

                            <div>
                                <label class="block text-sm font-medium text-gray-500 dark:text-gray-400">
                                    {{ $t('pages.created_at') }}
                                </label>
                                <p class="mt-1 text-sm text-gray-900 dark:text-white">
                                    {{ formatDateTime(page.created_at) }}
                                </p>
                            </div>

                            <div>
                                <label class="block text-sm font-medium text-gray-500 dark:text-gray-400">
                                    {{ $t('pages.updated_at') }}
                                </label>
                                <p class="mt-1 text-sm text-gray-900 dark:text-white">
                                    {{ formatDateTime(page.updated_at) }}
                                </p>
                            </div>
                        </div>

                        <!-- Meta Description -->
                        <div v-if="page.meta_description" class="mt-6">
                            <label class="block text-sm font-medium text-gray-500 dark:text-gray-400">
                                {{ $t('pages.meta_description') }}
                            </label>
                            <p class="mt-1 text-sm text-gray-900 dark:text-white">
                                {{ page.meta_description }}
                            </p>
                        </div>
                    </div>
                </div>

                <!-- Actions -->
                <div class="bg-white dark:bg-gray-800 overflow-hidden shadow-xl sm:rounded-lg">
                    <div class="p-6">
                        <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-4">
                            {{ $t('pages.actions') }}
                        </h3>
                        
                        <div class="flex flex-wrap gap-4">
                            <Link
                                :href="route('admin.dynamic-pages.edit', page.id)"
                                class="inline-flex items-center px-4 py-2 bg-blue-500 hover:bg-blue-700 text-white font-bold rounded"
                            >
                                <i class="fas fa-edit mr-2"></i>
                                {{ $t('common.edit') }}
                            </Link>

                            <button
                                @click="toggleStatus"
                                :class="page.status === 'active' ? 'bg-yellow-500 hover:bg-yellow-700' : 'bg-green-500 hover:bg-green-700'"
                                class="inline-flex items-center px-4 py-2 text-white font-bold rounded"
                            >
                                <i :class="page.status === 'active' ? 'fas fa-pause' : 'fas fa-play'" class="mr-2"></i>
                                {{ page.status === 'active' ? $t('pages.deactivate') : $t('pages.activate') }}
                            </button>

                            <button
                                @click="duplicatePage"
                                class="inline-flex items-center px-4 py-2 bg-purple-500 hover:bg-purple-700 text-white font-bold rounded"
                            >
                                <i class="fas fa-copy mr-2"></i>
                                {{ $t('pages.duplicate') }}
                            </button>

                            <button
                                @click="deletePage"
                                class="inline-flex items-center px-4 py-2 bg-red-500 hover:bg-red-700 text-white font-bold rounded"
                            >
                                <i class="fas fa-trash mr-2"></i>
                                {{ $t('common.delete') }}
                            </button>

                            <a
                                v-if="page.status === 'active'"
                                :href="getPageUrl()"
                                target="_blank"
                                class="inline-flex items-center px-4 py-2 bg-gray-500 hover:bg-gray-700 text-white font-bold rounded"
                            >
                                <i class="fas fa-external-link-alt mr-2"></i>
                                {{ $t('pages.view_public') }}
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </AdminLayout>
</template>

<script setup>
import { Link, router } from '@inertiajs/vue3'
import AdminLayout from '@/Layouts/AdminLayout.vue'
import { useI18n } from 'vue-i18n'

const { t } = useI18n()

const props = defineProps({
    page: Object,
})

const formatDate = (date) => {
    return new Date(date).toLocaleDateString()
}

const formatDateTime = (date) => {
    return new Date(date).toLocaleString()
}

const getStatusClass = (status) => {
    const classes = {
        active: 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200',
        inactive: 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200',
    }
    return classes[status] || classes.inactive
}

const getPageUrl = () => {
    // This would be the public URL for the page
    return `/${props.page.slug}`
}

const toggleStatus = () => {
    const newStatus = props.page.status === 'active' ? 'inactive' : 'active'
    const confirmMessage = newStatus === 'active' 
        ? t('pages.confirm_activate')
        : t('pages.confirm_deactivate')

    if (confirm(confirmMessage)) {
        router.patch(route('admin.dynamic-pages.update', props.page.id), {
            status: newStatus
        }, {
            preserveScroll: true,
        })
    }
}

const duplicatePage = () => {
    if (confirm(t('pages.confirm_duplicate'))) {
        router.post(route('admin.dynamic-pages.store'), {
            ...props.page,
            title_en: `${props.page.title} (Copy)`,
            slug: `${props.page.slug}-copy`,
            status: 'inactive',
        }, {
            preserveScroll: true,
        })
    }
}

const deletePage = () => {
    if (confirm(t('pages.confirm_delete'))) {
        router.delete(route('admin.dynamic-pages.destroy', props.page.id))
    }
}
</script>
