<template>
    <AppLayout title="Delivery Dashboard">
        <template #header>
            <div class="flex justify-between items-center">
                <h2 class="font-semibold text-xl text-gray-800 dark:text-gray-200 leading-tight">
                    {{ $t('delivery.dashboard.title') }}
                </h2>
                <div class="flex items-center space-x-4">
                    <div class="text-sm text-gray-600 dark:text-gray-400">
                        {{ $t('delivery.dashboard.status') }}: 
                        <span class="font-semibold" :class="statusClass">{{ currentStatus }}</span>
                    </div>
                    <button
                        @click="toggleStatus"
                        class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded"
                    >
                        {{ currentStatus === 'available' ? $t('delivery.dashboard.go_offline') : $t('delivery.dashboard.go_online') }}
                    </button>
                </div>
            </div>
        </template>

        <div class="py-12">
            <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
                <!-- Stats Cards -->
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
                    <div class="bg-white dark:bg-gray-800 overflow-hidden shadow-xl sm:rounded-lg p-6">
                        <div class="flex items-center">
                            <div class="flex-shrink-0">
                                <div class="w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center">
                                    <i class="fas fa-motorcycle text-white text-sm"></i>
                                </div>
                            </div>
                            <div class="ml-4">
                                <div class="text-sm font-medium text-gray-500 dark:text-gray-400">
                                    {{ $t('delivery.dashboard.todays_deliveries') }}
                                </div>
                                <div class="text-2xl font-bold text-gray-900 dark:text-white">
                                    {{ stats.todays_deliveries || 0 }}
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="bg-white dark:bg-gray-800 overflow-hidden shadow-xl sm:rounded-lg p-6">
                        <div class="flex items-center">
                            <div class="flex-shrink-0">
                                <div class="w-8 h-8 bg-green-500 rounded-full flex items-center justify-center">
                                    <i class="fas fa-dollar-sign text-white text-sm"></i>
                                </div>
                            </div>
                            <div class="ml-4">
                                <div class="text-sm font-medium text-gray-500 dark:text-gray-400">
                                    {{ $t('delivery.dashboard.todays_earnings') }}
                                </div>
                                <div class="text-2xl font-bold text-gray-900 dark:text-white">
                                    ${{ formatCurrency(stats.todays_earnings || 0) }}
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="bg-white dark:bg-gray-800 overflow-hidden shadow-xl sm:rounded-lg p-6">
                        <div class="flex items-center">
                            <div class="flex-shrink-0">
                                <div class="w-8 h-8 bg-yellow-500 rounded-full flex items-center justify-center">
                                    <i class="fas fa-clock text-white text-sm"></i>
                                </div>
                            </div>
                            <div class="ml-4">
                                <div class="text-sm font-medium text-gray-500 dark:text-gray-400">
                                    {{ $t('delivery.dashboard.avg_delivery_time') }}
                                </div>
                                <div class="text-2xl font-bold text-gray-900 dark:text-white">
                                    {{ stats.avg_delivery_time || 0 }}m
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="bg-white dark:bg-gray-800 overflow-hidden shadow-xl sm:rounded-lg p-6">
                        <div class="flex items-center">
                            <div class="flex-shrink-0">
                                <div class="w-8 h-8 bg-purple-500 rounded-full flex items-center justify-center">
                                    <i class="fas fa-star text-white text-sm"></i>
                                </div>
                            </div>
                            <div class="ml-4">
                                <div class="text-sm font-medium text-gray-500 dark:text-gray-400">
                                    {{ $t('delivery.dashboard.rating') }}
                                </div>
                                <div class="text-2xl font-bold text-gray-900 dark:text-white">
                                    {{ stats.rating || 0 }}/5
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Available Orders and Current Delivery -->
                <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
                    <!-- Available Orders -->
                    <div class="bg-white dark:bg-gray-800 overflow-hidden shadow-xl sm:rounded-lg p-6">
                        <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-4">
                            {{ $t('delivery.dashboard.available_orders') }}
                        </h3>
                        <div class="space-y-3 max-h-96 overflow-y-auto">
                            <div
                                v-for="order in availableOrders"
                                :key="order.id"
                                class="border border-gray-200 dark:border-gray-700 rounded-lg p-4 hover:bg-gray-50 dark:hover:bg-gray-700 cursor-pointer"
                                @click="acceptOrder(order)"
                            >
                                <div class="flex justify-between items-start">
                                    <div>
                                        <p class="font-medium text-gray-900 dark:text-white">
                                            {{ $t('delivery.dashboard.order') }} #{{ order.order_number }}
                                        </p>
                                        <p class="text-sm text-gray-600 dark:text-gray-400">
                                            {{ order.customer_name }}
                                        </p>
                                        <p class="text-sm text-gray-600 dark:text-gray-400">
                                            {{ order.delivery_address }}
                                        </p>
                                    </div>
                                    <div class="text-right">
                                        <p class="font-bold text-green-600">
                                            ${{ formatCurrency(order.delivery_fee) }}
                                        </p>
                                        <p class="text-xs text-gray-500">
                                            {{ order.distance }}km
                                        </p>
                                    </div>
                                </div>
                            </div>
                            <div v-if="!availableOrders || availableOrders.length === 0" class="text-center py-8">
                                <i class="fas fa-box text-gray-400 text-3xl mb-2"></i>
                                <p class="text-gray-500 dark:text-gray-400">{{ $t('delivery.dashboard.no_available_orders') }}</p>
                            </div>
                        </div>
                    </div>

                    <!-- Current Delivery -->
                    <div class="bg-white dark:bg-gray-800 overflow-hidden shadow-xl sm:rounded-lg p-6">
                        <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-4">
                            {{ $t('delivery.dashboard.current_delivery') }}
                        </h3>
                        <div v-if="currentDelivery" class="space-y-4">
                            <div class="border border-blue-200 dark:border-blue-700 rounded-lg p-4 bg-blue-50 dark:bg-blue-900/20">
                                <div class="flex justify-between items-start mb-3">
                                    <div>
                                        <p class="font-medium text-gray-900 dark:text-white">
                                            {{ $t('delivery.dashboard.order') }} #{{ currentDelivery.order_number }}
                                        </p>
                                        <p class="text-sm text-gray-600 dark:text-gray-400">
                                            {{ currentDelivery.customer_name }}
                                        </p>
                                    </div>
                                    <span
                                        class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium"
                                        :class="getDeliveryStatusClass(currentDelivery.status)"
                                    >
                                        {{ currentDelivery.status }}
                                    </span>
                                </div>
                                
                                <div class="space-y-2">
                                    <div class="flex items-center text-sm">
                                        <i class="fas fa-map-marker-alt text-gray-400 mr-2"></i>
                                        <span class="text-gray-600 dark:text-gray-400">{{ currentDelivery.delivery_address }}</span>
                                    </div>
                                    <div class="flex items-center text-sm">
                                        <i class="fas fa-phone text-gray-400 mr-2"></i>
                                        <span class="text-gray-600 dark:text-gray-400">{{ currentDelivery.customer_phone }}</span>
                                    </div>
                                    <div class="flex items-center text-sm">
                                        <i class="fas fa-dollar-sign text-gray-400 mr-2"></i>
                                        <span class="text-gray-600 dark:text-gray-400">${{ formatCurrency(currentDelivery.total_amount) }}</span>
                                    </div>
                                </div>

                                <div class="mt-4 flex space-x-2">
                                    <button
                                        v-if="currentDelivery.status === 'assigned'"
                                        @click="markPickedUp(currentDelivery)"
                                        class="flex-1 bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded text-sm"
                                    >
                                        {{ $t('delivery.dashboard.mark_picked_up') }}
                                    </button>
                                    <button
                                        v-if="currentDelivery.status === 'picked_up'"
                                        @click="markDelivered(currentDelivery)"
                                        class="flex-1 bg-green-500 hover:bg-green-700 text-white font-bold py-2 px-4 rounded text-sm"
                                    >
                                        {{ $t('delivery.dashboard.mark_delivered') }}
                                    </button>
                                    <button
                                        @click="openMap(currentDelivery)"
                                        class="bg-gray-500 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded text-sm"
                                    >
                                        <i class="fas fa-map"></i>
                                    </button>
                                    <button
                                        @click="callCustomer(currentDelivery)"
                                        class="bg-green-500 hover:bg-green-700 text-white font-bold py-2 px-4 rounded text-sm"
                                    >
                                        <i class="fas fa-phone"></i>
                                    </button>
                                </div>
                            </div>
                        </div>
                        <div v-else class="text-center py-8">
                            <i class="fas fa-truck text-gray-400 text-3xl mb-2"></i>
                            <p class="text-gray-500 dark:text-gray-400">{{ $t('delivery.dashboard.no_current_delivery') }}</p>
                        </div>
                    </div>
                </div>

                <!-- Recent Deliveries -->
                <div class="bg-white dark:bg-gray-800 overflow-hidden shadow-xl sm:rounded-lg p-6">
                    <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-4">
                        {{ $t('delivery.dashboard.recent_deliveries') }}
                    </h3>
                    <div class="overflow-x-auto">
                        <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                            <thead class="bg-gray-50 dark:bg-gray-700">
                                <tr>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                                        {{ $t('delivery.dashboard.order') }}
                                    </th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                                        {{ $t('delivery.dashboard.customer') }}
                                    </th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                                        {{ $t('delivery.dashboard.amount') }}
                                    </th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                                        {{ $t('delivery.dashboard.status') }}
                                    </th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                                        {{ $t('delivery.dashboard.time') }}
                                    </th>
                                </tr>
                            </thead>
                            <tbody class="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                                <tr v-for="delivery in recentDeliveries" :key="delivery.id">
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white">
                                        #{{ delivery.order_number }}
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white">
                                        {{ delivery.customer_name }}
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white">
                                        ${{ formatCurrency(delivery.total_amount) }}
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <span
                                            class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium"
                                            :class="getDeliveryStatusClass(delivery.status)"
                                        >
                                            {{ delivery.status }}
                                        </span>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                                        {{ formatTime(delivery.delivered_at) }}
                                    </td>
                                </tr>
                                <tr v-if="!recentDeliveries || recentDeliveries.length === 0">
                                    <td colspan="5" class="px-6 py-8 text-center text-gray-500 dark:text-gray-400">
                                        <i class="fas fa-history text-3xl mb-2"></i>
                                        <p>{{ $t('delivery.dashboard.no_recent_deliveries') }}</p>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </AppLayout>
</template>

<script>
import { defineComponent } from 'vue'
import AppLayout from '@/Layouts/AppLayout.vue'

export default defineComponent({
    name: 'DeliveryDashboard',
    components: {
        AppLayout,
    },
    props: {
        stats: {
            type: Object,
            default: () => ({})
        },
        availableOrders: {
            type: Array,
            default: () => []
        },
        currentDelivery: {
            type: Object,
            default: null
        },
        recentDeliveries: {
            type: Array,
            default: () => []
        }
    },
    data() {
        return {
            currentStatus: 'available' // available, busy, offline
        }
    },
    computed: {
        statusClass() {
            const classes = {
                'available': 'text-green-600',
                'busy': 'text-yellow-600',
                'offline': 'text-red-600'
            }
            return classes[this.currentStatus] || 'text-gray-600'
        }
    },
    methods: {
        toggleStatus() {
            this.currentStatus = this.currentStatus === 'available' ? 'offline' : 'available'
            // API call to update status
        },
        acceptOrder(order) {
            this.$inertia.post(route('delivery.accept', order.id))
        },
        markPickedUp(delivery) {
            this.$inertia.post(route('delivery.pickup', delivery.id))
        },
        markDelivered(delivery) {
            this.$inertia.post(route('delivery.deliver', delivery.id))
        },
        openMap(delivery) {
            const address = encodeURIComponent(delivery.delivery_address)
            window.open(`https://maps.google.com/maps?q=${address}`, '_blank')
        },
        callCustomer(delivery) {
            window.open(`tel:${delivery.customer_phone}`)
        },
        formatCurrency(amount) {
            return new Intl.NumberFormat('en-US', {
                minimumFractionDigits: 2,
                maximumFractionDigits: 2
            }).format(amount)
        },
        formatTime(time) {
            return new Date(time).toLocaleTimeString()
        },
        getDeliveryStatusClass(status) {
            const classes = {
                'assigned': 'bg-blue-100 text-blue-800',
                'picked_up': 'bg-yellow-100 text-yellow-800',
                'delivered': 'bg-green-100 text-green-800',
                'failed': 'bg-red-100 text-red-800'
            }
            return classes[status] || 'bg-gray-100 text-gray-800'
        }
    }
})
</script>
