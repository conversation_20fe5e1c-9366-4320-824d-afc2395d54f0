<template>
    <WaiterLayout>
        <template #header>
            <div class="flex justify-between items-center">
                <div>
                    <h2 class="font-semibold text-xl text-gray-800 dark:text-gray-200 leading-tight">
                        My Orders
                    </h2>
                    <p class="text-sm text-gray-600 dark:text-gray-400 mt-1">
                        Manage your assigned orders
                    </p>
                </div>
                <div class="flex space-x-3">
                    <Link
                        :href="route('waiter.orders.create')"
                        class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md text-sm font-medium transition-colors duration-200"
                    >
                        <PlusIcon class="w-4 h-4 inline mr-2" />
                        New Order
                    </Link>
                </div>
            </div>
        </template>

        <div class="py-6">
            <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
                <!-- Status Filter Tabs -->
                <div class="bg-white dark:bg-gray-800 overflow-hidden shadow-sm sm:rounded-lg mb-6">
                    <div class="border-b border-gray-200 dark:border-gray-700">
                        <nav class="-mb-px flex space-x-8 px-6">
                            <button
                                v-for="(count, status) in statusCounts"
                                :key="status"
                                @click="filterByStatus(status)"
                                :class="[
                                    'py-4 px-1 border-b-2 font-medium text-sm transition-colors duration-200',
                                    currentStatus === status
                                        ? 'border-blue-500 text-blue-600 dark:text-blue-400'
                                        : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 dark:text-gray-400 dark:hover:text-gray-300'
                                ]"
                            >
                                {{ formatStatusLabel(status) }}
                                <span class="ml-2 bg-gray-100 dark:bg-gray-700 text-gray-900 dark:text-gray-300 py-0.5 px-2.5 rounded-full text-xs">
                                    {{ count }}
                                </span>
                            </button>
                        </nav>
                    </div>
                </div>

                <!-- Orders List -->
                <div class="bg-white dark:bg-gray-800 overflow-hidden shadow-sm sm:rounded-lg">
                    <div class="p-6">
                        <div v-if="orders.data.length === 0" class="text-center py-12">
                            <div class="text-gray-400 dark:text-gray-500 text-lg mb-4">
                                <ClipboardDocumentListIcon class="w-16 h-16 mx-auto mb-4" />
                                No orders found
                            </div>
                            <p class="text-gray-500 dark:text-gray-400 mb-6">
                                {{ currentStatus === 'all' ? 'You have no orders today.' : `No ${currentStatus} orders found.` }}
                            </p>
                            <Link
                                :href="route('waiter.orders.create')"
                                class="bg-blue-600 hover:bg-blue-700 text-white px-6 py-3 rounded-md text-sm font-medium transition-colors duration-200"
                            >
                                Create Your First Order
                            </Link>
                        </div>

                        <div v-else class="space-y-4">
                            <div
                                v-for="order in orders.data"
                                :key="order.id"
                                class="border border-gray-200 dark:border-gray-700 rounded-lg p-6 hover:shadow-md transition-shadow duration-200"
                            >
                                <div class="flex justify-between items-start">
                                    <div class="flex-1">
                                        <div class="flex items-center space-x-4 mb-3">
                                            <h3 class="text-lg font-semibold text-gray-900 dark:text-gray-100">
                                                Order #{{ order.id }}
                                            </h3>
                                            <span
                                                :class="[
                                                    'inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium',
                                                    getStatusColor(order.status)
                                                ]"
                                            >
                                                {{ formatStatus(order.status) }}
                                            </span>
                                            <span class="text-sm text-gray-500 dark:text-gray-400">
                                                {{ formatDateTime(order.created_at) }}
                                            </span>
                                        </div>

                                        <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
                                            <div>
                                                <p class="text-sm font-medium text-gray-500 dark:text-gray-400">Customer</p>
                                                <p class="text-sm text-gray-900 dark:text-gray-100">
                                                    {{ order.customer?.name || 'Walk-in Customer' }}
                                                </p>
                                            </div>
                                            <div>
                                                <p class="text-sm font-medium text-gray-500 dark:text-gray-400">Table</p>
                                                <p class="text-sm text-gray-900 dark:text-gray-100">
                                                    {{ order.table?.name || 'Takeaway' }}
                                                </p>
                                            </div>
                                            <div>
                                                <p class="text-sm font-medium text-gray-500 dark:text-gray-400">Total</p>
                                                <p class="text-sm font-semibold text-gray-900 dark:text-gray-100">
                                                    ${{ order.total_amount }}
                                                </p>
                                            </div>
                                        </div>

                                        <div class="flex items-center space-x-2 text-sm text-gray-600 dark:text-gray-400">
                                            <span>{{ order.items.length }} item(s)</span>
                                            <span>•</span>
                                            <span>{{ order.order_type.replace('_', ' ').toUpperCase() }}</span>
                                        </div>
                                    </div>

                                    <div class="flex flex-col space-y-2 ml-6">
                                        <Link
                                            :href="route('waiter.order-details', order.id)"
                                            class="bg-gray-100 hover:bg-gray-200 dark:bg-gray-700 dark:hover:bg-gray-600 text-gray-700 dark:text-gray-300 px-3 py-2 rounded-md text-sm font-medium transition-colors duration-200"
                                        >
                                            View Details
                                        </Link>
                                        
                                        <button
                                            v-if="canUpdateStatus(order.status)"
                                            @click="showStatusModal(order)"
                                            class="bg-blue-600 hover:bg-blue-700 text-white px-3 py-2 rounded-md text-sm font-medium transition-colors duration-200"
                                        >
                                            Update Status
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Pagination -->
                        <div v-if="orders.links && orders.links.length > 3" class="mt-6">
                            <nav class="flex justify-center">
                                <div class="flex space-x-1">
                                    <Link
                                        v-for="link in orders.links"
                                        :key="link.label"
                                        :href="link.url"
                                        v-html="link.label"
                                        :class="[
                                            'px-3 py-2 text-sm font-medium rounded-md transition-colors duration-200',
                                            link.active
                                                ? 'bg-blue-600 text-white'
                                                : 'bg-white dark:bg-gray-800 text-gray-500 dark:text-gray-400 hover:bg-gray-50 dark:hover:bg-gray-700 border border-gray-300 dark:border-gray-600'
                                        ]"
                                    />
                                </div>
                            </nav>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Status Update Modal -->
        <StatusUpdateModal
            v-if="showingStatusModal"
            :order="selectedOrder"
            @close="closeStatusModal"
            @updated="handleStatusUpdated"
        />
    </WaiterLayout>
</template>

<script setup>
import { ref, computed } from 'vue'
import { Link, router } from '@inertiajs/vue3'
import WaiterLayout from '@/Layouts/WaiterLayout.vue'
import StatusUpdateModal from '@/Components/Waiter/StatusUpdateModal.vue'
import { PlusIcon, ClipboardDocumentListIcon } from '@heroicons/vue/24/outline'

const props = defineProps({
    orders: Object,
    statusCounts: Object,
    currentStatus: String,
    currentDate: String,
    waiter: Object,
})

const showingStatusModal = ref(false)
const selectedOrder = ref(null)

const filterByStatus = (status) => {
    router.get(route('waiter.orders'), {
        status: status,
        date: props.currentDate
    }, {
        preserveState: true,
        preserveScroll: true
    })
}

const formatStatusLabel = (status) => {
    const labels = {
        all: 'All Orders',
        pending: 'Pending',
        confirmed: 'Confirmed',
        preparing: 'Preparing',
        ready: 'Ready',
        served: 'Served',
        completed: 'Completed'
    }
    return labels[status] || status
}

const formatStatus = (status) => {
    return status.charAt(0).toUpperCase() + status.slice(1)
}

const getStatusColor = (status) => {
    const colors = {
        pending: 'bg-yellow-100 text-yellow-800',
        confirmed: 'bg-blue-100 text-blue-800',
        preparing: 'bg-orange-100 text-orange-800',
        ready: 'bg-green-100 text-green-800',
        served: 'bg-purple-100 text-purple-800',
        completed: 'bg-gray-100 text-gray-800'
    }
    return colors[status] || 'bg-gray-100 text-gray-800'
}

const canUpdateStatus = (status) => {
    return ['confirmed', 'preparing', 'ready'].includes(status)
}

const formatDateTime = (dateTime) => {
    return new Date(dateTime).toLocaleString()
}

const showStatusModal = (order) => {
    selectedOrder.value = order
    showingStatusModal.value = true
}

const closeStatusModal = () => {
    showingStatusModal.value = false
    selectedOrder.value = null
}

const handleStatusUpdated = () => {
    closeStatusModal()
    router.reload({ only: ['orders', 'statusCounts'] })
}
</script>
