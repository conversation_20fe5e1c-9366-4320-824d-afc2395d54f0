<template>
    <WaiterLayout>
        <template #header>
            <div class="flex justify-between items-center">
                <div>
                    <h2 class="font-semibold text-xl text-gray-800 dark:text-gray-200 leading-tight">
                        Create New Order
                    </h2>
                    <p class="text-sm text-gray-600 dark:text-gray-400 mt-1">
                        Take a new order for your customers
                    </p>
                </div>
                <Link
                    :href="route('waiter.orders')"
                    class="bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded-md text-sm font-medium transition-colors duration-200"
                >
                    Back to Orders
                </Link>
            </div>
        </template>

        <div class="py-6">
            <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
                <!-- Order Creation Interface (Reusing POS Design) -->
                <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
                    <!-- Menu Items (Left Side) -->
                    <div class="lg:col-span-2">
                        <div class="bg-white dark:bg-gray-800 overflow-hidden shadow-sm sm:rounded-lg">
                            <div class="p-6">
                                <h3 class="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-4">
                                    Menu Items
                                </h3>
                                
                                <!-- Category Tabs -->
                                <div class="border-b border-gray-200 dark:border-gray-700 mb-6">
                                    <nav class="-mb-px flex space-x-8">
                                        <button
                                            v-for="category in categories"
                                            :key="category.id"
                                            @click="selectedCategory = category.id"
                                            :class="[
                                                'py-2 px-1 border-b-2 font-medium text-sm transition-colors duration-200',
                                                selectedCategory === category.id
                                                    ? 'border-blue-500 text-blue-600 dark:text-blue-400'
                                                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 dark:text-gray-400 dark:hover:text-gray-300'
                                            ]"
                                        >
                                            {{ category.name }}
                                        </button>
                                    </nav>
                                </div>

                                <!-- Menu Items Grid -->
                                <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
                                    <div
                                        v-for="item in filteredMenuItems"
                                        :key="item.id"
                                        @click="addToOrder(item)"
                                        class="border border-gray-200 dark:border-gray-700 rounded-lg p-4 hover:shadow-md cursor-pointer transition-all duration-200 hover:border-blue-300"
                                    >
                                        <div class="aspect-w-16 aspect-h-9 mb-3">
                                            <img
                                                v-if="item.image_url"
                                                :src="item.image_url"
                                                :alt="item.name"
                                                class="w-full h-32 object-cover rounded-md"
                                            />
                                            <div
                                                v-else
                                                class="w-full h-32 bg-gray-200 dark:bg-gray-700 rounded-md flex items-center justify-center"
                                            >
                                                <span class="text-gray-400 text-sm">No Image</span>
                                            </div>
                                        </div>
                                        <h4 class="font-medium text-gray-900 dark:text-gray-100 mb-1">
                                            {{ item.name }}
                                        </h4>
                                        <p class="text-sm text-gray-600 dark:text-gray-400 mb-2">
                                            {{ item.description }}
                                        </p>
                                        <div class="flex justify-between items-center">
                                            <span class="font-semibold text-green-600 dark:text-green-400">
                                                ${{ item.price }}
                                            </span>
                                            <button class="bg-blue-600 hover:bg-blue-700 text-white px-3 py-1 rounded text-sm transition-colors duration-200">
                                                Add
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Order Summary (Right Side) -->
                    <div class="lg:col-span-1">
                        <div class="bg-white dark:bg-gray-800 overflow-hidden shadow-sm sm:rounded-lg sticky top-6">
                            <div class="p-6">
                                <h3 class="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-4">
                                    Order Summary
                                </h3>

                                <!-- Order Details Form -->
                                <form @submit.prevent="createOrder" class="space-y-4 mb-6">
                                    <!-- Customer Selection -->
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                                            Customer
                                        </label>
                                        <select
                                            v-model="orderForm.customer_id"
                                            class="w-full rounded-md border-gray-300 dark:border-gray-700 dark:bg-gray-900 dark:text-gray-300 focus:border-blue-500 focus:ring-blue-500"
                                        >
                                            <option value="">Walk-in Customer</option>
                                            <option v-for="customer in customers" :key="customer.id" :value="customer.id">
                                                {{ customer.name }} ({{ customer.phone }})
                                            </option>
                                        </select>
                                    </div>

                                    <!-- Table Selection -->
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                                            Table
                                        </label>
                                        <select
                                            v-model="orderForm.table_id"
                                            class="w-full rounded-md border-gray-300 dark:border-gray-700 dark:bg-gray-900 dark:text-gray-300 focus:border-blue-500 focus:ring-blue-500"
                                        >
                                            <option value="">Takeaway</option>
                                            <option v-for="table in availableTables" :key="table.id" :value="table.id">
                                                {{ table.name }} ({{ table.floor?.name }})
                                            </option>
                                        </select>
                                    </div>

                                    <!-- Order Type -->
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                                            Order Type
                                        </label>
                                        <select
                                            v-model="orderForm.order_type"
                                            class="w-full rounded-md border-gray-300 dark:border-gray-700 dark:bg-gray-900 dark:text-gray-300 focus:border-blue-500 focus:ring-blue-500"
                                        >
                                            <option value="dine_in">Dine In</option>
                                            <option value="takeaway">Takeaway</option>
                                            <option value="delivery">Delivery</option>
                                        </select>
                                    </div>

                                    <!-- Guest Count -->
                                    <div v-if="orderForm.order_type === 'dine_in'">
                                        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                                            Guest Count
                                        </label>
                                        <input
                                            v-model.number="orderForm.guest_count"
                                            type="number"
                                            min="1"
                                            max="20"
                                            class="w-full rounded-md border-gray-300 dark:border-gray-700 dark:bg-gray-900 dark:text-gray-300 focus:border-blue-500 focus:ring-blue-500"
                                        />
                                    </div>
                                </form>

                                <!-- Order Items -->
                                <div class="space-y-3 mb-6">
                                    <h4 class="font-medium text-gray-900 dark:text-gray-100">Items</h4>
                                    <div v-if="orderItems.length === 0" class="text-center py-8 text-gray-500 dark:text-gray-400">
                                        No items added yet
                                    </div>
                                    <div v-else class="space-y-2">
                                        <div
                                            v-for="(item, index) in orderItems"
                                            :key="index"
                                            class="flex justify-between items-center p-3 bg-gray-50 dark:bg-gray-700 rounded-md"
                                        >
                                            <div class="flex-1">
                                                <p class="font-medium text-gray-900 dark:text-gray-100">{{ item.name }}</p>
                                                <p class="text-sm text-gray-600 dark:text-gray-400">${{ item.price }} each</p>
                                            </div>
                                            <div class="flex items-center space-x-2">
                                                <button
                                                    @click="decrementQuantity(index)"
                                                    class="w-8 h-8 rounded-full bg-gray-200 dark:bg-gray-600 text-gray-700 dark:text-gray-300 hover:bg-gray-300 dark:hover:bg-gray-500 transition-colors duration-200"
                                                >
                                                    -
                                                </button>
                                                <span class="w-8 text-center font-medium">{{ item.quantity }}</span>
                                                <button
                                                    @click="incrementQuantity(index)"
                                                    class="w-8 h-8 rounded-full bg-gray-200 dark:bg-gray-600 text-gray-700 dark:text-gray-300 hover:bg-gray-300 dark:hover:bg-gray-500 transition-colors duration-200"
                                                >
                                                    +
                                                </button>
                                                <button
                                                    @click="removeItem(index)"
                                                    class="w-8 h-8 rounded-full bg-red-100 dark:bg-red-900 text-red-600 dark:text-red-400 hover:bg-red-200 dark:hover:bg-red-800 transition-colors duration-200 ml-2"
                                                >
                                                    ×
                                                </button>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- Order Total -->
                                <div class="border-t border-gray-200 dark:border-gray-700 pt-4 mb-6">
                                    <div class="flex justify-between items-center text-lg font-semibold">
                                        <span>Total:</span>
                                        <span>${{ orderTotal.toFixed(2) }}</span>
                                    </div>
                                </div>

                                <!-- Action Buttons -->
                                <div class="space-y-3">
                                    <button
                                        @click="createOrder"
                                        :disabled="orderItems.length === 0 || processing"
                                        class="w-full bg-blue-600 hover:bg-blue-700 disabled:bg-gray-400 text-white py-3 rounded-md font-medium transition-colors duration-200"
                                    >
                                        {{ processing ? 'Creating...' : 'Create Order' }}
                                    </button>
                                    <button
                                        @click="clearOrder"
                                        :disabled="orderItems.length === 0"
                                        class="w-full bg-gray-600 hover:bg-gray-700 disabled:bg-gray-400 text-white py-2 rounded-md font-medium transition-colors duration-200"
                                    >
                                        Clear Order
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </WaiterLayout>
</template>

<script setup>
import { ref, computed, reactive } from 'vue'
import { Link, router } from '@inertiajs/vue3'
import WaiterLayout from '@/Layouts/WaiterLayout.vue'

const props = defineProps({
    branch: Object,
    categories: Array,
    floors: Array,
    availableTables: Array,
    customers: Array,
    waiter: Object,
    paymentMethods: Array,
    discountTypes: Array,
})

const selectedCategory = ref(props.categories[0]?.id || null)
const orderItems = ref([])
const processing = ref(false)

const orderForm = reactive({
    branch_id: props.branch.id,
    customer_id: '',
    table_id: '',
    order_type: 'dine_in',
    guest_count: 2,
    special_instructions: '',
})

const filteredMenuItems = computed(() => {
    const category = props.categories.find(c => c.id === selectedCategory.value)
    return category?.menu_items || []
})

const orderTotal = computed(() => {
    return orderItems.value.reduce((total, item) => total + (item.price * item.quantity), 0)
})

const addToOrder = (menuItem) => {
    const existingItem = orderItems.value.find(item => item.id === menuItem.id)
    if (existingItem) {
        existingItem.quantity++
    } else {
        orderItems.value.push({
            id: menuItem.id,
            name: menuItem.name,
            price: parseFloat(menuItem.price),
            quantity: 1
        })
    }
}

const incrementQuantity = (index) => {
    orderItems.value[index].quantity++
}

const decrementQuantity = (index) => {
    if (orderItems.value[index].quantity > 1) {
        orderItems.value[index].quantity--
    } else {
        removeItem(index)
    }
}

const removeItem = (index) => {
    orderItems.value.splice(index, 1)
}

const clearOrder = () => {
    orderItems.value = []
    orderForm.customer_id = ''
    orderForm.table_id = ''
    orderForm.special_instructions = ''
}

const createOrder = async () => {
    if (orderItems.value.length === 0) return

    processing.value = true

    try {
        // First create the order
        const response = await axios.post(route('waiter.orders.store'), {
            ...orderForm,
            items: orderItems.value
        })

        if (response.data.success) {
            // Redirect to order details
            router.visit(route('waiter.order-details', response.data.order.id))
        }
    } catch (error) {
        console.error('Error creating order:', error)
        alert('Failed to create order. Please try again.')
    } finally {
        processing.value = false
    }
}
</script>
