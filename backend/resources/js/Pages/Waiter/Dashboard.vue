<script setup>
import { ref, onMounted, computed } from 'vue';
import { Head, Link } from '@inertiajs/vue3';
import WaiterLayout from '@/Layouts/WaiterLayout.vue';

const props = defineProps({
    tables: Array,
    activeOrders: Array,
    todayStats: Object,
    myStats: Object,
});

const t = ref(() => '');

onMounted(() => {
    setTimeout(() => {
        t.value = window.t || ((key, fallback) => fallback || key);
    }, 100);
});

// Computed properties
const occupiedTables = computed(() => {
    return props.tables?.filter(table => table.status === 'occupied').length || 0;
});

const availableTables = computed(() => {
    return props.tables?.filter(table => table.status === 'available').length || 0;
});

const reservedTables = computed(() => {
    return props.tables?.filter(table => table.status === 'reserved').length || 0;
});

const pendingOrders = computed(() => {
    return props.activeOrders?.filter(order => order.status === 'pending').length || 0;
});

const preparingOrders = computed(() => {
    return props.activeOrders?.filter(order => order.status === 'preparing').length || 0;
});

const readyOrders = computed(() => {
    return props.activeOrders?.filter(order => order.status === 'ready').length || 0;
});

// Table status colors
const getTableStatusColor = (status) => {
    const colors = {
        'available': 'bg-green-100 text-green-800 border-green-200',
        'occupied': 'bg-red-100 text-red-800 border-red-200',
        'reserved': 'bg-yellow-100 text-yellow-800 border-yellow-200',
        'cleaning': 'bg-blue-100 text-blue-800 border-blue-200'
    };
    return colors[status] || 'bg-gray-100 text-gray-800 border-gray-200';
};

// Order status colors
const getOrderStatusColor = (status) => {
    const colors = {
        'pending': 'bg-yellow-100 text-yellow-800',
        'confirmed': 'bg-blue-100 text-blue-800',
        'preparing': 'bg-orange-100 text-orange-800',
        'ready': 'bg-green-100 text-green-800',
        'served': 'bg-gray-100 text-gray-800'
    };
    return colors[status] || 'bg-gray-100 text-gray-800';
};

// Format currency
const formatCurrency = (amount) => {
    return new Intl.NumberFormat('en-US', {
        style: 'currency',
        currency: 'USD'
    }).format(amount);
};

// Format time
const formatTime = (dateString) => {
    return new Date(dateString).toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
};
</script>

<template>
    <Head :title="t('dashboard_titles.waiter_dashboard', 'Waiter Dashboard')" />

    <WaiterLayout>
        <template #header>
            <div class="flex justify-between items-center">
                <h2 class="font-semibold text-xl text-gray-800 dark:text-gray-200 leading-tight">
                    {{ t('dashboard_titles.waiter_dashboard', 'Waiter Dashboard') }}
                </h2>
                <div class="flex items-center space-x-3">
                    <Link 
                        :href="route('waiter.orders.create')"
                        class="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-md text-sm font-medium flex items-center"
                    >
                        <span class="mr-2">➕</span>
                        {{ t('order_management.new_order', 'New Order') }}
                    </Link>
                </div>
            </div>
        </template>

        <div class="space-y-6">
            <!-- Quick Stats -->
            <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
                <div class="bg-white dark:bg-gray-800 overflow-hidden shadow rounded-lg">
                    <div class="p-5">
                        <div class="flex items-center">
                            <div class="flex-shrink-0">
                                <span class="text-2xl">🪑</span>
                            </div>
                            <div class="ml-5 w-0 flex-1">
                                <dl>
                                    <dt class="text-sm font-medium text-gray-500 dark:text-gray-400 truncate">
                                        {{ t('table_management.occupied', 'Occupied') }}
                                    </dt>
                                    <dd class="text-lg font-medium text-gray-900 dark:text-gray-100">
                                        {{ occupiedTables }} / {{ tables?.length || 0 }}
                                    </dd>
                                </dl>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="bg-white dark:bg-gray-800 overflow-hidden shadow rounded-lg">
                    <div class="p-5">
                        <div class="flex items-center">
                            <div class="flex-shrink-0">
                                <span class="text-2xl">🔥</span>
                            </div>
                            <div class="ml-5 w-0 flex-1">
                                <dl>
                                    <dt class="text-sm font-medium text-gray-500 dark:text-gray-400 truncate">
                                        {{ t('order_management.pending', 'Pending') }}
                                    </dt>
                                    <dd class="text-lg font-medium text-gray-900 dark:text-gray-100">
                                        {{ pendingOrders }}
                                    </dd>
                                </dl>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="bg-white dark:bg-gray-800 overflow-hidden shadow rounded-lg">
                    <div class="p-5">
                        <div class="flex items-center">
                            <div class="flex-shrink-0">
                                <span class="text-2xl">🍳</span>
                            </div>
                            <div class="ml-5 w-0 flex-1">
                                <dl>
                                    <dt class="text-sm font-medium text-gray-500 dark:text-gray-400 truncate">
                                        {{ t('order_management.preparing', 'Preparing') }}
                                    </dt>
                                    <dd class="text-lg font-medium text-gray-900 dark:text-gray-100">
                                        {{ preparingOrders }}
                                    </dd>
                                </dl>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="bg-white dark:bg-gray-800 overflow-hidden shadow rounded-lg">
                    <div class="p-5">
                        <div class="flex items-center">
                            <div class="flex-shrink-0">
                                <span class="text-2xl">✅</span>
                            </div>
                            <div class="ml-5 w-0 flex-1">
                                <dl>
                                    <dt class="text-sm font-medium text-gray-500 dark:text-gray-400 truncate">
                                        {{ t('order_management.ready', 'Ready') }}
                                    </dt>
                                    <dd class="text-lg font-medium text-gray-900 dark:text-gray-100">
                                        {{ readyOrders }}
                                    </dd>
                                </dl>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Main Content Grid -->
            <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
                <!-- Table Status Overview -->
                <div class="lg:col-span-2">
                    <div class="bg-white dark:bg-gray-800 shadow rounded-lg">
                        <div class="px-4 py-5 sm:p-6">
                            <h3 class="text-lg leading-6 font-medium text-gray-900 dark:text-gray-100 mb-4">
                                {{ t('table_management.table_status', 'Table Status') }}
                            </h3>
                            <div class="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 gap-4">
                                <div 
                                    v-for="table in tables" 
                                    :key="table.id"
                                    :class="getTableStatusColor(table.status)"
                                    class="relative rounded-lg border-2 p-4 cursor-pointer hover:shadow-md transition-shadow duration-200"
                                >
                                    <div class="text-center">
                                        <div class="text-lg font-bold">{{ table.number }}</div>
                                        <div class="text-xs mt-1">
                                            {{ t(`table_management.${table.status}`, table.status) }}
                                        </div>
                                        <div class="text-xs text-gray-600 mt-1">
                                            {{ table.capacity }} {{ t('table_management.capacity', 'seats') }}
                                        </div>
                                    </div>
                                    <div v-if="table.current_order" class="absolute top-1 right-1">
                                        <span class="inline-flex items-center px-1.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                                            #{{ table.current_order.order_number }}
                                        </span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Active Orders -->
                <div class="lg:col-span-1">
                    <div class="bg-white dark:bg-gray-800 shadow rounded-lg">
                        <div class="px-4 py-5 sm:p-6">
                            <h3 class="text-lg leading-6 font-medium text-gray-900 dark:text-gray-100 mb-4">
                                {{ t('order_management.active_orders', 'Active Orders') }}
                            </h3>
                            <div class="space-y-3 max-h-96 overflow-y-auto">
                                <div 
                                    v-for="order in activeOrders?.slice(0, 10)" 
                                    :key="order.id"
                                    class="border border-gray-200 dark:border-gray-600 rounded-lg p-3 hover:bg-gray-50 dark:hover:bg-gray-700 cursor-pointer"
                                >
                                    <div class="flex justify-between items-start mb-2">
                                        <div>
                                            <p class="text-sm font-medium text-gray-900 dark:text-gray-100">
                                                #{{ order.order_number }}
                                            </p>
                                            <p class="text-xs text-gray-500 dark:text-gray-400">
                                                {{ t('table_management.table_number', 'Table') }} {{ order.table?.number || 'N/A' }}
                                            </p>
                                        </div>
                                        <span :class="getOrderStatusColor(order.status)" class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium">
                                            {{ t(`order_management.${order.status}`, order.status) }}
                                        </span>
                                    </div>
                                    <div class="flex justify-between items-center">
                                        <span class="text-sm font-medium text-gray-900 dark:text-gray-100">
                                            {{ formatCurrency(order.total_amount) }}
                                        </span>
                                        <span class="text-xs text-gray-500 dark:text-gray-400">
                                            {{ formatTime(order.created_at) }}
                                        </span>
                                    </div>
                                </div>
                                <div v-if="!activeOrders?.length" class="text-center py-8">
                                    <p class="text-gray-500 dark:text-gray-400">{{ t('messages.no_data', 'No active orders') }}</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Today's Performance -->
            <div class="bg-white dark:bg-gray-800 shadow rounded-lg">
                <div class="px-4 py-5 sm:p-6">
                    <h3 class="text-lg leading-6 font-medium text-gray-900 dark:text-gray-100 mb-4">
                        {{ t('time_date.today', 'Today\'s Performance') }}
                    </h3>
                    <div class="grid grid-cols-1 md:grid-cols-4 gap-6">
                        <div class="text-center">
                            <div class="text-2xl font-bold text-gray-900 dark:text-gray-100">
                                {{ myStats?.orders_taken || 0 }}
                            </div>
                            <div class="text-sm text-gray-500 dark:text-gray-400">
                                {{ t('order_management.orders_taken', 'Orders Taken') }}
                            </div>
                        </div>
                        <div class="text-center">
                            <div class="text-2xl font-bold text-gray-900 dark:text-gray-100">
                                {{ formatCurrency(myStats?.total_sales || 0) }}
                            </div>
                            <div class="text-sm text-gray-500 dark:text-gray-400">
                                {{ t('reports_analytics.total_sales', 'Total Sales') }}
                            </div>
                        </div>
                        <div class="text-center">
                            <div class="text-2xl font-bold text-gray-900 dark:text-gray-100">
                                {{ myStats?.tables_served || 0 }}
                            </div>
                            <div class="text-sm text-gray-500 dark:text-gray-400">
                                {{ t('table_management.tables_served', 'Tables Served') }}
                            </div>
                        </div>
                        <div class="text-center">
                            <div class="text-2xl font-bold text-gray-900 dark:text-gray-100">
                                {{ formatCurrency(myStats?.average_order_value || 0) }}
                            </div>
                            <div class="text-sm text-gray-500 dark:text-gray-400">
                                {{ t('order_management.average_value', 'Avg Order') }}
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </WaiterLayout>
</template>
