<template>
    <ManagerLayout>
        <Head title="Loyalty Accounts" />

        <div class="py-6">
            <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                <!-- Header -->
                <div class="flex justify-between items-center mb-8">
                    <div>
                        <h1 class="text-3xl font-bold text-gray-900">{{ $t('loyalty.accounts.title') }}</h1>
                        <p class="mt-2 text-gray-600">{{ $t('loyalty.accounts.description') }}</p>
                    </div>
                    <div class="flex space-x-3">
                        <button
                            @click="exportAccounts"
                            class="bg-green-600 text-white px-4 py-2 rounded-md hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-green-500"
                        >
                            <i class="fas fa-download mr-2"></i>
                            {{ $t('common.export') }}
                        </button>
                    </div>
                </div>

                <!-- Filters -->
                <div class="bg-white shadow rounded-lg p-6 mb-6">
                    <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">
                                {{ $t('common.search') }}
                            </label>
                            <input
                                type="text"
                                v-model="searchForm.search"
                                @input="debouncedSearch"
                                :placeholder="$t('loyalty.accounts.search_placeholder')"
                                class="w-full rounded-md border-gray-300 shadow-sm focus:border-orange-500 focus:ring-orange-500"
                            >
                        </div>

                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">
                                {{ $t('loyalty.accounts.tier') }}
                            </label>
                            <select
                                v-model="searchForm.tier"
                                @change="search"
                                class="w-full rounded-md border-gray-300 shadow-sm focus:border-orange-500 focus:ring-orange-500"
                            >
                                <option value="">{{ $t('common.all') }}</option>
                                <option v-for="tier in tiers" :key="tier" :value="tier">
                                    {{ $t(`loyalty.tiers.${tier}`) }}
                                </option>
                            </select>
                        </div>

                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">
                                {{ $t('common.status') }}
                            </label>
                            <select
                                v-model="searchForm.status"
                                @change="search"
                                class="w-full rounded-md border-gray-300 shadow-sm focus:border-orange-500 focus:ring-orange-500"
                            >
                                <option value="">{{ $t('common.all') }}</option>
                                <option value="active">{{ $t('common.active') }}</option>
                                <option value="inactive">{{ $t('common.inactive') }}</option>
                            </select>
                        </div>

                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">
                                {{ $t('common.sort_by') }}
                            </label>
                            <select
                                v-model="searchForm.sort"
                                @change="search"
                                class="w-full rounded-md border-gray-300 shadow-sm focus:border-orange-500 focus:ring-orange-500"
                            >
                                <option value="created_at">{{ $t('common.created_at') }}</option>
                                <option value="total_points">{{ $t('loyalty.accounts.total_points') }}</option>
                                <option value="total_spent">{{ $t('loyalty.accounts.total_spent') }}</option>
                                <option value="last_transaction_at">{{ $t('loyalty.accounts.last_transaction') }}</option>
                            </select>
                        </div>
                    </div>
                </div>

                <!-- Accounts Table -->
                <div class="bg-white shadow rounded-lg overflow-hidden">
                    <div class="overflow-x-auto">
                        <table class="min-w-full divide-y divide-gray-200">
                            <thead class="bg-gray-50">
                                <tr>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        {{ $t('loyalty.accounts.customer') }}
                                    </th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        {{ $t('loyalty.accounts.contact') }}
                                    </th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        {{ $t('loyalty.accounts.points') }}
                                    </th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        {{ $t('loyalty.accounts.tier') }}
                                    </th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        {{ $t('loyalty.accounts.stats') }}
                                    </th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        {{ $t('common.status') }}
                                    </th>
                                    <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        {{ $t('common.actions') }}
                                    </th>
                                </tr>
                            </thead>
                            <tbody class="bg-white divide-y divide-gray-200">
                                <tr v-for="account in accounts.data" :key="account.id" class="hover:bg-gray-50">
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div>
                                            <div class="text-sm font-medium text-gray-900">{{ account.customer_name }}</div>
                                            <div class="text-sm text-gray-500">{{ account.phone_number }}</div>
                                        </div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="text-sm text-gray-900">{{ account.email || '-' }}</div>
                                        <div class="text-sm text-gray-500">{{ account.address || '-' }}</div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="text-sm font-medium text-gray-900">
                                            {{ formatNumber(account.total_points) }} {{ $t('loyalty.points') }}
                                        </div>
                                        <div class="text-sm text-gray-500">
                                            {{ $t('loyalty.accounts.lifetime_earned') }}: {{ formatNumber(account.lifetime_points_earned) }}
                                        </div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <span
                                            class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium"
                                            :style="{ backgroundColor: account.tier_color + '20', color: account.tier_color }"
                                        >
                                            {{ $t(`loyalty.tiers.${account.tier}`) }}
                                        </span>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                        <div>${{ formatCurrency(account.total_spent) }} {{ $t('loyalty.accounts.spent') }}</div>
                                        <div>{{ account.total_orders }} {{ $t('loyalty.accounts.orders') }}</div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <span
                                            :class="[
                                                'inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium',
                                                account.is_active 
                                                    ? 'bg-green-100 text-green-800' 
                                                    : 'bg-red-100 text-red-800'
                                            ]"
                                        >
                                            {{ account.is_active ? $t('common.active') : $t('common.inactive') }}
                                        </span>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                                        <div class="flex justify-end space-x-2">
                                            <Link
                                                :href="route('loyalty.accounts.show', account.id)"
                                                class="text-orange-600 hover:text-orange-900"
                                            >
                                                <i class="fas fa-eye"></i>
                                            </Link>
                                            <button
                                                @click="openAdjustModal(account)"
                                                class="text-blue-600 hover:text-blue-900"
                                            >
                                                <i class="fas fa-edit"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>

                    <!-- Pagination -->
                    <div v-if="accounts.links" class="bg-white px-4 py-3 border-t border-gray-200 sm:px-6">
                        <div class="flex items-center justify-between">
                            <div class="flex-1 flex justify-between sm:hidden">
                                <Link
                                    v-if="accounts.prev_page_url"
                                    :href="accounts.prev_page_url"
                                    class="relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50"
                                >
                                    {{ $t('common.previous') }}
                                </Link>
                                <Link
                                    v-if="accounts.next_page_url"
                                    :href="accounts.next_page_url"
                                    class="ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50"
                                >
                                    {{ $t('common.next') }}
                                </Link>
                            </div>
                            <div class="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
                                <div>
                                    <p class="text-sm text-gray-700">
                                        {{ $t('common.showing') }}
                                        <span class="font-medium">{{ accounts.from }}</span>
                                        {{ $t('common.to') }}
                                        <span class="font-medium">{{ accounts.to }}</span>
                                        {{ $t('common.of') }}
                                        <span class="font-medium">{{ accounts.total }}</span>
                                        {{ $t('common.results') }}
                                    </p>
                                </div>
                                <div>
                                    <nav class="relative z-0 inline-flex rounded-md shadow-sm -space-x-px">
                                        <Link
                                            v-for="link in accounts.links"
                                            :key="link.label"
                                            :href="link.url"
                                            v-html="link.label"
                                            :class="[
                                                'relative inline-flex items-center px-4 py-2 border text-sm font-medium',
                                                link.active
                                                    ? 'z-10 bg-orange-50 border-orange-500 text-orange-600'
                                                    : 'bg-white border-gray-300 text-gray-500 hover:bg-gray-50',
                                                link.url ? 'cursor-pointer' : 'cursor-not-allowed opacity-50'
                                            ]"
                                        />
                                    </nav>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Points Adjustment Modal -->
        <PointsAdjustmentModal
            v-if="showAdjustModal"
            :account="selectedAccount"
            @close="closeAdjustModal"
            @success="handleAdjustmentSuccess"
        />
    </ManagerLayout>
</template>

<script setup>
import { ref, reactive } from 'vue'
import { Head, Link, router } from '@inertiajs/vue3'
import { useI18n } from 'vue-i18n'
import { debounce } from 'lodash'
import ManagerLayout from '@/Layouts/ManagerLayout.vue'
import PointsAdjustmentModal from '@/Components/Loyalty/PointsAdjustmentModal.vue'

const { t } = useI18n()

const props = defineProps({
    accounts: Object,
    filters: Object,
    tiers: Array,
})

const searchForm = reactive({
    search: props.filters.search || '',
    tier: props.filters.tier || '',
    status: props.filters.status || '',
    sort: props.filters.sort || 'created_at',
    direction: props.filters.direction || 'desc',
})

const showAdjustModal = ref(false)
const selectedAccount = ref(null)

const search = () => {
    router.get(route('loyalty.accounts'), searchForm, {
        preserveState: true,
        replace: true,
    })
}

const debouncedSearch = debounce(search, 300)

const exportAccounts = () => {
    window.open(route('loyalty.accounts.export', searchForm))
}

const openAdjustModal = (account) => {
    selectedAccount.value = account
    showAdjustModal.value = true
}

const closeAdjustModal = () => {
    showAdjustModal.value = false
    selectedAccount.value = null
}

const handleAdjustmentSuccess = () => {
    closeAdjustModal()
    router.reload()
}

const formatNumber = (number) => {
    return new Intl.NumberFormat().format(number || 0)
}

const formatCurrency = (amount) => {
    return new Intl.NumberFormat('en-US', {
        minimumFractionDigits: 2,
        maximumFractionDigits: 2,
    }).format(amount || 0)
}
</script>
