<template>
    <ManagerLayout>
        <Head title="Loyalty Program Settings" />

        <div class="py-6">
            <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                <!-- Header -->
                <div class="mb-8">
                    <h1 class="text-3xl font-bold text-gray-900">{{ $t('loyalty.settings.title') }}</h1>
                    <p class="mt-2 text-gray-600">{{ $t('loyalty.settings.description') }}</p>
                </div>

                <!-- Statistics Cards -->
                <div v-if="settings.enabled" class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
                    <div class="bg-white rounded-lg shadow p-6">
                        <div class="flex items-center">
                            <div class="p-3 rounded-full bg-blue-100">
                                <i class="fas fa-users text-blue-600 text-xl"></i>
                            </div>
                            <div class="ml-4">
                                <p class="text-sm font-medium text-gray-600">{{ $t('loyalty.stats.total_accounts') }}</p>
                                <p class="text-2xl font-bold text-gray-900">{{ statistics.total_accounts || 0 }}</p>
                            </div>
                        </div>
                    </div>

                    <div class="bg-white rounded-lg shadow p-6">
                        <div class="flex items-center">
                            <div class="p-3 rounded-full bg-green-100">
                                <i class="fas fa-coins text-green-600 text-xl"></i>
                            </div>
                            <div class="ml-4">
                                <p class="text-sm font-medium text-gray-600">{{ $t('loyalty.stats.points_issued') }}</p>
                                <p class="text-2xl font-bold text-gray-900">{{ formatNumber(statistics.total_points_issued) }}</p>
                            </div>
                        </div>
                    </div>

                    <div class="bg-white rounded-lg shadow p-6">
                        <div class="flex items-center">
                            <div class="p-3 rounded-full bg-orange-100">
                                <i class="fas fa-gift text-orange-600 text-xl"></i>
                            </div>
                            <div class="ml-4">
                                <p class="text-sm font-medium text-gray-600">{{ $t('loyalty.stats.points_redeemed') }}</p>
                                <p class="text-2xl font-bold text-gray-900">{{ formatNumber(statistics.total_points_redeemed) }}</p>
                            </div>
                        </div>
                    </div>

                    <div class="bg-white rounded-lg shadow p-6">
                        <div class="flex items-center">
                            <div class="p-3 rounded-full bg-purple-100">
                                <i class="fas fa-chart-line text-purple-600 text-xl"></i>
                            </div>
                            <div class="ml-4">
                                <p class="text-sm font-medium text-gray-600">{{ $t('loyalty.stats.active_30_days') }}</p>
                                <p class="text-2xl font-bold text-gray-900">{{ statistics.active_accounts_30_days || 0 }}</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Settings Form -->
                <div class="bg-white shadow rounded-lg">
                    <form @submit.prevent="updateSettings" class="p-6 space-y-6">
                        <!-- Enable/Disable Toggle -->
                        <div class="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                            <div>
                                <h3 class="text-lg font-medium text-gray-900">{{ $t('loyalty.settings.enable_program') }}</h3>
                                <p class="text-sm text-gray-600">{{ $t('loyalty.settings.enable_description') }}</p>
                            </div>
                            <label class="relative inline-flex items-center cursor-pointer">
                                <input
                                    type="checkbox"
                                    v-model="form.enabled"
                                    class="sr-only peer"
                                >
                                <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-orange-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-orange-600"></div>
                            </label>
                        </div>

                        <div v-if="form.enabled" class="space-y-6">
                            <!-- Basic Settings -->
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">
                                        {{ $t('loyalty.settings.points_per_dollar') }}
                                    </label>
                                    <input
                                        type="number"
                                        step="0.01"
                                        min="0"
                                        max="100"
                                        v-model="form.points_per_dollar"
                                        class="w-full rounded-md border-gray-300 shadow-sm focus:border-orange-500 focus:ring-orange-500"
                                        required
                                    >
                                    <p class="mt-1 text-xs text-gray-500">{{ $t('loyalty.settings.points_per_dollar_help') }}</p>
                                </div>

                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">
                                        {{ $t('loyalty.settings.points_for_dollar') }}
                                    </label>
                                    <input
                                        type="number"
                                        min="1"
                                        max="1000"
                                        v-model="form.points_for_dollar_discount"
                                        class="w-full rounded-md border-gray-300 shadow-sm focus:border-orange-500 focus:ring-orange-500"
                                        required
                                    >
                                    <p class="mt-1 text-xs text-gray-500">{{ $t('loyalty.settings.points_for_dollar_help') }}</p>
                                </div>

                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">
                                        {{ $t('loyalty.settings.max_discount_percentage') }}
                                    </label>
                                    <input
                                        type="number"
                                        step="0.01"
                                        min="0"
                                        max="100"
                                        v-model="form.max_discount_percentage"
                                        class="w-full rounded-md border-gray-300 shadow-sm focus:border-orange-500 focus:ring-orange-500"
                                        required
                                    >
                                    <p class="mt-1 text-xs text-gray-500">{{ $t('loyalty.settings.max_discount_help') }}</p>
                                </div>

                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">
                                        {{ $t('loyalty.settings.minimum_order_amount') }}
                                    </label>
                                    <input
                                        type="number"
                                        step="0.01"
                                        min="0"
                                        v-model="form.minimum_order_amount"
                                        class="w-full rounded-md border-gray-300 shadow-sm focus:border-orange-500 focus:ring-orange-500"
                                        required
                                    >
                                    <p class="mt-1 text-xs text-gray-500">{{ $t('loyalty.settings.minimum_order_help') }}</p>
                                </div>
                            </div>

                            <!-- Advanced Settings -->
                            <div class="border-t pt-6">
                                <h3 class="text-lg font-medium text-gray-900 mb-4">{{ $t('loyalty.settings.advanced_settings') }}</h3>
                                
                                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700 mb-2">
                                            {{ $t('loyalty.settings.points_expiry_days') }}
                                        </label>
                                        <input
                                            type="number"
                                            min="1"
                                            max="3650"
                                            v-model="form.points_expiry_days"
                                            class="w-full rounded-md border-gray-300 shadow-sm focus:border-orange-500 focus:ring-orange-500"
                                            placeholder="Leave empty for no expiry"
                                        >
                                        <p class="mt-1 text-xs text-gray-500">{{ $t('loyalty.settings.expiry_help') }}</p>
                                    </div>
                                </div>
                            </div>

                            <!-- Birthday Bonus -->
                            <div class="border-t pt-6">
                                <div class="flex items-center justify-between mb-4">
                                    <div>
                                        <h3 class="text-lg font-medium text-gray-900">{{ $t('loyalty.settings.birthday_bonus') }}</h3>
                                        <p class="text-sm text-gray-600">{{ $t('loyalty.settings.birthday_bonus_description') }}</p>
                                    </div>
                                    <label class="relative inline-flex items-center cursor-pointer">
                                        <input
                                            type="checkbox"
                                            v-model="form.birthday_bonus_enabled"
                                            class="sr-only peer"
                                        >
                                        <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-orange-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-orange-600"></div>
                                    </label>
                                </div>

                                <div v-if="form.birthday_bonus_enabled" class="grid grid-cols-1 md:grid-cols-2 gap-6">
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700 mb-2">
                                            {{ $t('loyalty.settings.birthday_bonus_points') }}
                                        </label>
                                        <input
                                            type="number"
                                            min="0"
                                            max="10000"
                                            v-model="form.birthday_bonus_points"
                                            class="w-full rounded-md border-gray-300 shadow-sm focus:border-orange-500 focus:ring-orange-500"
                                            required
                                        >
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Submit Button -->
                        <div class="flex justify-end pt-6 border-t">
                            <button
                                type="submit"
                                :disabled="processing"
                                class="bg-orange-600 text-white px-6 py-2 rounded-md hover:bg-orange-700 focus:outline-none focus:ring-2 focus:ring-orange-500 focus:ring-offset-2 disabled:opacity-50"
                            >
                                <i v-if="processing" class="fas fa-spinner fa-spin mr-2"></i>
                                {{ $t('common.save_changes') }}
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </ManagerLayout>
</template>

<script setup>
import { ref, reactive } from 'vue'
import { Head, useForm } from '@inertiajs/vue3'
import { useI18n } from 'vue-i18n'
import ManagerLayout from '@/Layouts/ManagerLayout.vue'

const { t } = useI18n()

const props = defineProps({
    settings: Object,
    statistics: Object,
})

const form = useForm({
    enabled: props.settings.enabled,
    points_per_dollar: props.settings.points_per_dollar,
    points_for_dollar_discount: props.settings.points_for_dollar_discount,
    max_discount_percentage: props.settings.max_discount_percentage,
    minimum_order_amount: props.settings.minimum_order_amount,
    points_expiry_days: props.settings.points_expiry_days,
    birthday_bonus_enabled: props.settings.birthday_bonus_enabled,
    birthday_bonus_points: props.settings.birthday_bonus_points,
    referral_program_enabled: props.settings.referral_program_enabled,
    referral_bonus_points: props.settings.referral_bonus_points,
})

const processing = ref(false)

const updateSettings = () => {
    processing.value = true
    form.post(route('loyalty.settings.update'), {
        onFinish: () => {
            processing.value = false
        }
    })
}

const formatNumber = (number) => {
    return new Intl.NumberFormat().format(number || 0)
}
</script>
