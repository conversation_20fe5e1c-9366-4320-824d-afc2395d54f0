<script setup>
import { Head, <PERSON>, router } from '@inertiajs/vue3';
import ManagerLayout from '@/Layouts/ManagerLayout.vue';
import { ref, computed } from 'vue';

const props = defineProps({
    order: Object,
});

// Methods
const formatPrice = (price) => {
    return new Intl.NumberFormat('en-US', {
        style: 'currency',
        currency: 'USD'
    }).format(price);
};

const formatDate = (date) => {
    return new Date(date).toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'short',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit'
    });
};

const getStatusBadge = (status) => {
    const badges = {
        pending: { class: 'bg-yellow-100 text-yellow-800', text: 'Pending' },
        confirmed: { class: 'bg-blue-100 text-blue-800', text: 'Confirmed' },
        preparing: { class: 'bg-orange-100 text-orange-800', text: 'Preparing' },
        ready: { class: 'bg-purple-100 text-purple-800', text: 'Ready' },
        served: { class: 'bg-green-100 text-green-800', text: 'Served' },
        delivered: { class: 'bg-green-100 text-green-800', text: 'Delivered' },
        cancelled: { class: 'bg-red-100 text-red-800', text: 'Cancelled' },
    };
    return badges[status] || { class: 'bg-gray-100 text-gray-800', text: status };
};

const getPaymentStatusBadge = (status) => {
    const badges = {
        pending: { class: 'bg-yellow-100 text-yellow-800', text: 'Pending' },
        paid: { class: 'bg-green-100 text-green-800', text: 'Paid' },
        failed: { class: 'bg-red-100 text-red-800', text: 'Failed' },
        refunded: { class: 'bg-gray-100 text-gray-800', text: 'Refunded' },
    };
    return badges[status] || { class: 'bg-gray-100 text-gray-800', text: status };
};

const getOrderTypeIcon = (type) => {
    const icons = {
        dine_in: '🍽️',
        takeaway: '🥡',
        delivery: '🚚',
    };
    return icons[type] || '📦';
};

const updateStatus = (newStatus) => {
    router.patch(route('orders.update', props.order.id), {
        status: newStatus
    }, {
        preserveScroll: true,
        onSuccess: () => {
            // Order will be refreshed automatically
        }
    });
};

const printOrder = () => {
    window.open(route('orders.print', props.order.id), '_blank');
};

const downloadInvoice = () => {
    window.open(route('orders.invoice', props.order.id), '_blank');
};
</script>

<template>
    <ManagerLayout :title="`Order #${order.order_number}`">
        <template #header>
            <div class="flex justify-between items-center">
                <div>
                    <h2 class="font-semibold text-xl text-gray-800 dark:text-gray-200 leading-tight">
                        🛒 Order #{{ order.order_number }}
                    </h2>
                    <p class="text-sm text-gray-600 dark:text-gray-400 mt-1">
                        {{ formatDate(order.created_at) }}
                    </p>
                </div>
                
                <div class="flex space-x-3">
                    <button
                        @click="printOrder"
                        class="inline-flex items-center px-4 py-2 bg-gray-600 border border-transparent rounded-md font-semibold text-xs text-white uppercase tracking-widest hover:bg-gray-700 focus:bg-gray-700 active:bg-gray-900 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2 transition ease-in-out duration-150"
                    >
                        🖨️ Print
                    </button>
                    <button
                        @click="downloadInvoice"
                        class="inline-flex items-center px-4 py-2 bg-green-600 border border-transparent rounded-md font-semibold text-xs text-white uppercase tracking-widest hover:bg-green-700 focus:bg-green-700 active:bg-green-900 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2 transition ease-in-out duration-150"
                    >
                        📄 Invoice
                    </button>
                    <Link
                        :href="route('orders.edit', order.id)"
                        class="inline-flex items-center px-4 py-2 bg-indigo-600 border border-transparent rounded-md font-semibold text-xs text-white uppercase tracking-widest hover:bg-indigo-700 focus:bg-indigo-700 active:bg-indigo-900 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 transition ease-in-out duration-150"
                    >
                        ✏️ Edit Order
                    </Link>
                </div>
            </div>
        </template>

        <div class="py-6">
            <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
                    <!-- Order Details -->
                    <div class="lg:col-span-2 space-y-6">
                        <!-- Order Summary -->
                        <div class="bg-white dark:bg-gray-800 shadow-sm rounded-lg">
                            <div class="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
                                <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100">
                                    📋 Order Summary
                                </h3>
                            </div>
                            <div class="p-6">
                                <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300">
                                            Order Type
                                        </label>
                                        <div class="mt-1 flex items-center">
                                            <span class="mr-2">{{ getOrderTypeIcon(order.order_type) }}</span>
                                            <span class="text-sm text-gray-900 dark:text-gray-100 capitalize">
                                                {{ order.order_type?.replace('_', ' ') }}
                                            </span>
                                        </div>
                                    </div>
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300">
                                            Status
                                        </label>
                                        <div class="mt-1">
                                            <span
                                                :class="[
                                                    'px-2 py-1 text-xs font-medium rounded-full',
                                                    getStatusBadge(order.status).class
                                                ]"
                                            >
                                                {{ getStatusBadge(order.status).text }}
                                            </span>
                                        </div>
                                    </div>
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300">
                                            Payment Status
                                        </label>
                                        <div class="mt-1">
                                            <span
                                                :class="[
                                                    'px-2 py-1 text-xs font-medium rounded-full',
                                                    getPaymentStatusBadge(order.payment_status).class
                                                ]"
                                            >
                                                {{ getPaymentStatusBadge(order.payment_status).text }}
                                            </span>
                                        </div>
                                    </div>
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300">
                                            Total Amount
                                        </label>
                                        <div class="mt-1 text-lg font-semibold text-gray-900 dark:text-gray-100">
                                            {{ formatPrice(order.total_amount) }}
                                        </div>
                                    </div>
                                </div>

                                <div v-if="order.table || order.customer || order.waiter" class="mt-6 grid grid-cols-1 md:grid-cols-3 gap-4">
                                    <div v-if="order.table">
                                        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300">
                                            Table
                                        </label>
                                        <div class="mt-1 text-sm text-gray-900 dark:text-gray-100">
                                            🪑 {{ order.table.name }}
                                        </div>
                                    </div>
                                    <div v-if="order.customer">
                                        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300">
                                            Customer
                                        </label>
                                        <div class="mt-1 text-sm text-gray-900 dark:text-gray-100">
                                            👤 {{ order.customer.name }}
                                        </div>
                                        <div v-if="order.customer.phone" class="text-sm text-gray-500">
                                            📞 {{ order.customer.phone }}
                                        </div>
                                    </div>
                                    <div v-if="order.waiter">
                                        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300">
                                            Waiter
                                        </label>
                                        <div class="mt-1 text-sm text-gray-900 dark:text-gray-100">
                                            👨‍💼 {{ order.waiter.name }}
                                        </div>
                                    </div>
                                </div>

                                <div v-if="order.notes" class="mt-6">
                                    <label class="block text-sm font-medium text-gray-700 dark:text-gray-300">
                                        Notes
                                    </label>
                                    <div class="mt-1 text-sm text-gray-900 dark:text-gray-100 bg-gray-50 dark:bg-gray-700 p-3 rounded-md">
                                        {{ order.notes }}
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Order Items -->
                        <div class="bg-white dark:bg-gray-800 shadow-sm rounded-lg">
                            <div class="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
                                <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100">
                                    🍽️ Order Items
                                </h3>
                            </div>
                            <div class="overflow-x-auto">
                                <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                                    <thead class="bg-gray-50 dark:bg-gray-700">
                                        <tr>
                                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                                                Item
                                            </th>
                                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                                                Quantity
                                            </th>
                                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                                                Price
                                            </th>
                                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                                                Total
                                            </th>
                                        </tr>
                                    </thead>
                                    <tbody class="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                                        <tr
                                            v-for="item in order.items"
                                            :key="item.id"
                                        >
                                            <td class="px-6 py-4 whitespace-nowrap">
                                                <div class="text-sm font-medium text-gray-900 dark:text-gray-100">
                                                    {{ item.menu_item?.name || item.item_name }}
                                                </div>
                                                <div v-if="item.special_instructions" class="text-sm text-gray-500">
                                                    📝 {{ item.special_instructions }}
                                                </div>
                                            </td>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100">
                                                {{ item.quantity }}
                                            </td>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100">
                                                {{ formatPrice(item.unit_price) }}
                                            </td>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900 dark:text-gray-100">
                                                {{ formatPrice(item.total_price) }}
                                            </td>
                                        </tr>
                                    </tbody>
                                    <tfoot class="bg-gray-50 dark:bg-gray-700">
                                        <tr>
                                            <td colspan="3" class="px-6 py-4 text-right text-sm font-medium text-gray-900 dark:text-gray-100">
                                                Subtotal:
                                            </td>
                                            <td class="px-6 py-4 text-sm font-medium text-gray-900 dark:text-gray-100">
                                                {{ formatPrice(order.subtotal) }}
                                            </td>
                                        </tr>
                                        <tr v-if="order.discount > 0">
                                            <td colspan="3" class="px-6 py-4 text-right text-sm font-medium text-gray-900 dark:text-gray-100">
                                                Discount:
                                            </td>
                                            <td class="px-6 py-4 text-sm font-medium text-red-600">
                                                -{{ formatPrice(order.discount) }}
                                            </td>
                                        </tr>
                                        <tr v-if="order.delivery_charge > 0">
                                            <td colspan="3" class="px-6 py-4 text-right text-sm font-medium text-gray-900 dark:text-gray-100">
                                                Delivery Charge:
                                            </td>
                                            <td class="px-6 py-4 text-sm font-medium text-gray-900 dark:text-gray-100">
                                                {{ formatPrice(order.delivery_charge) }}
                                            </td>
                                        </tr>
                                        <tr class="border-t-2 border-gray-300 dark:border-gray-600">
                                            <td colspan="3" class="px-6 py-4 text-right text-lg font-bold text-gray-900 dark:text-gray-100">
                                                Total:
                                            </td>
                                            <td class="px-6 py-4 text-lg font-bold text-gray-900 dark:text-gray-100">
                                                {{ formatPrice(order.total_amount) }}
                                            </td>
                                        </tr>
                                    </tfoot>
                                </table>
                            </div>
                        </div>
                    </div>

                    <!-- Sidebar -->
                    <div class="space-y-6">
                        <!-- Quick Actions -->
                        <div class="bg-white dark:bg-gray-800 shadow-sm rounded-lg">
                            <div class="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
                                <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100">
                                    ⚡ Quick Actions
                                </h3>
                            </div>
                            <div class="p-6 space-y-3">
                                <button
                                    v-if="order.status === 'pending'"
                                    @click="updateStatus('confirmed')"
                                    class="w-full px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500"
                                >
                                    ✅ Confirm Order
                                </button>
                                <button
                                    v-if="order.status === 'confirmed'"
                                    @click="updateStatus('preparing')"
                                    class="w-full px-4 py-2 bg-orange-600 text-white rounded-md hover:bg-orange-700 focus:outline-none focus:ring-2 focus:ring-orange-500"
                                >
                                    👨‍🍳 Start Preparing
                                </button>
                                <button
                                    v-if="order.status === 'preparing'"
                                    @click="updateStatus('ready')"
                                    class="w-full px-4 py-2 bg-purple-600 text-white rounded-md hover:bg-purple-700 focus:outline-none focus:ring-2 focus:ring-purple-500"
                                >
                                    🔔 Mark Ready
                                </button>
                                <button
                                    v-if="order.status === 'ready'"
                                    @click="updateStatus('served')"
                                    class="w-full px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-green-500"
                                >
                                    🍽️ Mark Served
                                </button>
                            </div>
                        </div>

                        <!-- Payment Information -->
                        <div v-if="order.payments && order.payments.length > 0" class="bg-white dark:bg-gray-800 shadow-sm rounded-lg">
                            <div class="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
                                <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100">
                                    💳 Payment Information
                                </h3>
                            </div>
                            <div class="p-6">
                                <div class="space-y-4">
                                    <div
                                        v-for="payment in order.payments"
                                        :key="payment.id"
                                        class="flex justify-between items-center p-4 bg-gray-50 dark:bg-gray-700 rounded-lg"
                                    >
                                        <div>
                                            <div class="text-sm font-medium text-gray-900 dark:text-gray-100">
                                                {{ payment.payment_method?.toUpperCase() || 'CASH' }}
                                            </div>
                                            <div class="text-sm text-gray-500">
                                                {{ formatDate(payment.created_at) }}
                                            </div>
                                            <div v-if="payment.transaction_reference" class="text-xs text-gray-400">
                                                Ref: {{ payment.transaction_reference }}
                                            </div>
                                        </div>
                                        <div class="text-right">
                                            <div class="text-lg font-semibold text-gray-900 dark:text-gray-100">
                                                {{ formatPrice(payment.amount) }}
                                            </div>
                                            <span
                                                :class="[
                                                    'px-2 py-1 text-xs font-medium rounded-full',
                                                    payment.status === 'completed' ? 'bg-green-100 text-green-800' : 'bg-yellow-100 text-yellow-800'
                                                ]"
                                            >
                                                {{ payment.status?.toUpperCase() || 'COMPLETED' }}
                                            </span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Order History -->
                        <div class="bg-white dark:bg-gray-800 shadow-sm rounded-lg">
                            <div class="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
                                <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100">
                                    📜 Order History
                                </h3>
                            </div>
                            <div class="p-6">
                                <div class="flow-root">
                                    <ul class="-mb-8">
                                        <!-- Order Created -->
                                        <li>
                                            <div class="relative pb-8">
                                                <span class="absolute top-4 left-4 -ml-px h-full w-0.5 bg-gray-200" aria-hidden="true"></span>
                                                <div class="relative flex space-x-3">
                                                    <div>
                                                        <span class="h-8 w-8 rounded-full bg-blue-500 flex items-center justify-center ring-8 ring-white">
                                                            <span class="text-white text-sm">📝</span>
                                                        </span>
                                                    </div>
                                                    <div class="min-w-0 flex-1 pt-1.5 flex justify-between space-x-4">
                                                        <div>
                                                            <p class="text-sm text-gray-500">
                                                                Order created
                                                                <span class="font-medium text-gray-900 dark:text-gray-100">
                                                                    #{{ order.order_number }}
                                                                </span>
                                                            </p>
                                                        </div>
                                                        <div class="text-right text-sm whitespace-nowrap text-gray-500">
                                                            {{ formatDate(order.created_at) }}
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </li>

                                        <!-- Order Updates -->
                                        <li v-for="(update, index) in order.updates" :key="update.id">
                                            <div class="relative pb-8" :class="{ 'pb-0': index === order.updates.length - 1 }">
                                                <span
                                                    v-if="index !== order.updates.length - 1"
                                                    class="absolute top-4 left-4 -ml-px h-full w-0.5 bg-gray-200"
                                                    aria-hidden="true"
                                                ></span>
                                                <div class="relative flex space-x-3">
                                                    <div>
                                                        <span
                                                            :class="[
                                                                'h-8 w-8 rounded-full flex items-center justify-center ring-8 ring-white',
                                                                update.status === 'confirmed' ? 'bg-blue-500' :
                                                                update.status === 'preparing' ? 'bg-orange-500' :
                                                                update.status === 'ready' ? 'bg-purple-500' :
                                                                update.status === 'served' ? 'bg-green-500' :
                                                                update.status === 'delivered' ? 'bg-green-600' :
                                                                update.status === 'cancelled' ? 'bg-red-500' :
                                                                'bg-gray-500'
                                                            ]"
                                                        >
                                                            <span class="text-white text-sm">
                                                                {{
                                                                    update.status === 'confirmed' ? '✅' :
                                                                    update.status === 'preparing' ? '👨‍🍳' :
                                                                    update.status === 'ready' ? '🔔' :
                                                                    update.status === 'served' ? '🍽️' :
                                                                    update.status === 'delivered' ? '🚚' :
                                                                    update.status === 'cancelled' ? '❌' :
                                                                    '📝'
                                                                }}
                                                            </span>
                                                        </span>
                                                    </div>
                                                    <div class="min-w-0 flex-1 pt-1.5 flex justify-between space-x-4">
                                                        <div>
                                                            <p class="text-sm text-gray-500">
                                                                Order status changed to
                                                                <span class="font-medium text-gray-900 dark:text-gray-100 capitalize">
                                                                    {{ update.status?.replace('_', ' ') }}
                                                                </span>
                                                            </p>
                                                            <p v-if="update.notes" class="mt-1 text-sm text-gray-600 dark:text-gray-400">
                                                                {{ update.notes }}
                                                            </p>
                                                        </div>
                                                        <div class="text-right text-sm whitespace-nowrap text-gray-500">
                                                            {{ formatDate(update.created_at) }}
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </li>

                                        <!-- Current Status (if no updates) -->
                                        <li v-if="!order.updates || order.updates.length === 0">
                                            <div class="relative">
                                                <div class="relative flex space-x-3">
                                                    <div>
                                                        <span
                                                            :class="[
                                                                'h-8 w-8 rounded-full flex items-center justify-center ring-8 ring-white',
                                                                getStatusBadge(order.status).class.includes('yellow') ? 'bg-yellow-500' :
                                                                getStatusBadge(order.status).class.includes('blue') ? 'bg-blue-500' :
                                                                getStatusBadge(order.status).class.includes('orange') ? 'bg-orange-500' :
                                                                getStatusBadge(order.status).class.includes('purple') ? 'bg-purple-500' :
                                                                getStatusBadge(order.status).class.includes('green') ? 'bg-green-500' :
                                                                getStatusBadge(order.status).class.includes('red') ? 'bg-red-500' :
                                                                'bg-gray-500'
                                                            ]"
                                                        >
                                                            <span class="text-white text-sm">
                                                                {{
                                                                    order.status === 'pending' ? '⏳' :
                                                                    order.status === 'confirmed' ? '✅' :
                                                                    order.status === 'preparing' ? '👨‍🍳' :
                                                                    order.status === 'ready' ? '🔔' :
                                                                    order.status === 'served' ? '🍽️' :
                                                                    order.status === 'delivered' ? '🚚' :
                                                                    order.status === 'cancelled' ? '❌' :
                                                                    '📝'
                                                                }}
                                                            </span>
                                                        </span>
                                                    </div>
                                                    <div class="min-w-0 flex-1 pt-1.5 flex justify-between space-x-4">
                                                        <div>
                                                            <p class="text-sm text-gray-500">
                                                                Current status:
                                                                <span class="font-medium text-gray-900 dark:text-gray-100 capitalize">
                                                                    {{ order.status?.replace('_', ' ') }}
                                                                </span>
                                                            </p>
                                                        </div>
                                                        <div class="text-right text-sm whitespace-nowrap text-gray-500">
                                                            {{ formatDate(order.updated_at) }}
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </ManagerLayout>
</template>
