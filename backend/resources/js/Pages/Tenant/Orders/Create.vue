<script setup>
import { Head, Link, useForm } from '@inertiajs/vue3';
import ManagerLayout from '@/Layouts/ManagerLayout.vue';
import { ref, computed } from 'vue';

const props = defineProps({
    restaurant: Object,
    menuItems: Object,
    tables: Array,
    customers: Array,
});

// Form data
const form = useForm({
    order_type: 'dine_in',
    table_id: '',
    customer_id: '',
    customer_name: '',
    customer_phone: '',
    customer_email: '',
    delivery_address: '',
    special_instructions: '',
    items: [],
});

// Reactive data
const selectedItems = ref([]);
const searchQuery = ref('');

// Computed properties
const filteredMenuItems = computed(() => {
    if (!searchQuery.value) return props.menuItems;
    
    const filtered = {};
    Object.keys(props.menuItems).forEach(category => {
        const items = props.menuItems[category].filter(item =>
            item.name.toLowerCase().includes(searchQuery.value.toLowerCase())
        );
        if (items.length > 0) {
            filtered[category] = items;
        }
    });
    return filtered;
});

const orderTotal = computed(() => {
    return selectedItems.value.reduce((total, item) => {
        return total + (item.price * item.quantity);
    }, 0);
});

const availableTables = computed(() => {
    return props.tables.filter(table => table.is_active && table.is_available);
});

// Methods
const addItem = (menuItem) => {
    const existingItem = selectedItems.value.find(item => item.id === menuItem.id);
    
    if (existingItem) {
        existingItem.quantity++;
    } else {
        selectedItems.value.push({
            id: menuItem.id,
            name: menuItem.name,
            price: menuItem.price,
            quantity: 1,
            special_instructions: '',
        });
    }
    
    updateFormItems();
};

const removeItem = (itemId) => {
    const index = selectedItems.value.findIndex(item => item.id === itemId);
    if (index > -1) {
        selectedItems.value.splice(index, 1);
        updateFormItems();
    }
};

const updateQuantity = (itemId, quantity) => {
    const item = selectedItems.value.find(item => item.id === itemId);
    if (item) {
        item.quantity = Math.max(1, quantity);
        updateFormItems();
    }
};

const updateFormItems = () => {
    form.items = selectedItems.value.map(item => ({
        menu_item_id: item.id,
        quantity: item.quantity,
        special_instructions: item.special_instructions,
    }));
};

const selectCustomer = (customer) => {
    form.customer_id = customer.id;
    form.customer_name = customer.name;
    form.customer_phone = customer.phone;
    form.customer_email = customer.email;
};

const formatPrice = (price) => {
    return new Intl.NumberFormat('en-US', {
        style: 'currency',
        currency: 'USD'
    }).format(price);
};

const submit = () => {
    if (selectedItems.value.length === 0) {
        alert('Please add at least one item to the order.');
        return;
    }
    
    form.post(route('orders.store'), {
        onSuccess: () => {
            // Success handled by redirect
        },
    });
};
</script>

<template>
    <ManagerLayout title="Create Order">
        <template #header>
            <div class="flex justify-between items-center">
                <h2 class="font-semibold text-xl text-gray-800 dark:text-gray-200 leading-tight">
                    ➕ Create New Order
                </h2>
                
                <Link
                    :href="route('orders.index')"
                    class="inline-flex items-center px-4 py-2 bg-gray-600 border border-transparent rounded-md font-semibold text-xs text-white uppercase tracking-widest hover:bg-gray-700 focus:bg-gray-700 active:bg-gray-900 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2 transition ease-in-out duration-150"
                >
                    ← Back to Orders
                </Link>
            </div>
        </template>

        <div class="py-6">
            <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                <form @submit.prevent="submit" class="space-y-6">
                    <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
                        <!-- Order Details -->
                        <div class="lg:col-span-2 space-y-6">
                            <!-- Order Type & Table -->
                            <div class="bg-white dark:bg-gray-800 shadow-sm rounded-lg">
                                <div class="p-6 border-b border-gray-200 dark:border-gray-700">
                                    <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100">
                                        Order Details
                                    </h3>
                                </div>
                                <div class="p-6 space-y-4">
                                    <!-- Order Type -->
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                                            Order Type *
                                        </label>
                                        <div class="grid grid-cols-3 gap-3">
                                            <label class="flex items-center p-3 border rounded-lg cursor-pointer" :class="form.order_type === 'dine_in' ? 'border-indigo-500 bg-indigo-50' : 'border-gray-300'">
                                                <input v-model="form.order_type" type="radio" value="dine_in" class="sr-only">
                                                <div class="text-center w-full">
                                                    <div class="text-2xl mb-1">🍽️</div>
                                                    <div class="text-sm font-medium">Dine In</div>
                                                </div>
                                            </label>
                                            <label class="flex items-center p-3 border rounded-lg cursor-pointer" :class="form.order_type === 'takeaway' ? 'border-indigo-500 bg-indigo-50' : 'border-gray-300'">
                                                <input v-model="form.order_type" type="radio" value="takeaway" class="sr-only">
                                                <div class="text-center w-full">
                                                    <div class="text-2xl mb-1">🥡</div>
                                                    <div class="text-sm font-medium">Takeaway</div>
                                                </div>
                                            </label>
                                            <label class="flex items-center p-3 border rounded-lg cursor-pointer" :class="form.order_type === 'delivery' ? 'border-indigo-500 bg-indigo-50' : 'border-gray-300'">
                                                <input v-model="form.order_type" type="radio" value="delivery" class="sr-only">
                                                <div class="text-center w-full">
                                                    <div class="text-2xl mb-1">🚚</div>
                                                    <div class="text-sm font-medium">Delivery</div>
                                                </div>
                                            </label>
                                        </div>
                                        <div v-if="form.errors.order_type" class="mt-1 text-sm text-red-600">
                                            {{ form.errors.order_type }}
                                        </div>
                                    </div>

                                    <!-- Table Selection (for dine-in) -->
                                    <div v-if="form.order_type === 'dine_in'">
                                        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                                            Table
                                        </label>
                                        <select v-model="form.table_id" class="w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500">
                                            <option value="">Select Table</option>
                                            <option v-for="table in availableTables" :key="table.id" :value="table.id">
                                                {{ table.name || `Table ${table.number}` }} ({{ table.capacity }} seats)
                                            </option>
                                        </select>
                                        <div v-if="form.errors.table_id" class="mt-1 text-sm text-red-600">
                                            {{ form.errors.table_id }}
                                        </div>
                                    </div>

                                    <!-- Delivery Address (for delivery) -->
                                    <div v-if="form.order_type === 'delivery'">
                                        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                                            Delivery Address *
                                        </label>
                                        <textarea
                                            v-model="form.delivery_address"
                                            rows="3"
                                            class="w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
                                            placeholder="Enter full delivery address..."
                                        ></textarea>
                                        <div v-if="form.errors.delivery_address" class="mt-1 text-sm text-red-600">
                                            {{ form.errors.delivery_address }}
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Customer Information -->
                            <div class="bg-white dark:bg-gray-800 shadow-sm rounded-lg">
                                <div class="p-6 border-b border-gray-200 dark:border-gray-700">
                                    <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100">
                                        Customer Information
                                    </h3>
                                </div>
                                <div class="p-6 space-y-4">
                                    <!-- Quick Customer Selection -->
                                    <div v-if="customers.length > 0">
                                        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                                            Select Existing Customer
                                        </label>
                                        <div class="grid grid-cols-1 md:grid-cols-2 gap-2 max-h-32 overflow-y-auto">
                                            <button
                                                v-for="customer in customers"
                                                :key="customer.id"
                                                type="button"
                                                @click="selectCustomer(customer)"
                                                class="text-left p-2 border rounded hover:bg-gray-50 text-sm"
                                                :class="form.customer_id === customer.id ? 'border-indigo-500 bg-indigo-50' : 'border-gray-300'"
                                            >
                                                <div class="font-medium">{{ customer.name }}</div>
                                                <div class="text-gray-500">{{ customer.phone }}</div>
                                            </button>
                                        </div>
                                        <div class="mt-2 text-sm text-gray-500">Or enter new customer details below</div>
                                    </div>

                                    <!-- Customer Details -->
                                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                        <div>
                                            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                                                Customer Name *
                                            </label>
                                            <input
                                                v-model="form.customer_name"
                                                type="text"
                                                required
                                                class="w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
                                            >
                                            <div v-if="form.errors.customer_name" class="mt-1 text-sm text-red-600">
                                                {{ form.errors.customer_name }}
                                            </div>
                                        </div>

                                        <div>
                                            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                                                Phone Number *
                                            </label>
                                            <input
                                                v-model="form.customer_phone"
                                                type="tel"
                                                required
                                                class="w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
                                            >
                                            <div v-if="form.errors.customer_phone" class="mt-1 text-sm text-red-600">
                                                {{ form.errors.customer_phone }}
                                            </div>
                                        </div>

                                        <div class="md:col-span-2">
                                            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                                                Email (Optional)
                                            </label>
                                            <input
                                                v-model="form.customer_email"
                                                type="email"
                                                class="w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
                                            >
                                            <div v-if="form.errors.customer_email" class="mt-1 text-sm text-red-600">
                                                {{ form.errors.customer_email }}
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Menu Items -->
                            <div class="bg-white dark:bg-gray-800 shadow-sm rounded-lg">
                                <div class="p-6 border-b border-gray-200 dark:border-gray-700">
                                    <div class="flex justify-between items-center">
                                        <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100">
                                            Menu Items
                                        </h3>
                                        <input
                                            v-model="searchQuery"
                                            type="text"
                                            placeholder="Search menu items..."
                                            class="w-64 rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
                                        >
                                    </div>
                                </div>
                                <div class="p-6">
                                    <div v-if="Object.keys(filteredMenuItems).length === 0" class="text-center py-8 text-gray-500">
                                        <div class="text-4xl mb-4">🔍</div>
                                        <p>No menu items found</p>
                                    </div>
                                    
                                    <div v-else class="space-y-6">
                                        <div v-for="(items, category) in filteredMenuItems" :key="category">
                                            <h4 class="text-lg font-medium text-gray-900 dark:text-gray-100 mb-3">
                                                {{ category || 'Uncategorized' }}
                                            </h4>
                                            <div class="grid grid-cols-1 md:grid-cols-2 gap-3">
                                                <div
                                                    v-for="item in items"
                                                    :key="item.id"
                                                    class="flex items-center justify-between p-3 border border-gray-200 rounded-lg hover:border-indigo-300"
                                                >
                                                    <div class="flex-1">
                                                        <div class="font-medium text-gray-900 dark:text-gray-100">{{ item.name }}</div>
                                                        <div class="text-sm text-gray-500">{{ formatPrice(item.price) }}</div>
                                                    </div>
                                                    <button
                                                        type="button"
                                                        @click="addItem(item)"
                                                        class="ml-3 px-3 py-1 bg-indigo-600 text-white rounded-md hover:bg-indigo-700 text-sm"
                                                    >
                                                        Add
                                                    </button>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Order Summary -->
                        <div class="lg:col-span-1">
                            <div class="bg-white dark:bg-gray-800 shadow-sm rounded-lg sticky top-6">
                                <div class="p-6 border-b border-gray-200 dark:border-gray-700">
                                    <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100">
                                        Order Summary
                                    </h3>
                                </div>
                                <div class="p-6">
                                    <div v-if="selectedItems.length === 0" class="text-center py-8 text-gray-500">
                                        <div class="text-4xl mb-4">🛒</div>
                                        <p>No items selected</p>
                                    </div>
                                    
                                    <div v-else class="space-y-4">
                                        <div
                                            v-for="item in selectedItems"
                                            :key="item.id"
                                            class="flex items-center justify-between"
                                        >
                                            <div class="flex-1">
                                                <div class="font-medium text-sm">{{ item.name }}</div>
                                                <div class="text-xs text-gray-500">{{ formatPrice(item.price) }} each</div>
                                            </div>
                                            <div class="flex items-center space-x-2">
                                                <input
                                                    :value="item.quantity"
                                                    @input="updateQuantity(item.id, parseInt($event.target.value))"
                                                    type="number"
                                                    min="1"
                                                    class="w-16 text-center rounded border-gray-300 text-sm"
                                                >
                                                <button
                                                    type="button"
                                                    @click="removeItem(item.id)"
                                                    class="text-red-600 hover:text-red-800 text-sm"
                                                >
                                                    ✕
                                                </button>
                                            </div>
                                        </div>
                                        
                                        <div class="border-t pt-4">
                                            <div class="flex justify-between text-lg font-bold">
                                                <span>Total:</span>
                                                <span>{{ formatPrice(orderTotal) }}</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                
                                <!-- Special Instructions -->
                                <div class="p-6 border-t border-gray-200 dark:border-gray-700">
                                    <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                                        Special Instructions
                                    </label>
                                    <textarea
                                        v-model="form.special_instructions"
                                        rows="3"
                                        class="w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
                                        placeholder="Any special requests..."
                                    ></textarea>
                                </div>
                                
                                <!-- Submit Button -->
                                <div class="p-6 border-t border-gray-200 dark:border-gray-700">
                                    <button
                                        type="submit"
                                        :disabled="form.processing || selectedItems.length === 0"
                                        class="w-full inline-flex justify-center items-center px-4 py-3 bg-indigo-600 border border-transparent rounded-md font-semibold text-sm text-white uppercase tracking-widest hover:bg-indigo-700 focus:bg-indigo-700 active:bg-indigo-900 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 transition ease-in-out duration-150 disabled:opacity-50"
                                    >
                                        <span v-if="form.processing" class="mr-2">
                                            <svg class="animate-spin h-4 w-4" fill="none" viewBox="0 0 24 24">
                                                <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                                                <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                                            </svg>
                                        </span>
                                        {{ form.processing ? 'Creating Order...' : 'Create Order' }}
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </ManagerLayout>
</template>
