<template>
    <ManagerLayout>
        <template #header>
            <h2 class="font-semibold text-xl text-gray-800 dark:text-gray-200 leading-tight">
                {{ $t('subscription.plan_comparison') }}
            </h2>
        </template>

        <div class="py-12">
            <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
                <!-- Current Plan Banner -->
                <div v-if="currentSubscription" class="bg-blue-50 dark:bg-blue-900 border border-blue-200 dark:border-blue-700 rounded-lg p-4 mb-8">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <svg class="h-5 w-5 text-blue-400" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
                            </svg>
                        </div>
                        <div class="ml-3">
                            <p class="text-sm text-blue-700 dark:text-blue-200">
                                {{ $t('subscription.current') }}: <strong>{{ currentSubscription.plan.name }}</strong> - 
                                {{ currentSubscription.formatted_amount }}/{{ $t(`subscription.${currentSubscription.billing_cycle}`) }}
                            </p>
                        </div>
                    </div>
                </div>

                <!-- Plans Grid -->
                <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
                    <div v-for="plan in availablePlans" :key="plan.id" 
                         class="relative bg-white dark:bg-gray-800 rounded-lg shadow-lg overflow-hidden"
                         :class="{ 'ring-2 ring-blue-500': isCurrentPlan(plan.id), 'transform scale-105': plan.slug === 'basic-plus' }">
                        
                        <!-- Popular Badge -->
                        <div v-if="plan.slug === 'basic-plus'" 
                             class="absolute top-0 left-1/2 transform -translate-x-1/2 -translate-y-1/2">
                            <span class="bg-blue-500 text-white px-3 py-1 rounded-full text-sm font-medium">
                                {{ $t('subscription.popular') }}
                            </span>
                        </div>

                        <!-- Current Plan Badge -->
                        <div v-if="isCurrentPlan(plan.id)" 
                             class="absolute top-4 right-4">
                            <span class="bg-green-500 text-white px-2 py-1 rounded text-xs font-medium">
                                {{ $t('subscription.current') }}
                            </span>
                        </div>

                        <div class="p-6">
                            <!-- Plan Header -->
                            <div class="text-center mb-6">
                                <h3 class="text-2xl font-bold text-gray-900 dark:text-white">{{ plan.name }}</h3>
                                <p class="text-gray-600 dark:text-gray-400 mt-2">{{ plan.description }}</p>
                                <div class="mt-4">
                                    <span class="text-4xl font-bold text-gray-900 dark:text-white">{{ plan.formatted_price }}</span>
                                </div>
                            </div>

                            <!-- Features List -->
                            <div class="space-y-3 mb-6">
                                <div v-for="feature in plan.features" :key="feature" class="flex items-start">
                                    <svg class="flex-shrink-0 h-5 w-5 text-green-500 mt-0.5" fill="currentColor" viewBox="0 0 20 20">
                                        <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd" />
                                    </svg>
                                    <span class="ml-3 text-sm text-gray-700 dark:text-gray-300">{{ feature }}</span>
                                </div>
                            </div>

                            <!-- Usage Limits -->
                            <div class="border-t border-gray-200 dark:border-gray-700 pt-4 mb-6">
                                <h4 class="text-sm font-medium text-gray-900 dark:text-white mb-3">{{ $t('subscription.usage_limits') }}</h4>
                                <div class="space-y-2 text-sm text-gray-600 dark:text-gray-400">
                                    <div class="flex justify-between">
                                        <span>{{ $t('subscription.menu_items') }}</span>
                                        <span>{{ plan.max_menu_items || $t('subscription.unlimited') }}</span>
                                    </div>
                                    <div class="flex justify-between">
                                        <span>{{ $t('subscription.orders_per_month') }}</span>
                                        <span>{{ plan.max_orders_per_month || $t('subscription.unlimited') }}</span>
                                    </div>
                                    <div class="flex justify-between">
                                        <span>{{ $t('subscription.custom_pages') }}</span>
                                        <span>{{ plan.max_pages || $t('subscription.unlimited') }}</span>
                                    </div>
                                    <div class="flex justify-between">
                                        <span>{{ $t('subscription.branches') }}</span>
                                        <span>{{ plan.max_branches || $t('subscription.unlimited') }}</span>
                                    </div>
                                    <div class="flex justify-between">
                                        <span>{{ $t('subscription.staff_members') }}</span>
                                        <span>{{ plan.max_staff || $t('subscription.unlimited') }}</span>
                                    </div>
                                </div>
                            </div>

                            <!-- Action Button -->
                            <div class="text-center">
                                <button v-if="isCurrentPlan(plan.id)"
                                        disabled
                                        class="w-full bg-gray-300 text-gray-500 font-bold py-3 px-4 rounded cursor-not-allowed">
                                    {{ $t('subscription.current') }}
                                </button>
                                <button v-else-if="canUpgrade(plan)"
                                        @click="requestPlanChange(plan, 'upgrade')"
                                        class="w-full bg-blue-500 hover:bg-blue-700 text-white font-bold py-3 px-4 rounded transition-colors">
                                    {{ $t('subscription.request_upgrade') }}
                                </button>
                                <button v-else-if="canDowngrade(plan)"
                                        @click="requestPlanChange(plan, 'downgrade')"
                                        class="w-full bg-yellow-500 hover:bg-yellow-700 text-white font-bold py-3 px-4 rounded transition-colors">
                                    {{ $t('subscription.request_downgrade') }}
                                </button>
                                <button v-else
                                        @click="requestPlanChange(plan, 'change')"
                                        class="w-full bg-green-500 hover:bg-green-700 text-white font-bold py-3 px-4 rounded transition-colors">
                                    {{ $t('subscription.select_plan') }}
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Feature Comparison Table -->
                <div class="mt-12 bg-white dark:bg-gray-800 rounded-lg shadow-lg overflow-hidden">
                    <div class="p-6">
                        <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-6">{{ $t('subscription.features_included') }}</h3>
                        
                        <div class="overflow-x-auto">
                            <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                                <thead class="bg-gray-50 dark:bg-gray-700">
                                    <tr>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                                            Feature
                                        </th>
                                        <th v-for="plan in availablePlans" :key="`header-${plan.id}`" 
                                            class="px-6 py-3 text-center text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                                            {{ plan.name }}
                                        </th>
                                    </tr>
                                </thead>
                                <tbody class="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                                    <tr v-for="feature in featureComparison" :key="feature.name">
                                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900 dark:text-white">
                                            {{ feature.label }}
                                        </td>
                                        <td v-for="plan in availablePlans" :key="`${feature.name}-${plan.id}`" 
                                            class="px-6 py-4 whitespace-nowrap text-center">
                                            <span v-if="plan[feature.name]" class="text-green-500">
                                                <svg class="h-5 w-5 mx-auto" fill="currentColor" viewBox="0 0 20 20">
                                                    <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd" />
                                                </svg>
                                            </span>
                                            <span v-else class="text-gray-400">
                                                <svg class="h-5 w-5 mx-auto" fill="currentColor" viewBox="0 0 20 20">
                                                    <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd" />
                                                </svg>
                                            </span>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>

                <!-- Back to Dashboard -->
                <div class="mt-8 text-center">
                    <Link :href="route('subscription.index')" 
                          class="inline-flex items-center px-4 py-2 bg-gray-500 hover:bg-gray-700 text-white font-bold rounded transition-colors">
                        <svg class="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M9.707 16.707a1 1 0 01-1.414 0l-6-6a1 1 0 010-1.414l6-6a1 1 0 011.414 1.414L5.414 9H17a1 1 0 110 2H5.414l4.293 4.293a1 1 0 010 1.414z" clip-rule="evenodd" />
                        </svg>
                        Back to Subscription Dashboard
                    </Link>
                </div>
            </div>
        </div>
    </ManagerLayout>
</template>

<script setup>
import { computed } from 'vue'
import { Link, useForm } from '@inertiajs/vue3'
import ManagerLayout from '@/Layouts/ManagerLayout.vue'

const props = defineProps({
    currentSubscription: Object,
    availablePlans: Array,
    tenant: Object,
})

const form = useForm({
    plan_id: null,
    reason: '',
})

const featureComparison = computed(() => [
    { name: 'has_delivery', label: 'Basic Delivery' },
    { name: 'has_home_delivery', label: 'Home Delivery' },
    { name: 'has_email_marketing', label: 'Email Marketing' },
    { name: 'has_loyalty_program', label: 'Loyalty Program' },
    { name: 'has_analytics', label: 'Basic Analytics' },
    { name: 'has_advanced_reporting', label: 'Advanced Reporting' },
    { name: 'has_multi_location', label: 'Multi-Location' },
    { name: 'has_api_access', label: 'API Access' },
])

const isCurrentPlan = (planId) => {
    return props.currentSubscription && props.currentSubscription.subscription_plan_id === planId
}

const canUpgrade = (plan) => {
    if (!props.currentSubscription) return true
    return plan.price > props.currentSubscription.plan.price
}

const canDowngrade = (plan) => {
    if (!props.currentSubscription) return false
    return plan.price < props.currentSubscription.plan.price
}

const requestPlanChange = (plan, type) => {
    if (confirm(`Are you sure you want to ${type} to ${plan.name}?`)) {
        form.plan_id = plan.id
        form.reason = `${type} to ${plan.name}`
        
        form.post(route('subscription.request-change'), {
            onSuccess: () => {
                form.reset()
            },
            onError: (errors) => {
                console.error('Plan change request failed:', errors)
            }
        })
    }
}
</script>
