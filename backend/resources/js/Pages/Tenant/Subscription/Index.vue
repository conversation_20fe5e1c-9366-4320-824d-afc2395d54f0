<template>
    <ManagerLayout>
        <template #header>
            <h2 class="font-semibold text-xl text-gray-800 dark:text-gray-200 leading-tight">
                {{ $t('subscription.title') }}
            </h2>
        </template>

        <div class="py-12">
            <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
                <!-- Current Subscription Card -->
                <div class="bg-white dark:bg-gray-800 overflow-hidden shadow-xl sm:rounded-lg mb-6">
                    <div class="p-6">
                        <div class="flex items-center justify-between mb-4">
                            <h3 class="text-lg font-medium text-gray-900 dark:text-white">
                                {{ $t('subscription.current_plan') }}
                            </h3>
                            <span v-if="currentSubscription" 
                                  :class="getStatusBadgeClass(currentSubscription.status)"
                                  class="px-3 py-1 rounded-full text-sm font-medium">
                                {{ $t(`subscription.status.${currentSubscription.status}`) }}
                            </span>
                        </div>

                        <div v-if="currentSubscription" class="grid grid-cols-1 md:grid-cols-3 gap-6">
                            <!-- Plan Details -->
                            <div class="col-span-2">
                                <h4 class="text-xl font-semibold text-gray-900 dark:text-white mb-2">
                                    {{ currentSubscription.plan.name }}
                                </h4>
                                <p class="text-gray-600 dark:text-gray-400 mb-4">
                                    {{ currentSubscription.plan.description }}
                                </p>
                                <div class="text-2xl font-bold text-blue-600 dark:text-blue-400">
                                    {{ currentSubscription.formatted_amount }}/{{ $t(`subscription.${currentSubscription.billing_cycle}`) }}
                                </div>
                            </div>

                            <!-- Subscription Info -->
                            <div class="space-y-3">
                                <div v-if="currentSubscription.trial_ends_at">
                                    <span class="text-sm text-gray-500 dark:text-gray-400">{{ $t('subscription.trial_ends') }}</span>
                                    <div class="font-medium text-gray-900 dark:text-white">
                                        {{ formatDate(currentSubscription.trial_ends_at) }}
                                    </div>
                                </div>
                                <div v-if="currentSubscription.current_period_end">
                                    <span class="text-sm text-gray-500 dark:text-gray-400">{{ $t('subscription.next_billing') }}</span>
                                    <div class="font-medium text-gray-900 dark:text-white">
                                        {{ formatDate(currentSubscription.current_period_end) }}
                                    </div>
                                </div>
                                <div v-if="currentSubscription.days_remaining">
                                    <span class="text-sm text-gray-500 dark:text-gray-400">{{ $t('subscription.days_remaining') }}</span>
                                    <div class="font-medium text-gray-900 dark:text-white">
                                        {{ currentSubscription.days_remaining }} days
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div v-else class="text-center py-8">
                            <p class="text-gray-500 dark:text-gray-400 mb-4">
                                No active subscription found
                            </p>
                            <Link :href="route('subscription.plans')" 
                                  class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
                                View Plans
                            </Link>
                        </div>
                    </div>
                </div>

                <!-- Usage Statistics -->
                <div v-if="usageStats && Object.keys(usageStats).length > 0" 
                     class="bg-white dark:bg-gray-800 overflow-hidden shadow-xl sm:rounded-lg mb-6">
                    <div class="p-6">
                        <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-4">
                            {{ $t('subscription.usage_stats') }}
                        </h3>
                        
                        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                            <div v-for="(stat, key) in usageStats" :key="key" class="space-y-2">
                                <div class="flex justify-between items-center">
                                    <span class="text-sm font-medium text-gray-700 dark:text-gray-300">
                                        {{ $t(`subscription.${key}`) }}
                                    </span>
                                    <span class="text-sm text-gray-500 dark:text-gray-400">
                                        {{ stat.used }} / {{ stat.is_unlimited ? $t('subscription.unlimited') : stat.limit }}
                                    </span>
                                </div>
                                <div class="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                                    <div class="bg-blue-600 h-2 rounded-full transition-all duration-300"
                                         :style="{ width: stat.is_unlimited ? '0%' : `${Math.min(stat.percentage, 100)}%` }"
                                         :class="{ 'bg-red-500': stat.percentage > 90, 'bg-yellow-500': stat.percentage > 75 }">
                                    </div>
                                </div>
                                <div v-if="stat.percentage > 90" class="text-xs text-red-500">
                                    {{ $t('subscription.upgrade_needed') }}
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Quick Actions -->
                <div class="bg-white dark:bg-gray-800 overflow-hidden shadow-xl sm:rounded-lg">
                    <div class="p-6">
                        <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-4">
                            Quick Actions
                        </h3>
                        
                        <div class="flex flex-wrap gap-4">
                            <Link :href="route('subscription.plans')" 
                                  class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
                                {{ $t('subscription.plan_comparison') }}
                            </Link>
                            <Link :href="route('subscription.billing')" 
                                  class="bg-gray-500 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded">
                                {{ $t('subscription.billing_history') }}
                            </Link>
                            <button class="bg-green-500 hover:bg-green-700 text-white font-bold py-2 px-4 rounded">
                                {{ $t('subscription.contact_support') }}
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </ManagerLayout>
</template>

<script setup>
import { computed } from 'vue'
import { Link } from '@inertiajs/vue3'
import ManagerLayout from '@/Layouts/ManagerLayout.vue'

const props = defineProps({
    currentSubscription: Object,
    availablePlans: Array,
    currentUsage: Object,
    usageStats: Object,
    tenant: Object,
})

const getStatusBadgeClass = (status) => {
    const classes = {
        'active': 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300',
        'trial': 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300',
        'expired': 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300',
        'cancelled': 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-300',
        'past_due': 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300',
    }
    return classes[status] || 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-300'
}

const formatDate = (dateString) => {
    if (!dateString) return ''
    return new Date(dateString).toLocaleDateString()
}
</script>
