<template>
    <ManagerLayout title="Categories">
        <template #header>
            <div class="flex items-center justify-between">
                <h2 class="font-semibold text-xl text-gray-800 dark:text-gray-200 leading-tight">
                    📁 Categories Management
                </h2>
                <Link
                    :href="route('categories.create')"
                    class="inline-flex items-center px-4 py-2 bg-indigo-600 border border-transparent rounded-md font-semibold text-xs text-white uppercase tracking-widest hover:bg-indigo-700 focus:bg-indigo-700 active:bg-indigo-900 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 transition ease-in-out duration-150"
                >
                    <PlusIcon class="h-4 w-4 mr-2" />
                    Add Category
                </Link>
            </div>
        </template>

        <div class="py-12">
            <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
                <!-- Filters -->
                <div class="bg-white dark:bg-gray-800 overflow-hidden shadow-sm sm:rounded-lg mb-6">
                    <div class="p-6">
                        <div class="flex flex-col sm:flex-row gap-4">
                            <!-- Search -->
                            <div class="flex-1">
                                <input
                                    v-model="searchForm.search"
                                    type="text"
                                    placeholder="Search categories..."
                                    class="w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-gray-100 shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
                                    @input="search"
                                />
                            </div>

                            <!-- Status Filter -->
                            <div class="sm:w-48">
                                <select
                                    v-model="searchForm.status"
                                    class="w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-gray-100 shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
                                    @change="search"
                                >
                                    <option value="">All Status</option>
                                    <option value="active">Active</option>
                                    <option value="inactive">Inactive</option>
                                </select>
                            </div>

                            <!-- Bulk Actions -->
                            <div v-if="selectedCategories.length > 0" class="flex gap-2">
                                <button
                                    @click="bulkAction('activate')"
                                    class="bg-green-600 hover:bg-green-700 text-white px-3 py-2 rounded-md text-sm font-medium transition-colors"
                                >
                                    Activate
                                </button>
                                <button
                                    @click="bulkAction('deactivate')"
                                    class="bg-yellow-600 hover:bg-yellow-700 text-white px-3 py-2 rounded-md text-sm font-medium transition-colors"
                                >
                                    Deactivate
                                </button>
                                <button
                                    @click="bulkAction('delete')"
                                    class="bg-red-600 hover:bg-red-700 text-white px-3 py-2 rounded-md text-sm font-medium transition-colors"
                                >
                                    Delete
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Categories Table -->
                <div class="bg-white dark:bg-gray-800 overflow-hidden shadow-sm sm:rounded-lg">
                    <div class="overflow-x-auto">
                        <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                            <thead class="bg-gray-50 dark:bg-gray-700">
                                <tr>
                                    <th class="px-6 py-3 text-left">
                                        <input
                                            type="checkbox"
                                            :checked="allSelected"
                                            @change="toggleAll"
                                            class="rounded border-gray-300 text-indigo-600 shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
                                        />
                                    </th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                                        Category
                                    </th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                                        Parent Category
                                    </th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                                        Menu Items
                                    </th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                                        Status
                                    </th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                                        Sort Order
                                    </th>
                                    <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                                        Actions
                                    </th>
                                </tr>
                            </thead>
                            <tbody class="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                                <tr
                                    v-for="category in categories.data"
                                    :key="category.id"
                                    class="hover:bg-gray-50 dark:hover:bg-gray-700"
                                >
                                    <td class="px-6 py-4">
                                        <input
                                            v-model="selectedCategories"
                                            :value="category.id"
                                            type="checkbox"
                                            class="rounded border-gray-300 text-indigo-600 shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
                                        />
                                    </td>
                                    <td class="px-6 py-4">
                                        <div class="flex items-center">
                                            <div class="flex-shrink-0 h-12 w-12">
                                                <img
                                                    v-if="category.media && category.media.length > 0"
                                                    :src="category.media[0].url"
                                                    :alt="category.name"
                                                    class="h-12 w-12 rounded-lg object-cover"
                                                />
                                                <div
                                                    v-else
                                                    class="h-12 w-12 rounded-lg bg-gray-200 dark:bg-gray-600 flex items-center justify-center"
                                                >
                                                    <FolderIcon class="h-6 w-6 text-gray-400" />
                                                </div>
                                            </div>
                                            <div class="ml-4">
                                                <div class="text-sm font-medium text-gray-900 dark:text-gray-100">
                                                    {{ category.name }}
                                                </div>
                                                <div v-if="category.description" class="text-sm text-gray-500 dark:text-gray-400">
                                                    {{ category.description.substring(0, 50) }}{{ category.description.length > 50 ? '...' : '' }}
                                                </div>
                                            </div>
                                        </div>
                                    </td>
                                    <td class="px-6 py-4 text-sm text-gray-900 dark:text-gray-100">
                                        <div v-if="category.parent">
                                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200">
                                                {{ category.parent.name }}
                                            </span>
                                        </div>
                                        <span v-else class="text-gray-400 dark:text-gray-500">-</span>
                                    </td>
                                    <td class="px-6 py-4 text-sm text-gray-900 dark:text-gray-100">
                                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200">
                                            {{ category.menu_items ? category.menu_items.length : 0 }} items
                                        </span>
                                    </td>
                                    <td class="px-6 py-4">
                                        <button
                                            @click="toggleStatus(category)"
                                            :class="[
                                                'inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium transition-colors',
                                                category.is_active
                                                    ? 'bg-green-100 text-green-800 hover:bg-green-200 dark:bg-green-900 dark:text-green-200'
                                                    : 'bg-red-100 text-red-800 hover:bg-red-200 dark:bg-red-900 dark:text-red-200'
                                            ]"
                                        >
                                            {{ category.is_active ? 'Active' : 'Inactive' }}
                                        </button>
                                    </td>
                                    <td class="px-6 py-4 text-sm text-gray-900 dark:text-gray-100">
                                        {{ category.sort_order }}
                                    </td>
                                    <td class="px-6 py-4 text-right text-sm font-medium">
                                        <div class="flex items-center justify-end space-x-2">
                                            <Link
                                                :href="route('categories.show', category.id)"
                                                class="text-indigo-600 hover:text-indigo-900 dark:text-indigo-400 dark:hover:text-indigo-300"
                                            >
                                                <EyeIcon class="h-4 w-4" />
                                            </Link>
                                            <Link
                                                :href="route('categories.edit', category.id)"
                                                class="text-yellow-600 hover:text-yellow-900 dark:text-yellow-400 dark:hover:text-yellow-300"
                                            >
                                                <PencilIcon class="h-4 w-4" />
                                            </Link>
                                            <button
                                                @click="deleteCategory(category)"
                                                class="text-red-600 hover:text-red-900 dark:text-red-400 dark:hover:text-red-300"
                                            >
                                                <TrashIcon class="h-4 w-4" />
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>

                    <!-- Pagination -->
                    <div v-if="categories.links" class="px-6 py-4 border-t border-gray-200 dark:border-gray-700">
                        <Pagination :links="categories.links" />
                    </div>
                </div>
            </div>
        </div>
    </ManagerLayout>
</template>

<script setup>
import { ref, computed } from 'vue'
import { Link, router } from '@inertiajs/vue3'
import ManagerLayout from '@/Layouts/ManagerLayout.vue'
import Pagination from '@/Components/Pagination.vue'
import { 
    PlusIcon, 
    FolderIcon, 
    EyeIcon, 
    PencilIcon, 
    TrashIcon 
} from '@heroicons/vue/24/outline'
import { debounce } from 'lodash'

const props = defineProps({
    categories: Object,
    filters: Object,
})

const selectedCategories = ref([])
const searchForm = ref({
    search: props.filters.search || '',
    status: props.filters.status || '',
})

const allSelected = computed(() => {
    return props.categories.data.length > 0 && selectedCategories.value.length === props.categories.data.length
})

const search = debounce(() => {
    router.get(route('categories.index'), searchForm.value, {
        preserveState: true,
        replace: true,
    })
}, 300)

const toggleAll = () => {
    if (allSelected.value) {
        selectedCategories.value = []
    } else {
        selectedCategories.value = props.categories.data.map(category => category.id)
    }
}

const toggleStatus = (category) => {
    router.post(route('categories.toggle-status', category.id), {}, {
        preserveScroll: true,
        onSuccess: () => {
            // Status will be updated via page refresh
        }
    })
}

const deleteCategory = (category) => {
    if (confirm(`Are you sure you want to delete "${category.name}"?`)) {
        router.delete(route('categories.destroy', category.id), {
            preserveScroll: true,
        })
    }
}

const bulkAction = (action) => {
    if (selectedCategories.value.length === 0) {
        alert('Please select categories first')
        return
    }

    console.log('Bulk action requested:', action, 'for categories:', selectedCategories.value)

    const message = action === 'delete'
        ? 'Are you sure you want to delete the selected categories?'
        : `Are you sure you want to ${action} the selected categories?`

    if (confirm(message)) {
        console.log('Sending bulk update request...')

        router.post(route('categories.bulk-update'), {
            ids: selectedCategories.value,
            action: action,
        }, {
            preserveScroll: true,
            onStart: () => {
                console.log('Request started')
            },
            onSuccess: (response) => {
                console.log('Request successful:', response)
                selectedCategories.value = []
            },
            onError: (errors) => {
                console.error('Bulk action error:', errors)
                if (errors.error) {
                    alert(errors.error)
                } else {
                    alert('An error occurred during the bulk action')
                }
            },
            onFinish: () => {
                console.log('Request finished')
            }
        })
    }
}
</script>
