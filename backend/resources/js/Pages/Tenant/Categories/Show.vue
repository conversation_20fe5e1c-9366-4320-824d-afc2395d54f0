<template>
    <ManagerLayout :title="category.name">
        <template #header>
            <div class="flex items-center justify-between">
                <div class="flex items-center space-x-4">
                    <h2 class="font-semibold text-xl text-gray-800 dark:text-gray-200 leading-tight">
                        👁️ {{ category.name }}
                    </h2>
                    <span
                        :class="[
                            'inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium',
                            category.is_active
                                ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200'
                                : 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200'
                        ]"
                    >
                        {{ category.is_active ? 'Active' : 'Inactive' }}
                    </span>
                </div>
                <div class="flex space-x-2">
                    <Link
                        :href="route('categories.edit', category.id)"
                        class="bg-yellow-600 hover:bg-yellow-700 text-white px-4 py-2 rounded-md text-sm font-medium transition-colors"
                    >
                        <PencilIcon class="h-4 w-4 inline mr-2" />
                        Edit
                    </Link>
                    <Link
                        :href="route('subcategories.create', { category_id: category.id })"
                        class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md text-sm font-medium transition-colors"
                    >
                        <PlusIcon class="h-4 w-4 inline mr-2" />
                        Add Subcategory
                    </Link>
                    <Link
                        :href="route('categories.index')"
                        class="bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded-md text-sm font-medium transition-colors"
                    >
                        <ArrowLeftIcon class="h-4 w-4 inline mr-2" />
                        Back to Categories
                    </Link>
                </div>
            </div>
        </template>

        <div class="py-12">
            <div class="max-w-7xl mx-auto sm:px-6 lg:px-8 space-y-6">
                <!-- Category Overview -->
                <div class="bg-white dark:bg-gray-800 overflow-hidden shadow-sm sm:rounded-lg">
                    <div class="p-6">
                        <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
                            <!-- Category Images -->
                            <div class="lg:col-span-1">
                                <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100 mb-4">Images</h3>
                                <div v-if="category.media && category.media.length > 0" class="space-y-4">
                                    <div
                                        v-for="media in category.media"
                                        :key="media.id"
                                        class="relative group"
                                    >
                                        <img
                                            :src="media.url"
                                            :alt="media.name"
                                            class="w-full h-48 object-cover rounded-lg"
                                        />
                                        <div class="absolute inset-0 bg-black bg-opacity-50 opacity-0 group-hover:opacity-100 transition-opacity rounded-lg flex items-center justify-center">
                                            <span class="text-white text-sm">{{ media.name }}</span>
                                        </div>
                                    </div>
                                </div>
                                <div v-else class="text-center py-8">
                                    <PhotoIcon class="mx-auto h-12 w-12 text-gray-400" />
                                    <p class="mt-2 text-sm text-gray-500 dark:text-gray-400">No images uploaded</p>
                                </div>
                            </div>

                            <!-- Category Details -->
                            <div class="lg:col-span-2">
                                <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100 mb-4">Details</h3>
                                <dl class="grid grid-cols-1 sm:grid-cols-2 gap-4">
                                    <div>
                                        <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">Name</dt>
                                        <dd class="mt-1 text-sm text-gray-900 dark:text-gray-100">{{ category.name }}</dd>
                                    </div>
                                    <div>
                                        <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">Sort Order</dt>
                                        <dd class="mt-1 text-sm text-gray-900 dark:text-gray-100">{{ category.sort_order }}</dd>
                                    </div>
                                    <div>
                                        <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">Status</dt>
                                        <dd class="mt-1">
                                            <span
                                                :class="[
                                                    'inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium',
                                                    category.is_active
                                                        ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200'
                                                        : 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200'
                                                ]"
                                            >
                                                {{ category.is_active ? 'Active' : 'Inactive' }}
                                            </span>
                                        </dd>
                                    </div>
                                    <div>
                                        <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">Created</dt>
                                        <dd class="mt-1 text-sm text-gray-900 dark:text-gray-100">
                                            {{ new Date(category.created_at).toLocaleDateString() }}
                                        </dd>
                                    </div>
                                    <div v-if="category.description" class="sm:col-span-2">
                                        <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">Description</dt>
                                        <dd class="mt-1 text-sm text-gray-900 dark:text-gray-100">{{ category.description }}</dd>
                                    </div>
                                </dl>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Statistics -->
                <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                    <div class="bg-white dark:bg-gray-800 overflow-hidden shadow-sm sm:rounded-lg p-6">
                        <div class="flex items-center">
                            <div class="flex-shrink-0">
                                <FolderIcon class="h-8 w-8 text-blue-600" />
                            </div>
                            <div class="ml-4">
                                <div class="text-sm font-medium text-gray-500 dark:text-gray-400">Subcategories</div>
                                <div class="text-2xl font-bold text-gray-900 dark:text-gray-100">
                                    {{ category.active_subcategories ? category.active_subcategories.length : 0 }}
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="bg-white dark:bg-gray-800 overflow-hidden shadow-sm sm:rounded-lg p-6">
                        <div class="flex items-center">
                            <div class="flex-shrink-0">
                                <DocumentTextIcon class="h-8 w-8 text-green-600" />
                            </div>
                            <div class="ml-4">
                                <div class="text-sm font-medium text-gray-500 dark:text-gray-400">Menu Items</div>
                                <div class="text-2xl font-bold text-gray-900 dark:text-gray-100">
                                    {{ category.menu_items ? category.menu_items.length : 0 }}
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="bg-white dark:bg-gray-800 overflow-hidden shadow-sm sm:rounded-lg p-6">
                        <div class="flex items-center">
                            <div class="flex-shrink-0">
                                <PhotoIcon class="h-8 w-8 text-purple-600" />
                            </div>
                            <div class="ml-4">
                                <div class="text-sm font-medium text-gray-500 dark:text-gray-400">Images</div>
                                <div class="text-2xl font-bold text-gray-900 dark:text-gray-100">
                                    {{ category.media ? category.media.length : 0 }}
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Subcategories -->
                <div v-if="category.active_subcategories && category.active_subcategories.length > 0" class="bg-white dark:bg-gray-800 overflow-hidden shadow-sm sm:rounded-lg">
                    <div class="p-6">
                        <div class="flex items-center justify-between mb-6">
                            <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100">Subcategories</h3>
                            <Link
                                :href="route('subcategories.create', { category_id: category.id })"
                                class="bg-blue-600 hover:bg-blue-700 text-white px-3 py-1 rounded-md text-sm font-medium transition-colors"
                            >
                                <PlusIcon class="h-4 w-4 inline mr-1" />
                                Add Subcategory
                            </Link>
                        </div>
                        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                            <div
                                v-for="subcategory in category.active_subcategories"
                                :key="subcategory.id"
                                class="border border-gray-200 dark:border-gray-700 rounded-lg p-4 hover:shadow-md transition-shadow"
                            >
                                <div class="flex items-center justify-between mb-2">
                                    <h4 class="font-medium text-gray-900 dark:text-gray-100">{{ subcategory.name }}</h4>
                                    <span class="text-xs text-gray-500 dark:text-gray-400">
                                        {{ subcategory.menu_items ? subcategory.menu_items.length : 0 }} items
                                    </span>
                                </div>
                                <p v-if="subcategory.description" class="text-sm text-gray-600 dark:text-gray-400 mb-3">
                                    {{ subcategory.description }}
                                </p>
                                <div class="flex items-center justify-between">
                                    <span
                                        :class="[
                                            'inline-flex items-center px-2 py-1 rounded-full text-xs font-medium',
                                            subcategory.is_active
                                                ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200'
                                                : 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200'
                                        ]"
                                    >
                                        {{ subcategory.is_active ? 'Active' : 'Inactive' }}
                                    </span>
                                    <Link
                                        :href="route('subcategories.edit', subcategory.id)"
                                        class="text-indigo-600 hover:text-indigo-900 dark:text-indigo-400 dark:hover:text-indigo-300 text-sm"
                                    >
                                        Edit
                                    </Link>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Menu Items -->
                <div v-if="category.menu_items && category.menu_items.length > 0" class="bg-white dark:bg-gray-800 overflow-hidden shadow-sm sm:rounded-lg">
                    <div class="p-6">
                        <div class="flex items-center justify-between mb-6">
                            <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100">Menu Items</h3>
                            <Link
                                :href="route('menu-items.create', { category_id: category.id })"
                                class="bg-green-600 hover:bg-green-700 text-white px-3 py-1 rounded-md text-sm font-medium transition-colors"
                            >
                                <PlusIcon class="h-4 w-4 inline mr-1" />
                                Add Menu Item
                            </Link>
                        </div>
                        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                            <div
                                v-for="item in category.menu_items"
                                :key="item.id"
                                class="border border-gray-200 dark:border-gray-700 rounded-lg overflow-hidden hover:shadow-md transition-shadow"
                            >
                                <div class="h-32 bg-gray-200 dark:bg-gray-600">
                                    <img
                                        v-if="item.media && item.media.length > 0"
                                        :src="item.media[0].url"
                                        :alt="item.name"
                                        class="w-full h-full object-cover"
                                    />
                                    <div v-else class="flex items-center justify-center h-full">
                                        <DocumentTextIcon class="h-8 w-8 text-gray-400" />
                                    </div>
                                </div>
                                <div class="p-4">
                                    <h4 class="font-medium text-gray-900 dark:text-gray-100 mb-1">{{ item.name }}</h4>
                                    <p class="text-sm text-gray-600 dark:text-gray-400 mb-2">
                                        ${{ item.price }}
                                    </p>
                                    <div class="flex items-center justify-between">
                                        <span
                                            :class="[
                                                'inline-flex items-center px-2 py-1 rounded-full text-xs font-medium',
                                                item.is_active
                                                    ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200'
                                                    : 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200'
                                            ]"
                                        >
                                            {{ item.is_active ? 'Active' : 'Inactive' }}
                                        </span>
                                        <Link
                                            :href="route('menu-items.show', item.id)"
                                            class="text-indigo-600 hover:text-indigo-900 dark:text-indigo-400 dark:hover:text-indigo-300 text-sm"
                                        >
                                            View
                                        </Link>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Empty States -->
                <div v-if="(!category.active_subcategories || category.active_subcategories.length === 0) && (!category.menu_items || category.menu_items.length === 0)" class="bg-white dark:bg-gray-800 overflow-hidden shadow-sm sm:rounded-lg">
                    <div class="p-12 text-center">
                        <FolderIcon class="mx-auto h-12 w-12 text-gray-400" />
                        <h3 class="mt-2 text-sm font-medium text-gray-900 dark:text-gray-100">No content yet</h3>
                        <p class="mt-1 text-sm text-gray-500 dark:text-gray-400">
                            Get started by adding subcategories or menu items to this category.
                        </p>
                        <div class="mt-6 flex justify-center space-x-4">
                            <Link
                                :href="route('subcategories.create', { category_id: category.id })"
                                class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md text-sm font-medium transition-colors"
                            >
                                Add Subcategory
                            </Link>
                            <Link
                                :href="route('menu-items.create', { category_id: category.id })"
                                class="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-md text-sm font-medium transition-colors"
                            >
                                Add Menu Item
                            </Link>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </ManagerLayout>
</template>

<script setup>
import { Link } from '@inertiajs/vue3'
import ManagerLayout from '@/Layouts/ManagerLayout.vue'
import { 
    ArrowLeftIcon, 
    PencilIcon, 
    PlusIcon, 
    FolderIcon, 
    DocumentTextIcon, 
    PhotoIcon 
} from '@heroicons/vue/24/outline'

defineProps({
    category: Object,
})
</script>
