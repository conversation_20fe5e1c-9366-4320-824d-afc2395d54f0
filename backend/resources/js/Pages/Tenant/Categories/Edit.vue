<template>
    <ManagerLayout title="Edit Category">
        <template #header>
            <div class="flex items-center justify-between">
                <h2 class="font-semibold text-xl text-gray-800 dark:text-gray-200 leading-tight">
                    ✏️ Edit Category: {{ category.name }}
                </h2>
                <div class="flex space-x-2">
                    <Link
                        :href="route('categories.show', category.id)"
                        class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md text-sm font-medium transition-colors"
                    >
                        <EyeIcon class="h-4 w-4 inline mr-2" />
                        View
                    </Link>
                    <Link
                        :href="route('categories.index')"
                        class="bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded-md text-sm font-medium transition-colors"
                    >
                        <ArrowLeftIcon class="h-4 w-4 inline mr-2" />
                        Back to Categories
                    </Link>
                </div>
            </div>
        </template>

        <div class="py-12">
            <div class="max-w-4xl mx-auto sm:px-6 lg:px-8">
                <div class="bg-white dark:bg-gray-800 overflow-hidden shadow-sm sm:rounded-lg">
                    <form @submit.prevent="submit" class="p-6 space-y-6">
                        <!-- Basic Information -->
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <!-- Name -->
                            <div>
                                <label for="name" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                                    Category Name *
                                </label>
                                <input
                                    id="name"
                                    v-model="form.name"
                                    type="text"
                                    required
                                    class="w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-gray-100 shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
                                    :class="{ 'border-red-500': form.errors.name }"
                                />
                                <div v-if="form.errors.name" class="mt-1 text-sm text-red-600">
                                    {{ form.errors.name }}
                                </div>
                            </div>

                            <!-- Sort Order -->
                            <div>
                                <label for="sort_order" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                                    Sort Order
                                </label>
                                <input
                                    id="sort_order"
                                    v-model.number="form.sort_order"
                                    type="number"
                                    min="0"
                                    class="w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-gray-100 shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
                                    :class="{ 'border-red-500': form.errors.sort_order }"
                                />
                                <div v-if="form.errors.sort_order" class="mt-1 text-sm text-red-600">
                                    {{ form.errors.sort_order }}
                                </div>
                                <p class="mt-1 text-sm text-gray-500 dark:text-gray-400">
                                    Lower numbers appear first in the menu
                                </p>
                            </div>
                        </div>

                        <!-- Description -->
                        <div>
                            <label for="description" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                                Description
                            </label>
                            <textarea
                                id="description"
                                v-model="form.description"
                                rows="3"
                                class="w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-gray-100 shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
                                :class="{ 'border-red-500': form.errors.description }"
                                placeholder="Brief description of this category..."
                            ></textarea>
                            <div v-if="form.errors.description" class="mt-1 text-sm text-red-600">
                                {{ form.errors.description }}
                            </div>
                        </div>

                        <!-- Images Selection -->
                        <div>
                            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                                📁 Category Images (Up to 5)
                            </label>
                            <MultipleImageUpload
                                v-model="selectedImages"
                                :max-images="5"
                                :error="form.errors.media_ids"
                                @update:modelValue="handleMultipleImagesChange"
                            />
                            <div v-if="form.errors.media_ids" class="mt-2 text-sm text-red-600">
                                {{ form.errors.media_ids }}
                            </div>
                            <p class="mt-1 text-sm text-gray-500 dark:text-gray-400">
                                Select up to 5 images for this category. The first image will be used as the primary image.
                            </p>
                        </div>

                        <!-- Status -->
                        <div class="flex items-center">
                            <input
                                id="is_active"
                                v-model="form.is_active"
                                type="checkbox"
                                class="rounded border-gray-300 text-indigo-600 shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
                            />
                            <label for="is_active" class="ml-2 text-sm text-gray-700 dark:text-gray-300">
                                Active (visible to customers)
                            </label>
                        </div>

                        <!-- Form Actions -->
                        <div class="flex items-center justify-end space-x-4 pt-6 border-t border-gray-200 dark:border-gray-700">
                            <Link
                                :href="route('categories.index')"
                                class="bg-gray-300 hover:bg-gray-400 text-gray-800 px-6 py-2 rounded-md text-sm font-medium transition-colors"
                            >
                                Cancel
                            </Link>
                            <button
                                type="submit"
                                :disabled="form.processing"
                                class="bg-indigo-600 hover:bg-indigo-700 disabled:bg-indigo-400 text-white px-6 py-2 rounded-md text-sm font-medium transition-colors"
                            >
                                <span v-if="form.processing">Updating...</span>
                                <span v-else>Update Category</span>
                            </button>
                        </div>
                    </form>
                </div>

                <!-- Category Statistics -->
                <div class="mt-6 grid grid-cols-1 md:grid-cols-3 gap-6">
                    <div class="bg-white dark:bg-gray-800 overflow-hidden shadow-sm sm:rounded-lg p-6">
                        <div class="flex items-center">
                            <div class="flex-shrink-0">
                                <FolderIcon class="h-8 w-8 text-blue-600" />
                            </div>
                            <div class="ml-4">
                                <div class="text-sm font-medium text-gray-500 dark:text-gray-400">Subcategories</div>
                                <div class="text-2xl font-bold text-gray-900 dark:text-gray-100">
                                    {{ category.active_subcategories ? category.active_subcategories.length : 0 }}
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="bg-white dark:bg-gray-800 overflow-hidden shadow-sm sm:rounded-lg p-6">
                        <div class="flex items-center">
                            <div class="flex-shrink-0">
                                <DocumentTextIcon class="h-8 w-8 text-green-600" />
                            </div>
                            <div class="ml-4">
                                <div class="text-sm font-medium text-gray-500 dark:text-gray-400">Menu Items</div>
                                <div class="text-2xl font-bold text-gray-900 dark:text-gray-100">
                                    {{ category.menu_items ? category.menu_items.length : 0 }}
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="bg-white dark:bg-gray-800 overflow-hidden shadow-sm sm:rounded-lg p-6">
                        <div class="flex items-center">
                            <div class="flex-shrink-0">
                                <PhotoIcon class="h-8 w-8 text-purple-600" />
                            </div>
                            <div class="ml-4">
                                <div class="text-sm font-medium text-gray-500 dark:text-gray-400">Images</div>
                                <div class="text-2xl font-bold text-gray-900 dark:text-gray-100">
                                    {{ category.media ? category.media.length : 0 }}
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </ManagerLayout>
</template>

<script setup>
import { useForm } from '@inertiajs/vue3'
import { Link } from '@inertiajs/vue3'
import { ref } from 'vue'
import ManagerLayout from '@/Layouts/ManagerLayout.vue'
import MultipleImageUpload from '@/Components/MenuItems/MultipleImageUpload.vue'
import { 
    ArrowLeftIcon, 
    EyeIcon, 
    FolderIcon, 
    DocumentTextIcon, 
    PhotoIcon 
} from '@heroicons/vue/24/outline'

const props = defineProps({
    category: Object,
    restaurant: Object,
})

// Reactive data
const selectedImages = ref(props.category.media || [])

const form = useForm({
    name: props.category.name,
    description: props.category.description || '',
    sort_order: props.category.sort_order || 0,
    is_active: props.category.is_active,
    media_ids: props.category.media ? props.category.media.map(m => m.id) : [],
})

// Methods
const handleMultipleImagesChange = (images) => {
    selectedImages.value = images
    form.media_ids = images.map(img => img.id)
}

const submit = () => {
    form.put(route('categories.update', props.category.id), {
        onSuccess: () => {
            // Redirect handled by controller
        },
    })
}
</script>
