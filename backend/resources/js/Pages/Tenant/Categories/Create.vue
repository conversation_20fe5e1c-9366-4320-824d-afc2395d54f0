<template>
    <ManagerLayout title="Create Category">
        <template #header>
            <div class="flex items-center justify-between">
                <h2 class="font-semibold text-xl text-gray-800 dark:text-gray-200 leading-tight">
                    ➕ Create Category
                </h2>
                <Link
                    :href="route('categories.index')"
                    class="inline-flex items-center px-4 py-2 bg-gray-600 border border-transparent rounded-md font-semibold text-xs text-white uppercase tracking-widest hover:bg-gray-700 focus:bg-gray-700 active:bg-gray-900 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2 transition ease-in-out duration-150"
                >
                    <ArrowLeftIcon class="h-4 w-4 mr-2" />
                    Back to Categories
                </Link>
            </div>
        </template>

        <div class="py-12">
            <div class="max-w-4xl mx-auto sm:px-6 lg:px-8">
                <div class="bg-white dark:bg-gray-800 overflow-hidden shadow-sm sm:rounded-lg">
                    <form @submit.prevent="submit" class="p-6 space-y-6">
                        <!-- Basic Information -->
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <!-- Name -->
                            <div>
                                <label for="name" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                                    Category Name *
                                </label>
                                <input
                                    id="name"
                                    v-model="form.name"
                                    type="text"
                                    required
                                    class="w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-gray-100 shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
                                    :class="{ 'border-red-500': form.errors.name }"
                                />
                                <div v-if="form.errors.name" class="mt-1 text-sm text-red-600">
                                    {{ form.errors.name }}
                                </div>
                            </div>

                            <!-- Parent Category -->
                            <div>
                                <label for="parent_id" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                                    Parent Category
                                </label>
                                <select
                                    id="parent_id"
                                    v-model="form.parent_id"
                                    class="w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-gray-100 shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
                                    :class="{ 'border-red-500': form.errors.parent_id }"
                                >
                                    <option value="">None (Main Category)</option>
                                    <option
                                        v-for="category in categories"
                                        :key="category.id"
                                        :value="category.id"
                                    >
                                        {{ category.name }}
                                    </option>
                                </select>
                                <div v-if="form.errors.parent_id" class="mt-1 text-sm text-red-600">
                                    {{ form.errors.parent_id }}
                                </div>
                                <p class="mt-1 text-xs text-gray-500 dark:text-gray-400">
                                    Select a parent category to create a subcategory
                                </p>
                            </div>
                        </div>

                        <!-- Description -->
                        <div class="grid grid-cols-1 gap-6">
                            <!-- Description -->
                            <div>
                                <label for="description" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                                    Description
                                </label>
                                <textarea
                                    id="description"
                                    v-model="form.description"
                                    rows="3"
                                    class="w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-gray-100 shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
                                    :class="{ 'border-red-500': form.errors.description }"
                                ></textarea>
                                <div v-if="form.errors.description" class="mt-1 text-sm text-red-600">
                                    {{ form.errors.description }}
                                </div>
                            </div>
                        </div>

                        <!-- Settings -->
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">

                            <!-- Sort Order -->
                            <div>
                                <label for="sort_order" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                                    Sort Order
                                </label>
                                <input
                                    id="sort_order"
                                    v-model.number="form.sort_order"
                                    type="number"
                                    min="0"
                                    class="w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-gray-100 shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
                                    :class="{ 'border-red-500': form.errors.sort_order }"
                                />
                                <div v-if="form.errors.sort_order" class="mt-1 text-sm text-red-600">
                                    {{ form.errors.sort_order }}
                                </div>
                                <p class="mt-1 text-sm text-gray-500 dark:text-gray-400">
                                    Lower numbers appear first in the menu
                                </p>
                            </div>
                        </div>

                        <!-- Images Selection -->
                        <div>
                            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                                📁 Category Images (Up to 5)
                            </label>
                            <MultipleImageUpload
                                v-model="selectedImages"
                                :max-images="5"
                                :error="form.errors.media_ids"
                                @update:modelValue="handleMultipleImagesChange"
                            />
                            <div v-if="form.errors.media_ids" class="mt-2 text-sm text-red-600">
                                {{ form.errors.media_ids }}
                            </div>
                            <p class="mt-1 text-sm text-gray-500 dark:text-gray-400">
                                Select up to 5 images for this category. The first image will be used as the primary image.
                            </p>
                        </div>

                        <!-- Status -->
                        <div class="flex items-center">
                            <input
                                id="is_active"
                                v-model="form.is_active"
                                type="checkbox"
                                class="rounded border-gray-300 text-indigo-600 shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
                            />
                            <label for="is_active" class="ml-2 text-sm text-gray-700 dark:text-gray-300">
                                Active (visible to customers)
                            </label>
                        </div>

                        <!-- Form Actions -->
                        <div class="flex items-center justify-end space-x-4 pt-6 border-t border-gray-200 dark:border-gray-700">
                            <Link
                                :href="route('categories.index')"
                                class="bg-gray-300 hover:bg-gray-400 text-gray-800 px-6 py-2 rounded-md text-sm font-medium transition-colors"
                            >
                                Cancel
                            </Link>
                            <button
                                type="submit"
                                :disabled="form.processing"
                                class="bg-indigo-600 hover:bg-indigo-700 disabled:bg-indigo-400 text-white px-6 py-2 rounded-md text-sm font-medium transition-colors"
                            >
                                <span v-if="form.processing">Creating...</span>
                                <span v-else>Create Category</span>
                            </button>
                        </div>
                    </form>
                </div>

                <!-- Help Section -->
                <div class="mt-6 bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-4">
                    <div class="flex">
                        <InformationCircleIcon class="h-5 w-5 text-blue-400 mt-0.5" />
                        <div class="ml-3">
                            <h3 class="text-sm font-medium text-blue-800 dark:text-blue-200">
                                Category Management Tips
                            </h3>
                            <div class="mt-2 text-sm text-blue-700 dark:text-blue-300">
                                <ul class="list-disc list-inside space-y-1">
                                    <li>Categories help organize your menu items for customers</li>
                                    <li>Use descriptive names like "Appetizers", "Main Courses", "Desserts"</li>
                                    <li>Sort order determines the display sequence in your menu</li>
                                    <li>You can add subcategories after creating the main category</li>
                                    <li>Inactive categories are hidden from customers but preserved in the system</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </ManagerLayout>
</template>

<script setup>
import { useForm } from '@inertiajs/vue3'
import { Link } from '@inertiajs/vue3'
import { ref } from 'vue'
import ManagerLayout from '@/Layouts/ManagerLayout.vue'
import MultipleImageUpload from '@/Components/MenuItems/MultipleImageUpload.vue'
import { ArrowLeftIcon, InformationCircleIcon } from '@heroicons/vue/24/outline'

const props = defineProps({
    restaurant: Object,
    categories: {
        type: Array,
        default: () => []
    },
})

// Reactive data
const selectedImages = ref([])

const form = useForm({
    name: '',
    description: '',
    parent_id: '',
    sort_order: 0,
    is_active: true,
    media_ids: [],
})

// Methods
const handleMultipleImagesChange = (images) => {
    selectedImages.value = images
    form.media_ids = images.map(img => img.id)
}

const submit = () => {
    form.post(route('categories.store'), {
        onSuccess: () => {
            // Redirect handled by controller
        },
    })
}
</script>
