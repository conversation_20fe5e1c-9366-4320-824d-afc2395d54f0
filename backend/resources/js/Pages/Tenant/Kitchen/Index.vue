<script setup>
import { Head, router } from '@inertiajs/vue3';
import { ref, computed, onMounted, onUnmounted } from 'vue';
import { useI18n } from 'vue-i18n';
import KitchenLayout from '@/Layouts/KitchenLayout.vue';
import OrderCard from '@/Components/Kitchen/OrderCard.vue';

// Vue i18n integration
const { t } = useI18n();

const props = defineProps({
    branch: {
        type: Object,
        default: () => ({ name: 'Kitchen' })
    },
    pendingOrders: {
        type: Array,
        default: () => []
    },
    receivedOrders: {
        type: Array,
        default: () => []
    },
    preparingOrders: {
        type: Array,
        default: () => []
    },
    readyOrders: {
        type: Array,
        default: () => []
    },
    kitchenStats: {
        type: Object,
        default: () => ({
            pending_orders: 0,
            preparing_orders: 0,
            ready_orders: 0,
            pending_items: 0,
            average_prep_time: 0,
            completed_today: 0
        })
    },
});

// Reactive state
const isLoading = ref(false);
const refreshInterval = ref(null);
const selectedOrder = ref(null);

// Auto-refresh every 30 seconds
const startAutoRefresh = () => {
    refreshInterval.value = setInterval(() => {
        router.reload({ only: ['pendingOrders', 'receivedOrders', 'preparingOrders', 'readyOrders', 'kitchenStats'] });
    }, 30000);
};

const stopAutoRefresh = () => {
    if (refreshInterval.value) {
        clearInterval(refreshInterval.value);
        refreshInterval.value = null;
    }
};

// Manual refresh
const refreshOrders = () => {
    router.reload({ only: ['pendingOrders', 'receivedOrders', 'preparingOrders', 'readyOrders', 'kitchenStats'] });
};

// Order actions
const updateOrderStatus = async (order, status) => {
    isLoading.value = true;
    try {
        const response = await axios.put(route('kitchen.orders.update-status', order.id), {
            kitchen_status: status
        });

        if (response.data.success) {
            refreshOrders();
        }
    } catch (error) {
        console.error('Failed to update order status:', error);
        alert('Failed to update order status. Please try again.');
    } finally {
        isLoading.value = false;
    }
};

const startPreparing = async (order) => {
    isLoading.value = true;
    try {
        const response = await axios.post(route('kitchen.orders.start-preparing', order.id));

        if (response.data.success) {
            refreshOrders();
        }
    } catch (error) {
        console.error('Failed to start preparation:', error);
        alert('Failed to start preparation. Please try again.');
    } finally {
        isLoading.value = false;
    }
};

const markReady = async (order) => {
    isLoading.value = true;
    try {
        const response = await axios.post(route('kitchen.orders.mark-ready', order.id));

        if (response.data.success) {
            refreshOrders();
        }
    } catch (error) {
        console.error('Failed to mark order as ready:', error);
        alert('Failed to mark order as ready. Please try again.');
    } finally {
        isLoading.value = false;
    }
};

const markServed = async (order) => {
    isLoading.value = true;
    try {
        const response = await axios.post(route('kitchen.orders.mark-served', order.id));

        if (response.data.success) {
            refreshOrders();
        }
    } catch (error) {
        console.error('Failed to mark order as served:', error);
        alert('Failed to mark order as served. Please try again.');
    } finally {
        isLoading.value = false;
    }
};

// Format time
const formatTime = (dateString) => {
    if (!dateString) return '';
    return new Date(dateString).toLocaleTimeString();
};

// Get time elapsed since order creation
const getTimeElapsed = (dateString) => {
    if (!dateString) return '';
    const now = new Date();
    const orderTime = new Date(dateString);
    const diffMinutes = Math.floor((now - orderTime) / (1000 * 60));
    
    if (diffMinutes < 60) {
        return `${diffMinutes}m`;
    } else {
        const hours = Math.floor(diffMinutes / 60);
        const minutes = diffMinutes % 60;
        return `${hours}h ${minutes}m`;
    }
};

// Lifecycle
onMounted(() => {
    startAutoRefresh();
});

onUnmounted(() => {
    stopAutoRefresh();
});
</script>

<template>
    <KitchenLayout :title="`Kitchen - ${branch?.name || 'Kitchen'}`">
        <template #header>
            <div class="flex justify-between items-center">
                <h1 class="text-2xl font-bold text-gray-900 dark:text-white">
                    👨‍🍳 {{ branch?.name || 'Kitchen' }} - Kitchen Display
                </h1>
                
                <div class="flex items-center space-x-4">
                    <!-- Stats -->
                    <div class="flex items-center space-x-4 text-sm">
                        <div class="bg-yellow-100 text-yellow-800 px-3 py-1 rounded-full">
                            {{ t('kitchen.pending_orders') }}: {{ kitchenStats?.pending_orders || 0 }}
                        </div>
                        <div class="bg-blue-100 text-blue-800 px-3 py-1 rounded-full">
                            {{ t('kitchen.preparing_orders') }}: {{ kitchenStats?.preparing_orders || 0 }}
                        </div>
                        <div class="bg-green-100 text-green-800 px-3 py-1 rounded-full">
                            {{ t('kitchen.ready_orders') }}: {{ kitchenStats?.ready_orders || 0 }}
                        </div>
                    </div>
                    
                    <!-- Refresh Button -->
                    <button
                        @click="refreshOrders"
                        :disabled="isLoading"
                        class="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 transition-colors"
                    >
                        <svg class="w-4 h-4 inline mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                        </svg>
                        {{ t('kitchen.refresh_queue') }}
                    </button>
                </div>
            </div>
        </template>

        <!-- Kitchen Stats -->
        <div class="grid grid-cols-2 md:grid-cols-4 gap-4 mb-6">
            <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm p-4">
                <div class="flex items-center">
                    <div class="p-2 bg-yellow-100 rounded-lg">
                        <svg class="w-6 h-6 text-yellow-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-600 dark:text-gray-400">{{ t('kitchen.items_pending') }}</p>
                        <p class="text-2xl font-semibold text-gray-900 dark:text-white">{{ kitchenStats?.pending_items || 0 }}</p>
                    </div>
                </div>
            </div>

            <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm p-4">
                <div class="flex items-center">
                    <div class="p-2 bg-blue-100 rounded-lg">
                        <svg class="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
                        </svg>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-600 dark:text-gray-400">{{ t('kitchen.average_prep_time') }}</p>
                        <p class="text-2xl font-semibold text-gray-900 dark:text-white">
                            {{ kitchenStats?.average_prep_time ? `${kitchenStats.average_prep_time}m` : 'N/A' }}
                        </p>
                    </div>
                </div>
            </div>

            <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm p-4">
                <div class="flex items-center">
                    <div class="p-2 bg-green-100 rounded-lg">
                        <svg class="w-6 h-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-600 dark:text-gray-400">{{ t('kitchen.completed_today') }}</p>
                        <p class="text-2xl font-semibold text-gray-900 dark:text-white">{{ kitchenStats?.completed_today || 0 }}</p>
                    </div>
                </div>
            </div>

            <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm p-4">
                <div class="flex items-center">
                    <div class="p-2 bg-purple-100 rounded-lg">
                        <svg class="w-6 h-6 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"></path>
                        </svg>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-600 dark:text-gray-400">{{ t('kitchen.orders_in_queue') }}</p>
                        <p class="text-2xl font-semibold text-gray-900 dark:text-white">
                            {{ pendingOrders.length + receivedOrders.length + preparingOrders.length }}
                        </p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Kitchen Queue -->
        <div class="grid grid-cols-1 lg:grid-cols-4 gap-6">
            <!-- Pending Orders -->
            <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm">
                <div class="p-4 border-b border-gray-200 dark:border-gray-700">
                    <h2 class="text-lg font-semibold text-gray-900 dark:text-white flex items-center">
                        <span class="w-3 h-3 bg-yellow-500 rounded-full mr-2"></span>
                        {{ t('kitchen.pending_orders') }} ({{ pendingOrders.length }})
                    </h2>
                </div>
                <div class="p-4 space-y-4 max-h-96 overflow-y-auto">
                    <div v-if="pendingOrders.length === 0" class="text-center py-8 text-gray-500 dark:text-gray-400">
                        {{ t('No pending orders') }}
                    </div>
                    <OrderCard
                        v-for="order in pendingOrders"
                        :key="order.id"
                        :order="order"
                        status="pending"
                        @start-preparing="startPreparing"
                        @update-status="updateOrderStatus"
                        :loading="isLoading"
                    />
                </div>
            </div>

            <!-- Received Orders -->
            <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm">
                <div class="p-4 border-b border-gray-200 dark:border-gray-700">
                    <h2 class="text-lg font-semibold text-gray-900 dark:text-white flex items-center">
                        <span class="w-3 h-3 bg-blue-500 rounded-full mr-2"></span>
                        {{ t('kitchen.order_received') }} ({{ receivedOrders.length }})
                    </h2>
                </div>
                <div class="p-4 space-y-4 max-h-96 overflow-y-auto">
                    <div v-if="receivedOrders.length === 0" class="text-center py-8 text-gray-500 dark:text-gray-400">
                        {{ t('No received orders') }}
                    </div>
                    <OrderCard
                        v-for="order in receivedOrders"
                        :key="order.id"
                        :order="order"
                        status="received"
                        @start-preparing="startPreparing"
                        @update-status="updateOrderStatus"
                        :loading="isLoading"
                    />
                </div>
            </div>

            <!-- Preparing Orders -->
            <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm">
                <div class="p-4 border-b border-gray-200 dark:border-gray-700">
                    <h2 class="text-lg font-semibold text-gray-900 dark:text-white flex items-center">
                        <span class="w-3 h-3 bg-orange-500 rounded-full mr-2"></span>
                        {{ t('kitchen.in_preparation') }} ({{ preparingOrders.length }})
                    </h2>
                </div>
                <div class="p-4 space-y-4 max-h-96 overflow-y-auto">
                    <div v-if="preparingOrders.length === 0" class="text-center py-8 text-gray-500 dark:text-gray-400">
                        {{ t('No orders in preparation') }}
                    </div>
                    <OrderCard
                        v-for="order in preparingOrders"
                        :key="order.id"
                        :order="order"
                        status="preparing"
                        @mark-ready="markReady"
                        @update-status="updateOrderStatus"
                        :loading="isLoading"
                    />
                </div>
            </div>

            <!-- Ready Orders -->
            <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm">
                <div class="p-4 border-b border-gray-200 dark:border-gray-700">
                    <h2 class="text-lg font-semibold text-gray-900 dark:text-white flex items-center">
                        <span class="w-3 h-3 bg-green-500 rounded-full mr-2"></span>
                        {{ t('kitchen.ready_to_serve') }} ({{ readyOrders.length }})
                    </h2>
                </div>
                <div class="p-4 space-y-4 max-h-96 overflow-y-auto">
                    <div v-if="readyOrders.length === 0" class="text-center py-8 text-gray-500 dark:text-gray-400">
                        {{ t('No orders ready') }}
                    </div>
                    <OrderCard
                        v-for="order in readyOrders"
                        :key="order.id"
                        :order="order"
                        status="ready"
                        @mark-served="markServed"
                        @update-status="updateOrderStatus"
                        :loading="isLoading"
                    />
                </div>
            </div>
        </div>
    </KitchenLayout>
</template>

<style scoped>
/* Custom scrollbar for order lists */
.overflow-y-auto::-webkit-scrollbar {
    width: 4px;
}

.overflow-y-auto::-webkit-scrollbar-track {
    background: transparent;
}

.overflow-y-auto::-webkit-scrollbar-thumb {
    background: #d1d5db;
    border-radius: 2px;
}

.overflow-y-auto::-webkit-scrollbar-thumb:hover {
    background: #9ca3af;
}
</style>
