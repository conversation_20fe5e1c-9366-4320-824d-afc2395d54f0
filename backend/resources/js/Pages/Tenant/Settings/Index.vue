<template>
    <ManagerLayout>
        <Head title="Site Settings" />

        <div class="py-12">
            <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
                <div class="bg-white dark:bg-gray-800 overflow-hidden shadow-xl sm:rounded-lg">
                    <div class="p-6 lg:p-8">
                        <div class="flex items-center justify-between mb-8">
                            <div>
                                <h1 class="text-2xl font-bold text-gray-900 dark:text-white">Site Settings</h1>
                                <p class="mt-2 text-sm text-gray-600 dark:text-gray-400">
                                    Manage your restaurant's site configuration and appearance.
                                </p>
                            </div>
                        </div>

                        <form @submit.prevent="updateSettings" class="space-y-8">
                            <!-- General Settings -->
                            <div class="bg-gray-50 dark:bg-gray-700 rounded-lg p-6">
                                <h2 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">
                                    <i class="fas fa-cog mr-2"></i>
                                    General Settings
                                </h2>

                                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                                    <div>
                                        <InputLabel for="site_name" value="Site Name" />
                                        <TextInput
                                            id="site_name"
                                            v-model="form.general.site_name"
                                            type="text"
                                            class="mt-1 block w-full"
                                            required
                                        />
                                        <InputError class="mt-2" :message="form.errors['general.site_name']" />
                                    </div>

                                    <div>
                                        <InputLabel for="site_description" value="Site Description" />
                                        <TextInput
                                            id="site_description"
                                            v-model="form.general.site_description"
                                            type="text"
                                            class="mt-1 block w-full"
                                        />
                                        <InputError class="mt-2" :message="form.errors['general.site_description']" />
                                    </div>
                                </div>
                            </div>

                            <!-- Tax & VAT Settings -->
                            <div class="bg-gray-50 dark:bg-gray-700 rounded-lg p-6">
                                <h2 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">
                                    <i class="fas fa-calculator mr-2"></i>
                                    Tax & VAT Settings
                                </h2>

                                <div class="space-y-6">
                                    <!-- Tax Settings -->
                                    <div class="border border-gray-200 dark:border-gray-600 rounded-lg p-4">
                                        <div class="flex items-center justify-between mb-4">
                                            <div>
                                                <h3 class="text-md font-medium text-gray-900 dark:text-white">Tax</h3>
                                                <p class="text-sm text-gray-500 dark:text-gray-400">Configure tax settings for your orders</p>
                                            </div>
                                            <label class="relative inline-flex items-center cursor-pointer">
                                                <input
                                                    type="checkbox"
                                                    v-model="taxVatForm.tax_enabled"
                                                    class="sr-only peer"
                                                >
                                                <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 dark:peer-focus:ring-blue-800 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-blue-600"></div>
                                            </label>
                                        </div>

                                        <div v-if="taxVatForm.tax_enabled" class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                            <div>
                                                <InputLabel for="tax_name" value="Tax Name" />
                                                <TextInput
                                                    id="tax_name"
                                                    v-model="taxVatForm.tax_name"
                                                    type="text"
                                                    class="mt-1 block w-full"
                                                    placeholder="e.g., Sales Tax, GST"
                                                />
                                                <InputError class="mt-2" :message="taxVatForm.errors.tax_name" />
                                            </div>
                                            <div>
                                                <InputLabel for="tax_rate" value="Tax Rate (%)" />
                                                <TextInput
                                                    id="tax_rate"
                                                    v-model="taxVatForm.tax_rate"
                                                    type="number"
                                                    step="0.01"
                                                    min="0"
                                                    max="100"
                                                    class="mt-1 block w-full"
                                                    placeholder="0.00"
                                                />
                                                <InputError class="mt-2" :message="taxVatForm.errors.tax_rate" />
                                            </div>
                                        </div>
                                    </div>

                                    <!-- VAT Settings -->
                                    <div class="border border-gray-200 dark:border-gray-600 rounded-lg p-4">
                                        <div class="flex items-center justify-between mb-4">
                                            <div>
                                                <h3 class="text-md font-medium text-gray-900 dark:text-white">VAT</h3>
                                                <p class="text-sm text-gray-500 dark:text-gray-400">Configure VAT settings for your orders</p>
                                            </div>
                                            <label class="relative inline-flex items-center cursor-pointer">
                                                <input
                                                    type="checkbox"
                                                    v-model="taxVatForm.vat_enabled"
                                                    class="sr-only peer"
                                                >
                                                <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 dark:peer-focus:ring-blue-800 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-blue-600"></div>
                                            </label>
                                        </div>

                                        <div v-if="taxVatForm.vat_enabled" class="grid grid-cols-1 md:grid-cols-3 gap-4">
                                            <div>
                                                <InputLabel for="vat_rate" value="VAT Rate (%)" />
                                                <TextInput
                                                    id="vat_rate"
                                                    v-model="taxVatForm.vat_rate"
                                                    type="number"
                                                    step="0.01"
                                                    min="0"
                                                    max="100"
                                                    class="mt-1 block w-full"
                                                    placeholder="0.00"
                                                />
                                                <InputError class="mt-2" :message="taxVatForm.errors.vat_rate" />
                                            </div>
                                            <div class="md:col-span-2">
                                                <InputLabel for="vat_number" value="VAT Number (Optional)" />
                                                <TextInput
                                                    id="vat_number"
                                                    v-model="taxVatForm.vat_number"
                                                    type="text"
                                                    class="mt-1 block w-full"
                                                    placeholder="e.g., GB123456789"
                                                />
                                                <InputError class="mt-2" :message="taxVatForm.errors.vat_number" />
                                            </div>
                                        </div>
                                    </div>

                                    <!-- Service Charge Settings -->
                                    <div class="border border-gray-200 dark:border-gray-600 rounded-lg p-4">
                                        <div class="flex items-center justify-between mb-4">
                                            <div>
                                                <h3 class="text-md font-medium text-gray-900 dark:text-white">Service Charge</h3>
                                                <p class="text-sm text-gray-500 dark:text-gray-400">Configure service charge for your orders</p>
                                            </div>
                                            <label class="relative inline-flex items-center cursor-pointer">
                                                <input
                                                    type="checkbox"
                                                    v-model="taxVatForm.service_charge_enabled"
                                                    class="sr-only peer"
                                                >
                                                <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 dark:peer-focus:ring-blue-800 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-blue-600"></div>
                                            </label>
                                        </div>

                                        <div v-if="taxVatForm.service_charge_enabled" class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                            <div>
                                                <InputLabel for="service_charge_name" value="Service Charge Name" />
                                                <TextInput
                                                    id="service_charge_name"
                                                    v-model="taxVatForm.service_charge_name"
                                                    type="text"
                                                    class="mt-1 block w-full"
                                                    placeholder="e.g., Service Charge, Tip"
                                                />
                                                <InputError class="mt-2" :message="taxVatForm.errors.service_charge_name" />
                                            </div>
                                            <div>
                                                <InputLabel for="service_charge" value="Service Charge (%)" />
                                                <TextInput
                                                    id="service_charge"
                                                    v-model="taxVatForm.service_charge"
                                                    type="number"
                                                    step="0.01"
                                                    min="0"
                                                    max="100"
                                                    class="mt-1 block w-full"
                                                    placeholder="0.00"
                                                />
                                                <InputError class="mt-2" :message="taxVatForm.errors.service_charge" />
                                            </div>
                                        </div>
                                    </div>

                                    <!-- Save Tax/VAT Settings Button -->
                                    <div class="flex items-center justify-end">
                                        <PrimaryButton
                                            @click="updateTaxVatSettings"
                                            :class="{ 'opacity-25': taxVatForm.processing }"
                                            :disabled="taxVatForm.processing"
                                        >
                                            <i class="fas fa-save mr-2"></i>
                                            Save Tax & VAT Settings
                                        </PrimaryButton>
                                    </div>
                                </div>
                            </div>

                            <!-- Submit Button -->
                            <div class="flex items-center justify-end">
                                <PrimaryButton :class="{ 'opacity-25': form.processing }" :disabled="form.processing">
                                    <i class="fas fa-save mr-2"></i>
                                    Save Settings
                                </PrimaryButton>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </ManagerLayout>
</template>

<script setup>
import { Head } from '@inertiajs/vue3'
import { useForm } from '@inertiajs/vue3'
import ManagerLayout from '@/Layouts/ManagerLayout.vue'
import InputLabel from '@/Components/InputLabel.vue'
import TextInput from '@/Components/TextInput.vue'
import InputError from '@/Components/InputError.vue'
import PrimaryButton from '@/Components/PrimaryButton.vue'

const props = defineProps({
    settings: Object,
    logoMedia: Object,
})

const form = useForm({
    general: {
        site_name: props.settings.general?.site_name || '',
        site_description: props.settings.general?.site_description || '',
        logo_media_id: props.settings.general?.logo_media_id || null,
    },
})

const taxVatForm = useForm({
    tax_enabled: props.settings.tax_vat?.tax_enabled || false,
    tax_rate: props.settings.tax_vat?.tax_rate || 0,
    tax_name: props.settings.tax_vat?.tax_name || 'Tax',
    vat_enabled: props.settings.tax_vat?.vat_enabled || false,
    vat_rate: props.settings.tax_vat?.vat_rate || 0,
    vat_number: props.settings.tax_vat?.vat_number || '',
    service_charge_enabled: props.settings.tax_vat?.service_charge_enabled || false,
    service_charge: props.settings.tax_vat?.service_charge || 0,
    service_charge_name: props.settings.tax_vat?.service_charge_name || 'Service Charge',
})

const updateSettings = () => {
    form.put(route('site-settings.update'), {
        preserveScroll: true,
        onSuccess: () => {
            // Settings updated successfully
        },
    })
}

const updateTaxVatSettings = () => {
    taxVatForm.put(route('settings.update.tax_vat'), {
        preserveScroll: true,
        onSuccess: () => {
            // Tax/VAT settings updated successfully
        },
    })
}
</script>