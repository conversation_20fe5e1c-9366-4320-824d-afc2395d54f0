<template>
    <ManagerLayout>
        <template #header>
            <div class="flex justify-between items-center">
                <div>
                    <h2 class="font-semibold text-xl text-gray-800 dark:text-gray-200 leading-tight">
                        Create Purchase Order
                    </h2>
                    <p class="text-sm text-gray-600 dark:text-gray-400 mt-1">
                        Create a new purchase order for inventory items
                    </p>
                </div>
                <Link
                    :href="route('purchase-orders.index')"
                    class="bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded-md text-sm font-medium transition-colors duration-200"
                >
                    Back to Purchase Orders
                </Link>
            </div>
        </template>

        <div class="py-6">
            <div class="max-w-6xl mx-auto sm:px-6 lg:px-8">
                <div class="bg-white dark:bg-gray-800 overflow-hidden shadow-sm sm:rounded-lg">
                    <form @submit.prevent="submit" class="p-6 space-y-6">
                        <!-- Order Information -->
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <div>
                                <label for="vendor_id" class="block text-sm font-medium text-gray-700 dark:text-gray-300">
                                    Vendor *
                                </label>
                                <select
                                    id="vendor_id"
                                    v-model="form.vendor_id"
                                    required
                                    @change="onVendorChange"
                                    class="mt-1 block w-full border-gray-300 dark:border-gray-700 dark:bg-gray-900 dark:text-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500"
                                >
                                    <option value="">Select Vendor</option>
                                    <option v-for="vendor in vendors" :key="vendor.id" :value="vendor.id">
                                        {{ vendor.name }}
                                    </option>
                                </select>
                                <div v-if="errors.vendor_id" class="mt-1 text-sm text-red-600">{{ errors.vendor_id }}</div>
                            </div>

                            <div>
                                <label for="order_date" class="block text-sm font-medium text-gray-700 dark:text-gray-300">
                                    Order Date *
                                </label>
                                <input
                                    type="date"
                                    id="order_date"
                                    v-model="form.order_date"
                                    required
                                    class="mt-1 block w-full border-gray-300 dark:border-gray-700 dark:bg-gray-900 dark:text-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500"
                                />
                                <div v-if="errors.order_date" class="mt-1 text-sm text-red-600">{{ errors.order_date }}</div>
                            </div>

                            <div>
                                <label for="expected_delivery_date" class="block text-sm font-medium text-gray-700 dark:text-gray-300">
                                    Expected Delivery Date
                                </label>
                                <input
                                    type="date"
                                    id="expected_delivery_date"
                                    v-model="form.expected_delivery_date"
                                    class="mt-1 block w-full border-gray-300 dark:border-gray-700 dark:bg-gray-900 dark:text-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500"
                                />
                                <div v-if="errors.expected_delivery_date" class="mt-1 text-sm text-red-600">{{ errors.expected_delivery_date }}</div>
                            </div>

                            <div>
                                <label for="payment_terms" class="block text-sm font-medium text-gray-700 dark:text-gray-300">
                                    Payment Terms
                                </label>
                                <input
                                    type="text"
                                    id="payment_terms"
                                    v-model="form.payment_terms"
                                    class="mt-1 block w-full border-gray-300 dark:border-gray-700 dark:bg-gray-900 dark:text-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500"
                                    placeholder="e.g., Net 30"
                                />
                                <div v-if="errors.payment_terms" class="mt-1 text-sm text-red-600">{{ errors.payment_terms }}</div>
                            </div>
                        </div>

                        <!-- Items Section -->
                        <div class="border-t border-gray-200 dark:border-gray-700 pt-6">
                            <div class="flex justify-between items-center mb-4">
                                <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100">Order Items</h3>
                                <button
                                    type="button"
                                    @click="addItem"
                                    class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md text-sm font-medium transition-colors duration-200"
                                >
                                    <PlusIcon class="w-4 h-4 inline mr-2" />
                                    Add Item
                                </button>
                            </div>

                            <div v-if="form.items.length === 0" class="text-center py-8 border-2 border-dashed border-gray-300 dark:border-gray-600 rounded-lg">
                                <CubeIcon class="mx-auto h-12 w-12 text-gray-400" />
                                <p class="mt-2 text-sm text-gray-500 dark:text-gray-400">No items added yet</p>
                                <p class="text-sm text-gray-500 dark:text-gray-400">Click "Add Item" to start building your purchase order</p>
                            </div>

                            <div v-else class="space-y-4">
                                <div
                                    v-for="(item, index) in form.items"
                                    :key="index"
                                    class="bg-gray-50 dark:bg-gray-700 rounded-lg p-4"
                                >
                                    <div class="grid grid-cols-1 md:grid-cols-6 gap-4 items-end">
                                        <div class="md:col-span-2">
                                            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                                                Inventory Item *
                                            </label>
                                            <select
                                                v-model="item.inventory_item_id"
                                                required
                                                @change="onItemChange(index)"
                                                class="w-full border-gray-300 dark:border-gray-700 dark:bg-gray-900 dark:text-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500"
                                            >
                                                <option value="">Select Item</option>
                                                <option v-for="inventoryItem in inventoryItems" :key="inventoryItem.id" :value="inventoryItem.id">
                                                    {{ inventoryItem.name }} ({{ inventoryItem.sku }})
                                                </option>
                                            </select>
                                        </div>

                                        <div>
                                            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                                                Quantity *
                                            </label>
                                            <input
                                                type="number"
                                                v-model.number="item.quantity"
                                                required
                                                min="0.01"
                                                step="0.01"
                                                @input="calculateItemTotal(index)"
                                                class="w-full border-gray-300 dark:border-gray-700 dark:bg-gray-900 dark:text-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500"
                                                placeholder="0.00"
                                            />
                                        </div>

                                        <div>
                                            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                                                Unit Cost *
                                            </label>
                                            <input
                                                type="number"
                                                v-model.number="item.unit_cost"
                                                required
                                                min="0"
                                                step="0.01"
                                                @input="calculateItemTotal(index)"
                                                class="w-full border-gray-300 dark:border-gray-700 dark:bg-gray-900 dark:text-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500"
                                                placeholder="0.00"
                                            />
                                        </div>

                                        <div>
                                            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                                                Total
                                            </label>
                                            <input
                                                type="text"
                                                :value="formatCurrency(item.total_cost || 0)"
                                                readonly
                                                class="w-full border-gray-300 dark:border-gray-700 dark:bg-gray-800 dark:text-gray-300 rounded-md shadow-sm bg-gray-100"
                                            />
                                        </div>

                                        <div>
                                            <button
                                                type="button"
                                                @click="removeItem(index)"
                                                class="w-full bg-red-600 hover:bg-red-700 text-white px-3 py-2 rounded-md text-sm font-medium transition-colors duration-200"
                                            >
                                                <TrashIcon class="w-4 h-4 inline mr-1" />
                                                Remove
                                            </button>
                                        </div>
                                    </div>

                                    <div class="mt-4">
                                        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                                            Notes
                                        </label>
                                        <input
                                            type="text"
                                            v-model="item.notes"
                                            class="w-full border-gray-300 dark:border-gray-700 dark:bg-gray-900 dark:text-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500"
                                            placeholder="Optional notes for this item"
                                        />
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Order Summary -->
                        <div v-if="form.items.length > 0" class="border-t border-gray-200 dark:border-gray-700 pt-6">
                            <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100 mb-4">Order Summary</h3>
                            <div class="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
                                <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                                            Subtotal
                                        </label>
                                        <input
                                            type="text"
                                            :value="formatCurrency(subtotal)"
                                            readonly
                                            class="w-full border-gray-300 dark:border-gray-700 dark:bg-gray-800 dark:text-gray-300 rounded-md shadow-sm bg-gray-100"
                                        />
                                    </div>

                                    <div>
                                        <label for="tax_amount" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                                            Tax Amount
                                        </label>
                                        <input
                                            type="number"
                                            id="tax_amount"
                                            v-model.number="form.tax_amount"
                                            min="0"
                                            step="0.01"
                                            @input="calculateTotal"
                                            class="w-full border-gray-300 dark:border-gray-700 dark:bg-gray-900 dark:text-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500"
                                            placeholder="0.00"
                                        />
                                    </div>

                                    <div>
                                        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                                            Total Amount
                                        </label>
                                        <input
                                            type="text"
                                            :value="formatCurrency(totalAmount)"
                                            readonly
                                            class="w-full border-gray-300 dark:border-gray-700 dark:bg-gray-800 dark:text-gray-300 rounded-md shadow-sm bg-gray-100 font-bold"
                                        />
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Additional Information -->
                        <div class="border-t border-gray-200 dark:border-gray-700 pt-6">
                            <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100 mb-4">Additional Information</h3>
                            <div class="space-y-4">
                                <div>
                                    <label for="delivery_address" class="block text-sm font-medium text-gray-700 dark:text-gray-300">
                                        Delivery Address
                                    </label>
                                    <textarea
                                        id="delivery_address"
                                        v-model="form.delivery_address"
                                        rows="3"
                                        class="mt-1 block w-full border-gray-300 dark:border-gray-700 dark:bg-gray-900 dark:text-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500"
                                        placeholder="Enter delivery address"
                                    ></textarea>
                                    <div v-if="errors.delivery_address" class="mt-1 text-sm text-red-600">{{ errors.delivery_address }}</div>
                                </div>

                                <div>
                                    <label for="notes" class="block text-sm font-medium text-gray-700 dark:text-gray-300">
                                        Order Notes
                                    </label>
                                    <textarea
                                        id="notes"
                                        v-model="form.notes"
                                        rows="3"
                                        class="mt-1 block w-full border-gray-300 dark:border-gray-700 dark:bg-gray-900 dark:text-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500"
                                        placeholder="Additional notes for this purchase order"
                                    ></textarea>
                                    <div v-if="errors.notes" class="mt-1 text-sm text-red-600">{{ errors.notes }}</div>
                                </div>
                            </div>
                        </div>

                        <!-- Submit Buttons -->
                        <div class="border-t border-gray-200 dark:border-gray-700 pt-6">
                            <div class="flex justify-end space-x-3">
                                <Link
                                    :href="route('purchase-orders.index')"
                                    class="bg-gray-300 hover:bg-gray-400 text-gray-800 px-4 py-2 rounded-md text-sm font-medium transition-colors duration-200"
                                >
                                    Cancel
                                </Link>
                                <button
                                    type="submit"
                                    :disabled="processing || form.items.length === 0"
                                    class="bg-blue-600 hover:bg-blue-700 disabled:bg-blue-400 text-white px-4 py-2 rounded-md text-sm font-medium transition-colors duration-200"
                                >
                                    {{ processing ? 'Creating...' : 'Create Purchase Order' }}
                                </button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </ManagerLayout>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from 'vue'
import { Link, router } from '@inertiajs/vue3'
import ManagerLayout from '@/Layouts/ManagerLayout.vue'
import { PlusIcon, TrashIcon, CubeIcon } from '@heroicons/vue/24/outline'

const props = defineProps({
    vendors: Array,
    inventoryItems: Array,
    selectedVendor: Object,
    errors: Object,
})

const processing = ref(false)

const form = reactive({
    vendor_id: props.selectedVendor?.id || '',
    order_date: new Date().toISOString().split('T')[0],
    expected_delivery_date: '',
    payment_terms: '',
    delivery_address: '',
    notes: '',
    tax_amount: 0,
    shipping_cost: 0,
    discount_amount: 0,
    items: [],
})

const subtotal = computed(() => {
    return form.items.reduce((sum, item) => sum + (item.total_cost || 0), 0)
})

const totalAmount = computed(() => {
    return subtotal.value + (form.tax_amount || 0) + (form.shipping_cost || 0) - (form.discount_amount || 0)
})

const addItem = () => {
    form.items.push({
        inventory_item_id: '',
        quantity: 1,
        unit_cost: 0,
        total_cost: 0,
        notes: '',
    })
}

const removeItem = (index) => {
    form.items.splice(index, 1)
}

const onVendorChange = () => {
    // You could filter inventory items by vendor here if needed
}

const onItemChange = (index) => {
    const item = form.items[index]
    const inventoryItem = props.inventoryItems.find(i => i.id == item.inventory_item_id)
    
    if (inventoryItem) {
        item.unit_cost = inventoryItem.unit_cost || 0
        calculateItemTotal(index)
    }
}

const calculateItemTotal = (index) => {
    const item = form.items[index]
    item.total_cost = (item.quantity || 0) * (item.unit_cost || 0)
}

const calculateTotal = () => {
    // This is reactive, so it will update automatically
}

const formatCurrency = (amount) => {
    return new Intl.NumberFormat('en-US', {
        style: 'currency',
        currency: 'USD'
    }).format(amount || 0)
}

const submit = () => {
    processing.value = true
    
    router.post(route('purchase-orders.store'), form, {
        onFinish: () => {
            processing.value = false
        },
        onSuccess: () => {
            // Success handled by redirect
        },
        onError: (errors) => {
            console.error('Validation errors:', errors)
        }
    })
}

onMounted(() => {
    // Add one item by default
    addItem()
})
</script>
