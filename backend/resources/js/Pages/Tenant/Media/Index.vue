<template>
    <AppLayout title="Media Library">
        <template #header>
            <div class="flex justify-between items-center">
                <h2 class="font-semibold text-xl text-gray-800 leading-tight">
                    Media Library
                </h2>
                <div class="flex space-x-3">
                    <button @click="showUploadModal = true" class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md">
                        Upload Files
                    </button>
                    <button @click="showCreateFolderModal = true" class="bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded-md">
                        Create Folder
                    </button>
                </div>
            </div>
        </template>

        <div class="py-12">
            <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
                <!-- Filters -->
                <div class="bg-white overflow-hidden shadow-xl sm:rounded-lg mb-6">
                    <div class="p-6">
                        <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">Search</label>
                                <input 
                                    v-model="filters.search" 
                                    @input="applyFilters"
                                    type="text" 
                                    placeholder="Search media..."
                                    class="w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500"
                                >
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">Type</label>
                                <select 
                                    v-model="filters.type" 
                                    @change="applyFilters"
                                    class="w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500"
                                >
                                    <option value="">All Types</option>
                                    <option value="images">Images</option>
                                    <option value="videos">Videos</option>
                                    <option value="documents">Documents</option>
                                </select>
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">Folder</label>
                                <select 
                                    v-model="filters.folder" 
                                    @change="applyFilters"
                                    class="w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500"
                                >
                                    <option value="">All Folders</option>
                                    <option v-for="folder in folders" :key="folder" :value="folder">
                                        {{ folder }}
                                    </option>
                                </select>
                            </div>
                            <div class="flex items-end">
                                <button @click="clearFilters" class="w-full bg-gray-500 hover:bg-gray-600 text-white px-4 py-2 rounded-md">
                                    Clear Filters
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Bulk Actions -->
                <div v-if="selectedMedia.length > 0" class="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-6">
                    <div class="flex justify-between items-center">
                        <span class="text-blue-800">{{ selectedMedia.length }} item(s) selected</span>
                        <div class="space-x-2">
                            <button @click="bulkDelete" class="bg-red-600 hover:bg-red-700 text-white px-3 py-1 rounded text-sm">
                                Delete Selected
                            </button>
                            <button @click="selectedMedia = []" class="bg-gray-500 hover:bg-gray-600 text-white px-3 py-1 rounded text-sm">
                                Clear Selection
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Media Grid -->
                <div class="bg-white overflow-hidden shadow-xl sm:rounded-lg">
                    <div class="p-6">
                        <div v-if="media.data.length === 0" class="text-center py-12">
                            <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
                            </svg>
                            <h3 class="mt-2 text-sm font-medium text-gray-900">No media files</h3>
                            <p class="mt-1 text-sm text-gray-500">Get started by uploading your first file.</p>
                            <div class="mt-6">
                                <button @click="showUploadModal = true" class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md">
                                    Upload Files
                                </button>
                            </div>
                        </div>

                        <div v-else class="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-6 gap-4">
                            <div 
                                v-for="item in media.data" 
                                :key="item.id"
                                class="relative group cursor-pointer border-2 border-transparent hover:border-blue-300 rounded-lg overflow-hidden"
                                @click="selectMedia(item)"
                            >
                                <!-- Selection Checkbox -->
                                <div class="absolute top-2 left-2 z-10">
                                    <input 
                                        type="checkbox" 
                                        :checked="selectedMedia.includes(item.id)"
                                        @change="toggleSelection(item.id)"
                                        @click.stop
                                        class="rounded border-gray-300 text-blue-600 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50"
                                    >
                                </div>

                                <!-- Media Preview -->
                                <div class="aspect-square bg-gray-100 flex items-center justify-center">
                                    <img 
                                        v-if="item.mime_type.startsWith('image/')" 
                                        :src="item.thumbnail || item.url" 
                                        :alt="item.alt_text || item.name"
                                        class="w-full h-full object-cover"
                                    >
                                    <div v-else class="text-center p-4">
                                        <svg class="mx-auto h-8 w-8 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                                        </svg>
                                        <p class="text-xs text-gray-500 mt-1">{{ item.extension.toUpperCase() }}</p>
                                    </div>
                                </div>

                                <!-- Media Info -->
                                <div class="p-2 bg-white">
                                    <p class="text-xs font-medium text-gray-900 truncate" :title="item.name">
                                        {{ item.name }}
                                    </p>
                                    <p class="text-xs text-gray-500">
                                        {{ item.human_readable_size }}
                                    </p>
                                </div>

                                <!-- Hover Actions -->
                                <div class="absolute inset-0 bg-black bg-opacity-50 opacity-0 group-hover:opacity-100 transition-opacity flex items-center justify-center space-x-2">
                                    <button @click.stop="viewMedia(item)" class="bg-white text-gray-900 p-2 rounded-full hover:bg-gray-100">
                                        <svg class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                                        </svg>
                                    </button>
                                    <button @click.stop="editMedia(item)" class="bg-white text-gray-900 p-2 rounded-full hover:bg-gray-100">
                                        <svg class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                                        </svg>
                                    </button>
                                    <button @click.stop="deleteMedia(item)" class="bg-red-600 text-white p-2 rounded-full hover:bg-red-700">
                                        <svg class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                                        </svg>
                                    </button>
                                </div>
                            </div>
                        </div>

                        <!-- Pagination -->
                        <div v-if="media.last_page > 1" class="mt-6">
                            <Pagination :links="media.links" />
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Upload Modal -->
        <MediaUploadModal 
            :show="showUploadModal" 
            @close="showUploadModal = false"
            @uploaded="handleUploaded"
        />

        <!-- Create Folder Modal -->
        <CreateFolderModal 
            :show="showCreateFolderModal" 
            @close="showCreateFolderModal = false"
            @created="handleFolderCreated"
        />

        <!-- Media Detail Modal -->
        <MediaDetailModal 
            :show="showDetailModal" 
            :media="selectedMediaItem"
            @close="showDetailModal = false"
            @updated="handleMediaUpdated"
            @deleted="handleMediaDeleted"
        />
    </AppLayout>
</template>

<script setup>
import { ref, reactive } from 'vue'
import { router } from '@inertiajs/vue3'
import AppLayout from '@/Layouts/AppLayout.vue'
import Pagination from '@/Components/Pagination.vue'
import MediaUploadModal from '@/Components/Media/MediaUploadModal.vue'
import CreateFolderModal from '@/Components/Media/CreateFolderModal.vue'
import MediaDetailModal from '@/Components/Media/MediaDetailModal.vue'

const props = defineProps({
    media: Object,
    folders: Array,
    filters: Object
})

const showUploadModal = ref(false)
const showCreateFolderModal = ref(false)
const showDetailModal = ref(false)
const selectedMedia = ref([])
const selectedMediaItem = ref(null)

const filters = reactive({
    search: props.filters.search || '',
    type: props.filters.type || '',
    folder: props.filters.folder || '',
    collection: props.filters.collection || ''
})

const applyFilters = () => {
    router.get(route('media.index'), filters, {
        preserveState: true,
        preserveScroll: true
    })
}

const clearFilters = () => {
    Object.keys(filters).forEach(key => {
        filters[key] = ''
    })
    applyFilters()
}

const toggleSelection = (id) => {
    const index = selectedMedia.value.indexOf(id)
    if (index > -1) {
        selectedMedia.value.splice(index, 1)
    } else {
        selectedMedia.value.push(id)
    }
}

const selectMedia = (item) => {
    selectedMediaItem.value = item
    showDetailModal.value = true
}

const viewMedia = (item) => {
    selectedMediaItem.value = item
    showDetailModal.value = true
}

const editMedia = (item) => {
    selectedMediaItem.value = item
    showDetailModal.value = true
}

const deleteMedia = (item) => {
    if (confirm('Are you sure you want to delete this media file?')) {
        router.delete(route('media.destroy', item.id))
    }
}

const bulkDelete = () => {
    if (confirm(`Are you sure you want to delete ${selectedMedia.value.length} media file(s)?`)) {
        router.post(route('media.bulk-delete'), {
            ids: selectedMedia.value
        }, {
            onSuccess: () => {
                selectedMedia.value = []
            }
        })
    }
}

const handleUploaded = () => {
    showUploadModal.value = false
    router.reload()
}

const handleFolderCreated = () => {
    showCreateFolderModal.value = false
    router.reload()
}

const handleMediaUpdated = () => {
    showDetailModal.value = false
    router.reload()
}

const handleMediaDeleted = () => {
    showDetailModal.value = false
    router.reload()
}
</script>
