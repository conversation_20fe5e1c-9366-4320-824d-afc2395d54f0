<template>
    <ManagerLayout title="Leave Types">
        <template #header>
            <div class="flex justify-between items-center">
                <h2 class="font-semibold text-xl text-gray-800 dark:text-gray-200 leading-tight">
                    Leave Types Management
                </h2>
                <Link :href="route('hrm.leave-types.create')" class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
                    Add Leave Type
                </Link>
            </div>
        </template>

        <div class="py-12">
            <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
                <!-- Leave Types Grid -->
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
                    <div v-for="leaveType in leaveTypes.data" :key="leaveType.id" class="bg-white overflow-hidden shadow-xl sm:rounded-lg">
                        <div class="p-6">
                            <div class="flex items-center justify-between mb-4">
                                <div class="flex items-center">
                                    <div class="w-4 h-4 rounded-full mr-3" :style="{ backgroundColor: leaveType.color_code }"></div>
                                    <h3 class="text-lg font-medium text-gray-900">{{ leaveType.name }}</h3>
                                </div>
                                <div class="flex items-center space-x-2">
                                    <button @click="toggleStatus(leaveType.id)" :class="leaveType.is_active ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'" class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium">
                                        {{ leaveType.is_active ? 'Active' : 'Inactive' }}
                                    </button>
                                </div>
                            </div>
                            
                            <div class="space-y-2 mb-4">
                                <div class="flex justify-between text-sm">
                                    <span class="text-gray-500">Default Days/Year:</span>
                                    <span class="font-medium">{{ leaveType.default_days_per_year }}</span>
                                </div>
                                <div class="flex justify-between text-sm">
                                    <span class="text-gray-500">Max Consecutive:</span>
                                    <span class="font-medium">{{ leaveType.max_consecutive_days || 'No limit' }}</span>
                                </div>
                                <div class="flex justify-between text-sm">
                                    <span class="text-gray-500">Advance Notice:</span>
                                    <span class="font-medium">{{ leaveType.advance_notice_days }} days</span>
                                </div>
                                <div class="flex justify-between text-sm">
                                    <span class="text-gray-500">Carry Forward:</span>
                                    <span class="font-medium">{{ leaveType.can_carry_forward ? 'Yes' : 'No' }}</span>
                                </div>
                                <div class="flex justify-between text-sm">
                                    <span class="text-gray-500">Half Day:</span>
                                    <span class="font-medium">{{ leaveType.allow_half_day ? 'Allowed' : 'Not Allowed' }}</span>
                                </div>
                            </div>

                            <div v-if="leaveType.description" class="text-sm text-gray-600 mb-4">
                                {{ leaveType.description }}
                            </div>

                            <div class="flex justify-between items-center">
                                <div class="text-xs text-gray-500">
                                    Created {{ formatDate(leaveType.created_at) }}
                                </div>
                                <div class="flex space-x-2">
                                    <Link :href="route('hrm.leave-types.show', leaveType.id)" class="text-blue-600 hover:text-blue-900 text-sm">View</Link>
                                    <Link :href="route('hrm.leave-types.edit', leaveType.id)" class="text-green-600 hover:text-green-900 text-sm">Edit</Link>
                                    <button @click="deleteLeaveType(leaveType.id)" class="text-red-600 hover:text-red-900 text-sm">Delete</button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Usage Statistics -->
                <div class="bg-white overflow-hidden shadow-xl sm:rounded-lg mb-8">
                    <div class="p-6">
                        <h3 class="text-lg font-medium text-gray-900 mb-4">Leave Type Usage Statistics</h3>
                        <div class="overflow-x-auto">
                            <table class="min-w-full divide-y divide-gray-200">
                                <thead class="bg-gray-50">
                                    <tr>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Leave Type</th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Total Requests</th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Approved</th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Pending</th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Total Days Used</th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                                    </tr>
                                </thead>
                                <tbody class="bg-white divide-y divide-gray-200">
                                    <tr v-for="stat in usageStats" :key="stat.leave_type_id" class="hover:bg-gray-50">
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <div class="flex items-center">
                                                <div class="w-3 h-3 rounded-full mr-2" :style="{ backgroundColor: stat.color_code }"></div>
                                                <span class="text-sm font-medium text-gray-900">{{ stat.name }}</span>
                                            </div>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                            {{ stat.total_requests }}
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                            {{ stat.approved_requests }}
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                            {{ stat.pending_requests }}
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                            {{ stat.total_days_used }}
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                            <Link :href="route('hrm.leave-types.usage', stat.leave_type_id)" class="text-blue-600 hover:text-blue-900">View Details</Link>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>

                <!-- Bulk Actions -->
                <div class="bg-white overflow-hidden shadow-xl sm:rounded-lg">
                    <div class="p-6">
                        <h3 class="text-lg font-medium text-gray-900 mb-4">Bulk Actions</h3>
                        <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                            <button @click="showEntitlementModal = true" class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-3 px-4 rounded">
                                Create Leave Entitlements
                            </button>
                            <button @click="exportLeaveTypes" class="bg-green-600 hover:bg-green-700 text-white font-bold py-3 px-4 rounded">
                                Export Leave Types
                            </button>
                            <button @click="generateReport" class="bg-purple-600 hover:bg-purple-700 text-white font-bold py-3 px-4 rounded">
                                Generate Usage Report
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Create Entitlements Modal -->
        <div v-if="showEntitlementModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
            <div class="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
                <div class="mt-3">
                    <h3 class="text-lg font-medium text-gray-900 mb-4">Create Leave Entitlements</h3>
                    <form @submit.prevent="createEntitlements">
                        <div class="mb-4">
                            <label class="block text-sm font-medium text-gray-700">Year</label>
                            <select v-model="entitlementForm.year" required class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500">
                                <option value="">Select Year</option>
                                <option v-for="year in availableYears" :key="year" :value="year">{{ year }}</option>
                            </select>
                        </div>
                        <div class="mb-4">
                            <label class="block text-sm font-medium text-gray-700">Leave Type</label>
                            <select v-model="entitlementForm.leave_type_id" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500">
                                <option value="">All Leave Types</option>
                                <option v-for="type in leaveTypes.data" :key="type.id" :value="type.id">{{ type.name }}</option>
                            </select>
                        </div>
                        <div class="mb-4">
                            <label class="block text-sm font-medium text-gray-700">Employee</label>
                            <select v-model="entitlementForm.employee_id" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500">
                                <option value="">All Employees</option>
                                <option v-for="employee in employees" :key="employee.id" :value="employee.id">{{ employee.first_name }} {{ employee.last_name }}</option>
                            </select>
                        </div>
                        <div class="mb-4">
                            <div class="flex items-center">
                                <input v-model="entitlementForm.override_existing" type="checkbox" id="override_existing" class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                                <label for="override_existing" class="ml-2 block text-sm text-gray-900">Override existing entitlements</label>
                            </div>
                        </div>
                        <div class="flex justify-end space-x-3">
                            <button type="button" @click="showEntitlementModal = false" class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded">
                                Cancel
                            </button>
                            <button type="submit" class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
                                Create Entitlements
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </ManagerLayout>
</template>

<script setup>
import { ref, reactive, computed } from 'vue'
import { Link, router } from '@inertiajs/vue3'
import ManagerLayout from '@/Layouts/ManagerLayout.vue'

const props = defineProps({
    leaveTypes: Object,
    usageStats: Array,
    employees: Array
})

const showEntitlementModal = ref(false)

const entitlementForm = reactive({
    year: new Date().getFullYear(),
    leave_type_id: '',
    employee_id: '',
    override_existing: false
})

const availableYears = computed(() => {
    const currentYear = new Date().getFullYear()
    return [currentYear - 1, currentYear, currentYear + 1]
})

const toggleStatus = (id) => {
    if (confirm('Are you sure you want to toggle the status of this leave type?')) {
        router.post(route('hrm.leave-types.toggle-status', id), {}, {
            preserveScroll: true
        })
    }
}

const deleteLeaveType = (id) => {
    if (confirm('Are you sure you want to delete this leave type? This action cannot be undone.')) {
        router.delete(route('hrm.leave-types.destroy', id), {
            preserveScroll: true
        })
    }
}

const createEntitlements = () => {
    router.post(route('hrm.leave-types.create-entitlements'), entitlementForm, {
        preserveScroll: true,
        onSuccess: () => {
            showEntitlementModal.value = false
            entitlementForm.leave_type_id = ''
            entitlementForm.employee_id = ''
            entitlementForm.override_existing = false
        }
    })
}

const exportLeaveTypes = () => {
    window.open(route('hrm.leave-types.export'), '_blank')
}

const generateReport = () => {
    // This would typically open a report generation modal or redirect to a report page
    alert('Report generation feature will be implemented')
}

const formatDate = (date) => {
    return new Date(date).toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'short',
        day: 'numeric'
    })
}
</script>
