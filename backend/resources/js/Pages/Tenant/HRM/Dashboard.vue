<template>
    <ManagerLayout title="HRM Dashboard">
        <template #header>
            <h2 class="font-semibold text-xl text-gray-800 dark:text-gray-200 leading-tight">
                Human Resource Management
            </h2>
        </template>

        <div class="py-12">
            <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
                <!-- Stats Cards -->
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
                    <!-- Employee Stats -->
                    <div class="bg-white overflow-hidden shadow-xl sm:rounded-lg p-6">
                        <div class="flex items-center">
                            <div class="flex-shrink-0">
                                <div class="flex items-center justify-center h-8 w-8 rounded-md bg-blue-500 text-white">
                                    <svg class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z" />
                                    </svg>
                                </div>
                            </div>
                            <div class="ml-5 w-0 flex-1">
                                <dl>
                                    <dt class="text-sm font-medium text-gray-500 truncate">Total Employees</dt>
                                    <dd class="text-lg font-medium text-gray-900">{{ employeeStats.total_employees }}</dd>
                                </dl>
                            </div>
                        </div>
                    </div>

                    <!-- Attendance Stats -->
                    <div class="bg-white overflow-hidden shadow-xl sm:rounded-lg p-6">
                        <div class="flex items-center">
                            <div class="flex-shrink-0">
                                <div class="flex items-center justify-center h-8 w-8 rounded-md bg-green-500 text-white">
                                    <svg class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                                    </svg>
                                </div>
                            </div>
                            <div class="ml-5 w-0 flex-1">
                                <dl>
                                    <dt class="text-sm font-medium text-gray-500 truncate">Present Today</dt>
                                    <dd class="text-lg font-medium text-gray-900">{{ attendanceStats.present_today }}</dd>
                                </dl>
                            </div>
                        </div>
                    </div>

                    <!-- Leave Stats -->
                    <div class="bg-white overflow-hidden shadow-xl sm:rounded-lg p-6">
                        <div class="flex items-center">
                            <div class="flex-shrink-0">
                                <div class="flex items-center justify-center h-8 w-8 rounded-md bg-yellow-500 text-white">
                                    <svg class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
                                    </svg>
                                </div>
                            </div>
                            <div class="ml-5 w-0 flex-1">
                                <dl>
                                    <dt class="text-sm font-medium text-gray-500 truncate">Pending Leaves</dt>
                                    <dd class="text-lg font-medium text-gray-900">{{ leaveStats.pending_requests }}</dd>
                                </dl>
                            </div>
                        </div>
                    </div>

                    <!-- Payroll Stats -->
                    <div class="bg-white overflow-hidden shadow-xl sm:rounded-lg p-6">
                        <div class="flex items-center">
                            <div class="flex-shrink-0">
                                <div class="flex items-center justify-center h-8 w-8 rounded-md bg-purple-500 text-white">
                                    <svg class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1" />
                                    </svg>
                                </div>
                            </div>
                            <div class="ml-5 w-0 flex-1">
                                <dl>
                                    <dt class="text-sm font-medium text-gray-500 truncate">Monthly Payroll</dt>
                                    <dd class="text-lg font-medium text-gray-900">{{ formatCurrency(payrollStats.total_payroll_this_month) }}</dd>
                                </dl>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Quick Actions -->
                <div class="grid grid-cols-1 lg:grid-cols-3 gap-6 mb-8">
                    <div class="bg-white overflow-hidden shadow-xl sm:rounded-lg p-6">
                        <h3 class="text-lg font-medium text-gray-900 mb-4">Quick Actions</h3>
                        <div class="space-y-3">
                            <Link :href="route('hrm.attendance.index')" class="flex items-center p-3 bg-blue-50 rounded-lg hover:bg-blue-100 transition-colors">
                                <svg class="h-5 w-5 text-blue-600 mr-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
                                </svg>
                                <span class="text-sm font-medium text-blue-900">Mark Attendance</span>
                            </Link>
                            <Link :href="route('hrm.leave.create')" class="flex items-center p-3 bg-green-50 rounded-lg hover:bg-green-100 transition-colors">
                                <svg class="h-5 w-5 text-green-600 mr-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                                </svg>
                                <span class="text-sm font-medium text-green-900">Apply for Leave</span>
                            </Link>
                            <Link :href="route('hrm.payroll.create')" class="flex items-center p-3 bg-purple-50 rounded-lg hover:bg-purple-100 transition-colors">
                                <svg class="h-5 w-5 text-purple-600 mr-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 7h6m0 10v-3m-3 3h.01M9 17h.01M9 14h.01M12 14h.01M15 11h.01M12 11h.01M9 11h.01M7 21h10a2 2 0 002-2V5a2 2 0 00-2-2H7a2 2 0 00-2 2v14a2 2 0 002 2z" />
                                </svg>
                                <span class="text-sm font-medium text-purple-900">Generate Payroll</span>
                            </Link>
                        </div>
                    </div>

                    <!-- Recent Activities -->
                    <div class="bg-white overflow-hidden shadow-xl sm:rounded-lg p-6">
                        <h3 class="text-lg font-medium text-gray-900 mb-4">Recent Activities</h3>
                        <div class="space-y-3">
                            <div v-for="activity in recentActivities.slice(0, 5)" :key="activity.id" class="flex items-start">
                                <div class="flex-shrink-0">
                                    <div :class="getActivityColor(activity.type)" class="h-2 w-2 rounded-full mt-2"></div>
                                </div>
                                <div class="ml-3 min-w-0 flex-1">
                                    <p class="text-sm font-medium text-gray-900">{{ activity.title }}</p>
                                    <p class="text-sm text-gray-500">{{ activity.description }}</p>
                                    <p class="text-xs text-gray-400">{{ formatDate(activity.timestamp) }}</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Upcoming Events -->
                    <div class="bg-white overflow-hidden shadow-xl sm:rounded-lg p-6">
                        <h3 class="text-lg font-medium text-gray-900 mb-4">Upcoming Events</h3>
                        <div class="space-y-3">
                            <div v-for="event in upcomingEvents.slice(0, 5)" :key="event.id" class="flex items-start">
                                <div class="flex-shrink-0">
                                    <div :class="getEventColor(event.type)" class="h-2 w-2 rounded-full mt-2"></div>
                                </div>
                                <div class="ml-3 min-w-0 flex-1">
                                    <p class="text-sm font-medium text-gray-900">{{ event.title }}</p>
                                    <p class="text-sm text-gray-500">{{ event.description }}</p>
                                    <p class="text-xs text-gray-400">{{ formatDate(event.date) }}</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Detailed Stats Grid -->
                <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                    <!-- Attendance Overview -->
                    <div class="bg-white overflow-hidden shadow-xl sm:rounded-lg p-6">
                        <h3 class="text-lg font-medium text-gray-900 mb-4">Today's Attendance</h3>
                        <div class="grid grid-cols-2 gap-4">
                            <div class="text-center p-4 bg-green-50 rounded-lg">
                                <div class="text-2xl font-bold text-green-600">{{ attendanceStats.present_today }}</div>
                                <div class="text-sm text-green-800">Present</div>
                            </div>
                            <div class="text-center p-4 bg-yellow-50 rounded-lg">
                                <div class="text-2xl font-bold text-yellow-600">{{ attendanceStats.late_today }}</div>
                                <div class="text-sm text-yellow-800">Late</div>
                            </div>
                            <div class="text-center p-4 bg-red-50 rounded-lg">
                                <div class="text-2xl font-bold text-red-600">{{ attendanceStats.absent_today }}</div>
                                <div class="text-sm text-red-800">Absent</div>
                            </div>
                            <div class="text-center p-4 bg-blue-50 rounded-lg">
                                <div class="text-2xl font-bold text-blue-600">{{ attendanceStats.on_leave_today }}</div>
                                <div class="text-sm text-blue-800">On Leave</div>
                            </div>
                        </div>
                    </div>

                    <!-- Leave Overview -->
                    <div class="bg-white overflow-hidden shadow-xl sm:rounded-lg p-6">
                        <h3 class="text-lg font-medium text-gray-900 mb-4">Leave Management</h3>
                        <div class="space-y-4">
                            <div class="flex justify-between items-center">
                                <span class="text-sm font-medium text-gray-700">Pending Requests</span>
                                <span class="text-sm font-bold text-yellow-600">{{ leaveStats.pending_requests }}</span>
                            </div>
                            <div class="flex justify-between items-center">
                                <span class="text-sm font-medium text-gray-700">Approved This Month</span>
                                <span class="text-sm font-bold text-green-600">{{ leaveStats.approved_this_month }}</span>
                            </div>
                            <div class="flex justify-between items-center">
                                <span class="text-sm font-medium text-gray-700">On Leave Today</span>
                                <span class="text-sm font-bold text-blue-600">{{ leaveStats.on_leave_today }}</span>
                            </div>
                            <div class="flex justify-between items-center">
                                <span class="text-sm font-medium text-gray-700">Total Days This Month</span>
                                <span class="text-sm font-bold text-purple-600">{{ leaveStats.leave_days_this_month }}</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </ManagerLayout>
</template>

<script setup>
import { Link } from '@inertiajs/vue3'
import ManagerLayout from '@/Layouts/ManagerLayout.vue'

const props = defineProps({
    employeeStats: Object,
    attendanceStats: Object,
    leaveStats: Object,
    payrollStats: Object,
    loanStats: Object,
    recentActivities: Array,
    upcomingEvents: Array,
    quickActionsData: Object
})

const formatCurrency = (amount) => {
    return new Intl.NumberFormat('en-BD', {
        style: 'currency',
        currency: 'BDT',
        minimumFractionDigits: 0,
        maximumFractionDigits: 0
    }).format(amount || 0)
}

const formatDate = (date) => {
    return new Date(date).toLocaleDateString('en-US', {
        month: 'short',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit'
    })
}

const getActivityColor = (type) => {
    const colors = {
        'leave_request': 'bg-yellow-400',
        'loan': 'bg-blue-400',
        'payroll': 'bg-green-400',
        'attendance': 'bg-purple-400'
    }
    return colors[type] || 'bg-gray-400'
}

const getEventColor = (type) => {
    const colors = {
        'leave': 'bg-yellow-400',
        'loan_payment': 'bg-blue-400',
        'probation_end': 'bg-red-400',
        'birthday': 'bg-green-400'
    }
    return colors[type] || 'bg-gray-400'
}
</script>
