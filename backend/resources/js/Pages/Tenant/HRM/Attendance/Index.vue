<template>
    <ManagerLayout title="Attendance Management">
        <template #header>
            <div class="flex justify-between items-center">
                <h2 class="font-semibold text-xl text-gray-800 dark:text-gray-200 leading-tight">
                    Attendance Management
                </h2>
                <div class="flex space-x-3">
                    <button @click="showClockInModal = true" class="bg-green-600 hover:bg-green-700 text-white font-bold py-2 px-4 rounded">
                        Clock In/Out
                    </button>
                    <Link :href="route('hrm.attendance.report')" class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
                        Generate Report
                    </Link>
                </div>
            </div>
        </template>

        <div class="py-12">
            <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
                <!-- Today's Summary -->
                <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
                    <div class="bg-white overflow-hidden shadow-xl sm:rounded-lg p-6">
                        <div class="text-center">
                            <div class="text-2xl font-bold text-green-600">{{ stats?.present_count || 0 }}</div>
                            <div class="text-sm text-gray-500">Present</div>
                        </div>
                    </div>
                    <div class="bg-white overflow-hidden shadow-xl sm:rounded-lg p-6">
                        <div class="text-center">
                            <div class="text-2xl font-bold text-yellow-600">{{ stats?.late_count || 0 }}</div>
                            <div class="text-sm text-gray-500">Late</div>
                        </div>
                    </div>
                    <div class="bg-white overflow-hidden shadow-xl sm:rounded-lg p-6">
                        <div class="text-center">
                            <div class="text-2xl font-bold text-red-600">{{ stats?.absent_count || 0 }}</div>
                            <div class="text-sm text-gray-500">Absent</div>
                        </div>
                    </div>
                    <div class="bg-white overflow-hidden shadow-xl sm:rounded-lg p-6">
                        <div class="text-center">
                            <div class="text-2xl font-bold text-blue-600">{{ stats?.on_leave_count || 0 }}</div>
                            <div class="text-sm text-gray-500">On Leave</div>
                        </div>
                    </div>
                </div>

                <!-- Filters -->
                <div class="bg-white overflow-hidden shadow-xl sm:rounded-lg p-6 mb-6">
                    <form @submit.prevent="applyFilters" class="grid grid-cols-1 md:grid-cols-5 gap-4">
                        <div>
                            <label class="block text-sm font-medium text-gray-700">Date</label>
                            <input v-model="form.date" type="date" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500">
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700">Branch</label>
                            <select v-model="form.branch_id" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500">
                                <option value="">All Branches</option>
                                <option v-for="branch in branches" :key="branch.id" :value="branch.id">{{ branch.name }}</option>
                            </select>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700">Employee Search</label>
                            <input v-model="form.employee_search" type="text" placeholder="Search by name..." class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500">
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700">Status</label>
                            <select v-model="form.status" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500">
                                <option value="">All Status</option>
                                <option value="present">Present</option>
                                <option value="absent">Absent</option>
                                <option value="late">Late</option>
                                <option value="on_leave">On Leave</option>
                            </select>
                        </div>
                        <div class="flex items-end">
                            <button type="submit" class="w-full bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
                                Filter
                            </button>
                        </div>
                    </form>
                </div>

                <!-- Attendance Table -->
                <div class="bg-white overflow-hidden shadow-xl sm:rounded-lg">
                    <div class="overflow-x-auto">
                        <table class="min-w-full divide-y divide-gray-200">
                            <thead class="bg-gray-50">
                                <tr>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Employee</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Clock In</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Clock Out</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Total Hours</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                                </tr>
                            </thead>
                            <tbody class="bg-white divide-y divide-gray-200">
                                <tr v-for="attendance in teamAttendance" :key="attendance.employee_id" class="hover:bg-gray-50">
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="flex items-center">
                                            <div class="flex-shrink-0 h-10 w-10">
                                                <div class="h-10 w-10 rounded-full bg-gray-300 flex items-center justify-center">
                                                    <span class="text-sm font-medium text-gray-700">{{ getInitials(attendance.employee.first_name, attendance.employee.last_name) }}</span>
                                                </div>
                                            </div>
                                            <div class="ml-4">
                                                <div class="text-sm font-medium text-gray-900">{{ attendance.employee.first_name }} {{ attendance.employee.last_name }}</div>
                                                <div class="text-sm text-gray-500">{{ attendance.employee.employee_id }}</div>
                                            </div>
                                        </div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                        {{ formatDate(attendance.date) }}
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                        <span v-if="attendance.clock_in_time">{{ formatTime(attendance.clock_in_time) }}</span>
                                        <span v-else class="text-gray-400">-</span>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                        <span v-if="attendance.clock_out_time">{{ formatTime(attendance.clock_out_time) }}</span>
                                        <span v-else class="text-gray-400">-</span>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                        <span v-if="attendance.total_hours">{{ attendance.total_hours }}h</span>
                                        <span v-else class="text-gray-400">-</span>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <span :class="getStatusColor(attendance.status)" class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium">
                                            {{ attendance.status.charAt(0).toUpperCase() + attendance.status.slice(1) }}
                                        </span>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                        <div class="flex space-x-2">
                                            <Link :href="route('hrm.attendance.show', attendance.employee.id)" class="text-blue-600 hover:text-blue-900">View</Link>
                                            <button v-if="!attendance.clock_out_time && attendance.status === 'present'" @click="clockOut(attendance.id)" class="text-red-600 hover:text-red-900">Clock Out</button>
                                            <button v-if="attendance.status === 'absent'" @click="markPresent(attendance.id)" class="text-green-600 hover:text-green-900">Mark Present</button>
                                        </div>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>

                    <!-- Results Summary -->
                    <div class="bg-white px-4 py-3 border-t border-gray-200 sm:px-6">
                        <div class="text-sm text-gray-700">
                            Showing {{ teamAttendance?.length || 0 }} attendance records for {{ form.date }}
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Clock In/Out Modal -->
        <div v-if="showClockInModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
            <div class="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
                <div class="mt-3">
                    <h3 class="text-lg font-medium text-gray-900 mb-4">Clock In/Out</h3>
                    <form @submit.prevent="submitClockAction">
                        <div class="mb-4">
                            <label class="block text-sm font-medium text-gray-700">Employee</label>
                            <select v-model="clockForm.employee_id" required class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500">
                                <option value="">Select Employee</option>
                                <option v-for="attendance in teamAttendance" :key="attendance.employee_id" :value="attendance.employee_id">{{ attendance.employee.first_name }} {{ attendance.employee.last_name }}</option>
                            </select>
                        </div>
                        <div class="mb-4">
                            <label class="block text-sm font-medium text-gray-700">Action</label>
                            <select v-model="clockForm.action" required class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500">
                                <option value="">Select Action</option>
                                <option value="clock_in">Clock In</option>
                                <option value="clock_out">Clock Out</option>
                            </select>
                        </div>
                        <div class="mb-4">
                            <label class="block text-sm font-medium text-gray-700">Notes (Optional)</label>
                            <textarea v-model="clockForm.notes" rows="3" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"></textarea>
                        </div>
                        <div class="flex justify-end space-x-3">
                            <button type="button" @click="showClockInModal = false" class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded">
                                Cancel
                            </button>
                            <button type="submit" class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
                                Submit
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </ManagerLayout>
</template>

<script setup>
import { ref, reactive } from 'vue'
import { Link, router } from '@inertiajs/vue3'
import ManagerLayout from '@/Layouts/ManagerLayout.vue'

const props = defineProps({
    teamAttendance: Array,
    stats: Object,
    branches: Array,
    selectedDate: String,
    selectedBranch: Object
})

const showClockInModal = ref(false)

const form = reactive({
    date: props.selectedDate || new Date().toISOString().split('T')[0],
    branch_id: props.selectedBranch?.id || '',
    employee_search: '',
    status: ''
})

const clockForm = reactive({
    employee_id: '',
    action: '',
    notes: ''
})

const applyFilters = () => {
    router.get(route('hrm.attendance.index'), form, {
        preserveState: true,
        preserveScroll: true
    })
}

const submitClockAction = () => {
    if (clockForm.action === 'clock_in') {
        router.post(route('hrm.attendance.clock-in'), clockForm, {
            preserveScroll: true,
            onSuccess: () => {
                showClockInModal.value = false
                clockForm.employee_id = ''
                clockForm.action = ''
                clockForm.notes = ''
            }
        })
    } else {
        router.post(route('hrm.attendance.clock-out'), clockForm, {
            preserveScroll: true,
            onSuccess: () => {
                showClockInModal.value = false
                clockForm.employee_id = ''
                clockForm.action = ''
                clockForm.notes = ''
            }
        })
    }
}

const clockOut = (attendanceId) => {
    if (confirm('Are you sure you want to clock out this employee?')) {
        router.post(route('hrm.attendance.clock-out'), { attendance_id: attendanceId }, {
            preserveScroll: true
        })
    }
}

const markPresent = (attendanceId) => {
    if (confirm('Are you sure you want to mark this employee as present?')) {
        router.put(route('hrm.attendance.update', attendanceId), { status: 'present' }, {
            preserveScroll: true
        })
    }
}

const getInitials = (firstName, lastName) => {
    return (firstName.charAt(0) + lastName.charAt(0)).toUpperCase()
}

const formatDate = (date) => {
    return new Date(date).toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'short',
        day: 'numeric'
    })
}

const formatTime = (time) => {
    return new Date(`2000-01-01T${time}`).toLocaleTimeString('en-US', {
        hour: '2-digit',
        minute: '2-digit',
        hour12: true
    })
}

const getStatusColor = (status) => {
    const colors = {
        'present': 'bg-green-100 text-green-800',
        'absent': 'bg-red-100 text-red-800',
        'late': 'bg-yellow-100 text-yellow-800',
        'on_leave': 'bg-blue-100 text-blue-800'
    }
    return colors[status] || 'bg-gray-100 text-gray-800'
}
</script>
