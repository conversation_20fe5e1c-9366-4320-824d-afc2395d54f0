<template>
    <ManagerLayout title="Payroll Management">
        <template #header>
            <div class="flex justify-between items-center">
                <h2 class="font-semibold text-xl text-gray-800 dark:text-gray-200 leading-tight">
                    Payroll Management
                </h2>
                <div class="flex space-x-3">
                    <Link :href="route('hrm.payroll.create')" class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
                        Generate Payroll
                    </Link>
                    <button @click="showBulkModal = true" class="bg-green-600 hover:bg-green-700 text-white font-bold py-2 px-4 rounded">
                        Bulk Actions
                    </button>
                </div>
            </div>
        </template>

        <div class="py-12">
            <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
                <!-- Summary Cards -->
                <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
                    <div class="bg-white overflow-hidden shadow-xl sm:rounded-lg p-6">
                        <div class="text-center">
                            <div class="text-2xl font-bold text-blue-600">{{ formatCurrency(summary.total_gross_pay) }}</div>
                            <div class="text-sm text-gray-500">Total Gross Pay</div>
                        </div>
                    </div>
                    <div class="bg-white overflow-hidden shadow-xl sm:rounded-lg p-6">
                        <div class="text-center">
                            <div class="text-2xl font-bold text-green-600">{{ formatCurrency(summary.total_net_pay) }}</div>
                            <div class="text-sm text-gray-500">Total Net Pay</div>
                        </div>
                    </div>
                    <div class="bg-white overflow-hidden shadow-xl sm:rounded-lg p-6">
                        <div class="text-center">
                            <div class="text-2xl font-bold text-yellow-600">{{ summary.pending_approvals }}</div>
                            <div class="text-sm text-gray-500">Pending Approvals</div>
                        </div>
                    </div>
                    <div class="bg-white overflow-hidden shadow-xl sm:rounded-lg p-6">
                        <div class="text-center">
                            <div class="text-2xl font-bold text-purple-600">{{ summary.processed_count }}</div>
                            <div class="text-sm text-gray-500">Processed</div>
                        </div>
                    </div>
                </div>

                <!-- Filters -->
                <div class="bg-white overflow-hidden shadow-xl sm:rounded-lg p-6 mb-6">
                    <form @submit.prevent="applyFilters" class="grid grid-cols-1 md:grid-cols-6 gap-4">
                        <div>
                            <label class="block text-sm font-medium text-gray-700">Pay Period</label>
                            <select v-model="form.pay_period" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500">
                                <option value="">All Periods</option>
                                <option v-for="period in payPeriods" :key="period.value" :value="period.value">{{ period.label }}</option>
                            </select>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700">Employee</label>
                            <select v-model="form.employee_id" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500">
                                <option value="">All Employees</option>
                                <option v-for="employee in employees" :key="employee.id" :value="employee.id">{{ employee.first_name }} {{ employee.last_name }}</option>
                            </select>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700">Status</label>
                            <select v-model="form.status" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500">
                                <option value="">All Status</option>
                                <option value="draft">Draft</option>
                                <option value="approved">Approved</option>
                                <option value="paid">Paid</option>
                                <option value="cancelled">Cancelled</option>
                            </select>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700">Department</label>
                            <select v-model="form.department_id" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500">
                                <option value="">All Departments</option>
                                <option v-for="dept in departments" :key="dept.id" :value="dept.id">{{ dept.name }}</option>
                            </select>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700">From Date</label>
                            <input v-model="form.date_from" type="date" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500">
                        </div>
                        <div class="flex items-end">
                            <button type="submit" class="w-full bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
                                Filter
                            </button>
                        </div>
                    </form>
                </div>

                <!-- Payroll Table -->
                <div class="bg-white overflow-hidden shadow-xl sm:rounded-lg">
                    <div class="overflow-x-auto">
                        <table class="min-w-full divide-y divide-gray-200">
                            <thead class="bg-gray-50">
                                <tr>
                                    <th class="px-6 py-3 text-left">
                                        <input type="checkbox" @change="toggleSelectAll" class="rounded border-gray-300">
                                    </th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Employee</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Pay Period</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Gross Pay</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Deductions</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Net Pay</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                                </tr>
                            </thead>
                            <tbody class="bg-white divide-y divide-gray-200">
                                <tr v-for="payroll in payrollRecords.data" :key="payroll.id" class="hover:bg-gray-50">
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <input type="checkbox" v-model="selectedPayrolls" :value="payroll.id" class="rounded border-gray-300">
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="flex items-center">
                                            <div class="flex-shrink-0 h-10 w-10">
                                                <div class="h-10 w-10 rounded-full bg-gray-300 flex items-center justify-center">
                                                    <span class="text-sm font-medium text-gray-700">{{ getInitials(payroll.employee.first_name, payroll.employee.last_name) }}</span>
                                                </div>
                                            </div>
                                            <div class="ml-4">
                                                <div class="text-sm font-medium text-gray-900">{{ payroll.employee.first_name }} {{ payroll.employee.last_name }}</div>
                                                <div class="text-sm text-gray-500">{{ payroll.employee.employee_id }}</div>
                                            </div>
                                        </div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                        {{ formatDate(payroll.pay_period_start) }} - {{ formatDate(payroll.pay_period_end) }}
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                        {{ formatCurrency(payroll.gross_pay) }}
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                        {{ formatCurrency(payroll.total_deductions) }}
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                                        {{ formatCurrency(payroll.net_pay) }}
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <span :class="getStatusColor(payroll.status)" class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium">
                                            {{ payroll.status.charAt(0).toUpperCase() + payroll.status.slice(1) }}
                                        </span>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                        <div class="flex space-x-2">
                                            <Link :href="route('hrm.payroll.show', payroll.id)" class="text-blue-600 hover:text-blue-900">View</Link>
                                            <Link v-if="payroll.status === 'draft'" :href="route('hrm.payroll.edit', payroll.id)" class="text-green-600 hover:text-green-900">Edit</Link>
                                            <button v-if="payroll.status === 'draft'" @click="approvePayroll(payroll.id)" class="text-green-600 hover:text-green-900">Approve</button>
                                            <button v-if="payroll.status === 'approved'" @click="markAsPaid(payroll.id)" class="text-purple-600 hover:text-purple-900">Mark Paid</button>
                                            <Link :href="route('hrm.payroll.payslip', payroll.id)" class="text-indigo-600 hover:text-indigo-900">Payslip</Link>
                                        </div>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>

                    <!-- Pagination -->
                    <div class="bg-white px-4 py-3 flex items-center justify-between border-t border-gray-200 sm:px-6">
                        <div class="flex-1 flex justify-between sm:hidden">
                            <Link v-if="payrollRecords.prev_page_url" :href="payrollRecords.prev_page_url" class="relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">Previous</Link>
                            <Link v-if="payrollRecords.next_page_url" :href="payrollRecords.next_page_url" class="ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">Next</Link>
                        </div>
                        <div class="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
                            <div>
                                <p class="text-sm text-gray-700">
                                    Showing <span class="font-medium">{{ payrollRecords.from }}</span> to <span class="font-medium">{{ payrollRecords.to }}</span> of <span class="font-medium">{{ payrollRecords.total }}</span> results
                                </p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Bulk Actions Modal -->
        <div v-if="showBulkModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
            <div class="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
                <div class="mt-3">
                    <h3 class="text-lg font-medium text-gray-900 mb-4">Bulk Actions</h3>
                    <div class="space-y-4">
                        <button @click="bulkGenerate" class="w-full bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
                            Bulk Generate Payroll
                        </button>
                        <button @click="bulkApprove" :disabled="selectedPayrolls.length === 0" class="w-full bg-green-600 hover:bg-green-700 text-white font-bold py-2 px-4 rounded disabled:opacity-50">
                            Bulk Approve ({{ selectedPayrolls.length }} selected)
                        </button>
                        <button @click="bulkMarkAsPaid" :disabled="selectedPayrolls.length === 0" class="w-full bg-purple-600 hover:bg-purple-700 text-white font-bold py-2 px-4 rounded disabled:opacity-50">
                            Bulk Mark as Paid ({{ selectedPayrolls.length }} selected)
                        </button>
                        <button @click="showBulkModal = false" class="w-full bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded">
                            Cancel
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </ManagerLayout>
</template>

<script setup>
import { ref, reactive } from 'vue'
import { Link, router } from '@inertiajs/vue3'
import ManagerLayout from '@/Layouts/ManagerLayout.vue'

const props = defineProps({
    payrollRecords: Object,
    summary: Object,
    employees: Array,
    departments: Array,
    payPeriods: Array,
    filters: Object
})

const showBulkModal = ref(false)
const selectedPayrolls = ref([])

const form = reactive({
    pay_period: props.filters.pay_period || '',
    employee_id: props.filters.employee_id || '',
    status: props.filters.status || '',
    department_id: props.filters.department_id || '',
    date_from: props.filters.date_from || ''
})

const applyFilters = () => {
    router.get(route('hrm.payroll.index'), form, {
        preserveState: true,
        preserveScroll: true
    })
}

const toggleSelectAll = (event) => {
    if (event.target.checked) {
        selectedPayrolls.value = props.payrollRecords.data.map(p => p.id)
    } else {
        selectedPayrolls.value = []
    }
}

const approvePayroll = (id) => {
    if (confirm('Are you sure you want to approve this payroll?')) {
        router.post(route('hrm.payroll.approve', id), {}, {
            preserveScroll: true
        })
    }
}

const markAsPaid = (id) => {
    if (confirm('Are you sure you want to mark this payroll as paid?')) {
        router.post(route('hrm.payroll.mark-as-paid', id), {}, {
            preserveScroll: true
        })
    }
}

const bulkGenerate = () => {
    const payPeriod = prompt('Enter pay period (YYYY-MM):')
    if (payPeriod) {
        router.post(route('hrm.payroll.bulk-generate'), { pay_period: payPeriod }, {
            preserveScroll: true,
            onSuccess: () => {
                showBulkModal.value = false
            }
        })
    }
}

const bulkApprove = () => {
    if (confirm(`Are you sure you want to approve ${selectedPayrolls.value.length} payroll records?`)) {
        router.post(route('hrm.payroll.bulk-approve'), { payroll_ids: selectedPayrolls.value }, {
            preserveScroll: true,
            onSuccess: () => {
                selectedPayrolls.value = []
                showBulkModal.value = false
            }
        })
    }
}

const bulkMarkAsPaid = () => {
    if (confirm(`Are you sure you want to mark ${selectedPayrolls.value.length} payroll records as paid?`)) {
        router.post(route('hrm.payroll.bulk-mark-as-paid'), { payroll_ids: selectedPayrolls.value }, {
            preserveScroll: true,
            onSuccess: () => {
                selectedPayrolls.value = []
                showBulkModal.value = false
            }
        })
    }
}

const getInitials = (firstName, lastName) => {
    return (firstName.charAt(0) + lastName.charAt(0)).toUpperCase()
}

const formatDate = (date) => {
    return new Date(date).toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'short',
        day: 'numeric'
    })
}

const formatCurrency = (amount) => {
    return new Intl.NumberFormat('en-BD', {
        style: 'currency',
        currency: 'BDT',
        minimumFractionDigits: 0,
        maximumFractionDigits: 0
    }).format(amount || 0)
}

const getStatusColor = (status) => {
    const colors = {
        'draft': 'bg-gray-100 text-gray-800',
        'approved': 'bg-green-100 text-green-800',
        'paid': 'bg-blue-100 text-blue-800',
        'cancelled': 'bg-red-100 text-red-800'
    }
    return colors[status] || 'bg-gray-100 text-gray-800'
}
</script>
