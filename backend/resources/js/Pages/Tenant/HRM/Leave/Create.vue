<template>
    <ManagerLayout title="Apply for Leave">
        <template #header>
            <div class="flex justify-between items-center">
                <h2 class="font-semibold text-xl text-gray-800 dark:text-gray-200 leading-tight">
                    Apply for Leave
                </h2>
                <Link :href="route('hrm.leave.index')" class="bg-gray-600 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded">
                    Back to Leave Requests
                </Link>
            </div>
        </template>

        <div class="py-12">
            <div class="max-w-4xl mx-auto sm:px-6 lg:px-8">
                <div class="bg-white overflow-hidden shadow-xl sm:rounded-lg">
                    <form @submit.prevent="submit" class="p-6 space-y-6">
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <!-- Employee Selection -->
                            <div>
                                <label for="employee_id" class="block text-sm font-medium text-gray-700">Employee *</label>
                                <select v-model="form.employee_id" id="employee_id" required class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500">
                                    <option value="">Select Employee</option>
                                    <option v-for="employee in employees" :key="employee.id" :value="employee.id">
                                        {{ employee.first_name }} {{ employee.last_name }}
                                    </option>
                                </select>
                                <div v-if="form.errors.employee_id" class="mt-1 text-sm text-red-600">{{ form.errors.employee_id }}</div>
                            </div>

                            <!-- Leave Type -->
                            <div>
                                <label for="leave_type_id" class="block text-sm font-medium text-gray-700">Leave Type *</label>
                                <select v-model="form.leave_type_id" id="leave_type_id" required class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500">
                                    <option value="">Select Leave Type</option>
                                    <option v-for="type in leaveTypes" :key="type.id" :value="type.id">
                                        {{ type.name }} ({{ type.default_days_per_year }} days/year)
                                    </option>
                                </select>
                                <div v-if="form.errors.leave_type_id" class="mt-1 text-sm text-red-600">{{ form.errors.leave_type_id }}</div>
                            </div>

                            <!-- Start Date -->
                            <div>
                                <label for="start_date" class="block text-sm font-medium text-gray-700">Start Date *</label>
                                <input v-model="form.start_date" type="date" id="start_date" required :min="minDate" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500">
                                <div v-if="form.errors.start_date" class="mt-1 text-sm text-red-600">{{ form.errors.start_date }}</div>
                            </div>

                            <!-- End Date -->
                            <div>
                                <label for="end_date" class="block text-sm font-medium text-gray-700">End Date *</label>
                                <input v-model="form.end_date" type="date" id="end_date" required :min="form.start_date || minDate" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500">
                                <div v-if="form.errors.end_date" class="mt-1 text-sm text-red-600">{{ form.errors.end_date }}</div>
                            </div>

                            <!-- Half Day Option -->
                            <div class="md:col-span-2">
                                <div class="flex items-center">
                                    <input v-model="form.is_half_day" type="checkbox" id="is_half_day" class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                                    <label for="is_half_day" class="ml-2 block text-sm text-gray-900">This is a half-day leave</label>
                                </div>
                                <div v-if="form.errors.is_half_day" class="mt-1 text-sm text-red-600">{{ form.errors.is_half_day }}</div>
                            </div>

                            <!-- Half Day Period -->
                            <div v-if="form.is_half_day" class="md:col-span-2">
                                <label class="block text-sm font-medium text-gray-700">Half Day Period *</label>
                                <div class="mt-2 space-x-4">
                                    <label class="inline-flex items-center">
                                        <input v-model="form.half_day_period" type="radio" value="morning" class="form-radio h-4 w-4 text-blue-600">
                                        <span class="ml-2">Morning</span>
                                    </label>
                                    <label class="inline-flex items-center">
                                        <input v-model="form.half_day_period" type="radio" value="afternoon" class="form-radio h-4 w-4 text-blue-600">
                                        <span class="ml-2">Afternoon</span>
                                    </label>
                                </div>
                                <div v-if="form.errors.half_day_period" class="mt-1 text-sm text-red-600">{{ form.errors.half_day_period }}</div>
                            </div>
                        </div>

                        <!-- Reason -->
                        <div>
                            <label for="reason" class="block text-sm font-medium text-gray-700">Reason for Leave *</label>
                            <textarea v-model="form.reason" id="reason" rows="4" required placeholder="Please provide a detailed reason for your leave request..." class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"></textarea>
                            <div v-if="form.errors.reason" class="mt-1 text-sm text-red-600">{{ form.errors.reason }}</div>
                        </div>

                        <!-- Comments -->
                        <div>
                            <label for="comments" class="block text-sm font-medium text-gray-700">Additional Comments</label>
                            <textarea v-model="form.comments" id="comments" rows="3" placeholder="Any additional information..." class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"></textarea>
                            <div v-if="form.errors.comments" class="mt-1 text-sm text-red-600">{{ form.errors.comments }}</div>
                        </div>

                        <!-- Emergency Contact Information -->
                        <div class="border-t pt-6">
                            <h3 class="text-lg font-medium text-gray-900 mb-4">Emergency Contact Information</h3>
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                                <div>
                                    <label for="emergency_contact_name" class="block text-sm font-medium text-gray-700">Contact Name</label>
                                    <input v-model="form.emergency_contact_name" type="text" id="emergency_contact_name" placeholder="Full name of emergency contact" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500">
                                    <div v-if="form.errors.emergency_contact_name" class="mt-1 text-sm text-red-600">{{ form.errors.emergency_contact_name }}</div>
                                </div>

                                <div>
                                    <label for="emergency_contact_phone" class="block text-sm font-medium text-gray-700">Contact Phone</label>
                                    <input v-model="form.emergency_contact_phone" type="tel" id="emergency_contact_phone" placeholder="Phone number" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500">
                                    <div v-if="form.errors.emergency_contact_phone" class="mt-1 text-sm text-red-600">{{ form.errors.emergency_contact_phone }}</div>
                                </div>
                            </div>
                        </div>

                        <!-- Leave Balance Information -->
                        <div v-if="selectedEmployee && selectedLeaveType" class="bg-blue-50 border border-blue-200 rounded-md p-4">
                            <h4 class="text-sm font-medium text-blue-900 mb-2">Leave Balance Information</h4>
                            <div class="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                                <div>
                                    <span class="text-blue-700">Entitled:</span>
                                    <span class="font-medium text-blue-900">{{ leaveBalance.entitled_days || 0 }} days</span>
                                </div>
                                <div>
                                    <span class="text-blue-700">Used:</span>
                                    <span class="font-medium text-blue-900">{{ leaveBalance.used_days || 0 }} days</span>
                                </div>
                                <div>
                                    <span class="text-blue-700">Pending:</span>
                                    <span class="font-medium text-blue-900">{{ leaveBalance.pending_days || 0 }} days</span>
                                </div>
                                <div>
                                    <span class="text-blue-700">Available:</span>
                                    <span class="font-medium text-blue-900">{{ leaveBalance.available_days || 0 }} days</span>
                                </div>
                            </div>
                        </div>

                        <!-- Calculated Days -->
                        <div v-if="calculatedDays > 0" class="bg-green-50 border border-green-200 rounded-md p-4">
                            <div class="flex items-center">
                                <svg class="h-5 w-5 text-green-400 mr-2" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                                </svg>
                                <span class="text-sm font-medium text-green-900">
                                    Total leave days: {{ calculatedDays }} {{ form.is_half_day ? '(Half Day)' : 'day(s)' }}
                                </span>
                            </div>
                        </div>

                        <!-- Submit Buttons -->
                        <div class="flex justify-end space-x-3 pt-6 border-t">
                            <Link :href="route('hrm.leave.index')" class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded">
                                Cancel
                            </Link>
                            <button type="submit" :disabled="form.processing" class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded disabled:opacity-50">
                                <span v-if="form.processing">Submitting...</span>
                                <span v-else>Submit Leave Request</span>
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </ManagerLayout>
</template>

<script setup>
import { ref, computed, watch } from 'vue'
import { Link, useForm } from '@inertiajs/vue3'
import ManagerLayout from '@/Layouts/ManagerLayout.vue'

const props = defineProps({
    leaveTypes: Array,
    employees: Array
})

const form = useForm({
    employee_id: '',
    leave_type_id: '',
    start_date: '',
    end_date: '',
    reason: '',
    comments: '',
    is_half_day: false,
    half_day_period: 'morning',
    emergency_contact_name: '',
    emergency_contact_phone: ''
})

const leaveBalance = ref({})

const minDate = computed(() => {
    const today = new Date()
    return today.toISOString().split('T')[0]
})

const selectedEmployee = computed(() => {
    return props.employees.find(emp => emp.id == form.employee_id)
})

const selectedLeaveType = computed(() => {
    return props.leaveTypes.find(type => type.id == form.leave_type_id)
})

const calculatedDays = computed(() => {
    if (!form.start_date || !form.end_date) return 0
    
    const start = new Date(form.start_date)
    const end = new Date(form.end_date)
    
    if (end < start) return 0
    
    let days = 0
    const current = new Date(start)
    
    while (current <= end) {
        // Skip weekends (Saturday = 6, Sunday = 0)
        if (current.getDay() !== 0 && current.getDay() !== 6) {
            days++
        }
        current.setDate(current.getDate() + 1)
    }
    
    return form.is_half_day ? 0.5 : days
})

// Watch for employee and leave type changes to fetch balance
watch([() => form.employee_id, () => form.leave_type_id], async ([employeeId, leaveTypeId]) => {
    if (employeeId && leaveTypeId) {
        // In a real app, you'd fetch this from an API
        // For now, we'll simulate the data
        leaveBalance.value = {
            entitled_days: selectedLeaveType.value?.default_days_per_year || 0,
            used_days: Math.floor(Math.random() * 10),
            pending_days: Math.floor(Math.random() * 3),
            available_days: (selectedLeaveType.value?.default_days_per_year || 0) - Math.floor(Math.random() * 10) - Math.floor(Math.random() * 3)
        }
    }
})

const submit = () => {
    form.post(route('hrm.leave.store'))
}
</script>
