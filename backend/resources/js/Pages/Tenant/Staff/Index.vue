<script setup>
import { Head, Link, router } from '@inertiajs/vue3';
import { useI18n } from 'vue-i18n';
import ManagerLayout from '@/Layouts/ManagerLayout.vue';
import Pagination from '@/Components/Pagination.vue';
import { ref, computed, onMounted, onUnmounted } from 'vue';

// Vue i18n integration
const { t } = useI18n();

const props = defineProps({
    staff: Object,
    stats: Object,
    departments: Array,
    branches: Array,
    roles: Array,
    filters: Object,
});

// Reactive data
const isLoading = ref(false);
const selectedStaff = ref([]);
const showFilters = ref(false);
const openDropdowns = ref({});

// Form for filters
const filterForm = ref({
    search: props.filters.search || '',
    role: props.filters.role || '',
    status: props.filters.status || '',
    department_id: props.filters.department_id || '',
    branch_id: props.filters.branch_id || '',
});

// Computed properties
const hasFilters = computed(() => {
    return Object.values(props.filters).some(value => value);
});

// Methods
const toggleDropdown = (staffId) => {
    openDropdowns.value = {
        ...openDropdowns.value,
        [staffId]: !openDropdowns.value[staffId]
    };
};

const closeAllDropdowns = () => {
    openDropdowns.value = {};
};

const applyFilters = () => {
    router.get(route('staff.index'), filterForm.value, {
        preserveState: true,
        preserveScroll: true,
    });
};

const clearFilters = () => {
    filterForm.value = {
        search: '',
        role: '',
        status: '',
        department_id: '',
        branch_id: '',
    };
    applyFilters();
};

const toggleStatus = (staff) => {
    closeAllDropdowns();
    if (confirm(`Are you sure you want to ${staff.is_active ? 'deactivate' : 'activate'} ${staff.display_name || staff.name}?`)) {
        router.post(route('staff.toggle-status', staff.id), {}, {
            preserveScroll: true,
        });
    }
};

const resetPassword = (staff) => {
    closeAllDropdowns();
    if (confirm(`Are you sure you want to reset password for ${staff.display_name || staff.name}?`)) {
        router.post(route('staff.reset-password', staff.id), {}, {
            preserveScroll: true,
        });
    }
};

const deleteStaff = (staff) => {
    closeAllDropdowns();
    if (confirm(`Are you sure you want to terminate ${staff.display_name || staff.name}? This action cannot be undone.`)) {
        router.delete(route('staff.destroy', staff.id), {
            preserveScroll: true,
        });
    }
};

const getRoleColor = (role) => {
    const colors = {
        waiter: 'bg-blue-100 text-blue-800',
        chef: 'bg-orange-100 text-orange-800',
        rider: 'bg-green-100 text-green-800',
        manager: 'bg-purple-100 text-purple-800',
    };
    return colors[role] || 'bg-gray-100 text-gray-800';
};

const getStatusColor = (status) => {
    const colors = {
        active: 'bg-green-100 text-green-800',
        inactive: 'bg-yellow-100 text-yellow-800',
        terminated: 'bg-red-100 text-red-800',
    };
    return colors[status] || 'bg-gray-100 text-gray-800';
};

// Handle click outside to close dropdowns
const handleClickOutside = (event) => {
    // Check if the click is outside any dropdown
    const dropdownElements = document.querySelectorAll('[data-dropdown]');
    let clickedOutside = true;

    dropdownElements.forEach(element => {
        if (element.contains(event.target)) {
            clickedOutside = false;
        }
    });

    if (clickedOutside) {
        closeAllDropdowns();
    }
};

// Lifecycle hooks
onMounted(() => {
    document.addEventListener('click', handleClickOutside);
});

onUnmounted(() => {
    document.removeEventListener('click', handleClickOutside);
});
</script>

<template>
    <Head title="Staff Management" />

    <ManagerLayout title="Staff Management">
        <template #header>
            <div class="flex justify-between items-center">
                <div>
                    <h2 class="font-semibold text-xl text-gray-800 dark:text-gray-200 leading-tight">
                        👨‍💼 Staff Management
                    </h2>
                    <p class="text-sm text-gray-600 dark:text-gray-400 mt-1">
                        Manage restaurant staff with role-based access control
                    </p>
                </div>

                <div class="flex space-x-3">
                    <button
                        @click="showFilters = !showFilters"
                        class="inline-flex items-center px-4 py-2 bg-gray-600 border border-transparent rounded-md font-semibold text-xs text-white uppercase tracking-widest hover:bg-gray-700 focus:bg-gray-700 active:bg-gray-900 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2 transition ease-in-out duration-150"
                    >
                        🔍 Filters
                    </button>
                    <Link
                        :href="route('staff.create')"
                        class="inline-flex items-center px-4 py-2 bg-indigo-600 border border-transparent rounded-md font-semibold text-xs text-white uppercase tracking-widest hover:bg-indigo-700 focus:bg-indigo-700 active:bg-indigo-900 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 transition ease-in-out duration-150"
                    >
                        ➕ Add Staff
                    </Link>
                </div>
            </div>
        </template>

        <div class="py-12">
            <div class="max-w-7xl mx-auto sm:px-6 lg:px-8 space-y-6">

                <!-- Stats Cards -->
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-6 gap-6">
                    <div class="bg-white dark:bg-gray-800 overflow-hidden shadow rounded-lg">
                        <div class="p-5">
                            <div class="flex items-center">
                                <div class="flex-shrink-0">
                                    <div class="text-2xl">👥</div>
                                </div>
                                <div class="ml-5 w-0 flex-1">
                                    <dl>
                                        <dt class="text-sm font-medium text-gray-500 dark:text-gray-400 truncate">
                                            Total Staff
                                        </dt>
                                        <dd class="text-lg font-medium text-gray-900 dark:text-gray-100">
                                            {{ stats.total_staff }}
                                        </dd>
                                    </dl>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="bg-white dark:bg-gray-800 overflow-hidden shadow rounded-lg">
                        <div class="p-5">
                            <div class="flex items-center">
                                <div class="flex-shrink-0">
                                    <div class="text-2xl">🍽️</div>
                                </div>
                                <div class="ml-5 w-0 flex-1">
                                    <dl>
                                        <dt class="text-sm font-medium text-gray-500 dark:text-gray-400 truncate">
                                            Waiters
                                        </dt>
                                        <dd class="text-lg font-medium text-gray-900 dark:text-gray-100">
                                            {{ stats.waiters }}
                                        </dd>
                                    </dl>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="bg-white dark:bg-gray-800 overflow-hidden shadow rounded-lg">
                        <div class="p-5">
                            <div class="flex items-center">
                                <div class="flex-shrink-0">
                                    <div class="text-2xl">👨‍🍳</div>
                                </div>
                                <div class="ml-5 w-0 flex-1">
                                    <dl>
                                        <dt class="text-sm font-medium text-gray-500 dark:text-gray-400 truncate">
                                            Chefs
                                        </dt>
                                        <dd class="text-lg font-medium text-gray-900 dark:text-gray-100">
                                            {{ stats.chefs }}
                                        </dd>
                                    </dl>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="bg-white dark:bg-gray-800 overflow-hidden shadow rounded-lg">
                        <div class="p-5">
                            <div class="flex items-center">
                                <div class="flex-shrink-0">
                                    <div class="text-2xl">🏍️</div>
                                </div>
                                <div class="ml-5 w-0 flex-1">
                                    <dl>
                                        <dt class="text-sm font-medium text-gray-500 dark:text-gray-400 truncate">
                                            Riders
                                        </dt>
                                        <dd class="text-lg font-medium text-gray-900 dark:text-gray-100">
                                            {{ stats.riders }}
                                        </dd>
                                    </dl>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="bg-white dark:bg-gray-800 overflow-hidden shadow rounded-lg">
                        <div class="p-5">
                            <div class="flex items-center">
                                <div class="flex-shrink-0">
                                    <div class="text-2xl">👔</div>
                                </div>
                                <div class="ml-5 w-0 flex-1">
                                    <dl>
                                        <dt class="text-sm font-medium text-gray-500 dark:text-gray-400 truncate">
                                            Managers
                                        </dt>
                                        <dd class="text-lg font-medium text-gray-900 dark:text-gray-100">
                                            {{ stats.managers }}
                                        </dd>
                                    </dl>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="bg-white dark:bg-gray-800 overflow-hidden shadow rounded-lg">
                        <div class="p-5">
                            <div class="flex items-center">
                                <div class="flex-shrink-0">
                                    <div class="text-2xl">✅</div>
                                </div>
                                <div class="ml-5 w-0 flex-1">
                                    <dl>
                                        <dt class="text-sm font-medium text-gray-500 dark:text-gray-400 truncate">
                                            Active
                                        </dt>
                                        <dd class="text-lg font-medium text-gray-900 dark:text-gray-100">
                                            {{ stats.active_staff }}
                                        </dd>
                                    </dl>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Staff Table -->
                <div class="bg-white dark:bg-gray-800 shadow overflow-hidden sm:rounded-md">
                    <div class="px-4 py-5 sm:p-6">
                        <div class="sm:flex sm:items-center">
                            <div class="sm:flex-auto">
                                <h1 class="text-xl font-semibold text-gray-900 dark:text-gray-100">Staff Members</h1>
                                <p class="mt-2 text-sm text-gray-700 dark:text-gray-300">
                                    A list of all staff members including their role, department, and status.
                                </p>
                            </div>
                        </div>

                        <!-- Filters -->
                        <div v-show="showFilters" class="mt-6 bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
                            <div class="grid grid-cols-1 md:grid-cols-5 gap-4">
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 dark:text-gray-300">Search</label>
                                    <input
                                        v-model="filterForm.search"
                                        @input="applyFilters"
                                        type="text"
                                        placeholder="Search staff..."
                                        class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
                                    >
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 dark:text-gray-300">Role</label>
                                    <select
                                        v-model="filterForm.role"
                                        @change="applyFilters"
                                        class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
                                    >
                                        <option value="">All Roles</option>
                                        <option v-for="role in roles" :key="role.value" :value="role.value">
                                            {{ role.label }}
                                        </option>
                                    </select>
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 dark:text-gray-300">Status</label>
                                    <select
                                        v-model="filterForm.status"
                                        @change="applyFilters"
                                        class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
                                    >
                                        <option value="">All Status</option>
                                        <option value="active">Active</option>
                                        <option value="inactive">Inactive</option>
                                        <option value="terminated">Terminated</option>
                                    </select>
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 dark:text-gray-300">Department</label>
                                    <select
                                        v-model="filterForm.department_id"
                                        @change="applyFilters"
                                        class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
                                    >
                                        <option value="">All Departments</option>
                                        <option v-for="department in departments" :key="department.id" :value="department.id">
                                            {{ department.name }}
                                        </option>
                                    </select>
                                </div>
                                <div class="flex items-end">
                                    <button
                                        @click="clearFilters"
                                        v-if="hasFilters"
                                        class="w-full px-4 py-2 bg-gray-600 text-white rounded-md hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-gray-500"
                                    >
                                        Clear Filters
                                    </button>
                                </div>
                            </div>
                        </div>

                        <!-- Staff List -->
                        <div class="mt-8 flow-root">
                            <div v-if="staff.data.length === 0" class="text-center py-12">
                                <div class="text-6xl mb-4">👨‍💼</div>
                                <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100 mb-2">
                                    No staff members found
                                </h3>
                                <p class="text-gray-600 dark:text-gray-400 mb-6">
                                    Get started by adding your first staff member.
                                </p>
                                <Link
                                    :href="route('staff.create')"
                                    class="inline-flex items-center px-4 py-2 bg-indigo-600 border border-transparent rounded-md font-semibold text-xs text-white uppercase tracking-widest hover:bg-indigo-700 focus:bg-indigo-700 active:bg-indigo-900 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 transition ease-in-out duration-150"
                                >
                                    ➕ Add First Staff Member
                                </Link>
                            </div>

                            <div v-else class="-mx-4 -my-2 overflow-x-auto sm:-mx-6 lg:-mx-8">
                                <div class="inline-block min-w-full py-2 align-middle sm:px-6 lg:px-8">
                                    <table class="min-w-full divide-y divide-gray-300 dark:divide-gray-600">
                                        <thead>
                                            <tr>
                                                <th scope="col" class="py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900 dark:text-gray-100 sm:pl-0">
                                                    Staff Member
                                                </th>
                                                <th scope="col" class="px-3 py-3.5 text-left text-sm font-semibold text-gray-900 dark:text-gray-100">
                                                    Role
                                                </th>
                                                <th scope="col" class="px-3 py-3.5 text-left text-sm font-semibold text-gray-900 dark:text-gray-100">
                                                    Branch
                                                </th>
                                                <th scope="col" class="px-3 py-3.5 text-left text-sm font-semibold text-gray-900 dark:text-gray-100">
                                                    Status
                                                </th>
                                                <th scope="col" class="px-3 py-3.5 text-left text-sm font-semibold text-gray-900 dark:text-gray-100">
                                                    Hire Date
                                                </th>
                                                <th scope="col" class="relative py-3.5 pl-3 pr-4 sm:pr-0">
                                                    <span class="sr-only">Actions</span>
                                                </th>
                                            </tr>
                                        </thead>
                                        <tbody class="divide-y divide-gray-200 dark:divide-gray-700">
                                            <tr v-for="member in staff.data" :key="member.id">
                                                <td class="whitespace-nowrap py-4 pl-4 pr-3 text-sm sm:pl-0">
                                                    <div class="flex items-center">
                                                        <div class="h-10 w-10 flex-shrink-0">
                                                            <div class="h-10 w-10 rounded-full bg-gray-300 flex items-center justify-center">
                                                                <span class="text-sm font-medium text-gray-700">
                                                                    {{ member.first_name?.charAt(0) }}{{ member.last_name?.charAt(0) }}
                                                                </span>
                                                            </div>
                                                        </div>
                                                        <div class="ml-4">
                                                            <div class="font-medium text-gray-900 dark:text-gray-100">
                                                                {{ member.display_name || member.name }}
                                                            </div>
                                                            <div class="text-gray-500 dark:text-gray-400">
                                                                {{ member.email }}
                                                            </div>
                                                            <div class="text-xs text-gray-400">
                                                                ID: {{ member.employee_id }}
                                                            </div>
                                                        </div>
                                                    </div>
                                                </td>
                                                <td class="whitespace-nowrap px-3 py-4 text-sm text-gray-500 dark:text-gray-400">
                                                    <span :class="getRoleColor(member.role)" class="inline-flex rounded-full px-2 text-xs font-semibold leading-5">
                                                        {{ member.role_label || member.role }}
                                                    </span>
                                                </td>
                                                <td class="whitespace-nowrap px-3 py-4 text-sm text-gray-500 dark:text-gray-400">
                                                    {{ member.primary_branch?.name || 'Not assigned' }}
                                                </td>
                                                <td class="whitespace-nowrap px-3 py-4 text-sm text-gray-500 dark:text-gray-400">
                                                    <span :class="getStatusColor(member.employment_status)" class="inline-flex rounded-full px-2 text-xs font-semibold leading-5">
                                                        {{ member.employment_status }}
                                                    </span>
                                                </td>
                                                <td class="whitespace-nowrap px-3 py-4 text-sm text-gray-500 dark:text-gray-400">
                                                    {{ new Date(member.hire_date).toLocaleDateString() }}
                                                </td>
                                                <td class="relative whitespace-nowrap py-4 pl-3 pr-4 text-right text-sm font-medium sm:pr-0">
                                                    <div class="relative inline-block text-left" data-dropdown>
                                                        <!-- Three dots button -->
                                                        <button
                                                            @click="toggleDropdown(member.id)"
                                                            class="inline-flex items-center justify-center w-8 h-8 text-gray-400 hover:text-gray-600 dark:text-gray-500 dark:hover:text-gray-300 rounded-full hover:bg-gray-100 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-indigo-500"
                                                        >
                                                            <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                                                                <path d="M10 6a2 2 0 110-4 2 2 0 010 4zM10 12a2 2 0 110-4 2 2 0 010 4zM10 18a2 2 0 110-4 2 2 0 010 4z" />
                                                            </svg>
                                                        </button>

                                                        <!-- Dropdown menu -->
                                                        <div
                                                            v-show="openDropdowns[member.id]"
                                                            class="absolute right-0 z-10 mt-2 w-48 origin-top-right rounded-md bg-white dark:bg-gray-800 shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none"
                                                        >
                                                            <div class="py-1">
                                                                <!-- View -->
                                                                <Link
                                                                    :href="route('staff.show', member.id)"
                                                                    class="flex items-center px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700"
                                                                    @click="closeAllDropdowns"
                                                                >
                                                                    <svg class="w-4 h-4 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                                                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                                                                    </svg>
                                                                    View Details
                                                                </Link>

                                                                <!-- Edit -->
                                                                <Link
                                                                    :href="route('staff.edit', member.id)"
                                                                    class="flex items-center px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700"
                                                                    @click="closeAllDropdowns"
                                                                >
                                                                    <svg class="w-4 h-4 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                                                                    </svg>
                                                                    Edit Staff
                                                                </Link>

                                                                <!-- Divider -->
                                                                <div class="border-t border-gray-100 dark:border-gray-700"></div>

                                                                <!-- Toggle Status -->
                                                                <button
                                                                    @click="toggleStatus(member)"
                                                                    class="flex items-center w-full px-4 py-2 text-sm text-left hover:bg-gray-100 dark:hover:bg-gray-700"
                                                                    :class="member.is_active ? 'text-yellow-600 dark:text-yellow-400' : 'text-green-600 dark:text-green-400'"
                                                                >
                                                                    <svg v-if="member.is_active" class="w-4 h-4 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M18.364 18.364A9 9 0 005.636 5.636m12.728 12.728L5.636 5.636m12.728 12.728L5.636 5.636" />
                                                                    </svg>
                                                                    <svg v-else class="w-4 h-4 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                                                                    </svg>
                                                                    {{ member.is_active ? 'Deactivate' : 'Activate' }}
                                                                </button>

                                                                <!-- Reset Password -->
                                                                <button
                                                                    @click="resetPassword(member)"
                                                                    class="flex items-center w-full px-4 py-2 text-sm text-left text-blue-600 dark:text-blue-400 hover:bg-gray-100 dark:hover:bg-gray-700"
                                                                >
                                                                    <svg class="w-4 h-4 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 7a2 2 0 012 2m4 0a6 6 0 01-7.743 5.743L11 17H9v2H7v2H4a1 1 0 01-1-1v-2.586a1 1 0 01.293-.707l5.964-5.964A6 6 0 1121 9z" />
                                                                    </svg>
                                                                    Reset Password
                                                                </button>

                                                                <!-- Divider -->
                                                                <div class="border-t border-gray-100 dark:border-gray-700"></div>

                                                                <!-- Terminate -->
                                                                <button
                                                                    @click="deleteStaff(member)"
                                                                    class="flex items-center w-full px-4 py-2 text-sm text-left text-red-600 dark:text-red-400 hover:bg-gray-100 dark:hover:bg-gray-700"
                                                                >
                                                                    <svg class="w-4 h-4 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                                                                    </svg>
                                                                    Terminate Staff
                                                                </button>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </td>
                                            </tr>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>

                        <!-- Pagination -->
                        <div v-if="staff.data.length > 0" class="mt-6">
                            <Pagination :links="staff.links" />
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </ManagerLayout>
</template>