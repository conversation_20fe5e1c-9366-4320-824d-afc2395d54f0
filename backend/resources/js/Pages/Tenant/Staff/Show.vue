<script setup>
import { Head, <PERSON> } from '@inertiajs/vue3';
import ManagerLayout from '@/Layouts/ManagerLayout.vue';
import { computed } from 'vue';

const props = defineProps({
    staff: Object,
});

// Computed properties
const fullName = computed(() => {
    if (props.staff?.first_name && props.staff?.last_name) {
        return `${props.staff.first_name} ${props.staff.last_name}`;
    }
    return props.staff?.name || 'Unknown';
});

const statusBadgeClass = computed(() => {
    return props.staff?.is_active 
        ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200'
        : 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200';
});

const roleBadgeClass = computed(() => {
    const roleColors = {
        'manager': 'bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200',
        'waiter': 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200',
        'chef': 'bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-200',
        'rider': 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200',
    };
    return roleColors[props.staff?.role] || 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200';
});

const formatDate = (date) => {
    if (!date) return 'N/A';
    return new Date(date).toLocaleDateString();
};

const formatCurrency = (amount) => {
    if (!amount) return 'N/A';
    return new Intl.NumberFormat('en-US', {
        style: 'currency',
        currency: 'USD'
    }).format(amount);
};
</script>

<template>
    <Head :title="`Staff: ${fullName}`" />

    <ManagerLayout :title="`Staff: ${fullName}`">
        <template #header>
            <div class="flex justify-between items-center">
                <div>
                    <h2 class="font-semibold text-xl text-gray-800 dark:text-gray-200 leading-tight">
                        👤 {{ fullName }}
                    </h2>
                    <p class="text-sm text-gray-600 dark:text-gray-400 mt-1">
                        Staff member details and information
                    </p>
                </div>

                <div class="flex space-x-3">
                    <Link
                        :href="route('staff.edit', staff?.id)"
                        class="inline-flex items-center px-4 py-2 bg-indigo-600 border border-transparent rounded-md font-semibold text-xs text-white uppercase tracking-widest hover:bg-indigo-700 focus:bg-indigo-700 active:bg-indigo-900 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 transition ease-in-out duration-150"
                    >
                        ✏️ Edit
                    </Link>
                    <Link
                        :href="route('staff.index')"
                        class="inline-flex items-center px-4 py-2 bg-gray-600 border border-transparent rounded-md font-semibold text-xs text-white uppercase tracking-widest hover:bg-gray-700 focus:bg-gray-700 active:bg-gray-900 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2 transition ease-in-out duration-150"
                    >
                        ← Back to Staff
                    </Link>
                </div>
            </div>
        </template>

        <div class="py-12">
            <div class="max-w-4xl mx-auto sm:px-6 lg:px-8 space-y-6">
                
                <!-- Basic Information -->
                <div class="bg-white dark:bg-gray-800 shadow-sm rounded-lg">
                    <div class="p-6 border-b border-gray-200 dark:border-gray-700">
                        <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100">
                            👤 Basic Information
                        </h3>
                    </div>
                    <div class="p-6">
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <div>
                                <label class="block text-sm font-medium text-gray-500 dark:text-gray-400">
                                    Full Name
                                </label>
                                <p class="mt-1 text-sm text-gray-900 dark:text-gray-100">
                                    {{ fullName }}
                                </p>
                            </div>

                            <div>
                                <label class="block text-sm font-medium text-gray-500 dark:text-gray-400">
                                    Email
                                </label>
                                <p class="mt-1 text-sm text-gray-900 dark:text-gray-100">
                                    {{ staff?.email || staff?.user?.email || 'N/A' }}
                                </p>
                            </div>

                            <div>
                                <label class="block text-sm font-medium text-gray-500 dark:text-gray-400">
                                    Phone
                                </label>
                                <p class="mt-1 text-sm text-gray-900 dark:text-gray-100">
                                    {{ staff?.phone || staff?.user?.phone || 'N/A' }}
                                </p>
                            </div>

                            <div>
                                <label class="block text-sm font-medium text-gray-500 dark:text-gray-400">
                                    Status
                                </label>
                                <span class="mt-1 inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium" :class="statusBadgeClass">
                                    {{ staff?.is_active ? 'Active' : 'Inactive' }}
                                </span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Role & Assignment -->
                <div class="bg-white dark:bg-gray-800 shadow-sm rounded-lg">
                    <div class="p-6 border-b border-gray-200 dark:border-gray-700">
                        <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100">
                            🎭 Role & Assignment
                        </h3>
                    </div>
                    <div class="p-6">
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <div>
                                <label class="block text-sm font-medium text-gray-500 dark:text-gray-400">
                                    Role
                                </label>
                                <span class="mt-1 inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium capitalize" :class="roleBadgeClass">
                                    {{ staff?.role || staff?.user?.role || 'N/A' }}
                                </span>
                            </div>

                            <div>
                                <label class="block text-sm font-medium text-gray-500 dark:text-gray-400">
                                    Position
                                </label>
                                <p class="mt-1 text-sm text-gray-900 dark:text-gray-100">
                                    {{ staff?.position || 'N/A' }}
                                </p>
                            </div>

                            <div>
                                <label class="block text-sm font-medium text-gray-500 dark:text-gray-400">
                                    Department
                                </label>
                                <p class="mt-1 text-sm text-gray-900 dark:text-gray-100">
                                    {{ staff?.department?.name || 'N/A' }}
                                </p>
                            </div>

                            <div>
                                <label class="block text-sm font-medium text-gray-500 dark:text-gray-400">
                                    Primary Branch
                                </label>
                                <p class="mt-1 text-sm text-gray-900 dark:text-gray-100">
                                    {{ staff?.primaryBranch?.name || staff?.primary_branch?.name || 'N/A' }}
                                </p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Employment Details -->
                <div class="bg-white dark:bg-gray-800 shadow-sm rounded-lg">
                    <div class="p-6 border-b border-gray-200 dark:border-gray-700">
                        <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100">
                            💼 Employment Details
                        </h3>
                    </div>
                    <div class="p-6">
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <div>
                                <label class="block text-sm font-medium text-gray-500 dark:text-gray-400">
                                    Hire Date
                                </label>
                                <p class="mt-1 text-sm text-gray-900 dark:text-gray-100">
                                    {{ formatDate(staff?.hire_date) }}
                                </p>
                            </div>

                            <div>
                                <label class="block text-sm font-medium text-gray-500 dark:text-gray-400">
                                    Employment Status
                                </label>
                                <p class="mt-1 text-sm text-gray-900 dark:text-gray-100 capitalize">
                                    {{ staff?.employment_status || 'N/A' }}
                                </p>
                            </div>

                            <div v-if="staff?.salary">
                                <label class="block text-sm font-medium text-gray-500 dark:text-gray-400">
                                    Salary
                                </label>
                                <p class="mt-1 text-sm text-gray-900 dark:text-gray-100">
                                    {{ formatCurrency(staff.salary) }}
                                </p>
                            </div>

                            <div v-if="staff?.hourly_rate">
                                <label class="block text-sm font-medium text-gray-500 dark:text-gray-400">
                                    Hourly Rate
                                </label>
                                <p class="mt-1 text-sm text-gray-900 dark:text-gray-100">
                                    {{ formatCurrency(staff.hourly_rate) }}/hour
                                </p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Contact Information -->
                <div class="bg-white dark:bg-gray-800 shadow-sm rounded-lg" v-if="staff?.address || staff?.emergency_contact_name">
                    <div class="p-6 border-b border-gray-200 dark:border-gray-700">
                        <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100">
                            📞 Contact Information
                        </h3>
                    </div>
                    <div class="p-6">
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <div v-if="staff?.address">
                                <label class="block text-sm font-medium text-gray-500 dark:text-gray-400">
                                    Address
                                </label>
                                <p class="mt-1 text-sm text-gray-900 dark:text-gray-100">
                                    {{ staff.address }}
                                </p>
                            </div>

                            <div v-if="staff?.emergency_contact_name">
                                <label class="block text-sm font-medium text-gray-500 dark:text-gray-400">
                                    Emergency Contact
                                </label>
                                <p class="mt-1 text-sm text-gray-900 dark:text-gray-100">
                                    {{ staff.emergency_contact_name }}
                                    <span v-if="staff?.emergency_contact_phone" class="text-gray-500">
                                        ({{ staff.emergency_contact_phone }})
                                    </span>
                                </p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Notes -->
                <div class="bg-white dark:bg-gray-800 shadow-sm rounded-lg" v-if="staff?.notes">
                    <div class="p-6 border-b border-gray-200 dark:border-gray-700">
                        <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100">
                            📝 Notes
                        </h3>
                    </div>
                    <div class="p-6">
                        <p class="text-sm text-gray-900 dark:text-gray-100">
                            {{ staff.notes }}
                        </p>
                    </div>
                </div>

                <!-- Recent Shifts (if available) -->
                <div class="bg-white dark:bg-gray-800 shadow-sm rounded-lg" v-if="staff?.employeeShifts && staff.employeeShifts.length">
                    <div class="p-6 border-b border-gray-200 dark:border-gray-700">
                        <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100">
                            🕐 Recent Shifts
                        </h3>
                    </div>
                    <div class="p-6">
                        <div class="space-y-3">
                            <div 
                                v-for="shift in staff.employeeShifts.slice(0, 5)" 
                                :key="shift.id"
                                class="flex justify-between items-center py-2 border-b border-gray-200 dark:border-gray-700 last:border-b-0"
                            >
                                <div>
                                    <p class="text-sm font-medium text-gray-900 dark:text-gray-100">
                                        {{ formatDate(shift.shift_date) }}
                                    </p>
                                    <p class="text-xs text-gray-500 dark:text-gray-400">
                                        {{ shift.start_time }} - {{ shift.end_time }}
                                    </p>
                                </div>
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200 capitalize">
                                    {{ shift.status }}
                                </span>
                            </div>
                        </div>
                    </div>
                </div>

            </div>
        </div>
    </ManagerLayout>
</template>
