<script setup>
import { Head, Link, useForm } from '@inertiajs/vue3';
import ManagerLayout from '@/Layouts/ManagerLayout.vue';
import { ref, computed, onMounted } from 'vue';

const props = defineProps({
    staff: Object,
    departments: Array,
    branches: Array,
    roles: Array,
});

// Form data - initialize with staff data
const form = useForm({
    first_name: '',
    last_name: '',
    email: '',
    password: '',
    password_confirmation: '',
    phone: '',
    role: 'waiter',
    department_id: '',
    primary_branch_id: '',
    position: '',
    hire_date: '',
    salary: '',
    hourly_rate: '',
    address: '',
    emergency_contact_name: '',
    emergency_contact_phone: '',
    notes: '',
    is_active: true,
});

// Initialize form with staff data
onMounted(() => {
    if (props.staff) {
        form.first_name = props.staff.first_name || '';
        form.last_name = props.staff.last_name || '';
        // Email, phone, role, address, emergency contacts might be in user relationship
        form.email = props.staff.email || props.staff.user?.email || '';
        form.phone = props.staff.phone || props.staff.user?.phone || '';
        form.role = props.staff.role || props.staff.user?.role || 'waiter';
        form.address = props.staff.address || props.staff.user?.address || '';
        form.emergency_contact_name = props.staff.emergency_contact_name || props.staff.user?.emergency_contact_name || '';
        form.emergency_contact_phone = props.staff.emergency_contact_phone || props.staff.user?.emergency_contact_phone || '';

        form.department_id = props.staff.department_id || '';
        form.primary_branch_id = props.staff.primary_branch_id || '';
        form.position = props.staff.position || '';
        form.hire_date = props.staff.hire_date || '';
        form.salary = props.staff.salary || '';
        form.hourly_rate = props.staff.hourly_rate || '';
        form.notes = props.staff.notes || '';
        form.is_active = props.staff.is_active !== undefined ? props.staff.is_active : true;
    }
});

// Computed properties
const selectedRole = computed(() => {
    return props.roles?.find(role => role.value === form.role);
});

// Methods
const submit = () => {
    form.put(route('staff.update', props.staff.id), {
        onSuccess: () => {
            // Success handled by redirect
        },
    });
};
</script>

<template>
    <Head title="Edit Staff Member" />

    <ManagerLayout title="Edit Staff Member">
        <template #header>
            <div class="flex justify-between items-center">
                <div>
                    <h2 class="font-semibold text-xl text-gray-800 dark:text-gray-200 leading-tight">
                        ✏️ Edit Staff Member: {{ staff?.name || staff?.first_name + ' ' + staff?.last_name }}
                    </h2>
                    <p class="text-sm text-gray-600 dark:text-gray-400 mt-1">
                        Update staff member information and role-based access
                    </p>
                </div>

                <div class="flex space-x-3">
                    <Link
                        :href="route('staff.show', staff?.id)"
                        class="inline-flex items-center px-4 py-2 bg-blue-600 border border-transparent rounded-md font-semibold text-xs text-white uppercase tracking-widest hover:bg-blue-700 focus:bg-blue-700 active:bg-blue-900 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition ease-in-out duration-150"
                    >
                        👁 View
                    </Link>
                    <Link
                        :href="route('staff.index')"
                        class="inline-flex items-center px-4 py-2 bg-gray-600 border border-transparent rounded-md font-semibold text-xs text-white uppercase tracking-widest hover:bg-gray-700 focus:bg-gray-700 active:bg-gray-900 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2 transition ease-in-out duration-150"
                    >
                        ← Back to Staff
                    </Link>
                </div>
            </div>
        </template>

        <div class="py-12">
            <div class="max-w-4xl mx-auto sm:px-6 lg:px-8">
                <form @submit.prevent="submit" class="space-y-6">
                    
                    <!-- Basic Information -->
                    <div class="bg-white dark:bg-gray-800 shadow-sm rounded-lg">
                        <div class="p-6 border-b border-gray-200 dark:border-gray-700">
                            <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100">
                                👤 Basic Information
                            </h3>
                        </div>
                        <div class="p-6">
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                                <!-- First Name -->
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                                        First Name *
                                    </label>
                                    <input
                                        v-model="form.first_name"
                                        type="text"
                                        required
                                        class="w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
                                        :class="{ 'border-red-500': form.errors.first_name }"
                                    >
                                    <div v-if="form.errors.first_name" class="mt-1 text-sm text-red-600">
                                        {{ form.errors.first_name }}
                                    </div>
                                </div>

                                <!-- Last Name -->
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                                        Last Name *
                                    </label>
                                    <input
                                        v-model="form.last_name"
                                        type="text"
                                        required
                                        class="w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
                                        :class="{ 'border-red-500': form.errors.last_name }"
                                    >
                                    <div v-if="form.errors.last_name" class="mt-1 text-sm text-red-600">
                                        {{ form.errors.last_name }}
                                    </div>
                                </div>

                                <!-- Email -->
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                                        Email *
                                    </label>
                                    <input
                                        v-model="form.email"
                                        type="email"
                                        required
                                        class="w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
                                        :class="{ 'border-red-500': form.errors.email }"
                                    >
                                    <div v-if="form.errors.email" class="mt-1 text-sm text-red-600">
                                        {{ form.errors.email }}
                                    </div>
                                </div>

                                <!-- Phone -->
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                                        Phone
                                    </label>
                                    <input
                                        v-model="form.phone"
                                        type="tel"
                                        class="w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
                                        :class="{ 'border-red-500': form.errors.phone }"
                                    >
                                    <div v-if="form.errors.phone" class="mt-1 text-sm text-red-600">
                                        {{ form.errors.phone }}
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Authentication (Optional for Edit) -->
                    <div class="bg-white dark:bg-gray-800 shadow-sm rounded-lg">
                        <div class="p-6 border-b border-gray-200 dark:border-gray-700">
                            <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100">
                                🔐 Change Password (Optional)
                            </h3>
                            <p class="text-sm text-gray-600 dark:text-gray-400 mt-1">
                                Leave blank to keep current password
                            </p>
                        </div>
                        <div class="p-6">
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                                <!-- Password -->
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                                        New Password
                                    </label>
                                    <input
                                        v-model="form.password"
                                        type="password"
                                        class="w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
                                        :class="{ 'border-red-500': form.errors.password }"
                                    >
                                    <div v-if="form.errors.password" class="mt-1 text-sm text-red-600">
                                        {{ form.errors.password }}
                                    </div>
                                </div>

                                <!-- Confirm Password -->
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                                        Confirm New Password
                                    </label>
                                    <input
                                        v-model="form.password_confirmation"
                                        type="password"
                                        class="w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
                                        :class="{ 'border-red-500': form.errors.password_confirmation }"
                                    >
                                    <div v-if="form.errors.password_confirmation" class="mt-1 text-sm text-red-600">
                                        {{ form.errors.password_confirmation }}
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Role & Assignment -->
                    <div class="bg-white dark:bg-gray-800 shadow-sm rounded-lg">
                        <div class="p-6 border-b border-gray-200 dark:border-gray-700">
                            <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100">
                                🎭 Role & Assignment
                            </h3>
                        </div>
                        <div class="p-6">
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                                <!-- Role -->
                                <div class="md:col-span-2" v-if="roles && roles.length">
                                    <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                                        Role *
                                    </label>
                                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                                        <label
                                            v-for="role in roles"
                                            :key="role.value"
                                            class="relative flex cursor-pointer rounded-lg border p-4 focus:outline-none"
                                            :class="{
                                                'border-indigo-600 ring-2 ring-indigo-600': form.role === role.value,
                                                'border-gray-300': form.role !== role.value
                                            }"
                                        >
                                            <input
                                                v-model="form.role"
                                                :value="role.value"
                                                type="radio"
                                                class="sr-only"
                                            >
                                            <span class="flex flex-1">
                                                <span class="flex flex-col">
                                                    <span class="block text-sm font-medium text-gray-900 dark:text-gray-100">
                                                        {{ role.label }}
                                                    </span>
                                                    <span class="mt-1 flex items-center text-sm text-gray-500 dark:text-gray-400">
                                                        {{ role.description }}
                                                    </span>
                                                </span>
                                            </span>
                                        </label>
                                    </div>
                                    <div v-if="form.errors.role" class="mt-1 text-sm text-red-600">
                                        {{ form.errors.role }}
                                    </div>
                                </div>

                                <!-- Primary Branch -->
                                <div v-if="branches && branches.length">
                                    <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                                        Primary Branch *
                                    </label>
                                    <select
                                        v-model="form.primary_branch_id"
                                        required
                                        class="w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
                                        :class="{ 'border-red-500': form.errors.primary_branch_id }"
                                    >
                                        <option value="">Select a branch</option>
                                        <option v-for="branch in branches" :key="branch.id" :value="branch.id">
                                            {{ branch.name }}
                                        </option>
                                    </select>
                                    <div v-if="form.errors.primary_branch_id" class="mt-1 text-sm text-red-600">
                                        {{ form.errors.primary_branch_id }}
                                    </div>
                                </div>

                                <!-- Department -->
                                <div v-if="departments && departments.length">
                                    <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                                        Department
                                    </label>
                                    <select
                                        v-model="form.department_id"
                                        class="w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
                                        :class="{ 'border-red-500': form.errors.department_id }"
                                    >
                                        <option value="">Select a department</option>
                                        <option v-for="department in departments" :key="department.id" :value="department.id">
                                            {{ department.name }}
                                        </option>
                                    </select>
                                    <div v-if="form.errors.department_id" class="mt-1 text-sm text-red-600">
                                        {{ form.errors.department_id }}
                                    </div>
                                </div>

                                <!-- Position -->
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                                        Position *
                                    </label>
                                    <input
                                        v-model="form.position"
                                        type="text"
                                        required
                                        placeholder="e.g., Senior Waiter, Head Chef"
                                        class="w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
                                        :class="{ 'border-red-500': form.errors.position }"
                                    >
                                    <div v-if="form.errors.position" class="mt-1 text-sm text-red-600">
                                        {{ form.errors.position }}
                                    </div>
                                </div>

                                <!-- Hire Date -->
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                                        Hire Date *
                                    </label>
                                    <input
                                        v-model="form.hire_date"
                                        type="date"
                                        required
                                        class="w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
                                        :class="{ 'border-red-500': form.errors.hire_date }"
                                    >
                                    <div v-if="form.errors.hire_date" class="mt-1 text-sm text-red-600">
                                        {{ form.errors.hire_date }}
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Status -->
                    <div class="bg-white dark:bg-gray-800 shadow-sm rounded-lg">
                        <div class="p-6 border-b border-gray-200 dark:border-gray-700">
                            <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100">
                                📊 Status
                            </h3>
                        </div>
                        <div class="p-6">
                            <div class="flex items-center">
                                <input
                                    v-model="form.is_active"
                                    type="checkbox"
                                    class="rounded border-gray-300 text-indigo-600 shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
                                >
                                <label class="ml-2 block text-sm text-gray-700 dark:text-gray-300">
                                    Active Employee
                                </label>
                            </div>
                            <p class="text-sm text-gray-500 dark:text-gray-400 mt-1">
                                Inactive employees cannot access the system
                            </p>
                        </div>
                    </div>

                    <!-- Form Actions -->
                    <div class="flex justify-end space-x-3">
                        <Link
                            :href="route('staff.index')"
                            class="inline-flex items-center px-4 py-2 bg-gray-300 border border-transparent rounded-md font-semibold text-xs text-gray-700 uppercase tracking-widest hover:bg-gray-400 focus:bg-gray-400 active:bg-gray-500 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2 transition ease-in-out duration-150"
                        >
                            Cancel
                        </Link>
                        <button
                            type="submit"
                            :disabled="form.processing"
                            class="inline-flex items-center px-4 py-2 bg-indigo-600 border border-transparent rounded-md font-semibold text-xs text-white uppercase tracking-widest hover:bg-indigo-700 focus:bg-indigo-700 active:bg-indigo-900 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 transition ease-in-out duration-150 disabled:opacity-50"
                        >
                            <span v-if="form.processing" class="mr-2">
                                <svg class="animate-spin h-4 w-4" fill="none" viewBox="0 0 24 24">
                                    <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                                    <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                                </svg>
                            </span>
                            {{ form.processing ? 'Updating...' : 'Update Staff Member' }}
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </ManagerLayout>
</template>
