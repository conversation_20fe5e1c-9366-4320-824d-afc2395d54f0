<template>
    <div class="min-h-screen bg-gray-50 dark:bg-gray-900">
        <Head title="Menu" />

        <!-- Header -->
        <div class="bg-white dark:bg-gray-800 shadow-sm">
            <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
                <div class="flex items-center justify-between">
                    <div>
                        <h1 class="text-3xl font-bold text-gray-900 dark:text-white">Our Menu</h1>
                        <p class="mt-2 text-gray-600 dark:text-gray-400">
                            Discover our delicious menu items and combo deals
                        </p>
                    </div>
                    <div class="text-right">
                        <p class="text-sm text-gray-500 dark:text-gray-400">
                            {{ items.length }} items available
                        </p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Filters -->
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
            <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm p-6">
                <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <!-- Search -->
                    <div class="md:col-span-2">
                        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                            Search Menu
                        </label>
                        <input
                            v-model="filters.search"
                            @input="search"
                            type="text"
                            placeholder="Search for dishes, combos..."
                            class="w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
                        />
                    </div>

                    <!-- Category Filter -->
                    <div>
                        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                            Category
                        </label>
                        <select
                            v-model="filters.category_id"
                            @change="search"
                            class="w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
                        >
                            <option value="">All Categories</option>
                            <option
                                v-for="category in categories"
                                :key="category.id"
                                :value="category.id"
                            >
                                {{ category.name }}
                            </option>
                        </select>
                    </div>
                </div>
            </div>
        </div>

        <!-- Menu Items Grid -->
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 pb-12">
            <div v-if="filteredItems.length === 0" class="text-center py-12">
                <div class="text-6xl mb-4">🍽️</div>
                <h3 class="text-xl font-medium text-gray-900 dark:text-white mb-2">
                    No items found
                </h3>
                <p class="text-gray-600 dark:text-gray-400">
                    Try adjusting your search or category filter
                </p>
            </div>

            <div v-else class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
                <div
                    v-for="item in filteredItems"
                    :key="`${item.is_combo ? 'combo' : 'item'}-${item.id}`"
                    class="bg-white dark:bg-gray-800 rounded-lg shadow-md overflow-hidden hover:shadow-lg transition-shadow duration-300"
                >
                    <!-- Item Image -->
                    <div class="h-48 bg-gray-200 dark:bg-gray-700 flex items-center justify-center relative">
                        <img
                            v-if="item.primary_image_url || item.image_url"
                            :src="item.primary_image_url || item.image_url"
                            :alt="item.name"
                            class="w-full h-full object-cover"
                            @error="handleImageError"
                        />
                        <div v-else class="text-4xl">🍽️</div>
                        
                        <!-- Combo Badge -->
                        <div
                            v-if="item.is_combo"
                            class="absolute top-2 left-2 bg-indigo-600 text-white px-2 py-1 rounded-full text-xs font-medium"
                        >
                            Combo Deal
                        </div>
                        
                        <!-- Savings Badge -->
                        <div
                            v-if="item.is_combo && item.savings > 0"
                            class="absolute top-2 right-2 bg-green-600 text-white px-2 py-1 rounded-full text-xs font-medium"
                        >
                            Save ${{ item.savings.toFixed(2) }}
                        </div>
                    </div>

                    <!-- Item Details -->
                    <div class="p-4">
                        <div class="flex items-start justify-between mb-2">
                            <h3 class="text-lg font-semibold text-gray-900 dark:text-white line-clamp-1">
                                {{ item.name }}
                            </h3>
                        </div>

                        <p v-if="item.description" class="text-gray-600 dark:text-gray-400 text-sm mb-3 line-clamp-2">
                            {{ item.description }}
                        </p>

                        <!-- Combo Components -->
                        <div v-if="item.is_combo && item.components" class="mb-3">
                            <h4 class="text-sm font-medium text-gray-700 dark:text-gray-300 mb-3">Includes:</h4>
                            <div class="grid grid-cols-2 gap-2">
                                <div
                                    v-for="component in item.components.slice(0, 4)"
                                    :key="component.id"
                                    class="flex items-center space-x-2"
                                >
                                    <!-- Circular Image -->
                                    <div class="w-8 h-8 rounded-full bg-gray-200 dark:bg-gray-600 flex items-center justify-center flex-shrink-0 overflow-hidden">
                                        <img
                                            v-if="component.menu_item.primary_image_url || component.menu_item.image_url"
                                            :src="component.menu_item.primary_image_url || component.menu_item.image_url"
                                            :alt="component.menu_item.name"
                                            class="w-full h-full object-cover"
                                            @error="handleComponentImageError"
                                        />
                                        <span v-else class="text-xs">🍽️</span>
                                    </div>

                                    <!-- Name and Quantity -->
                                    <div class="flex-1 min-w-0">
                                        <div class="text-xs font-medium text-gray-700 dark:text-gray-300 truncate">
                                            {{ component.menu_item.name }}
                                        </div>
                                        <div class="text-xs text-gray-500 dark:text-gray-400">
                                            {{ getComponentQuantity(item.components, component.menu_item.id, component.component_type) }}x {{ component.component_type }}
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Show more indicator -->
                            <div
                                v-if="item.components.length > 4"
                                class="mt-2 text-center"
                            >
                                <span class="text-xs text-indigo-600 dark:text-indigo-400 font-medium">
                                    +{{ item.components.length - 4 }} more items
                                </span>
                            </div>
                        </div>

                        <!-- Category (for regular items) -->
                        <div v-if="!item.is_combo && item.category" class="mb-3">
                            <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-200">
                                {{ item.category.name }}
                            </span>
                        </div>

                        <!-- Price -->
                        <div class="flex items-center justify-between">
                            <div class="flex flex-col">
                                <span class="text-xl font-bold text-indigo-600 dark:text-indigo-400">
                                    ${{ item.price }}
                                </span>
                                <span
                                    v-if="item.is_combo && item.individual_total"
                                    class="text-sm text-gray-500 line-through"
                                >
                                    ${{ item.individual_total.toFixed(2) }}
                                </span>
                            </div>
                            <button
                                @click="addToCart(item)"
                                class="px-4 py-2 bg-indigo-600 text-white rounded-md hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 transition-colors duration-200"
                            >
                                Add to Cart
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script setup>
import { Head, router } from '@inertiajs/vue3'
import { ref, reactive, computed } from 'vue'

const props = defineProps({
    items: Array,
    categories: Array,
    filters: Object,
})

const filters = reactive({
    search: props.filters.search || '',
    category_id: props.filters.category_id || '',
})

const filteredItems = computed(() => {
    let filtered = props.items

    if (filters.search) {
        const searchTerm = filters.search.toLowerCase()
        filtered = filtered.filter(item => 
            item.name.toLowerCase().includes(searchTerm) ||
            (item.description && item.description.toLowerCase().includes(searchTerm))
        )
    }

    if (filters.category_id) {
        filtered = filtered.filter(item => {
            if (item.is_combo) return true // Show all combos regardless of category filter
            return item.category && item.category.id == filters.category_id
        })
    }

    return filtered
})

const search = () => {
    router.get(route('menu.public'), filters, {
        preserveState: true,
        replace: true,
    })
}

const addToCart = (item) => {
    // TODO: Implement cart functionality
    alert(`Added "${item.name}" to cart! (Cart functionality to be implemented)`)
}

// Helper function to calculate component quantity
const getComponentQuantity = (components, menuItemId, componentType) => {
    return components.filter(comp =>
        comp.menu_item.id === menuItemId && comp.component_type === componentType
    ).length
}

const handleImageError = (event) => {
    if (event.target.dataset.errorHandled) return
    event.target.dataset.errorHandled = 'true'

    // Use inline SVG placeholder
    const svgPlaceholder = 'data:image/svg+xml;base64,' + btoa(`
        <svg xmlns="http://www.w3.org/2000/svg" width="200" height="200" viewBox="0 0 200 200">
            <rect width="200" height="200" fill="#f3f4f6"/>
            <circle cx="80" cy="70" r="12" fill="#d1d5db"/>
            <polygon points="40,140 80,100 120,120 160,80 180,140" fill="#d1d5db"/>
            <text x="100" y="170" text-anchor="middle" fill="#9ca3af" font-size="16">Menu Item</text>
        </svg>
    `)

    event.target.src = svgPlaceholder
}

const handleComponentImageError = (event) => {
    if (event.target.dataset.errorHandled) return
    event.target.dataset.errorHandled = 'true'

    // Use inline SVG placeholder for small circular images
    const svgPlaceholder = 'data:image/svg+xml;base64,' + btoa(`
        <svg xmlns="http://www.w3.org/2000/svg" width="32" height="32" viewBox="0 0 32 32">
            <rect width="32" height="32" fill="#f3f4f6" rx="16"/>
            <circle cx="12" cy="12" r="2" fill="#d1d5db"/>
            <polygon points="6,22 12,16 18,18 24,14 26,22" fill="#d1d5db"/>
        </svg>
    `)

    event.target.src = svgPlaceholder
}
</script>

<style scoped>
.line-clamp-1 {
    display: -webkit-box;
    -webkit-line-clamp: 1;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.line-clamp-2 {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}
</style>
