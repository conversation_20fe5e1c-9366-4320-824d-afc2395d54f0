<script setup>
import { Head, router } from '@inertiajs/vue3';
import { ref, computed } from 'vue';
import axios from 'axios';
import POSLayout from '@/Layouts/POSLayout.vue';
import BranchSelector from '@/Components/POS/BranchSelector.vue';
import { formatPrice } from '@/utils/currency';

const props = defineProps({
    branch: Object,
    allBranches: {
        type: Array,
        default: () => []
    },
    selectedBranchId: Number,
    orders: Array,
});

// Reactive state
const searchQuery = ref('');
const selectedStatus = ref('all');

// Computed properties
const filteredOrders = computed(() => {
    let orders = props.orders || [];
    
    // Filter by status
    if (selectedStatus.value !== 'all') {
        orders = orders.filter(order => order.status === selectedStatus.value);
    }
    
    // Filter by search query
    if (searchQuery.value) {
        orders = orders.filter(order => 
            order.order_number.toLowerCase().includes(searchQuery.value.toLowerCase()) ||
            order.customer_name?.toLowerCase().includes(searchQuery.value.toLowerCase()) ||
            order.table?.name.toLowerCase().includes(searchQuery.value.toLowerCase())
        );
    }
    
    return orders;
});

const orderStatuses = [
    { value: 'all', label: 'All Orders', color: 'gray' },
    { value: 'pending', label: 'Pending', color: 'yellow' },
    { value: 'confirmed', label: 'Confirmed', color: 'blue' },
    { value: 'preparing', label: 'Preparing', color: 'orange' },
    { value: 'ready', label: 'Ready', color: 'green' },
    { value: 'served', label: 'Served', color: 'purple' },
];

// Methods
const getStatusColor = (status) => {
    const statusConfig = orderStatuses.find(s => s.value === status);
    return statusConfig?.color || 'gray';
};

const formatTime = (dateString) => {
    if (!dateString) return '';
    const date = new Date(dateString);
    return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
};

// formatPrice is now imported from currency utils

const getRunningTime = (createdAt) => {
    if (!createdAt) return '';
    const now = new Date();
    const created = new Date(createdAt);
    const diffInMinutes = Math.floor((now - created) / (1000 * 60));
    
    if (diffInMinutes < 60) {
        return `${diffInMinutes} min`;
    } else {
        const hours = Math.floor(diffInMinutes / 60);
        const minutes = diffInMinutes % 60;
        return `${hours}h ${minutes}m`;
    }
};

const viewOrderDetails = (order) => {
    router.visit(route('pos.orders.show', order.id));
};

const updateOrderStatus = async (order, newStatus) => {
    try {
        await axios.put(route('pos.orders.update-status', order.id), {
            status: newStatus
        });

        // If status is 'served', navigate to order details page after updating
        if (newStatus === 'served') {
            router.visit(route('pos.orders.show', order.id));
        } else {
            // Refresh the page to get updated data for other statuses
            router.reload();
        }
    } catch (error) {
        console.error('Failed to update order status:', error);
        alert('Failed to update order status. Please try again.');
    }
};

const completeOrder = (order) => {
    // Navigate to order show page for payment and completion
    router.visit(route('pos.orders.show', order.id));
};

const getNextStatuses = (currentStatus) => {
    const statusFlow = {
        'pending': ['confirmed'],
        'confirmed': ['preparing'],
        'preparing': ['ready'],
        'ready': ['served'],
        'served': []
    };
    return statusFlow[currentStatus] || [];
};

const getStatusLabel = (status) => {
    const labels = {
        'pending': 'Pending',
        'confirmed': 'Confirmed',
        'preparing': 'Preparing',
        'ready': 'Ready',
        'served': 'Served'
    };
    return labels[status] || status;
};

const editOrder = (order) => {
    router.visit(route('pos.orders.edit', order.id));
};

const deleteOrder = async (order) => {
    if (confirm(`Are you sure you want to delete order ${order.order_number}?`)) {
        try {
            await axios.delete(route('pos.orders.delete', order.id));
            router.reload();
        } catch (error) {
            console.error('Failed to delete order:', error);
            alert('Failed to delete order. Please try again.');
        }
    }
};
</script>

<template>
    <div class="min-h-screen bg-gray-100">
        <Head :title="`Ongoing Orders - ${branch.name}`" />

        <!-- Single Modern Navigation Header -->
        <nav class="bg-white shadow-sm border-b border-gray-200">
            <div class="px-6 py-4">
                <div class="flex items-center justify-between">
                    <!-- Navigation Tabs -->
                    <div class="flex space-x-1">
                        <button
                            @click="router.visit(route('dashboard'))"
                            class="px-4 py-2 text-sm font-medium text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded-lg transition-colors flex items-center space-x-2"
                        >
                            <span class="text-green-600">🏠</span>
                            <span>Home</span>
                        </button>
                        
                        <button
                            @click="router.visit(route('pos.index'))"
                            class="px-4 py-2 text-sm font-medium text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded-lg transition-colors flex items-center space-x-2"
                        >
                            <span>📝</span>
                            <span>New Order</span>
                        </button>
                        
                        <button
                            class="px-4 py-2 text-sm font-medium bg-blue-600 text-white rounded-lg flex items-center space-x-2"
                        >
                            <span>🔄</span>
                            <span>On Going Order</span>
                        </button>
                        
                        <button
                            @click="router.visit(route('kitchen.index'))"
                            class="px-4 py-2 text-sm font-medium text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded-lg transition-colors flex items-center space-x-2"
                        >
                            <span class="text-purple-600">👨‍🍳</span>
                            <span>Kitchen Status</span>
                        </button>
                        
                        <button
                            class="px-4 py-2 text-sm font-medium text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded-lg transition-colors flex items-center space-x-2"
                        >
                            <span class="text-orange-600">📱</span>
                            <span>QR Order</span>
                        </button>
                        
                        <button
                            class="px-4 py-2 text-sm font-medium text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded-lg transition-colors flex items-center space-x-2"
                        >
                            <span class="text-yellow-600">➕</span>
                            <span>Create Order</span>
                        </button>
                        
                        <button
                            class="px-4 py-2 text-sm font-medium text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded-lg transition-colors flex items-center space-x-2"
                        >
                            <span class="text-blue-600">📅</span>
                            <span>Today Order</span>
                        </button>
                    </div>
                    
                    <!-- Right side controls -->
                    <div class="flex items-center space-x-4">
                        <div class="flex items-center space-x-2 text-sm text-gray-600">
                            <span>🌐</span>
                            <span>ENG</span>
                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                            </svg>
                        </div>
                        
                        <button class="p-2 text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded-lg transition-colors">
                            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"></path>
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                            </svg>
                        </button>
                        
                        <button class="p-2 text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded-lg transition-colors">
                            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"></path>
                            </svg>
                        </button>
                    </div>
                </div>
            </div>
        </nav>

        <!-- Main Content -->
        <div class="max-w-full mx-auto py-6 px-4 sm:px-6 lg:px-8">
            <!-- Branch Selection Header -->
            <div class="bg-white rounded-lg shadow-sm p-6 mb-6">
                <div class="flex items-center justify-between">
                    <div class="flex items-center space-x-6">
                        <div class="flex items-center space-x-3">
                            <div class="w-10 h-10 bg-blue-600 rounded-lg flex items-center justify-center">
                                <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"></path>
                                </svg>
                            </div>
                            <div>
                                <h3 class="text-lg font-semibold text-gray-900">Ongoing Orders</h3>
                                <p class="text-sm text-gray-600">Monitor and manage active orders</p>
                            </div>
                        </div>

                        <!-- Branch Selector -->
                        <div class="min-w-[250px]">
                            <BranchSelector
                                :branches="allBranches"
                                :selected-branch-id="selectedBranchId"
                                size="md"
                            />
                        </div>
                    </div>

                    <!-- Order Statistics -->
                    <div class="flex items-center space-x-6">
                        <div class="text-center">
                            <div class="text-2xl font-bold text-blue-600">{{ orders.length }}</div>
                            <div class="text-xs text-gray-600">Total Orders</div>
                        </div>
                        <div class="text-center">
                            <div class="text-2xl font-bold text-orange-600">{{ orders.filter(o => o.status === 'preparing').length }}</div>
                            <div class="text-xs text-gray-600">Preparing</div>
                        </div>
                        <div class="text-center">
                            <div class="text-2xl font-bold text-green-600">{{ orders.filter(o => o.status === 'ready').length }}</div>
                            <div class="text-xs text-gray-600">Ready</div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Filters and Search -->
        <div class="bg-white rounded-lg shadow-sm p-6 mb-6">
            <div class="flex flex-col md:flex-row md:items-center md:justify-between space-y-4 md:space-y-0">
                <!-- Search -->
                <div class="relative flex-1 max-w-md">
                    <input
                        v-model="searchQuery"
                        type="text"
                        placeholder="Search orders..."
                        class="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    />
                    <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                        <svg class="h-5 w-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                        </svg>
                    </div>
                </div>

                <!-- Status Filter -->
                <div class="flex space-x-2">
                    <button
                        v-for="status in orderStatuses"
                        :key="status.value"
                        @click="selectedStatus = status.value"
                        :class="[
                            'px-4 py-2 rounded-lg font-medium text-sm transition-colors',
                            selectedStatus === status.value
                                ? 'bg-blue-600 text-white'
                                : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                        ]"
                    >
                        {{ status.label }}
                    </button>
                </div>
            </div>
        </div>

        <!-- Orders Grid -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
            <div
                v-for="order in filteredOrders"
                :key="order.id"
                @click="viewOrderDetails(order)"
                class="bg-white rounded-lg shadow-sm border border-gray-200 hover:shadow-md transition-shadow cursor-pointer"
            >
                <!-- Order Header -->
                <div class="p-4 border-b border-gray-200">
                    <div class="flex items-center justify-between mb-2">
                        <h3 class="font-semibold text-lg text-gray-900">{{ order.order_number }}</h3>
                        <span
                            :class="[
                                'inline-flex px-2 py-1 rounded-full text-xs font-medium',
                                getStatusColor(order.status) === 'yellow' ? 'bg-yellow-100 text-yellow-800' :
                                getStatusColor(order.status) === 'blue' ? 'bg-blue-100 text-blue-800' :
                                getStatusColor(order.status) === 'orange' ? 'bg-orange-100 text-orange-800' :
                                getStatusColor(order.status) === 'green' ? 'bg-green-100 text-green-800' :
                                getStatusColor(order.status) === 'purple' ? 'bg-purple-100 text-purple-800' :
                                'bg-gray-100 text-gray-800'
                            ]"
                        >
                            {{ order.status }}
                        </span>
                    </div>
                    
                    <div class="text-sm text-gray-600 space-y-1">
                        <div class="flex justify-between">
                            <span>Table:</span>
                            <span class="font-medium">{{ order.table?.name || 'N/A' }}</span>
                        </div>
                        <div class="flex justify-between">
                            <span>Waiter:</span>
                            <span class="font-medium">{{ order.waiter?.name || 'N/A' }}</span>
                        </div>
                        <div class="flex justify-between">
                            <span>Running Time:</span>
                            <span class="font-medium text-orange-600">{{ getRunningTime(order.created_at) }}</span>
                        </div>
                    </div>
                </div>

                <!-- Order Details -->
                <div class="p-4">
                    <div class="text-sm text-gray-600 mb-3">
                        <div class="flex justify-between">
                            <span>Time:</span>
                            <span>{{ formatTime(order.created_at) }}</span>
                        </div>
                        <div class="flex justify-between">
                            <span>Person:</span>
                            <span>{{ order.guest_count || 1 }}</span>
                        </div>
                    </div>

                    <!-- Action Buttons -->
                    <div class="flex space-x-2">
                        <!-- Status Update Dropdown -->
                        <div v-if="getNextStatuses(order.status).length > 0" class="relative flex-1">
                            <select
                                @change="updateOrderStatus(order, $event.target.value)"
                                @click.stop
                                class="w-full px-3 py-2 bg-blue-600 text-white text-sm rounded-lg hover:bg-blue-700 transition-colors border-0 focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 appearance-none cursor-pointer"
                            >
                                <option value="" disabled selected>
                                    Update to {{ getStatusLabel(getNextStatuses(order.status)[0]) }}
                                </option>
                                <option
                                    v-for="status in getNextStatuses(order.status)"
                                    :key="status"
                                    :value="status"
                                    class="bg-white text-gray-900"
                                >
                                    {{ getStatusLabel(status) }}
                                </option>
                            </select>
                            <!-- Dropdown arrow -->
                            <div class="absolute inset-y-0 right-0 flex items-center pr-2 pointer-events-none">
                                <svg class="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                                </svg>
                            </div>
                        </div>

                        <!-- Complete Order Button (for ready orders) -->
                        <button
                            v-if="order.status === 'ready'"
                            @click.stop="completeOrder(order)"
                            class="flex-1 px-3 py-2 bg-green-600 text-white text-sm rounded-lg hover:bg-green-700 transition-colors"
                        >
                            Complete & Pay
                        </button>

                        <!-- Delete Button (only for pending orders) -->
                        <button
                            v-if="order.status === 'pending'"
                            @click.stop="deleteOrder(order)"
                            class="px-3 py-2 bg-red-100 text-red-700 text-sm rounded-lg hover:bg-red-200 transition-colors"
                        >
                            Delete
                        </button>

                        <!-- Edit Button -->
                        <button
                            v-if="order.status === 'pending'"
                            @click.stop="editOrder(order)"
                            class="px-3 py-2 bg-orange-100 text-orange-700 text-sm rounded-lg hover:bg-orange-200 transition-colors"
                        >
                            Edit
                        </button>
                    </div>

                    <!-- Order Total -->
                    <div class="mt-3 pt-3 border-t border-gray-200">
                        <div class="flex justify-between items-center">
                            <span class="text-sm text-gray-600">Total:</span>
                            <span class="font-semibold text-lg text-gray-900">{{ formatPrice(order.total_amount) }}</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Empty State -->
        <div v-if="filteredOrders.length === 0" class="bg-white rounded-lg shadow-sm p-12">
            <div class="text-center">
                <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"></path>
                </svg>
                <h3 class="mt-2 text-sm font-medium text-gray-900">No ongoing orders</h3>
                <p class="mt-1 text-sm text-gray-500">
                    {{ searchQuery ? 'No orders match your search criteria.' : 'There are no ongoing orders at the moment.' }}
                </p>
            </div>
        </div>
        </div>
    </div>
</template>

<style scoped>
/* Custom styles for ongoing orders */
</style>
