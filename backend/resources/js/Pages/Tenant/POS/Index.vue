<script setup>
import { Head, router } from '@inertiajs/vue3';
import { ref, computed, onMounted } from 'vue';
import POSLayout from '@/Layouts/POSLayout.vue';
import MenuItemGrid from '@/Components/POS/MenuItemGrid.vue';
import OrderPanel from '@/Components/POS/OrderPanel.vue';
import TableSelector from '@/Components/POS/TableSelector.vue';
import PaymentModal from '@/Components/POS/PaymentModal.vue';
import DiscountModal from '@/Components/POS/DiscountModal.vue';
import CustomerSearchDropdown from '@/Components/POS/CustomerSearchDropdown.vue';
import ItemVariantModal from '@/Components/POS/ItemVariantModal.vue';
import HomeDeliveryModal from '@/Components/POS/HomeDeliveryModal.vue';
import BranchSelector from '@/Components/POS/BranchSelector.vue';

const props = defineProps({
    branch: Object,
    allBranches: {
        type: Array,
        default: () => []
    },
    selectedBranchId: Number,
    categories: Array,
    activeOrders: Array,
    floors: Array,
    paymentMethods: Array,
    discountTypes: Array,
    customers: Array,
    waiters: {
        type: Array,
        default: () => []
    },
    waiterAssignedTable: Object,
    currentUser: {
        type: Object,
        required: true
    }
});

// Reactive state
const selectedCategory = ref(props.categories[0]?.id || null);
const currentOrder = ref(null);
const selectedTable = ref(props.waiterAssignedTable || null); // Default to waiter's assigned table
const selectedBranch = ref(props.selectedBranchId);
const showPaymentModal = ref(false);
const showDiscountModal = ref(false);
const showTableSelector = ref(false);
const showItemVariantModal = ref(false);
const showHomeDeliveryModal = ref(false);
const selectedMenuItem = ref(null);
const isLoading = ref(false);
const searchQuery = ref('');
const selectedCustomer = ref(null);
const selectedWaiter = ref(null);

// Draft order state
const draftItems = ref([]);
const isDraftMode = ref(true);

// Computed properties
const filteredMenuItems = computed(() => {
    let items = [];
    if (selectedCategory.value === null) {
        // Show all items from all categories
        items = props.categories.flatMap(cat => cat.menu_items || []);
    } else {
        // Show items from selected category
        const category = props.categories.find(cat => cat.id === selectedCategory.value);
        items = category?.menu_items || [];
    }

    // Note: Items are already filtered by branch in the backend
    // The categories and menu_items are loaded based on the selected branch

    // Filter by search query
    if (searchQuery.value) {
        items = items.filter(item =>
            item.name.toLowerCase().includes(searchQuery.value.toLowerCase()) ||
            item.description?.toLowerCase().includes(searchQuery.value.toLowerCase())
        );
    }

    return items;
});

const orderTotal = computed(() => {
    if (isDraftMode.value) {
        return draftItems.value.reduce((total, item) => total + item.total_price, 0);
    }
    if (!currentOrder.value) return 0;
    return currentOrder.value.total_amount || 0;
});

const draftSubtotal = computed(() => {
    return draftItems.value.reduce((total, item) => total + item.total_price, 0);
});

const canPlaceOrder = computed(() => {
    return Boolean(isDraftMode.value && draftItems.value.length > 0 && selectedTable.value);
});

const hasItems = computed(() => {
    return isDraftMode.value ? draftItems.value.length > 0 : (currentOrder.value?.items?.length > 0);
});

// Role-based UI computed properties
const isWaiter = computed(() => props.currentUser.role === 'waiter');
const isManager = computed(() => ['manager', 'restaurant_manager'].includes(props.currentUser.role));
const showWaiterDropdown = computed(() => isManager.value);

// Auto-assign waiter if current user is a waiter
const currentWaiter = computed(() => {
    if (isWaiter.value) {
        return {
            id: props.currentUser.id,
            name: props.currentUser.name,
            email: props.currentUser.email,
            role: props.currentUser.role
        };
    }
    return selectedWaiter.value;
});

// Methods
const selectCategory = (categoryId) => {
    selectedCategory.value = categoryId;
};

// Customer handling methods
const handleCustomerSelect = (customer) => {
    selectedCustomer.value = customer;
};

const handleCreateCustomer = (customerName) => {
    // For now, create a temporary customer object
    // In a real app, you'd make an API call to create the customer
    const newCustomer = {
        id: Date.now(), // temporary ID
        name: customerName,
        type: 'new',
        tier: 'regular'
    };
    selectedCustomer.value = newCustomer;
};

// Waiter handling methods
const handleWaiterSelect = (waiterId) => {
    const waiter = props.waiters.find(w => w.id === parseInt(waiterId));
    selectedWaiter.value = waiter || null;
};

// Branch handling methods
const handleBranchChange = async (branchId) => {
    if (branchId === selectedBranch.value) return;

    isLoading.value = true;
    try {
        const response = await axios.post(route('pos.switch-branch'), {
            branch_id: branchId
        });

        if (response.data.success) {
            selectedBranch.value = branchId;

            // Clear current selections when switching branches
            selectedTable.value = null;
            currentOrder.value = null;
            draftItems.value = [];
            isDraftMode.value = true;

            // Reload the page to get updated data for the new branch
            router.reload();
        }
    } catch (error) {
        console.error('Failed to switch branch:', error);
        alert('Failed to switch branch. Please try again.');
    } finally {
        isLoading.value = false;
    }
};

// Handle branch change from BranchSelector component
const handleBranchChanged = (event) => {
    // The BranchSelector component handles the API call and page reload
    // This method is just for any additional logic if needed
    console.log('Branch changed to:', event.branchId);
};

const refreshCustomers = async () => {
    try {
        const response = await axios.get(route('customers.index'), {
            params: { format: 'json' }
        });

        if (response.data.customers) {
            // Update the customers prop - this would need to be handled by the parent component
            // For now, we'll just reload the page to get fresh data
            router.reload({ only: ['customers'] });
        }
    } catch (error) {
        console.error('Failed to refresh customers:', error);
    }
};

const createNewOrder = async () => {
    if (!selectedTable.value) {
        showTableSelector.value = true;
        return;
    }

    isLoading.value = true;
    try {
        const response = await axios.post(route('pos.orders.create'), {
            branch_id: selectedBranch.value || props.branch.id,
            table_id: selectedTable.value.id,
            order_type: 'dine_in',
            guest_count: 1,
        });

        if (response.data.success) {
            currentOrder.value = response.data.order;
            showTableSelector.value = false;
        }
    } catch (error) {
        console.error('Failed to create order:', error);
        alert('Failed to create order. Please try again.');
    } finally {
        isLoading.value = false;
    }
};

const selectOrder = (order) => {
    currentOrder.value = order;
    selectedTable.value = order.table;
};

// Store the pending item to add after table selection
const pendingMenuItem = ref(null);

const addItemToOrder = async (menuItem) => {
    // If in draft mode and no table selected, open table selector
    if (isDraftMode.value && !selectedTable.value) {
        pendingMenuItem.value = menuItem;
        showTableSelector.value = true;
        return;
    }

    // If not in draft mode and no current order, create one
    if (!isDraftMode.value && !currentOrder.value) {
        if (!selectedTable.value) {
            pendingMenuItem.value = menuItem;
            showTableSelector.value = true;
            return;
        }
        await createNewOrder();
        if (!currentOrder.value) return;
    }

    // Open variant selection modal
    selectedMenuItem.value = menuItem;
    showItemVariantModal.value = true;
};

// Generate unique key for draft items to detect duplicates
const generateDraftItemKey = (item) => {
    const modifiersKey = JSON.stringify(item.modifiers || {});
    const instructionsKey = (item.special_instructions || '').trim();
    return `${item.menu_item_id}_${modifiersKey}_${instructionsKey}`;
};

const handleAddToCart = async (cartItem) => {
    if (isDraftMode.value) {
        // Generate unique identifier for the item (including variants/modifiers)
        const itemKey = generateDraftItemKey(cartItem);

        // Check if item already exists in draft
        const existingItemIndex = draftItems.value.findIndex(item =>
            generateDraftItemKey(item) === itemKey
        );

        if (existingItemIndex > -1) {
            // Update existing item quantity
            const existingItem = draftItems.value[existingItemIndex];
            existingItem.quantity += cartItem.quantity;
            existingItem.total_price = existingItem.unit_price * existingItem.quantity;
            playSound('increment');
        } else {
            // Add new draft item
            const draftItem = {
                id: Date.now(), // Temporary ID for draft items
                menu_item_id: cartItem.menu_item_id,
                food_name: cartItem.food_name,
                food_description: cartItem.food_description,
                unit_price: cartItem.unit_price,
                quantity: cartItem.quantity,
                total_price: cartItem.total_price,
                special_instructions: cartItem.special_instructions,
                modifiers: cartItem.modifiers,
                is_draft: true
            };

            draftItems.value.push(draftItem);
            playSound('add');
        }

        showItemVariantModal.value = false;
        return;
    }

    // Original logic for placed orders
    if (!currentOrder.value) return;

    isLoading.value = true;
    try {
        const response = await axios.post(route('pos.orders.add-item', currentOrder.value.id), cartItem);

        if (response.data.success) {
            currentOrder.value = response.data.order;
            playSound('add');
        }
    } catch (error) {
        console.error('Failed to add item:', error);
        alert('Failed to add item. Please try again.');
    } finally {
        isLoading.value = false;
    }
};

const playSound = (type) => {
    try {
        const audioContext = new (window.AudioContext || window.webkitAudioContext)();

        let frequency;
        switch (type) {
            case 'add':
                frequency = 800; // Higher pitch for add
                break;
            case 'increment':
                frequency = 600; // Medium pitch for increment
                break;
            case 'decrement':
                frequency = 400; // Lower pitch for decrement
                break;
            default:
                frequency = 500;
        }

        const oscillator = audioContext.createOscillator();
        const gainNode = audioContext.createGain();

        oscillator.connect(gainNode);
        gainNode.connect(audioContext.destination);

        oscillator.frequency.setValueAtTime(frequency, audioContext.currentTime);
        oscillator.type = 'sine';

        gainNode.gain.setValueAtTime(0.1, audioContext.currentTime);
        gainNode.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + 0.2);

        oscillator.start(audioContext.currentTime);
        oscillator.stop(audioContext.currentTime + 0.2);
    } catch (error) {
        console.log('Audio not supported');
    }
};

const updateItemQuantity = async (orderItem, quantity) => {
    const oldQuantity = orderItem.quantity;

    isLoading.value = true;
    try {
        const response = await axios.put(route('pos.order-items.update-quantity', orderItem.id), {
            quantity: quantity,
        });

        if (response.data.success) {
            currentOrder.value = response.data.order;

            // Play sound based on quantity change
            if (quantity > oldQuantity) {
                playSound('increment');
            } else if (quantity < oldQuantity) {
                playSound('decrement');
            }
        }
    } catch (error) {
        console.error('Failed to update quantity:', error);
        alert('Failed to update quantity. Please try again.');
    } finally {
        isLoading.value = false;
    }
};

const removeItem = async (orderItem) => {
    if (!confirm('Are you sure you want to remove this item?')) return;

    isLoading.value = true;
    try {
        const response = await axios.delete(route('pos.order-items.remove', orderItem.id));

        if (response.data.success) {
            currentOrder.value = response.data.order;
        }
    } catch (error) {
        console.error('Failed to remove item:', error);
        alert('Failed to remove item. Please try again.');
    } finally {
        isLoading.value = false;
    }
};

const openPaymentModal = () => {
    if (!currentOrder.value || !currentOrder.value.items?.length) {
        alert('Please add items to the order first.');
        return;
    }
    showPaymentModal.value = true;
};

const openDiscountModal = () => {
    if (!currentOrder.value || !currentOrder.value.items?.length) {
        alert('Please add items to the order first.');
        return;
    }
    showDiscountModal.value = true;
};

const processPayment = async (paymentData) => {
    isLoading.value = true;
    try {
        const response = await axios.post(route('pos.orders.process-payment', currentOrder.value.id), paymentData);

        if (response.data.success) {
            currentOrder.value = response.data.order;
            showPaymentModal.value = false;
            
            // If fully paid, close the order
            if (currentOrder.value.payment_status === 'paid') {
                await closeOrder();
            }
        }
    } catch (error) {
        console.error('Failed to process payment:', error);
        alert('Failed to process payment. Please try again.');
    } finally {
        isLoading.value = false;
    }
};

const applyDiscount = async (discountData) => {
    isLoading.value = true;
    try {
        const response = await axios.post(route('pos.orders.apply-discount', currentOrder.value.id), discountData);

        if (response.data.success) {
            currentOrder.value = response.data.order;
            showDiscountModal.value = false;
        }
    } catch (error) {
        console.error('Failed to apply discount:', error);
        alert('Failed to apply discount. Please try again.');
    } finally {
        isLoading.value = false;
    }
};

const sendToKitchen = async () => {
    if (!currentOrder.value || !currentOrder.value.items?.length) {
        alert('Please add items to the order first.');
        return;
    }

    isLoading.value = true;
    try {
        const response = await axios.post(route('pos.orders.send-to-kitchen', currentOrder.value.id));

        if (response.data.success) {
            currentOrder.value = response.data.order;
            alert('Order sent to kitchen successfully!');
        }
    } catch (error) {
        console.error('Failed to send to kitchen:', error);
        alert('Failed to send order to kitchen. Please try again.');
    } finally {
        isLoading.value = false;
    }
};

const closeOrder = async () => {
    if (!currentOrder.value) return;

    isLoading.value = true;
    try {
        const response = await axios.post(route('pos.orders.close', currentOrder.value.id));

        if (response.data.success) {
            currentOrder.value = null;
            selectedTable.value = null;
            isDraftMode.value = true;
            draftItems.value = [];
            // Refresh the page to get updated active orders
            router.reload();
        }
    } catch (error) {
        console.error('Failed to close order:', error);
        alert('Failed to close order. Please try again.');
    } finally {
        isLoading.value = false;
    }
};

// Home delivery methods
const openHomeDeliveryModal = () => {
    if (!isDraftMode.value || draftItems.value.length === 0) {
        alert('Please add items to the order first.');
        return;
    }
    showHomeDeliveryModal.value = true;
};

const handleDeliveryOrderSubmit = async (deliveryData) => {
    isLoading.value = true;
    try {
        // Create delivery order
        const response = await axios.post(route('pos.orders.create-delivery'), {
            ...deliveryData,
            items: draftItems.value.map(item => ({
                menu_item_id: item.menu_item_id,
                quantity: item.quantity,
                unit_price: item.unit_price,
                special_instructions: item.special_instructions,
                modifiers: item.modifiers
            }))
        });

        if (response.data.success) {
            // Clear draft and close modal
            draftItems.value = [];
            showHomeDeliveryModal.value = false;
            selectedCustomer.value = null;

            alert('Delivery order created successfully!');

            // Refresh the page to show updated orders
            router.reload();
        }
    } catch (error) {
        console.error('Failed to create delivery order:', error);
        alert('Failed to create delivery order. Please try again.');
    } finally {
        isLoading.value = false;
    }
};

// Draft order management functions
const updateDraftItemQuantity = (draftItem, quantity) => {
    if (quantity <= 0) {
        removeDraftItem(draftItem);
        return;
    }

    const oldQuantity = draftItem.quantity;
    draftItem.quantity = quantity;
    draftItem.total_price = draftItem.unit_price * quantity;

    playSound(quantity > oldQuantity ? 'increment' : 'decrement');
};

const removeDraftItem = (draftItem) => {
    const index = draftItems.value.findIndex(item => item.id === draftItem.id);
    if (index > -1) {
        draftItems.value.splice(index, 1);
    }
};

const placeOrder = async () => {
    if (!canPlaceOrder.value) {
        alert('Please select a table and add items before placing the order.');
        return;
    }

    isLoading.value = true;
    try {
        // Create the order first
        const orderResponse = await axios.post(route('pos.orders.create'), {
            branch_id: selectedBranch.value || props.branch.id,
            table_id: selectedTable.value.id,
            order_type: 'dine_in',
            guest_count: 1,
            customer_id: selectedCustomer.value?.id,
            customer_name: selectedCustomer.value?.name,
            customer_phone: selectedCustomer.value?.phone,
            customer_email: selectedCustomer.value?.email,
            waiter_id: currentWaiter.value?.id,
        });

        if (!orderResponse.data.success) {
            throw new Error('Failed to create order');
        }

        const order = orderResponse.data.order;

        // Add all draft items to the order
        for (const draftItem of draftItems.value) {
            await axios.post(route('pos.orders.add-item', order.id), {
                menu_item_id: draftItem.menu_item_id,
                quantity: draftItem.quantity,
                unit_price: draftItem.unit_price,
                total_price: draftItem.total_price,
                special_instructions: draftItem.special_instructions,
                modifiers: draftItem.modifiers,
            });
        }

        // Switch to placed order mode
        isDraftMode.value = false;
        draftItems.value = [];
        currentOrder.value = order;

        alert('Order placed successfully!');

        // Refresh to get the updated order with all items
        router.reload();

    } catch (error) {
        console.error('Failed to place order:', error);
        alert('Failed to place order. Please try again.');
    } finally {
        isLoading.value = false;
    }
};

const selectTable = async (table) => {
    selectedTable.value = table;
    showTableSelector.value = false;

    // Don't create order immediately - stay in draft mode
    // If there's a pending menu item, open variant modal
    if (pendingMenuItem.value) {
        selectedMenuItem.value = pendingMenuItem.value;
        showItemVariantModal.value = true;
        pendingMenuItem.value = null;
    }
};

// Lifecycle
onMounted(() => {
    // Start with "All" selected (null) to show all items
    selectedCategory.value = null;

    // Auto-assign waiter if current user is a waiter
    if (isWaiter.value) {
        selectedWaiter.value = {
            id: props.currentUser.id,
            name: props.currentUser.name,
            email: props.currentUser.email,
            role: props.currentUser.role
        };
    }

    // Set default table selection for waiters (their assigned table)
    if (props.waiterAssignedTable && isWaiter.value) {
        selectedTable.value = props.waiterAssignedTable;
    }
});
</script>

<template>
    <div class="min-h-screen bg-gray-100">
        <Head :title="`POS - ${branch.name}`" />

        <!-- Single Modern Navigation Header -->
        <nav class="bg-white shadow-sm border-b border-gray-200">
            <div class="px-6 py-4">
                <div class="flex items-center justify-between">
                    <!-- Navigation Tabs -->
                    <div class="flex space-x-1">
                        <button
                            @click="router.visit(route('dashboard'))"
                            class="px-4 py-2 text-sm font-medium text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded-lg transition-colors flex items-center space-x-2"
                        >
                            <span class="text-green-600">🏠</span>
                            <span>Home</span>
                        </button>

                        <button
                            class="px-4 py-2 text-sm font-medium bg-blue-600 text-white rounded-lg flex items-center space-x-2"
                        >
                            <span>📝</span>
                            <span>New Order</span>
                        </button>

                        <button
                            @click="router.visit(route('pos.ongoing-orders'))"
                            class="px-4 py-2 text-sm font-medium text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded-lg transition-colors flex items-center space-x-2"
                        >
                            <span>🔄</span>
                            <span>On Going Order</span>
                        </button>

                        <button
                            @click="router.visit(route('kitchen.index'))"
                            class="px-4 py-2 text-sm font-medium text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded-lg transition-colors flex items-center space-x-2"
                        >
                            <span class="text-purple-600">👨‍🍳</span>
                            <span>Kitchen Status</span>
                        </button>

                        <button
                            class="px-4 py-2 text-sm font-medium text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded-lg transition-colors flex items-center space-x-2"
                        >
                            <span class="text-orange-600">📱</span>
                            <span>QR Order</span>
                        </button>

                        <button
                            class="px-4 py-2 text-sm font-medium text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded-lg transition-colors flex items-center space-x-2"
                        >
                            <span class="text-yellow-600">➕</span>
                            <span>Create Order</span>
                        </button>

                        <button
                            @click="router.visit(route('pos.table-view'))"
                            class="px-4 py-2 text-sm font-medium text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded-lg transition-colors flex items-center space-x-2"
                        >
                            <span class="text-blue-600">🪑</span>
                            <span>Table View</span>
                        </button>
                    </div>

                    <!-- Right side controls -->
                    <div class="flex items-center space-x-4">
                        <div class="flex items-center space-x-2 text-sm text-gray-600">
                            <span>🌐</span>
                            <span>ENG</span>
                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                            </svg>
                        </div>

                        <button class="p-2 text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded-lg transition-colors">
                            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"></path>
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                            </svg>
                        </button>

                        <button class="p-2 text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded-lg transition-colors">
                            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"></path>
                            </svg>
                        </button>
                    </div>
                </div>
            </div>
        </nav>

        <!-- Main Content -->
        <div class="max-w-full mx-auto py-6 px-4 sm:px-6 lg:px-8">
            <!-- Branch Selection Header -->
            <div class="bg-white rounded-lg shadow-sm p-6 mb-6">
                <div class="flex items-center justify-between">
                    <div class="flex items-center space-x-6">
                        <div class="flex items-center space-x-3">
                            <div class="w-10 h-10 bg-blue-600 rounded-lg flex items-center justify-center">
                                <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"></path>
                                </svg>
                            </div>
                            <div>
                                <h3 class="text-lg font-semibold text-gray-900">Point of Sale</h3>
                                <p class="text-sm text-gray-600">Manage orders and process payments</p>
                            </div>
                        </div>

                        <!-- Branch Selector -->
                        <div class="min-w-[250px]">
                            <BranchSelector
                                :branches="allBranches"
                                :selected-branch-id="selectedBranchId"
                                :disabled="isLoading"
                                size="md"
                                @branch-changed="handleBranchChanged"
                            />
                        </div>
                    </div>

                    <!-- Status Indicators -->
                    <div class="flex items-center space-x-4">
                        <div class="flex items-center space-x-2 text-sm">
                            <span class="w-2 h-2 bg-green-400 rounded-full"></span>
                            <span class="text-gray-600">System Online</span>
                        </div>
                        <div class="text-sm text-gray-600">
                            {{ new Date().toLocaleTimeString() }}
                        </div>
                    </div>
                </div>
            </div>

            <!-- Main Content Grid -->
            <div class="grid grid-cols-12 gap-6 h-full">
            <!-- Left Panel - Order Management -->
            <div class="col-span-4 space-y-6">
                <!-- Customer Information -->
                <div class="bg-white rounded-lg shadow-sm p-6">
                    <h3 class="text-lg font-semibold text-gray-900 mb-4">Customer Information</h3>

                    <!-- Customer Search -->
                    <div class="mb-4">
                        <label class="block text-sm font-medium text-gray-700 mb-2">Customer Name*</label>
                        <CustomerSearchDropdown
                            :customers="customers"
                            :selected-customer="selectedCustomer"
                            placeholder="Search customer or walk-in"
                            @select-customer="handleCustomerSelect"
                            @create-customer="handleCreateCustomer"
                            @refresh-customers="refreshCustomers"
                        />
                    </div>

                    <!-- Customer Details (if selected) -->
                    <div v-if="selectedCustomer && selectedCustomer.id" class="mb-4 p-3 bg-gray-50 rounded-lg">
                        <div class="flex items-center space-x-3">
                            <div class="w-10 h-10 bg-blue-500 rounded-full flex items-center justify-center text-white font-medium">
                                {{ selectedCustomer.name?.charAt(0)?.toUpperCase() }}
                            </div>
                            <div class="flex-1">
                                <div class="font-medium text-gray-900">{{ selectedCustomer.name }}</div>
                                <div class="text-sm text-gray-600">
                                    <span v-if="selectedCustomer.phone">📞 {{ selectedCustomer.phone }}</span>
                                    <span v-if="selectedCustomer.email" class="ml-2">✉️ {{ selectedCustomer.email }}</span>
                                </div>
                                <div class="text-xs text-gray-500 mt-1">
                                    <span v-if="selectedCustomer.total_orders">{{ selectedCustomer.total_orders }} orders</span>
                                    <span v-if="selectedCustomer.loyalty_points" class="ml-2">{{ selectedCustomer.loyalty_points }} points</span>
                                </div>
                            </div>
                            <span
                                :class="[
                                    'inline-flex px-2 py-1 rounded-full text-xs font-medium',
                                    selectedCustomer.tier === 'vip' ? 'bg-purple-100 text-purple-800' :
                                    selectedCustomer.tier === 'gold' ? 'bg-yellow-100 text-yellow-800' :
                                    selectedCustomer.tier === 'silver' ? 'bg-gray-100 text-gray-800' :
                                    'bg-green-100 text-green-800'
                                ]"
                            >
                                {{ selectedCustomer.tier || 'Regular' }}
                            </span>
                        </div>
                    </div>

                    <!-- Waiter Selection (Only for Managers) -->
                    <div v-if="showWaiterDropdown" class="mb-4">
                        <label class="block text-sm font-medium text-gray-700 mb-2">Waiter*</label>
                        <div class="relative">
                            <select
                                @change="handleWaiterSelect($event.target.value)"
                                class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 appearance-none"
                            >
                                <option value="">Select waiter</option>
                                <option
                                    v-for="waiter in waiters"
                                    :key="waiter.id"
                                    :value="waiter.id"
                                    :selected="selectedWaiter?.id === waiter.id"
                                >
                                    {{ waiter.name }}
                                </option>
                            </select>
                            <svg class="absolute right-3 top-3 w-4 h-4 text-gray-400 pointer-events-none" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                            </svg>
                        </div>

                        <!-- Selected Waiter Info -->
                        <div v-if="selectedWaiter" class="mt-2 p-2 bg-blue-50 rounded-lg">
                            <div class="flex items-center space-x-2">
                                <div class="w-6 h-6 bg-blue-500 rounded-full flex items-center justify-center text-white text-xs font-medium">
                                    {{ selectedWaiter.name?.charAt(0)?.toUpperCase() }}
                                </div>
                                <span class="text-sm font-medium text-blue-900">{{ selectedWaiter.name }}</span>
                                <span v-if="selectedWaiter.department" class="text-xs text-blue-600">{{ selectedWaiter.department }}</span>
                            </div>
                        </div>
                    </div>

                    <!-- Waiter Info Display (For Waiters) -->
                    <div v-else-if="isWaiter" class="mb-4">
                        <label class="block text-sm font-medium text-gray-700 mb-2">Assigned Waiter</label>
                        <div class="p-3 bg-green-50 border border-green-200 rounded-lg">
                            <div class="flex items-center space-x-3">
                                <div class="w-8 h-8 bg-green-500 rounded-full flex items-center justify-center text-white text-sm font-medium">
                                    {{ currentUser.name?.charAt(0)?.toUpperCase() }}
                                </div>
                                <div>
                                    <div class="text-sm font-medium text-green-900">{{ currentUser.name }}</div>
                                    <div class="text-xs text-green-600">{{ currentUser.email }}</div>
                                </div>
                                <span class="ml-auto inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                    Auto-assigned
                                </span>
                            </div>
                        </div>
                    </div>

                    <!-- Table Selection -->
                    <div class="mb-4">
                        <label class="block text-sm font-medium text-gray-700 mb-2">Table Selection</label>

                        <!-- Selected Table Display -->
                        <div v-if="selectedTable" class="mb-3 p-3 bg-blue-50 border border-blue-200 rounded-lg">
                            <div class="flex items-center justify-between">
                                <div class="flex items-center space-x-3">
                                    <div class="w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center text-white text-sm font-medium">
                                        🪑
                                    </div>
                                    <div>
                                        <div class="font-medium text-blue-900">Table {{ selectedTable.name }}</div>
                                        <div class="text-sm text-blue-600">
                                            Capacity: {{ selectedTable.capacity }} people
                                            <span v-if="selectedTable.assigned_waiter_id === currentUser.id" class="ml-2 text-green-600">
                                                (Your assigned table)
                                            </span>
                                        </div>
                                    </div>
                                </div>
                                <button
                                    @click="showTableSelector = true"
                                    class="text-blue-600 hover:text-blue-800 text-sm font-medium"
                                >
                                    Change
                                </button>
                            </div>
                        </div>

                        <!-- Table Selection Button -->
                        <div v-else class="flex space-x-2">
                            <button
                                @click="showTableSelector = true"
                                class="flex-1 px-3 py-2 border border-gray-300 rounded-lg text-left text-gray-500 hover:bg-gray-50 focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                            >
                                Select Table
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Order Panel -->
                <OrderPanel
                    :order="currentOrder"
                    :active-orders="activeOrders"
                    :selected-table="selectedTable"
                    :loading="isLoading"
                    :draft-items="draftItems"
                    :is-draft-mode="isDraftMode"
                    :can-place-order="canPlaceOrder"
                    @select-order="selectOrder"
                    @update-quantity="updateItemQuantity"
                    @remove-item="removeItem"
                    @update-draft-quantity="updateDraftItemQuantity"
                    @remove-draft-item="removeDraftItem"
                    @place-order="placeOrder"
                    @open-payment="openPaymentModal"
                    @open-discount="openDiscountModal"
                    @send-to-kitchen="sendToKitchen"
                    @close-order="closeOrder"
                    @open-home-delivery="openHomeDeliveryModal"
                />
            </div>

            <!-- Right Panel - Menu Items -->
            <div class="col-span-8 bg-white rounded-lg shadow-sm overflow-hidden">
                <!-- Search and Categories -->
                <div class="border-b border-gray-200 p-6">
                    <!-- Search Bar -->
                    <div class="mb-4">
                        <div class="relative">
                            <input
                                v-model="searchQuery"
                                type="text"
                                placeholder="Search Products"
                                class="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-lg"
                            />
                            <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                <svg class="h-5 w-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                                </svg>
                            </div>
                        </div>
                    </div>

                    <!-- Category Filter -->
                    <div class="flex flex-wrap gap-2">
                        <button
                            @click="selectedCategory = null"
                            :class="[
                                'px-4 py-2 rounded-lg font-medium text-sm transition-colors',
                                selectedCategory === null
                                    ? 'bg-blue-600 text-white'
                                    : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                            ]"
                        >
                            All
                        </button>
                        <button
                            v-for="category in categories"
                            :key="category.id"
                            @click="selectCategory(category.id)"
                            :class="[
                                'px-4 py-2 rounded-lg font-medium text-sm transition-colors',
                                selectedCategory === category.id
                                    ? 'bg-blue-600 text-white'
                                    : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                            ]"
                        >
                            {{ category.name }}
                        </button>
                    </div>
                </div>

                <!-- Menu Items Grid -->
                <div class="p-6">
                    <MenuItemGrid
                        :items="filteredMenuItems"
                        @add-item="addItemToOrder"
                        :loading="isLoading"
                    />
                </div>
            </div>
        </div>
        </div>

        <!-- Modals -->
        <TableSelector
            v-if="showTableSelector"
            :floors="floors"
            :selected-waiter="currentWaiter"
            @select-table="selectTable"
            @close="showTableSelector = false"
        />

        <PaymentModal
            v-if="showPaymentModal"
            :order="currentOrder"
            :payment-methods="paymentMethods"
            @process-payment="processPayment"
            @close="showPaymentModal = false"
        />

        <DiscountModal
            v-if="showDiscountModal"
            :order="currentOrder"
            :discount-types="discountTypes"
            @apply-discount="applyDiscount"
            @close="showDiscountModal = false"
        />

        <ItemVariantModal
            :show="showItemVariantModal"
            :item="selectedMenuItem"
            @add-to-cart="handleAddToCart"
            @close="showItemVariantModal = false"
        />

        <HomeDeliveryModal
            v-if="showHomeDeliveryModal"
            :show="showHomeDeliveryModal"
            :order-items="draftItems"
            :selected-customer="selectedCustomer"
            :branch="branch"
            :available-riders="[]"
            @submit="handleDeliveryOrderSubmit"
            @close="showHomeDeliveryModal = false"
        />
    </div>
</template>

<style scoped>
/* Add any component-specific styles here */
</style>
