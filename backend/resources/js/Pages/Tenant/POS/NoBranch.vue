<script setup>
import { Head, <PERSON> } from '@inertiajs/vue3';
import POSLayout from '@/Layouts/POSLayout.vue';

defineProps({
    error: {
        type: String,
        default: 'You do not have access to any branches for POS operations.'
    }
});
</script>

<template>
    <POSLayout title="No Branch Access">
        <template #header>
            <h1 class="text-2xl font-bold text-gray-900 dark:text-white">
                🏪 Point of Sale System
            </h1>
        </template>

        <div class="min-h-96 flex items-center justify-center">
            <div class="max-w-md w-full bg-white dark:bg-gray-800 rounded-lg shadow-lg p-8 text-center">
                <!-- Icon -->
                <div class="mx-auto flex items-center justify-center h-16 w-16 rounded-full bg-red-100 dark:bg-red-900/20 mb-6">
                    <svg class="h-8 w-8 text-red-600 dark:text-red-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
                    </svg>
                </div>

                <!-- Title -->
                <h2 class="text-xl font-semibold text-gray-900 dark:text-white mb-4">
                    {{ $t('No Branch Access') }}
                </h2>

                <!-- Error Message -->
                <p class="text-gray-600 dark:text-gray-400 mb-6">
                    {{ error }}
                </p>

                <!-- Suggestions -->
                <div class="text-left bg-gray-50 dark:bg-gray-700 rounded-lg p-4 mb-6">
                    <h3 class="font-medium text-gray-900 dark:text-white mb-2">
                        {{ $t('What you can do:') }}
                    </h3>
                    <ul class="text-sm text-gray-600 dark:text-gray-400 space-y-1">
                        <li>• {{ $t('Contact your administrator to assign you to a branch') }}</li>
                        <li>• {{ $t('Check if you have the correct role permissions') }}</li>
                        <li>• {{ $t('Ensure your employee record is properly configured') }}</li>
                    </ul>
                </div>

                <!-- Action Buttons -->
                <div class="space-y-3">
                    <Link
                        :href="route('dashboard')"
                        class="w-full inline-flex justify-center items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
                    >
                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6"></path>
                        </svg>
                        {{ $t('Go to Dashboard') }}
                    </Link>

                    <button
                        @click="$inertia.reload()"
                        class="w-full inline-flex justify-center items-center px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors"
                    >
                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                        </svg>
                        {{ $t('Refresh Page') }}
                    </button>
                </div>

                <!-- Help Text -->
                <p class="text-xs text-gray-500 dark:text-gray-400 mt-6">
                    {{ $t('If this problem persists, please contact your system administrator.') }}
                </p>
            </div>
        </div>
    </POSLayout>
</template>

<style scoped>
/* Custom styles for the no branch page */
</style>
