<script setup>
import { Head, router } from '@inertiajs/vue3';
import { ref, computed } from 'vue';
import axios from 'axios';
import PaymentModal from '@/Components/POS/PaymentModal.vue';
import { formatPrice } from '@/utils/currency';

const props = defineProps({
    order: Object,
    paymentMethods: Array,
});

// Reactive state
const showPaymentModal = ref(false);
const showPrintModal = ref(false);
const isLoading = ref(false);

// Computed properties
const orderTotal = computed(() => {
    return props.order.total_amount || props.order.grand_total || 0;
});

const paidAmount = computed(() => {
    return props.order.payments?.reduce((sum, payment) => sum + parseFloat(payment.amount), 0) || 0;
});

const remainingBalance = computed(() => {
    return orderTotal.value - paidAmount.value;
});

const isFullyPaid = computed(() => {
    return remainingBalance.value <= 0;
});

// Methods
// formatPrice is now imported from currency utils

const formatTime = (dateString) => {
    if (!dateString) return '';
    const date = new Date(dateString);
    return date.toLocaleString();
};

const getStatusColor = (status) => {
    const colors = {
        'pending': 'bg-yellow-100 text-yellow-800',
        'confirmed': 'bg-blue-100 text-blue-800',
        'preparing': 'bg-orange-100 text-orange-800',
        'ready': 'bg-green-100 text-green-800',
        'served': 'bg-purple-100 text-purple-800',
        'cancelled': 'bg-red-100 text-red-800',
    };
    return colors[status] || 'bg-gray-100 text-gray-800';
};

const processPayment = async (paymentData) => {
    isLoading.value = true;
    try {
        await axios.post(route('pos.orders.process-payment', props.order.id), paymentData);
        showPaymentModal.value = false;
        router.reload();
    } catch (error) {
        console.error('Payment failed:', error);
        alert('Payment processing failed. Please try again.');
    } finally {
        isLoading.value = false;
    }
};

const printReceipt = () => {
    window.open(route('pos.orders.print', props.order.id), '_blank');
};

const editOrder = () => {
    router.visit(route('pos.orders.edit', props.order.id));
};

const goBack = () => {
    router.visit(route('pos.ongoing-orders'));
};

// Status management
const getNextStatuses = (currentStatus) => {
    const statusFlow = {
        'pending': ['confirmed'],
        'confirmed': ['preparing'],
        'preparing': ['ready'],
        'ready': ['served'],
        'served': []
    };
    return statusFlow[currentStatus] || [];
};

const getStatusLabel = (status) => {
    const labels = {
        'pending': 'Pending',
        'confirmed': 'Confirmed',
        'preparing': 'Preparing',
        'ready': 'Ready',
        'served': 'Served',
        'cancelled': 'Cancelled'
    };
    return labels[status] || status.charAt(0).toUpperCase() + status.slice(1);
};

const updateOrderStatus = async (newStatus) => {
    if (!newStatus) return;

    isLoading.value = true;
    try {
        await axios.patch(route('pos.orders.update-status', props.order.id), {
            status: newStatus
        });

        // If status is served, navigate to order details after update
        if (newStatus === 'served') {
            router.visit(route('pos.orders.show', props.order.id));
        } else {
            router.reload();
        }
    } catch (error) {
        console.error('Failed to update order status:', error);
        alert('Failed to update order status. Please try again.');
    } finally {
        isLoading.value = false;
    }
};
</script>

<template>
    <div class="min-h-screen bg-gray-100">
        <Head :title="`Order ${order.order_number} - ${order.branch?.name}`" />

        <!-- Navigation Header -->
        <nav class="bg-white shadow-sm border-b border-gray-200">
            <div class="px-6 py-4">
                <div class="flex items-center justify-between">
                    <div class="flex items-center space-x-4">
                        <button
                            @click="goBack"
                            class="flex items-center space-x-2 text-gray-600 hover:text-gray-900"
                        >
                            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
                            </svg>
                            <span>Back to Orders</span>
                        </button>
                        
                        <div class="h-6 border-l border-gray-300"></div>
                        
                        <div>
                            <h1 class="text-xl font-semibold text-gray-900">Order {{ order.order_number }}</h1>
                            <p class="text-sm text-gray-600">{{ order.branch?.name }}</p>
                        </div>
                    </div>

                    <div class="flex items-center space-x-3">
                        <!-- Current Status Badge -->
                        <span
                            :class="[
                                'inline-flex px-3 py-1 rounded-full text-sm font-medium',
                                getStatusColor(order.status)
                            ]"
                        >
                            {{ getStatusLabel(order.status) }}
                        </span>

                        <!-- Status Update Dropdown -->
                        <div v-if="getNextStatuses(order.status).length > 0" class="relative">
                            <select
                                @change="updateOrderStatus($event.target.value)"
                                :disabled="isLoading"
                                class="px-4 py-2 bg-blue-600 text-white text-sm rounded-lg hover:bg-blue-700 transition-colors border-0 focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 appearance-none cursor-pointer disabled:opacity-50 disabled:cursor-not-allowed"
                            >
                                <option value="" disabled selected>
                                    Update Status
                                </option>
                                <option
                                    v-for="status in getNextStatuses(order.status)"
                                    :key="status"
                                    :value="status"
                                    class="bg-white text-gray-900"
                                >
                                    Update to {{ getStatusLabel(status) }}
                                </option>
                            </select>
                            <!-- Dropdown arrow -->
                            <div class="absolute inset-y-0 right-0 flex items-center pr-2 pointer-events-none">
                                <svg class="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                                </svg>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </nav>

        <!-- Main Content -->
        <div class="max-w-6xl mx-auto py-6 px-4 sm:px-6 lg:px-8">
            <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
                <!-- Order Details -->
                <div class="lg:col-span-2 space-y-6">
                    <!-- Order Information -->
                    <div class="bg-white rounded-lg shadow-sm p-6">
                        <h2 class="text-lg font-semibold text-gray-900 mb-4">Order Information</h2>
                        
                        <div class="grid grid-cols-2 gap-4 text-sm">
                            <div>
                                <span class="text-gray-600">Order Number:</span>
                                <span class="ml-2 font-medium">{{ order.order_number }}</span>
                            </div>
                            <div>
                                <span class="text-gray-600">Order Type:</span>
                                <span class="ml-2 font-medium capitalize">{{ order.order_type }}</span>
                            </div>
                            <div>
                                <span class="text-gray-600">Table:</span>
                                <span class="ml-2 font-medium">{{ order.table?.name || 'N/A' }}</span>
                            </div>
                            <div>
                                <span class="text-gray-600">Guest Count:</span>
                                <span class="ml-2 font-medium">{{ order.guest_count || 1 }}</span>
                            </div>
                            <div>
                                <span class="text-gray-600">Waiter:</span>
                                <span class="ml-2 font-medium">{{ order.waiter?.name || order.taken_by?.name || 'N/A' }}</span>
                            </div>
                            <div>
                                <span class="text-gray-600">Created:</span>
                                <span class="ml-2 font-medium">{{ formatTime(order.created_at) }}</span>
                            </div>
                        </div>

                        <div v-if="order.customer" class="mt-4 pt-4 border-t border-gray-200">
                            <h3 class="font-medium text-gray-900 mb-2">Customer Information</h3>
                            <div class="text-sm text-gray-600">
                                <div>{{ order.customer.name }}</div>
                                <div v-if="order.customer.phone">{{ order.customer.phone }}</div>
                                <div v-if="order.customer.email">{{ order.customer.email }}</div>
                            </div>
                        </div>
                    </div>

                    <!-- Order Items -->
                    <div class="bg-white rounded-lg shadow-sm p-6">
                        <h2 class="text-lg font-semibold text-gray-900 mb-4">Order Items</h2>
                        
                        <div class="space-y-3">
                            <div
                                v-for="item in order.items"
                                :key="item.id"
                                class="flex items-center justify-between py-3 border-b border-gray-200 last:border-b-0"
                            >
                                <div class="flex-1">
                                    <h4 class="font-medium text-gray-900">{{ item.item_name }}</h4>
                                    <p v-if="item.special_instructions" class="text-sm text-gray-600 mt-1">
                                        Note: {{ item.special_instructions }}
                                    </p>
                                    <div class="flex items-center space-x-4 mt-1">
                                        <span class="text-sm text-gray-600">Qty: {{ item.quantity }}</span>
                                        <span class="text-sm text-gray-600">{{ formatPrice(item.item_price) }} each</span>
                                        <span
                                            v-if="item.status"
                                            :class="[
                                                'inline-flex px-2 py-1 rounded-full text-xs font-medium',
                                                item.status === 'pending' ? 'bg-yellow-100 text-yellow-800' :
                                                item.status === 'preparing' ? 'bg-orange-100 text-orange-800' :
                                                item.status === 'ready' ? 'bg-green-100 text-green-800' :
                                                'bg-gray-100 text-gray-800'
                                            ]"
                                        >
                                            {{ item.status }}
                                        </span>
                                    </div>
                                </div>
                                <div class="text-right">
                                    <div class="font-medium text-gray-900">{{ formatPrice(item.total_price) }}</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Payment & Actions -->
                <div class="space-y-6">
                    <!-- Order Summary -->
                    <div class="bg-white rounded-lg shadow-sm p-6">
                        <h2 class="text-lg font-semibold text-gray-900 mb-4">Order Summary</h2>
                        
                        <div class="space-y-2 text-sm">
                            <div class="flex justify-between">
                                <span class="text-gray-600">Subtotal:</span>
                                <span>{{ formatPrice(order.subtotal) }}</span>
                            </div>
                            <div v-if="order.delivery_charge" class="flex justify-between">
                                <span class="text-gray-600">Delivery:</span>
                                <span>{{ formatPrice(order.delivery_charge) }}</span>
                            </div>
                            <div v-if="order.discount_amount" class="flex justify-between text-green-600">
                                <span>Discount:</span>
                                <span>-{{ formatPrice(order.discount_amount) }}</span>
                            </div>
                            <div class="border-t border-gray-200 pt-2 flex justify-between font-semibold">
                                <span>Total:</span>
                                <span>{{ formatPrice(orderTotal) }}</span>
                            </div>
                        </div>
                    </div>

                    <!-- Payment Status -->
                    <div class="bg-white rounded-lg shadow-sm p-6">
                        <h2 class="text-lg font-semibold text-gray-900 mb-4">Payment Status</h2>
                        
                        <div class="space-y-2 text-sm mb-4">
                            <div class="flex justify-between">
                                <span class="text-gray-600">Total Amount:</span>
                                <span>{{ formatPrice(orderTotal) }}</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-gray-600">Paid Amount:</span>
                                <span>{{ formatPrice(paidAmount) }}</span>
                            </div>
                            <div class="border-t border-gray-200 pt-2 flex justify-between font-semibold">
                                <span>Remaining:</span>
                                <span :class="isFullyPaid ? 'text-green-600' : 'text-red-600'">
                                    {{ formatPrice(remainingBalance) }}
                                </span>
                            </div>
                        </div>

                        <div v-if="order.payments && order.payments.length > 0" class="mb-4">
                            <h3 class="font-medium text-gray-900 mb-2">Payment History</h3>
                            <div class="space-y-2">
                                <div
                                    v-for="payment in order.payments"
                                    :key="payment.id"
                                    class="flex justify-between text-sm"
                                >
                                    <span class="text-gray-600">{{ payment.payment_method }}</span>
                                    <span>{{ formatPrice(payment.amount) }}</span>
                                </div>
                            </div>
                        </div>

                        <button
                            v-if="!isFullyPaid"
                            @click="showPaymentModal = true"
                            class="w-full px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors"
                        >
                            Process Payment
                        </button>
                        
                        <div v-else class="text-center py-2">
                            <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-green-100 text-green-800">
                                ✓ Fully Paid
                            </span>
                        </div>
                    </div>

                    <!-- Actions -->
                    <div class="bg-white rounded-lg shadow-sm p-6">
                        <h2 class="text-lg font-semibold text-gray-900 mb-4">Actions</h2>
                        
                        <div class="space-y-3">
                            <button
                                @click="printReceipt"
                                class="w-full px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors flex items-center justify-center space-x-2"
                            >
                                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 17h2a2 2 0 002-2v-4a2 2 0 00-2-2H5a2 2 0 00-2 2v4a2 2 0 002 2h2m2 4h6a2 2 0 002-2v-4a2 2 0 00-2-2H9a2 2 0 00-2 2v4a2 2 0 002 2zm8-12V5a2 2 0 00-2-2H9a2 2 0 00-2 2v4h10z"></path>
                                </svg>
                                <span>Print Receipt</span>
                            </button>
                            
                            <button
                                v-if="order.status === 'pending'"
                                @click="editOrder"
                                class="w-full px-4 py-2 bg-orange-600 text-white rounded-lg hover:bg-orange-700 transition-colors flex items-center justify-center space-x-2"
                            >
                                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
                                </svg>
                                <span>Edit Order</span>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Payment Modal -->
        <PaymentModal
            :show="showPaymentModal"
            :order="order"
            :payment-methods="paymentMethods"
            :remaining-amount="remainingBalance"
            @close="showPaymentModal = false"
            @process-payment="processPayment"
        />
    </div>
</template>

<style scoped>
/* Custom styles for order show page */
</style>
