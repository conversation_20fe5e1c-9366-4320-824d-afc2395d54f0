<script setup>
import { Head } from '@inertiajs/vue3';
import { onMounted } from 'vue';

const props = defineProps({
    order: Object,
});

// Auto-print when component mounts
onMounted(() => {
    // Small delay to ensure content is rendered
    setTimeout(() => {
        window.print();
    }, 500);
});

// Methods
const formatPrice = (price) => {
    return new Intl.NumberFormat('en-US', {
        style: 'currency',
        currency: 'USD'
    }).format(price || 0);
};

const formatTime = (dateString) => {
    if (!dateString) return '';
    const date = new Date(dateString);
    return date.toLocaleString();
};
</script>

<template>
    <div class="print-container">
        <Head :title="`Print Order ${order.order_number}`" />

        <div class="receipt">
            <!-- Header -->
            <div class="header">
                <h1 class="restaurant-name">{{ order.branch?.name || 'Restaurant' }}</h1>
                <p class="restaurant-info">{{ order.branch?.address || '' }}</p>
                <p class="restaurant-info">{{ order.branch?.phone || '' }}</p>
                <div class="divider"></div>
            </div>

            <!-- Order Info -->
            <div class="order-info">
                <div class="info-row">
                    <span class="label">Order #:</span>
                    <span class="value">{{ order.order_number }}</span>
                </div>
                <div class="info-row">
                    <span class="label">Date:</span>
                    <span class="value">{{ formatTime(order.created_at) }}</span>
                </div>
                <div v-if="order.table" class="info-row">
                    <span class="label">Table:</span>
                    <span class="value">{{ order.table.name }}</span>
                </div>
                <div v-if="order.waiter || order.taken_by" class="info-row">
                    <span class="label">Waiter:</span>
                    <span class="value">{{ order.waiter?.name || order.taken_by?.name }}</span>
                </div>
                <div v-if="order.guest_count" class="info-row">
                    <span class="label">Guests:</span>
                    <span class="value">{{ order.guest_count }}</span>
                </div>
                <div class="divider"></div>
            </div>

            <!-- Customer Info -->
            <div v-if="order.customer" class="customer-info">
                <h3>Customer Information</h3>
                <div class="info-row">
                    <span class="label">Name:</span>
                    <span class="value">{{ order.customer.name }}</span>
                </div>
                <div v-if="order.customer.phone" class="info-row">
                    <span class="label">Phone:</span>
                    <span class="value">{{ order.customer.phone }}</span>
                </div>
                <div class="divider"></div>
            </div>

            <!-- Order Items -->
            <div class="items">
                <h3>Order Items</h3>
                <div
                    v-for="item in order.items"
                    :key="item.id"
                    class="item"
                >
                    <div class="item-header">
                        <span class="item-name">{{ item.item_name }}</span>
                        <span class="item-total">{{ formatPrice(item.total_price) }}</span>
                    </div>
                    <div class="item-details">
                        <span class="quantity">{{ item.quantity }} x {{ formatPrice(item.item_price) }}</span>
                    </div>
                    <div v-if="item.special_instructions" class="item-notes">
                        Note: {{ item.special_instructions }}
                    </div>
                </div>
                <div class="divider"></div>
            </div>

            <!-- Order Summary -->
            <div class="summary">
                <div class="summary-row">
                    <span class="label">Subtotal:</span>
                    <span class="value">{{ formatPrice(order.subtotal) }}</span>
                </div>
                <div v-if="order.delivery_charge" class="summary-row">
                    <span class="label">Delivery:</span>
                    <span class="value">{{ formatPrice(order.delivery_charge) }}</span>
                </div>
                <div v-if="order.discount_amount" class="summary-row">
                    <span class="label">Discount:</span>
                    <span class="value">-{{ formatPrice(order.discount_amount) }}</span>
                </div>
                <div class="divider"></div>
                <div class="summary-row total">
                    <span class="label">Total:</span>
                    <span class="value">{{ formatPrice(order.total_amount || order.grand_total) }}</span>
                </div>
            </div>

            <!-- Payment Info -->
            <div v-if="order.payments && order.payments.length > 0" class="payments">
                <div class="divider"></div>
                <h3>Payment Details</h3>
                <div
                    v-for="payment in order.payments"
                    :key="payment.id"
                    class="payment-row"
                >
                    <span class="payment-method">{{ payment.payment_method }}</span>
                    <span class="payment-amount">{{ formatPrice(payment.amount) }}</span>
                </div>
                
                <div class="divider"></div>
                <div class="payment-summary">
                    <div class="summary-row">
                        <span class="label">Paid:</span>
                        <span class="value">{{ formatPrice(order.payments.reduce((sum, p) => sum + parseFloat(p.amount), 0)) }}</span>
                    </div>
                    <div class="summary-row">
                        <span class="label">Balance:</span>
                        <span class="value">{{ formatPrice((order.total_amount || order.grand_total) - order.payments.reduce((sum, p) => sum + parseFloat(p.amount), 0)) }}</span>
                    </div>
                </div>
            </div>

            <!-- Footer -->
            <div class="footer">
                <div class="divider"></div>
                <p class="thank-you">Thank you for your visit!</p>
                <p class="footer-text">Please come again</p>
            </div>
        </div>
    </div>
</template>

<style scoped>
@media print {
    body {
        margin: 0;
        padding: 0;
    }
    
    .print-container {
        width: 100%;
        margin: 0;
        padding: 0;
    }
}

.print-container {
    max-width: 400px;
    margin: 0 auto;
    padding: 20px;
    font-family: 'Courier New', monospace;
    font-size: 12px;
    line-height: 1.4;
}

.receipt {
    background: white;
    padding: 20px;
    border: 1px solid #ddd;
}

.header {
    text-align: center;
    margin-bottom: 20px;
}

.restaurant-name {
    font-size: 18px;
    font-weight: bold;
    margin: 0 0 5px 0;
    text-transform: uppercase;
}

.restaurant-info {
    margin: 2px 0;
    font-size: 11px;
}

.divider {
    border-top: 1px dashed #333;
    margin: 10px 0;
}

.order-info,
.customer-info {
    margin-bottom: 15px;
}

.info-row,
.summary-row,
.payment-row {
    display: flex;
    justify-content: space-between;
    margin: 3px 0;
}

.label {
    font-weight: bold;
}

.value {
    text-align: right;
}

.items h3,
.customer-info h3,
.payments h3 {
    font-size: 14px;
    font-weight: bold;
    margin: 0 0 10px 0;
    text-transform: uppercase;
}

.item {
    margin-bottom: 10px;
}

.item-header {
    display: flex;
    justify-content: space-between;
    font-weight: bold;
}

.item-details {
    font-size: 11px;
    color: #666;
    margin-top: 2px;
}

.item-notes {
    font-size: 10px;
    color: #666;
    font-style: italic;
    margin-top: 2px;
}

.summary {
    margin-top: 15px;
}

.total {
    font-weight: bold;
    font-size: 14px;
    border-top: 1px solid #333;
    padding-top: 5px;
    margin-top: 5px;
}

.payments {
    margin-top: 15px;
}

.payment-row {
    margin: 3px 0;
}

.payment-method {
    text-transform: capitalize;
}

.payment-summary {
    margin-top: 10px;
    padding-top: 5px;
    border-top: 1px solid #333;
}

.footer {
    text-align: center;
    margin-top: 20px;
}

.thank-you {
    font-size: 14px;
    font-weight: bold;
    margin: 10px 0 5px 0;
}

.footer-text {
    font-size: 11px;
    margin: 5px 0;
}

/* Print-specific styles */
@media print {
    .print-container {
        max-width: none;
        padding: 0;
    }
    
    .receipt {
        border: none;
        padding: 10px;
    }
    
    .divider {
        border-top: 1px dashed #000;
    }
    
    .total {
        border-top: 1px solid #000;
    }
    
    .payment-summary {
        border-top: 1px solid #000;
    }
}
</style>
