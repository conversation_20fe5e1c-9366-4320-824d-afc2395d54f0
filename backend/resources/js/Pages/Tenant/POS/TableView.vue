<script setup>
import { Head, router } from '@inertiajs/vue3';
import { ref, computed } from 'vue';
import POSLayout from '@/Layouts/POSLayout.vue';
import BranchSelector from '@/Components/POS/BranchSelector.vue';

const props = defineProps({
    branch: Object,
    allBranches: {
        type: Array,
        default: () => []
    },
    selectedBranchId: Number,
    floors: Array,
});

// Reactive state
const selectedFloor = ref(props.floors[0]?.id || null);
const searchQuery = ref('');

// Computed properties
const filteredTables = computed(() => {
    if (!selectedFloor.value) return [];
    
    const floor = props.floors.find(f => f.id === selectedFloor.value);
    if (!floor) return [];
    
    let tables = floor.tables || [];
    
    // Filter by search query
    if (searchQuery.value) {
        tables = tables.filter(table => 
            table.name.toLowerCase().includes(searchQuery.value.toLowerCase()) ||
            table.number?.toString().includes(searchQuery.value)
        );
    }
    
    return tables;
});

const availableTables = computed(() => {
    return filteredTables.value.filter(table => table.status === 'available');
});

const occupiedTables = computed(() => {
    return filteredTables.value.filter(table => table.status === 'occupied');
});

const reservedTables = computed(() => {
    return filteredTables.value.filter(table => table.status === 'reserved');
});

const cleaningTables = computed(() => {
    return filteredTables.value.filter(table => table.status === 'cleaning');
});

// Methods
const selectFloor = (floorId) => {
    selectedFloor.value = floorId;
};

const getTableStatusColor = (status) => {
    switch (status) {
        case 'available':
            return 'bg-green-100 text-green-800 border-green-200 hover:bg-green-200';
        case 'occupied':
            return 'bg-red-100 text-red-800 border-red-200 hover:bg-red-200';
        case 'reserved':
            return 'bg-yellow-100 text-yellow-800 border-yellow-200 hover:bg-yellow-200';
        case 'cleaning':
            return 'bg-blue-100 text-blue-800 border-blue-200 hover:bg-blue-200';
        default:
            return 'bg-gray-100 text-gray-800 border-gray-200 hover:bg-gray-200';
    }
};

const getTableStatusIcon = (status) => {
    switch (status) {
        case 'available':
            return '✅';
        case 'occupied':
            return '🔴';
        case 'reserved':
            return '🟡';
        case 'cleaning':
            return '🧹';
        default:
            return '⚪';
    }
};

const handleTableClick = (table) => {
    if (table.status === 'available') {
        // Redirect to POS with table selected
        router.visit(route('pos.index'), {
            data: { table_id: table.id },
            preserveState: true
        });
    } else if (table.status === 'occupied' && table.orders?.length > 0) {
        // Show current order for occupied table
        const currentOrder = table.orders[0];
        router.visit(route('pos.index'), {
            data: { order_id: currentOrder.id },
            preserveState: true
        });
    }
};

const formatTime = (dateString) => {
    if (!dateString) return '';
    return new Date(dateString).toLocaleTimeString();
};

const formatPrice = (price) => {
    return new Intl.NumberFormat('en-US', {
        style: 'currency',
        currency: 'USD'
    }).format(price || 0);
};
</script>

<template>
    <div class="min-h-screen bg-gray-100">
        <Head :title="`Table View - ${branch.name}`" />

        <!-- Single Modern Navigation Header -->
        <nav class="bg-white shadow-sm border-b border-gray-200">
            <div class="px-6 py-4">
                <div class="flex items-center justify-between">
                    <!-- Navigation Tabs -->
                    <div class="flex space-x-1">
                        <button
                            @click="router.visit(route('dashboard'))"
                            class="px-4 py-2 text-sm font-medium text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded-lg transition-colors flex items-center space-x-2"
                        >
                            <span class="text-green-600">🏠</span>
                            <span>Home</span>
                        </button>

                        <button
                            @click="router.visit(route('pos.index'))"
                            class="px-4 py-2 text-sm font-medium text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded-lg transition-colors flex items-center space-x-2"
                        >
                            <span>📝</span>
                            <span>New Order</span>
                        </button>

                        <button
                            @click="router.visit(route('pos.ongoing-orders'))"
                            class="px-4 py-2 text-sm font-medium text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded-lg transition-colors flex items-center space-x-2"
                        >
                            <span>🔄</span>
                            <span>On Going Order</span>
                        </button>

                        <button
                            @click="router.visit(route('kitchen.index'))"
                            class="px-4 py-2 text-sm font-medium text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded-lg transition-colors flex items-center space-x-2"
                        >
                            <span class="text-purple-600">👨‍🍳</span>
                            <span>Kitchen Status</span>
                        </button>

                        <button
                            class="px-4 py-2 text-sm font-medium text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded-lg transition-colors flex items-center space-x-2"
                        >
                            <span class="text-orange-600">📱</span>
                            <span>QR Order</span>
                        </button>

                        <button
                            class="px-4 py-2 text-sm font-medium text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded-lg transition-colors flex items-center space-x-2"
                        >
                            <span class="text-yellow-600">➕</span>
                            <span>Create Order</span>
                        </button>

                        <button
                            class="px-4 py-2 text-sm font-medium bg-blue-600 text-white rounded-lg flex items-center space-x-2"
                        >
                            <span class="text-white">🪑</span>
                            <span>Table View</span>
                        </button>
                    </div>

                    <!-- Right side controls -->
                    <div class="flex items-center space-x-4">
                        <div class="flex items-center space-x-2 text-sm text-gray-600">
                            <span>🌐</span>
                            <span>ENG</span>
                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                            </svg>
                        </div>

                        <button class="p-2 text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded-lg transition-colors">
                            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"></path>
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                            </svg>
                        </button>

                        <button class="p-2 text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded-lg transition-colors">
                            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"></path>
                            </svg>
                        </button>

                        <button class="px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors">
                            Merge Order
                        </button>
                    </div>
                </div>
            </div>
        </nav>

        <!-- Main Content -->
        <div class="max-w-full mx-auto py-6 px-4 sm:px-6 lg:px-8">
            <!-- Branch Selection Header -->
            <div class="bg-white rounded-lg shadow-sm p-6 mb-6">
                <div class="flex items-center justify-between">
                    <div class="flex items-center space-x-6">
                        <div class="flex items-center space-x-3">
                            <div class="w-10 h-10 bg-blue-600 rounded-lg flex items-center justify-center">
                                <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"></path>
                                </svg>
                            </div>
                            <div>
                                <h3 class="text-lg font-semibold text-gray-900">Table Management</h3>
                                <p class="text-sm text-gray-600">View and manage table status</p>
                            </div>
                        </div>

                        <!-- Branch Selector -->
                        <div class="min-w-[250px]">
                            <BranchSelector
                                :branches="allBranches"
                                :selected-branch-id="selectedBranchId"
                                size="md"
                            />
                        </div>
                    </div>

                    <!-- Table Statistics -->
                    <div class="flex items-center space-x-6">
                        <div class="text-center">
                            <div class="text-2xl font-bold text-green-600">{{ availableTables.length }}</div>
                            <div class="text-xs text-gray-600">Available</div>
                        </div>
                        <div class="text-center">
                            <div class="text-2xl font-bold text-red-600">{{ occupiedTables.length }}</div>
                            <div class="text-xs text-gray-600">Occupied</div>
                        </div>
                        <div class="text-center">
                            <div class="text-2xl font-bold text-yellow-600">{{ reservedTables.length }}</div>
                            <div class="text-xs text-gray-600">Reserved</div>
                        </div>
                        <div class="text-center">
                            <div class="text-2xl font-bold text-blue-600">{{ cleaningTables.length }}</div>
                            <div class="text-xs text-gray-600">Cleaning</div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Floor Selection and Search -->
        <div class="bg-white rounded-lg shadow-sm mb-6">
            <!-- Floor Tabs -->
            <div class="border-b border-gray-200">
                <div class="flex items-center justify-between px-6 py-4">
                    <div class="flex space-x-1">
                        <button
                            v-for="floor in floors"
                            :key="floor.id"
                            @click="selectFloor(floor.id)"
                            :class="[
                                'px-6 py-3 text-sm font-medium rounded-lg transition-colors',
                                selectedFloor === floor.id
                                    ? 'bg-blue-600 text-white'
                                    : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                            ]"
                        >
                            {{ floor.name }}
                        </button>
                    </div>

                    <!-- Search -->
                    <div class="relative">
                        <input
                            v-model="searchQuery"
                            type="text"
                            placeholder="Type and Select Order"
                            class="w-80 pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                        />
                        <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                            <svg class="h-5 w-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                            </svg>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Tables Grid -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            <div
                v-for="table in filteredTables"
                :key="table.id"
                @click="handleTableClick(table)"
                class="bg-white rounded-lg shadow-sm border border-gray-200 hover:shadow-md transition-shadow cursor-pointer"
            >
                <!-- Table Header -->
                <div class="p-4 border-b border-gray-200">
                    <div class="flex items-center justify-between mb-2">
                        <div class="flex items-center space-x-2">
                            <input type="checkbox" class="rounded border-gray-300 text-blue-600 focus:ring-blue-500">
                            <span class="text-lg font-semibold text-gray-900">Select This Table</span>
                        </div>
                    </div>

                    <div class="text-center">
                        <div class="text-6xl mb-2">🪑</div>
                        <div class="text-xl font-bold text-gray-900">Table {{ table.name }}</div>
                        <div class="text-sm text-gray-600">Seat: {{ table.capacity }}</div>
                        <div class="text-sm text-gray-600">Available: {{ table.capacity }}</div>
                    </div>
                </div>

                <!-- Table Details -->
                <div class="p-4">
                    <div class="grid grid-cols-4 gap-4 text-center text-sm">
                        <div>
                            <div class="font-medium text-gray-900">ORDER</div>
                            <div class="text-gray-600">{{ table.orders?.length || 0 }}</div>
                        </div>
                        <div>
                            <div class="font-medium text-gray-900">TIME</div>
                            <div class="text-gray-600">{{ table.orders?.[0] ? formatTime(table.orders[0].created_at) : '-' }}</div>
                        </div>
                        <div>
                            <div class="font-medium text-gray-900">PERSON</div>
                            <div class="text-gray-600">{{ table.orders?.[0]?.guest_count || '-' }}</div>
                        </div>
                        <div>
                            <div class="font-medium text-gray-900">ACTION</div>
                            <div class="text-gray-600">
                                <button
                                    v-if="table.status === 'available'"
                                    class="px-2 py-1 bg-green-100 text-green-800 rounded text-xs"
                                >
                                    Available
                                </button>
                                <button
                                    v-else-if="table.status === 'occupied'"
                                    class="px-2 py-1 bg-red-100 text-red-800 rounded text-xs"
                                >
                                    Occupied
                                </button>
                                <button
                                    v-else
                                    class="px-2 py-1 bg-gray-100 text-gray-800 rounded text-xs"
                                >
                                    {{ table.status }}
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- Customer Info -->
                    <div v-if="table.orders?.[0]" class="mt-4 pt-4 border-t border-gray-200 text-center">
                        <div class="text-sm text-gray-600">{{ table.orders[0].customer_name || 'No Customer' }}</div>
                    </div>

                    <!-- Action Buttons -->
                    <div class="mt-4 flex space-x-2">
                        <button
                            v-if="table.status === 'available'"
                            @click.stop=""
                            class="flex-1 px-3 py-2 bg-blue-600 text-white text-sm rounded-lg hover:bg-blue-700 transition-colors"
                        >
                            Person
                        </button>

                        <button
                            @click.stop=""
                            class="px-3 py-2 bg-gray-100 text-gray-700 text-sm rounded-lg hover:bg-gray-200 transition-colors"
                        >
                            +
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Empty State -->
        <div v-if="filteredTables.length === 0" class="bg-white rounded-lg shadow-sm p-12">
            <div class="text-center">
                <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"></path>
                </svg>
                <h3 class="mt-2 text-sm font-medium text-gray-900">No tables found</h3>
                <p class="mt-1 text-sm text-gray-500">
                    {{ searchQuery ? 'No tables match your search criteria.' : 'No tables available on this floor.' }}
                </p>
            </div>
        </div>
        </div>
    </div>
</template>

<style scoped>
/* Custom hover effects for tables */
.transform:hover {
    box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
}
</style>
