<template>
    <ManagerLayout>
        <template #header>
            <div class="flex justify-between items-center">
                <h1 class="text-2xl font-bold text-gray-900 dark:text-white">
                    📄 {{ page.title }}
                </h1>
                
                <div class="flex items-center space-x-4">
                    <button
                        @click="togglePublish"
                        :class="[
                            'px-4 py-2 text-white rounded-lg transition-colors',
                            page.is_published
                                ? 'bg-yellow-600 hover:bg-yellow-700'
                                : 'bg-green-600 hover:bg-green-700'
                        ]"
                    >
                        <i :class="page.is_published ? 'fas fa-eye-slash' : 'fas fa-eye'" class="mr-2"></i>
                        {{ page.is_published ? 'Unpublish' : 'Publish' }}
                    </button>
                    
                    <Link
                        :href="route('pages.edit', page.id)"
                        class="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
                    >
                        <i class="fas fa-edit mr-2"></i>
                        Edit Page
                    </Link>
                    
                    <Link
                        :href="route('pages.index')"
                        class="px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors"
                    >
                        <i class="fas fa-arrow-left mr-2"></i>
                        Back to Pages
                    </Link>
                </div>
            </div>
        </template>

        <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
            <!-- Main Content -->
            <div class="lg:col-span-2 space-y-6">
                <!-- Page Preview -->
                <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm overflow-hidden">
                    <!-- Banner Images -->
                    <div v-if="page.media && page.media.length > 0" class="aspect-video bg-gray-200 dark:bg-gray-700">
                        <div v-if="page.media.length === 1">
                            <img
                                :src="page.media[0].file_url"
                                :alt="page.title"
                                class="w-full h-full object-cover"
                            >
                        </div>
                        <div v-else class="grid grid-cols-2 gap-2 h-full p-2">
                            <img
                                v-for="(media, index) in page.media.slice(0, 4)"
                                :key="media.id"
                                :src="media.file_url"
                                :alt="`${page.title} - Image ${index + 1}`"
                                class="w-full h-full object-cover rounded"
                            >
                        </div>
                    </div>
                    
                    <!-- Content -->
                    <div class="p-6">
                        <h1 class="text-3xl font-bold text-gray-900 dark:text-white mb-4">
                            {{ page.title }}
                        </h1>
                        
                        <div
                            v-if="page.content"
                            class="prose prose-lg max-w-none dark:prose-invert"
                            v-html="page.content"
                        ></div>
                        
                        <div v-else class="text-gray-500 dark:text-gray-400 italic">
                            No content available
                        </div>
                    </div>
                </div>

                <!-- SEO Preview -->
                <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm p-6">
                    <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-4">
                        Search Engine Preview
                    </h3>
                    
                    <div class="border border-gray-200 dark:border-gray-600 rounded-lg p-4 bg-gray-50 dark:bg-gray-700">
                        <div class="text-blue-600 dark:text-blue-400 text-lg font-medium hover:underline cursor-pointer">
                            {{ page.meta_title || page.title }}
                        </div>
                        <div class="text-green-600 dark:text-green-400 text-sm mt-1">
                            {{ window.location.origin }}/pages/{{ page.slug }}
                        </div>
                        <div class="text-gray-600 dark:text-gray-300 text-sm mt-2">
                            {{ page.meta_description || page.excerpt || 'No description available' }}
                        </div>
                    </div>
                </div>
            </div>

            <!-- Sidebar -->
            <div class="space-y-6">
                <!-- Page Status -->
                <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm p-6">
                    <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-4">
                        Page Status
                    </h3>
                    
                    <div class="space-y-3">
                        <div class="flex justify-between items-center">
                            <span class="text-gray-600 dark:text-gray-400">Status:</span>
                            <span
                                :class="[
                                    'px-2 py-1 rounded-full text-xs font-medium',
                                    page.is_published
                                        ? 'bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400'
                                        : 'bg-gray-100 text-gray-800 dark:bg-gray-900/30 dark:text-gray-400'
                                ]"
                            >
                                <i :class="page.is_published ? 'fas fa-eye' : 'fas fa-eye-slash'" class="mr-1"></i>
                                {{ page.is_published ? 'Published' : 'Draft' }}
                            </span>
                        </div>
                        
                        <div class="flex justify-between items-center">
                            <span class="text-gray-600 dark:text-gray-400">Featured:</span>
                            <span class="text-gray-900 dark:text-white">
                                <i :class="page.is_featured ? 'fas fa-star text-yellow-500' : 'far fa-star text-gray-400'"></i>
                                {{ page.is_featured ? 'Yes' : 'No' }}
                            </span>
                        </div>
                        
                        <div class="flex justify-between items-center">
                            <span class="text-gray-600 dark:text-gray-400">In Menu:</span>
                            <span class="text-gray-900 dark:text-white">
                                <i :class="page.show_in_menu ? 'fas fa-check text-green-500' : 'fas fa-times text-red-500'"></i>
                                {{ page.show_in_menu ? 'Yes' : 'No' }}
                            </span>
                        </div>
                        
                        <div class="flex justify-between items-center">
                            <span class="text-gray-600 dark:text-gray-400">Template:</span>
                            <span class="text-gray-900 dark:text-white">{{ page.template }}</span>
                        </div>
                        
                        <div class="flex justify-between items-center">
                            <span class="text-gray-600 dark:text-gray-400">Sort Order:</span>
                            <span class="text-gray-900 dark:text-white">{{ page.sort_order }}</span>
                        </div>
                    </div>
                </div>

                <!-- Page Information -->
                <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm p-6">
                    <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-4">
                        Page Information
                    </h3>
                    
                    <div class="space-y-3 text-sm">
                        <div class="flex justify-between">
                            <span class="text-gray-600 dark:text-gray-400">Branch:</span>
                            <span class="text-gray-900 dark:text-white">{{ page.branch?.name }}</span>
                        </div>
                        
                        <div class="flex justify-between">
                            <span class="text-gray-600 dark:text-gray-400">Created:</span>
                            <span class="text-gray-900 dark:text-white">{{ formatDate(page.created_at) }}</span>
                        </div>
                        
                        <div class="flex justify-between">
                            <span class="text-gray-600 dark:text-gray-400">Updated:</span>
                            <span class="text-gray-900 dark:text-white">{{ formatDate(page.updated_at) }}</span>
                        </div>
                        
                        <div v-if="page.published_at" class="flex justify-between">
                            <span class="text-gray-600 dark:text-gray-400">Published:</span>
                            <span class="text-gray-900 dark:text-white">{{ formatDate(page.published_at) }}</span>
                        </div>
                        
                        <div class="flex justify-between">
                            <span class="text-gray-600 dark:text-gray-400">Created by:</span>
                            <span class="text-gray-900 dark:text-white">{{ page.creator?.name }}</span>
                        </div>
                        
                        <div v-if="page.updater" class="flex justify-between">
                            <span class="text-gray-600 dark:text-gray-400">Updated by:</span>
                            <span class="text-gray-900 dark:text-white">{{ page.updater?.name }}</span>
                        </div>
                    </div>
                </div>

                <!-- SEO Information -->
                <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm p-6">
                    <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-4">
                        SEO Information
                    </h3>
                    
                    <div class="space-y-3 text-sm">
                        <div>
                            <span class="text-gray-600 dark:text-gray-400 block mb-1">Meta Title:</span>
                            <span class="text-gray-900 dark:text-white">
                                {{ page.meta_title || 'Not set' }}
                            </span>
                            <div v-if="page.meta_title" class="text-xs text-gray-500 dark:text-gray-400 mt-1">
                                {{ page.meta_title.length }}/60 characters
                            </div>
                        </div>
                        
                        <div>
                            <span class="text-gray-600 dark:text-gray-400 block mb-1">Meta Description:</span>
                            <span class="text-gray-900 dark:text-white">
                                {{ page.meta_description || 'Not set' }}
                            </span>
                            <div v-if="page.meta_description" class="text-xs text-gray-500 dark:text-gray-400 mt-1">
                                {{ page.meta_description.length }}/160 characters
                            </div>
                        </div>
                        
                        <div>
                            <span class="text-gray-600 dark:text-gray-400 block mb-1">URL Slug:</span>
                            <span class="text-gray-900 dark:text-white font-mono text-xs bg-gray-100 dark:bg-gray-700 px-2 py-1 rounded">
                                /pages/{{ page.slug }}
                            </span>
                        </div>
                    </div>
                </div>

                <!-- Quick Actions -->
                <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm p-6">
                    <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-4">
                        Quick Actions
                    </h3>
                    
                    <div class="space-y-3">
                        <button
                            @click="duplicatePage"
                            class="w-full px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors"
                        >
                            <i class="fas fa-copy mr-2"></i>
                            Duplicate Page
                        </button>
                        
                        <button
                            @click="viewPublicPage"
                            v-if="page.is_published"
                            class="w-full px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors"
                        >
                            <i class="fas fa-external-link-alt mr-2"></i>
                            View Public Page
                        </button>
                        
                        <button
                            @click="deletePage"
                            class="w-full px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors"
                        >
                            <i class="fas fa-trash mr-2"></i>
                            Delete Page
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </ManagerLayout>
</template>

<script setup>
import { router, Link } from '@inertiajs/vue3'
import ManagerLayout from '@/Layouts/ManagerLayout.vue'

const props = defineProps({
    page: Object,
})

// Methods
const togglePublish = () => {
    router.post(route('pages.toggle-publish', props.page.id), {}, {
        preserveState: true,
        preserveScroll: true,
    })
}

const duplicatePage = () => {
    router.post(route('pages.duplicate', props.page.id))
}

const viewPublicPage = () => {
    window.open(`/pages/${props.page.slug}`, '_blank')
}

const deletePage = () => {
    if (confirm('Are you sure you want to delete this page? This action cannot be undone.')) {
        router.delete(route('pages.destroy', props.page.id))
    }
}

const formatDate = (date) => {
    return new Date(date).toLocaleDateString()
}
</script>

<style scoped>
.prose {
    max-width: none;
}

.prose h1,
.prose h2,
.prose h3,
.prose h4,
.prose h5,
.prose h6 {
    color: inherit;
}

.prose p {
    margin-bottom: 1rem;
}

.prose img {
    border-radius: 0.5rem;
}
</style>
