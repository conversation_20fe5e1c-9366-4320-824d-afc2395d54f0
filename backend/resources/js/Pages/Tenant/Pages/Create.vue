<template>
    <ManagerLayout>
        <template #header>
            <div class="flex justify-between items-center">
                <h1 class="text-2xl font-bold text-gray-900 dark:text-white">
                    📄 Create New Page
                </h1>
                
                <div class="flex items-center space-x-4">
                    <Link
                        :href="route('pages.index')"
                        class="px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors"
                    >
                        <i class="fas fa-arrow-left mr-2"></i>
                        {{ $t('common.back') }}
                    </Link>
                </div>
            </div>
        </template>

        <form @submit.prevent="submit" class="space-y-6">
            <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
                <!-- Main Content -->
                <div class="lg:col-span-2 space-y-6">
                    <!-- Basic Information -->
                    <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm p-6">
                        <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-4">
                            {{ $t('basic_info') }}
                        </h3>

                        <div class="space-y-4">
                            <div>
                                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                                    {{ $t('pages.fields.title') }} *
                                </label>
                                <input
                                    v-model="form.title"
                                    type="text"
                                    required
                                    class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white"
                                    :placeholder="$t('pages.placeholders.title')"
                                    @input="generateSlug"
                                >
                                <div v-if="errors.title" class="mt-1 text-sm text-red-600">{{ errors.title }}</div>
                            </div>

                            <div>
                                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                                    {{ $t('pages.fields.slug') }}
                                </label>
                                <div class="flex">
                                    <span class="inline-flex items-center px-3 rounded-l-lg border border-r-0 border-gray-300 dark:border-gray-600 bg-gray-50 dark:bg-gray-700 text-gray-500 dark:text-gray-400 text-sm">
                                        /pages/
                                    </span>
                                    <input
                                        v-model="form.slug"
                                        type="text"
                                        class="flex-1 px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-r-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white"
                                        :placeholder="$t('pages.placeholders.slug')"
                                    >
                                </div>
                                <div v-if="errors.slug" class="mt-1 text-sm text-red-600">{{ errors.slug }}</div>
                                <p class="mt-1 text-sm text-gray-500 dark:text-gray-400">
                                    {{ $t('pages.help.slug') }}
                                </p>
                            </div>



                            <div>
                                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                                    {{ $t('pages.fields.template') }}
                                </label>
                                <select
                                    v-model="form.template"
                                    class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white"
                                >
                                    <option v-for="(label, value) in templates" :key="value" :value="value">
                                        {{ label }}
                                    </option>
                                </select>
                                <div v-if="errors.template" class="mt-1 text-sm text-red-600">{{ errors.template }}</div>
                            </div>
                        </div>
                    </div>

                    <!-- Banner Images -->
                    <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm p-6">
                        <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-4">
                            Banner Images
                        </h3>

                        <MultipleImageUpload
                            v-model="form.media_ids"
                            :max-images="5"
                            :existing-images="[]"
                        />
                        <div v-if="errors.media_ids" class="mt-1 text-sm text-red-600">{{ errors.media_ids }}</div>
                    </div>

                    <!-- Content -->
                    <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm p-6">
                        <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-4">
                            {{ $t('pages.sections.content') }}
                        </h3>

                        <TinyMCEEditor
                            v-model="form.content"
                            name="content"
                            :label="$t('pages.fields.content')"
                            :placeholder="$t('pages.placeholders.content')"
                            :height="500"
                            :error="errors.content"
                            :help="$t('pages.help.content')"
                            :config="editorConfig"
                            @ready="onEditorReady"
                            @change="onContentChange"
                        />
                    </div>

                    <!-- SEO Settings -->
                    <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm p-6">
                        <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-4">
                            {{ $t('pages.sections.seo') }}
                        </h3>

                        <div class="space-y-4">
                            <div>
                                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                                    {{ $t('pages.fields.meta_title') }}
                                </label>
                                <input
                                    v-model="form.meta_title"
                                    type="text"
                                    maxlength="60"
                                    class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white"
                                    :placeholder="$t('pages.placeholders.meta_title')"
                                >
                                <div class="mt-1 flex justify-between text-sm">
                                    <div v-if="errors.meta_title" class="text-red-600">{{ errors.meta_title }}</div>
                                    <div class="text-gray-500 dark:text-gray-400 ml-auto">
                                        {{ (form.meta_title || '').length }}/60
                                    </div>
                                </div>
                            </div>

                            <div>
                                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                                    {{ $t('pages.fields.meta_description') }}
                                </label>
                                <textarea
                                    v-model="form.meta_description"
                                    rows="3"
                                    maxlength="160"
                                    class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white"
                                    :placeholder="$t('pages.placeholders.meta_description')"
                                ></textarea>
                                <div class="mt-1 flex justify-between text-sm">
                                    <div v-if="errors.meta_description" class="text-red-600">{{ errors.meta_description }}</div>
                                    <div class="text-gray-500 dark:text-gray-400 ml-auto">
                                        {{ (form.meta_description || '').length }}/160
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Sidebar -->
                <div class="space-y-6">
                    <!-- Publishing Options -->
                    <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm p-6">
                        <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-4">
                            {{ $t('pages.sections.publishing') }}
                        </h3>

                        <div class="space-y-4">
                            <div class="flex items-center">
                                <input
                                    v-model="form.is_published"
                                    type="checkbox"
                                    class="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                                >
                                <label class="ml-2 text-sm text-gray-700 dark:text-gray-300">
                                    {{ $t('pages.fields.is_published') }}
                                </label>
                            </div>

                            <div class="flex items-center">
                                <input
                                    v-model="form.is_featured"
                                    type="checkbox"
                                    class="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                                >
                                <label class="ml-2 text-sm text-gray-700 dark:text-gray-300">
                                    {{ $t('pages.fields.is_featured') }}
                                </label>
                            </div>

                            <div class="flex items-center">
                                <input
                                    v-model="form.show_in_menu"
                                    type="checkbox"
                                    class="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                                >
                                <label class="ml-2 text-sm text-gray-700 dark:text-gray-300">
                                    {{ $t('pages.fields.show_in_menu') }}
                                </label>
                            </div>

                            <div>
                                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                                    {{ $t('pages.fields.sort_order') }}
                                </label>
                                <input
                                    v-model.number="form.sort_order"
                                    type="number"
                                    min="0"
                                    class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white"
                                >
                                <div v-if="errors.sort_order" class="mt-1 text-sm text-red-600">{{ errors.sort_order }}</div>
                            </div>
                        </div>
                    </div>

                    <!-- Actions -->
                    <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm p-6">
                        <div class="space-y-3">
                            <button
                                type="submit"
                                :disabled="processing"
                                class="w-full px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
                            >
                                <i v-if="processing" class="fas fa-spinner fa-spin mr-2"></i>
                                <i v-else class="fas fa-save mr-2"></i>
                                {{ processing ? $t('common.saving') : $t('pages.actions.create') }}
                            </button>

                            <button
                                type="button"
                                @click="saveDraft"
                                :disabled="processing"
                                class="w-full px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
                            >
                                <i class="fas fa-file-alt mr-2"></i>
                                {{ $t('pages.actions.save_draft') }}
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </form>
    </ManagerLayout>
</template>

<script setup>
import { ref, watch } from 'vue'
import { Link, useForm } from '@inertiajs/vue3'
import { debounce } from 'lodash'
import ManagerLayout from '@/Layouts/ManagerLayout.vue'
import MultipleImageUpload from '@/Components/MenuItems/MultipleImageUpload.vue'
import TinyMCEEditor from '@/Components/TinyMCEEditor.vue'

const props = defineProps({
    templates: Object,
    errors: Object,
})

// Form data
const form = useForm({
    title: '',
    slug: '',
    content: '',
    meta_title: '',
    meta_description: '',
    is_published: false,
    is_featured: false,
    show_in_menu: false,
    sort_order: 0,
    template: 'default',
    media_ids: [],
})

const processing = ref(false)

// TinyMCE Editor Configuration
const editorConfig = {
    plugins: [
        'advlist', 'autolink', 'lists', 'link', 'image', 'charmap', 'preview',
        'anchor', 'searchreplace', 'visualblocks', 'code', 'fullscreen',
        'insertdatetime', 'media', 'table', 'help', 'wordcount', 'emoticons'
    ],
    toolbar: 'undo redo | blocks | bold italic forecolor backcolor | alignleft aligncenter alignright alignjustify | bullist numlist outdent indent | removeformat | link image media | table | code fullscreen | help',
    menubar: 'file edit view insert format tools table help',
    height: 500,
    content_style: `
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            font-size: 14px;
            line-height: 1.6;
            color: #374151;
            max-width: none;
        }
        img { max-width: 100%; height: auto; }
        table { border-collapse: collapse; width: 100%; }
        table td, table th { border: 1px solid #ddd; padding: 8px; }
    `,
    branding: false,
    promotion: false,
    license_key: 'gpl',
    // Image upload configuration
    images_upload_url: '/api/upload-image',
    images_upload_credentials: true,
    automatic_uploads: true,
    file_picker_types: 'image',
    // Custom styles
    style_formats: [
        { title: 'Headings', items: [
            { title: 'Heading 1', format: 'h1' },
            { title: 'Heading 2', format: 'h2' },
            { title: 'Heading 3', format: 'h3' },
            { title: 'Heading 4', format: 'h4' },
            { title: 'Heading 5', format: 'h5' },
            { title: 'Heading 6', format: 'h6' }
        ]},
        { title: 'Inline', items: [
            { title: 'Bold', format: 'bold' },
            { title: 'Italic', format: 'italic' },
            { title: 'Underline', format: 'underline' },
            { title: 'Strikethrough', format: 'strikethrough' },
            { title: 'Superscript', format: 'superscript' },
            { title: 'Subscript', format: 'subscript' },
            { title: 'Code', format: 'code' }
        ]},
        { title: 'Blocks', items: [
            { title: 'Paragraph', format: 'p' },
            { title: 'Blockquote', format: 'blockquote' },
            { title: 'Div', format: 'div' },
            { title: 'Pre', format: 'pre' }
        ]},
        { title: 'Alignment', items: [
            { title: 'Left', format: 'alignleft' },
            { title: 'Center', format: 'aligncenter' },
            { title: 'Right', format: 'alignright' },
            { title: 'Justify', format: 'alignjustify' }
        ]}
    ]
}

// Methods
const generateSlug = debounce(() => {
    if (form.title && !form.slug) {
        // Generate slug from title
        axios.post(route('pages.generate-slug'), {
            title: form.title
        }).then(response => {
            form.slug = response.data.slug
        }).catch(() => {
            // Fallback slug generation
            form.slug = form.title
                .toLowerCase()
                .replace(/[^a-z0-9]+/g, '-')
                .replace(/^-+|-+$/g, '')
        })
    }
}, 300)

const submit = () => {
    form.is_published = true
    form.post(route('pages.store'))
}

const saveDraft = () => {
    form.is_published = false
    form.post(route('pages.store'))
}

// TinyMCE Event Handlers
const onEditorReady = (editor) => {
    console.log('TinyMCE editor is ready:', editor)
}

const onContentChange = (content) => {
    // Content is automatically updated via v-model
    console.log('Content changed:', content.length, 'characters')
}

// Auto-generate meta title from title if not set
watch(() => form.title, (newTitle) => {
    if (newTitle && !form.meta_title) {
        form.meta_title = newTitle
    }
})
</script>
