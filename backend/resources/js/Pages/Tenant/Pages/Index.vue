<template>
    <ManagerLayout>
        <template #header>
            <div class="flex justify-between items-center">
                <h1 class="text-2xl font-bold text-gray-900 dark:text-white">
                    📄 {{ $t('pages.title') }}
                </h1>
                
                <div class="flex items-center space-x-4">
                    <Link
                        :href="route('pages.create')"
                        class="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
                    >
                        <i class="fas fa-plus mr-2"></i>
                        {{ $t('pages.create.title') }}
                    </Link>
                </div>
            </div>
        </template>

        <!-- Statistics Cards -->
        <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-6">
            <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm p-6">
                <div class="flex items-center">
                    <div class="p-3 rounded-full bg-blue-100 dark:bg-blue-900/30">
                        <i class="fas fa-file-alt text-blue-600 dark:text-blue-400"></i>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-600 dark:text-gray-400">{{ $t('pages.stats.total') }}</p>
                        <p class="text-2xl font-semibold text-gray-900 dark:text-white">{{ stats?.total_pages || 0 }}</p>
                    </div>
                </div>
            </div>

            <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm p-6">
                <div class="flex items-center">
                    <div class="p-3 rounded-full bg-green-100 dark:bg-green-900/30">
                        <i class="fas fa-eye text-green-600 dark:text-green-400"></i>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-600 dark:text-gray-400">{{ $t('pages.stats.published') }}</p>
                        <p class="text-2xl font-semibold text-gray-900 dark:text-white">{{ stats?.published_pages || 0 }}</p>
                    </div>
                </div>
            </div>

            <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm p-6">
                <div class="flex items-center">
                    <div class="p-3 rounded-full bg-yellow-100 dark:bg-yellow-900/30">
                        <i class="fas fa-edit text-yellow-600 dark:text-yellow-400"></i>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-600 dark:text-gray-400">{{ $t('pages.stats.drafts') }}</p>
                        <p class="text-2xl font-semibold text-gray-900 dark:text-white">{{ stats?.draft_pages || 0 }}</p>
                    </div>
                </div>
            </div>

            <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm p-6">
                <div class="flex items-center">
                    <div class="p-3 rounded-full bg-purple-100 dark:bg-purple-900/30">
                        <i class="fas fa-star text-purple-600 dark:text-purple-400"></i>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-600 dark:text-gray-400">{{ $t('pages.stats.featured') }}</p>
                        <p class="text-2xl font-semibold text-gray-900 dark:text-white">{{ stats?.featured_pages || 0 }}</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Filters -->
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm p-6 mb-6">
            <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
                <div>
                    <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                        {{ $t('common.search') }}
                    </label>
                    <input
                        v-model="filters.search"
                        type="text"
                        :placeholder="$t('pages.search_placeholder')"
                        class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white"
                        @input="debouncedSearch"
                    >
                </div>

                <div>
                    <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                        {{ $t('common.status') }}
                    </label>
                    <select
                        v-model="filters.status"
                        class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white"
                        @change="applyFilters"
                    >
                        <option value="">{{ $t('common.all') }}</option>
                        <option value="published">{{ $t('pages.status.published') }}</option>
                        <option value="draft">{{ $t('pages.status.draft') }}</option>
                    </select>
                </div>



                <div>
                    <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                        {{ $t('pages.template') }}
                    </label>
                    <select
                        v-model="filters.template"
                        class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white"
                        @change="applyFilters"
                    >
                        <option value="">{{ $t('common.all') }}</option>
                        <option v-for="(label, value) in templates" :key="value" :value="value">
                            {{ label }}
                        </option>
                    </select>
                </div>
            </div>
        </div>

        <!-- Pages Table -->
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm overflow-hidden">
            <div class="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
                <div class="flex justify-between items-center">
                    <h3 class="text-lg font-medium text-gray-900 dark:text-white">
                        {{ $t('pages.list.title') }}
                    </h3>
                    
                    <div class="flex items-center space-x-2">
                        <button
                            v-if="selectedPages.length > 0"
                            @click="showBulkActions = !showBulkActions"
                            class="px-3 py-2 text-sm bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 rounded-lg hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors"
                        >
                            <i class="fas fa-cog mr-1"></i>
                            {{ $t('common.bulk_actions') }} ({{ selectedPages.length }})
                        </button>
                    </div>
                </div>

                <!-- Bulk Actions -->
                <div v-if="showBulkActions && selectedPages.length > 0" class="mt-4 p-4 bg-gray-50 dark:bg-gray-700 rounded-lg">
                    <div class="flex items-center space-x-4">
                        <button
                            @click="bulkAction('publish')"
                            class="px-3 py-2 text-sm bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors"
                        >
                            <i class="fas fa-eye mr-1"></i>
                            {{ $t('pages.actions.publish') }}
                        </button>
                        
                        <button
                            @click="bulkAction('unpublish')"
                            class="px-3 py-2 text-sm bg-yellow-600 text-white rounded-lg hover:bg-yellow-700 transition-colors"
                        >
                            <i class="fas fa-eye-slash mr-1"></i>
                            {{ $t('pages.actions.unpublish') }}
                        </button>
                        
                        <button
                            @click="bulkAction('delete')"
                            class="px-3 py-2 text-sm bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors"
                        >
                            <i class="fas fa-trash mr-1"></i>
                            {{ $t('common.delete') }}
                        </button>
                    </div>
                </div>
            </div>

            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                    <thead class="bg-gray-50 dark:bg-gray-700">
                        <tr>
                            <th class="px-6 py-3 text-left">
                                <input
                                    type="checkbox"
                                    :checked="selectedPages.length === pages.data.length"
                                    @change="toggleSelectAll"
                                    class="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                                >
                            </th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                                {{ $t('pages.fields.title') }}
                            </th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                                {{ $t('pages.fields.status') }}
                            </th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                                {{ $t('pages.fields.template') }}
                            </th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                                {{ $t('common.created_at') }}
                            </th>
                            <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                                {{ $t('common.actions') }}
                            </th>
                        </tr>
                    </thead>
                    <tbody class="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                        <tr v-for="page in pages.data" :key="page.id" class="hover:bg-gray-50 dark:hover:bg-gray-700">
                            <td class="px-6 py-4">
                                <input
                                    type="checkbox"
                                    :value="page.id"
                                    v-model="selectedPages"
                                    class="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                                >
                            </td>
                            <td class="px-6 py-4">
                                <div class="flex items-center">
                                    <div class="flex-shrink-0 h-10 w-10">
                                        <img
                                            v-if="page.media && page.media.length > 0"
                                            :src="page.media[0].file_url"
                                            :alt="page.title"
                                            class="h-10 w-10 rounded-lg object-cover"
                                        >
                                        <div
                                            v-else
                                            class="h-10 w-10 rounded-lg bg-gray-200 dark:bg-gray-600 flex items-center justify-center"
                                        >
                                            <i class="fas fa-file-alt text-gray-400"></i>
                                        </div>
                                    </div>
                                    <div class="ml-4">
                                        <div class="text-sm font-medium text-gray-900 dark:text-white">
                                            {{ page.title }}
                                        </div>
                                        <div class="text-sm text-gray-500 dark:text-gray-400">
                                            /{{ page.slug }}
                                        </div>
                                    </div>
                                </div>
                            </td>
                            <td class="px-6 py-4">
                                <span
                                    :class="[
                                        'inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium',
                                        page.is_published
                                            ? 'bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400'
                                            : 'bg-gray-100 text-gray-800 dark:bg-gray-900/30 dark:text-gray-400'
                                    ]"
                                >
                                    <i :class="page.is_published ? 'fas fa-eye' : 'fas fa-eye-slash'" class="mr-1"></i>
                                    {{ page.is_published ? $t('pages.status.published') : $t('pages.status.draft') }}
                                </span>
                            </td>
                            <td class="px-6 py-4 text-sm text-gray-900 dark:text-white">
                                {{ templates[page.template] || page.template }}
                            </td>
                            <td class="px-6 py-4 text-sm text-gray-500 dark:text-gray-400">
                                {{ formatDate(page.created_at) }}
                            </td>
                            <td class="px-6 py-4 text-right text-sm font-medium">
                                <div class="flex items-center justify-end space-x-2">
                                    <Link
                                        :href="route('pages.show', page.slug)"
                                        class="text-blue-600 hover:text-blue-900 dark:text-blue-400 dark:hover:text-blue-300"
                                    >
                                        <i class="fas fa-eye"></i>
                                    </Link>

                                    <Link
                                        :href="route('pages.edit', page.slug)"
                                        class="text-indigo-600 hover:text-indigo-900 dark:text-indigo-400 dark:hover:text-indigo-300"
                                    >
                                        <i class="fas fa-edit"></i>
                                    </Link>
                                    
                                    <button
                                        @click="togglePublish(page)"
                                        :class="[
                                            'hover:opacity-75',
                                            page.is_published
                                                ? 'text-yellow-600 dark:text-yellow-400'
                                                : 'text-green-600 dark:text-green-400'
                                        ]"
                                    >
                                        <i :class="page.is_published ? 'fas fa-eye-slash' : 'fas fa-eye'"></i>
                                    </button>
                                    
                                    <button
                                        @click="duplicatePage(page)"
                                        class="text-purple-600 hover:text-purple-900 dark:text-purple-400 dark:hover:text-purple-300"
                                    >
                                        <i class="fas fa-copy"></i>
                                    </button>
                                    
                                    <button
                                        @click="deletePage(page)"
                                        class="text-red-600 hover:text-red-900 dark:text-red-400 dark:hover:text-red-300"
                                    >
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </div>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>

            <!-- Pagination -->
            <div v-if="pages.links" class="px-6 py-4 border-t border-gray-200 dark:border-gray-700">
                <Pagination :links="pages.links" />
            </div>
        </div>
    </ManagerLayout>
</template>

<script setup>
import { ref, reactive, computed } from 'vue'
import { router, Link } from '@inertiajs/vue3'
import { debounce } from 'lodash'
import ManagerLayout from '@/Layouts/ManagerLayout.vue'
import Pagination from '@/Components/Pagination.vue'

const props = defineProps({
    pages: Object,
    filters: Object,
    stats: Object,
    templates: Object,
})

// Reactive state
const filters = reactive({
    search: props.filters.search || '',
    status: props.filters.status || '',
    template: props.filters.template || '',
})

const selectedPages = ref([])
const showBulkActions = ref(false)

// Methods
const applyFilters = () => {
    router.get(route('pages.index'), filters, {
        preserveState: true,
        preserveScroll: true,
    })
}

const debouncedSearch = debounce(() => {
    applyFilters()
}, 300)

const toggleSelectAll = (event) => {
    if (event.target.checked) {
        selectedPages.value = props.pages.data.map(page => page.id)
    } else {
        selectedPages.value = []
    }
}

const togglePublish = (page) => {
    router.post(route('pages.toggle-publish', page.slug), {}, {
        preserveState: true,
        preserveScroll: true,
    })
}

const duplicatePage = (page) => {
    router.post(route('pages.duplicate', page.slug))
}

const deletePage = (page) => {
    if (confirm('Are you sure you want to delete this page?')) {
        router.delete(route('pages.destroy', page.slug))
    }
}

const bulkAction = (action) => {
    if (selectedPages.value.length === 0) return

    let message = ''
    switch (action) {
        case 'publish':
            message = 'Are you sure you want to publish the selected pages?'
            break
        case 'unpublish':
            message = 'Are you sure you want to unpublish the selected pages?'
            break
        case 'delete':
            message = 'Are you sure you want to delete the selected pages?'
            break
    }

    if (confirm(message)) {
        router.post(route('pages.bulk-action'), {
            action: action,
            page_ids: selectedPages.value,
        }, {
            onSuccess: () => {
                selectedPages.value = []
                showBulkActions.value = false
            }
        })
    }
}

const formatDate = (date) => {
    return new Date(date).toLocaleDateString()
}
</script>
