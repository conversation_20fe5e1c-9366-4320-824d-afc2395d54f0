<template>
    <ManagerLayout title="Floors">
        <template #header>
            <div class="flex justify-between items-center">
                <h2 class="font-semibold text-xl text-gray-800 dark:text-gray-200 leading-tight">
                    🏢 Floor Management
                </h2>
                <Link
                    :href="route('floors.create')"
                    class="inline-flex items-center px-4 py-2 bg-indigo-600 border border-transparent rounded-md font-semibold text-xs text-white uppercase tracking-widest hover:bg-indigo-700 transition ease-in-out duration-150"
                >
                    + Add Floor
                </Link>
            </div>
        </template>

        <div class="py-12">
            <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
                <!-- Search Section -->
                <div class="bg-white dark:bg-gray-800 overflow-hidden shadow-xl sm:rounded-lg mb-6">
                    <div class="p-6">
                        <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                            <!-- Search Input -->
                            <div>
                                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                                    Search
                                </label>
                                <input
                                    v-model="searchTerm"
                                    @input="handleSearch"
                                    type="text"
                                    placeholder="Search floors..."
                                    class="w-full rounded-md border-gray-300 dark:border-gray-700 dark:bg-gray-900 dark:text-gray-300 focus:border-indigo-500 focus:ring-indigo-500"
                                />
                            </div>

                            <!-- Branch Filter -->
                            <div>
                                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                                    Branch
                                </label>
                                <select
                                    v-model="branchFilter"
                                    @change="handleSearch"
                                    class="w-full rounded-md border-gray-300 dark:border-gray-700 dark:bg-gray-900 dark:text-gray-300 focus:border-indigo-500 focus:ring-indigo-500"
                                >
                                    <option value="">All Branches</option>
                                    <option v-for="branch in branches" :key="branch.id" :value="branch.id">
                                        {{ branch.name }}
                                    </option>
                                </select>
                            </div>

                            <!-- Status Filter -->
                            <div>
                                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                                    Status
                                </label>
                                <select
                                    v-model="statusFilter"
                                    @change="handleSearch"
                                    class="w-full rounded-md border-gray-300 dark:border-gray-700 dark:bg-gray-900 dark:text-gray-300 focus:border-indigo-500 focus:ring-indigo-500"
                                >
                                    <option value="">All Status</option>
                                    <option value="1">Active</option>
                                    <option value="0">Inactive</option>
                                </select>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Floors Table -->
                <div class="bg-white dark:bg-gray-800 overflow-hidden shadow-xl sm:rounded-lg">
                    <div class="p-6">
                        <!-- Loading State -->
                        <div v-if="loading" class="text-center py-8">
                            <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-indigo-600"></div>
                            <p class="mt-2 text-gray-600 dark:text-gray-400">Loading floors...</p>
                        </div>

                        <!-- Error State -->
                        <div v-else-if="error" class="text-center py-8">
                            <div class="text-red-600 dark:text-red-400">
                                <p class="text-lg font-medium">Error loading floors</p>
                                <p class="text-sm mt-1">{{ error }}</p>
                                <button
                                    @click="refreshData"
                                    class="mt-4 px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 transition-colors"
                                >
                                    Try Again
                                </button>
                            </div>
                        </div>

                        <!-- Empty State -->
                        <div v-else-if="!floorList || floorList.length === 0" class="text-center py-8">
                            <div class="text-gray-500 dark:text-gray-400">
                                <p class="text-lg font-medium">No floors found</p>
                                <p class="text-sm mt-1">Create your first floor to get started</p>
                                <Link
                                    :href="route('floors.create')"
                                    class="mt-4 inline-flex items-center px-4 py-2 bg-indigo-600 text-white rounded-md hover:bg-indigo-700 transition-colors"
                                >
                                    + Create Floor
                                </Link>
                            </div>
                        </div>

                        <!-- Floors Table -->
                        <div v-else class="overflow-x-auto">
                            <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                                <thead class="bg-gray-50 dark:bg-gray-700">
                                    <tr>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                                            Floor
                                        </th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                                            Branch
                                        </th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                                            Tables
                                        </th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                                            Status
                                        </th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                                            Actions
                                        </th>
                                    </tr>
                                </thead>
                                <tbody class="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                                    <tr v-for="floor in floorList" :key="floor.id || Math.random()" class="hover:bg-gray-50 dark:hover:bg-gray-700">
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <div class="flex items-center">
                                                <div>
                                                    <div class="text-sm font-medium text-gray-900 dark:text-gray-100">
                                                        {{ floor.name || 'Unnamed Floor' }}
                                                    </div>
                                                    <div v-if="floor.description" class="text-sm text-gray-500 dark:text-gray-400">
                                                        {{ floor.description }}
                                                    </div>
                                                </div>
                                            </div>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <div v-if="floor.branch && floor.branch.name" class="text-sm text-gray-900 dark:text-gray-100">
                                                {{ floor.branch.name }}
                                            </div>
                                            <div v-else class="text-sm text-gray-500 dark:text-gray-400">
                                                No branch assigned
                                            </div>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <div class="text-sm text-gray-900 dark:text-gray-100">
                                                {{ floor.tables_count || 0 }} tables
                                            </div>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <span
                                                :class="[
                                                    'inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium',
                                                    floor.is_active
                                                        ? 'bg-green-100 text-green-800'
                                                        : 'bg-red-100 text-red-800'
                                                ]"
                                            >
                                                {{ floor.is_active ? 'Active' : 'Inactive' }}
                                            </span>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                                            <div class="flex items-center space-x-2">
                                                <Link
                                                    :href="route('floors.show', floor.id)"
                                                    class="text-indigo-600 hover:text-indigo-900 dark:text-indigo-400 dark:hover:text-indigo-300 transition-colors"
                                                >
                                                    View
                                                </Link>
                                                <Link
                                                    :href="route('floors.edit', floor.id)"
                                                    class="text-indigo-600 hover:text-indigo-900 dark:text-indigo-400 dark:hover:text-indigo-300 transition-colors"
                                                >
                                                    Edit
                                                </Link>
                                                <button
                                                    @click="deleteFloor(floor)"
                                                    class="text-red-600 hover:text-red-900 dark:text-red-400 dark:hover:text-red-300 transition-colors"
                                                >
                                                    Delete
                                                </button>
                                            </div>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>

                        <!-- Simple Pagination -->
                        <div v-if="paginationData && paginationData.total > paginationData.per_page" class="mt-6 flex justify-between items-center">
                            <div class="text-sm text-gray-700 dark:text-gray-300">
                                Showing {{ paginationData.from || 0 }} to {{ paginationData.to || 0 }} of {{ paginationData.total || 0 }} results
                            </div>
                            <div class="flex space-x-2">
                                <Link
                                    v-if="paginationData.prev_page_url"
                                    :href="paginationData.prev_page_url"
                                    class="px-3 py-2 bg-gray-300 text-gray-700 rounded-md hover:bg-gray-400 transition-colors"
                                >
                                    Previous
                                </Link>
                                <Link
                                    v-if="paginationData.next_page_url"
                                    :href="paginationData.next_page_url"
                                    class="px-3 py-2 bg-gray-300 text-gray-700 rounded-md hover:bg-gray-400 transition-colors"
                                >
                                    Next
                                </Link>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </ManagerLayout>
</template>

<script setup>
import { ref, onMounted, computed } from 'vue'
import { Link, router } from '@inertiajs/vue3'
import ManagerLayout from '@/Layouts/ManagerLayout.vue'

// Props with safe defaults
const props = defineProps({
    floors: {
        type: [Object, Array],
        default: () => null
    },
    branches: {
        type: Array,
        default: () => []
    },
    filters: {
        type: Object,
        default: () => ({})
    }
})

// Reactive state
const loading = ref(false)
const error = ref(null)
const searchTerm = ref('')
const branchFilter = ref('')
const statusFilter = ref('')

// Safe data extraction
const floorList = computed(() => {
    try {
        if (!props.floors) return []
        
        // Handle paginated data
        if (props.floors && typeof props.floors === 'object' && props.floors.data) {
            return Array.isArray(props.floors.data) ? props.floors.data : []
        }
        
        // Handle direct array
        if (Array.isArray(props.floors)) {
            return props.floors
        }
        
        return []
    } catch (e) {
        console.error('Error processing floors data:', e)
        return []
    }
})

const paginationData = computed(() => {
    try {
        if (props.floors && typeof props.floors === 'object' && !Array.isArray(props.floors)) {
            return {
                total: props.floors.total || 0,
                per_page: props.floors.per_page || 20,
                from: props.floors.from || 0,
                to: props.floors.to || 0,
                prev_page_url: props.floors.prev_page_url || null,
                next_page_url: props.floors.next_page_url || null
            }
        }
        return null
    } catch (e) {
        console.error('Error processing pagination data:', e)
        return null
    }
})

// Initialize filters from props
onMounted(() => {
    try {
        if (props.filters) {
            searchTerm.value = props.filters.search || ''
            branchFilter.value = props.filters.branch_id || ''
            statusFilter.value = props.filters.status || ''
        }
    } catch (e) {
        console.error('Error initializing filters:', e)
    }
})

// Methods
const handleSearch = () => {
    try {
        loading.value = true
        error.value = null
        
        const params = {
            search: searchTerm.value,
            branch_id: branchFilter.value,
            status: statusFilter.value
        }
        
        router.get(route('floors.index'), params, {
            preserveState: true,
            replace: true,
            onFinish: () => {
                loading.value = false
            },
            onError: (errors) => {
                loading.value = false
                error.value = 'Failed to search floors'
                console.error('Search error:', errors)
            }
        })
    } catch (e) {
        loading.value = false
        error.value = 'Search failed'
        console.error('Search error:', e)
    }
}

const deleteFloor = (floor) => {
    try {
        if (!floor || !floor.id || !floor.name) return
        
        if (confirm(`Are you sure you want to delete "${floor.name}"? This action cannot be undone.`)) {
            router.delete(route('floors.destroy', floor.id), {
                onError: (errors) => {
                    console.error('Delete error:', errors)
                    alert('Failed to delete floor')
                }
            })
        }
    } catch (e) {
        console.error('Delete error:', e)
        alert('Failed to delete floor')
    }
}

const refreshData = () => {
    try {
        loading.value = true
        error.value = null
        
        router.reload({
            onFinish: () => {
                loading.value = false
            },
            onError: () => {
                loading.value = false
                error.value = 'Failed to refresh data'
            }
        })
    } catch (e) {
        loading.value = false
        error.value = 'Refresh failed'
        console.error('Refresh error:', e)
    }
}
</script>

<style scoped>
.animate-spin {
    animation: spin 1s linear infinite;
}

@keyframes spin {
    from {
        transform: rotate(0deg);
    }
    to {
        transform: rotate(360deg);
    }
}
</style>
