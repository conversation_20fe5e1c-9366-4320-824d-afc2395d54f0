<template>
    <ManagerLayout>
        <template #header>
            <div class="flex justify-between items-center">
                <div>
                    <h2 class="font-semibold text-xl text-gray-800 dark:text-gray-200 leading-tight">
                        Add New Inventory Item
                    </h2>
                    <p class="text-sm text-gray-600 dark:text-gray-400 mt-1">
                        Create a new inventory item for stock management
                    </p>
                </div>
                <Link
                    :href="route('inventory.index')"
                    class="bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded-md text-sm font-medium transition-colors duration-200"
                >
                    Back to Inventory
                </Link>
            </div>
        </template>

        <div class="py-6">
            <div class="max-w-4xl mx-auto sm:px-6 lg:px-8">
                <div class="bg-white dark:bg-gray-800 overflow-hidden shadow-sm sm:rounded-lg">
                    <form @submit.prevent="submit" class="p-6 space-y-6">
                        <!-- Basic Information -->
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <div>
                                <label for="name" class="block text-sm font-medium text-gray-700 dark:text-gray-300">
                                    Item Name *
                                </label>
                                <input
                                    type="text"
                                    id="name"
                                    v-model="form.name"
                                    required
                                    class="mt-1 block w-full border-gray-300 dark:border-gray-700 dark:bg-gray-900 dark:text-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500"
                                    placeholder="Enter item name"
                                />
                                <div v-if="errors.name" class="mt-1 text-sm text-red-600">{{ errors.name }}</div>
                            </div>

                            <div>
                                <label for="sku" class="block text-sm font-medium text-gray-700 dark:text-gray-300">
                                    SKU *
                                </label>
                                <input
                                    type="text"
                                    id="sku"
                                    v-model="form.sku"
                                    required
                                    class="mt-1 block w-full border-gray-300 dark:border-gray-700 dark:bg-gray-900 dark:text-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500"
                                    placeholder="Enter SKU"
                                />
                                <div v-if="errors.sku" class="mt-1 text-sm text-red-600">{{ errors.sku }}</div>
                            </div>

                            <div>
                                <label for="category_id" class="block text-sm font-medium text-gray-700 dark:text-gray-300">
                                    Category *
                                </label>
                                <select
                                    id="category_id"
                                    v-model="form.category_id"
                                    required
                                    class="mt-1 block w-full border-gray-300 dark:border-gray-700 dark:bg-gray-900 dark:text-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500"
                                >
                                    <option value="">Select Category</option>
                                    <option v-for="category in categories" :key="category.id" :value="category.id">
                                        {{ category.name }}
                                    </option>
                                </select>
                                <div v-if="errors.category_id" class="mt-1 text-sm text-red-600">{{ errors.category_id }}</div>
                            </div>

                            <div>
                                <label for="unit" class="block text-sm font-medium text-gray-700 dark:text-gray-300">
                                    Unit *
                                </label>
                                <select
                                    id="unit"
                                    v-model="form.unit"
                                    required
                                    class="mt-1 block w-full border-gray-300 dark:border-gray-700 dark:bg-gray-900 dark:text-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500"
                                >
                                    <option value="">Select Unit</option>
                                    <option value="kg">Kilogram (kg)</option>
                                    <option value="g">Gram (g)</option>
                                    <option value="l">Liter (l)</option>
                                    <option value="ml">Milliliter (ml)</option>
                                    <option value="pcs">Pieces (pcs)</option>
                                    <option value="box">Box</option>
                                    <option value="pack">Pack</option>
                                    <option value="bottle">Bottle</option>
                                    <option value="can">Can</option>
                                </select>
                                <div v-if="errors.unit" class="mt-1 text-sm text-red-600">{{ errors.unit }}</div>
                            </div>
                        </div>

                        <!-- Stock Information -->
                        <div class="border-t border-gray-200 dark:border-gray-700 pt-6">
                            <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100 mb-4">Stock Information</h3>
                            <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                                <div>
                                    <label for="current_stock" class="block text-sm font-medium text-gray-700 dark:text-gray-300">
                                        Current Stock *
                                    </label>
                                    <input
                                        type="number"
                                        id="current_stock"
                                        v-model.number="form.current_stock"
                                        required
                                        min="0"
                                        step="0.01"
                                        class="mt-1 block w-full border-gray-300 dark:border-gray-700 dark:bg-gray-900 dark:text-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500"
                                        placeholder="0.00"
                                    />
                                    <div v-if="errors.current_stock" class="mt-1 text-sm text-red-600">{{ errors.current_stock }}</div>
                                </div>

                                <div>
                                    <label for="minimum_stock" class="block text-sm font-medium text-gray-700 dark:text-gray-300">
                                        Minimum Stock *
                                    </label>
                                    <input
                                        type="number"
                                        id="minimum_stock"
                                        v-model.number="form.minimum_stock"
                                        required
                                        min="0"
                                        step="0.01"
                                        class="mt-1 block w-full border-gray-300 dark:border-gray-700 dark:bg-gray-900 dark:text-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500"
                                        placeholder="0.00"
                                    />
                                    <div v-if="errors.minimum_stock" class="mt-1 text-sm text-red-600">{{ errors.minimum_stock }}</div>
                                </div>

                                <div>
                                    <label for="maximum_stock" class="block text-sm font-medium text-gray-700 dark:text-gray-300">
                                        Maximum Stock
                                    </label>
                                    <input
                                        type="number"
                                        id="maximum_stock"
                                        v-model.number="form.maximum_stock"
                                        min="0"
                                        step="0.01"
                                        class="mt-1 block w-full border-gray-300 dark:border-gray-700 dark:bg-gray-900 dark:text-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500"
                                        placeholder="0.00"
                                    />
                                    <div v-if="errors.maximum_stock" class="mt-1 text-sm text-red-600">{{ errors.maximum_stock }}</div>
                                </div>
                            </div>
                        </div>

                        <!-- Pricing Information -->
                        <div class="border-t border-gray-200 dark:border-gray-700 pt-6">
                            <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100 mb-4">Pricing Information</h3>
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                                <div>
                                    <label for="cost_price" class="block text-sm font-medium text-gray-700 dark:text-gray-300">
                                        Cost Price *
                                    </label>
                                    <input
                                        type="number"
                                        id="cost_price"
                                        v-model.number="form.cost_price"
                                        required
                                        min="0"
                                        step="0.01"
                                        class="mt-1 block w-full border-gray-300 dark:border-gray-700 dark:bg-gray-900 dark:text-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500"
                                        placeholder="0.00"
                                    />
                                    <div v-if="errors.cost_price" class="mt-1 text-sm text-red-600">{{ errors.cost_price }}</div>
                                </div>

                                <div>
                                    <label for="selling_price" class="block text-sm font-medium text-gray-700 dark:text-gray-300">
                                        Selling Price
                                    </label>
                                    <input
                                        type="number"
                                        id="selling_price"
                                        v-model.number="form.selling_price"
                                        min="0"
                                        step="0.01"
                                        class="mt-1 block w-full border-gray-300 dark:border-gray-700 dark:bg-gray-900 dark:text-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500"
                                        placeholder="0.00"
                                    />
                                    <div v-if="errors.selling_price" class="mt-1 text-sm text-red-600">{{ errors.selling_price }}</div>
                                </div>
                            </div>
                        </div>

                        <!-- Additional Information -->
                        <div class="border-t border-gray-200 dark:border-gray-700 pt-6">
                            <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100 mb-4">Additional Information</h3>
                            <div class="space-y-4">
                                <div>
                                    <label for="description" class="block text-sm font-medium text-gray-700 dark:text-gray-300">
                                        Description
                                    </label>
                                    <textarea
                                        id="description"
                                        v-model="form.description"
                                        rows="3"
                                        class="mt-1 block w-full border-gray-300 dark:border-gray-700 dark:bg-gray-900 dark:text-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500"
                                        placeholder="Enter item description"
                                    ></textarea>
                                    <div v-if="errors.description" class="mt-1 text-sm text-red-600">{{ errors.description }}</div>
                                </div>

                                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                                    <div>
                                        <label for="expiry_date" class="block text-sm font-medium text-gray-700 dark:text-gray-300">
                                            Expiry Date
                                        </label>
                                        <input
                                            type="date"
                                            id="expiry_date"
                                            v-model="form.expiry_date"
                                            class="mt-1 block w-full border-gray-300 dark:border-gray-700 dark:bg-gray-900 dark:text-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500"
                                        />
                                        <div v-if="errors.expiry_date" class="mt-1 text-sm text-red-600">{{ errors.expiry_date }}</div>
                                    </div>

                                    <div>
                                        <label for="location" class="block text-sm font-medium text-gray-700 dark:text-gray-300">
                                            Storage Location
                                        </label>
                                        <input
                                            type="text"
                                            id="location"
                                            v-model="form.location"
                                            class="mt-1 block w-full border-gray-300 dark:border-gray-700 dark:bg-gray-900 dark:text-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500"
                                            placeholder="e.g., Warehouse A, Shelf 1"
                                        />
                                        <div v-if="errors.location" class="mt-1 text-sm text-red-600">{{ errors.location }}</div>
                                    </div>
                                </div>

                                <div class="flex items-center">
                                    <input
                                        type="checkbox"
                                        id="is_active"
                                        v-model="form.is_active"
                                        class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                                    />
                                    <label for="is_active" class="ml-2 block text-sm text-gray-900 dark:text-gray-300">
                                        Active (item is available for use)
                                    </label>
                                </div>
                            </div>
                        </div>

                        <!-- Submit Buttons -->
                        <div class="border-t border-gray-200 dark:border-gray-700 pt-6">
                            <div class="flex justify-end space-x-3">
                                <Link
                                    :href="route('inventory.index')"
                                    class="bg-gray-300 hover:bg-gray-400 text-gray-800 px-4 py-2 rounded-md text-sm font-medium transition-colors duration-200"
                                >
                                    Cancel
                                </Link>
                                <button
                                    type="submit"
                                    :disabled="processing"
                                    class="bg-blue-600 hover:bg-blue-700 disabled:bg-blue-400 text-white px-4 py-2 rounded-md text-sm font-medium transition-colors duration-200"
                                >
                                    {{ processing ? 'Creating...' : 'Create Item' }}
                                </button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </ManagerLayout>
</template>

<script setup>
import { ref, reactive } from 'vue'
import { Link, router } from '@inertiajs/vue3'
import ManagerLayout from '@/Layouts/ManagerLayout.vue'

const props = defineProps({
    categories: Array,
    errors: Object,
})

const processing = ref(false)

const form = reactive({
    name: '',
    sku: '',
    category_id: '',
    unit: '',
    current_stock: 0,
    minimum_stock: 0,
    maximum_stock: null,
    cost_price: 0,
    selling_price: null,
    description: '',
    expiry_date: '',
    location: '',
    is_active: true,
})

const submit = () => {
    processing.value = true
    
    router.post(route('inventory.store'), form, {
        onFinish: () => {
            processing.value = false
        },
        onSuccess: () => {
            // Success handled by redirect
        },
        onError: (errors) => {
            console.error('Validation errors:', errors)
        }
    })
}
</script>
