<template>
    <ManagerLayout>
        <template #header>
            <div class="flex justify-between items-center">
                <div>
                    <h2 class="font-semibold text-xl text-gray-800 dark:text-gray-200 leading-tight">
                        Edit Inventory Item
                    </h2>
                    <p class="text-sm text-gray-600 dark:text-gray-400 mt-1">
                        Update inventory item details and stock information
                    </p>
                </div>
                <Link
                    :href="route('inventory.index')"
                    class="bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded-md text-sm font-medium transition-colors duration-200"
                >
                    Back to Inventory
                </Link>
            </div>
        </template>

        <div class="py-6">
            <div class="max-w-4xl mx-auto sm:px-6 lg:px-8">
                <div class="bg-white dark:bg-gray-800 overflow-hidden shadow-sm sm:rounded-lg">
                    <form @submit.prevent="submit" class="p-6 space-y-6">
                        <!-- Basic Information -->
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <div>
                                <label for="name" class="block text-sm font-medium text-gray-700 dark:text-gray-300">
                                    Item Name *
                                </label>
                                <input
                                    type="text"
                                    id="name"
                                    v-model="form.name"
                                    required
                                    class="mt-1 block w-full border-gray-300 dark:border-gray-700 dark:bg-gray-900 dark:text-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500"
                                    placeholder="Enter item name"
                                />
                                <div v-if="errors.name" class="mt-1 text-sm text-red-600">{{ errors.name }}</div>
                            </div>

                            <div>
                                <label for="sku" class="block text-sm font-medium text-gray-700 dark:text-gray-300">
                                    SKU *
                                </label>
                                <input
                                    type="text"
                                    id="sku"
                                    v-model="form.sku"
                                    required
                                    class="mt-1 block w-full border-gray-300 dark:border-gray-700 dark:bg-gray-900 dark:text-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500"
                                    placeholder="Enter SKU"
                                />
                                <div v-if="errors.sku" class="mt-1 text-sm text-red-600">{{ errors.sku }}</div>
                            </div>

                            <div>
                                <label for="inventory_category_id" class="block text-sm font-medium text-gray-700 dark:text-gray-300">
                                    Category *
                                </label>
                                <select
                                    id="inventory_category_id"
                                    v-model="form.inventory_category_id"
                                    required
                                    class="mt-1 block w-full border-gray-300 dark:border-gray-700 dark:bg-gray-900 dark:text-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500"
                                >
                                    <option value="">Select Category</option>
                                    <option v-for="category in categories" :key="category.id" :value="category.id">
                                        {{ category.name }}
                                    </option>
                                </select>
                                <div v-if="errors.inventory_category_id" class="mt-1 text-sm text-red-600">{{ errors.inventory_category_id }}</div>
                            </div>

                            <div>
                                <label for="vendor_id" class="block text-sm font-medium text-gray-700 dark:text-gray-300">
                                    Vendor
                                </label>
                                <select
                                    id="vendor_id"
                                    v-model="form.vendor_id"
                                    class="mt-1 block w-full border-gray-300 dark:border-gray-700 dark:bg-gray-900 dark:text-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500"
                                >
                                    <option value="">Select Vendor</option>
                                    <option v-for="vendor in vendors" :key="vendor.id" :value="vendor.id">
                                        {{ vendor.name }}
                                    </option>
                                </select>
                                <div v-if="errors.vendor_id" class="mt-1 text-sm text-red-600">{{ errors.vendor_id }}</div>
                            </div>

                            <div>
                                <label for="unit_of_measurement" class="block text-sm font-medium text-gray-700 dark:text-gray-300">
                                    Unit of Measurement *
                                </label>
                                <select
                                    id="unit_of_measurement"
                                    v-model="form.unit_of_measurement"
                                    required
                                    class="mt-1 block w-full border-gray-300 dark:border-gray-700 dark:bg-gray-900 dark:text-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500"
                                >
                                    <option value="">Select Unit</option>
                                    <option v-for="unit in unitOptions" :key="unit.value" :value="unit.value">
                                        {{ unit.label }}
                                    </option>
                                </select>
                                <div v-if="errors.unit_of_measurement" class="mt-1 text-sm text-red-600">{{ errors.unit_of_measurement }}</div>
                            </div>

                            <div>
                                <label for="barcode" class="block text-sm font-medium text-gray-700 dark:text-gray-300">
                                    Barcode
                                </label>
                                <input
                                    type="text"
                                    id="barcode"
                                    v-model="form.barcode"
                                    class="mt-1 block w-full border-gray-300 dark:border-gray-700 dark:bg-gray-900 dark:text-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500"
                                    placeholder="Enter barcode"
                                />
                                <div v-if="errors.barcode" class="mt-1 text-sm text-red-600">{{ errors.barcode }}</div>
                            </div>
                        </div>

                        <!-- Stock Information -->
                        <div class="border-t border-gray-200 dark:border-gray-700 pt-6">
                            <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100 mb-4">Stock Information</h3>
                            <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                                <div>
                                    <label for="minimum_stock" class="block text-sm font-medium text-gray-700 dark:text-gray-300">
                                        Minimum Stock *
                                    </label>
                                    <input
                                        type="number"
                                        id="minimum_stock"
                                        v-model.number="form.minimum_stock"
                                        required
                                        min="0"
                                        step="0.01"
                                        class="mt-1 block w-full border-gray-300 dark:border-gray-700 dark:bg-gray-900 dark:text-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500"
                                        placeholder="0.00"
                                    />
                                    <div v-if="errors.minimum_stock" class="mt-1 text-sm text-red-600">{{ errors.minimum_stock }}</div>
                                </div>

                                <div>
                                    <label for="maximum_stock" class="block text-sm font-medium text-gray-700 dark:text-gray-300">
                                        Maximum Stock
                                    </label>
                                    <input
                                        type="number"
                                        id="maximum_stock"
                                        v-model.number="form.maximum_stock"
                                        min="0"
                                        step="0.01"
                                        class="mt-1 block w-full border-gray-300 dark:border-gray-700 dark:bg-gray-900 dark:text-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500"
                                        placeholder="0.00"
                                    />
                                    <div v-if="errors.maximum_stock" class="mt-1 text-sm text-red-600">{{ errors.maximum_stock }}</div>
                                </div>

                                <div>
                                    <label for="reorder_point" class="block text-sm font-medium text-gray-700 dark:text-gray-300">
                                        Reorder Point *
                                    </label>
                                    <input
                                        type="number"
                                        id="reorder_point"
                                        v-model.number="form.reorder_point"
                                        required
                                        min="0"
                                        step="0.01"
                                        class="mt-1 block w-full border-gray-300 dark:border-gray-700 dark:bg-gray-900 dark:text-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500"
                                        placeholder="0.00"
                                    />
                                    <div v-if="errors.reorder_point" class="mt-1 text-sm text-red-600">{{ errors.reorder_point }}</div>
                                </div>

                                <div>
                                    <label for="reorder_quantity" class="block text-sm font-medium text-gray-700 dark:text-gray-300">
                                        Reorder Quantity *
                                    </label>
                                    <input
                                        type="number"
                                        id="reorder_quantity"
                                        v-model.number="form.reorder_quantity"
                                        required
                                        min="0"
                                        step="0.01"
                                        class="mt-1 block w-full border-gray-300 dark:border-gray-700 dark:bg-gray-900 dark:text-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500"
                                        placeholder="0.00"
                                    />
                                    <div v-if="errors.reorder_quantity" class="mt-1 text-sm text-red-600">{{ errors.reorder_quantity }}</div>
                                </div>
                            </div>
                        </div>

                        <!-- Pricing Information -->
                        <div class="border-t border-gray-200 dark:border-gray-700 pt-6">
                            <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100 mb-4">Pricing Information</h3>
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                                <div>
                                    <label for="unit_cost" class="block text-sm font-medium text-gray-700 dark:text-gray-300">
                                        Unit Cost *
                                    </label>
                                    <input
                                        type="number"
                                        id="unit_cost"
                                        v-model.number="form.unit_cost"
                                        required
                                        min="0"
                                        step="0.01"
                                        class="mt-1 block w-full border-gray-300 dark:border-gray-700 dark:bg-gray-900 dark:text-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500"
                                        placeholder="0.00"
                                    />
                                    <div v-if="errors.unit_cost" class="mt-1 text-sm text-red-600">{{ errors.unit_cost }}</div>
                                </div>

                                <div>
                                    <label for="selling_price" class="block text-sm font-medium text-gray-700 dark:text-gray-300">
                                        Selling Price
                                    </label>
                                    <input
                                        type="number"
                                        id="selling_price"
                                        v-model.number="form.selling_price"
                                        min="0"
                                        step="0.01"
                                        class="mt-1 block w-full border-gray-300 dark:border-gray-700 dark:bg-gray-900 dark:text-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500"
                                        placeholder="0.00"
                                    />
                                    <div v-if="errors.selling_price" class="mt-1 text-sm text-red-600">{{ errors.selling_price }}</div>
                                </div>
                            </div>
                        </div>

                        <!-- Additional Information -->
                        <div class="border-t border-gray-200 dark:border-gray-700 pt-6">
                            <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100 mb-4">Additional Information</h3>
                            <div class="space-y-4">
                                <div>
                                    <label for="description" class="block text-sm font-medium text-gray-700 dark:text-gray-300">
                                        Description
                                    </label>
                                    <textarea
                                        id="description"
                                        v-model="form.description"
                                        rows="3"
                                        class="mt-1 block w-full border-gray-300 dark:border-gray-700 dark:bg-gray-900 dark:text-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500"
                                        placeholder="Enter item description"
                                    ></textarea>
                                    <div v-if="errors.description" class="mt-1 text-sm text-red-600">{{ errors.description }}</div>
                                </div>

                                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                                    <div>
                                        <label for="shelf_life_days" class="block text-sm font-medium text-gray-700 dark:text-gray-300">
                                            Shelf Life (Days)
                                        </label>
                                        <input
                                            type="number"
                                            id="shelf_life_days"
                                            v-model.number="form.shelf_life_days"
                                            min="1"
                                            class="mt-1 block w-full border-gray-300 dark:border-gray-700 dark:bg-gray-900 dark:text-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500"
                                            placeholder="e.g., 30"
                                        />
                                        <div v-if="errors.shelf_life_days" class="mt-1 text-sm text-red-600">{{ errors.shelf_life_days }}</div>
                                    </div>

                                    <div>
                                        <label for="storage_requirements" class="block text-sm font-medium text-gray-700 dark:text-gray-300">
                                            Storage Requirements
                                        </label>
                                        <select
                                            id="storage_requirements"
                                            v-model="form.storage_requirements"
                                            class="mt-1 block w-full border-gray-300 dark:border-gray-700 dark:bg-gray-900 dark:text-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500"
                                        >
                                            <option value="">Select Storage Type</option>
                                            <option v-for="storage in storageOptions" :key="storage.value" :value="storage.value">
                                                {{ storage.label }}
                                            </option>
                                        </select>
                                        <div v-if="errors.storage_requirements" class="mt-1 text-sm text-red-600">{{ errors.storage_requirements }}</div>
                                    </div>
                                </div>

                                <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                                    <div class="flex items-center">
                                        <input
                                            type="checkbox"
                                            id="is_perishable"
                                            v-model="form.is_perishable"
                                            class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                                        />
                                        <label for="is_perishable" class="ml-2 block text-sm text-gray-900 dark:text-gray-300">
                                            Perishable item
                                        </label>
                                    </div>

                                    <div class="flex items-center">
                                        <input
                                            type="checkbox"
                                            id="track_expiry"
                                            v-model="form.track_expiry"
                                            class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                                        />
                                        <label for="track_expiry" class="ml-2 block text-sm text-gray-900 dark:text-gray-300">
                                            Track expiry dates
                                        </label>
                                    </div>

                                    <div class="flex items-center">
                                        <input
                                            type="checkbox"
                                            id="track_batches"
                                            v-model="form.track_batches"
                                            class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                                        />
                                        <label for="track_batches" class="ml-2 block text-sm text-gray-900 dark:text-gray-300">
                                            Track batches
                                        </label>
                                    </div>
                                </div>

                                <div>
                                    <label for="notes" class="block text-sm font-medium text-gray-700 dark:text-gray-300">
                                        Notes
                                    </label>
                                    <textarea
                                        id="notes"
                                        v-model="form.notes"
                                        rows="3"
                                        class="mt-1 block w-full border-gray-300 dark:border-gray-700 dark:bg-gray-900 dark:text-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500"
                                        placeholder="Additional notes about this item"
                                    ></textarea>
                                    <div v-if="errors.notes" class="mt-1 text-sm text-red-600">{{ errors.notes }}</div>
                                </div>

                                <div class="flex items-center">
                                    <input
                                        type="checkbox"
                                        id="is_active"
                                        v-model="form.is_active"
                                        class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                                    />
                                    <label for="is_active" class="ml-2 block text-sm text-gray-900 dark:text-gray-300">
                                        Active (item is available for use)
                                    </label>
                                </div>
                            </div>
                        </div>

                        <!-- Submit Buttons -->
                        <div class="border-t border-gray-200 dark:border-gray-700 pt-6">
                            <div class="flex justify-end space-x-3">
                                <Link
                                    :href="route('inventory.index')"
                                    class="bg-gray-300 hover:bg-gray-400 text-gray-800 px-4 py-2 rounded-md text-sm font-medium transition-colors duration-200"
                                >
                                    Cancel
                                </Link>
                                <button
                                    type="submit"
                                    :disabled="processing"
                                    class="bg-blue-600 hover:bg-blue-700 disabled:bg-blue-400 text-white px-4 py-2 rounded-md text-sm font-medium transition-colors duration-200"
                                >
                                    {{ processing ? 'Updating...' : 'Update Item' }}
                                </button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </ManagerLayout>
</template>

<script setup>
import { ref, reactive } from 'vue'
import { Link, router } from '@inertiajs/vue3'
import ManagerLayout from '@/Layouts/ManagerLayout.vue'

const props = defineProps({
    item: Object,
    categories: Array,
    vendors: Array,
    unitOptions: Array,
    storageOptions: Array,
    errors: Object,
})

const processing = ref(false)

const form = reactive({
    name: props.item.name || '',
    sku: props.item.sku || '',
    inventory_category_id: props.item.inventory_category_id || '',
    vendor_id: props.item.vendor_id || '',
    unit_of_measurement: props.item.unit_of_measurement || '',
    barcode: props.item.barcode || '',
    minimum_stock: props.item.minimum_stock || 0,
    maximum_stock: props.item.maximum_stock || null,
    reorder_point: props.item.reorder_point || 0,
    reorder_quantity: props.item.reorder_quantity || 0,
    unit_cost: props.item.unit_cost || 0,
    selling_price: props.item.selling_price || null,
    description: props.item.description || '',
    shelf_life_days: props.item.shelf_life_days || null,
    storage_requirements: props.item.storage_requirements || '',
    is_perishable: props.item.is_perishable || false,
    track_expiry: props.item.track_expiry || false,
    track_batches: props.item.track_batches || false,
    notes: props.item.notes || '',
    is_active: props.item.is_active !== undefined ? props.item.is_active : true,
})

const submit = () => {
    processing.value = true
    
    router.put(route('inventory.update', props.item.id), form, {
        onFinish: () => {
            processing.value = false
        },
        onSuccess: () => {
            // Success handled by redirect
        },
        onError: (errors) => {
            console.error('Validation errors:', errors)
        }
    })
}
</script>
