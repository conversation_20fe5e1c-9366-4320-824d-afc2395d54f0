<template>
    <ManagerLayout title="Table Details">
        <template #header>
            <div class="flex justify-between items-center">
                <h2 class="font-semibold text-xl text-gray-800 dark:text-gray-200 leading-tight">
                    🪑 {{ table?.name || 'Table Details' }}
                </h2>
                <div class="flex space-x-3">
                    <Link
                        :href="route('manager.tables.edit', table?.id)"
                        class="inline-flex items-center px-4 py-2 bg-indigo-600 border border-transparent rounded-md font-semibold text-xs text-white uppercase tracking-widest hover:bg-indigo-700 transition ease-in-out duration-150"
                    >
                        ✏️ Edit
                    </Link>
                    <Link
                        :href="route('manager.tables.index')"
                        class="inline-flex items-center px-4 py-2 bg-gray-600 border border-transparent rounded-md font-semibold text-xs text-white uppercase tracking-widest hover:bg-gray-700 transition ease-in-out duration-150"
                    >
                        ← Back to Tables
                    </Link>
                </div>
            </div>
        </template>

        <div class="py-12">
            <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
                <!-- Table Information -->
                <div class="bg-white dark:bg-gray-800 overflow-hidden shadow-xl sm:rounded-lg mb-6">
                    <div class="p-6">
                        <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
                            <!-- Basic Information -->
                            <div>
                                <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100 mb-4">Table Information</h3>
                                <dl class="space-y-3">
                                    <div>
                                        <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">Table Name</dt>
                                        <dd class="text-sm text-gray-900 dark:text-gray-100">{{ table?.name || 'N/A' }}</dd>
                                    </div>
                                    <div v-if="table?.table_number">
                                        <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">Table Number</dt>
                                        <dd class="text-sm text-gray-900 dark:text-gray-100">{{ table.table_number }}</dd>
                                    </div>
                                    <div v-if="table?.description">
                                        <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">Description</dt>
                                        <dd class="text-sm text-gray-900 dark:text-gray-100">{{ table.description }}</dd>
                                    </div>
                                    <div>
                                        <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">Capacity</dt>
                                        <dd class="text-sm text-gray-900 dark:text-gray-100">{{ table?.capacity || 'N/A' }} seats</dd>
                                    </div>
                                    <div>
                                        <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">Status</dt>
                                        <dd class="text-sm">
                                            <span :class="[
                                                'inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium',
                                                table?.is_active 
                                                    ? 'bg-green-100 text-green-800' 
                                                    : 'bg-red-100 text-red-800'
                                            ]">
                                                {{ table?.is_active ? 'Active' : 'Inactive' }}
                                            </span>
                                        </dd>
                                    </div>
                                    <div v-if="table?.sort_order !== undefined">
                                        <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">Sort Order</dt>
                                        <dd class="text-sm text-gray-900 dark:text-gray-100">{{ table.sort_order }}</dd>
                                    </div>
                                </dl>
                            </div>

                            <!-- Location Information -->
                            <div>
                                <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100 mb-4">Location Information</h3>
                                <dl class="space-y-3">
                                    <div>
                                        <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">Branch</dt>
                                        <dd class="text-sm text-gray-900 dark:text-gray-100">
                                            <Link
                                                v-if="table?.branch"
                                                :href="route('branches.show', table.branch.id)"
                                                class="text-indigo-600 hover:text-indigo-500"
                                            >
                                                {{ table.branch.name }}
                                            </Link>
                                            <span v-else>N/A</span>
                                        </dd>
                                    </div>
                                    <div>
                                        <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">Floor</dt>
                                        <dd class="text-sm text-gray-900 dark:text-gray-100">
                                            <Link
                                                v-if="table?.floor"
                                                :href="route('floors.show', table.floor.id)"
                                                class="text-indigo-600 hover:text-indigo-500"
                                            >
                                                {{ table.floor.name }}
                                            </Link>
                                            <span v-else>N/A</span>
                                        </dd>
                                    </div>
                                    <div v-if="table?.branch?.address">
                                        <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">Branch Address</dt>
                                        <dd class="text-sm text-gray-900 dark:text-gray-100">
                                            {{ table.branch.address }}
                                        </dd>
                                    </div>
                                    <div v-if="table?.branch?.phone">
                                        <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">Branch Phone</dt>
                                        <dd class="text-sm text-gray-900 dark:text-gray-100">
                                            <a :href="'tel:' + table.branch.phone" class="text-indigo-600 hover:text-indigo-500">
                                                {{ table.branch.phone }}
                                            </a>
                                        </dd>
                                    </div>
                                </dl>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Recent Orders -->
                <div class="bg-white dark:bg-gray-800 overflow-hidden shadow-xl sm:rounded-lg mb-6">
                    <div class="p-6">
                        <div class="flex justify-between items-center mb-4">
                            <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100">Recent Orders</h3>
                            <Link
                                :href="route('pos.table-view', { table_id: table?.id })"
                                class="inline-flex items-center px-3 py-2 bg-indigo-600 text-white text-sm rounded-md hover:bg-indigo-700 transition-colors"
                            >
                                📱 POS View
                            </Link>
                        </div>

                        <!-- Orders List -->
                        <div v-if="table?.orders && table.orders.length > 0">
                            <div class="overflow-x-auto">
                                <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                                    <thead class="bg-gray-50 dark:bg-gray-700">
                                        <tr>
                                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                                                Order #
                                            </th>
                                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                                                Customer
                                            </th>
                                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                                                Amount
                                            </th>
                                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                                                Status
                                            </th>
                                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                                                Date
                                            </th>
                                        </tr>
                                    </thead>
                                    <tbody class="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                                        <tr v-for="order in table.orders" :key="order.id" class="hover:bg-gray-50 dark:hover:bg-gray-700">
                                            <td class="px-6 py-4 whitespace-nowrap">
                                                <div class="text-sm font-medium text-gray-900 dark:text-gray-100">
                                                    {{ order.order_number || `#${order.id}` }}
                                                </div>
                                            </td>
                                            <td class="px-6 py-4 whitespace-nowrap">
                                                <div class="text-sm text-gray-900 dark:text-gray-100">
                                                    {{ order.customer_name || order.customer?.name || 'Walk-in' }}
                                                </div>
                                            </td>
                                            <td class="px-6 py-4 whitespace-nowrap">
                                                <div class="text-sm text-gray-900 dark:text-gray-100">
                                                    ${{ order.total_amount || '0.00' }}
                                                </div>
                                            </td>
                                            <td class="px-6 py-4 whitespace-nowrap">
                                                <span :class="[
                                                    'inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium',
                                                    getOrderStatusClass(order.status)
                                                ]">
                                                    {{ order.status || 'pending' }}
                                                </span>
                                            </td>
                                            <td class="px-6 py-4 whitespace-nowrap">
                                                <div class="text-sm text-gray-900 dark:text-gray-100">
                                                    {{ formatDate(order.created_at) }}
                                                </div>
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>

                        <!-- Empty State for Orders -->
                        <div v-else class="text-center py-8">
                            <div class="text-gray-500 dark:text-gray-400">
                                <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
                                </svg>
                                <h3 class="mt-2 text-sm font-medium text-gray-900 dark:text-gray-100">No orders</h3>
                                <p class="mt-1 text-sm text-gray-500 dark:text-gray-400">
                                    No orders have been placed for this table yet.
                                </p>
                                <div class="mt-6">
                                    <Link
                                        :href="route('pos.table-view', { table_id: table?.id })"
                                        class="inline-flex items-center px-4 py-2 bg-indigo-600 text-white text-sm rounded-md hover:bg-indigo-700 transition-colors"
                                    >
                                        📱 Start New Order
                                    </Link>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Recent Reservations -->
                <div class="bg-white dark:bg-gray-800 overflow-hidden shadow-xl sm:rounded-lg">
                    <div class="p-6">
                        <div class="flex justify-between items-center mb-4">
                            <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100">Recent Reservations</h3>
                            <Link
                                :href="route('reservations.create', { table_id: table?.id })"
                                class="inline-flex items-center px-3 py-2 bg-green-600 text-white text-sm rounded-md hover:bg-green-700 transition-colors"
                            >
                                + New Reservation
                            </Link>
                        </div>

                        <!-- Reservations List -->
                        <div v-if="table?.reservations && table.reservations.length > 0">
                            <div class="overflow-x-auto">
                                <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                                    <thead class="bg-gray-50 dark:bg-gray-700">
                                        <tr>
                                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                                                Customer
                                            </th>
                                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                                                Date & Time
                                            </th>
                                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                                                Party Size
                                            </th>
                                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                                                Status
                                            </th>
                                        </tr>
                                    </thead>
                                    <tbody class="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                                        <tr v-for="reservation in table.reservations" :key="reservation.id" class="hover:bg-gray-50 dark:hover:bg-gray-700">
                                            <td class="px-6 py-4 whitespace-nowrap">
                                                <div class="text-sm font-medium text-gray-900 dark:text-gray-100">
                                                    {{ reservation.customer_name || reservation.customer?.name || 'N/A' }}
                                                </div>
                                                <div v-if="reservation.customer_phone" class="text-sm text-gray-500 dark:text-gray-400">
                                                    {{ reservation.customer_phone }}
                                                </div>
                                            </td>
                                            <td class="px-6 py-4 whitespace-nowrap">
                                                <div class="text-sm text-gray-900 dark:text-gray-100">
                                                    {{ formatDateTime(reservation.reservation_date, reservation.reservation_time) }}
                                                </div>
                                            </td>
                                            <td class="px-6 py-4 whitespace-nowrap">
                                                <div class="text-sm text-gray-900 dark:text-gray-100">
                                                    {{ reservation.party_size || 'N/A' }} guests
                                                </div>
                                            </td>
                                            <td class="px-6 py-4 whitespace-nowrap">
                                                <span :class="[
                                                    'inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium',
                                                    getReservationStatusClass(reservation.status)
                                                ]">
                                                    {{ reservation.status || 'pending' }}
                                                </span>
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>

                        <!-- Empty State for Reservations -->
                        <div v-else class="text-center py-8">
                            <div class="text-gray-500 dark:text-gray-400">
                                <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3a2 2 0 012-2h4a2 2 0 012 2v4m-6 4v10m6-10v10m-6-4h6" />
                                </svg>
                                <h3 class="mt-2 text-sm font-medium text-gray-900 dark:text-gray-100">No reservations</h3>
                                <p class="mt-1 text-sm text-gray-500 dark:text-gray-400">
                                    No reservations have been made for this table yet.
                                </p>
                                <div class="mt-6">
                                    <Link
                                        :href="route('reservations.create', { table_id: table?.id })"
                                        class="inline-flex items-center px-4 py-2 bg-green-600 text-white text-sm rounded-md hover:bg-green-700 transition-colors"
                                    >
                                        + Create Reservation
                                    </Link>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </ManagerLayout>
</template>

<script setup>
import { Link } from '@inertiajs/vue3'
import ManagerLayout from '@/Layouts/ManagerLayout.vue'

// Props
const props = defineProps({
    table: {
        type: Object,
        required: true
    }
})

// Methods
const getOrderStatusClass = (status) => {
    const statusClasses = {
        'pending': 'bg-yellow-100 text-yellow-800',
        'confirmed': 'bg-blue-100 text-blue-800',
        'preparing': 'bg-orange-100 text-orange-800',
        'ready': 'bg-green-100 text-green-800',
        'served': 'bg-green-100 text-green-800',
        'paid': 'bg-gray-100 text-gray-800',
        'cancelled': 'bg-red-100 text-red-800'
    }
    return statusClasses[status] || 'bg-gray-100 text-gray-800'
}

const getReservationStatusClass = (status) => {
    const statusClasses = {
        'pending': 'bg-yellow-100 text-yellow-800',
        'confirmed': 'bg-green-100 text-green-800',
        'seated': 'bg-blue-100 text-blue-800',
        'completed': 'bg-gray-100 text-gray-800',
        'cancelled': 'bg-red-100 text-red-800',
        'no_show': 'bg-red-100 text-red-800'
    }
    return statusClasses[status] || 'bg-gray-100 text-gray-800'
}

const formatDate = (dateString) => {
    if (!dateString) return 'N/A'
    try {
        return new Date(dateString).toLocaleDateString()
    } catch (e) {
        return 'Invalid Date'
    }
}

const formatDateTime = (dateString, timeString) => {
    if (!dateString) return 'N/A'
    try {
        const date = new Date(dateString)
        const dateStr = date.toLocaleDateString()
        const timeStr = timeString || date.toLocaleTimeString()
        return `${dateStr} ${timeStr}`
    } catch (e) {
        return 'Invalid Date'
    }
}
</script>
