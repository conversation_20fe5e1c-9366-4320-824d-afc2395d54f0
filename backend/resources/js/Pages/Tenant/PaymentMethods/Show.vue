<template>
    <ManagerLayout>
        <template #header>
            <div class="flex justify-between items-center">
                <div>
                    <h2 class="font-semibold text-xl text-gray-800 dark:text-gray-200 leading-tight">
                        {{ paymentMethod.name }}
                    </h2>
                    <p class="text-sm text-gray-600 dark:text-gray-400 mt-1">
                        Payment method details and usage statistics
                    </p>
                </div>
                <div class="flex space-x-3">
                    <Link
                        :href="route('payment-methods.edit', paymentMethod.id)"
                        class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md text-sm font-medium transition-colors duration-200"
                    >
                        <PencilIcon class="w-4 h-4 inline mr-2" />
                        Edit
                    </Link>
                    <Link
                        :href="route('payment-methods.index')"
                        class="bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded-md text-sm font-medium transition-colors duration-200"
                    >
                        Back to List
                    </Link>
                </div>
            </div>
        </template>

        <div class="py-6">
            <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
                <!-- Payment Method Details -->
                <div class="bg-white dark:bg-gray-800 overflow-hidden shadow-sm sm:rounded-lg mb-6">
                    <div class="p-6">
                        <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
                            <!-- Basic Information -->
                            <div class="lg:col-span-2">
                                <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100 mb-4">Basic Information</h3>
                                
                                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                    <div>
                                        <label class="block text-sm font-medium text-gray-500 dark:text-gray-400">Name</label>
                                        <p class="mt-1 text-sm text-gray-900 dark:text-gray-100">{{ paymentMethod.name }}</p>
                                    </div>
                                    
                                    <div>
                                        <label class="block text-sm font-medium text-gray-500 dark:text-gray-400">Type</label>
                                        <span class="mt-1 inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-blue-100 text-blue-800">
                                            {{ paymentMethod.type_label }}
                                        </span>
                                    </div>
                                    
                                    <div>
                                        <label class="block text-sm font-medium text-gray-500 dark:text-gray-400">Status</label>
                                        <span 
                                            :class="paymentMethod.is_active ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'"
                                            class="mt-1 inline-flex px-2 py-1 text-xs font-semibold rounded-full"
                                        >
                                            {{ paymentMethod.is_active ? 'Active' : 'Inactive' }}
                                        </span>
                                    </div>
                                    
                                    <div>
                                        <label class="block text-sm font-medium text-gray-500 dark:text-gray-400">Display Order</label>
                                        <p class="mt-1 text-sm text-gray-900 dark:text-gray-100">{{ paymentMethod.display_order }}</p>
                                    </div>
                                </div>

                                <div v-if="paymentMethod.instructions" class="mt-4">
                                    <label class="block text-sm font-medium text-gray-500 dark:text-gray-400">Customer Instructions</label>
                                    <p class="mt-1 text-sm text-gray-900 dark:text-gray-100 whitespace-pre-wrap">{{ paymentMethod.instructions }}</p>
                                </div>
                            </div>

                            <!-- Images -->
                            <div>
                                <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100 mb-4">Images</h3>
                                
                                <div class="space-y-4">
                                    <div v-if="paymentMethod.icon_url">
                                        <label class="block text-sm font-medium text-gray-500 dark:text-gray-400">Icon</label>
                                        <img :src="paymentMethod.icon_url" :alt="paymentMethod.name" class="mt-1 h-12 w-12 rounded object-cover">
                                    </div>
                                    
                                    <div v-if="paymentMethod.image_url">
                                        <label class="block text-sm font-medium text-gray-500 dark:text-gray-400">Image</label>
                                        <img :src="paymentMethod.image_url" :alt="paymentMethod.name" class="mt-1 h-24 w-48 rounded object-cover">
                                    </div>
                                    
                                    <div v-if="!paymentMethod.icon_url && !paymentMethod.image_url">
                                        <p class="text-sm text-gray-500 dark:text-gray-400">No images uploaded</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Usage Statistics -->
                <div class="bg-white dark:bg-gray-800 overflow-hidden shadow-sm sm:rounded-lg mb-6">
                    <div class="p-6">
                        <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100 mb-4">Usage Statistics</h3>
                        
                        <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                            <div class="bg-blue-50 dark:bg-blue-900/20 rounded-lg p-4">
                                <div class="flex items-center">
                                    <div class="flex-shrink-0">
                                        <DocumentTextIcon class="h-8 w-8 text-blue-600" />
                                    </div>
                                    <div class="ml-4">
                                        <p class="text-sm font-medium text-blue-600 dark:text-blue-400">Total Orders</p>
                                        <p class="text-2xl font-semibold text-blue-900 dark:text-blue-100">{{ stats?.total_orders || 0 }}</p>
                                    </div>
                                </div>
                            </div>

                            <div class="bg-green-50 dark:bg-green-900/20 rounded-lg p-4">
                                <div class="flex items-center">
                                    <div class="flex-shrink-0">
                                        <CurrencyDollarIcon class="h-8 w-8 text-green-600" />
                                    </div>
                                    <div class="ml-4">
                                        <p class="text-sm font-medium text-green-600 dark:text-green-400">Total Amount</p>
                                        <p class="text-2xl font-semibold text-green-900 dark:text-green-100">
                                            ${{ (stats?.total_amount || 0).toFixed(2) }}
                                        </p>
                                    </div>
                                </div>
                            </div>

                            <div class="bg-purple-50 dark:bg-purple-900/20 rounded-lg p-4">
                                <div class="flex items-center">
                                    <div class="flex-shrink-0">
                                        <ChartBarIcon class="h-8 w-8 text-purple-600" />
                                    </div>
                                    <div class="ml-4">
                                        <p class="text-sm font-medium text-purple-600 dark:text-purple-400">Average Order</p>
                                        <p class="text-2xl font-semibold text-purple-900 dark:text-purple-100">
                                            ${{ stats?.total_orders > 0 ? ((stats?.total_amount || 0) / stats.total_orders).toFixed(2) : '0.00' }}
                                        </p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Custom Fields -->
                <div class="bg-white dark:bg-gray-800 overflow-hidden shadow-sm sm:rounded-lg mb-6">
                    <div class="p-6">
                        <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100 mb-4">Custom Fields</h3>
                        
                        <div v-if="!paymentMethod.fields || paymentMethod.fields.length === 0" class="text-center py-8">
                            <DocumentTextIcon class="mx-auto h-12 w-12 text-gray-400" />
                            <p class="mt-2 text-sm text-gray-500 dark:text-gray-400">No custom fields configured</p>
                            <p class="text-sm text-gray-500 dark:text-gray-400">This payment method doesn't require additional customer information</p>
                        </div>

                        <div v-else class="space-y-4">
                            <div
                                v-for="field in paymentMethod.fields"
                                :key="field.id"
                                class="bg-gray-50 dark:bg-gray-700 rounded-lg p-4"
                            >
                                <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                                    <div>
                                        <label class="block text-sm font-medium text-gray-500 dark:text-gray-400">Field Name</label>
                                        <p class="mt-1 text-sm text-gray-900 dark:text-gray-100 font-mono">{{ field.field_name }}</p>
                                    </div>
                                    
                                    <div>
                                        <label class="block text-sm font-medium text-gray-500 dark:text-gray-400">Display Label</label>
                                        <p class="mt-1 text-sm text-gray-900 dark:text-gray-100">{{ field.field_label }}</p>
                                    </div>
                                    
                                    <div>
                                        <label class="block text-sm font-medium text-gray-500 dark:text-gray-400">Type</label>
                                        <span class="mt-1 inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-gray-100 text-gray-800">
                                            {{ field.field_type_label }}
                                        </span>
                                    </div>
                                </div>

                                <div class="mt-4 grid grid-cols-1 md:grid-cols-2 gap-4">
                                    <div v-if="field.placeholder">
                                        <label class="block text-sm font-medium text-gray-500 dark:text-gray-400">Placeholder</label>
                                        <p class="mt-1 text-sm text-gray-900 dark:text-gray-100">{{ field.placeholder }}</p>
                                    </div>
                                    
                                    <div v-if="field.default_value">
                                        <label class="block text-sm font-medium text-gray-500 dark:text-gray-400">Default Value</label>
                                        <p class="mt-1 text-sm text-gray-900 dark:text-gray-100">{{ field.default_value }}</p>
                                    </div>
                                    
                                    <div v-if="field.help_text">
                                        <label class="block text-sm font-medium text-gray-500 dark:text-gray-400">Help Text</label>
                                        <p class="mt-1 text-sm text-gray-900 dark:text-gray-100">{{ field.help_text }}</p>
                                    </div>
                                    
                                    <div>
                                        <label class="block text-sm font-medium text-gray-500 dark:text-gray-400">Required</label>
                                        <span 
                                            :class="field.is_required ? 'bg-red-100 text-red-800' : 'bg-gray-100 text-gray-800'"
                                            class="mt-1 inline-flex px-2 py-1 text-xs font-semibold rounded-full"
                                        >
                                            {{ field.is_required ? 'Yes' : 'No' }}
                                        </span>
                                    </div>
                                </div>

                                <!-- Select Options -->
                                <div v-if="field.field_type === 'select' && field.select_options && field.select_options.length > 0" class="mt-4">
                                    <label class="block text-sm font-medium text-gray-500 dark:text-gray-400 mb-2">Select Options</label>
                                    <div class="space-y-1">
                                        <div
                                            v-for="option in field.select_options"
                                            :key="option.value"
                                            class="flex items-center space-x-2 text-sm"
                                        >
                                            <span class="font-mono text-gray-600 dark:text-gray-400">{{ option.value }}</span>
                                            <span class="text-gray-500">→</span>
                                            <span class="text-gray-900 dark:text-gray-100">{{ option.label }}</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Recent Orders -->
                <div v-if="stats?.recent_orders && stats.recent_orders.length > 0" class="bg-white dark:bg-gray-800 overflow-hidden shadow-sm sm:rounded-lg">
                    <div class="p-6">
                        <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100 mb-4">Recent Orders</h3>
                        
                        <div class="overflow-x-auto">
                            <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                                <thead class="bg-gray-50 dark:bg-gray-700">
                                    <tr>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                                            Order
                                        </th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                                            Customer
                                        </th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                                            Amount
                                        </th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                                            Date
                                        </th>
                                    </tr>
                                </thead>
                                <tbody class="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                                    <tr v-for="order in stats.recent_orders" :key="order.id" class="hover:bg-gray-50 dark:hover:bg-gray-700">
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <div class="text-sm font-medium text-gray-900 dark:text-gray-100">
                                                #{{ order.order_number }}
                                            </div>
                                            <div class="text-sm text-gray-500 dark:text-gray-400">
                                                {{ order.order_type }}
                                            </div>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <div class="text-sm text-gray-900 dark:text-gray-100">
                                                {{ order.customer?.name || order.customer_name || 'Guest' }}
                                            </div>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <div class="text-sm font-medium text-gray-900 dark:text-gray-100">
                                                ${{ parseFloat(order.total_amount).toFixed(2) }}
                                            </div>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                                            {{ new Date(order.created_at).toLocaleDateString() }}
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </ManagerLayout>
</template>

<script setup>
import { Link } from '@inertiajs/vue3'
import ManagerLayout from '@/Layouts/ManagerLayout.vue'
import {
    PencilIcon,
    DocumentTextIcon,
    CurrencyDollarIcon,
    ChartBarIcon
} from '@heroicons/vue/24/outline'

const props = defineProps({
    paymentMethod: Object,
    stats: Object,
})
</script>
