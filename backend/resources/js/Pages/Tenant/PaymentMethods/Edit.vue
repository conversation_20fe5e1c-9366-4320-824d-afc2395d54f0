<template>
    <ManagerLayout>
        <template #header>
            <div class="flex justify-between items-center">
                <div>
                    <h2 class="font-semibold text-xl text-gray-800 dark:text-gray-200 leading-tight">
                        Edit Payment Method
                    </h2>
                    <p class="text-sm text-gray-600 dark:text-gray-400 mt-1">
                        Update payment method details and custom fields
                    </p>
                </div>
                <div class="flex space-x-3">
                    <Link
                        :href="route('payment-methods.show', paymentMethod.id)"
                        class="bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded-md text-sm font-medium transition-colors duration-200"
                    >
                        View Details
                    </Link>
                    <Link
                        :href="route('payment-methods.index')"
                        class="bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded-md text-sm font-medium transition-colors duration-200"
                    >
                        Back to List
                    </Link>
                </div>
            </div>
        </template>

        <div class="py-6">
            <div class="max-w-4xl mx-auto sm:px-6 lg:px-8">
                <div class="bg-white dark:bg-gray-800 overflow-hidden shadow-sm sm:rounded-lg">
                    <form @submit.prevent="submit" class="p-6 space-y-6">
                        <!-- Basic Information -->
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <div>
                                <label for="name" class="block text-sm font-medium text-gray-700 dark:text-gray-300">
                                    Payment Method Name *
                                </label>
                                <input
                                    type="text"
                                    id="name"
                                    v-model="form.name"
                                    required
                                    class="mt-1 block w-full border-gray-300 dark:border-gray-700 dark:bg-gray-900 dark:text-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500"
                                    placeholder="e.g., Credit Card, PayPal, Bank Transfer"
                                />
                                <div v-if="errors.name" class="mt-1 text-sm text-red-600">{{ errors.name }}</div>
                            </div>

                            <div>
                                <label for="type" class="block text-sm font-medium text-gray-700 dark:text-gray-300">
                                    Payment Type *
                                </label>
                                <select
                                    id="type"
                                    v-model="form.type"
                                    required
                                    class="mt-1 block w-full border-gray-300 dark:border-gray-700 dark:bg-gray-900 dark:text-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500"
                                >
                                    <option value="">Select Type</option>
                                    <option v-for="(label, value) in paymentTypes" :key="value" :value="value">
                                        {{ label }}
                                    </option>
                                </select>
                                <div v-if="errors.type" class="mt-1 text-sm text-red-600">{{ errors.type }}</div>
                            </div>

                            <div>
                                <label for="display_order" class="block text-sm font-medium text-gray-700 dark:text-gray-300">
                                    Display Order
                                </label>
                                <input
                                    type="number"
                                    id="display_order"
                                    v-model.number="form.display_order"
                                    min="0"
                                    class="mt-1 block w-full border-gray-300 dark:border-gray-700 dark:bg-gray-900 dark:text-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500"
                                    placeholder="0"
                                />
                                <div v-if="errors.display_order" class="mt-1 text-sm text-red-600">{{ errors.display_order }}</div>
                            </div>

                            <div class="flex items-center">
                                <input
                                    type="checkbox"
                                    id="is_active"
                                    v-model="form.is_active"
                                    class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                                />
                                <label for="is_active" class="ml-2 block text-sm text-gray-900 dark:text-gray-300">
                                    Active (available for customers)
                                </label>
                            </div>
                        </div>

                        <!-- Instructions -->
                        <div>
                            <label for="instructions" class="block text-sm font-medium text-gray-700 dark:text-gray-300">
                                Customer Instructions
                            </label>
                            <textarea
                                id="instructions"
                                v-model="form.instructions"
                                rows="3"
                                class="mt-1 block w-full border-gray-300 dark:border-gray-700 dark:bg-gray-900 dark:text-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500"
                                placeholder="Instructions that will be shown to customers when they select this payment method"
                            ></textarea>
                            <div v-if="errors.instructions" class="mt-1 text-sm text-red-600">{{ errors.instructions }}</div>
                        </div>

                        <!-- File Uploads -->
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <div>
                                <label for="icon" class="block text-sm font-medium text-gray-700 dark:text-gray-300">
                                    Icon (Optional)
                                </label>
                                <input
                                    type="file"
                                    id="icon"
                                    @change="handleIconUpload"
                                    accept="image/*"
                                    class="mt-1 block w-full text-sm text-gray-500 file:mr-4 file:py-2 file:px-4 file:rounded-full file:border-0 file:text-sm file:font-semibold file:bg-blue-50 file:text-blue-700 hover:file:bg-blue-100"
                                />
                                <p class="mt-1 text-xs text-gray-500">Small icon for payment method (recommended: 32x32px)</p>
                                <div v-if="paymentMethod.icon_url" class="mt-2">
                                    <img :src="paymentMethod.icon_url" :alt="paymentMethod.name" class="h-8 w-8 rounded">
                                    <p class="text-xs text-gray-500">Current icon</p>
                                </div>
                                <div v-if="errors.icon" class="mt-1 text-sm text-red-600">{{ errors.icon }}</div>
                            </div>

                            <div>
                                <label for="image" class="block text-sm font-medium text-gray-700 dark:text-gray-300">
                                    Image (Optional)
                                </label>
                                <input
                                    type="file"
                                    id="image"
                                    @change="handleImageUpload"
                                    accept="image/*"
                                    class="mt-1 block w-full text-sm text-gray-500 file:mr-4 file:py-2 file:px-4 file:rounded-full file:border-0 file:text-sm file:font-semibold file:bg-blue-50 file:text-blue-700 hover:file:bg-blue-100"
                                />
                                <p class="mt-1 text-xs text-gray-500">Larger image for detailed view (recommended: 200x100px)</p>
                                <div v-if="paymentMethod.image_url" class="mt-2">
                                    <img :src="paymentMethod.image_url" :alt="paymentMethod.name" class="h-16 w-32 rounded object-cover">
                                    <p class="text-xs text-gray-500">Current image</p>
                                </div>
                                <div v-if="errors.image" class="mt-1 text-sm text-red-600">{{ errors.image }}</div>
                            </div>
                        </div>

                        <!-- Custom Fields Section -->
                        <div class="border-t border-gray-200 dark:border-gray-700 pt-6">
                            <div class="flex justify-between items-center mb-4">
                                <div>
                                    <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100">Custom Fields</h3>
                                    <p class="text-sm text-gray-600 dark:text-gray-400">Add custom fields that customers need to fill when using this payment method</p>
                                </div>
                                <button
                                    type="button"
                                    @click="addField"
                                    class="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-md text-sm font-medium transition-colors duration-200"
                                >
                                    <PlusIcon class="w-4 h-4 inline mr-2" />
                                    Add Field
                                </button>
                            </div>

                            <div v-if="form.fields.length === 0" class="text-center py-8 border-2 border-dashed border-gray-300 dark:border-gray-600 rounded-lg">
                                <DocumentTextIcon class="mx-auto h-12 w-12 text-gray-400" />
                                <p class="mt-2 text-sm text-gray-500 dark:text-gray-400">No custom fields added yet</p>
                                <p class="text-sm text-gray-500 dark:text-gray-400">Click "Add Field" to create custom fields for this payment method</p>
                            </div>

                            <div v-else class="space-y-4">
                                <div
                                    v-for="(field, index) in form.fields"
                                    :key="field.id || index"
                                    class="bg-gray-50 dark:bg-gray-700 rounded-lg p-4"
                                >
                                    <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                                        <div>
                                            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                                                Field Name *
                                            </label>
                                            <input
                                                type="text"
                                                v-model="field.field_name"
                                                required
                                                class="w-full border-gray-300 dark:border-gray-700 dark:bg-gray-900 dark:text-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500"
                                                placeholder="e.g., card_number, account_number"
                                            />
                                        </div>

                                        <div>
                                            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                                                Field Type *
                                            </label>
                                            <select
                                                v-model="field.field_type"
                                                required
                                                class="w-full border-gray-300 dark:border-gray-700 dark:bg-gray-900 dark:text-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500"
                                            >
                                                <option value="">Select Type</option>
                                                <option v-for="(label, value) in fieldTypes" :key="value" :value="value">
                                                    {{ label }}
                                                </option>
                                            </select>
                                        </div>

                                        <div>
                                            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                                                Display Label *
                                            </label>
                                            <input
                                                type="text"
                                                v-model="field.field_label"
                                                required
                                                class="w-full border-gray-300 dark:border-gray-700 dark:bg-gray-900 dark:text-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500"
                                                placeholder="e.g., Card Number, Account Number"
                                            />
                                        </div>

                                        <div>
                                            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                                                Placeholder
                                            </label>
                                            <input
                                                type="text"
                                                v-model="field.placeholder"
                                                class="w-full border-gray-300 dark:border-gray-700 dark:bg-gray-900 dark:text-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500"
                                                placeholder="Placeholder text"
                                            />
                                        </div>

                                        <div>
                                            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                                                Default Value
                                            </label>
                                            <input
                                                type="text"
                                                v-model="field.default_value"
                                                class="w-full border-gray-300 dark:border-gray-700 dark:bg-gray-900 dark:text-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500"
                                                placeholder="Default value"
                                            />
                                        </div>

                                        <div>
                                            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                                                Display Order
                                            </label>
                                            <input
                                                type="number"
                                                v-model.number="field.display_order"
                                                min="0"
                                                class="w-full border-gray-300 dark:border-gray-700 dark:bg-gray-900 dark:text-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500"
                                                placeholder="0"
                                            />
                                        </div>
                                    </div>

                                    <div class="mt-4 grid grid-cols-1 md:grid-cols-2 gap-4">
                                        <div>
                                            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                                                Help Text
                                            </label>
                                            <input
                                                type="text"
                                                v-model="field.help_text"
                                                class="w-full border-gray-300 dark:border-gray-700 dark:bg-gray-900 dark:text-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500"
                                                placeholder="Additional help text for users"
                                            />
                                        </div>

                                        <div class="flex items-center space-x-4">
                                            <div class="flex items-center">
                                                <input
                                                    type="checkbox"
                                                    v-model="field.is_required"
                                                    class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                                                />
                                                <label class="ml-2 block text-sm text-gray-900 dark:text-gray-300">
                                                    Required
                                                </label>
                                            </div>

                                            <button
                                                type="button"
                                                @click="removeField(index)"
                                                class="text-red-600 hover:text-red-800 text-sm font-medium"
                                            >
                                                Remove Field
                                            </button>
                                        </div>
                                    </div>

                                    <!-- Select Options (only for select field type) -->
                                    <div v-if="field.field_type === 'select'" class="mt-4">
                                        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                                            Select Options
                                        </label>
                                        <div class="space-y-2">
                                            <div
                                                v-for="(option, optionIndex) in field.select_options"
                                                :key="optionIndex"
                                                class="flex items-center space-x-2"
                                            >
                                                <input
                                                    type="text"
                                                    v-model="option.label"
                                                    placeholder="Option Label"
                                                    class="flex-1 border-gray-300 dark:border-gray-700 dark:bg-gray-900 dark:text-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500"
                                                />
                                                <input
                                                    type="text"
                                                    v-model="option.value"
                                                    placeholder="Option Value"
                                                    class="flex-1 border-gray-300 dark:border-gray-700 dark:bg-gray-900 dark:text-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500"
                                                />
                                                <button
                                                    type="button"
                                                    @click="removeSelectOption(index, optionIndex)"
                                                    class="text-red-600 hover:text-red-800"
                                                >
                                                    <XMarkIcon class="w-4 h-4" />
                                                </button>
                                            </div>
                                            <button
                                                type="button"
                                                @click="addSelectOption(index)"
                                                class="text-blue-600 hover:text-blue-800 text-sm font-medium"
                                            >
                                                + Add Option
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Submit Buttons -->
                        <div class="border-t border-gray-200 dark:border-gray-700 pt-6">
                            <div class="flex justify-end space-x-3">
                                <Link
                                    :href="route('payment-methods.index')"
                                    class="bg-gray-300 hover:bg-gray-400 text-gray-800 px-4 py-2 rounded-md text-sm font-medium transition-colors duration-200"
                                >
                                    Cancel
                                </Link>
                                <button
                                    type="submit"
                                    :disabled="processing"
                                    class="bg-blue-600 hover:bg-blue-700 disabled:bg-blue-400 text-white px-4 py-2 rounded-md text-sm font-medium transition-colors duration-200"
                                >
                                    {{ processing ? 'Updating...' : 'Update Payment Method' }}
                                </button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </ManagerLayout>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { Link, router } from '@inertiajs/vue3'
import ManagerLayout from '@/Layouts/ManagerLayout.vue'
import { PlusIcon, DocumentTextIcon, XMarkIcon } from '@heroicons/vue/24/outline'

const props = defineProps({
    paymentMethod: Object,
    paymentTypes: Object,
    fieldTypes: Object,
    errors: Object,
})

const processing = ref(false)

const form = reactive({
    name: '',
    type: '',
    instructions: '',
    is_active: true,
    display_order: 0,
    icon: null,
    image: null,
    fields: [],
})

// Initialize form with existing data
onMounted(() => {
    form.name = props.paymentMethod.name
    form.type = props.paymentMethod.type
    form.instructions = props.paymentMethod.instructions || ''
    form.is_active = props.paymentMethod.is_active
    form.display_order = props.paymentMethod.display_order
    form.fields = props.paymentMethod.fields ? [...props.paymentMethod.fields] : []
    
    // Ensure select_options is an array for each field
    form.fields.forEach(field => {
        if (!field.select_options) {
            field.select_options = []
        }
    })
})

const addField = () => {
    form.fields.push({
        field_name: '',
        field_type: '',
        field_label: '',
        is_required: false,
        default_value: '',
        validation_rules: {},
        select_options: [],
        display_order: form.fields.length,
        placeholder: '',
        help_text: '',
    })
}

const removeField = (index) => {
    form.fields.splice(index, 1)
}

const addSelectOption = (fieldIndex) => {
    if (!form.fields[fieldIndex].select_options) {
        form.fields[fieldIndex].select_options = []
    }
    form.fields[fieldIndex].select_options.push({
        label: '',
        value: '',
    })
}

const removeSelectOption = (fieldIndex, optionIndex) => {
    form.fields[fieldIndex].select_options.splice(optionIndex, 1)
}

const handleIconUpload = (event) => {
    form.icon = event.target.files[0]
}

const handleImageUpload = (event) => {
    form.image = event.target.files[0]
}

const submit = () => {
    processing.value = true
    
    router.post(route('payment-methods.update', props.paymentMethod.id), {
        ...form,
        _method: 'PUT'
    }, {
        onFinish: () => {
            processing.value = false
        },
        onSuccess: () => {
            // Success handled by redirect
        },
        onError: (errors) => {
            console.error('Validation errors:', errors)
        }
    })
}
</script>
