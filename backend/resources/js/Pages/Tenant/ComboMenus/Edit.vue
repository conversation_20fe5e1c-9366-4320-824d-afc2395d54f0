<template>
    <ManagerLayout>
        <Head :title="`Edit Combo: ${combo.name}`" />

        <div class="py-12">
            <div class="max-w-4xl mx-auto sm:px-6 lg:px-8">
                <div class="bg-white dark:bg-gray-800 overflow-hidden shadow-xl sm:rounded-lg">
                    <div class="p-6 lg:p-8">
                        <!-- Header -->
                        <div class="flex items-center justify-between mb-8">
                            <div>
                                <h1 class="text-2xl font-bold text-gray-900 dark:text-white">Edit Combo Item</h1>
                                <p class="mt-2 text-sm text-gray-600 dark:text-gray-400">
                                    Update combo menu item and its components.
                                </p>
                            </div>
                            <div class="flex space-x-3">
                                <Link
                                    :href="route('combo-menus.show', combo.id)"
                                    class="inline-flex items-center px-4 py-2 bg-gray-600 border border-transparent rounded-md font-semibold text-xs text-white uppercase tracking-widest hover:bg-gray-700 focus:bg-gray-700 active:bg-gray-900 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2 dark:focus:ring-offset-gray-800 transition ease-in-out duration-150"
                                >
                                    <i class="fas fa-eye mr-2"></i>
                                    View
                                </Link>
                                <Link
                                    :href="route('combo-menus.index')"
                                    class="inline-flex items-center px-4 py-2 bg-gray-600 border border-transparent rounded-md font-semibold text-xs text-white uppercase tracking-widest hover:bg-gray-700 focus:bg-gray-700 active:bg-gray-900 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2 dark:focus:ring-offset-gray-800 transition ease-in-out duration-150"
                                >
                                    <i class="fas fa-arrow-left mr-2"></i>
                                    Back to List
                                </Link>
                            </div>
                        </div>

                        <!-- Form -->
                        <form @submit.prevent="submit" class="space-y-6">
                            <!-- Basic Information -->
                            <div class="bg-gray-50 dark:bg-gray-700 rounded-lg p-6">
                                <h2 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">
                                    Basic Information
                                </h2>

                                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                                    <div>
                                        <InputLabel for="name" value="Combo Name *" />
                                        <TextInput
                                            id="name"
                                            v-model="form.name"
                                            type="text"
                                            class="mt-1 block w-full"
                                            required
                                            placeholder="e.g., Family Set Menu"
                                        />
                                        <InputError class="mt-2" :message="form.errors.name" />
                                    </div>

                                    <div>
                                        <InputLabel for="category_id" value="Category" />
                                        <select
                                            id="category_id"
                                            v-model="form.category_id"
                                            class="mt-1 block w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
                                        >
                                            <option value="">Select Category</option>
                                            <option
                                                v-for="category in categories"
                                                :key="category.id"
                                                :value="category.id"
                                            >
                                                {{ category.name }}
                                            </option>
                                        </select>
                                        <InputError class="mt-2" :message="form.errors.category_id" />
                                    </div>

                                    <div>
                                        <InputLabel for="price" value="Combo Price *" />
                                        <TextInput
                                            id="price"
                                            v-model="form.price"
                                            type="number"
                                            step="0.01"
                                            min="0"
                                            class="mt-1 block w-full"
                                            required
                                            placeholder="0.00"
                                        />
                                        <InputError class="mt-2" :message="form.errors.price" />
                                    </div>

                                    <div class="md:col-span-2">
                                        <InputLabel for="description" value="Description" />
                                        <textarea
                                            id="description"
                                            v-model="form.description"
                                            rows="3"
                                            class="mt-1 block w-full border-gray-300 dark:border-gray-700 dark:bg-gray-900 dark:text-gray-300 focus:border-indigo-500 dark:focus:border-indigo-600 focus:ring-indigo-500 dark:focus:ring-indigo-600 rounded-md shadow-sm"
                                            placeholder="Describe what's included in this combo..."
                                        ></textarea>
                                        <InputError class="mt-2" :message="form.errors.description" />
                                    </div>

                                    <div class="md:col-span-2">
                                        <MediaPicker
                                            v-model="form.selectedMedia"
                                            label="Combo Image"
                                            placeholder="Select an image for this combo..."
                                            :error="form.errors.media_id"
                                        />
                                    </div>

                                    <div>
                                        <InputLabel for="sort_order" value="Sort Order" />
                                        <TextInput
                                            id="sort_order"
                                            v-model="form.sort_order"
                                            type="number"
                                            min="0"
                                            class="mt-1 block w-full"
                                            placeholder="0"
                                        />
                                        <InputError class="mt-2" :message="form.errors.sort_order" />
                                    </div>

                                    <div class="md:col-span-2">
                                        <label class="flex items-center">
                                            <Checkbox v-model:checked="form.is_available" />
                                            <span class="ml-2 text-sm text-gray-600 dark:text-gray-400">Available for ordering</span>
                                        </label>
                                    </div>
                                </div>
                            </div>

                            <!-- Components -->
                            <div class="bg-gray-50 dark:bg-gray-700 rounded-lg p-6">
                                <div class="flex items-center justify-between mb-4">
                                    <h2 class="text-lg font-semibold text-gray-900 dark:text-white">
                                        Combo Components
                                    </h2>
                                    <button
                                        type="button"
                                        @click="addComponent"
                                        class="inline-flex items-center px-3 py-2 bg-indigo-600 border border-transparent rounded-md font-semibold text-xs text-white uppercase tracking-widest hover:bg-indigo-700 focus:bg-indigo-700 active:bg-indigo-900 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 dark:focus:ring-offset-gray-800 transition ease-in-out duration-150"
                                    >
                                        <i class="fas fa-plus mr-2"></i>
                                        Add Component
                                    </button>
                                </div>

                                <div v-if="form.components.length === 0" class="text-center py-8 text-gray-500 dark:text-gray-400">
                                    <i class="fas fa-utensils text-4xl mb-4"></i>
                                    <p>No components added yet. Click "Add Component" to get started.</p>
                                </div>

                                <div v-else class="space-y-4">
                                    <div
                                        v-for="(component, index) in form.components"
                                        :key="component.id || index"
                                        class="bg-white dark:bg-gray-800 rounded-lg p-4 border border-gray-200 dark:border-gray-600"
                                    >
                                        <div class="flex items-center justify-between mb-4">
                                            <h3 class="text-md font-medium text-gray-900 dark:text-white">
                                                Component {{ index + 1 }}
                                            </h3>
                                            <button
                                                type="button"
                                                @click="removeComponent(index)"
                                                class="text-red-600 hover:text-red-900 dark:text-red-400 dark:hover:text-red-300"
                                            >
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </div>

                                        <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                                            <div>
                                                <InputLabel :for="`component_type_${index}`" value="Component Type *" />
                                                <select
                                                    :id="`component_type_${index}`"
                                                    v-model="component.component_type"
                                                    class="mt-1 block w-full border-gray-300 dark:border-gray-700 dark:bg-gray-900 dark:text-gray-300 focus:border-indigo-500 dark:focus:border-indigo-600 focus:ring-indigo-500 dark:focus:ring-indigo-600 rounded-md shadow-sm"
                                                    required
                                                >
                                                    <option value="">Select Type</option>
                                                    <option value="main">Main</option>
                                                    <option value="side">Side</option>
                                                    <option value="drink">Drink</option>
                                                    <option value="dessert">Dessert</option>
                                                </select>
                                                <InputError class="mt-2" :message="form.errors[`components.${index}.component_type`]" />
                                            </div>

                                            <div>
                                                <InputLabel :for="`menu_item_${index}`" value="Menu Item *" />
                                                <select
                                                    :id="`menu_item_${index}`"
                                                    v-model="component.menu_item_id"
                                                    class="mt-1 block w-full border-gray-300 dark:border-gray-700 dark:bg-gray-900 dark:text-gray-300 focus:border-indigo-500 dark:focus:border-indigo-600 focus:ring-indigo-500 dark:focus:ring-indigo-600 rounded-md shadow-sm"
                                                    required
                                                >
                                                    <option value="">Select Menu Item</option>
                                                    <option
                                                        v-for="menuItem in menuItems"
                                                        :key="menuItem.id"
                                                        :value="menuItem.id"
                                                    >
                                                        {{ menuItem.name }} - ${{ menuItem.price }}
                                                    </option>
                                                </select>
                                                <InputError class="mt-2" :message="form.errors[`components.${index}.menu_item_id`]" />
                                            </div>

                                            <div>
                                                <InputLabel :for="`sort_order_${index}`" value="Sort Order" />
                                                <TextInput
                                                    :id="`sort_order_${index}`"
                                                    v-model="component.sort_order"
                                                    type="number"
                                                    min="0"
                                                    class="mt-1 block w-full"
                                                    placeholder="0"
                                                />
                                            </div>

                                            <div class="md:col-span-3">
                                                <label class="flex items-center">
                                                    <Checkbox v-model:checked="component.is_required" />
                                                    <span class="ml-2 text-sm text-gray-600 dark:text-gray-400">Required component</span>
                                                </label>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Submit Button -->
                            <div class="flex items-center justify-end space-x-4">
                                <Link
                                    :href="route('combo-menus.show', combo.id)"
                                    class="inline-flex items-center px-4 py-2 bg-gray-600 border border-transparent rounded-md font-semibold text-xs text-white uppercase tracking-widest hover:bg-gray-700 focus:bg-gray-700 active:bg-gray-900 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2 dark:focus:ring-offset-gray-800 transition ease-in-out duration-150"
                                >
                                    Cancel
                                </Link>
                                <PrimaryButton :class="{ 'opacity-25': form.processing }" :disabled="form.processing">
                                    <i class="fas fa-save mr-2"></i>
                                    Update Combo Item
                                </PrimaryButton>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </ManagerLayout>
</template>

<script setup>
import { Head, Link, useForm } from '@inertiajs/vue3'
import ManagerLayout from '@/Layouts/ManagerLayout.vue'
import InputLabel from '@/Components/InputLabel.vue'
import TextInput from '@/Components/TextInput.vue'
import InputError from '@/Components/InputError.vue'
import PrimaryButton from '@/Components/PrimaryButton.vue'
import Checkbox from '@/Components/Checkbox.vue'
import MediaPicker from '@/Components/MediaLibrary/MediaPicker.vue'

const props = defineProps({
    combo: Object,
    menuItems: Array,
    categories: Array,
})

const form = useForm({
    name: props.combo.name,
    description: props.combo.description || '',
    price: props.combo.price,
    category_id: props.combo.category_id || '',
    is_available: props.combo.is_available,
    sort_order: props.combo.sort_order || 0,
    selectedMedia: props.combo.primary_media || null,
    media_id: props.combo.media_id || null,
    components: props.combo.combo_items ? props.combo.combo_items.map(item => ({
        component_type: item.pivot.component_type,
        menu_item_id: item.id,
        is_required: item.pivot.is_required,
        sort_order: item.pivot.sort_order || 0,
    })) : [],
})

const addComponent = () => {
    form.components.push({
        component_type: '',
        menu_item_id: '',
        is_required: false,
        sort_order: 0,
    })
}

const removeComponent = (index) => {
    form.components.splice(index, 1)
}

const submit = () => {
    // Set media_id from selected media
    if (form.selectedMedia) {
        form.media_id = form.selectedMedia.id
    }

    form.put(route('combo-menus.update', props.combo.id))
}
</script>
