<template>
    <ManagerLayout>
        <Head title="Combo Items" />

        <div class="py-12">
            <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
                <div class="bg-white dark:bg-gray-800 overflow-hidden shadow-xl sm:rounded-lg">
                    <div class="p-6 lg:p-8">
                        <!-- Header -->
                        <div class="flex items-center justify-between mb-8">
                            <div>
                                <h1 class="text-2xl font-bold text-gray-900 dark:text-white">Combo Items</h1>
                                <p class="mt-2 text-sm text-gray-600 dark:text-gray-400">
                                    Manage your combo menu items and their components.
                                </p>
                            </div>
                            <Link
                                :href="route('combo-menus.create')"
                                class="inline-flex items-center px-4 py-2 bg-indigo-600 border border-transparent rounded-md font-semibold text-xs text-white uppercase tracking-widest hover:bg-indigo-700 focus:bg-indigo-700 active:bg-indigo-900 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 dark:focus:ring-offset-gray-800 transition ease-in-out duration-150"
                            >
                                <i class="fas fa-plus mr-2"></i>
                                Add Combo Item
                            </Link>
                        </div>

                        <!-- Filters -->
                        <div class="mb-6 flex flex-col sm:flex-row gap-4">
                            <div class="flex-1">
                                <input
                                    v-model="filters.search"
                                    @input="search"
                                    type="text"
                                    placeholder="Search combo items..."
                                    class="w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
                                />
                            </div>
                            <div class="sm:w-48">
                                <select
                                    v-model="filters.is_available"
                                    @change="search"
                                    class="w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
                                >
                                    <option value="">All Status</option>
                                    <option value="1">Available</option>
                                    <option value="0">Unavailable</option>
                                </select>
                            </div>
                        </div>

                        <!-- Combo Items Grid -->
                        <div v-if="combos.data.length > 0" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                            <div
                                v-for="combo in combos.data"
                                :key="combo.id"
                                class="bg-white dark:bg-gray-700 rounded-lg shadow-md overflow-hidden border border-gray-200 dark:border-gray-600 hover:shadow-lg transition-shadow duration-200"
                            >
                                <!-- Combo Image -->
                                <div class="h-48 bg-gray-200 dark:bg-gray-600 flex items-center justify-center">
                                    <img
                                        v-if="combo.primary_image_url || combo.image_url"
                                        :src="combo.primary_image_url || combo.image_url"
                                        :alt="combo.name"
                                        class="w-full h-full object-cover"
                                    />
                                    <div v-else class="text-gray-400 dark:text-gray-500">
                                        <i class="fas fa-utensils text-4xl"></i>
                                    </div>
                                </div>

                                <!-- Combo Info -->
                                <div class="p-4">
                                    <div class="flex items-start justify-between mb-2">
                                        <h3 class="text-lg font-semibold text-gray-900 dark:text-white">
                                            {{ combo.name }}
                                        </h3>
                                        <span
                                            :class="[
                                                'px-2 py-1 text-xs font-medium rounded-full',
                                                combo.is_available
                                                    ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200'
                                                    : 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200'
                                            ]"
                                        >
                                            {{ combo.is_available ? 'Available' : 'Unavailable' }}
                                        </span>
                                    </div>

                                    <p v-if="combo.description" class="text-sm text-gray-600 dark:text-gray-400 mb-3">
                                        {{ combo.description }}
                                    </p>

                                    <!-- Price -->
                                    <div class="flex items-center justify-between mb-3">
                                        <span class="text-xl font-bold text-indigo-600 dark:text-indigo-400">
                                            ${{ combo.price }}
                                        </span>
                                        <span v-if="combo.individual_total" class="text-sm text-gray-500 line-through">
                                            ${{ combo.individual_total }}
                                        </span>
                                    </div>

                                    <!-- Components -->
                                    <div class="mb-4">
                                        <h4 class="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Components:</h4>
                                        <div class="space-y-1">
                                            <div
                                                v-for="component in combo.components"
                                                :key="component.id"
                                                class="text-xs text-gray-600 dark:text-gray-400 flex items-center"
                                            >
                                                <span class="w-2 h-2 bg-indigo-400 rounded-full mr-2"></span>
                                                {{ component.menu_item.name }}
                                                <span class="ml-1 text-gray-500">({{ component.component_type }})</span>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- Actions -->
                                    <div class="flex items-center justify-between pt-3 border-t border-gray-200 dark:border-gray-600">
                                        <div class="flex space-x-1">
                                            <Link
                                                :href="route('combo-menus.show', combo.id)"
                                                class="inline-flex items-center px-2 py-1 bg-blue-100 text-blue-700 rounded-md hover:bg-blue-200 text-xs font-medium transition-colors"
                                                title="View Details"
                                            >
                                                <i class="fas fa-eye mr-1"></i>
                                                View
                                            </Link>
                                            <Link
                                                :href="route('combo-menus.edit', combo.id)"
                                                class="inline-flex items-center px-2 py-1 bg-yellow-100 text-yellow-700 rounded-md hover:bg-yellow-200 text-xs font-medium transition-colors"
                                                title="Edit Combo"
                                            >
                                                <i class="fas fa-edit mr-1"></i>
                                                Edit
                                            </Link>
                                            <button
                                                @click="toggleAvailability(combo)"
                                                :disabled="loadingStates[combo.id]"
                                                :class="[
                                                    'inline-flex items-center px-2 py-1 rounded-md text-xs font-medium transition-colors',
                                                    loadingStates[combo.id] ? 'opacity-50 cursor-not-allowed' : '',
                                                    combo.is_available
                                                        ? 'bg-red-100 text-red-700 hover:bg-red-200'
                                                        : 'bg-green-100 text-green-700 hover:bg-green-200'
                                                ]"
                                                :title="combo.is_available ? 'Mark as Unavailable' : 'Mark as Available'"
                                            >
                                                <i v-if="loadingStates[combo.id]" class="fas fa-spinner fa-spin mr-1"></i>
                                                <i v-else :class="[
                                                    'mr-1',
                                                    combo.is_available ? 'fas fa-eye-slash' : 'fas fa-eye'
                                                ]"></i>
                                                {{ loadingStates[combo.id] ? 'Updating...' : (combo.is_available ? 'Disable' : 'Enable') }}
                                            </button>
                                        </div>
                                        <button
                                            @click="deleteCombo(combo)"
                                            class="inline-flex items-center px-2 py-1 bg-red-100 text-red-700 rounded-md hover:bg-red-200 text-xs font-medium transition-colors"
                                            title="Delete Combo"
                                        >
                                            <i class="fas fa-trash mr-1"></i>
                                            Delete
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Empty State -->
                        <div v-else class="text-center py-12">
                            <i class="fas fa-utensils text-6xl text-gray-400 dark:text-gray-600 mb-4"></i>
                            <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-2">No combo items found</h3>
                            <p class="text-gray-600 dark:text-gray-400 mb-6">
                                Get started by creating your first combo item.
                            </p>
                            <Link
                                :href="route('combo-menus.create')"
                                class="inline-flex items-center px-4 py-2 bg-indigo-600 border border-transparent rounded-md font-semibold text-xs text-white uppercase tracking-widest hover:bg-indigo-700 focus:bg-indigo-700 active:bg-indigo-900 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 dark:focus:ring-offset-gray-800 transition ease-in-out duration-150"
                            >
                                <i class="fas fa-plus mr-2"></i>
                                Add Combo Item
                            </Link>
                        </div>

                        <!-- Pagination -->
                        <div v-if="combos.data.length > 0" class="mt-8">
                            <Pagination :links="combos.links" />
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </ManagerLayout>
</template>

<script setup>
import { Head, Link, router } from '@inertiajs/vue3'
import { ref, reactive } from 'vue'
import ManagerLayout from '@/Layouts/ManagerLayout.vue'
import Pagination from '@/Components/Pagination.vue'

const props = defineProps({
    combos: Object,
    filters: Object,
})

const filters = reactive({
    search: props.filters.search || '',
    is_available: props.filters.is_available || '',
})

const loadingStates = ref({})

const search = () => {
    router.get(route('combo-menus.index'), filters, {
        preserveState: true,
        replace: true,
    })
}

const toggleAvailability = (combo) => {
    loadingStates.value[combo.id] = true

    router.post(route('combo-menus.toggle-availability', combo.id), {}, {
        preserveScroll: true,
        onSuccess: () => {
            loadingStates.value[combo.id] = false
        },
        onError: () => {
            loadingStates.value[combo.id] = false
            alert('Failed to update availability. Please try again.')
        }
    })
}

const deleteCombo = (combo) => {
    if (confirm(`Are you sure you want to delete "${combo.name}"?\n\nThis action cannot be undone and will remove all components associated with this combo.`)) {
        router.delete(route('combo-menus.destroy', combo.id), {
            preserveScroll: true,
            onSuccess: () => {
                // Success message will be shown via flash message
            },
            onError: (errors) => {
                console.error('Failed to delete combo:', errors)
                alert('Failed to delete combo. Please try again.')
            }
        })
    }
}
</script>
