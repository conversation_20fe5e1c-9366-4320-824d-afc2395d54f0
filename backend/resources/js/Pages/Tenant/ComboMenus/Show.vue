<template>
    <ManagerLayout>
        <Head :title="`Combo Item: ${combo.name}`" />

        <div class="py-12">
            <div class="max-w-4xl mx-auto sm:px-6 lg:px-8">
                <div class="bg-white dark:bg-gray-800 overflow-hidden shadow-xl sm:rounded-lg">
                    <div class="p-6 lg:p-8">
                        <!-- Header -->
                        <div class="flex items-center justify-between mb-8">
                            <div>
                                <h1 class="text-2xl font-bold text-gray-900 dark:text-white">{{ combo.name }}</h1>
                                <p class="mt-2 text-sm text-gray-600 dark:text-gray-400">
                                    Combo item details and components
                                </p>
                            </div>
                            <div class="flex space-x-3">
                                <Link
                                    :href="route('combo-menus.edit', combo.id)"
                                    class="inline-flex items-center px-4 py-2 bg-indigo-600 border border-transparent rounded-md font-semibold text-xs text-white uppercase tracking-widest hover:bg-indigo-700 focus:bg-indigo-700 active:bg-indigo-900 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 dark:focus:ring-offset-gray-800 transition ease-in-out duration-150"
                                >
                                    <i class="fas fa-edit mr-2"></i>
                                    Edit
                                </Link>
                                <Link
                                    :href="route('combo-menus.index')"
                                    class="inline-flex items-center px-4 py-2 bg-gray-600 border border-transparent rounded-md font-semibold text-xs text-white uppercase tracking-widest hover:bg-gray-700 focus:bg-gray-700 active:bg-gray-900 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2 dark:focus:ring-offset-gray-800 transition ease-in-out duration-150"
                                >
                                    <i class="fas fa-arrow-left mr-2"></i>
                                    Back to List
                                </Link>
                            </div>
                        </div>

                        <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
                            <!-- Main Content -->
                            <div class="lg:col-span-2 space-y-6">
                                <!-- Basic Information -->
                                <div class="bg-gray-50 dark:bg-gray-700 rounded-lg p-6">
                                    <h2 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">
                                        Basic Information
                                    </h2>
                                    
                                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                        <div>
                                            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300">Name</label>
                                            <p class="mt-1 text-sm text-gray-900 dark:text-white">{{ combo.name }}</p>
                                        </div>
                                        
                                        <div>
                                            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300">Price</label>
                                            <p class="mt-1 text-lg font-bold text-indigo-600 dark:text-indigo-400">${{ combo.price }}</p>
                                        </div>
                                        
                                        <div>
                                            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300">Status</label>
                                            <span
                                                :class="[
                                                    'mt-1 inline-flex px-2 py-1 text-xs font-medium rounded-full',
                                                    combo.is_available
                                                        ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200'
                                                        : 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200'
                                                ]"
                                            >
                                                {{ combo.is_available ? 'Available' : 'Unavailable' }}
                                            </span>
                                        </div>
                                        
                                        <div>
                                            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300">Sort Order</label>
                                            <p class="mt-1 text-sm text-gray-900 dark:text-white">{{ combo.sort_order }}</p>
                                        </div>
                                        
                                        <div v-if="combo.description" class="md:col-span-2">
                                            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300">Description</label>
                                            <p class="mt-1 text-sm text-gray-900 dark:text-white">{{ combo.description }}</p>
                                        </div>
                                    </div>
                                </div>

                                <!-- Components -->
                                <div class="bg-gray-50 dark:bg-gray-700 rounded-lg p-6">
                                    <h2 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">
                                        Combo Components
                                    </h2>
                                    
                                    <div class="space-y-4">
                                        <div
                                            v-for="component in combo.components"
                                            :key="component.id"
                                            class="bg-white dark:bg-gray-800 rounded-lg p-4 border border-gray-200 dark:border-gray-600"
                                        >
                                            <div class="flex items-start justify-between">
                                                <div class="flex-1">
                                                    <div class="flex items-center space-x-3">
                                                        <h3 class="text-md font-medium text-gray-900 dark:text-white">
                                                            {{ component.menu_item.name }}
                                                        </h3>
                                                        <span
                                                            :class="[
                                                                'px-2 py-1 text-xs font-medium rounded-full',
                                                                getComponentTypeColor(component.component_type)
                                                            ]"
                                                        >
                                                            {{ component.component_type }}
                                                        </span>
                                                        <span
                                                            v-if="component.is_required"
                                                            class="px-2 py-1 text-xs font-medium rounded-full bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200"
                                                        >
                                                            Required
                                                        </span>
                                                    </div>
                                                    
                                                    <p v-if="component.menu_item.description" class="mt-1 text-sm text-gray-600 dark:text-gray-400">
                                                        {{ component.menu_item.description }}
                                                    </p>
                                                    
                                                    <div class="mt-2 flex items-center space-x-4">
                                                        <span class="text-sm font-medium text-gray-900 dark:text-white">
                                                            Individual Price: ${{ component.menu_item.price }}
                                                        </span>
                                                        <span v-if="component.menu_item.category" class="text-sm text-gray-500">
                                                            Category: {{ component.menu_item.category.name }}
                                                        </span>
                                                    </div>
                                                </div>
                                                
                                                <div v-if="component.menu_item.primary_media" class="ml-4">
                                                    <img
                                                        :src="component.menu_item.primary_media.url"
                                                        :alt="component.menu_item.name"
                                                        class="w-16 h-16 object-cover rounded-lg"
                                                    />
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Sidebar -->
                            <div class="space-y-6">
                                <!-- Combo Image -->
                                <div class="bg-gray-50 dark:bg-gray-700 rounded-lg p-6">
                                    <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">Combo Image</h3>
                                    <div class="aspect-square bg-gray-200 dark:bg-gray-600 rounded-lg flex items-center justify-center">
                                        <img
                                            v-if="combo.primary_image_url || combo.image_url"
                                            :src="combo.primary_image_url || combo.image_url"
                                            :alt="combo.name"
                                            class="w-full h-full object-cover rounded-lg"
                                        />
                                        <div v-else class="text-gray-400 dark:text-gray-500 text-center">
                                            <i class="fas fa-utensils text-4xl mb-2"></i>
                                            <p class="text-sm">No image</p>
                                        </div>
                                    </div>
                                </div>

                                <!-- Price Breakdown -->
                                <div class="bg-gray-50 dark:bg-gray-700 rounded-lg p-6">
                                    <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">Price Breakdown</h3>
                                    
                                    <div class="space-y-3">
                                        <div
                                            v-for="component in combo.components"
                                            :key="component.id"
                                            class="flex justify-between text-sm"
                                        >
                                            <span class="text-gray-600 dark:text-gray-400">{{ component.menu_item.name }}</span>
                                            <span class="text-gray-900 dark:text-white">${{ component.menu_item.price }}</span>
                                        </div>
                                        
                                        <hr class="border-gray-300 dark:border-gray-600">
                                        
                                        <div class="flex justify-between text-sm">
                                            <span class="text-gray-600 dark:text-gray-400">Individual Total</span>
                                            <span class="text-gray-900 dark:text-white">${{ individualTotal }}</span>
                                        </div>
                                        
                                        <div class="flex justify-between text-lg font-bold">
                                            <span class="text-gray-900 dark:text-white">Combo Price</span>
                                            <span class="text-indigo-600 dark:text-indigo-400">${{ combo.price }}</span>
                                        </div>
                                        
                                        <div v-if="savings > 0" class="flex justify-between text-sm">
                                            <span class="text-green-600 dark:text-green-400">You Save</span>
                                            <span class="text-green-600 dark:text-green-400 font-medium">${{ savings }}</span>
                                        </div>
                                    </div>
                                </div>

                                <!-- Actions -->
                                <div class="bg-gray-50 dark:bg-gray-700 rounded-lg p-6">
                                    <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">Actions</h3>
                                    
                                    <div class="space-y-3">
                                        <button
                                            @click="toggleAvailability"
                                            :class="[
                                                'w-full inline-flex items-center justify-center px-4 py-2 border border-transparent rounded-md font-semibold text-xs text-white uppercase tracking-widest focus:outline-none focus:ring-2 focus:ring-offset-2 dark:focus:ring-offset-gray-800 transition ease-in-out duration-150',
                                                combo.is_available
                                                    ? 'bg-red-600 hover:bg-red-700 focus:bg-red-700 active:bg-red-900 focus:ring-red-500'
                                                    : 'bg-green-600 hover:bg-green-700 focus:bg-green-700 active:bg-green-900 focus:ring-green-500'
                                            ]"
                                        >
                                            <i :class="combo.is_available ? 'fas fa-toggle-off' : 'fas fa-toggle-on'" class="mr-2"></i>
                                            {{ combo.is_available ? 'Disable' : 'Enable' }}
                                        </button>
                                        
                                        <button
                                            @click="deleteCombo"
                                            class="w-full inline-flex items-center justify-center px-4 py-2 bg-red-600 border border-transparent rounded-md font-semibold text-xs text-white uppercase tracking-widest hover:bg-red-700 focus:bg-red-700 active:bg-red-900 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2 dark:focus:ring-offset-gray-800 transition ease-in-out duration-150"
                                        >
                                            <i class="fas fa-trash mr-2"></i>
                                            Delete Combo
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </ManagerLayout>
</template>

<script setup>
import { Head, Link, router } from '@inertiajs/vue3'
import { computed } from 'vue'
import ManagerLayout from '@/Layouts/ManagerLayout.vue'

const props = defineProps({
    combo: Object,
})

const individualTotal = computed(() => {
    return props.combo.components.reduce((total, component) => {
        return total + parseFloat(component.menu_item.price)
    }, 0).toFixed(2)
})

const savings = computed(() => {
    const individual = parseFloat(individualTotal.value)
    const combo = parseFloat(props.combo.price)
    return individual > combo ? (individual - combo).toFixed(2) : 0
})

const getComponentTypeColor = (type) => {
    const colors = {
        main: 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200',
        side: 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200',
        drink: 'bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200',
        dessert: 'bg-pink-100 text-pink-800 dark:bg-pink-900 dark:text-pink-200',
    }
    return colors[type] || 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200'
}

const toggleAvailability = () => {
    router.post(route('combo-menus.toggle-availability', props.combo.id), {}, {
        preserveScroll: true,
    })
}

const deleteCombo = () => {
    if (confirm(`Are you sure you want to delete "${props.combo.name}"?`)) {
        router.delete(route('combo-menus.destroy', props.combo.id))
    }
}
</script>
