<template>
    <ManagerLayout>
        <template #header>
            <div class="flex justify-between items-center">
                <div>
                    <h2 class="font-semibold text-xl text-gray-800 dark:text-gray-200 leading-tight">
                        Add New Vendor
                    </h2>
                    <p class="text-sm text-gray-600 dark:text-gray-400 mt-1">
                        Create a new vendor for your restaurant
                    </p>
                </div>
                <Link
                    :href="route('vendors.index')"
                    class="bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded-md text-sm font-medium transition-colors duration-200"
                >
                    Back to Vendors
                </Link>
            </div>
        </template>

        <div class="py-6">
            <div class="max-w-4xl mx-auto sm:px-6 lg:px-8">
                <div class="bg-white dark:bg-gray-800 overflow-hidden shadow-sm sm:rounded-lg">
                    <form @submit.prevent="submit" class="p-6 space-y-6">
                        <!-- Basic Information -->
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <div>
                                <label for="name" class="block text-sm font-medium text-gray-700 dark:text-gray-300">
                                    Vendor Name *
                                </label>
                                <input
                                    type="text"
                                    id="name"
                                    v-model="form.name"
                                    required
                                    class="mt-1 block w-full border-gray-300 dark:border-gray-700 dark:bg-gray-900 dark:text-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500"
                                    placeholder="Enter vendor name"
                                />
                                <div v-if="errors.name" class="mt-1 text-sm text-red-600">{{ errors.name }}</div>
                            </div>

                            <div>
                                <label for="company_name" class="block text-sm font-medium text-gray-700 dark:text-gray-300">
                                    Company Name
                                </label>
                                <input
                                    type="text"
                                    id="company_name"
                                    v-model="form.company_name"
                                    class="mt-1 block w-full border-gray-300 dark:border-gray-700 dark:bg-gray-900 dark:text-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500"
                                    placeholder="Enter company name"
                                />
                                <div v-if="errors.company_name" class="mt-1 text-sm text-red-600">{{ errors.company_name }}</div>
                            </div>

                            <div>
                                <label for="vendor_type" class="block text-sm font-medium text-gray-700 dark:text-gray-300">
                                    Vendor Type *
                                </label>
                                <select
                                    id="vendor_type"
                                    v-model="form.vendor_type"
                                    required
                                    class="mt-1 block w-full border-gray-300 dark:border-gray-700 dark:bg-gray-900 dark:text-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500"
                                >
                                    <option value="">Select Type</option>
                                    <option v-for="type in vendorTypeOptions" :key="type.value" :value="type.value">
                                        {{ type.label }}
                                    </option>
                                </select>
                                <div v-if="errors.vendor_type" class="mt-1 text-sm text-red-600">{{ errors.vendor_type }}</div>
                            </div>

                            <div>
                                <label for="contact_person" class="block text-sm font-medium text-gray-700 dark:text-gray-300">
                                    Contact Person
                                </label>
                                <input
                                    type="text"
                                    id="contact_person"
                                    v-model="form.contact_person"
                                    class="mt-1 block w-full border-gray-300 dark:border-gray-700 dark:bg-gray-900 dark:text-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500"
                                    placeholder="Enter contact person name"
                                />
                                <div v-if="errors.contact_person" class="mt-1 text-sm text-red-600">{{ errors.contact_person }}</div>
                            </div>
                        </div>

                        <!-- Contact Information -->
                        <div class="border-t border-gray-200 dark:border-gray-700 pt-6">
                            <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100 mb-4">Contact Information</h3>
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                                <div>
                                    <label for="email" class="block text-sm font-medium text-gray-700 dark:text-gray-300">
                                        Email
                                    </label>
                                    <input
                                        type="email"
                                        id="email"
                                        v-model="form.email"
                                        class="mt-1 block w-full border-gray-300 dark:border-gray-700 dark:bg-gray-900 dark:text-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500"
                                        placeholder="<EMAIL>"
                                    />
                                    <div v-if="errors.email" class="mt-1 text-sm text-red-600">{{ errors.email }}</div>
                                </div>

                                <div>
                                    <label for="phone" class="block text-sm font-medium text-gray-700 dark:text-gray-300">
                                        Phone
                                    </label>
                                    <input
                                        type="tel"
                                        id="phone"
                                        v-model="form.phone"
                                        class="mt-1 block w-full border-gray-300 dark:border-gray-700 dark:bg-gray-900 dark:text-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500"
                                        placeholder="+****************"
                                    />
                                    <div v-if="errors.phone" class="mt-1 text-sm text-red-600">{{ errors.phone }}</div>
                                </div>

                                <div>
                                    <label for="mobile" class="block text-sm font-medium text-gray-700 dark:text-gray-300">
                                        Mobile
                                    </label>
                                    <input
                                        type="tel"
                                        id="mobile"
                                        v-model="form.mobile"
                                        class="mt-1 block w-full border-gray-300 dark:border-gray-700 dark:bg-gray-900 dark:text-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500"
                                        placeholder="+****************"
                                    />
                                    <div v-if="errors.mobile" class="mt-1 text-sm text-red-600">{{ errors.mobile }}</div>
                                </div>

                                <div>
                                    <label for="website" class="block text-sm font-medium text-gray-700 dark:text-gray-300">
                                        Website
                                    </label>
                                    <input
                                        type="url"
                                        id="website"
                                        v-model="form.website"
                                        class="mt-1 block w-full border-gray-300 dark:border-gray-700 dark:bg-gray-900 dark:text-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500"
                                        placeholder="https://vendor-website.com"
                                    />
                                    <div v-if="errors.website" class="mt-1 text-sm text-red-600">{{ errors.website }}</div>
                                </div>
                            </div>
                        </div>

                        <!-- Address Information -->
                        <div class="border-t border-gray-200 dark:border-gray-700 pt-6">
                            <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100 mb-4">Address Information</h3>
                            <div class="space-y-4">
                                <div>
                                    <label for="address" class="block text-sm font-medium text-gray-700 dark:text-gray-300">
                                        Address
                                    </label>
                                    <textarea
                                        id="address"
                                        v-model="form.address"
                                        rows="3"
                                        class="mt-1 block w-full border-gray-300 dark:border-gray-700 dark:bg-gray-900 dark:text-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500"
                                        placeholder="Enter full address"
                                    ></textarea>
                                    <div v-if="errors.address" class="mt-1 text-sm text-red-600">{{ errors.address }}</div>
                                </div>

                                <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
                                    <div>
                                        <label for="city" class="block text-sm font-medium text-gray-700 dark:text-gray-300">
                                            City
                                        </label>
                                        <input
                                            type="text"
                                            id="city"
                                            v-model="form.city"
                                            class="mt-1 block w-full border-gray-300 dark:border-gray-700 dark:bg-gray-900 dark:text-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500"
                                            placeholder="City"
                                        />
                                        <div v-if="errors.city" class="mt-1 text-sm text-red-600">{{ errors.city }}</div>
                                    </div>

                                    <div>
                                        <label for="state" class="block text-sm font-medium text-gray-700 dark:text-gray-300">
                                            State/Province
                                        </label>
                                        <input
                                            type="text"
                                            id="state"
                                            v-model="form.state"
                                            class="mt-1 block w-full border-gray-300 dark:border-gray-700 dark:bg-gray-900 dark:text-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500"
                                            placeholder="State"
                                        />
                                        <div v-if="errors.state" class="mt-1 text-sm text-red-600">{{ errors.state }}</div>
                                    </div>

                                    <div>
                                        <label for="postal_code" class="block text-sm font-medium text-gray-700 dark:text-gray-300">
                                            Postal Code
                                        </label>
                                        <input
                                            type="text"
                                            id="postal_code"
                                            v-model="form.postal_code"
                                            class="mt-1 block w-full border-gray-300 dark:border-gray-700 dark:bg-gray-900 dark:text-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500"
                                            placeholder="12345"
                                        />
                                        <div v-if="errors.postal_code" class="mt-1 text-sm text-red-600">{{ errors.postal_code }}</div>
                                    </div>

                                    <div>
                                        <label for="country" class="block text-sm font-medium text-gray-700 dark:text-gray-300">
                                            Country
                                        </label>
                                        <input
                                            type="text"
                                            id="country"
                                            v-model="form.country"
                                            class="mt-1 block w-full border-gray-300 dark:border-gray-700 dark:bg-gray-900 dark:text-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500"
                                            placeholder="Country"
                                        />
                                        <div v-if="errors.country" class="mt-1 text-sm text-red-600">{{ errors.country }}</div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Business Information -->
                        <div class="border-t border-gray-200 dark:border-gray-700 pt-6">
                            <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100 mb-4">Business Information</h3>
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                                <div>
                                    <label for="tax_id" class="block text-sm font-medium text-gray-700 dark:text-gray-300">
                                        Tax ID
                                    </label>
                                    <input
                                        type="text"
                                        id="tax_id"
                                        v-model="form.tax_id"
                                        class="mt-1 block w-full border-gray-300 dark:border-gray-700 dark:bg-gray-900 dark:text-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500"
                                        placeholder="Tax identification number"
                                    />
                                    <div v-if="errors.tax_id" class="mt-1 text-sm text-red-600">{{ errors.tax_id }}</div>
                                </div>

                                <div>
                                    <label for="registration_number" class="block text-sm font-medium text-gray-700 dark:text-gray-300">
                                        Registration Number
                                    </label>
                                    <input
                                        type="text"
                                        id="registration_number"
                                        v-model="form.registration_number"
                                        class="mt-1 block w-full border-gray-300 dark:border-gray-700 dark:bg-gray-900 dark:text-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500"
                                        placeholder="Business registration number"
                                    />
                                    <div v-if="errors.registration_number" class="mt-1 text-sm text-red-600">{{ errors.registration_number }}</div>
                                </div>

                                <div>
                                    <label for="payment_terms" class="block text-sm font-medium text-gray-700 dark:text-gray-300">
                                        Payment Terms
                                    </label>
                                    <input
                                        type="text"
                                        id="payment_terms"
                                        v-model="form.payment_terms"
                                        class="mt-1 block w-full border-gray-300 dark:border-gray-700 dark:bg-gray-900 dark:text-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500"
                                        placeholder="e.g., Net 30, COD"
                                    />
                                    <div v-if="errors.payment_terms" class="mt-1 text-sm text-red-600">{{ errors.payment_terms }}</div>
                                </div>

                                <div>
                                    <label for="credit_limit" class="block text-sm font-medium text-gray-700 dark:text-gray-300">
                                        Credit Limit
                                    </label>
                                    <input
                                        type="number"
                                        id="credit_limit"
                                        v-model.number="form.credit_limit"
                                        min="0"
                                        step="0.01"
                                        class="mt-1 block w-full border-gray-300 dark:border-gray-700 dark:bg-gray-900 dark:text-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500"
                                        placeholder="0.00"
                                    />
                                    <div v-if="errors.credit_limit" class="mt-1 text-sm text-red-600">{{ errors.credit_limit }}</div>
                                </div>

                                <div>
                                    <label for="discount_percentage" class="block text-sm font-medium text-gray-700 dark:text-gray-300">
                                        Discount Percentage
                                    </label>
                                    <input
                                        type="number"
                                        id="discount_percentage"
                                        v-model.number="form.discount_percentage"
                                        min="0"
                                        max="100"
                                        step="0.01"
                                        class="mt-1 block w-full border-gray-300 dark:border-gray-700 dark:bg-gray-900 dark:text-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500"
                                        placeholder="0.00"
                                    />
                                    <div v-if="errors.discount_percentage" class="mt-1 text-sm text-red-600">{{ errors.discount_percentage }}</div>
                                </div>
                            </div>
                        </div>

                        <!-- Additional Information -->
                        <div class="border-t border-gray-200 dark:border-gray-700 pt-6">
                            <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100 mb-4">Additional Information</h3>
                            <div class="space-y-4">
                                <div>
                                    <label for="notes" class="block text-sm font-medium text-gray-700 dark:text-gray-300">
                                        Notes
                                    </label>
                                    <textarea
                                        id="notes"
                                        v-model="form.notes"
                                        rows="4"
                                        class="mt-1 block w-full border-gray-300 dark:border-gray-700 dark:bg-gray-900 dark:text-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500"
                                        placeholder="Additional notes about this vendor"
                                    ></textarea>
                                    <div v-if="errors.notes" class="mt-1 text-sm text-red-600">{{ errors.notes }}</div>
                                </div>

                                <div class="flex items-center">
                                    <input
                                        type="checkbox"
                                        id="is_active"
                                        v-model="form.is_active"
                                        class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                                    />
                                    <label for="is_active" class="ml-2 block text-sm text-gray-900 dark:text-gray-300">
                                        Active vendor (can receive purchase orders)
                                    </label>
                                </div>
                            </div>
                        </div>

                        <!-- Submit Buttons -->
                        <div class="border-t border-gray-200 dark:border-gray-700 pt-6">
                            <div class="flex justify-end space-x-3">
                                <Link
                                    :href="route('vendors.index')"
                                    class="bg-gray-300 hover:bg-gray-400 text-gray-800 px-4 py-2 rounded-md text-sm font-medium transition-colors duration-200"
                                >
                                    Cancel
                                </Link>
                                <button
                                    type="submit"
                                    :disabled="processing"
                                    class="bg-blue-600 hover:bg-blue-700 disabled:bg-blue-400 text-white px-4 py-2 rounded-md text-sm font-medium transition-colors duration-200"
                                >
                                    {{ processing ? 'Creating...' : 'Create Vendor' }}
                                </button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </ManagerLayout>
</template>

<script setup>
import { ref, reactive } from 'vue'
import { Link, router } from '@inertiajs/vue3'
import ManagerLayout from '@/Layouts/ManagerLayout.vue'

const props = defineProps({
    vendorTypeOptions: Array,
    errors: Object,
})

const processing = ref(false)

const form = reactive({
    name: '',
    company_name: '',
    vendor_type: '',
    contact_person: '',
    email: '',
    phone: '',
    mobile: '',
    website: '',
    tax_id: '',
    registration_number: '',
    address: '',
    city: '',
    state: '',
    country: '',
    postal_code: '',
    payment_terms: '',
    credit_limit: null,
    discount_percentage: 0,
    notes: '',
    is_active: true,
})

const submit = () => {
    processing.value = true
    
    router.post(route('vendors.store'), form, {
        onFinish: () => {
            processing.value = false
        },
        onSuccess: () => {
            // Success handled by redirect
        },
        onError: (errors) => {
            console.error('Validation errors:', errors)
        }
    })
}
</script>
