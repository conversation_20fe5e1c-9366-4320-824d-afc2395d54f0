<script setup>
import { Head, Link, useForm } from '@inertiajs/vue3';
import ManagerLayout from '@/Layouts/ManagerLayout.vue';
import MediaPicker from '@/Components/MediaLibrary/MediaPicker.vue';
import MultipleImageUpload from '@/Components/MenuItems/MultipleImageUpload.vue';
import HierarchicalCategorySelect from '@/Components/MenuItems/HierarchicalCategorySelect.vue';
import { ref, computed } from 'vue';

const props = defineProps({
    menuItem: Object,
    restaurant: Object,
    categories: Array,
    addons: {
        type: Array,
        default: () => []
    },
    addonCategories: {
        type: Array,
        default: () => []
    },
    availableMenuItems: {
        type: Array,
        default: () => []
    },
});

// Form data
const form = useForm({
    name: props.menuItem.name || '',
    slug: props.menuItem.slug || '',
    description: props.menuItem.description || '',
    short_description: props.menuItem.short_description || '',
    price: props.menuItem.price || '',
    cost_price: props.menuItem.cost_price || '',
    category_id: props.menuItem.category_id || '', // Keep for backward compatibility
    category_ids: props.menuItem.categories ? props.menuItem.categories.map(cat => cat.id) : [], // New multi-category field
    media_id: props.menuItem.media_id || null,
    media_ids: props.menuItem.media_items ? props.menuItem.media_items.map(item => item.id) : [],
    ingredients: props.menuItem.ingredients || '',
    preparation_time: props.menuItem.preparation_time || '',
    calories: props.menuItem.calories || '',
    is_vegetarian: props.menuItem.is_vegetarian || false,
    is_vegan: props.menuItem.is_vegan || false,
    is_gluten_free: props.menuItem.is_gluten_free || false,
    is_spicy: props.menuItem.is_spicy || false,
    is_active: props.menuItem.is_active !== undefined ? props.menuItem.is_active : true,
    is_available: props.menuItem.is_available !== undefined ? props.menuItem.is_available : true,
    is_featured: props.menuItem.is_featured || false,
    sort_order: props.menuItem.sort_order || 0,
    // Variations and add-ons fields
    sizes: props.menuItem.sizes || [],
    addon_ids: props.menuItem.addons ? props.menuItem.addons.map(addon => addon.id) : [],
    // Combo fields
    is_combo: props.menuItem.is_combo || false,
    combo_components: (() => {
        if (!props.menuItem.combo_items) return [];

        // Group combo items by menu_item_id and calculate quantities
        const grouped = {};
        props.menuItem.combo_items.forEach(item => {
            const key = `${item.id}_${item.pivot.component_type}`;
            if (grouped[key]) {
                grouped[key].quantity += 1;
            } else {
                grouped[key] = {
                    menu_item_id: item.id,
                    component_type: item.pivot.component_type,
                    quantity: 1,
                    is_required: item.pivot.is_required,
                    sort_order: item.pivot.sort_order,
                };
            }
        });

        return Object.values(grouped).sort((a, b) => a.sort_order - b.sort_order);
    })(),
    _method: 'PUT',
});

// Reactive data
const selectedMedia = ref(props.menuItem.media || null);
const selectedImages = ref(props.menuItem.media_items || []);

// Advanced sections visibility
const showSizeVariations = ref(false);
const showAddons = ref(false);
const showComboSection = ref(false);

// Computed properties
const groupedAddons = computed(() => {
    const groups = {};
    if (props.addons && Array.isArray(props.addons)) {
        props.addons.forEach(addon => {
            const category = addon.category || 'general';
            if (!groups[category]) {
                groups[category] = [];
            }
            groups[category].push(addon);
        });
    }
    return groups;
});

const priceRange = computed(() => {
    const basePrice = parseFloat(form.price || 0);
    if (form.sizes.length === 0) {
        return { min: basePrice, max: basePrice };
    }

    const modifiers = form.sizes.map(size => parseFloat(size.price_modifier || 0));
    const minModifier = Math.min(...modifiers);
    const maxModifier = Math.max(...modifiers);

    return {
        min: basePrice + minModifier,
        max: basePrice + maxModifier
    };
});

const selectedAddonsPrice = computed(() => {
    if (!props.addons || !Array.isArray(props.addons)) {
        return 0;
    }
    return form.addon_ids.reduce((total, addonId) => {
        const addon = props.addons.find(a => a.id === addonId);
        return total + (addon ? parseFloat(addon.price) : 0);
    }, 0);
});

// Methods
const generateSlug = () => {
    if (form.name) {
        form.slug = form.name
            .toLowerCase()
            .replace(/[^a-z0-9]+/g, '-')
            .replace(/(^-|-$)/g, '');
    }
};

const handleMediaSelection = (media) => {
    selectedMedia.value = media;
    form.media_id = media ? media.id : null;
};

const handleMultipleImagesChange = (images) => {
    selectedImages.value = images;
    form.media_ids = images.map(img => img.id);

    // Set first image as primary media_id for backward compatibility
    if (images.length > 0) {
        form.media_id = images[0].id;
        selectedMedia.value = images[0];
    } else {
        form.media_id = null;
        selectedMedia.value = null;
    }
};

const clearMedia = () => {
    selectedMedia.value = null;
    form.media_id = null;
};

// Size variation methods
const addSizeVariation = () => {
    form.sizes.push({
        name: '',
        name_bn: '',
        price_modifier: 0,
        is_available: true,
        sort_order: form.sizes.length,
        description: '',
        description_bn: ''
    });
};

const removeSizeVariation = (index) => {
    form.sizes.splice(index, 1);
    // Update sort orders
    form.sizes.forEach((size, idx) => {
        size.sort_order = idx;
    });
};

// Addon methods
const toggleAddon = (addonId) => {
    const index = form.addon_ids.indexOf(addonId);
    if (index > -1) {
        form.addon_ids.splice(index, 1);
    } else {
        form.addon_ids.push(addonId);
    }
};

const isAddonSelected = (addonId) => {
    return form.addon_ids.includes(addonId);
};

// Category methods
const removeCategoryId = (categoryId) => {
    const index = form.category_ids.indexOf(categoryId);
    if (index > -1) {
        form.category_ids.splice(index, 1);
    }
};

// Combo methods
const addComboComponent = () => {
    form.combo_components.push({
        menu_item_id: '',
        component_type: '',
        quantity: 1,
        is_required: false,
        sort_order: form.combo_components.length,
    });
};

const removeComboComponent = (index) => {
    form.combo_components.splice(index, 1);
    // Update sort orders
    form.combo_components.forEach((component, idx) => {
        component.sort_order = idx;
    });
};

const getComponentPrice = (menuItemId) => {
    const item = props.availableMenuItems.find(item => item.id === menuItemId);
    return item ? parseFloat(item.price) : 0;
};

const calculateIndividualTotal = computed(() => {
    return form.combo_components.reduce((total, component) => {
        const itemPrice = getComponentPrice(component.menu_item_id);
        return total + (itemPrice * component.quantity);
    }, 0);
});

const calculateSavings = computed(() => {
    const individual = calculateIndividualTotal.value;
    const combo = parseFloat(form.price) || 0;
    return Math.max(0, individual - combo);
});

const submit = () => {
    // Validate combo components if combo is enabled
    if (form.is_combo && form.combo_components.length === 0) {
        alert('Combo items must have at least one component. Please add components or disable combo mode.');
        return;
    }

    // Validate each combo component
    if (form.is_combo) {
        for (let i = 0; i < form.combo_components.length; i++) {
            const component = form.combo_components[i];
            if (!component.menu_item_id || !component.component_type || !component.quantity) {
                alert(`Component ${i + 1} is incomplete. Please fill in all required fields.`);
                return;
            }
        }
    }

    // Set backward compatibility category_id if only one category is selected
    if (form.category_ids.length === 1) {
        form.category_id = form.category_ids[0];
    } else if (form.category_ids.length === 0) {
        form.category_id = '';
    }

    form.put(route('menu-items.update', props.menuItem.slug), {
        onSuccess: () => {
            // Success handled by redirect
        },
    });
};

const deleteMenuItem = () => {
    if (confirm('Are you sure you want to delete this menu item? This action cannot be undone.')) {
        form.delete(route('menu-items.destroy', props.menuItem.id));
    }
};
</script>

<template>
    <ManagerLayout title="Edit Menu Item">
        <template #header>
            <div class="flex justify-between items-center">
                <h2 class="font-semibold text-xl text-gray-800 dark:text-gray-200 leading-tight">
                    ✏️ Edit Menu Item: {{ menuItem.name }}
                </h2>

                <div class="flex space-x-2">
                    <Link
                        :href="route('menu-items.show', menuItem.slug)"
                        class="inline-flex items-center px-4 py-2 bg-blue-600 border border-transparent rounded-md font-semibold text-xs text-white uppercase tracking-widest hover:bg-blue-700 focus:bg-blue-700 active:bg-blue-900 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition ease-in-out duration-150"
                    >
                        👁️ View
                    </Link>
                    <Link
                        :href="route('menu-items.index')"
                        class="inline-flex items-center px-4 py-2 bg-gray-600 border border-transparent rounded-md font-semibold text-xs text-white uppercase tracking-widest hover:bg-gray-700 focus:bg-gray-700 active:bg-gray-900 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2 transition ease-in-out duration-150"
                    >
                        ← Back to Menu Items
                    </Link>
                </div>
            </div>
        </template>

        <div class="py-6">
            <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
                <form @submit.prevent="submit" class="space-y-6">
                    <!-- Basic Information -->
                    <div class="bg-white dark:bg-gray-800 shadow-sm rounded-lg">
                        <div class="p-6 border-b border-gray-200 dark:border-gray-700">
                            <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100">
                                Basic Information
                            </h3>
                        </div>
                        <div class="p-6 space-y-6">
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                                <!-- Name -->
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                                        Name *
                                    </label>
                                    <input
                                        v-model="form.name"
                                        @input="generateSlug"
                                        type="text"
                                        required
                                        class="w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
                                        :class="{ 'border-red-500': form.errors.name }"
                                    >
                                    <div v-if="form.errors.name" class="mt-1 text-sm text-red-600">
                                        {{ form.errors.name }}
                                    </div>
                                </div>

                                <!-- Slug -->
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                                        Slug *
                                    </label>
                                    <input
                                        v-model="form.slug"
                                        type="text"
                                        required
                                        class="w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
                                        :class="{ 'border-red-500': form.errors.slug }"
                                    >
                                    <div v-if="form.errors.slug" class="mt-1 text-sm text-red-600">
                                        {{ form.errors.slug }}
                                    </div>
                                </div>

                                <!-- Price -->
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                                        Price *
                                    </label>
                                    <input
                                        v-model="form.price"
                                        type="number"
                                        step="0.01"
                                        min="0"
                                        required
                                        class="w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
                                        :class="{ 'border-red-500': form.errors.price }"
                                    >
                                    <div v-if="form.errors.price" class="mt-1 text-sm text-red-600">
                                        {{ form.errors.price }}
                                    </div>
                                </div>

                                <!-- Cost Price -->
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                                        Cost Price
                                    </label>
                                    <input
                                        v-model="form.cost_price"
                                        type="number"
                                        step="0.01"
                                        min="0"
                                        class="w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
                                        :class="{ 'border-red-500': form.errors.cost_price }"
                                    >
                                    <div v-if="form.errors.cost_price" class="mt-1 text-sm text-red-600">
                                        {{ form.errors.cost_price }}
                                    </div>
                                </div>

                                <!-- Categories (Hierarchical Select) -->
                                <div class="md:col-span-2">
                                    <HierarchicalCategorySelect
                                        v-model="form.category_ids"
                                        :categories="categories"
                                        :multiple="true"
                                        :max-selections="5"
                                        :required="true"
                                        :error="form.errors.category_ids"
                                        placeholder="Select categories..."
                                    >
                                        <template #label>
                                            Categories
                                        </template>
                                    </HierarchicalCategorySelect>
                                </div>

                                <!-- Preparation Time -->
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                                        Preparation Time (minutes)
                                    </label>
                                    <input
                                        v-model="form.preparation_time"
                                        type="number"
                                        min="0"
                                        class="w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
                                        :class="{ 'border-red-500': form.errors.preparation_time }"
                                    >
                                    <div v-if="form.errors.preparation_time" class="mt-1 text-sm text-red-600">
                                        {{ form.errors.preparation_time }}
                                    </div>
                                </div>
                            </div>

                            <!-- Description -->
                            <div>
                                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                                    Description
                                </label>
                                <textarea
                                    v-model="form.description"
                                    rows="4"
                                    class="w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
                                    :class="{ 'border-red-500': form.errors.description }"
                                ></textarea>
                                <div v-if="form.errors.description" class="mt-1 text-sm text-red-600">
                                    {{ form.errors.description }}
                                </div>
                            </div>

                            <!-- Short Description -->
                            <div>
                                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                                    Short Description
                                </label>
                                <input
                                    v-model="form.short_description"
                                    type="text"
                                    maxlength="500"
                                    class="w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
                                    :class="{ 'border-red-500': form.errors.short_description }"
                                >
                                <div v-if="form.errors.short_description" class="mt-1 text-sm text-red-600">
                                    {{ form.errors.short_description }}
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Multiple Images Selection -->
                    <div class="bg-white dark:bg-gray-800 shadow-sm rounded-lg">
                        <div class="p-6 border-b border-gray-200 dark:border-gray-700">
                            <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100">
                                📁 Images (Up to 5)
                            </h3>
                            <p class="mt-1 text-sm text-gray-600 dark:text-gray-400">
                                Select up to 5 images for this menu item. The first image will be used as the primary image.
                            </p>
                        </div>
                        <div class="p-6">
                            <MultipleImageUpload
                                v-model="selectedImages"
                                :max-images="5"
                                :error="form.errors.media_ids"
                                @update:modelValue="handleMultipleImagesChange"
                            />
                            <div v-if="form.errors.media_ids" class="mt-2 text-sm text-red-600">
                                {{ form.errors.media_ids }}
                            </div>
                        </div>
                    </div>

                    <!-- Advanced Options: Variations and Add-ons -->
                    <div class="bg-white dark:bg-gray-800 shadow-sm rounded-lg">
                        <div class="p-6 border-b border-gray-200 dark:border-gray-700">
                            <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100">
                                ⚙️ Advanced Options
                            </h3>
                            <p class="mt-1 text-sm text-gray-600 dark:text-gray-400">
                                Configure size variations, add-ons, and combo menu options
                            </p>
                        </div>
                        <div class="p-6 space-y-6">
                            <!-- Size Variations Section -->
                            <div class="border border-gray-200 dark:border-gray-700 rounded-lg">
                                <button
                                    @click="showSizeVariations = !showSizeVariations"
                                    type="button"
                                    class="w-full px-4 py-3 text-left bg-gray-50 dark:bg-gray-700 hover:bg-gray-100 dark:hover:bg-gray-600 rounded-t-lg focus:outline-none focus:ring-2 focus:ring-indigo-500"
                                >
                                    <div class="flex items-center justify-between">
                                        <div class="flex items-center space-x-3">
                                            <span class="text-lg">📏</span>
                                            <div>
                                                <h4 class="font-medium text-gray-900 dark:text-gray-100">
                                                    Size Variations
                                                </h4>
                                                <p class="text-sm text-gray-600 dark:text-gray-400">
                                                    Add different sizes with price modifiers
                                                </p>
                                            </div>
                                        </div>
                                        <svg
                                            :class="{ 'rotate-180': showSizeVariations }"
                                            class="w-5 h-5 text-gray-500 transition-transform"
                                            fill="none"
                                            stroke="currentColor"
                                            viewBox="0 0 24 24"
                                        >
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" />
                                        </svg>
                                    </div>
                                </button>

                                <div v-show="showSizeVariations" class="p-4 border-t border-gray-200 dark:border-gray-700">
                                    <div class="space-y-4">
                                        <div v-for="(size, index) in form.sizes" :key="index" class="p-4 bg-gray-50 dark:bg-gray-700 rounded-lg">
                                            <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
                                                <div>
                                                    <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                                                        Size Name *
                                                    </label>
                                                    <input
                                                        v-model="size.name"
                                                        type="text"
                                                        required
                                                        class="w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
                                                        placeholder="e.g., Small, Medium, Large"
                                                    >
                                                </div>
                                                <div>
                                                    <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                                                        Size Name (Bengali)
                                                    </label>
                                                    <input
                                                        v-model="size.name_bn"
                                                        type="text"
                                                        class="w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
                                                        placeholder="e.g., ছোট, মাঝারি, বড়"
                                                    >
                                                </div>
                                                <div>
                                                    <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                                                        Price Modifier *
                                                    </label>
                                                    <input
                                                        v-model="size.price_modifier"
                                                        type="number"
                                                        step="0.01"
                                                        required
                                                        class="w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
                                                        placeholder="0.00"
                                                    >
                                                </div>
                                                <div class="flex items-end space-x-2">
                                                    <label class="flex items-center">
                                                        <input
                                                            v-model="size.is_available"
                                                            type="checkbox"
                                                            class="rounded border-gray-300 text-indigo-600 shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
                                                        >
                                                        <span class="ml-2 text-sm text-gray-700 dark:text-gray-300">Available</span>
                                                    </label>
                                                    <button
                                                        @click="removeSizeVariation(index)"
                                                        type="button"
                                                        class="px-3 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-red-500"
                                                    >
                                                        🗑️
                                                    </button>
                                                </div>
                                            </div>
                                        </div>

                                        <button
                                            @click="addSizeVariation"
                                            type="button"
                                            class="w-full px-4 py-2 border-2 border-dashed border-gray-300 dark:border-gray-600 rounded-lg text-gray-600 dark:text-gray-400 hover:border-indigo-500 hover:text-indigo-500 focus:outline-none focus:ring-2 focus:ring-indigo-500"
                                        >
                                            ➕ Add Size Variation
                                        </button>
                                    </div>
                                </div>
                            </div>

                            <!-- Add-ons Section -->
                            <div class="border border-gray-200 dark:border-gray-700 rounded-lg">
                                <button
                                    @click="showAddons = !showAddons"
                                    type="button"
                                    class="w-full px-4 py-3 text-left bg-gray-50 dark:bg-gray-700 hover:bg-gray-100 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-indigo-500"
                                    :class="{ 'rounded-lg': !showAddons, 'rounded-t-lg': showAddons }"
                                >
                                    <div class="flex items-center justify-between">
                                        <div class="flex items-center space-x-3">
                                            <span class="text-lg">🍟</span>
                                            <div>
                                                <h4 class="font-medium text-gray-900 dark:text-gray-100">
                                                    Add-ons
                                                </h4>
                                                <p class="text-sm text-gray-600 dark:text-gray-400">
                                                    Select available add-ons for this menu item
                                                </p>
                                            </div>
                                        </div>
                                        <svg
                                            :class="{ 'rotate-180': showAddons }"
                                            class="w-5 h-5 text-gray-500 transition-transform"
                                            fill="none"
                                            stroke="currentColor"
                                            viewBox="0 0 24 24"
                                        >
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" />
                                        </svg>
                                    </div>
                                </button>

                                <div v-show="showAddons" class="p-4 border-t border-gray-200 dark:border-gray-700">
                                    <div v-if="Object.keys(groupedAddons).length === 0" class="text-center py-8 text-gray-500">
                                        No add-ons available. Create add-ons first to assign them to menu items.
                                    </div>

                                    <div v-else class="space-y-6">
                                        <div v-for="(addons, category) in groupedAddons" :key="category">
                                            <h5 class="font-medium text-gray-900 dark:text-gray-100 mb-3 capitalize">
                                                {{ category }}
                                            </h5>
                                            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3">
                                                <label
                                                    v-for="addon in addons"
                                                    :key="addon.id"
                                                    class="flex items-center p-3 border border-gray-200 dark:border-gray-600 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 cursor-pointer"
                                                    :class="{ 'bg-indigo-50 dark:bg-indigo-900 border-indigo-200 dark:border-indigo-700': isAddonSelected(addon.id) }"
                                                >
                                                    <input
                                                        :checked="isAddonSelected(addon.id)"
                                                        @change="toggleAddon(addon.id)"
                                                        type="checkbox"
                                                        class="rounded border-gray-300 text-indigo-600 shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
                                                    >
                                                    <div class="ml-3 flex-1">
                                                        <div class="font-medium text-gray-900 dark:text-gray-100">
                                                            {{ addon.name }}
                                                        </div>
                                                        <div class="text-sm text-gray-600 dark:text-gray-400">
                                                            Price: ${{ addon.price }}
                                                        </div>
                                                    </div>
                                                </label>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Combo Menu Integration Section -->
                            <div class="border border-gray-200 dark:border-gray-700 rounded-lg">
                                <button
                                    @click="showComboSection = !showComboSection"
                                    type="button"
                                    class="w-full px-4 py-3 text-left bg-gray-50 dark:bg-gray-700 hover:bg-gray-100 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-indigo-500"
                                    :class="{ 'rounded-lg': !showComboSection, 'rounded-t-lg': showComboSection }"
                                >
                                    <div class="flex items-center justify-between">
                                        <div class="flex items-center space-x-3">
                                            <span class="text-lg">🍽️</span>
                                            <div>
                                                <h4 class="font-medium text-gray-900 dark:text-gray-100">
                                                    Combo Menu Integration
                                                </h4>
                                                <p class="text-sm text-gray-600 dark:text-gray-400">
                                                    Create combo items by combining multiple menu items
                                                </p>
                                            </div>
                                        </div>
                                        <svg
                                            :class="{ 'rotate-180': showComboSection }"
                                            class="w-5 h-5 text-gray-500 transition-transform"
                                            fill="none"
                                            stroke="currentColor"
                                            viewBox="0 0 24 24"
                                        >
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" />
                                        </svg>
                                    </div>
                                </button>

                                <div v-show="showComboSection" class="p-4 border-t border-gray-200 dark:border-gray-700">
                                    <div class="space-y-6">
                                        <!-- Enable Combo Toggle -->
                                        <div class="flex items-center justify-between p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
                                            <div>
                                                <label class="flex items-center cursor-pointer">
                                                    <input
                                                        v-model="form.is_combo"
                                                        type="checkbox"
                                                        class="rounded border-gray-300 text-indigo-600 shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
                                                    >
                                                    <span class="ml-3 text-sm font-medium text-gray-900 dark:text-gray-100">
                                                        Enable as Combo Item
                                                    </span>
                                                </label>
                                                <p class="mt-1 text-xs text-gray-600 dark:text-gray-400 ml-6">
                                                    Turn this menu item into a combo that includes multiple items
                                                </p>
                                            </div>
                                            <div v-if="form.is_combo" class="text-right">
                                                <div class="text-sm font-medium text-indigo-600 dark:text-indigo-400">
                                                    Combo Mode Active
                                                </div>
                                                <div class="text-xs text-gray-500">
                                                    {{ form.combo_components.length }} components
                                                </div>
                                            </div>
                                        </div>

                                        <!-- Combo Components Section -->
                                        <div v-if="form.is_combo" class="space-y-4">
                                            <div class="flex items-center justify-between">
                                                <div>
                                                    <h5 class="font-medium text-gray-900 dark:text-gray-100">
                                                        Combo Components
                                                    </h5>
                                                    <p class="text-sm text-gray-600 dark:text-gray-400">
                                                        Add menu items that will be included in this combo
                                                    </p>
                                                </div>
                                                <button
                                                    @click="addComboComponent"
                                                    type="button"
                                                    class="inline-flex items-center px-3 py-2 bg-indigo-600 border border-transparent rounded-md font-semibold text-xs text-white uppercase tracking-widest hover:bg-indigo-700 focus:bg-indigo-700 active:bg-indigo-900 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 transition ease-in-out duration-150"
                                                >
                                                    <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                                                    </svg>
                                                    Add Component
                                                </button>
                                            </div>

                                            <!-- Validation Message -->
                                            <div v-if="form.is_combo && form.combo_components.length === 0" class="p-4 bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-lg">
                                                <div class="flex">
                                                    <svg class="w-5 h-5 text-yellow-400" fill="currentColor" viewBox="0 0 20 20">
                                                        <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd" />
                                                    </svg>
                                                    <div class="ml-3">
                                                        <p class="text-sm text-yellow-800 dark:text-yellow-200">
                                                            <strong>Combo items require at least one component.</strong> Add menu items to create your combo.
                                                        </p>
                                                    </div>
                                                </div>
                                            </div>

                                            <!-- Components List -->
                                            <div v-if="form.combo_components.length > 0" class="space-y-4">
                                                <div
                                                    v-for="(component, index) in form.combo_components"
                                                    :key="index"
                                                    class="p-4 bg-gray-50 dark:bg-gray-700 rounded-lg border border-gray-200 dark:border-gray-600"
                                                >
                                                    <div class="flex items-start justify-between mb-4">
                                                        <h6 class="font-medium text-gray-900 dark:text-gray-100">
                                                            Component {{ index + 1 }}
                                                        </h6>
                                                        <button
                                                            @click="removeComboComponent(index)"
                                                            type="button"
                                                            class="text-red-600 hover:text-red-800 dark:text-red-400 dark:hover:text-red-300"
                                                        >
                                                            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                                                            </svg>
                                                        </button>
                                                    </div>

                                                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                                                        <!-- Menu Item Selection -->
                                                        <div class="lg:col-span-2">
                                                            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                                                                Menu Item *
                                                            </label>
                                                            <select
                                                                v-model="component.menu_item_id"
                                                                class="w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-800 dark:text-white shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
                                                                required
                                                            >
                                                                <option value="">Select menu item...</option>
                                                                <option
                                                                    v-for="item in availableMenuItems"
                                                                    :key="item.id"
                                                                    :value="item.id"
                                                                >
                                                                    {{ item.name }} - ${{ item.price }}
                                                                </option>
                                                            </select>
                                                            <div v-if="component.menu_item_id" class="mt-1 text-xs text-gray-500">
                                                                Price: ${{ getComponentPrice(component.menu_item_id) }} each
                                                            </div>
                                                        </div>

                                                        <!-- Component Type -->
                                                        <div>
                                                            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                                                                Type *
                                                            </label>
                                                            <select
                                                                v-model="component.component_type"
                                                                class="w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-800 dark:text-white shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
                                                                required
                                                            >
                                                                <option value="">Select type...</option>
                                                                <option value="main">Main Course</option>
                                                                <option value="side">Side Dish</option>
                                                                <option value="drink">Beverage</option>
                                                                <option value="dessert">Dessert</option>
                                                            </select>
                                                        </div>

                                                        <!-- Quantity -->
                                                        <div>
                                                            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                                                                Quantity *
                                                            </label>
                                                            <input
                                                                v-model="component.quantity"
                                                                type="number"
                                                                min="1"
                                                                max="10"
                                                                class="w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-800 dark:text-white shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
                                                                required
                                                            >
                                                        </div>
                                                    </div>

                                                    <!-- Additional Options -->
                                                    <div class="mt-4 flex items-center space-x-6">
                                                        <label class="flex items-center">
                                                            <input
                                                                v-model="component.is_required"
                                                                type="checkbox"
                                                                class="rounded border-gray-300 text-indigo-600 shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
                                                            >
                                                            <span class="ml-2 text-sm text-gray-700 dark:text-gray-300">Required component</span>
                                                        </label>

                                                        <div class="flex items-center space-x-2">
                                                            <label class="text-sm text-gray-700 dark:text-gray-300">Sort Order:</label>
                                                            <input
                                                                v-model="component.sort_order"
                                                                type="number"
                                                                min="0"
                                                                class="w-16 rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-800 dark:text-white shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
                                                            >
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>

                                            <!-- Pricing Information -->
                                            <div v-if="form.combo_components.length > 0" class="p-4 bg-green-50 dark:bg-green-900/20 rounded-lg border border-green-200 dark:border-green-800">
                                                <h6 class="font-medium text-green-900 dark:text-green-100 mb-2">
                                                    💰 Pricing Information
                                                </h6>
                                                <div class="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
                                                    <div>
                                                        <div class="text-gray-600 dark:text-gray-400">Individual Total:</div>
                                                        <div class="font-semibold text-gray-900 dark:text-gray-100">
                                                            ${{ calculateIndividualTotal.toFixed(2) }}
                                                        </div>
                                                    </div>
                                                    <div>
                                                        <div class="text-gray-600 dark:text-gray-400">Combo Price:</div>
                                                        <div class="font-semibold text-indigo-600 dark:text-indigo-400">
                                                            ${{ (parseFloat(form.price) || 0).toFixed(2) }}
                                                        </div>
                                                    </div>
                                                    <div>
                                                        <div class="text-gray-600 dark:text-gray-400">Customer Savings:</div>
                                                        <div class="font-semibold" :class="calculateSavings > 0 ? 'text-green-600 dark:text-green-400' : 'text-red-600 dark:text-red-400'">
                                                            ${{ calculateSavings.toFixed(2) }}
                                                            <span v-if="calculateSavings > 0" class="text-xs">({{ ((calculateSavings / calculateIndividualTotal) * 100).toFixed(1) }}% off)</span>
                                                        </div>
                                                    </div>
                                                </div>
                                                <div v-if="calculateSavings <= 0 && form.price" class="mt-2 text-xs text-amber-600 dark:text-amber-400">
                                                    💡 Consider lowering the combo price to provide customer savings
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Dietary Information & Status -->
                    <div class="bg-white dark:bg-gray-800 shadow-sm rounded-lg">
                        <div class="p-6 border-b border-gray-200 dark:border-gray-700">
                            <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100">
                                Dietary Information & Status
                            </h3>
                        </div>
                        <div class="p-6 space-y-6">
                            <!-- Dietary Flags -->
                            <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
                                <label class="flex items-center">
                                    <input
                                        v-model="form.is_vegetarian"
                                        type="checkbox"
                                        class="rounded border-gray-300 text-indigo-600 shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
                                    >
                                    <span class="ml-2 text-sm text-gray-700 dark:text-gray-300">🥬 Vegetarian</span>
                                </label>

                                <label class="flex items-center">
                                    <input
                                        v-model="form.is_vegan"
                                        type="checkbox"
                                        class="rounded border-gray-300 text-indigo-600 shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
                                    >
                                    <span class="ml-2 text-sm text-gray-700 dark:text-gray-300">🌱 Vegan</span>
                                </label>

                                <label class="flex items-center">
                                    <input
                                        v-model="form.is_gluten_free"
                                        type="checkbox"
                                        class="rounded border-gray-300 text-indigo-600 shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
                                    >
                                    <span class="ml-2 text-sm text-gray-700 dark:text-gray-300">🌾 Gluten Free</span>
                                </label>

                                <label class="flex items-center">
                                    <input
                                        v-model="form.is_spicy"
                                        type="checkbox"
                                        class="rounded border-gray-300 text-indigo-600 shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
                                    >
                                    <span class="ml-2 text-sm text-gray-700 dark:text-gray-300">🌶️ Spicy</span>
                                </label>
                            </div>

                            <!-- Status Flags -->
                            <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                                <label class="flex items-center">
                                    <input
                                        v-model="form.is_active"
                                        type="checkbox"
                                        class="rounded border-gray-300 text-indigo-600 shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
                                    >
                                    <span class="ml-2 text-sm text-gray-700 dark:text-gray-300">✅ Active</span>
                                </label>

                                <label class="flex items-center">
                                    <input
                                        v-model="form.is_available"
                                        type="checkbox"
                                        class="rounded border-gray-300 text-indigo-600 shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
                                    >
                                    <span class="ml-2 text-sm text-gray-700 dark:text-gray-300">🟢 Available</span>
                                </label>

                                <label class="flex items-center">
                                    <input
                                        v-model="form.is_featured"
                                        type="checkbox"
                                        class="rounded border-gray-300 text-indigo-600 shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
                                    >
                                    <span class="ml-2 text-sm text-gray-700 dark:text-gray-300">⭐ Featured</span>
                                </label>
                            </div>
                        </div>
                    </div>

                    <!-- Form Actions -->
                    <div class="flex justify-between">
                        <button
                            type="button"
                            @click="deleteMenuItem"
                            class="inline-flex items-center px-4 py-2 bg-red-600 border border-transparent rounded-md font-semibold text-xs text-white uppercase tracking-widest hover:bg-red-700 focus:bg-red-700 active:bg-red-900 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2 transition ease-in-out duration-150"
                        >
                            🗑️ Delete Menu Item
                        </button>

                        <div class="flex space-x-4">
                            <Link
                                :href="route('menu-items.index')"
                                class="inline-flex items-center px-4 py-2 bg-gray-300 border border-transparent rounded-md font-semibold text-xs text-gray-700 uppercase tracking-widest hover:bg-gray-400 focus:bg-gray-400 active:bg-gray-500 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2 transition ease-in-out duration-150"
                            >
                                Cancel
                            </Link>
                            <button
                                type="submit"
                                :disabled="form.processing"
                                class="inline-flex items-center px-4 py-2 bg-indigo-600 border border-transparent rounded-md font-semibold text-xs text-white uppercase tracking-widest hover:bg-indigo-700 focus:bg-indigo-700 active:bg-indigo-900 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 transition ease-in-out duration-150 disabled:opacity-50"
                            >
                                <span v-if="form.processing" class="mr-2">
                                    <svg class="animate-spin h-4 w-4" fill="none" viewBox="0 0 24 24">
                                        <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                                        <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                                    </svg>
                                </span>
                                {{ form.processing ? 'Updating...' : 'Update Menu Item' }}
                            </button>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </ManagerLayout>
</template>
