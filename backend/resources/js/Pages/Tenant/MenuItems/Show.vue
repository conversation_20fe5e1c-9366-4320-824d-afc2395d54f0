<script setup>
import { Head, <PERSON> } from '@inertiajs/vue3';
import ManagerLayout from '@/Layouts/ManagerLayout.vue';

const props = defineProps({
    menuItem: Object,
    restaurant: Object,
});

// Methods
const formatPrice = (price) => {
    return new Intl.NumberFormat('en-US', {
        style: 'currency',
        currency: 'USD'
    }).format(price);
};

const getStatusBadge = (item) => {
    if (!item.is_active) {
        return { class: 'bg-red-100 text-red-800', text: 'Inactive' };
    }
    if (!item.is_available) {
        return { class: 'bg-yellow-100 text-yellow-800', text: 'Unavailable' };
    }
    return { class: 'bg-green-100 text-green-800', text: 'Active' };
};
</script>

<template>
    <ManagerLayout title="Menu Item Details">
        <template #header>
            <div class="flex justify-between items-center">
                <h2 class="font-semibold text-xl text-gray-800 dark:text-gray-200 leading-tight">
                    👁️ {{ menuItem.name }}
                </h2>
                
                <div class="flex space-x-2">
                    <Link
                        :href="route('menu-items.edit', menuItem.slug)"
                        class="inline-flex items-center px-4 py-2 bg-indigo-600 border border-transparent rounded-md font-semibold text-xs text-white uppercase tracking-widest hover:bg-indigo-700 focus:bg-indigo-700 active:bg-indigo-900 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 transition ease-in-out duration-150"
                    >
                        ✏️ Edit
                    </Link>
                    <Link
                        :href="route('menu-items.index')"
                        class="inline-flex items-center px-4 py-2 bg-gray-600 border border-transparent rounded-md font-semibold text-xs text-white uppercase tracking-widest hover:bg-gray-700 focus:bg-gray-700 active:bg-gray-900 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2 transition ease-in-out duration-150"
                    >
                        ← Back to Menu Items
                    </Link>
                </div>
            </div>
        </template>

        <div class="py-6">
            <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
                <div class="bg-white dark:bg-gray-800 shadow-sm rounded-lg overflow-hidden">
                    <!-- Header with Image -->
                    <div class="md:flex">
                        <!-- Image -->
                        <div class="md:w-1/3">
                            <div class="h-64 md:h-full bg-gray-200 dark:bg-gray-700 flex items-center justify-center">
                                <img
                                    v-if="menuItem.image"
                                    :src="menuItem.image"
                                    :alt="menuItem.name"
                                    class="w-full h-full object-cover"
                                >
                                <div v-else class="text-6xl">🍽️</div>
                            </div>
                        </div>

                        <!-- Content -->
                        <div class="md:w-2/3 p-6">
                            <div class="flex justify-between items-start mb-4">
                                <div>
                                    <h1 class="text-2xl font-bold text-gray-900 dark:text-gray-100 mb-2">
                                        {{ menuItem.name }}
                                    </h1>
                                    <div class="flex items-center space-x-3 mb-3">
                                        <span
                                            :class="[
                                                'px-3 py-1 text-sm font-medium rounded-full',
                                                getStatusBadge(menuItem).class
                                            ]"
                                        >
                                            {{ getStatusBadge(menuItem).text }}
                                        </span>
                                        <span v-if="menuItem.is_featured" class="px-3 py-1 text-sm font-medium rounded-full bg-yellow-100 text-yellow-800">
                                            ⭐ Featured
                                        </span>
                                    </div>
                                </div>
                                <div class="text-right">
                                    <div class="text-3xl font-bold text-green-600 mb-1">
                                        {{ formatPrice(menuItem.price) }}
                                    </div>
                                    <div v-if="menuItem.cost_price" class="text-sm text-gray-500">
                                        Cost: {{ formatPrice(menuItem.cost_price) }}
                                    </div>
                                </div>
                            </div>

                            <!-- Category and Preparation Time -->
                            <div class="flex items-center space-x-6 mb-4 text-sm text-gray-600 dark:text-gray-400">
                                <div v-if="menuItem.category">
                                    <span class="font-medium">Category:</span> {{ menuItem.category.name }}
                                </div>
                                <div v-if="menuItem.preparation_time">
                                    <span class="font-medium">Prep Time:</span> {{ menuItem.preparation_time }} min
                                </div>
                                <div v-if="menuItem.calories">
                                    <span class="font-medium">Calories:</span> {{ menuItem.calories }}
                                </div>
                            </div>

                            <!-- Dietary Information -->
                            <div class="flex flex-wrap gap-2 mb-4">
                                <span v-if="menuItem.is_vegetarian" class="inline-flex items-center px-2 py-1 text-xs font-medium rounded-full bg-green-100 text-green-800">
                                    🥬 Vegetarian
                                </span>
                                <span v-if="menuItem.is_vegan" class="inline-flex items-center px-2 py-1 text-xs font-medium rounded-full bg-green-100 text-green-800">
                                    🌱 Vegan
                                </span>
                                <span v-if="menuItem.is_gluten_free" class="inline-flex items-center px-2 py-1 text-xs font-medium rounded-full bg-blue-100 text-blue-800">
                                    🌾 Gluten Free
                                </span>
                                <span v-if="menuItem.is_spicy" class="inline-flex items-center px-2 py-1 text-xs font-medium rounded-full bg-red-100 text-red-800">
                                    🌶️ Spicy
                                </span>
                            </div>

                            <!-- Description -->
                            <div v-if="menuItem.description" class="mb-4">
                                <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100 mb-2">Description</h3>
                                <p class="text-gray-600 dark:text-gray-400 leading-relaxed">
                                    {{ menuItem.description }}
                                </p>
                            </div>

                            <!-- Short Description -->
                            <div v-if="menuItem.short_description" class="mb-4">
                                <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100 mb-2">Short Description</h3>
                                <p class="text-gray-600 dark:text-gray-400">
                                    {{ menuItem.short_description }}
                                </p>
                            </div>
                        </div>
                    </div>

                    <!-- Additional Information -->
                    <div class="border-t border-gray-200 dark:border-gray-700 px-6 py-4">
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <!-- Ingredients -->
                            <div v-if="menuItem.ingredients">
                                <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100 mb-2">Ingredients</h3>
                                <p class="text-gray-600 dark:text-gray-400">
                                    {{ menuItem.ingredients }}
                                </p>
                            </div>

                            <!-- Additional Details -->
                            <div>
                                <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100 mb-2">Details</h3>
                                <dl class="space-y-2 text-sm">
                                    <div class="flex justify-between">
                                        <dt class="text-gray-600 dark:text-gray-400">Slug:</dt>
                                        <dd class="text-gray-900 dark:text-gray-100 font-mono">{{ menuItem.slug }}</dd>
                                    </div>
                                    <div class="flex justify-between">
                                        <dt class="text-gray-600 dark:text-gray-400">Sort Order:</dt>
                                        <dd class="text-gray-900 dark:text-gray-100">{{ menuItem.sort_order || 0 }}</dd>
                                    </div>
                                    <div class="flex justify-between">
                                        <dt class="text-gray-600 dark:text-gray-400">Created:</dt>
                                        <dd class="text-gray-900 dark:text-gray-100">
                                            {{ new Date(menuItem.created_at).toLocaleDateString() }}
                                        </dd>
                                    </div>
                                    <div class="flex justify-between">
                                        <dt class="text-gray-600 dark:text-gray-400">Last Updated:</dt>
                                        <dd class="text-gray-900 dark:text-gray-100">
                                            {{ new Date(menuItem.updated_at).toLocaleDateString() }}
                                        </dd>
                                    </div>
                                </dl>
                            </div>
                        </div>
                    </div>

                    <!-- Actions -->
                    <div class="border-t border-gray-200 dark:border-gray-700 px-6 py-4">
                        <div class="flex justify-between">
                            <div class="flex space-x-3">
                                <Link
                                    :href="route('menu-items.edit', menuItem.slug)"
                                    class="inline-flex items-center px-4 py-2 bg-indigo-600 border border-transparent rounded-md font-semibold text-xs text-white uppercase tracking-widest hover:bg-indigo-700 focus:bg-indigo-700 active:bg-indigo-900 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 transition ease-in-out duration-150"
                                >
                                    ✏️ Edit Menu Item
                                </Link>
                                <Link
                                    :href="route('menu-items.create')"
                                    class="inline-flex items-center px-4 py-2 bg-green-600 border border-transparent rounded-md font-semibold text-xs text-white uppercase tracking-widest hover:bg-green-700 focus:bg-green-700 active:bg-green-900 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2 transition ease-in-out duration-150"
                                >
                                    ➕ Add New Item
                                </Link>
                            </div>
                            <Link
                                :href="route('menu-items.index')"
                                class="inline-flex items-center px-4 py-2 bg-gray-300 border border-transparent rounded-md font-semibold text-xs text-gray-700 uppercase tracking-widest hover:bg-gray-400 focus:bg-gray-400 active:bg-gray-500 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2 transition ease-in-out duration-150"
                            >
                                ← Back to List
                            </Link>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </ManagerLayout>
</template>
