<script setup>
import { Head, Link, router } from '@inertiajs/vue3';
import ManagerLayout from '@/Layouts/ManagerLayout.vue';
import { ref, computed } from 'vue';

const props = defineProps({
    menuItems: Object,
    categories: Array,
    filters: Object,
});

// Reactive data
const searchQuery = ref(props.filters?.search || '');
const selectedCategory = ref(props.filters?.category_id || '');
const selectedStatus = ref(props.filters?.status || '');

// Computed properties
const hasFilters = computed(() => {
    return searchQuery.value || selectedCategory.value || selectedStatus.value;
});

// Methods
const search = () => {
    router.get(route('menu-items.index'), {
        search: searchQuery.value,
        category_id: selectedCategory.value,
        status: selectedStatus.value,
    }, {
        preserveState: true,
        replace: true,
    });
};

const clearFilters = () => {
    searchQuery.value = '';
    selectedCategory.value = '';
    selectedStatus.value = '';
    router.get(route('menu-items.index'));
};

const formatPrice = (price) => {
    return new Intl.NumberFormat('en-US', {
        style: 'currency',
        currency: 'USD'
    }).format(price);
};

const getStatusBadge = (item) => {
    if (!item.is_active) {
        return { class: 'bg-red-100 text-red-800', text: 'Inactive' };
    }
    if (!item.is_available) {
        return { class: 'bg-yellow-100 text-yellow-800', text: 'Unavailable' };
    }
    return { class: 'bg-green-100 text-green-800', text: 'Active' };
};

const deleteMenuItem = (item) => {
    if (confirm(`Are you sure you want to delete "${item.name}"? This action cannot be undone.`)) {
        router.delete(route('menu-items.destroy', item.slug), {
            onSuccess: () => {
                // Success handled by redirect
            },
        });
    }
};

const handleImageError = (event) => {
    if (event.target.dataset.errorHandled) return;
    event.target.dataset.errorHandled = 'true';

    // Use inline SVG placeholder
    const svgPlaceholder = 'data:image/svg+xml;base64,' + btoa(`
        <svg xmlns="http://www.w3.org/2000/svg" width="200" height="200" viewBox="0 0 200 200">
            <rect width="200" height="200" fill="#f3f4f6"/>
            <circle cx="80" cy="70" r="12" fill="#d1d5db"/>
            <polygon points="40,140 80,100 120,120 160,80 180,140" fill="#d1d5db"/>
            <text x="100" y="170" text-anchor="middle" fill="#9ca3af" font-size="16">Menu Item</text>
        </svg>
    `);

    event.target.src = svgPlaceholder;
};
</script>

<template>
    <ManagerLayout title="Menu Items">
        <template #header>
            <div class="flex justify-between items-center">
                <h2 class="font-semibold text-xl text-gray-800 dark:text-gray-200 leading-tight">
                    📋 Menu Items
                </h2>

                <Link
                    :href="route('menu-items.create')"
                    class="inline-flex items-center px-4 py-2 bg-indigo-600 border border-transparent rounded-md font-semibold text-xs text-white uppercase tracking-widest hover:bg-indigo-700 focus:bg-indigo-700 active:bg-indigo-900 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 transition ease-in-out duration-150"
                >
                    ➕ Add Menu Item
                </Link>
            </div>
        </template>

        <div class="py-6">
            <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                <!-- Filters -->
                <div class="bg-white dark:bg-gray-800 shadow-sm rounded-lg mb-6">
                    <div class="p-6">
                        <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
                            <!-- Search -->
                            <div>
                                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                                    Search
                                </label>
                                <input
                                    v-model="searchQuery"
                                    @keyup.enter="search"
                                    type="text"
                                    placeholder="Search menu items..."
                                    class="w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
                                >
                            </div>

                            <!-- Category Filter -->
                            <div>
                                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                                    Category
                                </label>
                                <select
                                    v-model="selectedCategory"
                                    @change="search"
                                    class="w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
                                >
                                    <option value="">All Categories</option>
                                    <option
                                        v-for="category in categories"
                                        :key="category.id"
                                        :value="category.id"
                                    >
                                        {{ category.name }}
                                    </option>
                                </select>
                            </div>

                            <!-- Status Filter -->
                            <div>
                                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                                    Status
                                </label>
                                <select
                                    v-model="selectedStatus"
                                    @change="search"
                                    class="w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
                                >
                                    <option value="">All Status</option>
                                    <option value="active">Active</option>
                                    <option value="inactive">Inactive</option>
                                    <option value="unavailable">Unavailable</option>
                                </select>
                            </div>

                            <!-- Actions -->
                            <div class="flex items-end space-x-2">
                                <button
                                    @click="search"
                                    class="px-4 py-2 bg-indigo-600 text-white rounded-md hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-indigo-500"
                                >
                                    🔍 Search
                                </button>
                                <button
                                    v-if="hasFilters"
                                    @click="clearFilters"
                                    class="px-4 py-2 bg-gray-300 text-gray-700 rounded-md hover:bg-gray-400 focus:outline-none focus:ring-2 focus:ring-gray-500"
                                >
                                    Clear
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Menu Items Grid -->
                <div class="bg-white dark:bg-gray-800 shadow-sm rounded-lg">
                    <div class="p-6">
                        <div v-if="menuItems.data.length === 0" class="text-center py-12">
                            <div class="text-4xl mb-4">🍽️</div>
                            <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100 mb-2">
                                No menu items found
                            </h3>
                            <p class="text-gray-600 dark:text-gray-400 mb-4">
                                {{ hasFilters ? 'Try adjusting your filters' : 'Get started by adding your first menu item' }}
                            </p>
                            <Link
                                v-if="!hasFilters"
                                :href="route('menu-items.create')"
                                class="inline-flex items-center px-4 py-2 bg-indigo-600 text-white rounded-md hover:bg-indigo-700"
                            >
                                ➕ Add Menu Item
                            </Link>
                        </div>

                        <div v-else class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                            <div
                                v-for="item in menuItems.data"
                                :key="item.id"
                                class="border border-gray-200 dark:border-gray-700 rounded-lg overflow-hidden hover:shadow-md transition-shadow"
                            >
                                <!-- Item Image -->
                                <div class="h-48 bg-gray-200 dark:bg-gray-700 flex items-center justify-center">
                                    <img
                                        v-if="item.primary_image_url"
                                        :src="item.primary_image_url"
                                        :alt="item.name"
                                        class="w-full h-full object-cover"
                                        @error="handleImageError"
                                    >
                                    <div v-else class="text-4xl">🍽️</div>
                                </div>

                                <!-- Item Details -->
                                <div class="p-4">
                                    <div class="flex justify-between items-start mb-2">
                                        <h3 class="text-lg font-semibold text-gray-900 dark:text-gray-100">
                                            {{ item.name }}
                                        </h3>
                                        <span
                                            :class="[
                                                'px-2 py-1 text-xs font-medium rounded-full',
                                                getStatusBadge(item).class
                                            ]"
                                        >
                                            {{ getStatusBadge(item).text }}
                                        </span>
                                    </div>

                                    <p class="text-gray-600 dark:text-gray-400 text-sm mb-3 line-clamp-2">
                                        {{ item.description || 'No description available' }}
                                    </p>

                                    <div class="flex justify-between items-center mb-3">
                                        <span class="text-lg font-bold text-green-600">
                                            {{ formatPrice(item.price) }}
                                        </span>
                                        <span class="text-sm text-gray-500">
                                            {{ item.category?.name || 'No category' }}
                                        </span>
                                    </div>

                                    <!-- Actions -->
                                    <div class="flex space-x-1">
                                        <Link
                                            :href="route('menu-items.show', item.slug)"
                                            class="flex-1 text-center px-2 py-2 bg-gray-100 text-gray-700 rounded-md hover:bg-gray-200 text-xs"
                                        >
                                            👁️ View
                                        </Link>
                                        <Link
                                            :href="route('menu-items.edit', item.slug)"
                                            class="flex-1 text-center px-2 py-2 bg-indigo-100 text-indigo-700 rounded-md hover:bg-indigo-200 text-xs"
                                        >
                                            ✏️ Edit
                                        </Link>
                                        <button
                                            @click="deleteMenuItem(item)"
                                            class="flex-1 px-2 py-2 bg-red-100 text-red-700 rounded-md hover:bg-red-200 text-xs"
                                        >
                                            🗑️ Delete
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Pagination -->
                        <div v-if="menuItems.links && menuItems.links.length > 3" class="mt-6">
                            <nav class="flex justify-center">
                                <div class="flex space-x-1">
                                    <Link
                                        v-for="link in menuItems.links"
                                        :key="link.label"
                                        :href="link.url"
                                        v-html="link.label"
                                        :class="[
                                            'px-3 py-2 text-sm rounded-md',
                                            link.active
                                                ? 'bg-indigo-600 text-white'
                                                : 'bg-white text-gray-700 hover:bg-gray-50 border border-gray-300'
                                        ]"
                                    />
                                </div>
                            </nav>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </ManagerLayout>
</template>
