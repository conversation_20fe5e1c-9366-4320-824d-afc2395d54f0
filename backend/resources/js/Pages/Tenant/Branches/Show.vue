<template>
    <ManagerLayout title="Branch Details">
        <template #header>
            <div class="flex justify-between items-center">
                <h2 class="font-semibold text-xl text-gray-800 dark:text-gray-200 leading-tight">
                    🏢 {{ branch?.name || 'Branch Details' }}
                    <span v-if="branch?.is_main_branch" class="ml-2 inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                        Main Branch
                    </span>
                </h2>
                <div class="flex space-x-3">
                    <Link
                        :href="route('branches.edit', branch?.id)"
                        class="inline-flex items-center px-4 py-2 bg-indigo-600 border border-transparent rounded-md font-semibold text-xs text-white uppercase tracking-widest hover:bg-indigo-700 transition ease-in-out duration-150"
                    >
                        ✏️ Edit
                    </Link>
                    <Link
                        :href="route('branches.index')"
                        class="inline-flex items-center px-4 py-2 bg-gray-600 border border-transparent rounded-md font-semibold text-xs text-white uppercase tracking-widest hover:bg-gray-700 transition ease-in-out duration-150"
                    >
                        ← Back to Branches
                    </Link>
                </div>
            </div>
        </template>

        <div class="py-12">
            <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
                <!-- Branch Information -->
                <div class="bg-white dark:bg-gray-800 overflow-hidden shadow-xl sm:rounded-lg mb-6">
                    <div class="p-6">
                        <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
                            <!-- Basic Information -->
                            <div>
                                <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100 mb-4">Basic Information</h3>
                                <dl class="space-y-3">
                                    <div>
                                        <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">Branch Name</dt>
                                        <dd class="text-sm text-gray-900 dark:text-gray-100">{{ branch?.name || 'N/A' }}</dd>
                                    </div>
                                    <div v-if="branch?.code">
                                        <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">Branch Code</dt>
                                        <dd class="text-sm text-gray-900 dark:text-gray-100">{{ branch.code }}</dd>
                                    </div>
                                    <div v-if="branch?.description">
                                        <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">Description</dt>
                                        <dd class="text-sm text-gray-900 dark:text-gray-100">{{ branch.description }}</dd>
                                    </div>
                                    <div>
                                        <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">Status</dt>
                                        <dd class="text-sm">
                                            <span :class="[
                                                'inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium',
                                                branch?.is_active 
                                                    ? 'bg-green-100 text-green-800' 
                                                    : 'bg-red-100 text-red-800'
                                            ]">
                                                {{ branch?.is_active ? 'Active' : 'Inactive' }}
                                            </span>
                                        </dd>
                                    </div>
                                    <div v-if="branch?.sort_order !== undefined">
                                        <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">Sort Order</dt>
                                        <dd class="text-sm text-gray-900 dark:text-gray-100">{{ branch.sort_order }}</dd>
                                    </div>
                                    <div v-if="branch?.opening_time || branch?.closing_time">
                                        <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">Operating Hours</dt>
                                        <dd class="text-sm text-gray-900 dark:text-gray-100">
                                            {{ branch?.opening_time || 'N/A' }} - {{ branch?.closing_time || 'N/A' }}
                                        </dd>
                                    </div>
                                </dl>
                            </div>

                            <!-- Contact Information -->
                            <div>
                                <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100 mb-4">Contact Information</h3>
                                <dl class="space-y-3">
                                    <div>
                                        <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">Address</dt>
                                        <dd class="text-sm text-gray-900 dark:text-gray-100">
                                            <div>{{ branch?.address || 'N/A' }}</div>
                                            <div>
                                                {{ branch?.city || '' }}{{ branch?.state ? ', ' + branch.state : '' }}
                                                {{ branch?.postal_code || '' }}
                                            </div>
                                            <div v-if="branch?.country">{{ branch.country }}</div>
                                        </dd>
                                    </div>
                                    <div v-if="branch?.phone">
                                        <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">Phone</dt>
                                        <dd class="text-sm text-gray-900 dark:text-gray-100">
                                            <a :href="'tel:' + branch.phone" class="text-indigo-600 hover:text-indigo-500">
                                                {{ branch.phone }}
                                            </a>
                                        </dd>
                                    </div>
                                    <div v-if="branch?.email">
                                        <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">Email</dt>
                                        <dd class="text-sm text-gray-900 dark:text-gray-100">
                                            <a :href="'mailto:' + branch.email" class="text-indigo-600 hover:text-indigo-500">
                                                {{ branch.email }}
                                            </a>
                                        </dd>
                                    </div>
                                </dl>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Management Information -->
                <div class="bg-white dark:bg-gray-800 overflow-hidden shadow-xl sm:rounded-lg mb-6">
                    <div class="p-6">
                        <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100 mb-4">Management</h3>
                        <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
                            <!-- Manager Information -->
                            <div>
                                <h4 class="text-md font-medium text-gray-700 dark:text-gray-300 mb-3">Branch Manager</h4>
                                <div v-if="branch?.manager" class="flex items-center space-x-3">
                                    <div class="flex-shrink-0">
                                        <div class="h-10 w-10 rounded-full bg-indigo-100 dark:bg-indigo-900 flex items-center justify-center">
                                            <span class="text-sm font-medium text-indigo-600 dark:text-indigo-400">
                                                {{ getInitials(branch.manager.name) }}
                                            </span>
                                        </div>
                                    </div>
                                    <div>
                                        <p class="text-sm font-medium text-gray-900 dark:text-gray-100">
                                            {{ branch.manager.name }}
                                        </p>
                                        <p v-if="branch.manager.email" class="text-sm text-gray-500 dark:text-gray-400">
                                            {{ branch.manager.email }}
                                        </p>
                                    </div>
                                </div>
                                <div v-else class="text-sm text-gray-500 dark:text-gray-400">
                                    No manager assigned
                                </div>
                            </div>

                            <!-- Employee Count -->
                            <div>
                                <h4 class="text-md font-medium text-gray-700 dark:text-gray-300 mb-3">Staff</h4>
                                <div class="text-2xl font-bold text-gray-900 dark:text-gray-100">
                                    {{ branch?.employees_count || 0 }}
                                </div>
                                <div class="text-sm text-gray-500 dark:text-gray-400">
                                    Total employees
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Recent Activity -->
                <div v-if="branch?.shifts && branch.shifts.length > 0" class="bg-white dark:bg-gray-800 overflow-hidden shadow-xl sm:rounded-lg">
                    <div class="p-6">
                        <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100 mb-4">Recent Shifts</h3>
                        <div class="overflow-x-auto">
                            <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                                <thead class="bg-gray-50 dark:bg-gray-700">
                                    <tr>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                                            Employee
                                        </th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                                            Date
                                        </th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                                            Hours
                                        </th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                                            Status
                                        </th>
                                    </tr>
                                </thead>
                                <tbody class="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                                    <tr v-for="shift in branch.shifts" :key="shift.id" class="hover:bg-gray-50 dark:hover:bg-gray-700">
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <div class="text-sm font-medium text-gray-900 dark:text-gray-100">
                                                {{ shift.employee?.name || 'Unknown' }}
                                            </div>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <div class="text-sm text-gray-900 dark:text-gray-100">
                                                {{ formatDate(shift.shift_date) }}
                                            </div>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <div class="text-sm text-gray-900 dark:text-gray-100">
                                                {{ shift.start_time }} - {{ shift.end_time }}
                                            </div>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <span :class="[
                                                'inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium',
                                                getShiftStatusClass(shift.status)
                                            ]">
                                                {{ shift.status || 'Scheduled' }}
                                            </span>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>

                <!-- Empty State for No Recent Activity -->
                <div v-else class="bg-white dark:bg-gray-800 overflow-hidden shadow-xl sm:rounded-lg">
                    <div class="p-6 text-center">
                        <div class="text-gray-500 dark:text-gray-400">
                            <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                            </svg>
                            <h3 class="mt-2 text-sm font-medium text-gray-900 dark:text-gray-100">No recent activity</h3>
                            <p class="mt-1 text-sm text-gray-500 dark:text-gray-400">
                                No recent shifts or activities for this branch.
                            </p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </ManagerLayout>
</template>

<script setup>
import { Link } from '@inertiajs/vue3'
import ManagerLayout from '@/Layouts/ManagerLayout.vue'

// Props
const props = defineProps({
    branch: {
        type: Object,
        required: true
    }
})

// Helper methods
const getInitials = (name) => {
    if (!name) return '??'
    return name.split(' ').map(n => n[0]).join('').toUpperCase().slice(0, 2)
}

const formatDate = (date) => {
    if (!date) return 'N/A'
    try {
        return new Date(date).toLocaleDateString()
    } catch (e) {
        return 'Invalid Date'
    }
}

const getShiftStatusClass = (status) => {
    switch (status?.toLowerCase()) {
        case 'completed':
            return 'bg-green-100 text-green-800'
        case 'in_progress':
            return 'bg-blue-100 text-blue-800'
        case 'cancelled':
            return 'bg-red-100 text-red-800'
        default:
            return 'bg-gray-100 text-gray-800'
    }
}
</script>
