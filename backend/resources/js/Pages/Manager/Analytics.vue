<script setup>
import { Head } from '@inertiajs/vue3';
import ManagerLayout from '@/Layouts/ManagerLayout.vue';
import { ref, computed } from 'vue';

const props = defineProps({
    analytics: Object,
    period: String,
});

// Reactive data
const selectedPeriod = ref(props.period || '30');
const isLoading = ref(false);

// Computed properties
const salesOverview = computed(() => props.analytics?.sales_overview || {});
const menuPerformance = computed(() => props.analytics?.menu_performance || {});
const customerAnalytics = computed(() => props.analytics?.customer_analytics || {});
const operationalMetrics = computed(() => props.analytics?.operational_metrics || {});

// Methods
const updatePeriod = (period) => {
    selectedPeriod.value = period;
    // You can add logic here to refresh data
};

const formatCurrency = (amount) => {
    return new Intl.NumberFormat('en-US', {
        style: 'currency',
        currency: 'USD'
    }).format(amount || 0);
};

const formatNumber = (number) => {
    return new Intl.NumberFormat('en-US').format(number || 0);
};
</script>

<template>
    <ManagerLayout title="Analytics">
        <template #header>
            <div class="flex justify-between items-center">
                <h2 class="font-semibold text-xl text-gray-800 dark:text-gray-200 leading-tight">
                    📈 Restaurant Analytics
                </h2>
                
                <!-- Period Selector -->
                <div class="flex space-x-2">
                    <button
                        v-for="period in ['7', '30', '90', '365']"
                        :key="period"
                        @click="updatePeriod(period)"
                        :class="[
                            'px-4 py-2 rounded-md text-sm font-medium transition-colors',
                            selectedPeriod === period
                                ? 'bg-indigo-600 text-white'
                                : 'bg-white text-gray-700 border border-gray-300 hover:bg-gray-50'
                        ]"
                    >
                        {{ period === '7' ? '7 Days' : period === '30' ? '30 Days' : period === '90' ? '3 Months' : '1 Year' }}
                    </button>
                </div>
            </div>
        </template>

        <div class="py-6">
            <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                <!-- Loading State -->
                <div v-if="isLoading" class="text-center py-12">
                    <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-indigo-600"></div>
                    <p class="mt-2 text-gray-600">Loading analytics...</p>
                </div>

                <!-- Analytics Content -->
                <div v-else class="space-y-6">
                    <!-- Sales Overview -->
                    <div class="bg-white dark:bg-gray-800 overflow-hidden shadow-sm rounded-lg">
                        <div class="p-6 border-b border-gray-200 dark:border-gray-700">
                            <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100">
                                💰 Sales Overview
                            </h3>
                        </div>
                        <div class="p-6">
                            <div class="grid grid-cols-1 md:grid-cols-4 gap-6">
                                <div class="text-center">
                                    <div class="text-3xl font-bold text-green-600">{{ formatCurrency(salesOverview.total_revenue) }}</div>
                                    <div class="text-sm text-gray-600">Total Revenue</div>
                                </div>
                                <div class="text-center">
                                    <div class="text-3xl font-bold text-blue-600">{{ formatNumber(salesOverview.total_orders) }}</div>
                                    <div class="text-sm text-gray-600">Total Orders</div>
                                </div>
                                <div class="text-center">
                                    <div class="text-3xl font-bold text-purple-600">{{ formatCurrency(salesOverview.avg_order_value) }}</div>
                                    <div class="text-sm text-gray-600">Avg Order Value</div>
                                </div>
                                <div class="text-center">
                                    <div class="text-3xl font-bold text-orange-600">{{ formatNumber(salesOverview.growth_rate) }}%</div>
                                    <div class="text-sm text-gray-600">Growth Rate</div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Menu Performance -->
                    <div class="bg-white dark:bg-gray-800 overflow-hidden shadow-sm rounded-lg">
                        <div class="p-6 border-b border-gray-200 dark:border-gray-700">
                            <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100">
                                📋 Menu Performance
                            </h3>
                        </div>
                        <div class="p-6">
                            <div class="text-center py-8 text-gray-500">
                                <div class="text-4xl mb-4">📊</div>
                                <p>Menu performance analytics will be displayed here</p>
                                <p class="text-sm mt-2">Top selling items, category performance, and trends</p>
                            </div>
                        </div>
                    </div>

                    <!-- Customer Analytics -->
                    <div class="bg-white dark:bg-gray-800 overflow-hidden shadow-sm rounded-lg">
                        <div class="p-6 border-b border-gray-200 dark:border-gray-700">
                            <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100">
                                👥 Customer Analytics
                            </h3>
                        </div>
                        <div class="p-6">
                            <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                                <div class="text-center">
                                    <div class="text-2xl font-bold text-indigo-600">{{ formatNumber(customerAnalytics.new_customers) }}</div>
                                    <div class="text-sm text-gray-600">New Customers</div>
                                </div>
                                <div class="text-center">
                                    <div class="text-2xl font-bold text-green-600">{{ formatNumber(customerAnalytics.returning_customers) }}</div>
                                    <div class="text-sm text-gray-600">Returning Customers</div>
                                </div>
                                <div class="text-center">
                                    <div class="text-2xl font-bold text-purple-600">{{ customerAnalytics.retention_rate }}%</div>
                                    <div class="text-sm text-gray-600">Retention Rate</div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Operational Metrics -->
                    <div class="bg-white dark:bg-gray-800 overflow-hidden shadow-sm rounded-lg">
                        <div class="p-6 border-b border-gray-200 dark:border-gray-700">
                            <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100">
                                ⚙️ Operational Metrics
                            </h3>
                        </div>
                        <div class="p-6">
                            <div class="text-center py-8 text-gray-500">
                                <div class="text-4xl mb-4">📈</div>
                                <p>Operational metrics will be displayed here</p>
                                <p class="text-sm mt-2">Average preparation time, table turnover, staff efficiency</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </ManagerLayout>
</template>
