<script setup>
import { ref, onMounted, computed } from 'vue';
import { Head, Link, router } from '@inertiajs/vue3';
import ManagerLayout from '@/Layouts/ManagerLayout.vue';
import Modal from '@/Components/Modal.vue';
import PrimaryButton from '@/Components/PrimaryButton.vue';
import SecondaryButton from '@/Components/SecondaryButton.vue';
import DangerButton from '@/Components/DangerButton.vue';
import TextInput from '@/Components/TextInput.vue';
import InputLabel from '@/Components/InputLabel.vue';
import InputError from '@/Components/InputError.vue';

const props = defineProps({
    employees: Array,
    departments: Array,
    roles: Array,
    permissions: Array,
});

const t = ref(() => '');
const showCreateModal = ref(false);
const showEditModal = ref(false);
const showDeleteModal = ref(false);
const selectedEmployee = ref(null);
const searchQuery = ref('');
const selectedDepartment = ref('');
const selectedRole = ref('');

const form = ref({
    name: '',
    email: '',
    phone: '',
    position: '',
    department_id: '',
    role: '',
    salary: '',
    hourly_rate: '',
    hire_date: '',
    address: '',
    emergency_contact_name: '',
    emergency_contact_phone: '',
    notes: '',
    permissions: [],
});

const errors = ref({});

onMounted(() => {
    setTimeout(() => {
        t.value = window.t || ((key, fallback) => fallback || key);
    }, 100);
});

// Computed properties
const filteredEmployees = computed(() => {
    let filtered = props.employees || [];
    
    if (searchQuery.value) {
        filtered = filtered.filter(emp => 
            emp.name.toLowerCase().includes(searchQuery.value.toLowerCase()) ||
            emp.email.toLowerCase().includes(searchQuery.value.toLowerCase()) ||
            emp.position.toLowerCase().includes(searchQuery.value.toLowerCase())
        );
    }
    
    if (selectedDepartment.value) {
        filtered = filtered.filter(emp => emp.department_id == selectedDepartment.value);
    }
    
    if (selectedRole.value) {
        filtered = filtered.filter(emp => emp.roles?.some(role => role.name === selectedRole.value));
    }
    
    return filtered;
});

const activeEmployees = computed(() => {
    return props.employees?.filter(emp => emp.is_active).length || 0;
});

const onShiftEmployees = computed(() => {
    return props.employees?.filter(emp => emp.is_on_shift).length || 0;
});

// Methods
const openCreateModal = () => {
    resetForm();
    showCreateModal.value = true;
};

const openEditModal = (employee) => {
    selectedEmployee.value = employee;
    form.value = {
        name: employee.name,
        email: employee.email,
        phone: employee.phone || '',
        position: employee.position || '',
        department_id: employee.department_id || '',
        role: employee.roles?.[0]?.name || '',
        salary: employee.salary || '',
        hourly_rate: employee.hourly_rate || '',
        hire_date: employee.hire_date || '',
        address: employee.address || '',
        emergency_contact_name: employee.emergency_contact_name || '',
        emergency_contact_phone: employee.emergency_contact_phone || '',
        notes: employee.notes || '',
        permissions: employee.permissions?.map(p => p.name) || [],
    };
    showEditModal.value = true;
};

const openDeleteModal = (employee) => {
    selectedEmployee.value = employee;
    showDeleteModal.value = true;
};

const resetForm = () => {
    form.value = {
        name: '',
        email: '',
        phone: '',
        position: '',
        department_id: '',
        role: '',
        salary: '',
        hourly_rate: '',
        hire_date: '',
        address: '',
        emergency_contact_name: '',
        emergency_contact_phone: '',
        notes: '',
        permissions: [],
    };
    errors.value = {};
};

const createEmployee = () => {
    router.post(route('employees.store'), form.value, {
        onSuccess: () => {
            showCreateModal.value = false;
            resetForm();
        },
        onError: (errs) => {
            errors.value = errs;
        }
    });
};

const updateEmployee = () => {
    router.put(route('employees.update', selectedEmployee.value.id), form.value, {
        onSuccess: () => {
            showEditModal.value = false;
            resetForm();
        },
        onError: (errs) => {
            errors.value = errs;
        }
    });
};

const deleteEmployee = () => {
    router.delete(route('employees.destroy', selectedEmployee.value.id), {
        onSuccess: () => {
            showDeleteModal.value = false;
            selectedEmployee.value = null;
        }
    });
};

const toggleEmployeeStatus = (employee) => {
    router.post(route('employees.toggle-status', employee.id));
};

const getStatusColor = (employee) => {
    if (!employee.is_active) return 'bg-gray-100 text-gray-800';
    if (employee.is_on_shift) return 'bg-green-100 text-green-800';
    return 'bg-yellow-100 text-yellow-800';
};

const getStatusText = (employee) => {
    if (!employee.is_active) return t('staff_management.inactive', 'Inactive');
    if (employee.is_on_shift) return t('staff_management.on_shift', 'On Shift');
    return t('staff_management.off_shift', 'Off Shift');
};

const formatCurrency = (amount) => {
    return new Intl.NumberFormat('en-US', {
        style: 'currency',
        currency: 'USD'
    }).format(amount);
};
</script>

<template>
    <Head :title="t('staff_management.staff', 'Team Management')" />

    <ManagerLayout>
        <template #header>
            <div class="flex justify-between items-center">
                <h2 class="font-semibold text-xl text-gray-800 dark:text-gray-200 leading-tight">
                    {{ t('staff_management.staff', 'Team Management') }}
                </h2>
                <PrimaryButton @click="openCreateModal">
                    {{ t('actions.add', 'Add Employee') }}
                </PrimaryButton>
            </div>
        </template>

        <div class="space-y-6">
            <!-- Stats Cards -->
            <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
                <div class="bg-white dark:bg-gray-800 overflow-hidden shadow rounded-lg">
                    <div class="p-5">
                        <div class="flex items-center">
                            <div class="flex-shrink-0">
                                <span class="text-2xl">👥</span>
                            </div>
                            <div class="ml-5 w-0 flex-1">
                                <dl>
                                    <dt class="text-sm font-medium text-gray-500 dark:text-gray-400 truncate">
                                        {{ t('staff_management.total_employees', 'Total Employees') }}
                                    </dt>
                                    <dd class="text-lg font-medium text-gray-900 dark:text-gray-100">
                                        {{ employees?.length || 0 }}
                                    </dd>
                                </dl>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="bg-white dark:bg-gray-800 overflow-hidden shadow rounded-lg">
                    <div class="p-5">
                        <div class="flex items-center">
                            <div class="flex-shrink-0">
                                <span class="text-2xl">✅</span>
                            </div>
                            <div class="ml-5 w-0 flex-1">
                                <dl>
                                    <dt class="text-sm font-medium text-gray-500 dark:text-gray-400 truncate">
                                        {{ t('staff_management.active', 'Active') }}
                                    </dt>
                                    <dd class="text-lg font-medium text-gray-900 dark:text-gray-100">
                                        {{ activeEmployees }}
                                    </dd>
                                </dl>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="bg-white dark:bg-gray-800 overflow-hidden shadow rounded-lg">
                    <div class="p-5">
                        <div class="flex items-center">
                            <div class="flex-shrink-0">
                                <span class="text-2xl">🕐</span>
                            </div>
                            <div class="ml-5 w-0 flex-1">
                                <dl>
                                    <dt class="text-sm font-medium text-gray-500 dark:text-gray-400 truncate">
                                        {{ t('staff_management.on_shift', 'On Shift') }}
                                    </dt>
                                    <dd class="text-lg font-medium text-gray-900 dark:text-gray-100">
                                        {{ onShiftEmployees }}
                                    </dd>
                                </dl>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="bg-white dark:bg-gray-800 overflow-hidden shadow rounded-lg">
                    <div class="p-5">
                        <div class="flex items-center">
                            <div class="flex-shrink-0">
                                <span class="text-2xl">🏢</span>
                            </div>
                            <div class="ml-5 w-0 flex-1">
                                <dl>
                                    <dt class="text-sm font-medium text-gray-500 dark:text-gray-400 truncate">
                                        {{ t('staff_management.departments', 'Departments') }}
                                    </dt>
                                    <dd class="text-lg font-medium text-gray-900 dark:text-gray-100">
                                        {{ departments?.length || 0 }}
                                    </dd>
                                </dl>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Filters -->
            <div class="bg-white dark:bg-gray-800 shadow rounded-lg">
                <div class="px-4 py-5 sm:p-6">
                    <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
                        <div>
                            <InputLabel for="search" :value="t('actions.search', 'Search')" />
                            <TextInput
                                id="search"
                                v-model="searchQuery"
                                type="text"
                                :placeholder="t('staff_management.search_employees', 'Search employees...')"
                                class="mt-1 block w-full"
                            />
                        </div>
                        
                        <div>
                            <InputLabel for="department" :value="t('staff_management.department', 'Department')" />
                            <select
                                id="department"
                                v-model="selectedDepartment"
                                class="mt-1 block w-full border-gray-300 dark:border-gray-700 dark:bg-gray-900 dark:text-gray-300 focus:border-indigo-500 dark:focus:border-indigo-600 focus:ring-indigo-500 dark:focus:ring-indigo-600 rounded-md shadow-sm"
                            >
                                <option value="">{{ t('actions.filter', 'All Departments') }}</option>
                                <option v-for="dept in departments" :key="dept.id" :value="dept.id">
                                    {{ dept.name }}
                                </option>
                            </select>
                        </div>
                        
                        <div>
                            <InputLabel for="role" :value="t('staff_management.role', 'Role')" />
                            <select
                                id="role"
                                v-model="selectedRole"
                                class="mt-1 block w-full border-gray-300 dark:border-gray-700 dark:bg-gray-900 dark:text-gray-300 focus:border-indigo-500 dark:focus:border-indigo-600 focus:ring-indigo-500 dark:focus:ring-indigo-600 rounded-md shadow-sm"
                            >
                                <option value="">{{ t('actions.filter', 'All Roles') }}</option>
                                <option v-for="role in roles" :key="role.name" :value="role.name">
                                    {{ role.name }}
                                </option>
                            </select>
                        </div>
                        
                        <div class="flex items-end">
                            <SecondaryButton @click="searchQuery = ''; selectedDepartment = ''; selectedRole = ''">
                                {{ t('actions.clear', 'Clear Filters') }}
                            </SecondaryButton>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Employee List -->
            <div class="bg-white dark:bg-gray-800 shadow overflow-hidden sm:rounded-md">
                <ul class="divide-y divide-gray-200 dark:divide-gray-700">
                    <li v-for="employee in filteredEmployees" :key="employee.id" class="px-6 py-4">
                        <div class="flex items-center justify-between">
                            <div class="flex items-center">
                                <div class="flex-shrink-0 h-12 w-12">
                                    <img class="h-12 w-12 rounded-full object-cover" :src="employee.profile_photo_url" :alt="employee.name">
                                </div>
                                <div class="ml-4">
                                    <div class="flex items-center">
                                        <div class="text-sm font-medium text-gray-900 dark:text-gray-100">
                                            {{ employee.name }}
                                        </div>
                                        <span :class="getStatusColor(employee)" class="ml-2 inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium">
                                            {{ getStatusText(employee) }}
                                        </span>
                                    </div>
                                    <div class="text-sm text-gray-500 dark:text-gray-400">
                                        {{ employee.position }} • {{ employee.department?.name }}
                                    </div>
                                    <div class="text-sm text-gray-500 dark:text-gray-400">
                                        {{ employee.email }}
                                    </div>
                                </div>
                            </div>
                            
                            <div class="flex items-center space-x-2">
                                <div class="text-right mr-4">
                                    <div class="text-sm font-medium text-gray-900 dark:text-gray-100">
                                        {{ employee.salary ? formatCurrency(employee.salary) : (employee.hourly_rate ? formatCurrency(employee.hourly_rate) + '/hr' : 'N/A') }}
                                    </div>
                                    <div class="text-sm text-gray-500 dark:text-gray-400">
                                        {{ t('staff_management.hire_date', 'Hired') }}: {{ new Date(employee.hire_date).toLocaleDateString() }}
                                    </div>
                                </div>
                                
                                <SecondaryButton @click="openEditModal(employee)">
                                    {{ t('actions.edit', 'Edit') }}
                                </SecondaryButton>
                                
                                <button
                                    @click="toggleEmployeeStatus(employee)"
                                    :class="employee.is_active ? 'bg-red-100 text-red-800 hover:bg-red-200' : 'bg-green-100 text-green-800 hover:bg-green-200'"
                                    class="inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md focus:outline-none focus:ring-2 focus:ring-offset-2 transition ease-in-out duration-150"
                                >
                                    {{ employee.is_active ? t('actions.deactivate', 'Deactivate') : t('actions.activate', 'Activate') }}
                                </button>
                                
                                <DangerButton @click="openDeleteModal(employee)">
                                    {{ t('actions.delete', 'Delete') }}
                                </DangerButton>
                            </div>
                        </div>
                    </li>
                </ul>
                
                <div v-if="!filteredEmployees.length" class="text-center py-12">
                    <p class="text-gray-500 dark:text-gray-400">{{ t('messages.no_data', 'No employees found') }}</p>
                </div>
            </div>
        </div>

        <!-- Create Employee Modal -->
        <Modal :show="showCreateModal" @close="showCreateModal = false">
            <div class="px-6 py-4">
                <div class="text-lg font-medium text-gray-900 dark:text-gray-100">
                    {{ t('staff_management.add_employee', 'Add New Employee') }}
                </div>

                <div class="mt-6 space-y-4">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                            <InputLabel for="name" :value="t('staff_management.employee_name', 'Name')" />
                            <TextInput
                                id="name"
                                v-model="form.name"
                                type="text"
                                class="mt-1 block w-full"
                                required
                            />
                            <InputError :message="errors.name" class="mt-2" />
                        </div>

                        <div>
                            <InputLabel for="email" :value="t('customer_management.customer_email', 'Email')" />
                            <TextInput
                                id="email"
                                v-model="form.email"
                                type="email"
                                class="mt-1 block w-full"
                                required
                            />
                            <InputError :message="errors.email" class="mt-2" />
                        </div>
                    </div>

                    <!-- Add more form fields here -->
                </div>

                <div class="flex items-center justify-end mt-6 space-x-3">
                    <SecondaryButton @click="showCreateModal = false">
                        {{ t('actions.cancel', 'Cancel') }}
                    </SecondaryButton>
                    <PrimaryButton @click="createEmployee">
                        {{ t('actions.create', 'Create Employee') }}
                    </PrimaryButton>
                </div>
            </div>
        </Modal>

        <!-- Edit Employee Modal -->
        <Modal :show="showEditModal" @close="showEditModal = false">
            <!-- Similar structure to create modal -->
        </Modal>

        <!-- Delete Confirmation Modal -->
        <Modal :show="showDeleteModal" @close="showDeleteModal = false">
            <div class="px-6 py-4">
                <div class="text-lg font-medium text-gray-900 dark:text-gray-100">
                    {{ t('messages.confirm_delete', 'Confirm Delete') }}
                </div>
                <p class="mt-2 text-sm text-gray-600 dark:text-gray-400">
                    {{ t('staff_management.delete_employee_warning', 'Are you sure you want to delete this employee? This action cannot be undone.') }}
                </p>
                
                <div class="flex items-center justify-end mt-6 space-x-3">
                    <SecondaryButton @click="showDeleteModal = false">
                        {{ t('actions.cancel', 'Cancel') }}
                    </SecondaryButton>
                    <DangerButton @click="deleteEmployee">
                        {{ t('actions.delete', 'Delete') }}
                    </DangerButton>
                </div>
            </div>
        </Modal>
    </ManagerLayout>
</template>
