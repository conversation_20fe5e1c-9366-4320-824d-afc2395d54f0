<script setup>
import { ref, onMounted, computed } from 'vue';
import { Head } from '@inertiajs/vue3';
import ManagerLayout from '@/Layouts/ManagerLayout.vue';

const props = defineProps({
    analytics: Object,
    recentActivities: Object,
    dateRange: Object,
});

const t = ref(() => '');

onMounted(() => {
    setTimeout(() => {
        t.value = window.t || ((key, fallback) => fallback || key);
    }, 100);
});

// Computed properties for analytics
const revenueGrowth = computed(() => {
    return props.analytics?.revenue?.growth_rate || 0;
});

const totalRevenue = computed(() => {
    return props.analytics?.revenue?.total || 0;
});

const totalOrders = computed(() => {
    return props.analytics?.orders?.total || 0;
});

const averageOrderValue = computed(() => {
    return props.analytics?.orders?.average_value || 0;
});

const totalCustomers = computed(() => {
    return props.analytics?.customers?.total || 0;
});

const newCustomers = computed(() => {
    return props.analytics?.customers?.new || 0;
});

const onShiftStaff = computed(() => {
    return props.analytics?.staff?.on_shift || 0;
});

const totalStaff = computed(() => {
    return props.analytics?.staff?.total || 0;
});

// Format currency
const formatCurrency = (amount) => {
    return new Intl.NumberFormat('en-US', {
        style: 'currency',
        currency: 'USD'
    }).format(amount);
};

// Format percentage
const formatPercentage = (value) => {
    return `${value > 0 ? '+' : ''}${value.toFixed(1)}%`;
};
</script>

<template>
    <Head :title="t('dashboard_titles.manager_dashboard', 'Manager Dashboard')" />

    <ManagerLayout>
        <template #header>
            <div class="flex justify-between items-center">
                <h2 class="font-semibold text-xl text-gray-800 dark:text-gray-200 leading-tight">
                    {{ t('dashboard_titles.manager_dashboard', 'Manager Dashboard') }}
                </h2>
                <div class="flex items-center space-x-4">
                    <select class="rounded-md border-gray-300 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50">
                        <option value="today">{{ t('time_date.today', 'Today') }}</option>
                        <option value="week">{{ t('time_date.this_week', 'This Week') }}</option>
                        <option value="month">{{ t('time_date.this_month', 'This Month') }}</option>
                    </select>
                    <button class="bg-indigo-600 hover:bg-indigo-700 text-white px-4 py-2 rounded-md text-sm font-medium">
                        {{ t('actions.refresh', 'Refresh') }}
                    </button>
                </div>
            </div>
        </template>

        <div class="py-6">
            <!-- Key Metrics Cards -->
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
                <!-- Revenue Card -->
                <div class="bg-white dark:bg-gray-800 overflow-hidden shadow rounded-lg">
                    <div class="p-5">
                        <div class="flex items-center">
                            <div class="flex-shrink-0">
                                <div class="w-8 h-8 bg-green-500 rounded-md flex items-center justify-center">
                                    <span class="text-white text-lg">💰</span>
                                </div>
                            </div>
                            <div class="ml-5 w-0 flex-1">
                                <dl>
                                    <dt class="text-sm font-medium text-gray-500 dark:text-gray-400 truncate">
                                        {{ t('reports_analytics.monthly_revenue', 'Total Revenue') }}
                                    </dt>
                                    <dd class="text-lg font-medium text-gray-900 dark:text-gray-100">
                                        {{ formatCurrency(totalRevenue) }}
                                    </dd>
                                </dl>
                            </div>
                        </div>
                    </div>
                    <div class="bg-gray-50 dark:bg-gray-700 px-5 py-3">
                        <div class="text-sm">
                            <span :class="revenueGrowth >= 0 ? 'text-green-600' : 'text-red-600'" class="font-medium">
                                {{ formatPercentage(revenueGrowth) }}
                            </span>
                            <span class="text-gray-500 dark:text-gray-400"> from last period</span>
                        </div>
                    </div>
                </div>

                <!-- Orders Card -->
                <div class="bg-white dark:bg-gray-800 overflow-hidden shadow rounded-lg">
                    <div class="p-5">
                        <div class="flex items-center">
                            <div class="flex-shrink-0">
                                <div class="w-8 h-8 bg-blue-500 rounded-md flex items-center justify-center">
                                    <span class="text-white text-lg">🛒</span>
                                </div>
                            </div>
                            <div class="ml-5 w-0 flex-1">
                                <dl>
                                    <dt class="text-sm font-medium text-gray-500 dark:text-gray-400 truncate">
                                        {{ t('order_management.orders', 'Total Orders') }}
                                    </dt>
                                    <dd class="text-lg font-medium text-gray-900 dark:text-gray-100">
                                        {{ totalOrders.toLocaleString() }}
                                    </dd>
                                </dl>
                            </div>
                        </div>
                    </div>
                    <div class="bg-gray-50 dark:bg-gray-700 px-5 py-3">
                        <div class="text-sm">
                            <span class="text-gray-500 dark:text-gray-400">
                                {{ t('order_management.average_value', 'Avg:') }} {{ formatCurrency(averageOrderValue) }}
                            </span>
                        </div>
                    </div>
                </div>

                <!-- Customers Card -->
                <div class="bg-white dark:bg-gray-800 overflow-hidden shadow rounded-lg">
                    <div class="p-5">
                        <div class="flex items-center">
                            <div class="flex-shrink-0">
                                <div class="w-8 h-8 bg-purple-500 rounded-md flex items-center justify-center">
                                    <span class="text-white text-lg">👥</span>
                                </div>
                            </div>
                            <div class="ml-5 w-0 flex-1">
                                <dl>
                                    <dt class="text-sm font-medium text-gray-500 dark:text-gray-400 truncate">
                                        {{ t('customer_management.customers', 'Total Customers') }}
                                    </dt>
                                    <dd class="text-lg font-medium text-gray-900 dark:text-gray-100">
                                        {{ totalCustomers.toLocaleString() }}
                                    </dd>
                                </dl>
                            </div>
                        </div>
                    </div>
                    <div class="bg-gray-50 dark:bg-gray-700 px-5 py-3">
                        <div class="text-sm">
                            <span class="text-green-600 font-medium">+{{ newCustomers }}</span>
                            <span class="text-gray-500 dark:text-gray-400"> new this period</span>
                        </div>
                    </div>
                </div>

                <!-- Staff Card -->
                <div class="bg-white dark:bg-gray-800 overflow-hidden shadow rounded-lg">
                    <div class="p-5">
                        <div class="flex items-center">
                            <div class="flex-shrink-0">
                                <div class="w-8 h-8 bg-orange-500 rounded-md flex items-center justify-center">
                                    <span class="text-white text-lg">👨‍💼</span>
                                </div>
                            </div>
                            <div class="ml-5 w-0 flex-1">
                                <dl>
                                    <dt class="text-sm font-medium text-gray-500 dark:text-gray-400 truncate">
                                        {{ t('staff_management.staff', 'Staff On Duty') }}
                                    </dt>
                                    <dd class="text-lg font-medium text-gray-900 dark:text-gray-100">
                                        {{ onShiftStaff }} / {{ totalStaff }}
                                    </dd>
                                </dl>
                            </div>
                        </div>
                    </div>
                    <div class="bg-gray-50 dark:bg-gray-700 px-5 py-3">
                        <div class="text-sm">
                            <span class="text-gray-500 dark:text-gray-400">
                                {{ Math.round((onShiftStaff / totalStaff) * 100) }}% {{ t('staff_management.on_shift', 'on shift') }}
                            </span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Charts and Analytics Row -->
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
                <!-- Revenue Chart -->
                <div class="bg-white dark:bg-gray-800 overflow-hidden shadow rounded-lg">
                    <div class="px-4 py-5 sm:p-6">
                        <h3 class="text-lg leading-6 font-medium text-gray-900 dark:text-gray-100 mb-4">
                            {{ t('reports_analytics.revenue_chart', 'Revenue Trend') }}
                        </h3>
                        <div class="h-64 flex items-center justify-center bg-gray-50 dark:bg-gray-700 rounded">
                            <p class="text-gray-500 dark:text-gray-400">{{ t('messages.loading', 'Chart Loading...') }}</p>
                        </div>
                    </div>
                </div>

                <!-- Order Status Distribution -->
                <div class="bg-white dark:bg-gray-800 overflow-hidden shadow rounded-lg">
                    <div class="px-4 py-5 sm:p-6">
                        <h3 class="text-lg leading-6 font-medium text-gray-900 dark:text-gray-100 mb-4">
                            {{ t('order_management.order_status', 'Order Status') }}
                        </h3>
                        <div class="space-y-3">
                            <div v-for="status in analytics?.orders?.by_status || []" :key="status.status" class="flex justify-between items-center">
                                <span class="text-sm font-medium text-gray-900 dark:text-gray-100 capitalize">
                                    {{ t(`order_management.${status.status}`, status.status) }}
                                </span>
                                <span class="text-sm text-gray-500 dark:text-gray-400">
                                    {{ status.count }}
                                </span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Recent Activities and Top Performers -->
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                <!-- Recent Orders -->
                <div class="bg-white dark:bg-gray-800 overflow-hidden shadow rounded-lg">
                    <div class="px-4 py-5 sm:p-6">
                        <h3 class="text-lg leading-6 font-medium text-gray-900 dark:text-gray-100 mb-4">
                            {{ t('order_management.recent_orders', 'Recent Orders') }}
                        </h3>
                        <div class="space-y-3">
                            <div v-for="order in recentActivities?.orders?.slice(0, 5) || []" :key="order.id" class="flex justify-between items-center py-2 border-b border-gray-200 dark:border-gray-600 last:border-b-0">
                                <div>
                                    <p class="text-sm font-medium text-gray-900 dark:text-gray-100">
                                        #{{ order.order_number }}
                                    </p>
                                    <p class="text-xs text-gray-500 dark:text-gray-400">
                                        {{ order.customer?.name || 'Guest' }}
                                    </p>
                                </div>
                                <div class="text-right">
                                    <p class="text-sm font-medium text-gray-900 dark:text-gray-100">
                                        {{ formatCurrency(order.total_amount) }}
                                    </p>
                                    <span :class="{
                                        'bg-yellow-100 text-yellow-800': order.status === 'pending',
                                        'bg-blue-100 text-blue-800': order.status === 'confirmed',
                                        'bg-orange-100 text-orange-800': order.status === 'preparing',
                                        'bg-green-100 text-green-800': order.status === 'ready',
                                        'bg-gray-100 text-gray-800': order.status === 'served'
                                    }" class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium">
                                        {{ t(`order_management.${order.status}`, order.status) }}
                                    </span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Top Menu Items -->
                <div class="bg-white dark:bg-gray-800 overflow-hidden shadow rounded-lg">
                    <div class="px-4 py-5 sm:p-6">
                        <h3 class="text-lg leading-6 font-medium text-gray-900 dark:text-gray-100 mb-4">
                            {{ t('reports_analytics.top_items', 'Top Menu Items') }}
                        </h3>
                        <div class="space-y-3">
                            <div v-for="item in analytics?.menu?.top_items?.slice(0, 5) || []" :key="item.id" class="flex justify-between items-center">
                                <div>
                                    <p class="text-sm font-medium text-gray-900 dark:text-gray-100">
                                        {{ item.name }}
                                    </p>
                                    <p class="text-xs text-gray-500 dark:text-gray-400">
                                        {{ formatCurrency(item.price) }}
                                    </p>
                                </div>
                                <div class="text-right">
                                    <p class="text-sm font-medium text-gray-900 dark:text-gray-100">
                                        {{ item.order_items_sum_quantity || 0 }} {{ t('order_management.orders', 'sold') }}
                                    </p>
                                </div>
                            </div>
                            <div v-if="!analytics?.menu?.top_items?.length" class="text-center py-4">
                                <p class="text-gray-500 dark:text-gray-400">{{ t('messages.no_data', 'No data available') }}</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </ManagerLayout>
</template>
