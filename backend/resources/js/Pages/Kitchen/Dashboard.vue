<script setup>
import { ref, onMounted, computed } from 'vue';
import { Head } from '@inertiajs/vue3';
import KitchenLayout from '@/Layouts/KitchenLayout.vue';

const props = defineProps({
    orderQueue: Array,
    kitchenStats: Object,
    preparationTimes: Object,
});

const t = ref(() => '');

onMounted(() => {
    setTimeout(() => {
        t.value = window.t || ((key, fallback) => fallback || key);
    }, 100);

    // Auto-refresh every 30 seconds
    setInterval(() => {
        window.location.reload();
    }, 30000);
});

// Computed properties
const pendingOrders = computed(() => {
    return props.orderQueue?.filter(order => order.status === 'pending').length || 0;
});

const preparingOrders = computed(() => {
    return props.orderQueue?.filter(order => order.status === 'preparing').length || 0;
});

const readyOrders = computed(() => {
    return props.orderQueue?.filter(order => order.status === 'ready').length || 0;
});

// Order priority colors
const getOrderPriorityColor = (order) => {
    const timeSinceOrder = new Date() - new Date(order.created_at);
    const minutes = Math.floor(timeSinceOrder / 60000);
    
    if (minutes > 30) return 'border-red-500 bg-red-50';
    if (minutes > 20) return 'border-orange-500 bg-orange-50';
    if (minutes > 10) return 'border-yellow-500 bg-yellow-50';
    return 'border-green-500 bg-green-50';
};

// Order status colors
const getOrderStatusColor = (status) => {
    const colors = {
        'pending': 'bg-red-100 text-red-800',
        'preparing': 'bg-yellow-100 text-yellow-800',
        'ready': 'bg-green-100 text-green-800'
    };
    return colors[status] || 'bg-gray-100 text-gray-800';
};

// Format time
const formatTime = (dateString) => {
    return new Date(dateString).toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
};

// Calculate time since order
const getTimeSinceOrder = (dateString) => {
    const timeSinceOrder = new Date() - new Date(dateString);
    const minutes = Math.floor(timeSinceOrder / 60000);
    return `${minutes}m`;
};

// Get estimated completion time
const getEstimatedTime = (order) => {
    const avgPrepTime = props.preparationTimes?.[order.id] || 15; // Default 15 minutes
    const startTime = new Date(order.cooking_started_at || order.created_at);
    const estimatedCompletion = new Date(startTime.getTime() + (avgPrepTime * 60000));
    return formatTime(estimatedCompletion);
};
</script>

<template>
    <Head :title="t('dashboard_titles.kitchen_dashboard', 'Kitchen Dashboard')" />

    <KitchenLayout>
        <template #header>
            <div class="flex justify-between items-center">
                <h2 class="font-semibold text-2xl text-gray-800 dark:text-gray-200 leading-tight">
                    🍳 {{ t('dashboard_titles.kitchen_dashboard', 'Kitchen Dashboard') }}
                </h2>
                <div class="flex items-center space-x-4">
                    <div class="text-lg font-bold text-gray-900 dark:text-gray-100">
                        {{ new Date().toLocaleTimeString() }}
                    </div>
                    <div class="bg-orange-100 text-orange-800 px-3 py-1 rounded-full text-sm font-medium">
                        {{ orderQueue?.length || 0 }} {{ t('kitchen.order_queue', 'Orders in Queue') }}
                    </div>
                </div>
            </div>
        </template>

        <div class="space-y-6">
            <!-- Kitchen Stats -->
            <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
                <div class="bg-red-100 border border-red-200 rounded-lg p-6 text-center">
                    <div class="text-3xl font-bold text-red-800">{{ pendingOrders }}</div>
                    <div class="text-sm text-red-600 font-medium">{{ t('order_management.pending', 'Pending') }}</div>
                </div>
                
                <div class="bg-yellow-100 border border-yellow-200 rounded-lg p-6 text-center">
                    <div class="text-3xl font-bold text-yellow-800">{{ preparingOrders }}</div>
                    <div class="text-sm text-yellow-600 font-medium">{{ t('order_management.preparing', 'Preparing') }}</div>
                </div>
                
                <div class="bg-green-100 border border-green-200 rounded-lg p-6 text-center">
                    <div class="text-3xl font-bold text-green-800">{{ readyOrders }}</div>
                    <div class="text-sm text-green-600 font-medium">{{ t('order_management.ready', 'Ready') }}</div>
                </div>
                
                <div class="bg-blue-100 border border-blue-200 rounded-lg p-6 text-center">
                    <div class="text-3xl font-bold text-blue-800">{{ kitchenStats?.avg_prep_time || 0 }}m</div>
                    <div class="text-sm text-blue-600 font-medium">{{ t('kitchen.preparation_time', 'Avg Prep Time') }}</div>
                </div>
            </div>

            <!-- Order Queue Display -->
            <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
                <!-- Pending Orders -->
                <div>
                    <h3 class="text-xl font-bold text-red-800 mb-4 flex items-center">
                        <span class="mr-2">🔥</span>
                        {{ t('order_management.pending', 'Pending Orders') }}
                    </h3>
                    <div class="space-y-4 max-h-96 overflow-y-auto">
                        <div 
                            v-for="order in orderQueue?.filter(o => o.status === 'pending')" 
                            :key="order.id"
                            :class="getOrderPriorityColor(order)"
                            class="border-l-4 rounded-lg p-4 shadow-sm"
                        >
                            <div class="flex justify-between items-start mb-3">
                                <div>
                                    <h4 class="text-lg font-bold text-gray-900">#{{ order.order_number }}</h4>
                                    <p class="text-sm text-gray-600">
                                        {{ t('table_management.table_number', 'Table') }} {{ order.table?.number || 'Takeaway' }}
                                    </p>
                                    <p class="text-xs text-gray-500">
                                        {{ formatTime(order.created_at) }} ({{ getTimeSinceOrder(order.created_at) }} ago)
                                    </p>
                                </div>
                                <button 
                                    class="bg-orange-600 hover:bg-orange-700 text-white px-4 py-2 rounded-md text-sm font-medium"
                                    @click="startCooking(order.id)"
                                >
                                    {{ t('kitchen.start_cooking', 'Start Cooking') }}
                                </button>
                            </div>
                            
                            <div class="space-y-2">
                                <div v-for="item in order.items" :key="item.id" class="flex justify-between items-center bg-white rounded p-2">
                                    <div>
                                        <span class="font-medium">{{ item.quantity }}x {{ item.menu_item.name }}</span>
                                        <div v-if="item.special_instructions" class="text-xs text-orange-600 mt-1">
                                            📝 {{ item.special_instructions }}
                                        </div>
                                    </div>
                                    <div class="text-sm text-gray-500">
                                        {{ item.menu_item.preparation_time || 15 }}m
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div v-if="!orderQueue?.filter(o => o.status === 'pending').length" class="text-center py-8">
                            <p class="text-gray-500">{{ t('messages.no_data', 'No pending orders') }}</p>
                        </div>
                    </div>
                </div>

                <!-- Preparing Orders -->
                <div>
                    <h3 class="text-xl font-bold text-yellow-800 mb-4 flex items-center">
                        <span class="mr-2">🍳</span>
                        {{ t('order_management.preparing', 'Preparing') }}
                    </h3>
                    <div class="space-y-4 max-h-96 overflow-y-auto">
                        <div 
                            v-for="order in orderQueue?.filter(o => o.status === 'preparing')" 
                            :key="order.id"
                            class="border-l-4 border-yellow-500 bg-yellow-50 rounded-lg p-4 shadow-sm"
                        >
                            <div class="flex justify-between items-start mb-3">
                                <div>
                                    <h4 class="text-lg font-bold text-gray-900">#{{ order.order_number }}</h4>
                                    <p class="text-sm text-gray-600">
                                        {{ t('table_management.table_number', 'Table') }} {{ order.table?.number || 'Takeaway' }}
                                    </p>
                                    <p class="text-xs text-gray-500">
                                        {{ t('kitchen.estimated_time', 'Est:') }} {{ getEstimatedTime(order) }}
                                    </p>
                                </div>
                                <button 
                                    class="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-md text-sm font-medium"
                                    @click="markReady(order.id)"
                                >
                                    {{ t('kitchen.mark_ready', 'Mark Ready') }}
                                </button>
                            </div>
                            
                            <div class="space-y-2">
                                <div v-for="item in order.items" :key="item.id" class="flex justify-between items-center bg-white rounded p-2">
                                    <div>
                                        <span class="font-medium">{{ item.quantity }}x {{ item.menu_item.name }}</span>
                                        <div v-if="item.special_instructions" class="text-xs text-orange-600 mt-1">
                                            📝 {{ item.special_instructions }}
                                        </div>
                                    </div>
                                    <div class="text-sm text-gray-500">
                                        {{ getTimeSinceOrder(order.cooking_started_at || order.created_at) }}
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div v-if="!orderQueue?.filter(o => o.status === 'preparing').length" class="text-center py-8">
                            <p class="text-gray-500">{{ t('messages.no_data', 'No orders being prepared') }}</p>
                        </div>
                    </div>
                </div>

                <!-- Ready Orders -->
                <div>
                    <h3 class="text-xl font-bold text-green-800 mb-4 flex items-center">
                        <span class="mr-2">✅</span>
                        {{ t('order_management.ready', 'Ready for Pickup') }}
                    </h3>
                    <div class="space-y-4 max-h-96 overflow-y-auto">
                        <div 
                            v-for="order in orderQueue?.filter(o => o.status === 'ready')" 
                            :key="order.id"
                            class="border-l-4 border-green-500 bg-green-50 rounded-lg p-4 shadow-sm"
                        >
                            <div class="flex justify-between items-start mb-3">
                                <div>
                                    <h4 class="text-lg font-bold text-gray-900">#{{ order.order_number }}</h4>
                                    <p class="text-sm text-gray-600">
                                        {{ t('table_management.table_number', 'Table') }} {{ order.table?.number || 'Takeaway' }}
                                    </p>
                                    <p class="text-xs text-gray-500">
                                        {{ t('order_management.ready', 'Ready at') }} {{ formatTime(order.ready_at) }}
                                    </p>
                                </div>
                                <div class="bg-green-100 text-green-800 px-3 py-1 rounded-full text-sm font-medium">
                                    {{ t('order_management.ready', 'Ready') }}
                                </div>
                            </div>
                            
                            <div class="space-y-2">
                                <div v-for="item in order.items" :key="item.id" class="flex justify-between items-center bg-white rounded p-2">
                                    <span class="font-medium">{{ item.quantity }}x {{ item.menu_item.name }}</span>
                                </div>
                            </div>
                        </div>
                        
                        <div v-if="!orderQueue?.filter(o => o.status === 'ready').length" class="text-center py-8">
                            <p class="text-gray-500">{{ t('messages.no_data', 'No orders ready') }}</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </KitchenLayout>
</template>

<style scoped>
/* Kitchen-specific styles for better visibility */
.text-3xl {
    font-size: 2.5rem;
}

.max-h-96 {
    max-height: 24rem;
}

/* Pulse animation for urgent orders */
@keyframes pulse-red {
    0%, 100% { 
        border-color: rgb(239 68 68);
        background-color: rgb(254 242 242);
    }
    50% { 
        border-color: rgb(220 38 38);
        background-color: rgb(254 226 226);
    }
}

.border-red-500.bg-red-50 {
    animation: pulse-red 2s infinite;
}
</style>
