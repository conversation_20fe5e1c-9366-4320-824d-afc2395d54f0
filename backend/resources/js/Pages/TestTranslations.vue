<template>
    <ManagerLayout title="Test Translations">
        <template #header>
            <h2 class="font-semibold text-xl text-gray-800 dark:text-gray-200 leading-tight">
                🌐 {{ $t('Test Translations') }}
            </h2>
        </template>

        <div class="py-12">
            <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
                <div class="bg-white dark:bg-gray-800 overflow-hidden shadow-xl sm:rounded-lg">
                    <div class="p-6">
                        <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100 mb-6">
                            Vue i18n Translation Test
                        </h3>

                        <div class="space-y-4">
                            <!-- Test Basic Translations -->
                            <div class="border-b pb-4">
                                <h4 class="font-medium text-gray-900 dark:text-gray-100 mb-2">Basic Translations:</h4>
                                <div class="grid grid-cols-2 gap-4 text-sm">
                                    <div>
                                        <strong>branches.title:</strong> {{ $t('branches.title') }}
                                    </div>
                                    <div>
                                        <strong>branches.management:</strong> {{ $t('branches.management') }}
                                    </div>
                                    <div>
                                        <strong>categories.title:</strong> {{ $t('categories.title') }}
                                    </div>
                                    <div>
                                        <strong>forms.save:</strong> {{ $t('forms.save') }}
                                    </div>
                                </div>
                            </div>

                            <!-- Test Form Translations -->
                            <div class="border-b pb-4">
                                <h4 class="font-medium text-gray-900 dark:text-gray-100 mb-2">Form Translations:</h4>
                                <div class="grid grid-cols-2 gap-4 text-sm">
                                    <div>
                                        <strong>forms.required:</strong> {{ $t('forms.required') }}
                                    </div>
                                    <div>
                                        <strong>forms.optional:</strong> {{ $t('forms.optional') }}
                                    </div>
                                    <div>
                                        <strong>forms.search:</strong> {{ $t('forms.search') }}
                                    </div>
                                    <div>
                                        <strong>forms.cancel:</strong> {{ $t('forms.cancel') }}
                                    </div>
                                </div>
                            </div>

                            <!-- Test Message Translations -->
                            <div class="border-b pb-4">
                                <h4 class="font-medium text-gray-900 dark:text-gray-100 mb-2">Message Translations:</h4>
                                <div class="grid grid-cols-2 gap-4 text-sm">
                                    <div>
                                        <strong>messages.success:</strong> {{ $t('messages.success') }}
                                    </div>
                                    <div>
                                        <strong>messages.error:</strong> {{ $t('messages.error') }}
                                    </div>
                                    <div>
                                        <strong>messages.loading:</strong> {{ $t('messages.loading') }}
                                    </div>
                                    <div>
                                        <strong>messages.confirm_delete:</strong> {{ $t('messages.confirm_delete') }}
                                    </div>
                                </div>
                            </div>

                            <!-- Test Pagination Translations -->
                            <div class="border-b pb-4">
                                <h4 class="font-medium text-gray-900 dark:text-gray-100 mb-2">Pagination Translations:</h4>
                                <div class="grid grid-cols-2 gap-4 text-sm">
                                    <div>
                                        <strong>pagination.showing:</strong> {{ $t('pagination.showing') }}
                                    </div>
                                    <div>
                                        <strong>pagination.results:</strong> {{ $t('pagination.results') }}
                                    </div>
                                    <div>
                                        <strong>pagination.previous:</strong> {{ $t('pagination.previous') }}
                                    </div>
                                    <div>
                                        <strong>pagination.next:</strong> {{ $t('pagination.next') }}
                                    </div>
                                </div>
                            </div>

                            <!-- Current Locale Info -->
                            <div class="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg">
                                <h4 class="font-medium text-gray-900 dark:text-gray-100 mb-2">Current Locale Info:</h4>
                                <div class="text-sm text-gray-600 dark:text-gray-300">
                                    <p><strong>Current Locale:</strong> {{ currentLocale }}</p>
                                    <p><strong>Available Locales:</strong> {{ availableLocales.map(l => l.code).join(', ') }}</p>
                                </div>
                            </div>

                            <!-- Language Switcher Test -->
                            <div class="bg-blue-50 dark:bg-blue-900 p-4 rounded-lg">
                                <h4 class="font-medium text-gray-900 dark:text-gray-100 mb-2">Language Switcher Test:</h4>
                                <p class="text-sm text-gray-600 dark:text-gray-300 mb-3">
                                    Use the language switcher in the top navigation to test switching between English and Bengali.
                                </p>
                                <div class="flex space-x-2">
                                    <button
                                        @click="switchToEnglish"
                                        class="px-3 py-1 bg-blue-600 text-white rounded text-sm hover:bg-blue-700"
                                    >
                                        Switch to English
                                    </button>
                                    <button
                                        @click="switchToBengali"
                                        class="px-3 py-1 bg-green-600 text-white rounded text-sm hover:bg-green-700"
                                    >
                                        Switch to Bengali
                                    </button>
                                </div>
                            </div>

                            <!-- Test Navigation Links -->
                            <div class="bg-yellow-50 dark:bg-yellow-900 p-4 rounded-lg">
                                <h4 class="font-medium text-gray-900 dark:text-gray-100 mb-2">Test Navigation:</h4>
                                <div class="flex space-x-4">
                                    <Link
                                        :href="route('branches.index')"
                                        class="text-blue-600 hover:text-blue-800 underline"
                                    >
                                        Go to Branches ({{ $t('branches.title') }})
                                    </Link>
                                    <Link
                                        :href="route('categories.index')"
                                        class="text-blue-600 hover:text-blue-800 underline"
                                    >
                                        Go to Categories ({{ $t('categories.title') }})
                                    </Link>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </ManagerLayout>
</template>

<script setup>
import { computed } from 'vue'
import { Link } from '@inertiajs/vue3'
import { useI18n } from 'vue-i18n'
import ManagerLayout from '@/Layouts/ManagerLayout.vue'
import { changeLocale, getCurrentLocale, getAvailableLocales } from '@/i18n'

// Use Vue i18n
const { t, locale } = useI18n()

// Get current locale and available locales
const currentLocale = computed(() => getCurrentLocale())
const availableLocales = getAvailableLocales()

// Language switching functions
const switchToEnglish = () => {
    changeLocale('en')
}

const switchToBengali = () => {
    changeLocale('bn')
}
</script>
