-- php<PERSON><PERSON><PERSON><PERSON><PERSON> SQL Dump
-- version 5.2.1deb3
-- https://www.phpmyadmin.net/
--
-- Host: localhost:3306
-- Generation Time: Jun 30, 2025 at 08:37 AM
-- Server version: 8.0.42-0ubuntu0.24.04.1
-- PHP Version: 8.3.22

SET SQL_MODE = "NO_AUTO_VALUE_ON_ZERO";
START TRANSACTION;
SET time_zone = "+00:00";


/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!40101 SET NAMES utf8mb4 */;

--
-- Database: `rest`
--

-- --------------------------------------------------------

--
-- Table structure for table `blogs`
--

CREATE TABLE `blogs` (
  `id` bigint UNSIGNED NOT NULL,
  `title` json NOT NULL,
  `slug` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `content` longtext COLLATE utf8mb4_unicode_ci NOT NULL,
  `excerpt` text COLLATE utf8mb4_unicode_ci,
  `featured_image_id` bigint UNSIGNED DEFAULT NULL,
  `meta_description` text COLLATE utf8mb4_unicode_ci,
  `published_at` timestamp NULL DEFAULT NULL,
  `author_id` bigint UNSIGNED DEFAULT NULL,
  `tags` json DEFAULT NULL,
  `categories` json DEFAULT NULL,
  `sort_order` int NOT NULL DEFAULT '0',
  `created_by` bigint UNSIGNED DEFAULT NULL,
  `updated_by` bigint UNSIGNED DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `status` enum('draft','published') COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'draft'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `branches`
--

CREATE TABLE `branches` (
  `id` bigint UNSIGNED NOT NULL,
  `name` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `slug` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `description` text COLLATE utf8mb4_unicode_ci,
  `address` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `city` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `state` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `postal_code` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `country` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'Bangladesh',
  `phone` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `email` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `latitude` decimal(10,8) DEFAULT NULL,
  `longitude` decimal(11,8) DEFAULT NULL,
  `opening_time` time NOT NULL DEFAULT '09:00:00',
  `closing_time` time NOT NULL DEFAULT '22:00:00',
  `is_active` tinyint(1) NOT NULL DEFAULT '1',
  `is_main_branch` tinyint(1) NOT NULL DEFAULT '0',
  `sort_order` int NOT NULL DEFAULT '0',
  `operating_days` json DEFAULT NULL,
  `contact_info` json DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `cache`
--

CREATE TABLE `cache` (
  `key` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `value` mediumtext COLLATE utf8mb4_unicode_ci NOT NULL,
  `expiration` int NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `cache`
--

INSERT INTO `cache` (`key`, `value`, `expiration`) VALUES
('laravel_cache_spatie.permission.cache', 'a:3:{s:5:\"alias\";a:4:{s:1:\"a\";s:2:\"id\";s:1:\"b\";s:4:\"name\";s:1:\"c\";s:10:\"guard_name\";s:1:\"r\";s:5:\"roles\";}s:11:\"permissions\";a:44:{i:0;a:4:{s:1:\"a\";i:1;s:1:\"b\";s:14:\"manage_tenants\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:1:{i:0;i:1;}}i:1;a:4:{s:1:\"a\";i:2;s:1:\"b\";s:20:\"manage_subscriptions\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:1:{i:0;i:1;}}i:2;a:4:{s:1:\"a\";i:3;s:1:\"b\";s:15:\"manage_payments\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:1:{i:0;i:1;}}i:3;a:4:{s:1:\"a\";i:4;s:1:\"b\";s:20:\"view_admin_dashboard\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:1:{i:0;i:1;}}i:4;a:4:{s:1:\"a\";i:5;s:1:\"b\";s:22:\"manage_system_settings\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:1:{i:0;i:1;}}i:5;a:4:{s:1:\"a\";i:6;s:1:\"b\";s:17:\"manage_restaurant\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:2:{i:0;i:1;i:1;i:2;}}i:6;a:4:{s:1:\"a\";i:7;s:1:\"b\";s:11:\"manage_menu\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:2:{i:0;i:1;i:1;i:2;}}i:7;a:4:{s:1:\"a\";i:8;s:1:\"b\";s:17:\"manage_categories\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:2:{i:0;i:1;i:1;i:2;}}i:8;a:4:{s:1:\"a\";i:9;s:1:\"b\";s:20:\"manage_subcategories\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:2:{i:0;i:1;i:1;i:2;}}i:9;a:4:{s:1:\"a\";i:10;s:1:\"b\";s:12:\"manage_staff\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:2:{i:0;i:1;i:1;i:2;}}i:10;a:4:{s:1:\"a\";i:11;s:1:\"b\";s:15:\"manage_branches\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:2:{i:0;i:1;i:1;i:2;}}i:11;a:4:{s:1:\"a\";i:12;s:1:\"b\";s:16:\"manage_employees\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:2:{i:0;i:1;i:1;i:2;}}i:12;a:4:{s:1:\"a\";i:13;s:1:\"b\";s:22:\"manage_employee_shifts\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:2:{i:0;i:1;i:1;i:2;}}i:13;a:4:{s:1:\"a\";i:14;s:1:\"b\";s:12:\"view_reports\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:2:{i:0;i:1;i:1;i:2;}}i:14;a:4:{s:1:\"a\";i:15;s:1:\"b\";s:13:\"manage_orders\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:2:{i:0;i:1;i:1;i:2;}}i:15;a:4:{s:1:\"a\";i:16;s:1:\"b\";s:16:\"manage_customers\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:2:{i:0;i:1;i:1;i:2;}}i:16;a:4:{s:1:\"a\";i:17;s:1:\"b\";s:13:\"manage_tables\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:3:{i:0;i:1;i:1;i:2;i:2;i:3;}}i:17;a:4:{s:1:\"a\";i:18;s:1:\"b\";s:19:\"manage_reservations\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:3:{i:0;i:1;i:1;i:2;i:2;i:3;}}i:18;a:4:{s:1:\"a\";i:19;s:1:\"b\";s:14:\"view_analytics\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:2:{i:0;i:1;i:1;i:2;}}i:19;a:4:{s:1:\"a\";i:20;s:1:\"b\";s:16:\"manage_inventory\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:2:{i:0;i:1;i:1;i:2;}}i:20;a:4:{s:1:\"a\";i:21;s:1:\"b\";s:15:\"manage_expenses\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:2:{i:0;i:1;i:1;i:2;}}i:21;a:4:{s:1:\"a\";i:22;s:1:\"b\";s:21:\"manage_delivery_zones\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:2:{i:0;i:1;i:1;i:2;}}i:22;a:4:{s:1:\"a\";i:23;s:1:\"b\";s:17:\"manage_promotions\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:2:{i:0;i:1;i:1;i:2;}}i:23;a:4:{s:1:\"a\";i:24;s:1:\"b\";s:11:\"take_orders\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:3:{i:0;i:1;i:1;i:2;i:2;i:3;}}i:24;a:4:{s:1:\"a\";i:25;s:1:\"b\";s:9:\"view_menu\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:4:{i:0;i:1;i:1;i:2;i:2;i:3;i:3;i:4;}}i:25;a:4:{s:1:\"a\";i:26;s:1:\"b\";s:19:\"update_order_status\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:3:{i:0;i:1;i:1;i:2;i:2;i:3;}}i:26;a:4:{s:1:\"a\";i:27;s:1:\"b\";s:18:\"view_customer_info\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:3:{i:0;i:1;i:1;i:2;i:2;i:3;}}i:27;a:4:{s:1:\"a\";i:28;s:1:\"b\";s:16:\"process_payments\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:3:{i:0;i:1;i:1;i:2;i:2;i:3;}}i:28;a:4:{s:1:\"a\";i:29;s:1:\"b\";s:19:\"view_kitchen_orders\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:3:{i:0;i:1;i:1;i:2;i:2;i:4;}}i:29;a:4:{s:1:\"a\";i:30;s:1:\"b\";s:25:\"update_preparation_status\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:3:{i:0;i:1;i:1;i:2;i:2;i:4;}}i:30;a:4:{s:1:\"a\";i:31;s:1:\"b\";s:16:\"mark_items_ready\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:3:{i:0;i:1;i:1;i:2;i:2;i:4;}}i:31;a:4:{s:1:\"a\";i:32;s:1:\"b\";s:20:\"view_kitchen_display\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:2:{i:0;i:1;i:1;i:4;}}i:32;a:4:{s:1:\"a\";i:33;s:1:\"b\";s:22:\"manage_inventory_usage\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:2:{i:0;i:1;i:1;i:4;}}i:33;a:4:{s:1:\"a\";i:34;s:1:\"b\";s:20:\"view_delivery_orders\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:3:{i:0;i:1;i:1;i:2;i:2;i:5;}}i:34;a:4:{s:1:\"a\";i:35;s:1:\"b\";s:17:\"accept_deliveries\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:2:{i:0;i:1;i:1;i:5;}}i:35;a:4:{s:1:\"a\";i:36;s:1:\"b\";s:22:\"update_delivery_status\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:2:{i:0;i:1;i:1;i:5;}}i:36;a:4:{s:1:\"a\";i:37;s:1:\"b\";s:15:\"update_location\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:2:{i:0;i:1;i:1;i:5;}}i:37;a:4:{s:1:\"a\";i:38;s:1:\"b\";s:21:\"view_delivery_history\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:2:{i:0;i:1;i:1;i:5;}}i:38;a:4:{s:1:\"a\";i:39;s:1:\"b\";s:13:\"view_earnings\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:2:{i:0;i:1;i:1;i:5;}}i:39;a:4:{s:1:\"a\";i:40;s:1:\"b\";s:12:\"view_reviews\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:3:{i:0;i:1;i:1;i:2;i:2;i:3;}}i:40;a:4:{s:1:\"a\";i:41;s:1:\"b\";s:14:\"create_reviews\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:2:{i:0;i:1;i:1;i:3;}}i:41;a:4:{s:1:\"a\";i:42;s:1:\"b\";s:12:\"edit_reviews\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:1:{i:0;i:1;}}i:42;a:4:{s:1:\"a\";i:43;s:1:\"b\";s:14:\"delete_reviews\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:1:{i:0;i:1;}}i:43;a:4:{s:1:\"a\";i:44;s:1:\"b\";s:16:\"moderate_reviews\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:2:{i:0;i:1;i:1;i:2;}}}s:5:\"roles\";a:5:{i:0;a:3:{s:1:\"a\";i:1;s:1:\"b\";s:5:\"admin\";s:1:\"c\";s:3:\"web\";}i:1;a:3:{s:1:\"a\";i:2;s:1:\"b\";s:18:\"restaurant_manager\";s:1:\"c\";s:3:\"web\";}i:2;a:3:{s:1:\"a\";i:3;s:1:\"b\";s:6:\"waiter\";s:1:\"c\";s:3:\"web\";}i:3;a:3:{s:1:\"a\";i:4;s:1:\"b\";s:7:\"kitchen\";s:1:\"c\";s:3:\"web\";}i:4;a:3:{s:1:\"a\";i:5;s:1:\"b\";s:8:\"delivery\";s:1:\"c\";s:3:\"web\";}}}', 1751344982);

-- --------------------------------------------------------

--
-- Table structure for table `cache_locks`
--

CREATE TABLE `cache_locks` (
  `key` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `owner` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `expiration` int NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `combo_menu_media`
--

CREATE TABLE `combo_menu_media` (
  `id` bigint UNSIGNED NOT NULL,
  `combo_menu_id` bigint UNSIGNED NOT NULL,
  `media_id` bigint UNSIGNED NOT NULL,
  `sort_order` int NOT NULL DEFAULT '0',
  `is_primary` tinyint(1) NOT NULL DEFAULT '0',
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `domains`
--

CREATE TABLE `domains` (
  `id` int UNSIGNED NOT NULL,
  `domain` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `tenant_id` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `domains`
--

INSERT INTO `domains` (`id`, `domain`, `tenant_id`, `created_at`, `updated_at`) VALUES
(1, 'demo-restaurant.localhost', 'demo-restaurant', '2025-06-09 11:19:45', '2025-06-09 11:19:45'),
(2, 'khanas.localhost', 'khanas', '2025-06-09 11:27:58', '2025-06-09 11:27:58'),
(3, 'king.localhost', 'king', '2025-06-10 23:08:08', '2025-06-10 23:08:08');

-- --------------------------------------------------------

--
-- Table structure for table `failed_jobs`
--

CREATE TABLE `failed_jobs` (
  `id` bigint UNSIGNED NOT NULL,
  `uuid` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `connection` text COLLATE utf8mb4_unicode_ci NOT NULL,
  `queue` text COLLATE utf8mb4_unicode_ci NOT NULL,
  `payload` longtext COLLATE utf8mb4_unicode_ci NOT NULL,
  `exception` longtext COLLATE utf8mb4_unicode_ci NOT NULL,
  `failed_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `failed_jobs`
--

INSERT INTO `failed_jobs` (`id`, `uuid`, `connection`, `queue`, `payload`, `exception`, `failed_at`) VALUES
(1, 'd3bf82dc-ede3-4feb-9721-c2dd70b9a2b8', 'database', 'tenant-creation', '{\"uuid\":\"d3bf82dc-ede3-4feb-9721-c2dd70b9a2b8\",\"displayName\":\"App\\\\Jobs\\\\Tenant\\\\SimpleTenantSetupJob\",\"job\":\"Illuminate\\\\Queue\\\\CallQueuedHandler@call\",\"maxTries\":1,\"maxExceptions\":null,\"failOnTimeout\":false,\"backoff\":null,\"timeout\":1800,\"retryUntil\":null,\"data\":{\"commandName\":\"App\\\\Jobs\\\\Tenant\\\\SimpleTenantSetupJob\",\"command\":\"O:36:\\\"App\\\\Jobs\\\\Tenant\\\\SimpleTenantSetupJob\\\":3:{s:6:\\\"tenant\\\";O:45:\\\"Illuminate\\\\Contracts\\\\Database\\\\ModelIdentifier\\\":5:{s:5:\\\"class\\\";s:17:\\\"App\\\\Models\\\\Tenant\\\";s:2:\\\"id\\\";s:6:\\\"khanas\\\";s:9:\\\"relations\\\";a:0:{}s:10:\\\"connection\\\";s:5:\\\"mysql\\\";s:15:\\\"collectionClass\\\";N;}s:15:\\\"managerPassword\\\";s:8:\\\"12345678\\\";s:5:\\\"queue\\\";s:15:\\\"tenant-creation\\\";}\"},\"createdAt\":1749490078,\"delay\":null}', 'Exception: Final checks failed: No categories found in tenant database in /home/<USER>/Desktop/laravel/restaurant/backend/app/Jobs/Tenant/FinalizeTenantSetupJob.php:95\nStack trace:\n#0 /home/<USER>/Desktop/laravel/restaurant/backend/app/Jobs/Tenant/FinalizeTenantSetupJob.php(44): App\\Jobs\\Tenant\\FinalizeTenantSetupJob->performFinalChecks()\n#1 /home/<USER>/Desktop/laravel/restaurant/backend/app/Jobs/Tenant/SimpleTenantSetupJob.php(87): App\\Jobs\\Tenant\\FinalizeTenantSetupJob->handle()\n#2 /home/<USER>/Desktop/laravel/restaurant/backend/app/Jobs/Tenant/SimpleTenantSetupJob.php(120): App\\Jobs\\Tenant\\SimpleTenantSetupJob->App\\Jobs\\Tenant\\{closure}()\n#3 /home/<USER>/Desktop/laravel/restaurant/backend/app/Jobs/Tenant/SimpleTenantSetupJob.php(85): App\\Jobs\\Tenant\\SimpleTenantSetupJob->runStep()\n#4 /home/<USER>/Desktop/laravel/restaurant/backend/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(36): App\\Jobs\\Tenant\\SimpleTenantSetupJob->handle()\n#5 /home/<USER>/Desktop/laravel/restaurant/backend/vendor/laravel/framework/src/Illuminate/Container/Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()\n#6 /home/<USER>/Desktop/laravel/restaurant/backend/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure()\n#7 /home/<USER>/Desktop/laravel/restaurant/backend/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod()\n#8 /home/<USER>/Desktop/laravel/restaurant/backend/vendor/laravel/framework/src/Illuminate/Container/Container.php(754): Illuminate\\Container\\BoundMethod::call()\n#9 /home/<USER>/Desktop/laravel/restaurant/backend/vendor/laravel/framework/src/Illuminate/Bus/Dispatcher.php(132): Illuminate\\Container\\Container->call()\n#10 /home/<USER>/Desktop/laravel/restaurant/backend/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(169): Illuminate\\Bus\\Dispatcher->Illuminate\\Bus\\{closure}()\n#11 /home/<USER>/Desktop/laravel/restaurant/backend/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()\n#12 /home/<USER>/Desktop/laravel/restaurant/backend/vendor/laravel/framework/src/Illuminate/Bus/Dispatcher.php(136): Illuminate\\Pipeline\\Pipeline->then()\n#13 /home/<USER>/Desktop/laravel/restaurant/backend/vendor/laravel/framework/src/Illuminate/Queue/CallQueuedHandler.php(125): Illuminate\\Bus\\Dispatcher->dispatchNow()\n#14 /home/<USER>/Desktop/laravel/restaurant/backend/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(169): Illuminate\\Queue\\CallQueuedHandler->Illuminate\\Queue\\{closure}()\n#15 /home/<USER>/Desktop/laravel/restaurant/backend/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()\n#16 /home/<USER>/Desktop/laravel/restaurant/backend/vendor/laravel/framework/src/Illuminate/Queue/CallQueuedHandler.php(120): Illuminate\\Pipeline\\Pipeline->then()\n#17 /home/<USER>/Desktop/laravel/restaurant/backend/vendor/laravel/framework/src/Illuminate/Queue/CallQueuedHandler.php(68): Illuminate\\Queue\\CallQueuedHandler->dispatchThroughMiddleware()\n#18 /home/<USER>/Desktop/laravel/restaurant/backend/vendor/laravel/framework/src/Illuminate/Queue/Jobs/Job.php(102): Illuminate\\Queue\\CallQueuedHandler->call()\n#19 /home/<USER>/Desktop/laravel/restaurant/backend/vendor/laravel/framework/src/Illuminate/Queue/Worker.php(441): Illuminate\\Queue\\Jobs\\Job->fire()\n#20 /home/<USER>/Desktop/laravel/restaurant/backend/vendor/laravel/framework/src/Illuminate/Queue/Worker.php(391): Illuminate\\Queue\\Worker->process()\n#21 /home/<USER>/Desktop/laravel/restaurant/backend/vendor/laravel/framework/src/Illuminate/Queue/Worker.php(177): Illuminate\\Queue\\Worker->runJob()\n#22 /home/<USER>/Desktop/laravel/restaurant/backend/vendor/laravel/framework/src/Illuminate/Queue/Console/WorkCommand.php(148): Illuminate\\Queue\\Worker->daemon()\n#23 /home/<USER>/Desktop/laravel/restaurant/backend/vendor/laravel/framework/src/Illuminate/Queue/Console/WorkCommand.php(131): Illuminate\\Queue\\Console\\WorkCommand->runWorker()\n#24 /home/<USER>/Desktop/laravel/restaurant/backend/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(36): Illuminate\\Queue\\Console\\WorkCommand->handle()\n#25 /home/<USER>/Desktop/laravel/restaurant/backend/vendor/laravel/framework/src/Illuminate/Container/Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()\n#26 /home/<USER>/Desktop/laravel/restaurant/backend/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure()\n#27 /home/<USER>/Desktop/laravel/restaurant/backend/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod()\n#28 /home/<USER>/Desktop/laravel/restaurant/backend/vendor/laravel/framework/src/Illuminate/Container/Container.php(754): Illuminate\\Container\\BoundMethod::call()\n#29 /home/<USER>/Desktop/laravel/restaurant/backend/vendor/laravel/framework/src/Illuminate/Console/Command.php(211): Illuminate\\Container\\Container->call()\n#30 /home/<USER>/Desktop/laravel/restaurant/backend/vendor/symfony/console/Command/Command.php(279): Illuminate\\Console\\Command->execute()\n#31 /home/<USER>/Desktop/laravel/restaurant/backend/vendor/laravel/framework/src/Illuminate/Console/Command.php(180): Symfony\\Component\\Console\\Command\\Command->run()\n#32 /home/<USER>/Desktop/laravel/restaurant/backend/vendor/symfony/console/Application.php(1094): Illuminate\\Console\\Command->run()\n#33 /home/<USER>/Desktop/laravel/restaurant/backend/vendor/symfony/console/Application.php(342): Symfony\\Component\\Console\\Application->doRunCommand()\n#34 /home/<USER>/Desktop/laravel/restaurant/backend/vendor/symfony/console/Application.php(193): Symfony\\Component\\Console\\Application->doRun()\n#35 /home/<USER>/Desktop/laravel/restaurant/backend/vendor/laravel/framework/src/Illuminate/Foundation/Console/Kernel.php(197): Symfony\\Component\\Console\\Application->run()\n#36 /home/<USER>/Desktop/laravel/restaurant/backend/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(1234): Illuminate\\Foundation\\Console\\Kernel->handle()\n#37 /home/<USER>/Desktop/laravel/restaurant/backend/artisan(16): Illuminate\\Foundation\\Application->handleCommand()\n#38 {main}\n\nNext Exception: Step \'Setup Finalization\' failed: Final checks failed: No categories found in tenant database in /home/<USER>/Desktop/laravel/restaurant/backend/app/Jobs/Tenant/SimpleTenantSetupJob.php:124\nStack trace:\n#0 /home/<USER>/Desktop/laravel/restaurant/backend/app/Jobs/Tenant/SimpleTenantSetupJob.php(85): App\\Jobs\\Tenant\\SimpleTenantSetupJob->runStep()\n#1 /home/<USER>/Desktop/laravel/restaurant/backend/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(36): App\\Jobs\\Tenant\\SimpleTenantSetupJob->handle()\n#2 /home/<USER>/Desktop/laravel/restaurant/backend/vendor/laravel/framework/src/Illuminate/Container/Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()\n#3 /home/<USER>/Desktop/laravel/restaurant/backend/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure()\n#4 /home/<USER>/Desktop/laravel/restaurant/backend/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod()\n#5 /home/<USER>/Desktop/laravel/restaurant/backend/vendor/laravel/framework/src/Illuminate/Container/Container.php(754): Illuminate\\Container\\BoundMethod::call()\n#6 /home/<USER>/Desktop/laravel/restaurant/backend/vendor/laravel/framework/src/Illuminate/Bus/Dispatcher.php(132): Illuminate\\Container\\Container->call()\n#7 /home/<USER>/Desktop/laravel/restaurant/backend/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(169): Illuminate\\Bus\\Dispatcher->Illuminate\\Bus\\{closure}()\n#8 /home/<USER>/Desktop/laravel/restaurant/backend/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()\n#9 /home/<USER>/Desktop/laravel/restaurant/backend/vendor/laravel/framework/src/Illuminate/Bus/Dispatcher.php(136): Illuminate\\Pipeline\\Pipeline->then()\n#10 /home/<USER>/Desktop/laravel/restaurant/backend/vendor/laravel/framework/src/Illuminate/Queue/CallQueuedHandler.php(125): Illuminate\\Bus\\Dispatcher->dispatchNow()\n#11 /home/<USER>/Desktop/laravel/restaurant/backend/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(169): Illuminate\\Queue\\CallQueuedHandler->Illuminate\\Queue\\{closure}()\n#12 /home/<USER>/Desktop/laravel/restaurant/backend/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()\n#13 /home/<USER>/Desktop/laravel/restaurant/backend/vendor/laravel/framework/src/Illuminate/Queue/CallQueuedHandler.php(120): Illuminate\\Pipeline\\Pipeline->then()\n#14 /home/<USER>/Desktop/laravel/restaurant/backend/vendor/laravel/framework/src/Illuminate/Queue/CallQueuedHandler.php(68): Illuminate\\Queue\\CallQueuedHandler->dispatchThroughMiddleware()\n#15 /home/<USER>/Desktop/laravel/restaurant/backend/vendor/laravel/framework/src/Illuminate/Queue/Jobs/Job.php(102): Illuminate\\Queue\\CallQueuedHandler->call()\n#16 /home/<USER>/Desktop/laravel/restaurant/backend/vendor/laravel/framework/src/Illuminate/Queue/Worker.php(441): Illuminate\\Queue\\Jobs\\Job->fire()\n#17 /home/<USER>/Desktop/laravel/restaurant/backend/vendor/laravel/framework/src/Illuminate/Queue/Worker.php(391): Illuminate\\Queue\\Worker->process()\n#18 /home/<USER>/Desktop/laravel/restaurant/backend/vendor/laravel/framework/src/Illuminate/Queue/Worker.php(177): Illuminate\\Queue\\Worker->runJob()\n#19 /home/<USER>/Desktop/laravel/restaurant/backend/vendor/laravel/framework/src/Illuminate/Queue/Console/WorkCommand.php(148): Illuminate\\Queue\\Worker->daemon()\n#20 /home/<USER>/Desktop/laravel/restaurant/backend/vendor/laravel/framework/src/Illuminate/Queue/Console/WorkCommand.php(131): Illuminate\\Queue\\Console\\WorkCommand->runWorker()\n#21 /home/<USER>/Desktop/laravel/restaurant/backend/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(36): Illuminate\\Queue\\Console\\WorkCommand->handle()\n#22 /home/<USER>/Desktop/laravel/restaurant/backend/vendor/laravel/framework/src/Illuminate/Container/Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()\n#23 /home/<USER>/Desktop/laravel/restaurant/backend/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure()\n#24 /home/<USER>/Desktop/laravel/restaurant/backend/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod()\n#25 /home/<USER>/Desktop/laravel/restaurant/backend/vendor/laravel/framework/src/Illuminate/Container/Container.php(754): Illuminate\\Container\\BoundMethod::call()\n#26 /home/<USER>/Desktop/laravel/restaurant/backend/vendor/laravel/framework/src/Illuminate/Console/Command.php(211): Illuminate\\Container\\Container->call()\n#27 /home/<USER>/Desktop/laravel/restaurant/backend/vendor/symfony/console/Command/Command.php(279): Illuminate\\Console\\Command->execute()\n#28 /home/<USER>/Desktop/laravel/restaurant/backend/vendor/laravel/framework/src/Illuminate/Console/Command.php(180): Symfony\\Component\\Console\\Command\\Command->run()\n#29 /home/<USER>/Desktop/laravel/restaurant/backend/vendor/symfony/console/Application.php(1094): Illuminate\\Console\\Command->run()\n#30 /home/<USER>/Desktop/laravel/restaurant/backend/vendor/symfony/console/Application.php(342): Symfony\\Component\\Console\\Application->doRunCommand()\n#31 /home/<USER>/Desktop/laravel/restaurant/backend/vendor/symfony/console/Application.php(193): Symfony\\Component\\Console\\Application->doRun()\n#32 /home/<USER>/Desktop/laravel/restaurant/backend/vendor/laravel/framework/src/Illuminate/Foundation/Console/Kernel.php(197): Symfony\\Component\\Console\\Application->run()\n#33 /home/<USER>/Desktop/laravel/restaurant/backend/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(1234): Illuminate\\Foundation\\Console\\Kernel->handle()\n#34 /home/<USER>/Desktop/laravel/restaurant/backend/artisan(16): Illuminate\\Foundation\\Application->handleCommand()\n#35 {main}', '2025-06-09 11:28:08');

-- --------------------------------------------------------

--
-- Table structure for table `jobs`
--

CREATE TABLE `jobs` (
  `id` bigint UNSIGNED NOT NULL,
  `queue` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `payload` longtext COLLATE utf8mb4_unicode_ci NOT NULL,
  `attempts` tinyint UNSIGNED NOT NULL,
  `reserved_at` int UNSIGNED DEFAULT NULL,
  `available_at` int UNSIGNED NOT NULL,
  `created_at` int UNSIGNED NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `jobs`
--

INSERT INTO `jobs` (`id`, `queue`, `payload`, `attempts`, `reserved_at`, `available_at`, `created_at`) VALUES
(2, 'notifications', '{\"uuid\":\"c4377f16-0a15-46c3-b156-d8e74a322cc4\",\"displayName\":\"App\\\\Jobs\\\\Tenant\\\\NotifyTenantCreationFailedJob\",\"job\":\"Illuminate\\\\Queue\\\\CallQueuedHandler@call\",\"maxTries\":3,\"maxExceptions\":null,\"failOnTimeout\":false,\"backoff\":null,\"timeout\":60,\"retryUntil\":null,\"data\":{\"commandName\":\"App\\\\Jobs\\\\Tenant\\\\NotifyTenantCreationFailedJob\",\"command\":\"O:45:\\\"App\\\\Jobs\\\\Tenant\\\\NotifyTenantCreationFailedJob\\\":4:{s:6:\\\"tenant\\\";O:45:\\\"Illuminate\\\\Contracts\\\\Database\\\\ModelIdentifier\\\":5:{s:5:\\\"class\\\";s:17:\\\"App\\\\Models\\\\Tenant\\\";s:2:\\\"id\\\";s:6:\\\"khanas\\\";s:9:\\\"relations\\\";a:1:{i:0;s:16:\\\"subscriptionPlan\\\";}s:10:\\\"connection\\\";s:5:\\\"mysql\\\";s:15:\\\"collectionClass\\\";N;}s:10:\\\"failedStep\\\";s:13:\\\"Setup Process\\\";s:12:\\\"errorMessage\\\";s:93:\\\"Step \'Setup Finalization\' failed: Final checks failed: No categories found in tenant database\\\";s:5:\\\"queue\\\";s:13:\\\"notifications\\\";}\"},\"createdAt\":1749490088,\"delay\":null}', 0, NULL, 1749490088, 1749490088),
(3, 'notifications', '{\"uuid\":\"5258696e-7eb3-4853-a110-be889fdfd9ed\",\"displayName\":\"App\\\\Jobs\\\\Tenant\\\\NotifyTenantCreationFailedJob\",\"job\":\"Illuminate\\\\Queue\\\\CallQueuedHandler@call\",\"maxTries\":3,\"maxExceptions\":null,\"failOnTimeout\":false,\"backoff\":null,\"timeout\":60,\"retryUntil\":null,\"data\":{\"commandName\":\"App\\\\Jobs\\\\Tenant\\\\NotifyTenantCreationFailedJob\",\"command\":\"O:45:\\\"App\\\\Jobs\\\\Tenant\\\\NotifyTenantCreationFailedJob\\\":4:{s:6:\\\"tenant\\\";O:45:\\\"Illuminate\\\\Contracts\\\\Database\\\\ModelIdentifier\\\":5:{s:5:\\\"class\\\";s:17:\\\"App\\\\Models\\\\Tenant\\\";s:2:\\\"id\\\";s:6:\\\"khanas\\\";s:9:\\\"relations\\\";a:0:{}s:10:\\\"connection\\\";s:5:\\\"mysql\\\";s:15:\\\"collectionClass\\\";N;}s:10:\\\"failedStep\\\";s:22:\\\"Complete Setup Process\\\";s:12:\\\"errorMessage\\\";s:93:\\\"Step \'Setup Finalization\' failed: Final checks failed: No categories found in tenant database\\\";s:5:\\\"queue\\\";s:13:\\\"notifications\\\";}\"},\"createdAt\":1749490088,\"delay\":null}', 0, NULL, 1749490088, 1749490088),
(4, 'tenant-creation', '{\"uuid\":\"1a6e62b3-0844-44bb-9b86-702ba0f9637a\",\"displayName\":\"App\\\\Jobs\\\\Tenant\\\\SimpleTenantSetupJob\",\"job\":\"Illuminate\\\\Queue\\\\CallQueuedHandler@call\",\"maxTries\":1,\"maxExceptions\":null,\"failOnTimeout\":false,\"backoff\":null,\"timeout\":1800,\"retryUntil\":null,\"data\":{\"commandName\":\"App\\\\Jobs\\\\Tenant\\\\SimpleTenantSetupJob\",\"command\":\"O:36:\\\"App\\\\Jobs\\\\Tenant\\\\SimpleTenantSetupJob\\\":3:{s:6:\\\"tenant\\\";O:45:\\\"Illuminate\\\\Contracts\\\\Database\\\\ModelIdentifier\\\":5:{s:5:\\\"class\\\";s:17:\\\"App\\\\Models\\\\Tenant\\\";s:2:\\\"id\\\";s:4:\\\"king\\\";s:9:\\\"relations\\\";a:0:{}s:10:\\\"connection\\\";s:5:\\\"mysql\\\";s:15:\\\"collectionClass\\\";N;}s:15:\\\"managerPassword\\\";s:8:\\\"12345678\\\";s:5:\\\"queue\\\";s:15:\\\"tenant-creation\\\";}\"},\"createdAt\":1749618488,\"delay\":null}', 0, NULL, 1749618488, 1749618488);

-- --------------------------------------------------------

--
-- Table structure for table `job_batches`
--

CREATE TABLE `job_batches` (
  `id` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `name` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `total_jobs` int NOT NULL,
  `pending_jobs` int NOT NULL,
  `failed_jobs` int NOT NULL,
  `failed_job_ids` longtext COLLATE utf8mb4_unicode_ci NOT NULL,
  `options` mediumtext COLLATE utf8mb4_unicode_ci,
  `cancelled_at` int DEFAULT NULL,
  `created_at` int NOT NULL,
  `finished_at` int DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `loyalty_accounts`
--

CREATE TABLE `loyalty_accounts` (
  `id` bigint UNSIGNED NOT NULL,
  `phone_number` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `customer_name` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `address` text COLLATE utf8mb4_unicode_ci,
  `email` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `date_of_birth` date DEFAULT NULL,
  `total_points` int NOT NULL DEFAULT '0',
  `lifetime_points_earned` int NOT NULL DEFAULT '0',
  `lifetime_points_redeemed` int NOT NULL DEFAULT '0',
  `tier` enum('regular','bronze','silver','gold','platinum') COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'regular',
  `total_spent` decimal(12,2) NOT NULL DEFAULT '0.00',
  `total_orders` int NOT NULL DEFAULT '0',
  `last_transaction_at` timestamp NULL DEFAULT NULL,
  `last_order_at` timestamp NULL DEFAULT NULL,
  `is_active` tinyint(1) NOT NULL DEFAULT '1',
  `notes` text COLLATE utf8mb4_unicode_ci,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `loyalty_settings`
--

CREATE TABLE `loyalty_settings` (
  `id` bigint UNSIGNED NOT NULL,
  `enabled` tinyint(1) NOT NULL DEFAULT '0',
  `points_per_dollar` decimal(8,2) NOT NULL DEFAULT '1.00',
  `points_for_dollar_discount` int NOT NULL DEFAULT '100',
  `max_discount_percentage` decimal(5,2) NOT NULL DEFAULT '20.00',
  `minimum_order_amount` decimal(8,2) NOT NULL DEFAULT '0.00',
  `points_expiry_days` int DEFAULT NULL,
  `birthday_bonus_enabled` tinyint(1) NOT NULL DEFAULT '0',
  `birthday_bonus_points` int NOT NULL DEFAULT '0',
  `referral_program_enabled` tinyint(1) NOT NULL DEFAULT '0',
  `referral_bonus_points` int NOT NULL DEFAULT '0',
  `tier_settings` json DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `media`
--

CREATE TABLE `media` (
  `id` bigint UNSIGNED NOT NULL,
  `name` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `file_name` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `mime_type` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `disk` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'public',
  `collection_name` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `size` bigint UNSIGNED NOT NULL,
  `manipulations` json DEFAULT NULL,
  `custom_properties` json DEFAULT NULL,
  `generated_conversions` json DEFAULT NULL,
  `responsive_images` json DEFAULT NULL,
  `order_column` int UNSIGNED DEFAULT NULL,
  `model_type` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `model_id` char(36) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `uuid` char(36) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `conversions_disk` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `alt_text` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `caption` text COLLATE utf8mb4_unicode_ci,
  `tags` json DEFAULT NULL,
  `width` int DEFAULT NULL,
  `height` int DEFAULT NULL,
  `folder` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `is_featured` tinyint(1) NOT NULL DEFAULT '0',
  `uploaded_at` timestamp NULL DEFAULT NULL,
  `uploaded_by` bigint UNSIGNED DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `migrations`
--

CREATE TABLE `migrations` (
  `id` int UNSIGNED NOT NULL,
  `migration` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `batch` int NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `migrations`
--

INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES
(1, '0001_01_01_000000_create_users_table', 1),
(2, '0001_01_01_000001_create_cache_table', 1),
(3, '0001_01_01_000002_create_jobs_table', 1),
(4, '2019_09_15_000010_create_tenants_table', 1),
(5, '2019_09_15_000020_create_domains_table', 1),
(6, '2020_05_15_000010_create_tenant_user_impersonation_tokens_table', 1),
(7, '2025_05_28_090611_add_two_factor_columns_to_users_table', 1),
(8, '2025_05_28_090658_create_personal_access_tokens_table', 1),
(9, '2025_05_28_090659_create_teams_table', 1),
(10, '2025_05_28_090700_create_team_user_table', 1),
(11, '2025_05_28_090701_create_team_invitations_table', 1),
(12, '2025_05_28_091747_create_permission_tables', 1),
(13, '2025_05_28_100000_create_subscription_plans_table', 1),
(14, '2025_05_28_100001_create_payments_table', 1),
(15, '2025_05_28_100002_add_foreign_keys_to_tenants_table', 1),
(16, '2025_05_28_150310_add_role_fields_to_users_table', 1),
(17, '2025_05_28_150320_add_employee_fields_to_users_table', 1),
(18, '2025_06_02_145134_create_tenant_subscriptions_table', 1),
(19, '2025_06_02_145211_create_subscription_usage_table', 1),
(20, '2025_06_02_145244_update_subscription_plans_for_bdt_pricing', 1),
(21, '2025_06_02_171425_create_media_table', 1),
(22, '2025_06_02_173845_add_is_system_admin_to_users_table', 1),
(23, '2025_06_03_160000_create_and_fix_blogs_table', 1),
(24, '2025_06_03_161000_fix_blogs_table_structure', 1),
(25, '2025_06_03_162000_add_missing_blog_columns', 1),
(26, '2025_06_06_200000_add_setup_status_to_tenants_table', 1),
(27, '2025_06_06_220000_add_subscription_started_at_to_tenants_table', 1),
(28, '2024_12_02_100001_create_loyalty_settings_table', 2),
(29, '2024_12_02_100002_create_loyalty_accounts_table', 2),
(30, '2024_12_02_200001_create_riders_table', 2),
(31, '2025_01_14_100005_create_staff_table', 2),
(32, '2025_01_14_400001_create_branches_table', 2);

-- --------------------------------------------------------

--
-- Table structure for table `model_has_permissions`
--

CREATE TABLE `model_has_permissions` (
  `permission_id` bigint UNSIGNED NOT NULL,
  `model_type` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `model_id` bigint UNSIGNED NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `model_has_roles`
--

CREATE TABLE `model_has_roles` (
  `role_id` bigint UNSIGNED NOT NULL,
  `model_type` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `model_id` bigint UNSIGNED NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `model_has_roles`
--

INSERT INTO `model_has_roles` (`role_id`, `model_type`, `model_id`) VALUES
(1, 'App\\Models\\User', 1),
(2, 'App\\Models\\User', 2),
(3, 'App\\Models\\User', 3),
(4, 'App\\Models\\User', 4),
(5, 'App\\Models\\User', 5),
(3, 'App\\Models\\User', 6),
(3, 'App\\Models\\User', 7),
(3, 'App\\Models\\User', 8),
(4, 'App\\Models\\User', 9),
(4, 'App\\Models\\User', 10),
(5, 'App\\Models\\User', 11),
(5, 'App\\Models\\User', 12);

-- --------------------------------------------------------

--
-- Table structure for table `password_reset_tokens`
--

CREATE TABLE `password_reset_tokens` (
  `email` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `token` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `payments`
--

CREATE TABLE `payments` (
  `id` bigint UNSIGNED NOT NULL,
  `tenant_id` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `subscription_plan_id` bigint UNSIGNED NOT NULL,
  `amount` decimal(10,2) NOT NULL,
  `currency` varchar(3) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'USD',
  `payment_method` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `payment_gateway` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `transaction_id` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `gateway_response` json DEFAULT NULL,
  `status` enum('pending','completed','failed','cancelled') COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'pending',
  `paid_at` timestamp NULL DEFAULT NULL,
  `failed_at` timestamp NULL DEFAULT NULL,
  `failure_reason` text COLLATE utf8mb4_unicode_ci,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `permissions`
--

CREATE TABLE `permissions` (
  `id` bigint UNSIGNED NOT NULL,
  `name` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `guard_name` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `permissions`
--

INSERT INTO `permissions` (`id`, `name`, `guard_name`, `created_at`, `updated_at`) VALUES
(1, 'manage_tenants', 'web', '2025-06-09 11:18:57', '2025-06-09 11:18:57'),
(2, 'manage_subscriptions', 'web', '2025-06-09 11:18:57', '2025-06-09 11:18:57'),
(3, 'manage_payments', 'web', '2025-06-09 11:18:57', '2025-06-09 11:18:57'),
(4, 'view_admin_dashboard', 'web', '2025-06-09 11:18:57', '2025-06-09 11:18:57'),
(5, 'manage_system_settings', 'web', '2025-06-09 11:18:57', '2025-06-09 11:18:57'),
(6, 'manage_restaurant', 'web', '2025-06-09 11:18:57', '2025-06-09 11:18:57'),
(7, 'manage_menu', 'web', '2025-06-09 11:18:57', '2025-06-09 11:18:57'),
(8, 'manage_categories', 'web', '2025-06-09 11:18:57', '2025-06-09 11:18:57'),
(9, 'manage_subcategories', 'web', '2025-06-09 11:18:57', '2025-06-09 11:18:57'),
(10, 'manage_staff', 'web', '2025-06-09 11:18:57', '2025-06-09 11:18:57'),
(11, 'manage_branches', 'web', '2025-06-09 11:18:57', '2025-06-09 11:18:57'),
(12, 'manage_employees', 'web', '2025-06-09 11:18:57', '2025-06-09 11:18:57'),
(13, 'manage_employee_shifts', 'web', '2025-06-09 11:18:57', '2025-06-09 11:18:57'),
(14, 'view_reports', 'web', '2025-06-09 11:18:57', '2025-06-09 11:18:57'),
(15, 'manage_orders', 'web', '2025-06-09 11:18:57', '2025-06-09 11:18:57'),
(16, 'manage_customers', 'web', '2025-06-09 11:18:57', '2025-06-09 11:18:57'),
(17, 'manage_tables', 'web', '2025-06-09 11:18:57', '2025-06-09 11:18:57'),
(18, 'manage_reservations', 'web', '2025-06-09 11:18:57', '2025-06-09 11:18:57'),
(19, 'view_analytics', 'web', '2025-06-09 11:18:57', '2025-06-09 11:18:57'),
(20, 'manage_inventory', 'web', '2025-06-09 11:18:57', '2025-06-09 11:18:57'),
(21, 'manage_expenses', 'web', '2025-06-09 11:18:57', '2025-06-09 11:18:57'),
(22, 'manage_delivery_zones', 'web', '2025-06-09 11:18:57', '2025-06-09 11:18:57'),
(23, 'manage_promotions', 'web', '2025-06-09 11:18:57', '2025-06-09 11:18:57'),
(24, 'take_orders', 'web', '2025-06-09 11:18:57', '2025-06-09 11:18:57'),
(25, 'view_menu', 'web', '2025-06-09 11:18:57', '2025-06-09 11:18:57'),
(26, 'update_order_status', 'web', '2025-06-09 11:18:57', '2025-06-09 11:18:57'),
(27, 'view_customer_info', 'web', '2025-06-09 11:18:57', '2025-06-09 11:18:57'),
(28, 'process_payments', 'web', '2025-06-09 11:18:57', '2025-06-09 11:18:57'),
(29, 'view_kitchen_orders', 'web', '2025-06-09 11:18:57', '2025-06-09 11:18:57'),
(30, 'update_preparation_status', 'web', '2025-06-09 11:18:57', '2025-06-09 11:18:57'),
(31, 'mark_items_ready', 'web', '2025-06-09 11:18:57', '2025-06-09 11:18:57'),
(32, 'view_kitchen_display', 'web', '2025-06-09 11:18:57', '2025-06-09 11:18:57'),
(33, 'manage_inventory_usage', 'web', '2025-06-09 11:18:57', '2025-06-09 11:18:57'),
(34, 'view_delivery_orders', 'web', '2025-06-09 11:18:57', '2025-06-09 11:18:57'),
(35, 'accept_deliveries', 'web', '2025-06-09 11:18:57', '2025-06-09 11:18:57'),
(36, 'update_delivery_status', 'web', '2025-06-09 11:18:57', '2025-06-09 11:18:57'),
(37, 'update_location', 'web', '2025-06-09 11:18:57', '2025-06-09 11:18:57'),
(38, 'view_delivery_history', 'web', '2025-06-09 11:18:57', '2025-06-09 11:18:57'),
(39, 'view_earnings', 'web', '2025-06-09 11:18:57', '2025-06-09 11:18:57'),
(40, 'view_reviews', 'web', '2025-06-09 11:18:57', '2025-06-09 11:18:57'),
(41, 'create_reviews', 'web', '2025-06-09 11:18:57', '2025-06-09 11:18:57'),
(42, 'edit_reviews', 'web', '2025-06-09 11:18:57', '2025-06-09 11:18:57'),
(43, 'delete_reviews', 'web', '2025-06-09 11:18:57', '2025-06-09 11:18:57'),
(44, 'moderate_reviews', 'web', '2025-06-09 11:18:57', '2025-06-09 11:18:57');

-- --------------------------------------------------------

--
-- Table structure for table `personal_access_tokens`
--

CREATE TABLE `personal_access_tokens` (
  `id` bigint UNSIGNED NOT NULL,
  `tokenable_type` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `tokenable_id` bigint UNSIGNED NOT NULL,
  `name` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `token` varchar(64) COLLATE utf8mb4_unicode_ci NOT NULL,
  `abilities` text COLLATE utf8mb4_unicode_ci,
  `last_used_at` timestamp NULL DEFAULT NULL,
  `expires_at` timestamp NULL DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `riders`
--

CREATE TABLE `riders` (
  `id` bigint UNSIGNED NOT NULL,
  `user_id` bigint UNSIGNED DEFAULT NULL,
  `employee_id` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `name` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `phone` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `email` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `vehicle_type` enum('bike','car','scooter','bicycle') COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'bike',
  `license_number` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `status` enum('active','inactive','suspended') COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'active',
  `hire_date` date NOT NULL,
  `address` text COLLATE utf8mb4_unicode_ci,
  `profile_photo` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `delivery_status` enum('available','busy','offline','on_break') COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'offline',
  `current_delivery_count` int NOT NULL DEFAULT '0',
  `max_concurrent_deliveries` int NOT NULL DEFAULT '3',
  `current_latitude` decimal(10,8) DEFAULT NULL,
  `current_longitude` decimal(11,8) DEFAULT NULL,
  `last_location_update` timestamp NULL DEFAULT NULL,
  `total_deliveries` int NOT NULL DEFAULT '0',
  `average_rating` decimal(3,2) NOT NULL DEFAULT '5.00',
  `rating_count` int NOT NULL DEFAULT '0',
  `average_delivery_time` decimal(8,2) NOT NULL DEFAULT '0.00',
  `completed_orders_today` int NOT NULL DEFAULT '0',
  `total_earnings` decimal(10,2) NOT NULL DEFAULT '0.00',
  `commission_rate` decimal(5,2) NOT NULL DEFAULT '10.00',
  `shift_start_time` time DEFAULT NULL,
  `shift_end_time` time DEFAULT NULL,
  `working_days` json DEFAULT NULL,
  `emergency_contact_name` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `emergency_contact_phone` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `notes` text COLLATE utf8mb4_unicode_ci,
  `is_verified` tinyint(1) NOT NULL DEFAULT '0',
  `last_active_at` timestamp NULL DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `roles`
--

CREATE TABLE `roles` (
  `id` bigint UNSIGNED NOT NULL,
  `name` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `guard_name` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `roles`
--

INSERT INTO `roles` (`id`, `name`, `guard_name`, `created_at`, `updated_at`) VALUES
(1, 'admin', 'web', '2025-06-09 11:18:57', '2025-06-09 11:18:57'),
(2, 'restaurant_manager', 'web', '2025-06-09 11:18:57', '2025-06-09 11:18:57'),
(3, 'waiter', 'web', '2025-06-09 11:18:57', '2025-06-09 11:18:57'),
(4, 'kitchen', 'web', '2025-06-09 11:18:57', '2025-06-09 11:18:57'),
(5, 'delivery', 'web', '2025-06-09 11:18:57', '2025-06-09 11:18:57');

-- --------------------------------------------------------

--
-- Table structure for table `role_has_permissions`
--

CREATE TABLE `role_has_permissions` (
  `permission_id` bigint UNSIGNED NOT NULL,
  `role_id` bigint UNSIGNED NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `role_has_permissions`
--

INSERT INTO `role_has_permissions` (`permission_id`, `role_id`) VALUES
(1, 1),
(2, 1),
(3, 1),
(4, 1),
(5, 1),
(6, 1),
(7, 1),
(8, 1),
(9, 1),
(10, 1),
(11, 1),
(12, 1),
(13, 1),
(14, 1),
(15, 1),
(16, 1),
(17, 1),
(18, 1),
(19, 1),
(20, 1),
(21, 1),
(22, 1),
(23, 1),
(24, 1),
(25, 1),
(26, 1),
(27, 1),
(28, 1),
(29, 1),
(30, 1),
(31, 1),
(32, 1),
(33, 1),
(34, 1),
(35, 1),
(36, 1),
(37, 1),
(38, 1),
(39, 1),
(40, 1),
(41, 1),
(42, 1),
(43, 1),
(44, 1),
(6, 2),
(7, 2),
(8, 2),
(9, 2),
(10, 2),
(11, 2),
(12, 2),
(13, 2),
(14, 2),
(15, 2),
(16, 2),
(17, 2),
(18, 2),
(19, 2),
(20, 2),
(21, 2),
(22, 2),
(23, 2),
(24, 2),
(25, 2),
(26, 2),
(27, 2),
(28, 2),
(29, 2),
(30, 2),
(31, 2),
(34, 2),
(40, 2),
(44, 2),
(17, 3),
(18, 3),
(24, 3),
(25, 3),
(26, 3),
(27, 3),
(28, 3),
(40, 3),
(41, 3),
(25, 4),
(29, 4),
(30, 4),
(31, 4),
(32, 4),
(33, 4),
(34, 5),
(35, 5),
(36, 5),
(37, 5),
(38, 5),
(39, 5);

-- --------------------------------------------------------

--
-- Table structure for table `sessions`
--

CREATE TABLE `sessions` (
  `id` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `user_id` bigint UNSIGNED DEFAULT NULL,
  `ip_address` varchar(45) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `user_agent` text COLLATE utf8mb4_unicode_ci,
  `payload` longtext COLLATE utf8mb4_unicode_ci NOT NULL,
  `last_activity` int NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `sessions`
--

INSERT INTO `sessions` (`id`, `user_id`, `ip_address`, `user_agent`, `payload`, `last_activity`) VALUES
('bVJvjFoPrlfFfJNV0NuUsQOAP1qVUEzhAwIlPFVF', 1, '127.0.0.1', 'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'YTo1OntzOjY6Il90b2tlbiI7czo0MDoiRlBxRTBzdlNGOW11Z04yOEdXSUFPekU2dVVod3pUTzRMdjNnWXdadCI7czo2OiJsb2NhbGUiO3M6MjoiZW4iO3M6OToiX3ByZXZpb3VzIjthOjE6e3M6MzoidXJsIjtzOjMxOiJodHRwOi8vbG9jYWxob3N0OjgwMDAvZGFzaGJvYXJkIjt9czo2OiJfZmxhc2giO2E6Mjp7czozOiJvbGQiO2E6MDp7fXM6MzoibmV3IjthOjA6e319czo1MDoibG9naW5fd2ViXzU5YmEzNmFkZGMyYjJmOTQwMTU4MGYwMTRjN2Y1OGVhNGUzMDk4OWQiO2k6MTt9', 1749528868),
('Erzi4R9SMEqNRfZPfTmWIAEaXuMweNr8qB9VEONv', 1, '127.0.0.1', 'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'YTo3OntzOjY6Il90b2tlbiI7czo0MDoiaW9PV2JKd21jaXBJelF2VUo5Q2lxcXRnNHhoRzZFV2tZUUd1Q05tWCI7czo2OiJsb2NhbGUiO3M6MjoiZW4iO3M6OToiX3ByZXZpb3VzIjthOjE6e3M6MzoidXJsIjtzOjM1OiJodHRwOi8vbG9jYWxob3N0OjgwMDAvYWRtaW4vdGVuYW50cyI7fXM6NjoiX2ZsYXNoIjthOjI6e3M6Mzoib2xkIjthOjA6e31zOjM6Im5ldyI7YTowOnt9fXM6NTA6ImxvZ2luX3dlYl81OWJhMzZhZGRjMmIyZjk0MDE1ODBmMDE0YzdmNThlYTRlMzA5ODlkIjtpOjE7czoyMToicGFzc3dvcmRfaGFzaF9zYW5jdHVtIjtzOjYwOiIkMnkkMTIkRzF4c3dRMUN6LzdCL1pvbkE3QTFwZURzcUpKcVpIT0RrVVIvMW9ZWXo1V09XcHkwWVAxSzYiO3M6MTk6InR3b19mYWN0b3JfZW1wdHlfYXQiO2k6MTc1MTI1ODU5Njt9', 1751258648),
('L5xo7aNNjz5H6FveQaD1YQgUsQNSJSO3ivCsuX4s', 1, '127.0.0.1', 'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'YTo2OntzOjY6Il90b2tlbiI7czo0MDoiSWJUd0NlMnVMT0xDcm4yTGlJUXZhNk9JdGlGMmJuWEhuWTNaRURSdiI7czo2OiJsb2NhbGUiO3M6MjoiZW4iO3M6OToiX3ByZXZpb3VzIjthOjE6e3M6MzoidXJsIjtzOjM1OiJodHRwOi8vbG9jYWxob3N0OjgwMDAvYWRtaW4vdGVuYW50cyI7fXM6NjoiX2ZsYXNoIjthOjI6e3M6Mzoib2xkIjthOjA6e31zOjM6Im5ldyI7YTowOnt9fXM6NTA6ImxvZ2luX3dlYl81OWJhMzZhZGRjMmIyZjk0MDE1ODBmMDE0YzdmNThlYTRlMzA5ODlkIjtpOjE7czoyMToicGFzc3dvcmRfaGFzaF9zYW5jdHVtIjtzOjYwOiIkMnkkMTIkRzF4c3dRMUN6LzdCL1pvbkE3QTFwZURzcUpKcVpIT0RrVVIvMW9ZWXo1V09XcHkwWVAxSzYiO30=', 1749618488),
('N2IhMG6jTSUrctIMIrW7zfZFh8alODEFvDJ9tgdK', 1, '127.0.0.1', 'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'YTo1OntzOjY6Il90b2tlbiI7czo0MDoiTzBkNUlOVzM1TXFBSE5MNlg4dXlPcUtaU2wySG5adDNURnNEU3VMSSI7czo2OiJsb2NhbGUiO3M6MjoiZW4iO3M6OToiX3ByZXZpb3VzIjthOjE6e3M6MzoidXJsIjtzOjI4OiJodHRwOi8vbG9jYWxob3N0OjgwMDAvc2lnbmluIjt9czo2OiJfZmxhc2giO2E6Mjp7czozOiJvbGQiO2E6MDp7fXM6MzoibmV3IjthOjA6e319czo1MDoibG9naW5fd2ViXzU5YmEzNmFkZGMyYjJmOTQwMTU4MGYwMTRjN2Y1OGVhNGUzMDk4OWQiO2k6MTt9', 1749651229),
('x9vETg5acpjMsaVMdESkQbK2mA7bLdvSrn62dp0D', NULL, '127.0.0.1', 'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'YTo0OntzOjY6Il90b2tlbiI7czo0MDoiUmI0cEJzUXJiUTNLeXVyRWIyUXZoRGhnYzFJRjhJSE5pNkJmVlg3ZSI7czo2OiJsb2NhbGUiO3M6MjoiZW4iO3M6OToiX3ByZXZpb3VzIjthOjE6e3M6MzoidXJsIjtzOjE5OiJodHRwOi8vYmFja2VuZC50ZXN0Ijt9czo2OiJfZmxhc2giO2E6Mjp7czozOiJvbGQiO2E6MDp7fXM6MzoibmV3IjthOjA6e319fQ==', 1749528485),
('zFk9vK0LDrM8nep5ez0AI0CjHqnwftluK2uYLIZ5', 1, '127.0.0.1', 'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'YTo2OntzOjY6Il90b2tlbiI7czo0MDoiWXJkZ05sZFhKVUVJS29IZ3NzYllJeHVlT3E4YmFUNDJLV2RKYkNGRCI7czo2OiJsb2NhbGUiO3M6MjoiZW4iO3M6OToiX3ByZXZpb3VzIjthOjE6e3M6MzoidXJsIjtzOjM1OiJodHRwOi8vbG9jYWxob3N0OjgwMDAvYWRtaW4vdGVuYW50cyI7fXM6NjoiX2ZsYXNoIjthOjI6e3M6Mzoib2xkIjthOjA6e31zOjM6Im5ldyI7YTowOnt9fXM6NTA6ImxvZ2luX3dlYl81OWJhMzZhZGRjMmIyZjk0MDE1ODBmMDE0YzdmNThlYTRlMzA5ODlkIjtpOjE7czoyMToicGFzc3dvcmRfaGFzaF9zYW5jdHVtIjtzOjYwOiIkMnkkMTIkRzF4c3dRMUN6LzdCL1pvbkE3QTFwZURzcUpKcVpIT0RrVVIvMW9ZWXo1V09XcHkwWVAxSzYiO30=', 1749492360);

-- --------------------------------------------------------

--
-- Table structure for table `staff`
--

CREATE TABLE `staff` (
  `id` bigint UNSIGNED NOT NULL,
  `user_id` bigint UNSIGNED DEFAULT NULL,
  `employee_id` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `first_name` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `last_name` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `phone` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `email` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `address` text COLLATE utf8mb4_unicode_ci,
  `date_of_birth` date DEFAULT NULL,
  `gender` enum('male','female','other') COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `position` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `department` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `salary` decimal(10,2) DEFAULT NULL,
  `salary_type` enum('hourly','daily','monthly') COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'monthly',
  `hire_date` date NOT NULL,
  `termination_date` date DEFAULT NULL,
  `employment_status` enum('active','inactive','terminated') COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'active',
  `shift_start` time DEFAULT NULL,
  `shift_end` time DEFAULT NULL,
  `working_days` json DEFAULT NULL,
  `emergency_contact_name` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `emergency_contact_phone` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `notes` text COLLATE utf8mb4_unicode_ci,
  `avatar` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `is_active` tinyint(1) NOT NULL DEFAULT '1',
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `subscription_plans`
--

CREATE TABLE `subscription_plans` (
  `id` bigint UNSIGNED NOT NULL,
  `name` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `slug` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `description` text COLLATE utf8mb4_unicode_ci,
  `price` decimal(10,2) NOT NULL DEFAULT '0.00',
  `currency` varchar(3) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'BDT',
  `billing_cycle` enum('monthly','yearly') COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'monthly',
  `trial_days` int NOT NULL DEFAULT '14',
  `features` json DEFAULT NULL,
  `max_orders_per_month` int DEFAULT NULL,
  `max_menu_items` int DEFAULT NULL,
  `max_tables` int DEFAULT NULL,
  `max_staff` int DEFAULT NULL,
  `max_pages` int DEFAULT NULL,
  `max_branches` int DEFAULT NULL,
  `has_delivery` tinyint(1) NOT NULL DEFAULT '0',
  `has_home_delivery` tinyint(1) NOT NULL DEFAULT '0',
  `has_email_marketing` tinyint(1) NOT NULL DEFAULT '0',
  `has_loyalty_program` tinyint(1) NOT NULL DEFAULT '0',
  `has_advanced_reporting` tinyint(1) NOT NULL DEFAULT '0',
  `has_analytics` tinyint(1) NOT NULL DEFAULT '0',
  `has_multi_location` tinyint(1) NOT NULL DEFAULT '0',
  `has_api_access` tinyint(1) NOT NULL DEFAULT '0',
  `is_active` tinyint(1) NOT NULL DEFAULT '1',
  `sort_order` int NOT NULL DEFAULT '0',
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `subscription_plans`
--

INSERT INTO `subscription_plans` (`id`, `name`, `slug`, `description`, `price`, `currency`, `billing_cycle`, `trial_days`, `features`, `max_orders_per_month`, `max_menu_items`, `max_tables`, `max_staff`, `max_pages`, `max_branches`, `has_delivery`, `has_home_delivery`, `has_email_marketing`, `has_loyalty_program`, `has_advanced_reporting`, `has_analytics`, `has_multi_location`, `has_api_access`, `is_active`, `sort_order`, `created_at`, `updated_at`) VALUES
(1, 'Starter', 'starter', 'Perfect for small restaurants just getting started', 0.00, 'BDT', 'monthly', 14, '[\"Up to 50 menu items\", \"Up to 10 tables\", \"Up to 5 staff members\", \"Basic reporting\", \"Email support\"]', 500, 50, 10, 5, NULL, NULL, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, '2025-06-09 11:18:57', '2025-06-09 11:18:57'),
(2, 'Professional', 'professional', 'Great for growing restaurants with more features', 29.99, 'BDT', 'monthly', 14, '[\"Up to 200 menu items\", \"Up to 25 tables\", \"Up to 15 staff members\", \"Advanced reporting\", \"Delivery management\", \"Priority support\"]', 2000, 200, 25, 15, NULL, NULL, 1, 0, 0, 0, 0, 1, 0, 0, 1, 2, '2025-06-09 11:18:57', '2025-06-09 11:18:57'),
(3, 'Enterprise', 'enterprise', 'For large restaurants and chains with unlimited features', 99.99, 'BDT', 'monthly', 14, '[\"Unlimited menu items\", \"Unlimited tables\", \"Unlimited staff members\", \"Advanced analytics\", \"Multi-location support\", \"API access\", \"Custom integrations\", \"24/7 phone support\"]', NULL, NULL, NULL, NULL, NULL, NULL, 1, 0, 0, 0, 0, 1, 1, 1, 1, 3, '2025-06-09 11:18:57', '2025-06-09 11:18:57');

-- --------------------------------------------------------

--
-- Table structure for table `subscription_usage`
--

CREATE TABLE `subscription_usage` (
  `id` bigint UNSIGNED NOT NULL,
  `tenant_id` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `subscription_plan_id` bigint UNSIGNED NOT NULL,
  `period_start` date NOT NULL,
  `period_end` date NOT NULL,
  `menu_items_used` int NOT NULL DEFAULT '0',
  `orders_used` int NOT NULL DEFAULT '0',
  `pages_used` int NOT NULL DEFAULT '0',
  `branches_used` int NOT NULL DEFAULT '0',
  `staff_used` int NOT NULL DEFAULT '0',
  `total_menu_items` int NOT NULL DEFAULT '0',
  `total_pages` int NOT NULL DEFAULT '0',
  `total_branches` int NOT NULL DEFAULT '0',
  `revenue_generated` decimal(10,2) NOT NULL DEFAULT '0.00',
  `customers_served` int NOT NULL DEFAULT '0',
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `teams`
--

CREATE TABLE `teams` (
  `id` bigint UNSIGNED NOT NULL,
  `user_id` bigint UNSIGNED NOT NULL,
  `name` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `personal_team` tinyint(1) NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `team_invitations`
--

CREATE TABLE `team_invitations` (
  `id` bigint UNSIGNED NOT NULL,
  `team_id` bigint UNSIGNED NOT NULL,
  `email` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `role` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `team_user`
--

CREATE TABLE `team_user` (
  `id` bigint UNSIGNED NOT NULL,
  `team_id` bigint UNSIGNED NOT NULL,
  `user_id` bigint UNSIGNED NOT NULL,
  `role` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `tenants`
--

CREATE TABLE `tenants` (
  `id` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `name` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `email` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `phone` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `address` text COLLATE utf8mb4_unicode_ci,
  `city` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `state` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `country` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `postal_code` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `timezone` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'UTC',
  `currency` varchar(3) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'USD',
  `language` varchar(2) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'en',
  `logo` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `theme_color` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '#3B82F6',
  `subscription_plan_id` bigint UNSIGNED DEFAULT NULL,
  `subscription_status` enum('trial','active','inactive','cancelled','expired') COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'trial',
  `subscription_started_at` timestamp NULL DEFAULT NULL,
  `trial_ends_at` timestamp NULL DEFAULT NULL,
  `subscription_ends_at` timestamp NULL DEFAULT NULL,
  `setup_status` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'pending',
  `setup_started_at` timestamp NULL DEFAULT NULL,
  `setup_completed_at` timestamp NULL DEFAULT NULL,
  `setup_failed_at` timestamp NULL DEFAULT NULL,
  `setup_error` text COLLATE utf8mb4_unicode_ci,
  `setup_duration_minutes` int DEFAULT NULL,
  `last_activity_at` timestamp NULL DEFAULT NULL,
  `is_active` tinyint(1) NOT NULL DEFAULT '0',
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `data` json DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `tenants`
--

INSERT INTO `tenants` (`id`, `name`, `email`, `phone`, `address`, `city`, `state`, `country`, `postal_code`, `timezone`, `currency`, `language`, `logo`, `theme_color`, `subscription_plan_id`, `subscription_status`, `subscription_started_at`, `trial_ends_at`, `subscription_ends_at`, `setup_status`, `setup_started_at`, `setup_completed_at`, `setup_failed_at`, `setup_error`, `setup_duration_minutes`, `last_activity_at`, `is_active`, `created_at`, `updated_at`, `data`) VALUES
('demo-restaurant', 'Demo Restaurant', '<EMAIL>', '******-0123', '123 Main Street', 'New York', 'NY', 'USA', '10001', 'America/New_York', 'USD', 'en', NULL, '#3B82F6', NULL, 'active', NULL, '2025-07-09 11:19:00', NULL, 'pending', NULL, NULL, NULL, NULL, NULL, NULL, 0, '2025-06-09 11:19:00', '2025-06-09 11:19:00', '{\"created_at\": \"2025-06-09 17:19:00\", \"updated_at\": \"2025-06-09 17:19:00\", \"tenancy_db_name\": \"tenant_demo-restaurant\"}'),
('khanas', 'khanas', '<EMAIL>', '01736937161', 'sss', 'ss', 'sd', 'sd', 'sd', 'UTC', 'USD', 'en', NULL, '#3B82F6', 1, 'active', NULL, NULL, NULL, 'pending', NULL, NULL, NULL, NULL, NULL, NULL, 0, '2025-06-09 11:27:16', '2025-06-09 11:28:08', '{\"is_active\": false, \"created_at\": \"2025-06-09 17:27:16\", \"updated_at\": \"2025-06-09 17:27:16\", \"setup_error\": \"Step \'Setup Finalization\' failed: Final checks failed: No categories found in tenant database\", \"setup_status\": \"permanently_failed\", \"setup_failed_at\": \"2025-06-09 17:28:08\", \"tenancy_db_name\": \"tenant_khanas\", \"last_activity_at\": null, \"setup_started_at\": \"2025-06-09 17:28:07\", \"setup_completed_at\": null, \"setup_duration_minutes\": null, \"subscription_started_at\": null}'),
('king', 'king', '<EMAIL>', '0156555', NULL, NULL, NULL, NULL, NULL, 'UTC', 'USD', 'en', NULL, '#3B82F6', 2, 'active', NULL, NULL, NULL, 'pending', NULL, NULL, NULL, NULL, NULL, NULL, 0, '2025-06-10 23:07:16', '2025-06-10 23:08:08', '{\"is_active\": false, \"created_at\": \"2025-06-11 05:07:16\", \"updated_at\": \"2025-06-11 05:07:16\", \"setup_error\": null, \"setup_status\": \"in_progress\", \"tenancy_db_name\": \"tenant_king\", \"setup_started_at\": \"2025-06-11 05:08:08\"}');

-- --------------------------------------------------------

--
-- Table structure for table `tenant_subscriptions`
--

CREATE TABLE `tenant_subscriptions` (
  `id` bigint UNSIGNED NOT NULL,
  `tenant_id` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `subscription_plan_id` bigint UNSIGNED NOT NULL,
  `status` enum('trial','active','inactive','cancelled','expired','past_due') COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'trial',
  `trial_ends_at` timestamp NULL DEFAULT NULL,
  `current_period_start` timestamp NULL DEFAULT NULL,
  `current_period_end` timestamp NULL DEFAULT NULL,
  `cancelled_at` timestamp NULL DEFAULT NULL,
  `ends_at` timestamp NULL DEFAULT NULL,
  `amount` decimal(10,2) NOT NULL DEFAULT '0.00',
  `currency` varchar(3) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'BDT',
  `billing_cycle` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'monthly',
  `metadata` json DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `tenant_user_impersonation_tokens`
--

CREATE TABLE `tenant_user_impersonation_tokens` (
  `token` varchar(128) COLLATE utf8mb4_unicode_ci NOT NULL,
  `tenant_id` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `user_id` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `auth_guard` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `redirect_url` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `created_at` timestamp NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `users`
--

CREATE TABLE `users` (
  `id` bigint UNSIGNED NOT NULL,
  `name` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `email` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `phone` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `role` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `preferred_language` varchar(10) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'en',
  `theme_preference` varchar(20) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'light',
  `is_active` tinyint(1) NOT NULL DEFAULT '1',
  `is_system_admin` tinyint(1) NOT NULL DEFAULT '0',
  `last_login_at` timestamp NULL DEFAULT NULL,
  `department` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `position` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `hire_date` date DEFAULT NULL,
  `salary` decimal(10,2) DEFAULT NULL,
  `hourly_rate` decimal(8,2) DEFAULT NULL,
  `address` text COLLATE utf8mb4_unicode_ci,
  `emergency_contact_name` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `emergency_contact_phone` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `notes` text COLLATE utf8mb4_unicode_ci,
  `is_on_shift` tinyint(1) NOT NULL DEFAULT '0',
  `email_verified_at` timestamp NULL DEFAULT NULL,
  `password` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `two_factor_secret` text COLLATE utf8mb4_unicode_ci,
  `two_factor_recovery_codes` text COLLATE utf8mb4_unicode_ci,
  `two_factor_confirmed_at` timestamp NULL DEFAULT NULL,
  `remember_token` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `current_team_id` bigint UNSIGNED DEFAULT NULL,
  `profile_photo_path` varchar(2048) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `users`
--

INSERT INTO `users` (`id`, `name`, `email`, `phone`, `role`, `preferred_language`, `theme_preference`, `is_active`, `is_system_admin`, `last_login_at`, `department`, `position`, `hire_date`, `salary`, `hourly_rate`, `address`, `emergency_contact_name`, `emergency_contact_phone`, `notes`, `is_on_shift`, `email_verified_at`, `password`, `two_factor_secret`, `two_factor_recovery_codes`, `two_factor_confirmed_at`, `remember_token`, `current_team_id`, `profile_photo_path`, `created_at`, `updated_at`) VALUES
(1, 'Super Admin', '<EMAIL>', NULL, 'admin', 'en', 'light', 1, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, '2025-06-09 11:18:58', '$2y$12$G1xswQ1Cz/7B/ZonA7A1peDsqJJqZHODkUR/1oYYz5WOWpy0YP1K6', NULL, NULL, NULL, NULL, NULL, NULL, '2025-06-09 11:18:58', '2025-06-09 11:18:58'),
(2, 'Restaurant Manager', '<EMAIL>', NULL, 'restaurant_manager', 'en', 'light', 1, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, '2025-06-09 11:18:58', '$2y$12$1K01rY5qL8Pk1W6a2JBYIO89KaYoTgxVfoMumCiLM30.j8rwrR9bi', NULL, NULL, NULL, NULL, NULL, NULL, '2025-06-09 11:18:58', '2025-06-09 11:18:58'),
(3, 'John Smith', '<EMAIL>', NULL, 'waiter', 'en', 'light', 1, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, '2025-06-09 11:18:58', '$2y$12$T1k.TdKsRLDP9qevGZ3KxO1olHLho1QzllC9L8I92YN8fznjX/UO.', NULL, NULL, NULL, NULL, NULL, NULL, '2025-06-09 11:18:58', '2025-06-09 11:18:58'),
(4, 'Chef Maria', '<EMAIL>', NULL, 'kitchen', 'en', 'light', 1, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, '2025-06-09 11:18:58', '$2y$12$5Z6wKR3atq7HbukmPORF7.PjwG1gh8w5q338n3IND.kJAlbTlQDZK', NULL, NULL, NULL, NULL, NULL, NULL, '2025-06-09 11:18:58', '2025-06-09 11:18:58'),
(5, 'Ahmed Rahman', '<EMAIL>', NULL, 'delivery', 'bn', 'light', 1, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, '2025-06-09 11:18:58', '$2y$12$58LcgNEk6Jy/3j1SP1rN3e0LtsCJVBumoAfBr/7R9vsFtftBOGili', NULL, NULL, NULL, NULL, NULL, NULL, '2025-06-09 11:18:58', '2025-06-09 11:18:58'),
(6, 'Waiter 1', '<EMAIL>', NULL, 'waiter', 'en', 'light', 1, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, '2025-06-09 11:18:59', '$2y$12$w.tyMH0PPyOfODCdK.A55OEAZXYtgiqy6UqMYZaZ9.bQ4lB/RqRa.', NULL, NULL, NULL, NULL, NULL, NULL, '2025-06-09 11:18:59', '2025-06-09 11:18:59'),
(7, 'Waiter 2', '<EMAIL>', NULL, 'waiter', 'en', 'light', 1, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, '2025-06-09 11:18:59', '$2y$12$EKwA0YW4G9vxJ.5j4jfd2.d0mnlfjzqHELwIgGx8jNFzaXbHr1UEG', NULL, NULL, NULL, NULL, NULL, NULL, '2025-06-09 11:18:59', '2025-06-09 11:18:59'),
(8, 'Waiter 3', '<EMAIL>', NULL, 'waiter', 'en', 'light', 1, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, '2025-06-09 11:18:59', '$2y$12$wjhKXXJXx1noqfN.BFC1VORvfhkjMWIeqx967PFj7WpjueMBuMBQe', NULL, NULL, NULL, NULL, NULL, NULL, '2025-06-09 11:18:59', '2025-06-09 11:18:59'),
(9, 'Kitchen Staff 1', '<EMAIL>', NULL, 'kitchen', 'en', 'light', 1, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, '2025-06-09 11:18:59', '$2y$12$IRAfBbaHk/RgXeqNMROHoOIfQICQD9AN2stEc7YdBLovZT63Lz.au', NULL, NULL, NULL, NULL, NULL, NULL, '2025-06-09 11:18:59', '2025-06-09 11:18:59'),
(10, 'Kitchen Staff 2', '<EMAIL>', NULL, 'kitchen', 'en', 'light', 1, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, '2025-06-09 11:19:00', '$2y$12$p1Yuc5yRzzoI2UIZo0nWYua6fBmNcbgJYlZQDDdxNJ.RbM65jd/ru', NULL, NULL, NULL, NULL, NULL, NULL, '2025-06-09 11:19:00', '2025-06-09 11:19:00'),
(11, 'Driver 1', '<EMAIL>', NULL, 'delivery', 'bn', 'light', 1, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, '2025-06-09 11:19:00', '$2y$12$qhA6G/eBI4/ttMDw21XZWe/9CDEuX9ZuGakL/LXeRPjXrO/7kyMYe', NULL, NULL, NULL, NULL, NULL, NULL, '2025-06-09 11:19:00', '2025-06-09 11:19:00'),
(12, 'Driver 2', '<EMAIL>', NULL, 'delivery', 'bn', 'light', 1, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, '2025-06-09 11:19:00', '$2y$12$2V55RAw9qs3uAwMFdmhUfuwNlBFkJDWmYkWBsN.kdstbZ6zE3YkXe', NULL, NULL, NULL, NULL, NULL, NULL, '2025-06-09 11:19:00', '2025-06-09 11:19:00');

--
-- Indexes for dumped tables
--

--
-- Indexes for table `blogs`
--
ALTER TABLE `blogs`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `blogs_slug_unique` (`slug`),
  ADD KEY `blogs_featured_image_id_foreign` (`featured_image_id`),
  ADD KEY `blogs_created_by_foreign` (`created_by`),
  ADD KEY `blogs_updated_by_foreign` (`updated_by`),
  ADD KEY `blogs_status_published_at_index` (`published_at`),
  ADD KEY `blogs_author_id_status_index` (`author_id`),
  ADD KEY `blogs_sort_order_index` (`sort_order`);

--
-- Indexes for table `branches`
--
ALTER TABLE `branches`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `branches_slug_unique` (`slug`),
  ADD KEY `branches_is_active_sort_order_index` (`is_active`,`sort_order`),
  ADD KEY `branches_is_main_branch_index` (`is_main_branch`);

--
-- Indexes for table `cache`
--
ALTER TABLE `cache`
  ADD PRIMARY KEY (`key`);

--
-- Indexes for table `cache_locks`
--
ALTER TABLE `cache_locks`
  ADD PRIMARY KEY (`key`);

--
-- Indexes for table `combo_menu_media`
--
ALTER TABLE `combo_menu_media`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `domains`
--
ALTER TABLE `domains`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `domains_domain_unique` (`domain`),
  ADD KEY `domains_tenant_id_foreign` (`tenant_id`);

--
-- Indexes for table `failed_jobs`
--
ALTER TABLE `failed_jobs`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `failed_jobs_uuid_unique` (`uuid`);

--
-- Indexes for table `jobs`
--
ALTER TABLE `jobs`
  ADD PRIMARY KEY (`id`),
  ADD KEY `jobs_queue_index` (`queue`);

--
-- Indexes for table `job_batches`
--
ALTER TABLE `job_batches`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `loyalty_accounts`
--
ALTER TABLE `loyalty_accounts`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `loyalty_accounts_phone_number_unique` (`phone_number`),
  ADD KEY `loyalty_accounts_phone_number_index` (`phone_number`),
  ADD KEY `loyalty_accounts_tier_is_active_index` (`tier`,`is_active`),
  ADD KEY `loyalty_accounts_total_points_index` (`total_points`),
  ADD KEY `loyalty_accounts_last_transaction_at_index` (`last_transaction_at`);

--
-- Indexes for table `loyalty_settings`
--
ALTER TABLE `loyalty_settings`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `media`
--
ALTER TABLE `media`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `media_uuid_unique` (`uuid`),
  ADD KEY `media_model_type_model_id_index` (`model_type`,`model_id`),
  ADD KEY `media_collection_name_index` (`collection_name`),
  ADD KEY `media_folder_index` (`folder`),
  ADD KEY `media_uploaded_by_foreign` (`uploaded_by`);

--
-- Indexes for table `migrations`
--
ALTER TABLE `migrations`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `model_has_permissions`
--
ALTER TABLE `model_has_permissions`
  ADD PRIMARY KEY (`permission_id`,`model_id`,`model_type`),
  ADD KEY `model_has_permissions_model_id_model_type_index` (`model_id`,`model_type`);

--
-- Indexes for table `model_has_roles`
--
ALTER TABLE `model_has_roles`
  ADD PRIMARY KEY (`role_id`,`model_id`,`model_type`),
  ADD KEY `model_has_roles_model_id_model_type_index` (`model_id`,`model_type`);

--
-- Indexes for table `password_reset_tokens`
--
ALTER TABLE `password_reset_tokens`
  ADD PRIMARY KEY (`email`);

--
-- Indexes for table `payments`
--
ALTER TABLE `payments`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `payments_transaction_id_unique` (`transaction_id`),
  ADD KEY `payments_tenant_id_foreign` (`tenant_id`),
  ADD KEY `payments_subscription_plan_id_foreign` (`subscription_plan_id`);

--
-- Indexes for table `permissions`
--
ALTER TABLE `permissions`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `permissions_name_guard_name_unique` (`name`,`guard_name`);

--
-- Indexes for table `personal_access_tokens`
--
ALTER TABLE `personal_access_tokens`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `personal_access_tokens_token_unique` (`token`),
  ADD KEY `personal_access_tokens_tokenable_type_tokenable_id_index` (`tokenable_type`,`tokenable_id`);

--
-- Indexes for table `riders`
--
ALTER TABLE `riders`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `riders_employee_id_unique` (`employee_id`),
  ADD UNIQUE KEY `riders_phone_unique` (`phone`),
  ADD KEY `riders_status_delivery_status_index` (`status`,`delivery_status`),
  ADD KEY `riders_phone_index` (`phone`),
  ADD KEY `riders_delivery_status_current_delivery_count_index` (`delivery_status`,`current_delivery_count`);

--
-- Indexes for table `roles`
--
ALTER TABLE `roles`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `roles_name_guard_name_unique` (`name`,`guard_name`);

--
-- Indexes for table `role_has_permissions`
--
ALTER TABLE `role_has_permissions`
  ADD PRIMARY KEY (`permission_id`,`role_id`),
  ADD KEY `role_has_permissions_role_id_foreign` (`role_id`);

--
-- Indexes for table `sessions`
--
ALTER TABLE `sessions`
  ADD PRIMARY KEY (`id`),
  ADD KEY `sessions_user_id_index` (`user_id`),
  ADD KEY `sessions_last_activity_index` (`last_activity`);

--
-- Indexes for table `staff`
--
ALTER TABLE `staff`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `staff_employee_id_unique` (`employee_id`),
  ADD UNIQUE KEY `staff_email_unique` (`email`);

--
-- Indexes for table `subscription_plans`
--
ALTER TABLE `subscription_plans`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `subscription_plans_slug_unique` (`slug`);

--
-- Indexes for table `subscription_usage`
--
ALTER TABLE `subscription_usage`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `subscription_usage_tenant_id_period_start_period_end_unique` (`tenant_id`,`period_start`,`period_end`),
  ADD KEY `subscription_usage_tenant_id_period_start_index` (`tenant_id`,`period_start`),
  ADD KEY `subscription_usage_subscription_plan_id_index` (`subscription_plan_id`);

--
-- Indexes for table `teams`
--
ALTER TABLE `teams`
  ADD PRIMARY KEY (`id`),
  ADD KEY `teams_user_id_index` (`user_id`);

--
-- Indexes for table `team_invitations`
--
ALTER TABLE `team_invitations`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `team_invitations_team_id_email_unique` (`team_id`,`email`);

--
-- Indexes for table `team_user`
--
ALTER TABLE `team_user`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `team_user_team_id_user_id_unique` (`team_id`,`user_id`);

--
-- Indexes for table `tenants`
--
ALTER TABLE `tenants`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `tenants_email_unique` (`email`),
  ADD KEY `tenants_subscription_plan_id_foreign` (`subscription_plan_id`),
  ADD KEY `tenants_setup_status_index` (`setup_status`),
  ADD KEY `tenants_is_active_index` (`is_active`);

--
-- Indexes for table `tenant_subscriptions`
--
ALTER TABLE `tenant_subscriptions`
  ADD PRIMARY KEY (`id`),
  ADD KEY `tenant_subscriptions_tenant_id_status_index` (`tenant_id`,`status`),
  ADD KEY `tenant_subscriptions_subscription_plan_id_index` (`subscription_plan_id`),
  ADD KEY `tenant_subscriptions_current_period_end_index` (`current_period_end`);

--
-- Indexes for table `tenant_user_impersonation_tokens`
--
ALTER TABLE `tenant_user_impersonation_tokens`
  ADD PRIMARY KEY (`token`),
  ADD KEY `tenant_user_impersonation_tokens_tenant_id_foreign` (`tenant_id`);

--
-- Indexes for table `users`
--
ALTER TABLE `users`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `users_email_unique` (`email`);

--
-- AUTO_INCREMENT for dumped tables
--

--
-- AUTO_INCREMENT for table `blogs`
--
ALTER TABLE `blogs`
  MODIFY `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `branches`
--
ALTER TABLE `branches`
  MODIFY `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `combo_menu_media`
--
ALTER TABLE `combo_menu_media`
  MODIFY `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `domains`
--
ALTER TABLE `domains`
  MODIFY `id` int UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=4;

--
-- AUTO_INCREMENT for table `failed_jobs`
--
ALTER TABLE `failed_jobs`
  MODIFY `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=2;

--
-- AUTO_INCREMENT for table `jobs`
--
ALTER TABLE `jobs`
  MODIFY `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=5;

--
-- AUTO_INCREMENT for table `loyalty_accounts`
--
ALTER TABLE `loyalty_accounts`
  MODIFY `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `loyalty_settings`
--
ALTER TABLE `loyalty_settings`
  MODIFY `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `media`
--
ALTER TABLE `media`
  MODIFY `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `migrations`
--
ALTER TABLE `migrations`
  MODIFY `id` int UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=33;

--
-- AUTO_INCREMENT for table `payments`
--
ALTER TABLE `payments`
  MODIFY `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `permissions`
--
ALTER TABLE `permissions`
  MODIFY `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=45;

--
-- AUTO_INCREMENT for table `personal_access_tokens`
--
ALTER TABLE `personal_access_tokens`
  MODIFY `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `riders`
--
ALTER TABLE `riders`
  MODIFY `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `roles`
--
ALTER TABLE `roles`
  MODIFY `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=6;

--
-- AUTO_INCREMENT for table `staff`
--
ALTER TABLE `staff`
  MODIFY `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `subscription_plans`
--
ALTER TABLE `subscription_plans`
  MODIFY `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=4;

--
-- AUTO_INCREMENT for table `subscription_usage`
--
ALTER TABLE `subscription_usage`
  MODIFY `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `teams`
--
ALTER TABLE `teams`
  MODIFY `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `team_invitations`
--
ALTER TABLE `team_invitations`
  MODIFY `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `team_user`
--
ALTER TABLE `team_user`
  MODIFY `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `tenant_subscriptions`
--
ALTER TABLE `tenant_subscriptions`
  MODIFY `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `users`
--
ALTER TABLE `users`
  MODIFY `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=13;

--
-- Constraints for dumped tables
--

--
-- Constraints for table `blogs`
--
ALTER TABLE `blogs`
  ADD CONSTRAINT `blogs_author_id_foreign` FOREIGN KEY (`author_id`) REFERENCES `users` (`id`) ON DELETE SET NULL,
  ADD CONSTRAINT `blogs_created_by_foreign` FOREIGN KEY (`created_by`) REFERENCES `users` (`id`) ON DELETE SET NULL,
  ADD CONSTRAINT `blogs_featured_image_id_foreign` FOREIGN KEY (`featured_image_id`) REFERENCES `media` (`id`) ON DELETE SET NULL,
  ADD CONSTRAINT `blogs_updated_by_foreign` FOREIGN KEY (`updated_by`) REFERENCES `users` (`id`) ON DELETE SET NULL;

--
-- Constraints for table `domains`
--
ALTER TABLE `domains`
  ADD CONSTRAINT `domains_tenant_id_foreign` FOREIGN KEY (`tenant_id`) REFERENCES `tenants` (`id`) ON DELETE CASCADE ON UPDATE CASCADE;

--
-- Constraints for table `media`
--
ALTER TABLE `media`
  ADD CONSTRAINT `media_uploaded_by_foreign` FOREIGN KEY (`uploaded_by`) REFERENCES `users` (`id`) ON DELETE SET NULL;

--
-- Constraints for table `model_has_permissions`
--
ALTER TABLE `model_has_permissions`
  ADD CONSTRAINT `model_has_permissions_permission_id_foreign` FOREIGN KEY (`permission_id`) REFERENCES `permissions` (`id`) ON DELETE CASCADE;

--
-- Constraints for table `model_has_roles`
--
ALTER TABLE `model_has_roles`
  ADD CONSTRAINT `model_has_roles_role_id_foreign` FOREIGN KEY (`role_id`) REFERENCES `roles` (`id`) ON DELETE CASCADE;

--
-- Constraints for table `payments`
--
ALTER TABLE `payments`
  ADD CONSTRAINT `payments_subscription_plan_id_foreign` FOREIGN KEY (`subscription_plan_id`) REFERENCES `subscription_plans` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `payments_tenant_id_foreign` FOREIGN KEY (`tenant_id`) REFERENCES `tenants` (`id`) ON DELETE CASCADE;

--
-- Constraints for table `role_has_permissions`
--
ALTER TABLE `role_has_permissions`
  ADD CONSTRAINT `role_has_permissions_permission_id_foreign` FOREIGN KEY (`permission_id`) REFERENCES `permissions` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `role_has_permissions_role_id_foreign` FOREIGN KEY (`role_id`) REFERENCES `roles` (`id`) ON DELETE CASCADE;

--
-- Constraints for table `subscription_usage`
--
ALTER TABLE `subscription_usage`
  ADD CONSTRAINT `subscription_usage_subscription_plan_id_foreign` FOREIGN KEY (`subscription_plan_id`) REFERENCES `subscription_plans` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `subscription_usage_tenant_id_foreign` FOREIGN KEY (`tenant_id`) REFERENCES `tenants` (`id`) ON DELETE CASCADE;

--
-- Constraints for table `team_invitations`
--
ALTER TABLE `team_invitations`
  ADD CONSTRAINT `team_invitations_team_id_foreign` FOREIGN KEY (`team_id`) REFERENCES `teams` (`id`) ON DELETE CASCADE;

--
-- Constraints for table `tenants`
--
ALTER TABLE `tenants`
  ADD CONSTRAINT `tenants_subscription_plan_id_foreign` FOREIGN KEY (`subscription_plan_id`) REFERENCES `subscription_plans` (`id`) ON DELETE SET NULL;

--
-- Constraints for table `tenant_subscriptions`
--
ALTER TABLE `tenant_subscriptions`
  ADD CONSTRAINT `tenant_subscriptions_subscription_plan_id_foreign` FOREIGN KEY (`subscription_plan_id`) REFERENCES `subscription_plans` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `tenant_subscriptions_tenant_id_foreign` FOREIGN KEY (`tenant_id`) REFERENCES `tenants` (`id`) ON DELETE CASCADE;

--
-- Constraints for table `tenant_user_impersonation_tokens`
--
ALTER TABLE `tenant_user_impersonation_tokens`
  ADD CONSTRAINT `tenant_user_impersonation_tokens_tenant_id_foreign` FOREIGN KEY (`tenant_id`) REFERENCES `tenants` (`id`) ON DELETE CASCADE ON UPDATE CASCADE;
COMMIT;

/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
