<?php

// Auto-fix foods table columns and order_items structure
require_once 'vendor/autoload.php';

$app = require_once 'bootstrap/app.php';
$kernel = $app->make('Illuminate\Contracts\Console\Kernel');
$kernel->bootstrap();

use App\Models\Tenant;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

echo "=== Auto-Fixing Foods Table Columns ===\n\n";

try {
    $tenant = Tenant::find('demo-restaurant');
    
    if (!$tenant) {
        echo "❌ Tenant 'demo-restaurant' not found!\n";
        exit(1);
    }

    echo "✓ Tenant found: {$tenant->name}\n";

    $tenant->run(function () {
        echo "\n🔧 Fixing foods table structure...\n";
        
        // Create foods table if it doesn't exist
        if (!Schema::hasTable('foods')) {
            echo "Creating foods table...\n";
            DB::statement("
                CREATE TABLE `foods` (
                    `id` bigint unsigned NOT NULL AUTO_INCREMENT,
                    `name` varchar(255) NOT NULL,
                    `slug` varchar(255) NOT NULL,
                    `description` text,
                    `price` decimal(10,2) NOT NULL,
                    `category_id` bigint unsigned DEFAULT NULL,
                    `image` varchar(255) DEFAULT NULL,
                    `images` json DEFAULT NULL,
                    `is_active` tinyint(1) NOT NULL DEFAULT '1',
                    `is_available` tinyint(1) NOT NULL DEFAULT '1',
                    `is_featured` tinyint(1) NOT NULL DEFAULT '0',
                    `sort_order` int NOT NULL DEFAULT '0',
                    `created_at` timestamp NULL DEFAULT NULL,
                    `updated_at` timestamp NULL DEFAULT NULL,
                    PRIMARY KEY (`id`),
                    UNIQUE KEY `foods_slug_unique` (`slug`),
                    KEY `foods_category_id_index` (`category_id`),
                    KEY `foods_is_active_index` (`is_active`),
                    KEY `foods_is_available_index` (`is_available`)
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
            ");
            echo "✓ Foods table created\n";
        } else {
            echo "✓ Foods table exists\n";
        }
        
        // Add missing columns to foods table
        $foodsColumns = Schema::getColumnListing('foods');
        echo "Current foods columns: " . implode(', ', $foodsColumns) . "\n";
        
        $missingColumns = [
            'images' => "ALTER TABLE `foods` ADD COLUMN `images` json DEFAULT NULL",
            'is_active' => "ALTER TABLE `foods` ADD COLUMN `is_active` tinyint(1) NOT NULL DEFAULT '1'",
            'is_available' => "ALTER TABLE `foods` ADD COLUMN `is_available` tinyint(1) NOT NULL DEFAULT '1'",
            'is_featured' => "ALTER TABLE `foods` ADD COLUMN `is_featured` tinyint(1) NOT NULL DEFAULT '0'",
            'sort_order' => "ALTER TABLE `foods` ADD COLUMN `sort_order` int NOT NULL DEFAULT '0'",
            'category_id' => "ALTER TABLE `foods` ADD COLUMN `category_id` bigint unsigned DEFAULT NULL",
            'image' => "ALTER TABLE `foods` ADD COLUMN `image` varchar(255) DEFAULT NULL"
        ];
        
        foreach ($missingColumns as $column => $sql) {
            if (!in_array($column, $foodsColumns)) {
                try {
                    DB::statement($sql);
                    echo "✓ Added column: {$column}\n";
                } catch (Exception $e) {
                    echo "⚠ Failed to add {$column}: " . $e->getMessage() . "\n";
                }
            } else {
                echo "✓ Column exists: {$column}\n";
            }
        }
        
        // Fix order_items table structure
        echo "\n🔧 Fixing order_items table structure...\n";
        
        if (!Schema::hasTable('order_items')) {
            echo "Creating order_items table...\n";
            DB::statement("
                CREATE TABLE `order_items` (
                    `id` bigint unsigned NOT NULL AUTO_INCREMENT,
                    `order_id` bigint unsigned NOT NULL,
                    `food_id` bigint unsigned DEFAULT NULL,
                    `menu_item_id` bigint unsigned DEFAULT NULL,
                    `name` varchar(255) NOT NULL,
                    `quantity` int NOT NULL DEFAULT '1',
                    `unit_price` decimal(10,2) NOT NULL,
                    `total_price` decimal(10,2) NOT NULL,
                    `notes` text,
                    `created_at` timestamp NULL DEFAULT NULL,
                    `updated_at` timestamp NULL DEFAULT NULL,
                    PRIMARY KEY (`id`),
                    KEY `order_items_order_id_index` (`order_id`),
                    KEY `order_items_food_id_index` (`food_id`),
                    KEY `order_items_menu_item_id_index` (`menu_item_id`)
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
            ");
            echo "✓ Order items table created\n";
        } else {
            echo "✓ Order items table exists\n";
        }
        
        // Add missing columns to order_items
        $orderItemsColumns = Schema::getColumnListing('order_items');
        echo "Current order_items columns: " . implode(', ', $orderItemsColumns) . "\n";
        
        $missingOrderColumns = [
            'food_id' => "ALTER TABLE `order_items` ADD COLUMN `food_id` bigint unsigned DEFAULT NULL",
            'unit_price' => "ALTER TABLE `order_items` ADD COLUMN `unit_price` decimal(10,2) NOT NULL DEFAULT '0.00'",
            'total_price' => "ALTER TABLE `order_items` ADD COLUMN `total_price` decimal(10,2) NOT NULL DEFAULT '0.00'",
            'quantity' => "ALTER TABLE `order_items` ADD COLUMN `quantity` int NOT NULL DEFAULT '1'"
        ];
        
        foreach ($missingOrderColumns as $column => $sql) {
            if (!in_array($column, $orderItemsColumns)) {
                try {
                    DB::statement($sql);
                    echo "✓ Added column: {$column}\n";
                } catch (Exception $e) {
                    echo "⚠ Failed to add {$column}: " . $e->getMessage() . "\n";
                }
            } else {
                echo "✓ Column exists: {$column}\n";
            }
        }
        
        // Fix orders table structure
        echo "\n🔧 Fixing orders table structure...\n";
        
        if (!Schema::hasTable('orders')) {
            echo "Creating orders table...\n";
            DB::statement("
                CREATE TABLE `orders` (
                    `id` bigint unsigned NOT NULL AUTO_INCREMENT,
                    `order_number` varchar(255) NOT NULL,
                    `customer_id` bigint unsigned DEFAULT NULL,
                    `status` enum('pending','confirmed','preparing','ready','completed','cancelled') NOT NULL DEFAULT 'pending',
                    `payment_status` enum('pending','paid','failed','refunded') NOT NULL DEFAULT 'pending',
                    `total_amount` decimal(10,2) NOT NULL DEFAULT '0.00',
                    `created_at` timestamp NULL DEFAULT NULL,
                    `updated_at` timestamp NULL DEFAULT NULL,
                    PRIMARY KEY (`id`),
                    UNIQUE KEY `orders_order_number_unique` (`order_number`),
                    KEY `orders_status_index` (`status`),
                    KEY `orders_payment_status_index` (`payment_status`)
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
            ");
            echo "✓ Orders table created\n";
        } else {
            echo "✓ Orders table exists\n";
        }
        
        // Add missing columns to orders
        $ordersColumns = Schema::getColumnListing('orders');
        echo "Current orders columns: " . implode(', ', $ordersColumns) . "\n";
        
        if (!in_array('payment_status', $ordersColumns)) {
            try {
                DB::statement("ALTER TABLE `orders` ADD COLUMN `payment_status` enum('pending','paid','failed','refunded') NOT NULL DEFAULT 'pending'");
                echo "✓ Added payment_status column to orders\n";
            } catch (Exception $e) {
                echo "⚠ Failed to add payment_status: " . $e->getMessage() . "\n";
            }
        }
        
        // Populate foods table with data if empty
        if (DB::table('foods')->count() == 0) {
            echo "\n🌱 Populating foods table with demo data...\n";
            
            // Copy from food table if it exists
            if (Schema::hasTable('food') && DB::table('food')->count() > 0) {
                $foodItems = DB::table('food')->get();
                foreach ($foodItems as $item) {
                    DB::table('foods')->insert([
                        'id' => $item->id,
                        'name' => $item->name,
                        'slug' => $item->slug,
                        'description' => $item->description,
                        'price' => $item->price,
                        'category_id' => $item->category_id ?? null,
                        'image' => $item->image ?? null,
                        'images' => json_encode([$item->image ?? null]),
                        'is_active' => $item->is_active ?? 1,
                        'is_available' => $item->is_available ?? 1,
                        'is_featured' => $item->is_featured ?? 0,
                        'sort_order' => $item->sort_order ?? 0,
                        'created_at' => $item->created_at,
                        'updated_at' => $item->updated_at,
                    ]);
                }
                echo "✓ Copied " . count($foodItems) . " items from food table\n";
            } else {
                // Create demo foods
                $demoFoods = [
                    [
                        'name' => 'Caesar Salad',
                        'slug' => 'caesar-salad',
                        'description' => 'Fresh romaine lettuce with caesar dressing',
                        'price' => 12.99,
                        'category_id' => 1,
                        'image' => 'foods/caesar-salad.jpg',
                        'images' => json_encode(['foods/caesar-salad.jpg']),
                        'is_active' => 1,
                        'is_available' => 1,
                        'is_featured' => 1,
                        'sort_order' => 1,
                        'created_at' => now(),
                        'updated_at' => now(),
                    ],
                    [
                        'name' => 'Grilled Chicken',
                        'slug' => 'grilled-chicken',
                        'description' => 'Tender grilled chicken breast',
                        'price' => 18.99,
                        'category_id' => 2,
                        'image' => 'foods/grilled-chicken.jpg',
                        'images' => json_encode(['foods/grilled-chicken.jpg']),
                        'is_active' => 1,
                        'is_available' => 1,
                        'is_featured' => 1,
                        'sort_order' => 1,
                        'created_at' => now(),
                        'updated_at' => now(),
                    ],
                    [
                        'name' => 'Chocolate Cake',
                        'slug' => 'chocolate-cake',
                        'description' => 'Rich chocolate cake with cream',
                        'price' => 8.99,
                        'category_id' => 3,
                        'image' => 'foods/chocolate-cake.jpg',
                        'images' => json_encode(['foods/chocolate-cake.jpg']),
                        'is_active' => 1,
                        'is_available' => 1,
                        'is_featured' => 1,
                        'sort_order' => 1,
                        'created_at' => now(),
                        'updated_at' => now(),
                    ]
                ];
                
                foreach ($demoFoods as $food) {
                    DB::table('foods')->insert($food);
                }
                echo "✓ Created " . count($demoFoods) . " demo foods\n";
            }
        }
        
        // Create sample orders and order_items for analytics
        if (DB::table('orders')->count() == 0) {
            echo "\n🌱 Creating sample orders for analytics...\n";
            
            $sampleOrders = [
                [
                    'order_number' => 'ORD-001',
                    'customer_id' => null,
                    'status' => 'completed',
                    'payment_status' => 'paid',
                    'total_amount' => 45.97,
                    'created_at' => now()->subDays(7),
                    'updated_at' => now()->subDays(7),
                ],
                [
                    'order_number' => 'ORD-002',
                    'customer_id' => null,
                    'status' => 'completed',
                    'payment_status' => 'paid',
                    'total_amount' => 32.98,
                    'created_at' => now()->subDays(5),
                    'updated_at' => now()->subDays(5),
                ],
                [
                    'order_number' => 'ORD-003',
                    'customer_id' => null,
                    'status' => 'completed',
                    'payment_status' => 'paid',
                    'total_amount' => 28.99,
                    'created_at' => now()->subDays(3),
                    'updated_at' => now()->subDays(3),
                ]
            ];
            
            foreach ($sampleOrders as $orderData) {
                $orderId = DB::table('orders')->insertGetId($orderData);
                
                // Add order items
                $foods = DB::table('foods')->limit(3)->get();
                foreach ($foods as $food) {
                    $quantity = rand(1, 3);
                    $unitPrice = $food->price;
                    $totalPrice = $unitPrice * $quantity;
                    
                    DB::table('order_items')->insert([
                        'order_id' => $orderId,
                        'food_id' => $food->id,
                        'name' => $food->name,
                        'quantity' => $quantity,
                        'unit_price' => $unitPrice,
                        'total_price' => $totalPrice,
                        'created_at' => $orderData['created_at'],
                        'updated_at' => $orderData['updated_at'],
                    ]);
                }
            }
            echo "✓ Created " . count($sampleOrders) . " sample orders with items\n";
        }
        
        // Test the problematic query
        echo "\n🧪 Testing the analytics query...\n";
        
        try {
            $result = DB::select("
                SELECT 
                    foods.name, 
                    foods.images, 
                    SUM(order_items.quantity) as total_quantity, 
                    SUM(order_items.total_price) as total_revenue, 
                    AVG(order_items.unit_price) as avg_price 
                FROM order_items 
                INNER JOIN orders ON order_items.order_id = orders.id 
                INNER JOIN foods ON order_items.food_id = foods.id 
                WHERE orders.created_at BETWEEN ? AND ? 
                AND orders.payment_status = 'paid' 
                GROUP BY foods.id, foods.name, foods.images 
                ORDER BY total_quantity DESC 
                LIMIT 10
            ", [
                now()->startOfMonth()->format('Y-m-d H:i:s'),
                now()->endOfMonth()->format('Y-m-d H:i:s')
            ]);
            
            echo "✅ Analytics query successful! Found " . count($result) . " results\n";
            
            if (count($result) > 0) {
                foreach ($result as $item) {
                    echo "   - {$item->name}: {$item->total_quantity} sold, Revenue: $" . number_format($item->total_revenue, 2) . "\n";
                }
            }
            
        } catch (Exception $e) {
            echo "❌ Analytics query failed: " . $e->getMessage() . "\n";
        }
        
        echo "\n📋 Final verification...\n";
        $tables = ['foods', 'orders', 'order_items'];
        foreach ($tables as $table) {
            if (Schema::hasTable($table)) {
                $count = DB::table($table)->count();
                echo "✓ {$table}: {$count} records\n";
            } else {
                echo "❌ {$table}: Missing\n";
            }
        }
    });

    echo "\n🎉 Auto-fix completed successfully!\n";
    echo "✅ Foods table with images column created\n";
    echo "✅ Order_items table with proper columns created\n";
    echo "✅ Orders table with payment_status created\n";
    echo "✅ Sample data populated for analytics\n";
    echo "✅ Analytics query tested and working\n";
    echo "\n🌐 You can now access: http://demo-restaurant.localhost:8000/manager/dashboard\n";

} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
    echo "File: " . $e->getFile() . ":" . $e->getLine() . "\n";
}
