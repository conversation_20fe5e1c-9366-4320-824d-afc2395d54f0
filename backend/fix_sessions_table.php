<?php

// Quick fix for sessions table in tenant database
echo "=== Fixing Sessions Table in Tenant Database ===\n";

// Database configuration
$host = '127.0.0.1';
$dbname = 'tenant_demo-restaurant'; // Tenant database
$username = 'root'; // Update if needed
$password = ''; // Update if needed

try {
    // Connect to tenant database
    $pdo = new PDO("mysql:host=$host;dbname=$dbname", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    echo "✓ Connected to tenant database: $dbname\n";

    // Check if sessions table exists
    $stmt = $pdo->query("SHOW TABLES LIKE 'sessions'");
    $sessionTableExists = $stmt->rowCount() > 0;

    if (!$sessionTableExists) {
        echo "Creating sessions table...\n";
        
        // Create sessions table
        $createSessionsTable = "
            CREATE TABLE `sessions` (
                `id` varchar(255) NOT NULL,
                `user_id` bigint unsigned DEFAULT NULL,
                `ip_address` varchar(45) DEFAULT NULL,
                `user_agent` text,
                `payload` longtext NOT NULL,
                `last_activity` int NOT NULL,
                PRIMARY KEY (`id`),
                KEY `sessions_user_id_index` (`user_id`),
                KEY `sessions_last_activity_index` (`last_activity`)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        ";
        
        $pdo->exec($createSessionsTable);
        echo "✓ Sessions table created\n";
    } else {
        echo "✓ Sessions table already exists\n";
    }

    // Check if cache table exists (also needed)
    $stmt = $pdo->query("SHOW TABLES LIKE 'cache'");
    $cacheTableExists = $stmt->rowCount() > 0;

    if (!$cacheTableExists) {
        echo "Creating cache table...\n";
        
        // Create cache table
        $createCacheTable = "
            CREATE TABLE `cache` (
                `key` varchar(255) NOT NULL,
                `value` mediumtext NOT NULL,
                `expiration` int NOT NULL,
                PRIMARY KEY (`key`)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        ";
        
        $pdo->exec($createCacheTable);
        echo "✓ Cache table created\n";
    } else {
        echo "✓ Cache table already exists\n";
    }

    // Check if cache_locks table exists
    $stmt = $pdo->query("SHOW TABLES LIKE 'cache_locks'");
    $cacheLocksTableExists = $stmt->rowCount() > 0;

    if (!$cacheLocksTableExists) {
        echo "Creating cache_locks table...\n";
        
        // Create cache_locks table
        $createCacheLocksTable = "
            CREATE TABLE `cache_locks` (
                `key` varchar(255) NOT NULL,
                `owner` varchar(255) NOT NULL,
                `expiration` int NOT NULL,
                PRIMARY KEY (`key`)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        ";
        
        $pdo->exec($createCacheLocksTable);
        echo "✓ Cache locks table created\n";
    } else {
        echo "✓ Cache locks table already exists\n";
    }

    // Check if jobs table exists
    $stmt = $pdo->query("SHOW TABLES LIKE 'jobs'");
    $jobsTableExists = $stmt->rowCount() > 0;

    if (!$jobsTableExists) {
        echo "Creating jobs table...\n";
        
        // Create jobs table
        $createJobsTable = "
            CREATE TABLE `jobs` (
                `id` bigint unsigned NOT NULL AUTO_INCREMENT,
                `queue` varchar(255) NOT NULL,
                `payload` longtext NOT NULL,
                `attempts` tinyint unsigned NOT NULL,
                `reserved_at` int unsigned DEFAULT NULL,
                `available_at` int unsigned NOT NULL,
                `created_at` int unsigned NOT NULL,
                PRIMARY KEY (`id`),
                KEY `jobs_queue_index` (`queue`)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        ";
        
        $pdo->exec($createJobsTable);
        echo "✓ Jobs table created\n";
    } else {
        echo "✓ Jobs table already exists\n";
    }

    // Show all tables in tenant database
    echo "\n=== Tenant Database Tables ===\n";
    $stmt = $pdo->query("SHOW TABLES");
    $tables = $stmt->fetchAll(PDO::FETCH_COLUMN);
    
    foreach ($tables as $table) {
        echo "  ✓ $table\n";
    }

    echo "\n✅ Sessions fix completed!\n";
    echo "You can now access: http://demo-restaurant.localhost:8000\n";

} catch (PDOException $e) {
    echo "❌ Database error: " . $e->getMessage() . "\n";
    
    if (strpos($e->getMessage(), 'Unknown database') !== false) {
        echo "\n🔧 The tenant database doesn't exist yet.\n";
        echo "Run this to create it:\n";
        echo "php fix_tenant_migrations.php\n";
    } else {
        echo "\nPlease check your database credentials:\n";
        echo "- Host: $host\n";
        echo "- Database: $dbname\n";
        echo "- Username: $username\n";
    }
}
