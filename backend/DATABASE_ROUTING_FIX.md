# 🔧 Database Routing Fix - Multi-Tenant Architecture

## ❌ Problem: `Table 'tenant_demo-restaurant.users' doesn't exist`

**Root Cause**: The tenancy middleware is incorrectly routing user authentication queries to the tenant database instead of the central database.

## ✅ Solution Applied

### 1. **User Model Fixed** ✅
- Added `CentralConnection` trait to `User` model
- Users will always be queried from central database

### 2. **Role & Permission Models Fixed** ✅
- Created custom `Role` and `Permission` models with `CentralConnection` trait
- Updated `config/permission.php` to use custom models
- Authentication system will use central database for roles/permissions

### 3. **Database Architecture Confirmed** ✅
```
Central Database (restaurant_management):
├── users (authentication)
├── roles (authorization)
├── permissions (authorization)
├── tenants (tenant management)
└── domains (subdomain routing)

Tenant Database (tenant_demo-restaurant):
├── sessions (tenant-specific sessions)
├── restaurants (restaurant data)
├── menu_items (restaurant content)
└── orders (restaurant operations)
```

## 🚀 **Apply the Fix**

### Method 1: Run Fix Script (Recommended)
```bash
php fix_database_routing.php
```

### Method 2: Manual Steps
```bash
# 1. Clear all caches
php artisan config:clear
php artisan cache:clear
php artisan route:clear

# 2. Test the fix
php artisan tinker --execute="
\$user = App\Models\User::find(1);
echo 'User connection: ' . \$user->getConnectionName();
"
```

## 🧪 **Verification Tests**

### Test 1: User Model Connection
```bash
php artisan tinker --execute="
\$user = App\Models\User::first();
echo 'User found: ' . \$user->name . PHP_EOL;
echo 'Connection: ' . \$user->getConnectionName() . PHP_EOL;
echo 'Should be: mysql (central)';
"
```

### Test 2: Tenant Context Test
```bash
php artisan tinker --execute="
\$tenant = App\Models\Tenant::find('demo-restaurant');
\$tenant->run(function () {
    \$user = App\Models\User::first();
    echo 'User in tenant context: ' . \$user->name . PHP_EOL;
    echo 'Connection: ' . \$user->getConnectionName() . PHP_EOL;
});
"
```

### Test 3: Dashboard Access
1. **Visit**: http://demo-restaurant.localhost:8000/login
2. **Login**: <EMAIL> / Manager@2024
3. **Expected**: Redirect to dashboard without database errors

## 📋 **What Was Fixed**

### Before (BROKEN):
```php
// User model queries went to tenant database
SQLSTATE[42S02]: Table 'tenant_demo-restaurant.users' doesn't exist
```

### After (FIXED):
```php
// User model always uses central database
class User extends Authenticatable
{
    use CentralConnection; // ✅ Always use central database
}
```

### Configuration Updates:
```php
// config/permission.php
'models' => [
    'permission' => App\Models\Permission::class, // ✅ Uses central DB
    'role' => App\Models\Role::class,             // ✅ Uses central DB
],
```

## 🎯 **Expected Behavior After Fix**

### ✅ Authentication Flow:
1. User visits tenant URL: `demo-restaurant.localhost:8000`
2. Tenancy middleware identifies tenant: `demo-restaurant`
3. User authentication queries central database: `restaurant_management.users`
4. Role/permission queries use central database: `restaurant_management.roles`
5. Restaurant data queries use tenant database: `tenant_demo-restaurant.restaurants`

### ✅ Database Queries:
```sql
-- ✅ CORRECT: User authentication (central database)
SELECT * FROM restaurant_management.users WHERE email = '<EMAIL>';

-- ✅ CORRECT: Restaurant data (tenant database)  
SELECT * FROM tenant_demo-restaurant.restaurants WHERE slug = 'demo-restaurant';
```

## 🔍 **Troubleshooting**

### Issue: Still getting users table error
**Solution**: Clear caches and restart server
```bash
php artisan optimize:clear
php artisan serve
```

### Issue: Permission errors
**Solution**: Clear permission cache
```bash
php artisan tinker --execute="
app()[\Spatie\Permission\PermissionRegistrar::class]->forgetCachedPermissions();
echo 'Permission cache cleared';
"
```

### Issue: Connection errors
**Solution**: Check database configuration
```bash
php artisan tinker --execute="
echo 'Central connection: ' . config('tenancy.database.central_connection');
echo PHP_EOL . 'Default connection: ' . config('database.default');
"
```

## ✅ **Success Indicators**

You'll know it's working when:

1. ✅ No "users table doesn't exist" errors
2. ✅ Login works at `demo-restaurant.localhost:8000/login`
3. ✅ Dashboard redirects work properly
4. ✅ User queries use central database
5. ✅ Restaurant queries use tenant database

## 🎉 **Final Test**

```bash
# Complete test sequence
php fix_database_routing.php

# Then test in browser:
# 1. http://demo-restaurant.localhost:8000/login
# 2. Login: <EMAIL> / Manager@2024
# 3. Should redirect to /manager/dashboard successfully
```

---

**🎊 The database routing is now properly configured for multi-tenant architecture!**

Users, roles, and permissions will always use the central database, while restaurant-specific data uses the tenant database. This maintains proper data isolation while ensuring authentication works correctly across all tenants.
