<?php

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;
use App\Http\Controllers\Api\DashboardApiController;

Route::get('/user', function (Request $request) {
    return $request->user();
})->middleware('auth:sanctum');

// Dashboard API Routes
Route::middleware(['auth:sanctum'])->group(function () {
    // Real-time dashboard data
    Route::get('/dashboard/data', [DashboardApiController::class, 'getDashboardData']);
    Route::get('/dashboard/orders/updates', [DashboardApiController::class, 'getOrderUpdates']);

    // Notifications
    Route::get('/notifications', [DashboardApiController::class, 'getNotifications']);
    Route::post('/notifications/{id}/read', [DashboardApiController::class, 'markNotificationRead']);
    Route::post('/notifications/read-all', [DashboardApiController::class, 'markAllNotificationsRead']);
});
