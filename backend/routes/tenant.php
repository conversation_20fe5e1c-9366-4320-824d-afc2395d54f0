<?php

declare(strict_types=1);

use Illuminate\Support\Facades\Route;
use Stancl\Tenancy\Middleware\InitializeTenancyByDomain;
use Stancl\Tenancy\Middleware\PreventAccessFromCentralDomains;
use App\Http\Controllers\Tenant\DashboardController;
use App\Http\Controllers\Tenant\RestaurantController;
use App\Http\Controllers\Tenant\CategoryController;
use App\Http\Controllers\Tenant\MenuItemController;
use App\Http\Controllers\Tenant\ComboController;
use App\Http\Controllers\Tenant\TableController;
use App\Http\Controllers\Tenant\OrderController;
use App\Http\Controllers\Tenant\CustomerController;
use App\Http\Controllers\Tenant\UnifiedStaffController;
use App\Http\Controllers\Tenant\ReportController;
use App\Http\Controllers\Tenant\SettingsController;
use App\Http\Controllers\Tenant\ReservationController;
use App\Http\Controllers\Tenant\WaitlistController;
use App\Http\Controllers\Tenant\DeliveryController;
use App\Http\Controllers\Tenant\DeliveryZoneController;
use App\Http\Controllers\Tenant\DeliveryPersonnelController;
use App\Http\Controllers\Tenant\ExpenseController;
use App\Http\Controllers\Tenant\ExpenseCategoryController;
use App\Http\Controllers\Tenant\VendorController;
use App\Http\Controllers\Tenant\PaymentMethodController;
use App\Http\Controllers\Tenant\BudgetController;
use App\Http\Controllers\Tenant\InventoryController;
use App\Http\Controllers\Tenant\InventoryCategoryController;
use App\Http\Controllers\Tenant\PurchaseOrderController;
use App\Http\Controllers\Tenant\WasteController;
use App\Http\Controllers\Tenant\DepartmentController;
use App\Http\Controllers\Tenant\EmployeeController;
use App\Http\Controllers\Tenant\ShiftController;
use App\Http\Controllers\Tenant\TimeEntryController;
use App\Http\Controllers\Tenant\GuestController;

/*
|--------------------------------------------------------------------------
| Tenant Routes
|--------------------------------------------------------------------------
|
| Here you can register the tenant routes for your application.
| These routes are loaded by the TenantRouteServiceProvider.
|
| Feel free to customize them however you want. Good luck!
|
*/

Route::middleware([
    'web',
    InitializeTenancyByDomain::class,
    PreventAccessFromCentralDomains::class,
    \App\Http\Middleware\SetTenantLocale::class,
])->group(function () {

    // Guest Routes (Public Restaurant Page)

    // Authentication Routes for Tenants
    Route::get('/login', [App\Http\Controllers\Tenant\Auth\LoginController::class, 'showLoginForm'])->name('login');
    Route::post('/login', [App\Http\Controllers\Tenant\Auth\LoginController::class, 'login']);
    Route::post('/logout', [App\Http\Controllers\Tenant\Auth\LoginController::class, 'logout'])->name('logout');

    // Test route to verify tenant authentication
    Route::get('/test-auth', function () {
        return response()->json([
            'tenant' => [
                'id' => tenant('id'),
                'name' => tenant('name'),
            ],
            'auth_guard' => 'tenant',
            'user_model' => \App\Models\Tenant\User::class,
            'users_count' => \App\Models\Tenant\User::count(),
            'sample_users' => \App\Models\Tenant\User::select('id', 'name', 'email', 'role')->limit(3)->get(),
        ]);
    })->name('test.auth');

    // Language Routes (available to all users)
    Route::put('/user/language', [App\Http\Controllers\LanguageController::class, 'updateLanguage'])->name('user.language.update');
    Route::get('/languages', [App\Http\Controllers\LanguageController::class, 'getLanguages'])->name('languages.index');

    // Debug authentication flow
    Route::get('/debug-auth', function () {
        return response()->json([
            'tenant_context' => tenant() ? [
                'id' => tenant('id'),
                'name' => tenant('name'),
            ] : null,
            'auth_check' => auth('tenant')->check(),
            'auth_user' => auth('tenant')->user() ? [
                'id' => auth('tenant')->user()->id,
                'email' => auth('tenant')->user()->email,
                'role' => auth('tenant')->user()->role,
                'email_verified_at' => auth('tenant')->user()->email_verified_at,
                'is_active' => auth('tenant')->user()->is_active,
            ] : null,
            'session_data' => [
                'session_id' => session()->getId(),
                'auth_session_key' => session()->get('login_tenant_' . sha1(static::class)),
                'all_session_keys' => array_keys(session()->all()),
            ],
            'guard_info' => [
                'guard_name' => 'tenant',
                'provider' => config('auth.guards.tenant.provider'),
                'driver' => config('auth.guards.tenant.driver'),
            ],
        ]);
    })->name('debug.auth');

    // Simple auth test without middleware
    Route::get('/simple-auth-test', function () {
        $user = auth('tenant')->user();

        if (!$user) {
            return response()->json([
                'authenticated' => false,
                'message' => 'Not authenticated',
                'session_id' => session()->getId(),
                'tenant' => tenant() ? tenant('id') : null,
            ]);
        }

        return response()->json([
            'authenticated' => true,
            'user' => [
                'id' => $user->id,
                'email' => $user->email,
                'role' => $user->role,
                'email_verified' => $user->hasVerifiedEmail(),
            ],
            'tenant' => tenant() ? tenant('id') : null,
            'session_id' => session()->getId(),
        ]);
    })->name('simple.auth.test');

    // Test waiter dashboard relationships
    Route::get('/test-waiter-relationships', function () {
        try {
            $tables = \App\Models\Tenant\Table::with(['currentOrder', 'reservation'])->limit(5)->get();

            return response()->json([
                'success' => true,
                'tables_count' => $tables->count(),
                'tables' => $tables->map(function ($table) {
                    return [
                        'id' => $table->id,
                        'name' => $table->name,
                        'status' => $table->status,
                        'assigned_waiter_id' => $table->assigned_waiter_id,
                        'current_party_size' => $table->current_party_size,
                        'occupied_at' => $table->occupied_at,
                        'has_current_order' => $table->currentOrder ? true : false,
                        'has_reservation' => $table->reservation ? true : false,
                    ];
                }),
                'relationships_working' => true,
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ], 500);
        }
    })->name('test.waiter.relationships');

    // Test waiter dashboard data loading
    Route::get('/test-waiter-dashboard-data', function () {
        try {
            $waiter = auth('tenant')->user();

            if (!$waiter) {
                return response()->json([
                    'success' => false,
                    'error' => 'Not authenticated',
                ], 401);
            }

            // Test each data query separately
            $assignedTables = \App\Models\Tenant\Table::with(['currentOrder', 'reservation'])->limit(3)->get();

            $activeOrders = \App\Models\Tenant\Order::where('assigned_to', $waiter->id)
                ->whereIn('status', ['pending', 'confirmed', 'preparing', 'ready'])
                ->with(['customer', 'table', 'items.food'])
                ->orderBy('created_at')
                ->limit(3)
                ->get();

            $menuItems = \App\Models\Tenant\Food::with(['category', 'variants', 'addons'])
                ->active()
                ->available()
                ->orderBy('sort_order')
                ->limit(5)
                ->get()
                ->groupBy(function ($item) {
                    return $item->category ? $item->category->name : 'Uncategorized';
                });

            return response()->json([
                'success' => true,
                'waiter_id' => $waiter->id,
                'assigned_tables_count' => $assignedTables->count(),
                'active_orders_count' => $activeOrders->count(),
                'menu_categories_count' => $menuItems->count(),
                'menu_items_total' => $menuItems->flatten()->count(),
                'data_loading_working' => true,
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ], 500);
        }
    })->middleware('auth:tenant')->name('test.waiter.dashboard.data');

    // Test basic data queries without authentication
    Route::get('/test-basic-queries', function () {
        try {
            // Test basic table query
            $tablesCount = \App\Models\Tenant\Table::count();

            // Check what tables exist in the database
            $databaseTables = DB::select('SHOW TABLES');
            $tableNames = array_map(function($table) {
                $tableName = 'Tables_in_tenant_' . tenant('id');
                return $table->$tableName ?? 'unknown';
            }, $databaseTables);

            return response()->json([
                'success' => true,
                'tables_count' => $tablesCount,
                'database_tables' => $tableNames,
                'tenant_id' => tenant('id'),
                'basic_queries_working' => true,
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ], 500);
        }
    })->name('test.basic.queries');













    // Test waiter dashboard data with MenuItem
    Route::get('/test-waiter-dashboard-final', function () {
        try {
            $waiter = auth('tenant')->user();

            if (!$waiter) {
                return response()->json([
                    'success' => false,
                    'error' => 'Not authenticated',
                ], 401);
            }

            // Test MenuItem query
            $menuItems = \App\Models\Tenant\MenuItem::with(['category', 'variations', 'addons'])
                ->available()
                ->orderBy('sort_order')
                ->limit(5)
                ->get()
                ->groupBy(function ($item) {
                    return $item->category ? $item->category->name : 'Uncategorized';
                });

            // Test Table query
            $tables = \App\Models\Tenant\Table::with(['currentOrder', 'reservation'])->limit(3)->get();

            return response()->json([
                'success' => true,
                'waiter_id' => $waiter->id,
                'menu_categories_count' => $menuItems->count(),
                'menu_items_total' => $menuItems->flatten()->count(),
                'tables_count' => $tables->count(),
                'sample_menu_item' => $menuItems->flatten()->first() ? [
                    'id' => $menuItems->flatten()->first()->id,
                    'name' => $menuItems->flatten()->first()->name,
                    'category' => $menuItems->flatten()->first()->category ? $menuItems->flatten()->first()->category->name : null,
                    'variations_count' => $menuItems->flatten()->first()->variations->count(),
                    'addons_count' => $menuItems->flatten()->first()->addons->count(),
                ] : null,
                'waiter_dashboard_ready' => true,
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ], 500);
        }
    })->middleware('auth:tenant')->name('test.waiter.dashboard.final');



    // Authenticated Routes
    Route::middleware(['auth:tenant', 'tenant.verified'])->group(function () {

        // Simple test dashboard without any redirects
        Route::get('/test-dashboard', function () {
            return response()->json([
                'success' => true,
                'message' => 'Dashboard accessible!',
                'user' => auth('tenant')->user() ? [
                    'id' => auth('tenant')->user()->id,
                    'email' => auth('tenant')->user()->email,
                    'role' => auth('tenant')->user()->role,
                ] : null,
                'tenant' => tenant() ? tenant('id') : null,
            ]);
        })->name('test.dashboard');

        // Dashboard - Generic (will redirect based on role)
        Route::get('/dashboard', [DashboardController::class, 'index'])->name('dashboard');
        Route::get('/dashboard/stats', [DashboardController::class, 'stats'])->name('dashboard.stats');

        // Role-based Dashboards
        Route::middleware(['role:admin'])->group(function () {
            Route::get('/admin/dashboard', [App\Http\Controllers\Tenant\AdminDashboardController::class, 'index'])->name('admin.dashboard');
        });
        //role:restaurant_manager
        Route::middleware(['auth'])->group(function () {
            Route::get('/manager/dashboard', [App\Http\Controllers\Tenant\ManagerDashboardController::class, 'index'])->name('manager.dashboard');
            Route::get('/manager/analytics', [App\Http\Controllers\Tenant\ManagerDashboardController::class, 'analytics'])->name('manager.analytics');

            // Manager Space Management Routes
            Route::prefix('manager')->name('manager.')->group(function () {
                // Branches (using Inertia controller)
                Route::resource('branches', App\Http\Controllers\Tenant\BranchController::class);
                Route::post('branches/{branch}/toggle-status', [App\Http\Controllers\Tenant\BranchController::class, 'toggleStatus'])->name('branches.toggle-status');

                // Floors (using Inertia controller)
                Route::resource('floors', App\Http\Controllers\FloorController::class);
                Route::post('floors/{floor}/toggle-status', [App\Http\Controllers\FloorController::class, 'toggleStatus'])->name('floors.toggle-status');
                Route::get('api/floors/by-branch/{branchId}', [App\Http\Controllers\FloorController::class, 'getByBranch'])->name('floors.by-branch');

                // Tables (using existing Inertia controller)
                Route::resource('tables', App\Http\Controllers\Tenant\TableController::class);
                Route::get('tables-layout', [App\Http\Controllers\Tenant\TableController::class, 'layout'])->name('tables.layout');
                Route::post('tables/{table}/toggle-status', [App\Http\Controllers\Tenant\TableController::class, 'toggleStatus'])->name('tables.toggle-status');
                Route::get('tables/{table}/qr-code', [App\Http\Controllers\Tenant\TableController::class, 'generateQrCode'])->name('tables.qr-code');
                Route::post('tables/update-layout', [App\Http\Controllers\Tenant\TableController::class, 'updateLayout'])->name('tables.update-layout');
                Route::post('tables/bulk-update', [App\Http\Controllers\Tenant\TableController::class, 'bulkUpdate'])->name('tables.bulk-update');
            });
        });

        Route::middleware(['role:waiter'])->group(function () {
            Route::get('/waiter/dashboard', [App\Http\Controllers\Tenant\WaiterDashboardController::class, 'index'])->name('waiter.dashboard');
            Route::get('/waiter/tables', [App\Http\Controllers\Tenant\WaiterDashboardController::class, 'tables'])->name('waiter.tables');

            // Waiter Order Management (reusing POS system)
            Route::get('/waiter/orders', [App\Http\Controllers\Tenant\WaiterDashboardController::class, 'orders'])->name('waiter.orders');
            Route::get('/waiter/orders/create', [App\Http\Controllers\Tenant\WaiterDashboardController::class, 'createOrder'])->name('waiter.orders.create');
            Route::post('/waiter/orders', [App\Http\Controllers\Tenant\POSController::class, 'createOrder'])->name('waiter.orders.store');
            Route::post('/waiter/orders/{order}/add-item', [App\Http\Controllers\Tenant\POSController::class, 'addItem'])->name('waiter.orders.add-item');
            Route::put('/waiter/order-items/{orderItem}/quantity', [App\Http\Controllers\Tenant\POSController::class, 'updateItemQuantity'])->name('waiter.order-items.update-quantity');
            Route::delete('/waiter/order-items/{orderItem}', [App\Http\Controllers\Tenant\POSController::class, 'removeItem'])->name('waiter.order-items.remove');
            Route::get('/waiter/orders/{order}', [App\Http\Controllers\Tenant\WaiterDashboardController::class, 'orderDetails'])->name('waiter.order-details');
            Route::post('/waiter/orders/{order}/status', [App\Http\Controllers\Tenant\WaiterDashboardController::class, 'updateOrderStatus'])->name('waiter.update-order-status');
            Route::post('/waiter/orders/{order}/send-to-kitchen', [App\Http\Controllers\Tenant\POSController::class, 'sendToKitchen'])->name('waiter.orders.send-to-kitchen');
            Route::post('/waiter/orders/{order}/payment', [App\Http\Controllers\Tenant\POSController::class, 'processPayment'])->name('waiter.orders.payment');

            Route::get('/waiter/menu', [App\Http\Controllers\Tenant\WaiterDashboardController::class, 'menu'])->name('waiter.menu');
            Route::get('/waiter/customers', [App\Http\Controllers\Tenant\WaiterDashboardController::class, 'customers'])->name('waiter.customers');
            Route::get('/waiter/reservations', [App\Http\Controllers\Tenant\WaiterDashboardController::class, 'reservations'])->name('waiter.reservations');
            Route::get('/waiter/take-order', [App\Http\Controllers\Tenant\WaiterDashboardController::class, 'takeOrder'])->name('waiter.take-order');
            Route::get('/waiter/customers/{customer}', [App\Http\Controllers\Tenant\WaiterDashboardController::class, 'customerInfo'])->name('waiter.customer-info');
            Route::post('/waiter/tables/{table}/assign', [App\Http\Controllers\Tenant\WaiterDashboardController::class, 'assignTable'])->name('waiter.assign-table');
            Route::post('/waiter/tables/{table}/clear', [App\Http\Controllers\Tenant\WaiterDashboardController::class, 'clearTable'])->name('waiter.clear-table');
        });

        Route::middleware(['role:kitchen'])->group(function () {
            Route::get('/kitchen/dashboard', [App\Http\Controllers\Tenant\KitchenDashboardController::class, 'index'])->name('kitchen.dashboard');
            Route::get('/kitchen/display', [App\Http\Controllers\Tenant\KitchenDashboardController::class, 'kitchenDisplay'])->name('kitchen.display');
            Route::post('/kitchen/items/{orderItem}/start', [App\Http\Controllers\Tenant\KitchenDashboardController::class, 'startPreparation'])->name('kitchen.start-preparation');
            Route::post('/kitchen/items/{orderItem}/ready', [App\Http\Controllers\Tenant\KitchenDashboardController::class, 'markItemReady'])->name('kitchen.mark-ready');
            Route::get('/kitchen/orders/{order}', [App\Http\Controllers\Tenant\KitchenDashboardController::class, 'orderDetails'])->name('kitchen.order-details');
            Route::post('/kitchen/items/bulk-update', [App\Http\Controllers\Tenant\KitchenDashboardController::class, 'bulkUpdateItems'])->name('kitchen.bulk-update');
        });

        Route::middleware(['role:delivery'])->group(function () {
            Route::get('/delivery/dashboard', [App\Http\Controllers\Tenant\DeliveryDashboardController::class, 'index'])->name('delivery.dashboard');
            Route::get('/delivery/history', [App\Http\Controllers\Tenant\DeliveryDashboardController::class, 'deliveryHistory'])->name('delivery.history');
            Route::get('/delivery/earnings', [App\Http\Controllers\Tenant\DeliveryDashboardController::class, 'earnings'])->name('delivery.earnings');
            Route::post('/delivery/orders/{deliveryOrder}/accept', [App\Http\Controllers\Tenant\DeliveryDashboardController::class, 'acceptDelivery'])->name('delivery.accept');
            Route::post('/delivery/orders/{deliveryOrder}/pickup', [App\Http\Controllers\Tenant\DeliveryDashboardController::class, 'markPickedUp'])->name('delivery.pickup');
            Route::post('/delivery/orders/{deliveryOrder}/deliver', [App\Http\Controllers\Tenant\DeliveryDashboardController::class, 'markDelivered'])->name('delivery.deliver');
            Route::post('/delivery/orders/{deliveryOrder}/fail', [App\Http\Controllers\Tenant\DeliveryDashboardController::class, 'markFailed'])->name('delivery.fail');
            Route::post('/delivery/location/update', [App\Http\Controllers\Tenant\DeliveryDashboardController::class, 'updateLocation'])->name('delivery.update-location');
        });

        // Settings Routes (available to all authenticated users)
        Route::prefix('settings')->name('settings.')->group(function () {
            Route::get('/', [App\Http\Controllers\SettingsController::class, 'index'])->name('index');
            Route::post('/profile', [App\Http\Controllers\SettingsController::class, 'updateProfile'])->name('profile.update');
            Route::post('/password', [App\Http\Controllers\SettingsController::class, 'updatePassword'])->name('password.update');
            Route::post('/preferences', [App\Http\Controllers\SettingsController::class, 'updatePreferences'])->name('preferences.update');
            Route::post('/notifications', [App\Http\Controllers\SettingsController::class, 'updateNotificationPreferences'])->name('notifications.update');
            Route::post('/avatar', [App\Http\Controllers\SettingsController::class, 'updateAvatar'])->name('avatar.update');
            Route::get('/activity', [App\Http\Controllers\SettingsController::class, 'getActivityLog'])->name('activity');
            Route::get('/export', [App\Http\Controllers\SettingsController::class, 'exportData'])->name('export');
            Route::delete('/account', [App\Http\Controllers\SettingsController::class, 'deleteAccount'])->name('account.delete');
            Route::get('/security', [App\Http\Controllers\SettingsController::class, 'getSecuritySettings'])->name('security');
            Route::post('/two-factor', [App\Http\Controllers\SettingsController::class, 'toggleTwoFactor'])->name('two-factor.toggle');
        });

        // Restaurant Management
        Route::prefix('restaurant')->name('restaurant.')->group(function () {
            Route::get('/', [RestaurantController::class, 'index'])->name('index');
            Route::post('/profile', [RestaurantController::class, 'updateProfile'])->name('profile.update');
            Route::post('/settings', [RestaurantController::class, 'updateSettings'])->name('settings.update');
            Route::post('/status', [RestaurantController::class, 'updateStatus'])->name('status.update');
            Route::get('/analytics', [RestaurantController::class, 'analytics'])->name('analytics');
            Route::post('/export', [RestaurantController::class, 'exportData'])->name('export');
            Route::get('/qr-code', [RestaurantController::class, 'getQRCode'])->name('qr-code');
            Route::post('/branding', [RestaurantController::class, 'updateBranding'])->name('branding.update');
            Route::get('/stats', [RestaurantController::class, 'getStats'])->name('stats');
        });

        // Category Management
        Route::resource('categories', CategoryController::class);
        Route::post('categories/{category}/toggle-status', [CategoryController::class, 'toggleStatus'])->name('categories.toggle-status');
        Route::post('categories/bulk-update', [CategoryController::class, 'bulkUpdate'])->name('categories.bulk-update');

        // Subcategory Management
        Route::resource('subcategories', App\Http\Controllers\Tenant\SubcategoryController::class);
        Route::get('categories/{category}/subcategories', [App\Http\Controllers\Tenant\SubcategoryController::class, 'getByCategory'])->name('subcategories.by-category');
        Route::post('subcategories/{subcategory}/toggle-status', [App\Http\Controllers\Tenant\SubcategoryController::class, 'toggleStatus'])->name('subcategories.toggle-status');
        Route::post('subcategories/bulk-update', [App\Http\Controllers\Tenant\SubcategoryController::class, 'bulkUpdate'])->name('subcategories.bulk-update');

        // Menu Item Management
        Route::resource('menu-items', MenuItemController::class);
        Route::post('menu-items/{menuItem}/toggle-status', [MenuItemController::class, 'toggleStatus'])->name('menu-items.toggle-status');
        Route::post('menu-items/{menuItem}/toggle-availability', [MenuItemController::class, 'toggleAvailability'])->name('menu-items.toggle-availability');
        Route::post('menu-items/bulk-update', [MenuItemController::class, 'bulkUpdate'])->name('menu-items.bulk-update');
        Route::post('menu-items/{menuItem}/duplicate', [MenuItemController::class, 'duplicate'])->name('menu-items.duplicate');

        // Combo Menu Management (using MenuItem model)
        // Route::resource('combo-menus', ComboController::class);
        Route::post('combo-menus/{comboMenu}/toggle-availability', [ComboController::class, 'toggleAvailability'])->name('combo-menus.toggle-availability');

        // Combined Menu View (for customers)
        Route::get('/item', [MenuItemController::class, 'publicIndex'])->name('menu.public');

        // Menu Item Addons
        Route::resource('menu-item-addons', App\Http\Controllers\Tenant\MenuItemAddonController::class);
        Route::post('menu-item-addons/{menuItemAddon}/toggle-availability', [App\Http\Controllers\Tenant\MenuItemAddonController::class, 'toggleAvailability'])->name('menu-item-addons.toggle-availability');
        Route::post('menu-item-addons/bulk-toggle-availability', [App\Http\Controllers\Tenant\MenuItemAddonController::class, 'bulkToggleAvailability'])->name('menu-item-addons.bulk-toggle-availability');
        Route::get('menu-item-addons-api', [App\Http\Controllers\Tenant\MenuItemAddonController::class, 'getAddons'])->name('menu-item-addons.api');

        // Combo Menus
        Route::resource('combo-menus', App\Http\Controllers\Tenant\ComboMenuController::class);
        Route::post('combo-menus/{comboMenu}/toggle-availability', [App\Http\Controllers\Tenant\ComboMenuController::class, 'toggleAvailability'])->name('combo-menus.toggle-availability');
        Route::get('combo-menus-api', [App\Http\Controllers\Tenant\ComboMenuController::class, 'getAvailableCombos'])->name('combo-menus.api');



        // Floor Management
        Route::resource('floors', App\Http\Controllers\FloorController::class);
        Route::post('floors/{floor}/toggle-status', [App\Http\Controllers\FloorController::class, 'toggleStatus'])->name('floors.toggle-status');
        Route::get('floors/by-branch/{branchId}', [App\Http\Controllers\FloorController::class, 'getByBranch'])->name('floors.by-branch');

        // Table Management (DEPRECATED - now handled in Manager namespace above)

        // Reservation Management
        Route::resource('reservations', ReservationController::class);
        Route::get('reservations-calendar', [ReservationController::class, 'calendar'])->name('reservations.calendar');
        Route::post('reservations/{reservation}/confirm', [ReservationController::class, 'confirm'])->name('reservations.confirm');
        Route::post('reservations/{reservation}/check-in', [ReservationController::class, 'checkIn'])->name('reservations.check-in');
        Route::post('reservations/{reservation}/complete', [ReservationController::class, 'complete'])->name('reservations.complete');
        Route::post('reservations/{reservation}/cancel', [ReservationController::class, 'cancel'])->name('reservations.cancel');
        Route::post('reservations/{reservation}/no-show', [ReservationController::class, 'noShow'])->name('reservations.no-show');
        Route::get('reservations/available-slots', [ReservationController::class, 'availableSlots'])->name('reservations.available-slots');

        // Waitlist Management
        Route::resource('waitlist', WaitlistController::class);
        Route::get('waitlist-board', [WaitlistController::class, 'board'])->name('waitlist.board');
        Route::post('waitlist/{waitlist}/notify', [WaitlistController::class, 'notify'])->name('waitlist.notify');
        Route::post('waitlist/{waitlist}/seat', [WaitlistController::class, 'seat'])->name('waitlist.seat');
        Route::post('waitlist/{waitlist}/mark-left', [WaitlistController::class, 'markLeft'])->name('waitlist.mark-left');
        Route::post('waitlist/{waitlist}/mark-no-show', [WaitlistController::class, 'markNoShow'])->name('waitlist.mark-no-show');
        Route::post('waitlist/update-wait-times', [WaitlistController::class, 'updateWaitTimes'])->name('waitlist.update-wait-times');
        Route::get('waitlist/next-customer', [WaitlistController::class, 'nextCustomer'])->name('waitlist.next-customer');
        Route::get('waitlist/available-tables', [WaitlistController::class, 'availableTables'])->name('waitlist.available-tables');

        // Simple test route to verify POS access (remove in production)
        Route::get('/test-pos', function () {
            return Inertia::render('Tenant/POS/TableView', [
                'branch' => (object)[
                    'id' => 1,
                    'name' => 'Test Branch',
                    'floors' => []
                ],
                'floors' => []
            ]);
        })->name('test.pos');

        // Debug route for branch access (remove in production)
        Route::get('/debug/branch-access', function () {
            $user = auth()->user();
            if (!$user) {
                return response()->json(['error' => 'Not authenticated']);
            }

            $stats = \App\Services\BranchSelectionService::getCacheStats();
            $branchData = \App\Services\BranchSelectionService::getBranchDataForController();
            $accessCheck = \App\Services\BranchSelectionService::ensureBranchAccess();

            return response()->json([
                'user' => [
                    'id' => $user->id,
                    'name' => $user->name,
                    'email' => $user->email,
                    'roles' => $user->getRoleNames(),
                ],
                'tenant' => [
                    'id' => tenant('id'),
                    'name' => tenant('name'),
                ],
                'branches' => [
                    'all_branches' => \App\Models\Tenant\Branch::all(),
                    'active_branches' => \App\Models\Tenant\Branch::active()->get(),
                ],
                'employee' => \App\Models\Tenant\Employee::where('user_id', $user->id)->first(),
                'cache_stats' => $stats,
                'branch_data' => $branchData,
                'access_check' => $accessCheck,
            ]);
        })->name('debug.branch-access');

        // POS System (temporarily disabled middleware for debugging)
        Route::prefix('pos')->name('pos.')->group(function () {
            Route::get('/', [App\Http\Controllers\Tenant\POSController::class, 'index'])->name('index');
            Route::get('/table-view', [App\Http\Controllers\Tenant\POSController::class, 'tableView'])->name('table-view');
            Route::get('/ongoing-orders', [App\Http\Controllers\Tenant\POSController::class, 'ongoingOrders'])->name('ongoing-orders');
            Route::post('/switch-branch', [App\Http\Controllers\Tenant\POSController::class, 'switchBranch'])->name('switch-branch');

            // Order management
            Route::post('/orders', [App\Http\Controllers\Tenant\POSController::class, 'createOrder'])->name('orders.create');
            Route::post('/orders/delivery', [App\Http\Controllers\Tenant\POSController::class, 'createDeliveryOrder'])->name('orders.create-delivery');
            Route::post('/orders/{order}/add-item', [App\Http\Controllers\Tenant\POSController::class, 'addItem'])->name('orders.add-item');
            Route::put('/order-items/{orderItem}/quantity', [App\Http\Controllers\Tenant\POSController::class, 'updateItemQuantity'])->name('order-items.update-quantity');
            Route::delete('/order-items/{orderItem}', [App\Http\Controllers\Tenant\POSController::class, 'removeItem'])->name('order-items.remove');

            // Discounts and payments
            Route::post('/orders/{order}/discount', [App\Http\Controllers\Tenant\POSController::class, 'applyDiscount'])->name('orders.apply-discount');
            Route::post('/orders/{order}/payment', [App\Http\Controllers\Tenant\POSController::class, 'processPayment'])->name('orders.process-payment');

            // Order actions
            Route::post('/orders/{order}/send-to-kitchen', [App\Http\Controllers\Tenant\POSController::class, 'sendToKitchen'])->name('orders.send-to-kitchen');
            Route::post('/orders/{order}/close', [App\Http\Controllers\Tenant\POSController::class, 'closeOrder'])->name('orders.close');
            Route::get('/orders/{order}/summary', [App\Http\Controllers\Tenant\POSController::class, 'getOrderSummary'])->name('orders.summary');

            // Order management actions
            Route::get('/orders/{order}', [App\Http\Controllers\Tenant\POSController::class, 'showOrder'])->name('orders.show');
            Route::put('/orders/{order}/status', [App\Http\Controllers\Tenant\POSController::class, 'updateOrderStatus'])->name('orders.update-status');
            Route::delete('/orders/{order}', [App\Http\Controllers\Tenant\POSController::class, 'deleteOrder'])->name('orders.delete');
            Route::get('/orders/{order}/edit', [App\Http\Controllers\Tenant\POSController::class, 'editOrder'])->name('orders.edit');
            Route::get('/orders/{order}/print', [App\Http\Controllers\Tenant\POSController::class, 'printOrder'])->name('orders.print');
            Route::post('/orders/{order}/process-payment', [App\Http\Controllers\Tenant\POSController::class, 'processOrderPayment'])->name('orders.process-payment');
        });

        // Kitchen Display System (temporarily disabled middleware for debugging)
        Route::prefix('kitchen')->name('kitchen.')->group(function () {
            Route::get('/', [App\Http\Controllers\Tenant\KitchenController::class, 'index'])->name('index');
            Route::get('/orders/{order}', [App\Http\Controllers\Tenant\KitchenController::class, 'showOrder'])->name('orders.show');
            Route::put('/orders/{order}/status', [App\Http\Controllers\Tenant\KitchenController::class, 'updateOrderStatus'])->name('orders.update-status');
            Route::put('/order-items/{orderItem}/status', [App\Http\Controllers\Tenant\KitchenController::class, 'updateItemStatus'])->name('order-items.update-status');
            Route::post('/orders/{order}/start-preparing', [App\Http\Controllers\Tenant\KitchenController::class, 'startPreparing'])->name('orders.start-preparing');
            Route::post('/orders/{order}/mark-ready', [App\Http\Controllers\Tenant\KitchenController::class, 'markReady'])->name('orders.mark-ready');
            Route::post('/orders/{order}/mark-served', [App\Http\Controllers\Tenant\KitchenController::class, 'markServed'])->name('orders.mark-served');
            Route::get('/queue', [App\Http\Controllers\Tenant\KitchenController::class, 'getQueue'])->name('queue');
        });

        // Order Management
        Route::resource('orders', OrderController::class);
        Route::post('orders/{order}/update-status', [OrderController::class, 'updateStatus'])->name('orders.update-status');
        Route::post('orders/{order}/update-payment', [OrderController::class, 'updatePayment'])->name('orders.update-payment');
        Route::get('orders/{order}/invoice', [OrderController::class, 'invoice'])->name('orders.invoice');
        Route::get('orders/{order}/print', [OrderController::class, 'print'])->name('orders.print');

        // Kitchen Routes
        Route::prefix('kitchen')->name('kitchen.')->group(function () {
            Route::get('/', [OrderController::class, 'kitchen'])->name('index');
            Route::post('orders/{order}/start-preparation', [OrderController::class, 'startPreparation'])->name('orders.start-preparation');
            Route::post('orders/{order}/mark-ready', [OrderController::class, 'markReady'])->name('orders.mark-ready');
        });

        // Customer Management
        Route::resource('customers', CustomerController::class);
        Route::post('customers/api/store', [CustomerController::class, 'storeApi'])->name('customers.api.store');
        Route::get('customers/{customer}/orders', [CustomerController::class, 'orders'])->name('customers.orders');

        // Unified Staff Management
        Route::prefix('staff')->name('staff.')->group(function () {
            Route::get('/', [UnifiedStaffController::class, 'index'])->name('index');
            Route::get('/create', [UnifiedStaffController::class, 'create'])->name('create');
            Route::post('/', [UnifiedStaffController::class, 'store'])->name('store');
            Route::get('/{staff}', [UnifiedStaffController::class, 'show'])->name('show');
            Route::get('/{staff}/edit', [UnifiedStaffController::class, 'edit'])->name('edit');
            Route::put('/{staff}', [UnifiedStaffController::class, 'update'])->name('update');
            Route::delete('/{staff}', [UnifiedStaffController::class, 'destroy'])->name('destroy');
            Route::post('/{staff}/toggle-status', [UnifiedStaffController::class, 'toggleStatus'])->name('toggle-status');
            Route::post('/{staff}/reset-password', [UnifiedStaffController::class, 'resetPassword'])->name('reset-password');
        });

        // Reports
        Route::prefix('reports')->name('reports.')->group(function () {
            Route::get('/', [ReportController::class, 'index'])->name('index');
            Route::get('/sales', [ReportController::class, 'salesReport'])->name('sales');
            Route::get('/inventory', [ReportController::class, 'inventoryReport'])->name('inventory');
            Route::get('/staff', [ReportController::class, 'staffReport'])->name('staff');
            Route::get('/customer', [ReportController::class, 'customerReport'])->name('customer');
            Route::get('/financial', [ReportController::class, 'financialReport'])->name('financial');
            Route::get('/operational', [ReportController::class, 'operationalReport'])->name('operational');
            Route::post('/export', [ReportController::class, 'exportReport'])->name('export');
            Route::post('/schedule', [ReportController::class, 'scheduleReport'])->name('schedule');
            Route::get('/custom-builder', [ReportController::class, 'customReportBuilder'])->name('custom-builder');
            Route::post('/custom-generate', [ReportController::class, 'generateCustomReport'])->name('custom-generate');
        });

        // Delivery System
        Route::prefix('delivery')->name('delivery.')->group(function () {
            Route::get('/', [DeliveryController::class, 'index'])->name('index');
            Route::get('/map', [DeliveryController::class, 'map'])->name('map');
            Route::post('/orders/{order}/assign-delivery-person', [DeliveryController::class, 'assignDeliveryPerson'])->name('assign-delivery-person');
            Route::post('/orders/{order}/auto-assign', [DeliveryController::class, 'autoAssignDeliveryPerson'])->name('auto-assign');
            Route::post('/orders/{order}/mark-picked-up', [DeliveryController::class, 'markPickedUp'])->name('mark-picked-up');
            Route::post('/orders/{order}/mark-delivered', [DeliveryController::class, 'markDelivered'])->name('mark-delivered');
            Route::post('/create-routes', [DeliveryController::class, 'createRoutes'])->name('create-routes');
            Route::post('/routes/{route}/start', [DeliveryController::class, 'startRoute'])->name('start-route');
            Route::post('/routes/{route}/complete', [DeliveryController::class, 'completeRoute'])->name('complete-route');
            Route::post('/personnel/{deliveryPersonnel}/update-location', [DeliveryController::class, 'updateLocation'])->name('update-location');
            Route::get('/zone-for-address', [DeliveryController::class, 'getZoneForAddress'])->name('zone-for-address');
            Route::get('/calculate-delivery', [DeliveryController::class, 'calculateDelivery'])->name('calculate-delivery');
        });

        // Delivery Zones
        Route::resource('delivery-zones', DeliveryZoneController::class);
        Route::get('delivery-zones-map', [DeliveryZoneController::class, 'map'])->name('delivery-zones.map');
        Route::post('delivery-zones/{deliveryZone}/toggle-status', [DeliveryZoneController::class, 'toggleStatus'])->name('delivery-zones.toggle-status');
        Route::post('delivery-zones/reorder', [DeliveryZoneController::class, 'reorder'])->name('delivery-zones.reorder');
        Route::post('delivery-zones/{deliveryZone}/test-coordinates', [DeliveryZoneController::class, 'testCoordinates'])->name('delivery-zones.test-coordinates');
        Route::get('delivery-zones-coverage', [DeliveryZoneController::class, 'coverage'])->name('delivery-zones.coverage');
        Route::post('delivery-zones/bulk-update', [DeliveryZoneController::class, 'bulkUpdate'])->name('delivery-zones.bulk-update');
        Route::get('delivery-zones/export/geojson', [DeliveryZoneController::class, 'exportGeoJson'])->name('delivery-zones.export-geojson');

        // Delivery Personnel
        Route::resource('delivery-personnel', DeliveryPersonnelController::class);
        Route::post('delivery-personnel/{deliveryPersonnel}/toggle-status', [DeliveryPersonnelController::class, 'toggleStatus'])->name('delivery-personnel.toggle-status');
        Route::post('delivery-personnel/{deliveryPersonnel}/update-status', [DeliveryPersonnelController::class, 'updateStatus'])->name('delivery-personnel.update-status');
        Route::get('delivery-personnel-available', [DeliveryPersonnelController::class, 'available'])->name('delivery-personnel.available');
        Route::post('delivery-personnel/bulk-update', [DeliveryPersonnelController::class, 'bulkUpdate'])->name('delivery-personnel.bulk-update');

        // Expense Management
        Route::resource('expenses', ExpenseController::class);
        Route::post('expenses/{expense}/approve', [ExpenseController::class, 'approve'])->name('expenses.approve');
        Route::post('expenses/{expense}/reject', [ExpenseController::class, 'reject'])->name('expenses.reject');
        Route::post('expenses/{expense}/mark-paid', [ExpenseController::class, 'markPaid'])->name('expenses.mark-paid');
        Route::post('expenses/{expense}/duplicate', [ExpenseController::class, 'duplicate'])->name('expenses.duplicate');
        Route::post('expenses/bulk-update', [ExpenseController::class, 'bulkUpdate'])->name('expenses.bulk-update');

        // Expense Categories
        Route::resource('expense-categories', ExpenseCategoryController::class);
        Route::post('expense-categories/{expenseCategory}/toggle-status', [ExpenseCategoryController::class, 'toggleStatus'])->name('expense-categories.toggle-status');
        Route::post('expense-categories/reorder', [ExpenseCategoryController::class, 'reorder'])->name('expense-categories.reorder');
        Route::post('expense-categories/bulk-update', [ExpenseCategoryController::class, 'bulkUpdate'])->name('expense-categories.bulk-update');
        Route::get('expense-categories-hierarchy', [ExpenseCategoryController::class, 'hierarchy'])->name('expense-categories.hierarchy');
        Route::get('expense-categories-budget-overview', [ExpenseCategoryController::class, 'budgetOverview'])->name('expense-categories.budget-overview');

        // Vendors
        Route::resource('vendors', VendorController::class);
        Route::post('vendors/{vendor}/toggle-status', [VendorController::class, 'toggleStatus'])->name('vendors.toggle-status');
        Route::post('vendors/{vendor}/update-rating', [VendorController::class, 'updateRating'])->name('vendors.update-rating');
        Route::post('vendors/bulk-update', [VendorController::class, 'bulkUpdate'])->name('vendors.bulk-update');
        Route::get('vendors-payment-summary', [VendorController::class, 'paymentSummary'])->name('vendors.payment-summary');

        // Payment Method Management
        Route::resource('payment-methods', PaymentMethodController::class);
        Route::post('payment-methods/{paymentMethod}/toggle-status', [PaymentMethodController::class, 'toggleStatus'])->name('payment-methods.toggle-status');
        Route::post('payment-methods/update-order', [PaymentMethodController::class, 'updateOrder'])->name('payment-methods.update-order');
        Route::get('api/payment-methods/active', [PaymentMethodController::class, 'getActive'])->name('api.payment-methods.active');



        // Budget Management
        Route::resource('budgets', BudgetController::class);
        Route::get('budgets-dashboard', [BudgetController::class, 'dashboard'])->name('budgets.dashboard');
        Route::post('budgets/{budget}/toggle-status', [BudgetController::class, 'toggleStatus'])->name('budgets.toggle-status');
        Route::post('budgets/{budget}/create-next-period', [BudgetController::class, 'createNextPeriod'])->name('budgets.create-next-period');
        Route::post('budgets/update-spent-amounts', [BudgetController::class, 'updateSpentAmounts'])->name('budgets.update-spent-amounts');
        Route::get('budget-alerts', [BudgetController::class, 'alerts'])->name('budgets.alerts');
        Route::post('budget-alerts/{alert}/mark-read', [BudgetController::class, 'markAlertRead'])->name('budgets.mark-alert-read');

        // Inventory Management
        Route::get('inventory-dashboard', [InventoryController::class, 'dashboard'])->name('inventory.dashboard');
        Route::resource('inventory', InventoryController::class);
        Route::post('inventory/{inventory}/adjust-stock', [InventoryController::class, 'adjustStock'])->name('inventory.adjust-stock');
        Route::post('inventory/{inventory}/add-stock', [InventoryController::class, 'addStock'])->name('inventory.add-stock');
        Route::post('inventory/{inventory}/remove-stock', [InventoryController::class, 'removeStock'])->name('inventory.remove-stock');

        // Inventory Categories
        Route::resource('inventory-categories', InventoryCategoryController::class);
        Route::post('inventory-categories/{inventoryCategory}/toggle-status', [InventoryCategoryController::class, 'toggleStatus'])->name('inventory-categories.toggle-status');
        Route::post('inventory-categories/reorder', [InventoryCategoryController::class, 'reorder'])->name('inventory-categories.reorder');
        Route::post('inventory-categories/bulk-update', [InventoryCategoryController::class, 'bulkUpdate'])->name('inventory-categories.bulk-update');
        Route::get('inventory-categories-hierarchy', [InventoryCategoryController::class, 'hierarchy'])->name('inventory-categories.hierarchy');

        // Purchase Orders
        Route::resource('purchase-orders', PurchaseOrderController::class);
        Route::post('purchase-orders/{purchaseOrder}/approve', [PurchaseOrderController::class, 'approve'])->name('purchase-orders.approve');
        Route::post('purchase-orders/{purchaseOrder}/send-to-vendor', [PurchaseOrderController::class, 'sendToVendor'])->name('purchase-orders.send-to-vendor');
        Route::post('purchase-orders/{purchaseOrder}/mark-received', [PurchaseOrderController::class, 'markReceived'])->name('purchase-orders.mark-received');
        Route::post('purchase-orders/{purchaseOrder}/partial-receive', [PurchaseOrderController::class, 'partialReceive'])->name('purchase-orders.partial-receive');
        Route::post('purchase-orders/{purchaseOrder}/cancel', [PurchaseOrderController::class, 'cancel'])->name('purchase-orders.cancel');
        Route::post('purchase-orders/{purchaseOrder}/duplicate', [PurchaseOrderController::class, 'duplicate'])->name('purchase-orders.duplicate');
        Route::post('purchase-orders-generate-auto', [PurchaseOrderController::class, 'generateAutoOrders'])->name('purchase-orders.generate-auto');

        // Waste Management
        Route::get('waste-dashboard', [WasteController::class, 'dashboard'])->name('waste.dashboard');
        Route::resource('waste', WasteController::class);
        Route::post('waste-bulk-create-expired', [WasteController::class, 'bulkCreateExpired'])->name('waste.bulk-create-expired');
        Route::get('waste-analytics', [WasteController::class, 'analytics'])->name('waste.analytics');

        // Staff Management
        Route::resource('departments', DepartmentController::class);
        Route::post('departments/{department}/toggle-status', [DepartmentController::class, 'toggleStatus'])->name('departments.toggle-status');
        Route::post('departments/reorder', [DepartmentController::class, 'reorder'])->name('departments.reorder');
        Route::post('departments/bulk-update', [DepartmentController::class, 'bulkUpdate'])->name('departments.bulk-update');

        Route::resource('employees', EmployeeController::class);
        Route::post('employees/{employee}/toggle-status', [EmployeeController::class, 'toggleStatus'])->name('employees.toggle-status');
        Route::post('employees/{employee}/reset-password', [EmployeeController::class, 'resetPassword'])->name('employees.reset-password');
        Route::get('employees/{employee}/performance', [EmployeeController::class, 'getPerformance'])->name('employees.performance');
        Route::post('employees/export', [EmployeeController::class, 'export'])->name('employees.export');
        Route::post('employees/bulk-update', [EmployeeController::class, 'bulkUpdate'])->name('employees.bulk-update');

        // Branch Management
        Route::resource('branches', App\Http\Controllers\Tenant\BranchController::class);
        Route::post('branches/{branch}/toggle-status', [App\Http\Controllers\Tenant\BranchController::class, 'toggleStatus'])->name('branches.toggle-status');
        Route::post('branches/bulk-update', [App\Http\Controllers\Tenant\BranchController::class, 'bulkUpdate'])->name('branches.bulk-update');
        Route::get('branches-api', [App\Http\Controllers\Tenant\BranchController::class, 'getBranches'])->name('branches.api');

        // Test translation route
        Route::get('test-translations', function () {
            return Inertia::render('TestTranslations');
        })->name('test.translations');

        // Enhanced Employee Management
        Route::resource('enhanced-employees', App\Http\Controllers\Tenant\EnhancedEmployeeController::class);
        Route::get('enhanced-employees-api', [App\Http\Controllers\Tenant\EnhancedEmployeeController::class, 'getEmployees'])->name('enhanced-employees.api');

        // Employee Shift Management
        Route::resource('employee-shifts', App\Http\Controllers\Tenant\EmployeeShiftController::class);
        Route::get('employee-shifts-calendar', [App\Http\Controllers\Tenant\EmployeeShiftController::class, 'calendar'])->name('employee-shifts.calendar');

        // Shift Management
        Route::resource('shifts', ShiftController::class);
        Route::post('shifts/{shift}/start', [ShiftController::class, 'start'])->name('shifts.start');
        Route::post('shifts/{shift}/end', [ShiftController::class, 'end'])->name('shifts.end');
        Route::get('shifts/calendar', [ShiftController::class, 'calendar'])->name('shifts.calendar');
        Route::post('shifts/export', [ShiftController::class, 'export'])->name('shifts.export');
        Route::get('shifts/stats', [ShiftController::class, 'getStats'])->name('shifts.stats');

        // Time Tracking
        Route::resource('time-entries', TimeEntryController::class);
        Route::get('time-entries/my-entries', [TimeEntryController::class, 'myTimeEntries'])->name('time-entries.my-entries');
        Route::post('time-entries/clock-in', [TimeEntryController::class, 'clockIn'])->name('time-entries.clock-in');
        Route::post('time-entries/clock-out', [TimeEntryController::class, 'clockOut'])->name('time-entries.clock-out');
        Route::get('time-entries/attendance-report', [TimeEntryController::class, 'attendanceReport'])->name('time-entries.attendance-report');

        // Tenant Asset Route for serving media files
        Route::get('/tenant-assets/{path}', [App\Http\Controllers\Tenant\MediaController::class, 'serveAsset'])
            ->where('path', '.*')
            ->name('tenant.asset');

        // Media Library
        Route::prefix('media')->name('media.')->group(function () {
            Route::get('/', [App\Http\Controllers\Tenant\MediaController::class, 'index'])->name('index');
            Route::post('/upload', [App\Http\Controllers\Tenant\MediaController::class, 'store'])->name('upload');
            Route::get('/picker', [App\Http\Controllers\Tenant\MediaController::class, 'index'])->name('picker');
            Route::get('/search', [App\Http\Controllers\Tenant\MediaController::class, 'search'])->name('search');
            Route::post('/create-folder', [App\Http\Controllers\Tenant\MediaController::class, 'createFolder'])->name('create-folder');
            Route::get('/{media}', [App\Http\Controllers\Tenant\MediaController::class, 'show'])->name('show');
            Route::put('/{media}', [App\Http\Controllers\Tenant\MediaController::class, 'update'])->name('update');
            Route::delete('/{media}', [App\Http\Controllers\Tenant\MediaController::class, 'destroy'])->name('destroy');
            Route::post('/bulk-delete', [App\Http\Controllers\Tenant\MediaController::class, 'bulkDestroy'])->name('bulk-delete');
        });

        // Settings Management (Manager only)
        // Route::middleware(['role:restaurant_manager'])->group(function () {
            Route::prefix('site-settings')->name('site-settings.')->group(function () {
                Route::get('/', [App\Http\Controllers\Tenant\SettingController::class, 'index'])->name('index');
                Route::put('/', [App\Http\Controllers\Tenant\SettingController::class, 'update'])->name('update');
                Route::get('/public', [App\Http\Controllers\Tenant\SettingController::class, 'getPublicSettings'])->name('public');
            });

            // Pages Management (Manager only)
            Route::resource('pages', App\Http\Controllers\Tenant\PageController::class);
            Route::post('pages/{page}/toggle-status', [App\Http\Controllers\Tenant\PageController::class, 'toggleStatus'])->name('pages.toggle-status');
        // });

        // Settings (Legacy - keeping for compatibility)
        Route::prefix('settings')->name('settings.')->group(function () {
            Route::get('/', [SettingsController::class, 'index'])->name('index');
            Route::put('/general', [SettingsController::class, 'updateGeneral'])->name('update.general');
            Route::put('/operational', [SettingsController::class, 'updateOperational'])->name('update.operational');
            Route::put('/tax-vat', [SettingsController::class, 'updateTaxVat'])->name('update.tax_vat');
            Route::put('/payment', [SettingsController::class, 'updatePayment'])->name('update.payment');
            Route::put('/notification', [SettingsController::class, 'updateNotification'])->name('update.notification');
            Route::put('/branding', [SettingsController::class, 'updateBranding'])->name('update.branding');
        });
    });
        Route::name('guest.')->group(function () {
        Route::get('/', [GuestController::class, 'index'])->name('home');
        Route::get('/menu', [GuestController::class, 'menu'])->name('menu');
        Route::get('/menu/{category:slug}', [GuestController::class, 'categoryMenu'])->name('menu.category');
        Route::get('/item/{menuItem:slug}', [GuestController::class, 'menuItem'])->name('menu.item');
        Route::post('/language/switch', [GuestController::class, 'switchLanguage'])->name('language.switch');
        Route::get('/checkout', [GuestController::class, 'checkout'])->name('checkout');

        // Guest Orders
        Route::post('/orders', [App\Http\Controllers\Tenant\GuestOrderController::class, 'store'])->name('orders.store');

        // Dynamic Pages (must be last to avoid conflicts)
        Route::get('/{page:slug}', [GuestController::class, 'dynamicPage'])->name('page.show');
        Route::post('/order', [GuestController::class, 'placeOrder'])->name('order.place');
        Route::get('/order/{order}/track', [GuestController::class, 'trackOrder'])->name('order.track');
        Route::get('/table/{table}/qr', [GuestController::class, 'qrOrder'])->name('table.qr');
    });
});

/*
|--------------------------------------------------------------------------
| Tenant API Routes
|--------------------------------------------------------------------------
|
| API routes for the enhanced restaurant management system
|
*/

Route::middleware([
    'api',
    InitializeTenancyByDomain::class,
    PreventAccessFromCentralDomains::class,
])->prefix('api')->name('api.')->group(function () {

    // Public API Routes (for customer-facing apps)
    Route::prefix('public')->name('public.')->group(function () {
        // Menu browsing
        Route::get('/menu', [App\Http\Controllers\Api\Tenant\FoodController::class, 'index'])->name('menu.index');
        Route::get('/menu/featured', [App\Http\Controllers\Api\Tenant\FoodController::class, 'featured'])->name('menu.featured');
        Route::get('/menu/popular', [App\Http\Controllers\Api\Tenant\FoodController::class, 'popular'])->name('menu.popular');
        Route::get('/menu/new', [App\Http\Controllers\Api\Tenant\FoodController::class, 'new'])->name('menu.new');
        Route::get('/menu/categories/{category}', [App\Http\Controllers\Api\Tenant\FoodController::class, 'byCategory'])->name('menu.category');
        Route::get('/menu/foods/{food}', [App\Http\Controllers\Api\Tenant\FoodController::class, 'show'])->name('menu.food');

        // Categories
        Route::get('/categories', [App\Http\Controllers\Api\Tenant\FoodCategoryController::class, 'index'])->name('categories.index');

        // Restaurant info
        Route::get('/restaurant', [App\Http\Controllers\Api\Tenant\RestaurantController::class, 'show'])->name('restaurant.show');

        // Order placement (guest orders)
        Route::post('/orders', [App\Http\Controllers\Api\Tenant\OrderController::class, 'store'])->name('orders.store');
        Route::get('/orders/{order}/track', [App\Http\Controllers\Api\Tenant\OrderController::class, 'show'])->name('orders.track');
    });

    // Authenticated API Routes
    Route::middleware(['auth:tenant'])->group(function () {

        // Orders Management
        Route::apiResource('orders', App\Http\Controllers\Api\Tenant\OrderController::class);
        Route::post('orders/{order}/status', [App\Http\Controllers\Api\Tenant\OrderController::class, 'updateStatus'])->name('orders.update-status');
        Route::post('orders/{order}/cancel', [App\Http\Controllers\Api\Tenant\OrderController::class, 'cancel'])->name('orders.cancel');
        Route::get('orders/statistics', [App\Http\Controllers\Api\Tenant\OrderController::class, 'statistics'])->name('orders.statistics');
        Route::get('kitchen/orders', [App\Http\Controllers\Api\Tenant\OrderController::class, 'kitchen'])->name('kitchen.orders');
        Route::get('customers/{customer}/orders/active', [App\Http\Controllers\Api\Tenant\OrderController::class, 'customerActive'])->name('customers.orders.active');

        // Foods Management
        Route::apiResource('foods', App\Http\Controllers\Api\Tenant\FoodController::class);
        Route::post('foods/{food}/toggle-availability', [App\Http\Controllers\Api\Tenant\FoodController::class, 'toggleAvailability'])->name('foods.toggle-availability');
        Route::post('foods/bulk-availability', [App\Http\Controllers\Api\Tenant\FoodController::class, 'bulkUpdateAvailability'])->name('foods.bulk-availability');

        // Food Categories
        Route::apiResource('food-categories', App\Http\Controllers\Api\Tenant\FoodCategoryController::class);

        // Customers Management
        Route::apiResource('customers', App\Http\Controllers\Api\Tenant\CustomerController::class);
        Route::get('customers/statistics', [App\Http\Controllers\Api\Tenant\CustomerController::class, 'statistics'])->name('customers.statistics');
        Route::post('customers/{customer}/loyalty-points/add', [App\Http\Controllers\Api\Tenant\CustomerController::class, 'addLoyaltyPoints'])->name('customers.loyalty-points.add');
        Route::post('customers/{customer}/loyalty-points/redeem', [App\Http\Controllers\Api\Tenant\CustomerController::class, 'redeemLoyaltyPoints'])->name('customers.loyalty-points.redeem');
        Route::get('customers/{customer}/order-history', [App\Http\Controllers\Api\Tenant\CustomerController::class, 'orderHistory'])->name('customers.order-history');
        Route::get('customers/{customer}/addresses', [App\Http\Controllers\Api\Tenant\CustomerController::class, 'addresses'])->name('customers.addresses');
        Route::post('customers/{customer}/tier', [App\Http\Controllers\Api\Tenant\CustomerController::class, 'updateTier'])->name('customers.update-tier');
        Route::post('customers/{customer}/toggle-block', [App\Http\Controllers\Api\Tenant\CustomerController::class, 'toggleBlock'])->name('customers.toggle-block');

        // Customer Addresses
        Route::apiResource('customer-addresses', App\Http\Controllers\Api\Tenant\CustomerAddressController::class);
        Route::post('customer-addresses/{address}/set-default', [App\Http\Controllers\Api\Tenant\CustomerAddressController::class, 'setDefault'])->name('customer-addresses.set-default');
        Route::post('customer-addresses/validate', [App\Http\Controllers\Api\Tenant\CustomerAddressController::class, 'validateAddress'])->name('customer-addresses.validate');
        Route::get('customer-addresses/delivery-addresses', [App\Http\Controllers\Api\Tenant\CustomerAddressController::class, 'getDeliveryAddresses'])->name('customer-addresses.delivery-addresses');
        Route::get('customer-addresses/search', [App\Http\Controllers\Api\Tenant\CustomerAddressController::class, 'search'])->name('customer-addresses.search');
        Route::get('customer-addresses/suggestions', [App\Http\Controllers\Api\Tenant\CustomerAddressController::class, 'suggestions'])->name('customer-addresses.suggestions');
        Route::get('customer-addresses/place-details', [App\Http\Controllers\Api\Tenant\CustomerAddressController::class, 'getPlaceDetails'])->name('customer-addresses.place-details');






        // Table Reservations
        Route::apiResource('table-reservations', App\Http\Controllers\Api\Tenant\TableReservationController::class);
        Route::post('table-reservations/{reservation}/cancel', [App\Http\Controllers\Api\Tenant\TableReservationController::class, 'cancel'])->name('table-reservations.cancel');
        Route::get('table-reservations/available-tables', [App\Http\Controllers\Api\Tenant\TableReservationController::class, 'getAvailableTables'])->name('table-reservations.available-tables');
        Route::get('table-reservations/available-time-slots', [App\Http\Controllers\Api\Tenant\TableReservationController::class, 'getAvailableTimeSlots'])->name('table-reservations.available-time-slots');

        // Delivery Management
        Route::apiResource('delivery-drivers', App\Http\Controllers\Api\Tenant\DeliveryDriverController::class);
        Route::apiResource('delivery-orders', App\Http\Controllers\Api\Tenant\DeliveryOrderController::class);
        Route::post('delivery-orders/{deliveryOrder}/assign', [App\Http\Controllers\Api\Tenant\DeliveryOrderController::class, 'assign'])->name('delivery-orders.assign');
        Route::post('delivery-orders/{deliveryOrder}/pickup', [App\Http\Controllers\Api\Tenant\DeliveryOrderController::class, 'pickup'])->name('delivery-orders.pickup');
        Route::post('delivery-orders/{deliveryOrder}/deliver', [App\Http\Controllers\Api\Tenant\DeliveryOrderController::class, 'deliver'])->name('delivery-orders.deliver');
        Route::post('delivery-orders/{deliveryOrder}/fail', [App\Http\Controllers\Api\Tenant\DeliveryOrderController::class, 'fail'])->name('delivery-orders.fail');
        Route::post('delivery-drivers/{driver}/location', [App\Http\Controllers\Api\Tenant\DeliveryDriverController::class, 'updateLocation'])->name('delivery-drivers.update-location');

        // Reviews Management
        Route::apiResource('food-reviews', App\Http\Controllers\Api\Tenant\FoodReviewController::class);
        Route::apiResource('restaurant-reviews', App\Http\Controllers\Api\Tenant\RestaurantReviewController::class);
        Route::post('food-reviews/{review}/approve', [App\Http\Controllers\Api\Tenant\FoodReviewController::class, 'approve'])->name('food-reviews.approve');
        Route::post('food-reviews/{review}/reject', [App\Http\Controllers\Api\Tenant\FoodReviewController::class, 'reject'])->name('food-reviews.reject');
        Route::post('food-reviews/{review}/feature', [App\Http\Controllers\Api\Tenant\FoodReviewController::class, 'feature'])->name('food-reviews.feature');
        Route::post('food-reviews/{review}/respond', [App\Http\Controllers\Api\Tenant\FoodReviewController::class, 'respond'])->name('food-reviews.respond');

        // Coupons & Promotions
        Route::apiResource('coupons', App\Http\Controllers\Api\Tenant\CouponController::class);
        Route::apiResource('promotions', App\Http\Controllers\Api\Tenant\PromotionController::class);
        Route::post('coupons/{coupon}/validate', [App\Http\Controllers\Api\Tenant\CouponController::class, 'validate'])->name('coupons.validate');
        Route::get('coupons/usage/statistics', [App\Http\Controllers\Api\Tenant\CouponController::class, 'usageStatistics'])->name('coupons.usage-statistics');

        // Analytics & Reports
        Route::prefix('analytics')->name('analytics.')->group(function () {
            Route::get('/dashboard', [App\Http\Controllers\Api\Tenant\AnalyticsController::class, 'dashboard'])->name('dashboard');
            Route::get('/sales', [App\Http\Controllers\Api\Tenant\AnalyticsController::class, 'sales'])->name('sales');
            Route::get('/orders', [App\Http\Controllers\Api\Tenant\AnalyticsController::class, 'orders'])->name('orders');
            Route::get('/customers', [App\Http\Controllers\Api\Tenant\AnalyticsController::class, 'customers'])->name('customers');
            Route::get('/menu-performance', [App\Http\Controllers\Api\Tenant\AnalyticsController::class, 'menuPerformance'])->name('menu-performance');
            Route::get('/delivery-performance', [App\Http\Controllers\Api\Tenant\AnalyticsController::class, 'deliveryPerformance'])->name('delivery-performance');
        });
    });
});
