<?php

use Illuminate\Support\Facades\Route;
use App\Http\Controllers\Manager\BranchController;
use App\Http\Controllers\Manager\FloorController;
use App\Http\Controllers\Manager\TableController;
use App\Http\Controllers\Manager\DashboardController;

/*
|--------------------------------------------------------------------------
| Manager Routes
|--------------------------------------------------------------------------
|
| Here is where you can register manager routes for your application.
| These routes are loaded by the RouteServiceProvider within a group which
| contains the "manager" middleware group.
|
*/

Route::middleware(['auth', 'verified'])->prefix('manager')->name('manager.')->group(function () {
    
    // Dashboard
    Route::get('/dashboard', [DashboardController::class, 'index'])->name('dashboard');
    
    // Analytics
    Route::get('/analytics', [DashboardController::class, 'analytics'])->name('analytics');
    
    // Space Management
    Route::prefix('space')->group(function () {
        
        // Branches
        Route::resource('branches', BranchController::class);
        Route::post('branches/{branch}/toggle-status', [BranchController::class, 'toggleStatus'])->name('branches.toggle-status');
        
        // Floors
        Route::resource('floors', FloorController::class);
        Route::post('floors/{floor}/toggle-status', [FloorController::class, 'toggleStatus'])->name('floors.toggle-status');
        Route::get('api/floors/by-branch/{branchId}', [FloorController::class, 'getByBranch'])->name('floors.by-branch');
        
        // Tables
        Route::resource('tables', TableController::class);
        Route::post('tables/{table}/toggle-status', [TableController::class, 'toggleStatus'])->name('tables.toggle-status');
        Route::get('api/tables/by-floor/{floorId}', [TableController::class, 'getByFloor'])->name('tables.by-floor');
        
    });
    
    // Orders
    Route::prefix('orders')->name('orders.')->group(function () {
        Route::get('/', [OrderController::class, 'index'])->name('index');
        Route::get('/create', [OrderController::class, 'create'])->name('create');
        Route::post('/', [OrderController::class, 'store'])->name('store');
        Route::get('/{order}', [OrderController::class, 'show'])->name('show');
        Route::get('/{order}/edit', [OrderController::class, 'edit'])->name('edit');
        Route::put('/{order}', [OrderController::class, 'update'])->name('update');
        Route::delete('/{order}', [OrderController::class, 'destroy'])->name('destroy');
    });
    
    // Kitchen
    Route::prefix('kitchen')->name('kitchen.')->group(function () {
        Route::get('/', [KitchenController::class, 'index'])->name('index');
        Route::post('/orders/{order}/update-status', [KitchenController::class, 'updateOrderStatus'])->name('update-status');
    });
    
    // Menu Management
    Route::prefix('menu')->name('menu.')->group(function () {
        Route::resource('items', MenuItemController::class)->names([
            'index' => 'menu-items.index',
            'create' => 'menu-items.create',
            'store' => 'menu-items.store',
            'show' => 'menu-items.show',
            'edit' => 'menu-items.edit',
            'update' => 'menu-items.update',
            'destroy' => 'menu-items.destroy',
        ]);
        
        Route::resource('categories', CategoryController::class);
    });
    
    // Staff Management
    Route::prefix('staff')->name('staff.')->group(function () {
        Route::resource('employees', EmployeeController::class)->names([
            'index' => 'staff.index',
            'create' => 'staff.create',
            'store' => 'staff.store',
            'show' => 'staff.show',
            'edit' => 'staff.edit',
            'update' => 'staff.update',
            'destroy' => 'staff.destroy',
        ]);
        
        Route::resource('departments', DepartmentController::class);
    });
    
    // Customers
    Route::resource('customers', CustomerController::class);
    
    // Reports
    Route::prefix('reports')->name('reports.')->group(function () {
        Route::get('/', [ReportController::class, 'index'])->name('index');
        Route::get('/sales', [ReportController::class, 'sales'])->name('sales');
        Route::get('/inventory', [ReportController::class, 'inventory'])->name('inventory');
        Route::get('/staff', [ReportController::class, 'staff'])->name('staff');
    });
    
    // Settings
    Route::prefix('settings')->name('settings.')->group(function () {
        Route::get('/', [SettingsController::class, 'index'])->name('index');
        Route::get('/general', [SettingsController::class, 'general'])->name('general');
        Route::post('/general', [SettingsController::class, 'updateGeneral'])->name('general.update');
        Route::get('/restaurant', [SettingsController::class, 'restaurant'])->name('restaurant');
        Route::post('/restaurant', [SettingsController::class, 'updateRestaurant'])->name('restaurant.update');
    });
    
});
