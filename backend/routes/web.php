<?php

use Illuminate\Foundation\Application;
use Illuminate\Support\Facades\Route;
use Illuminate\Support\Facades\Auth;
use Inertia\Inertia;
use App\Http\Controllers\Admin\DashboardController as AdminDashboardController;
use App\Http\Controllers\Admin\TenantController;
use App\Http\Controllers\Admin\SubscriptionPlanController;
use App\Http\Controllers\Admin\PaymentController;
use App\Http\Controllers\LandingController;

/*
|--------------------------------------------------------------------------
| Global Authentication Routes (Before Domain Restrictions)
|--------------------------------------------------------------------------
*/

// Simple login route that always works





// Simple test route to verify routing works
Route::get('/test-route', function() {
    return response()->json([
        'message' => 'Routing is working!',
        'host' => request()->getHost(),
        'path' => request()->path(),
        'url' => request()->url(),
        'timestamp' => now()->toDateTimeString()
    ]);
});

/*
|--------------------------------------------------------------------------
| Central/Admin Routes
|--------------------------------------------------------------------------
|
| These routes are for the central admin application that manages
| tenants, subscriptions, and billing.
|
*/
foreach (config('tenancy.central_domains') as $domain) {
    Route::domain($domain)->group(function () {
        Route::get('/dashboard', function() {
    return Inertia::render('Dashboard', [
        'user' => Auth::user(),
    ]);
})->name('dashboard')->middleware('auth');

// Simple test route for Inertia
Route::get('/inertia-test', function() {
    return Inertia::render('Landing/Index', [
        'plans' => []
    ]);
});
Route::get('/signin', function() {
            return Inertia::render('Auth/Login', [
                'canResetPassword' => Route::has('password.request'),
                'status' => session('status'),
            ]);
        })->name('admin.login');

        Route::post('/signin', [\Laravel\Fortify\Http\Controllers\AuthenticatedSessionController::class, 'store'])->name('admin.submit.login');
    // Authentication Routes for Central Domain
    Route::middleware('guest')->group(function () {
        

        Route::get('/register', function() {
            return Inertia::render('Auth/Register');
        })->name('register');

        Route::post('/register', [\Laravel\Fortify\Http\Controllers\RegisteredUserController::class, 'store']);

        Route::get('/forgot-password', function() {
            return Inertia::render('Auth/ForgotPassword', [
                'status' => session('status'),
            ]);
        })->name('password.request');

        Route::post('/forgot-password', [\Laravel\Fortify\Http\Controllers\PasswordResetLinkController::class, 'store'])
            ->name('password.email');

        Route::get('/reset-password/{token}', function(\Illuminate\Http\Request $request, string $token) {
            return Inertia::render('Auth/ResetPassword', [
                'email' => $request->email,
                'token' => $token,
            ]);
        })->name('password.reset');

        Route::post('/reset-password', [\Laravel\Fortify\Http\Controllers\NewPasswordController::class, 'store'])
            ->name('password.update');
    });

    // Superadmin Dashboard (protected route)
    Route::middleware('auth')->group(function () {
        Route::post('/logouts', [\Laravel\Fortify\Http\Controllers\AuthenticatedSessionController::class, 'destroy'])
            ->name('admin.logout');

        Route::get('/dashboard', function() {
            return Inertia::render('Dashboard', [
                'user' => Auth::user(),
            ]);
        })->name('dashboard');
    });

    // Landing Page
    Route::get('/', function() {
    
        $plans = App\Models\SubscriptionPlan::where('is_active', true)->orderBy('sort_order')->get();
        $stats = [
            'restaurants' => 150,
            'orders_processed' => 25000,
            'revenue_generated' => 5000000,
            'satisfied_customers' => 98,
        ];
        return view('marketing.home', compact('plans', 'stats'));
    
})->name('marketing.home');
Route::get('/plans', [App\Http\Controllers\Marketing\HomeController::class, 'plans'])->name('marketing.plans');
Route::get('/contact', [App\Http\Controllers\Marketing\HomeController::class, 'contact'])->name('marketing.contact');
Route::post('/contact', [App\Http\Controllers\Marketing\HomeController::class, 'submitContact'])->name('marketing.contact.submit');

// Subscription Routes
 Route::get('/signup', [App\Http\Controllers\Marketing\SubscriptionController::class, 'signup'])->name('marketing.signup');
//auth routes
Route::post('/signup', [App\Http\Controllers\Marketing\SubscriptionController::class, 'processSignup'])->name('marketing.signup.process');
Route::get('/check-slug', [App\Http\Controllers\Marketing\SubscriptionController::class, 'checkSlug'])->name('marketing.check-slug');
Route::get('/generate-slug', [App\Http\Controllers\Marketing\SubscriptionController::class, 'generateSlug'])->name('marketing.generate-slug');
Route::get('/features', [App\Http\Controllers\Marketing\HomeController::class, 'features'])->name('marketing.features');

    // Admin Authentication Routes
    Route::middleware([
        'auth:sanctum',
        config('jetstream.auth_session'),
        'verified',
    ])->group(function () {

        // Admin Dashboard
        Route::get('/admin/dashboard', [AdminDashboardController::class, 'index'])->name('admin.dashboard');

        // Tenant Management
        Route::prefix('admin/tenants')->name('admin.tenants.')->group(function () {
            Route::get('/', [TenantController::class, 'index'])->name('index');
            Route::get('/create', [TenantController::class, 'create'])->name('create');
            Route::post('/', [TenantController::class, 'store'])->name('store');
            Route::get('/{tenant}', [TenantController::class, 'show'])->name('show');
            Route::get('/{tenant}/edit', [TenantController::class, 'edit'])->name('edit');
            Route::put('/{tenant}', [TenantController::class, 'update'])->name('update');
            Route::delete('/{tenant}', [TenantController::class, 'destroy'])->name('destroy');
            Route::post('/{tenant}/suspend', [TenantController::class, 'suspend'])->name('suspend');
            Route::post('/{tenant}/activate', [TenantController::class, 'activate'])->name('activate');
            Route::get('/{tenant}/impersonate', [TenantController::class, 'impersonate'])->name('impersonate');
            Route::get('/{tenant}/setup-status', [TenantController::class, 'setupStatus'])->name('setup-status');

            // Bulk operations
            Route::post('/bulk-delete', [TenantController::class, 'bulkDelete'])->name('bulk-delete');

            // Subscription management
            Route::post('/{tenant}/subscribe', [TenantController::class, 'subscribe'])->name('subscribe');
            Route::post('/{tenant}/renew-subscription', [TenantController::class, 'renewSubscription'])->name('renew-subscription');

            // Debug route for testing queue
            Route::get('/{tenant}/test-queue', function(\App\Models\Tenant $tenant) {
                \App\Jobs\Tenant\TestQueueJob::dispatch($tenant, 'Queue test at ' . now());
                return response()->json(['message' => 'Test job dispatched', 'tenant_id' => $tenant->id]);
            })->name('test-queue');
        });

        // Subscription Plan Management
        Route::prefix('admin/subscription-plans')->name('admin.subscription-plans.')->group(function () {
            Route::get('/', [SubscriptionPlanController::class, 'index'])->name('index');
            Route::get('/create', [SubscriptionPlanController::class, 'create'])->name('create');
            Route::post('/', [SubscriptionPlanController::class, 'store'])->name('store');
            Route::get('/{plan}', [SubscriptionPlanController::class, 'show'])->name('show');
            Route::get('/{plan}/edit', [SubscriptionPlanController::class, 'edit'])->name('edit');
            Route::put('/{plan}', [SubscriptionPlanController::class, 'update'])->name('update');
            Route::delete('/{plan}', [SubscriptionPlanController::class, 'destroy'])->name('destroy');
            Route::post('/{plan}/toggle-status', [SubscriptionPlanController::class, 'toggleStatus'])->name('toggle-status');
            Route::post('/bulk-action', [SubscriptionPlanController::class, 'bulkAction'])->name('bulk-action');
        });

        // Payment Management
        Route::prefix('admin/payments')->name('admin.payments.')->group(function () {
            Route::get('/', [PaymentController::class, 'index'])->name('index');
            Route::get('/{payment}', [PaymentController::class, 'show'])->name('show');
            Route::post('/{payment}/refund', [PaymentController::class, 'refund'])->name('refund');
        });
    });

    // Payment Gateway Return URLs (outside auth middleware for webhooks)
    Route::get('/return', [PaymentController::class, 'paymentReturn'])->name('payment.return');
    Route::post('/webhook', [PaymentController::class, 'webhook'])->name('payment.webhook');
    Route::get('/payment/success', [PaymentController::class, 'paymentSuccess'])->name('payment.success');
    Route::get('/payment/cancel', [PaymentController::class, 'paymentCancel'])->name('payment.cancel');
    Route::get('/payment/fail', [PaymentController::class, 'paymentFail'])->name('payment.fail');

// Language switching routes (outside domain middleware)
Route::post('/language/switch', [App\Http\Controllers\LanguageController::class, 'switch'])->name('language.switch');
Route::get('/language/current', [App\Http\Controllers\LanguageController::class, 'current'])->name('language.current');
Route::get('/language/translations', [App\Http\Controllers\LanguageController::class, 'translations'])->name('language.translations');
Route::middleware([
    'auth:sanctum',
    config('jetstream.auth_session'),
    'verified',
   // 'system.admin', // Ensure only system admins can access
])->group(function () {

        // Admin Dashboard
        Route::get('/admin/dashboard', [AdminDashboardController::class, 'index'])->name('admin.dashboard');
        Route::get('/admin', function () {
            return redirect()->route('admin.dashboard');
        })->name('admin.index');

        // Tenant Management
        Route::prefix('admin/tenants')->name('admin.tenants.')->group(function () {
            Route::get('/', [TenantController::class, 'index'])->name('index');
            Route::get('/create', [TenantController::class, 'create'])->name('create');
            Route::post('/', [TenantController::class, 'store'])->name('store');
            Route::get('/{tenant}', [TenantController::class, 'show'])->name('show');
            Route::get('/{tenant}/edit', [TenantController::class, 'edit'])->name('edit');
            Route::put('/{tenant}', [TenantController::class, 'update'])->name('update');
            Route::delete('/{tenant}', [TenantController::class, 'destroy'])->name('destroy');
            Route::post('/{tenant}/suspend', [TenantController::class, 'suspend'])->name('suspend');
            Route::post('/{tenant}/activate', [TenantController::class, 'activate'])->name('activate');
            Route::get('/{tenant}/impersonate', [TenantController::class, 'impersonate'])->name('impersonate');
        });

        // Subscription Plan Management
        Route::prefix('admin/subscription-plans')->name('admin.subscription-plans.')->group(function () {
            Route::get('/', [SubscriptionPlanController::class, 'index'])->name('index');
            Route::get('/create', [SubscriptionPlanController::class, 'create'])->name('create');
            Route::post('/', [SubscriptionPlanController::class, 'store'])->name('store');
            Route::get('/{plan}', [SubscriptionPlanController::class, 'show'])->name('show');
            Route::get('/{plan}/edit', [SubscriptionPlanController::class, 'edit'])->name('edit');
            Route::put('/{plan}', [SubscriptionPlanController::class, 'update'])->name('update');
            Route::delete('/{plan}', [SubscriptionPlanController::class, 'destroy'])->name('destroy');
            Route::post('/{plan}/toggle-status', [SubscriptionPlanController::class, 'toggleStatus'])->name('toggle-status');
        });

        // Payment Management
        Route::prefix('admin/payments')->name('admin.payments.')->group(function () {
            Route::get('/', [PaymentController::class, 'index'])->name('index');
            Route::get('/{payment}', [PaymentController::class, 'show'])->name('show');
            Route::post('/{payment}/refund', [PaymentController::class, 'refund'])->name('refund');
        });

        // Tenant Management
        Route::prefix('admin')->name('admin.')->group(function () {
            Route::resource('tenants', TenantController::class);
            Route::post('tenants/{tenant}/suspend', [TenantController::class, 'suspend'])->name('tenants.suspend');
            Route::post('tenants/{tenant}/activate', [TenantController::class, 'activate'])->name('tenants.activate');
            Route::get('tenants/{tenant}/impersonate', [TenantController::class, 'impersonate'])->name('tenants.impersonate');
        });

        // Subscription Plan Management
        Route::prefix('admin')->name('admin.')->group(function () {
            Route::resource('subscription-plans', SubscriptionPlanController::class);
        });

        // CMS Management
        Route::prefix('admin')->name('admin.')->group(function () {
            Route::resource('dynamic-pages', \App\Http\Controllers\Admin\DynamicPageController::class);
            Route::post('dynamic-pages/bulk-action', [\App\Http\Controllers\Admin\DynamicPageController::class, 'bulkAction'])->name('dynamic-pages.bulk-action');

            Route::resource('blogs', \App\Http\Controllers\Admin\BlogController::class);
            Route::post('blogs/bulk-action', [\App\Http\Controllers\Admin\BlogController::class, 'bulkAction'])->name('blogs.bulk-action');
        });
});
// Public Blog Routes (SEO-optimized, accessible at root URL)
Route::prefix('blog')->name('blog.')->group(function () {
    Route::get('/', [App\Http\Controllers\BlogController::class, 'index'])->name('index');
    Route::get('/tag/{tag}', [App\Http\Controllers\BlogController::class, 'tag'])->name('tag');
    Route::get('/category/{category}', [App\Http\Controllers\BlogController::class, 'category'])->name('category');
    Route::get('/{slug}', [App\Http\Controllers\BlogController::class, 'show'])->name('show');
});

// Public Dynamic Pages Routes (SEO-optimized, accessible at root URL)
Route::get('/page/{slug}', [App\Http\Controllers\DynamicPageController::class, 'show'])->name('page.show');

// Test route to debug 404 issue
Route::get('/test', function() {
    return response()->json(['message' => 'Test route working', 'timestamp' => now()]);
});

        // other central routes...
    });
}
// Central Domain Authentication Routes (outside domain restriction)
Route::middleware(['web'])->group(function () {
    // Only register these routes for central domains
    if (in_array(request()->getHost(), config('tenancy.central_domains', ['localhost']))) {

        // Guest routes (login, register, etc.)
        Route::middleware('guest')->group(function () {
            Route::get('/login', function() {
                return Inertia::render('Auth/Login', [
                    'canResetPassword' => Route::has('password.request'),
                    'status' => session('status'),
                ]);
            })->name('central.login');

            Route::post('/login', function(\Illuminate\Http\Request $request) {
                $request->validate([
                    'email' => 'required|email',
                    'password' => 'required|string',
                ]);

                if (Auth::attempt($request->only('email', 'password'), $request->boolean('remember'))) {
                    $request->session()->regenerate();
                    return redirect()->intended('/dashboard');
                }

                return back()->withErrors([
                    'email' => 'The provided credentials do not match our records.',
                ]);
            })->name('central.login.store');

            Route::get('/register', function() {
                return Inertia::render('Auth/Register');
            })->name('central.register');

            Route::get('/forgot-password', function() {
                return Inertia::render('Auth/ForgotPassword', [
                    'status' => session('status'),
                ]);
            })->name('central.password.request');
        });

        // Authenticated routes
        Route::middleware('auth')->group(function () {
            Route::post('/logout', function(\Illuminate\Http\Request $request) {
                Auth::logout();
                $request->session()->invalidate();
                $request->session()->regenerateToken();
                return redirect('/');
            })->name('central.logout');

            Route::get('/dashboard', function() {
                return Inertia::render('Dashboard', [
                    'user' => Auth::user(),
                ]);
            })->name('central.dashboard');
        });
    }
});

// Fallback authentication routes (ensure they always exist)
Route::get('/login', function() {
    return Inertia::render('Auth/Login', [
        'canResetPassword' => false,
        'status' => session('status'),
    ]);
})->name('login')->middleware('web');

Route::post('/login', function(\Illuminate\Http\Request $request) {
    $request->validate([
        'email' => 'required|email',
        'password' => 'required|string',
    ]);

    if (Auth::attempt($request->only('email', 'password'), $request->boolean('remember'))) {
        $request->session()->regenerate();
        return redirect()->intended('/dashboard');
    }

    return back()->withErrors([
        'email' => 'The provided credentials do not match our records.',
    ]);
})->middleware('web');

Route::post('/logout', function(\Illuminate\Http\Request $request) {
    Auth::logout();
    $request->session()->invalidate();
    $request->session()->regenerateToken();
    return redirect('/');
})->name('logout')->middleware('web');

// Test route without middleware
