<?php

require_once __DIR__ . '/vendor/autoload.php';

use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

// Bootstrap Laravel
$app = require_once __DIR__ . '/bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

try {
    // Switch to tenant database
    config(['database.default' => 'tenant']);
    
    // Check if columns exist
    $columns = Schema::getColumnListing('orders');
    
    echo "Current orders table columns:\n";
    foreach ($columns as $column) {
        echo "- $column\n";
    }
    
    $requiredColumns = ['branch_id', 'order_source', 'order_closed_at'];
    $missingColumns = [];
    
    foreach ($requiredColumns as $column) {
        if (!in_array($column, $columns)) {
            $missingColumns[] = $column;
        }
    }
    
    if (empty($missingColumns)) {
        echo "\n✅ All required columns exist!\n";
    } else {
        echo "\n❌ Missing columns: " . implode(', ', $missingColumns) . "\n";
        
        // Try to add missing columns
        echo "\nAttempting to add missing columns...\n";
        
        Schema::table('orders', function ($table) use ($missingColumns, $columns) {
            if (in_array('branch_id', $missingColumns)) {
                $table->foreignId('branch_id')->nullable()->constrained('branches')->onDelete('set null')->after('id');
                echo "✅ Added branch_id column\n";
            }
            
            if (in_array('order_source', $missingColumns)) {
                $table->enum('order_source', ['online', 'pos', 'phone', 'walk_in'])->default('online')->after('order_type');
                echo "✅ Added order_source column\n";
            }
            
            if (in_array('order_closed_at', $missingColumns)) {
                $table->timestamp('order_closed_at')->nullable()->after('updated_at');
                echo "✅ Added order_closed_at column\n";
            }
        });
        
        echo "\n✅ Migration completed successfully!\n";
    }
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
    echo "Stack trace:\n" . $e->getTraceAsString() . "\n";
}
