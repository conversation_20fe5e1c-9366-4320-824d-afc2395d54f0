# 🔐 Multi-Tenant Role Management System Documentation

## 📋 **System Architecture Overview**

### **Database Structure**
```
Central Database (restaurant_management):
├── users (tenant owners + system admins)
├── roles (restaurant_manager, admin, waiter, chef, etc.)
├── permissions (optional granular permissions)
├── model_has_roles (user-role assignments)
├── tenants (tenant information)
└── tenant_subscriptions (subscription data)

Tenant Database (tenant_demo-restaurant):
├── restaurants (tenant-specific restaurant data)
├── menu_items, categories, orders, etc.
└── (NO users, roles, or permissions - these stay central)
```

### **Key Principles**
1. **Users, Roles, Permissions**: Always stored in central database
2. **Restaurant Data**: Stored in tenant-specific databases
3. **Role Assignment**: Automatic for tenant owners
4. **Authentication**: Single sign-on across all tenants

---

## 🚀 **User Setup & Role Assignment**

### **Manager User Created**
```bash
# ✅ COMPLETED: Manager user setup
Email: <EMAIL>
Password: password123
Role: restaurant_manager
Tenant: demo-restaurant
URL: http://demo-restaurant.localhost:8000/login
```

### **Automatic Role Assignment**
The system automatically assigns `restaurant_manager` role to users accessing tenant contexts through the `EnsureTenantManagerRole` middleware.

---

## 🔍 **Role Checking Methods**

### **1. In Controllers**
```php
// Check if user has specific role
if (auth()->user()->hasRole('restaurant_manager')) {
    // User can access manager features
}

// Check multiple roles (OR condition)
if (auth()->user()->hasAnyRole(['restaurant_manager', 'admin'])) {
    // User has at least one of these roles
}

// Check all roles (AND condition)
if (auth()->user()->hasAllRoles(['restaurant_manager', 'waiter'])) {
    // User has both roles
}

// Using User model methods
if (auth()->user()->isRestaurantManager()) {
    // Convenient method for manager check
}

if (auth()->user()->canManageTenant()) {
    // Check if user can manage tenant features
}
```

### **2. In Middleware**
```php
// Route protection with role middleware
Route::middleware(['role:restaurant_manager'])->group(function () {
    Route::get('/subscription', [SubscriptionController::class, 'index']);
});

// Multiple role options
Route::middleware(['role:restaurant_manager|admin'])->group(function () {
    Route::get('/settings', [SettingsController::class, 'index']);
});
```

### **3. In Blade Templates (if used)**
```php
@role('restaurant_manager')
    <a href="/subscription">Subscription Management</a>
@endrole

@hasanyrole('restaurant_manager|admin')
    <a href="/settings">Settings</a>
@endhasanyrole
```

### **4. In Vue.js Components**
```javascript
// Access user data from Inertia props
const { auth } = usePage().props;

// Check roles in computed properties
const canManage = computed(() => {
    return auth.user.roles.some(role => 
        ['restaurant_manager', 'admin'].includes(role.name)
    );
});

// In template
<template>
    <div v-if="canManage">
        <router-link to="/subscription">Subscription</router-link>
    </div>
</template>
```

---

## 🛡️ **Middleware System**

### **Available Middleware**
```php
'role' => \App\Http\Middleware\RoleMiddleware::class,
'permission' => \App\Http\Middleware\PermissionMiddleware::class,
'tenant.manager' => \App\Http\Middleware\EnsureTenantManagerRole::class,
'redirect.role' => \App\Http\Middleware\RedirectBasedOnRole::class,
```

### **Middleware Usage Examples**
```php
// Single role requirement
Route::middleware(['role:restaurant_manager'])->group(function () {
    Route::get('/subscription', [SubscriptionController::class, 'index']);
});

// Multiple role options (OR condition)
Route::middleware(['role:restaurant_manager|admin|waiter'])->group(function () {
    Route::get('/orders', [OrderController::class, 'index']);
});

// Automatic role assignment for tenant access
Route::middleware(['tenant.manager'])->group(function () {
    // Automatically assigns restaurant_manager role if needed
});
```

---

## 🎯 **Role Hierarchy & Permissions**

### **Role Levels (Higher = More Permissions)**
```php
admin: 100              // System administrator
restaurant_manager: 80  // Tenant owner/manager
chef: 60                // Kitchen supervisor
waiter: 40              // Service staff
delivery: 20            // Delivery rider
```

### **Feature Access by Role**
```php
// Manager Features
✅ restaurant_manager: Subscription, Settings, Reports, Staff Management
✅ admin: All features + system administration

// Staff Features  
✅ waiter: Orders, Tables, Customers, Menu (view)
✅ chef: Kitchen Orders, Menu (view)
✅ delivery: Delivery Orders, Route Management
```

---

## 🔧 **Implementation Examples**

### **Controller Role Checking**
```php
class SubscriptionController extends Controller
{
    public function index()
    {
        // Automatic role check via middleware
        // User already has restaurant_manager role
        
        $user = auth()->user();
        $tenant = Tenancy::tenant();
        
        // Additional checks if needed
        if (!$user->canManageTenant()) {
            abort(403, 'Access denied');
        }
        
        return Inertia::render('Subscription/Index');
    }
}
```

### **Service Class Usage**
```php
use App\Services\TenantRoleService;

class SomeController extends Controller
{
    protected $tenantRoleService;
    
    public function __construct(TenantRoleService $tenantRoleService)
    {
        $this->tenantRoleService = $tenantRoleService;
    }
    
    public function someMethod()
    {
        $user = auth()->user();
        $tenant = Tenancy::tenant();
        
        // Check feature access based on subscription
        if ($this->tenantRoleService->canAccessFeature($user, 'home_delivery', $tenant)) {
            // Enable delivery features
        }
        
        // Get user's accessible menu items
        $menuItems = $this->tenantRoleService->getAccessibleMenuItems($user);
    }
}
```

---

## 🚨 **Troubleshooting**

### **Common Issues & Solutions**

#### **1. User doesn't have restaurant_manager role**
```bash
# Run the seeder to fix role assignment
php artisan db:seed --class=TenantManagerSeeder
```

#### **2. Role middleware not working**
```php
// Check if middleware is registered in bootstrap/app.php
'role' => \App\Http\Middleware\RoleMiddleware::class,

// Verify route has middleware applied
Route::middleware(['role:restaurant_manager'])->group(function () {
    // Protected routes
});
```

#### **3. User can't access subscription page**
```bash
# Verify user has correct role
php artisan tinker
>>> $user = App\Models\User::where('email', '<EMAIL>')->first();
>>> $user->hasRole('restaurant_manager'); // Should return true
>>> $user->roles->pluck('name'); // Should include 'restaurant_manager'
```

#### **4. Database connection issues**
```php
// Verify models use CentralConnection trait
class User extends Authenticatable
{
    use CentralConnection; // ✅ Always use central database
}
```

---

## 📊 **Testing Role System**

### **Manual Testing Steps**
1. **Login**: http://demo-restaurant.localhost:8000/login
2. **Credentials**: <EMAIL> / password123
3. **Test URLs**:
   - Dashboard: `/dashboard`
   - Subscription: `/subscription` (should work)
   - Settings: `/settings` (should work)

### **Programmatic Testing**
```php
// In tinker or test
$user = User::where('email', '<EMAIL>')->first();
$user->hasRole('restaurant_manager'); // true
$user->isRestaurantManager(); // true
$user->canManageTenant(); // true
$user->getRoleLevel(); // 80
```

---

## 🎉 **Success Indicators**

✅ **User created**: <EMAIL> with restaurant_manager role  
✅ **Middleware working**: Automatic role assignment on tenant access  
✅ **Route protection**: Manager-only routes accessible  
✅ **Subscription access**: `/subscription` page loads correctly  
✅ **Role checking**: All role methods return expected values  

The multi-tenant role management system is now fully operational! 🚀
