#!/bin/bash

echo "=== Multi-Tenant Restaurant Management System Setup ==="
echo ""

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${GREEN}✓${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}⚠${NC} $1"
}

print_error() {
    echo -e "${RED}✗${NC} $1"
}

echo "Step 1: Setting up environment..."
# Copy environment file if it doesn't exist
if [ ! -f .env ]; then
    cp .env.example .env
    print_status "Environment file created"
else
    print_warning "Environment file already exists"
fi

echo ""
echo "Step 2: Installing dependencies..."
composer install --no-interaction --prefer-dist --optimize-autoloader
npm install

echo ""
echo "Step 3: Generating application key..."
php artisan key:generate

echo ""
echo "Step 4: Running central database migrations..."
php artisan migrate:fresh --force

echo ""
echo "Step 5: Setting up demo tenant (before seeding)..."
php setup_demo_tenant.php

echo ""
echo "Step 6: Seeding central database..."
php artisan db:seed --class=SubscriptionPlanSeeder --force
php artisan db:seed --class=RolePermissionSeeder --force
php artisan db:seed --class=UserSeeder --force

echo ""
echo "Step 7: Building frontend assets..."
npm run build

echo ""
echo "Step 8: Setting up storage links..."
php artisan storage:link

echo ""
echo "Step 9: Clearing caches..."
php artisan config:clear
php artisan cache:clear
php artisan route:clear
php artisan view:clear

echo ""
print_status "Multi-tenant setup completed!"
echo ""
echo "=== Login Credentials ==="
echo "Super Admin: <EMAIL> / Restaurant@2024"
echo "Manager: <EMAIL> / Manager@2024"
echo "Waiter: <EMAIL> / Waiter@2024"
echo "Kitchen: <EMAIL> / Kitchen@2024"
echo "Delivery: <EMAIL> / Delivery@2024"
echo ""
echo "=== Access URLs ==="
echo "Central Admin: http://localhost:8000"
echo "Demo Restaurant: http://demo-restaurant.localhost:8000"
echo ""
echo "To start the server: php artisan serve"
