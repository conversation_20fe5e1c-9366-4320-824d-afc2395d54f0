<?php

// Quick fix for tenant domain issue
echo "=== Quick Tenant Domain Fix ===\n";

// Database configuration from .env
$host = '127.0.0.1';
$dbname = 'restaurant_management'; // Update this to match your database name
$username = 'root'; // Update this to match your database username
$password = ''; // Update this to match your database password

try {
    // Connect to database
    $pdo = new PDO("mysql:host=$host;dbname=$dbname", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    echo "✓ Connected to database\n";

    // Check if tenant exists
    $stmt = $pdo->prepare("SELECT * FROM tenants WHERE id = ?");
    $stmt->execute(['demo-restaurant']);
    $tenant = $stmt->fetch();

    if (!$tenant) {
        echo "Creating demo tenant...\n";
        
        // Create tenant
        $stmt = $pdo->prepare("
            INSERT INTO tenants (id, name, email, phone, address, city, state, country, postal_code, timezone, currency, language, subscription_status, trial_ends_at, created_at, updated_at)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        ");
        
        $stmt->execute([
            'demo-restaurant',
            'Demo Restaurant',
            '<EMAIL>',
            '******-0123',
            '123 Main Street',
            'New York',
            'NY',
            'USA',
            '10001',
            'America/New_York',
            'USD',
            'en',
            'active',
            date('Y-m-d H:i:s', strtotime('+30 days')),
            date('Y-m-d H:i:s'),
            date('Y-m-d H:i:s')
        ]);
        
        echo "✓ Tenant created\n";
    } else {
        echo "✓ Tenant already exists: {$tenant['name']}\n";
    }

    // Check if domain exists
    $stmt = $pdo->prepare("SELECT * FROM domains WHERE domain = ?");
    $stmt->execute(['demo-restaurant.localhost']);
    $domain = $stmt->fetch();

    if (!$domain) {
        echo "Creating domain...\n";
        
        // Create domain
        $stmt = $pdo->prepare("
            INSERT INTO domains (domain, tenant_id, created_at, updated_at)
            VALUES (?, ?, ?, ?)
        ");
        
        $stmt->execute([
            'demo-restaurant.localhost',
            'demo-restaurant',
            date('Y-m-d H:i:s'),
            date('Y-m-d H:i:s')
        ]);
        
        echo "✓ Domain created\n";
    } else {
        echo "✓ Domain already exists: {$domain['domain']}\n";
    }

    // Verify setup
    echo "\n=== Verification ===\n";
    $stmt = $pdo->prepare("
        SELECT 
            t.id as tenant_id,
            t.name as tenant_name,
            t.subscription_status,
            d.domain as domain_name
        FROM tenants t
        LEFT JOIN domains d ON t.id = d.tenant_id
        WHERE t.id = ?
    ");
    $stmt->execute(['demo-restaurant']);
    $result = $stmt->fetch();

    if ($result) {
        echo "Tenant ID: {$result['tenant_id']}\n";
        echo "Tenant Name: {$result['tenant_name']}\n";
        echo "Status: {$result['subscription_status']}\n";
        echo "Domain: {$result['domain_name']}\n";
    }

    echo "\n✅ Fix completed! You can now access: http://demo-restaurant.localhost:8000\n";

} catch (PDOException $e) {
    echo "❌ Database error: " . $e->getMessage() . "\n";
    echo "\nPlease update the database credentials in this script:\n";
    echo "- Host: $host\n";
    echo "- Database: $dbname\n";
    echo "- Username: $username\n";
    echo "- Password: [hidden]\n";
}
