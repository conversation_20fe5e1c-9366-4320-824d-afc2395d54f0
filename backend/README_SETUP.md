# 🍽️ Multi-Tenant Restaurant Management System

A comprehensive Laravel-based multi-tenant restaurant management system with subdomain-based tenant isolation, role-based access control, and complete restaurant operations management.

## 🏗️ Architecture

### Multi-Tenant Structure
- **Central Application**: Super admin management, billing, tenant provisioning
- **Tenant Applications**: Individual restaurant operations on subdomains
- **Database Separation**: Central DB + isolated tenant databases
- **Subdomain Routing**: `tenant.localhost` for restaurant access

### Technology Stack
- **Backend**: Laravel 11 with Jetstream (Inertia + Vue.js)
- **Multi-Tenancy**: stancl/tenancy package
- **Authentication**: Laravel Fortify with role-based access
- **Authorization**: <PERSON><PERSON> Permission
- **Frontend**: Vue.js 3 with Inertia.js
- **Database**: MySQL with tenant isolation

## 🚀 Quick Setup

### Prerequisites
- PHP 8.2+
- Composer
- Node.js & NPM
- MySQL
- Git

### Installation Steps

1. **Clone and Install Dependencies**
```bash
git clone <repository-url>
cd backend
composer install
npm install
```

2. **Environment Setup**
```bash
cp .env.example .env
php artisan key:generate
```

3. **Database Configuration**
Update `.env` with your database credentials:
```env
DB_CONNECTION=mysql
DB_HOST=127.0.0.1
DB_PORT=3306
DB_DATABASE=restaurant_management
DB_USERNAME=your_username
DB_PASSWORD=your_password
```

4. **Run Automated Setup**
```bash
chmod +x setup_multitenant.sh
./setup_multitenant.sh
```

**OR Manual Setup:**

```bash
# Run migrations
php artisan migrate:fresh

# Run manual seeding
php artisan tinker
>>> require 'manual_seed.php';

# Build assets
npm run build

# Start server
php artisan serve
```

5. **Verify Setup**
```bash
php verify_setup.php
```

## 🔐 Login Credentials

| Role | Email | Password | Dashboard URL |
|------|-------|----------|---------------|
| **Super Admin** | <EMAIL> | Restaurant@2024 | http://localhost:8000/admin/dashboard |
| **Restaurant Manager** | <EMAIL> | Manager@2024 | http://demo-restaurant.localhost:8000/manager/dashboard |
| **Waiter** | <EMAIL> | Waiter@2024 | http://demo-restaurant.localhost:8000/waiter/dashboard |
| **Kitchen Staff** | <EMAIL> | Kitchen@2024 | http://demo-restaurant.localhost:8000/kitchen/dashboard |
| **Delivery Driver** | <EMAIL> | Delivery@2024 | http://demo-restaurant.localhost:8000/delivery/dashboard |

## 🌐 Access URLs

### Central Application (Super Admin)
- **Base URL**: http://localhost:8000
- **Login**: http://localhost:8000/login
- **Dashboard**: http://localhost:8000/admin/dashboard

### Demo Restaurant (Tenant)
- **Base URL**: http://demo-restaurant.localhost:8000
- **Login**: http://demo-restaurant.localhost:8000/login
- **Dashboards**: Role-based redirects after login

### Local DNS Setup (Optional)
Add to `/etc/hosts` (Linux/Mac) or `C:\Windows\System32\drivers\etc\hosts` (Windows):
```
127.0.0.1 demo-restaurant.localhost
```

## 🎯 Features by Role

### 🔧 Super Admin
- **Tenant Management**: Create, manage, and monitor restaurants
- **Subscription Management**: Billing, plans, and payments
- **System Analytics**: Cross-tenant reporting and insights
- **User Management**: Global user administration

### 👨‍💼 Restaurant Manager
- **Restaurant Settings**: Configuration and branding
- **Menu Management**: Items, categories, pricing
- **Staff Management**: Employee roles and schedules
- **Financial Reports**: Sales, expenses, profitability
- **Inventory Control**: Stock levels and suppliers

### 👨‍🍳 Waiter
- **Order Management**: Take and manage customer orders
- **Table Management**: Assign and track table status
- **Customer Service**: Handle customer requests
- **Menu Knowledge**: Access to current menu and specials

### 🍳 Kitchen Staff
- **Order Queue**: View and manage cooking orders
- **Recipe Management**: Access to preparation instructions
- **Inventory Tracking**: Monitor ingredient usage
- **Kitchen Analytics**: Preparation times and efficiency

### 🚚 Delivery Driver
- **Delivery Assignments**: View assigned deliveries
- **Route Optimization**: Efficient delivery planning
- **Order Tracking**: Real-time delivery status
- **Customer Communication**: Delivery notifications

## 📊 Database Structure

### Central Database Tables
- `users` - System users and authentication
- `tenants` - Restaurant tenant information
- `domains` - Subdomain routing configuration
- `subscription_plans` - Billing plans and features
- `roles` & `permissions` - Authorization system

### Tenant Database Tables (Per Restaurant)
- `restaurants` - Restaurant-specific settings
- `menu_items` - Menu and pricing
- `orders` & `order_items` - Order management
- `customers` - Customer information
- `staff` - Restaurant employees
- `inventory_items` - Stock management
- `expenses` - Financial tracking

## 🧪 Testing Guide

### 1. Super Admin Testing
```bash
# Access central admin
curl -I http://localhost:8000/admin/dashboard
# Should redirect to login if not authenticated
```

### 2. Tenant Access Testing
```bash
# Access tenant application
curl -I http://demo-restaurant.localhost:8000/manager/dashboard
# Should redirect to tenant login
```

### 3. Role-Based Dashboard Testing
1. Login with each role
2. Verify correct dashboard redirect
3. Test role-specific features
4. Confirm access restrictions

### 4. Multi-Language Testing
- Switch between English and Bengali
- Verify translations load correctly
- Test language persistence

## 🐛 Troubleshooting

### Common Issues

**1. Subdomain Not Working**
```bash
# Add to /etc/hosts
echo "127.0.0.1 demo-restaurant.localhost" >> /etc/hosts

# Or use port-based access
http://localhost:8000/?tenant=demo-restaurant
```

**2. 404 on Dashboard Pages**
```bash
# Check if Vue components exist
ls resources/js/Pages/*/Dashboard.vue

# Rebuild assets
npm run build
```

**3. Permission Denied Errors**
```bash
# Check user roles
php artisan tinker
>>> User::with('roles')->find(1)

# Reset permissions
php artisan db:seed --class=RolePermissionSeeder
```

**4. Database Connection Issues**
```bash
# Test connection
php artisan tinker
>>> DB::connection()->getPdo()

# Check tenant database
php artisan tenants:list
```

### Debug Commands
```bash
# Clear all caches
php artisan optimize:clear

# Check routes
php artisan route:list --name=dashboard

# View tenant status
php artisan tenants:list

# Check migrations
php artisan migrate:status
php artisan tenants:migrate-status
```

## 📈 Performance Optimization

### Production Deployment
```bash
# Optimize for production
composer install --optimize-autoloader --no-dev
npm run build
php artisan config:cache
php artisan route:cache
php artisan view:cache
```

### Database Optimization
- Index frequently queried columns
- Use database connection pooling
- Implement query caching
- Monitor slow queries

## 🔒 Security Considerations

- **Tenant Isolation**: Complete data separation between restaurants
- **Role-Based Access**: Granular permission system
- **CSRF Protection**: Laravel's built-in CSRF tokens
- **SQL Injection Prevention**: Eloquent ORM protection
- **XSS Protection**: Vue.js template escaping

## 📚 Documentation

- [Testing Guide](TESTING_GUIDE.md) - Comprehensive testing instructions
- [API Documentation](docs/api.md) - REST API endpoints
- [Deployment Guide](docs/deployment.md) - Production deployment
- [Contributing](CONTRIBUTING.md) - Development guidelines

## 🤝 Support

For issues and questions:
1. Check the troubleshooting section
2. Run the verification script: `php verify_setup.php`
3. Review logs in `storage/logs/`
4. Create an issue with detailed error information

---

**🎉 Congratulations! Your multi-tenant restaurant management system is ready to use!**
