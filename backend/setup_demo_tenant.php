<?php

// Setup demo tenant script - Run after migrate:fresh, before seed
require_once 'vendor/autoload.php';

$app = require_once 'bootstrap/app.php';
$kernel = $app->make('Illuminate\Contracts\Console\Kernel');
$kernel->bootstrap();

use App\Models\Tenant;
use App\Models\SubscriptionPlan;
use Illuminate\Support\Facades\Artisan;
use Illuminate\Support\Facades\DB;

echo "=== Setting Up Demo Restaurant Tenant ===\n\n";

try {
    // Step 1: Create tenant record in central database
    echo "1. Creating tenant record in central database...\n";
    
    $plan = SubscriptionPlan::first();
    
    $tenant = Tenant::updateOrCreate(
        ['id' => 'demo-restaurant'],
        [
            'name' => 'Demo Restaurant',
            'email' => '<EMAIL>',
            'phone' => '******-0123',
            'address' => '123 Main Street',
            'city' => 'New York',
            'state' => 'NY',
            'country' => 'USA',
            'postal_code' => '10001',
            'timezone' => 'America/New_York',
            'currency' => 'USD',
            'language' => 'en',
            'subscription_plan_id' => $plan?->id,
            'subscription_status' => 'active',
            'trial_ends_at' => now()->addDays(30),
        ]
    );
    
    echo "   ✓ Tenant created: {$tenant->name}\n";

    // Step 2: Create domain
    echo "2. Creating domain...\n";
    
    $domain = $tenant->domains()->updateOrCreate([
        'domain' => 'demo-restaurant.localhost'
    ]);
    
    echo "   ✓ Domain created: {$domain->domain}\n";

    // Step 3: Run tenant migrations
    echo "3. Running tenant migrations...\n";
    
    try {
        // Method 1: Use tenants:migrate command
        echo "   Attempting tenants:migrate command...\n";
        Artisan::call('tenants:migrate', [
            '--tenants' => ['demo-restaurant'],
            '--force' => true
        ]);
        echo "   ✓ Tenant migrations completed via tenants:migrate\n";
        
    } catch (Exception $e) {
        echo "   ⚠ tenants:migrate failed, trying alternative method...\n";
        
        // Method 2: Run migrations in tenant context
        $tenant->run(function () {
            echo "   Running migrations in tenant context...\n";
            
            Artisan::call('migrate', [
                '--path' => 'database/migrations/tenant',
                '--force' => true
            ]);
            
            echo "   ✓ Tenant migrations completed via tenant context\n";
        });
    }

    // Step 4: Verify tenant database
    echo "4. Verifying tenant database...\n";
    
    $tenant->run(function () {
        $tables = ['sessions', 'cache', 'jobs', 'restaurants'];
        
        foreach ($tables as $table) {
            if (Schema::hasTable($table)) {
                echo "   ✓ Table '{$table}' exists\n";
            } else {
                echo "   ✗ Table '{$table}' missing\n";
                
                // Create essential tables if missing
                if ($table === 'sessions') {
                    DB::statement("
                        CREATE TABLE `sessions` (
                            `id` varchar(255) NOT NULL,
                            `user_id` bigint unsigned DEFAULT NULL,
                            `ip_address` varchar(45) DEFAULT NULL,
                            `user_agent` text,
                            `payload` longtext NOT NULL,
                            `last_activity` int NOT NULL,
                            PRIMARY KEY (`id`),
                            KEY `sessions_user_id_index` (`user_id`),
                            KEY `sessions_last_activity_index` (`last_activity`)
                        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4
                    ");
                    echo "   ✓ Sessions table created manually\n";
                }
                
                if ($table === 'restaurants') {
                    DB::statement("
                        CREATE TABLE `restaurants` (
                            `id` bigint unsigned NOT NULL AUTO_INCREMENT,
                            `name` varchar(255) NOT NULL,
                            `slug` varchar(255) NOT NULL,
                            `description` text,
                            `phone` varchar(255),
                            `email` varchar(255),
                            `address` text,
                            `city` varchar(255),
                            `state` varchar(255),
                            `postal_code` varchar(255),
                            `country` varchar(255),
                            `timezone` varchar(255) DEFAULT 'UTC',
                            `currency` varchar(3) DEFAULT 'USD',
                            `is_active` tinyint(1) NOT NULL DEFAULT '1',
                            `created_at` timestamp NULL DEFAULT NULL,
                            `updated_at` timestamp NULL DEFAULT NULL,
                            PRIMARY KEY (`id`),
                            UNIQUE KEY `restaurants_slug_unique` (`slug`)
                        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4
                    ");
                    echo "   ✓ Restaurants table created manually\n";
                }
            }
        }
    });

    // Step 5: Seed demo restaurant data
    echo "5. Seeding demo restaurant data...\n";
    
    $tenant->run(function () {
        // Check if restaurant data exists
        $restaurantExists = DB::table('restaurants')->where('slug', 'demo-restaurant')->exists();
        
        if (!$restaurantExists) {
            DB::table('restaurants')->insert([
                'name' => 'Demo Restaurant',
                'slug' => 'demo-restaurant',
                'description' => 'A wonderful demo restaurant for testing the multi-tenant system',
                'phone' => '******-0123',
                'email' => '<EMAIL>',
                'address' => '123 Main Street',
                'city' => 'New York',
                'state' => 'NY',
                'postal_code' => '10001',
                'country' => 'USA',
                'timezone' => 'America/New_York',
                'currency' => 'USD',
                'is_active' => true,
                'created_at' => now(),
                'updated_at' => now(),
            ]);
            
            echo "   ✓ Demo restaurant data inserted\n";
        } else {
            echo "   ✓ Demo restaurant data already exists\n";
        }
    });

    echo "\n=== Demo Tenant Setup Complete ===\n";
    echo "✅ Tenant Record: Created in central database\n";
    echo "✅ Domain: demo-restaurant.localhost\n";
    echo "✅ Database: tenant_demo-restaurant\n";
    echo "✅ Migrations: Completed\n";
    echo "✅ Demo Data: Seeded\n";
    echo "\n🌐 Access Information:\n";
    echo "   Restaurant URL: http://demo-restaurant.localhost:8000\n";
    echo "   Login URL: http://demo-restaurant.localhost:8000/login\n";
    echo "\n🔐 Test Credentials (after running user seeder):\n";
    echo "   Manager: <EMAIL> / Manager@2024\n";
    echo "   Waiter: <EMAIL> / Waiter@2024\n";
    echo "   Kitchen: <EMAIL> / Kitchen@2024\n";
    echo "   Delivery: <EMAIL> / Delivery@2024\n";

} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
    echo "File: " . $e->getFile() . ":" . $e->getLine() . "\n";
}
