# Multi-Tenant Restaurant Management System - Testing Guide

## 🚀 Quick Setup

### 1. Run Setup Script
```bash
chmod +x setup_multitenant.sh
./setup_multitenant.sh
```

### 2. Start Development Server
```bash
php artisan serve --host=0.0.0.0 --port=8000
```

### 3. Configure Local DNS (Optional)
Add to your `/etc/hosts` file:
```
127.0.0.1 demo-restaurant.localhost
127.0.0.1 localhost
```

## 🏗️ Architecture Overview

### Central Database (Main Application)
- **Location**: Main database
- **Tables**: users, tenants, domains, subscription_plans, roles, permissions
- **Purpose**: System administration, user management, billing

### Tenant Databases (Restaurant-Specific)
- **Location**: Separate databases per tenant (e.g., `tenant_demo-restaurant`)
- **Tables**: restaurants, menu_items, orders, customers, inventory, staff
- **Purpose**: Restaurant operations, menu management, order processing

## 🔐 Login Credentials

| Role | Email | Password | Access URL |
|------|-------|----------|------------|
| **Super Admin** | <EMAIL> | Restaurant@2024 | http://localhost:8000 |
| **Restaurant Manager** | <EMAIL> | Manager@2024 | http://demo-restaurant.localhost:8000 |
| **Waiter** | <EMAIL> | Waiter@2024 | http://demo-restaurant.localhost:8000 |
| **Kitchen Staff** | <EMAIL> | Kitchen@2024 | http://demo-restaurant.localhost:8000 |
| **Delivery Driver** | <EMAIL> | Delivery@2024 | http://demo-restaurant.localhost:8000 |

## 🧪 Testing Scenarios

### 1. Super Admin Access
1. **URL**: http://localhost:8000/login
2. **Login**: <EMAIL> / Restaurant@2024
3. **Expected**: Redirect to `/admin/dashboard`
4. **Features**: 
   - Tenant management
   - Subscription management
   - System-wide analytics
   - User management

### 2. Restaurant Manager Access
1. **URL**: http://demo-restaurant.localhost:8000/login
2. **Login**: <EMAIL> / Manager@2024
3. **Expected**: Redirect to `/manager/dashboard`
4. **Features**:
   - Restaurant settings
   - Menu management
   - Staff management
   - Sales reports

### 3. Waiter Access
1. **URL**: http://demo-restaurant.localhost:8000/login
2. **Login**: <EMAIL> / Waiter@2024
3. **Expected**: Redirect to `/waiter/dashboard`
4. **Features**:
   - Order management
   - Table assignments
   - Customer service

### 4. Kitchen Staff Access
1. **URL**: http://demo-restaurant.localhost:8000/login
2. **Login**: <EMAIL> / Kitchen@2024
3. **Expected**: Redirect to `/kitchen/dashboard`
4. **Features**:
   - Order queue
   - Recipe management
   - Inventory tracking

### 5. Delivery Driver Access
1. **URL**: http://demo-restaurant.localhost:8000/login
2. **Login**: <EMAIL> / Delivery@2024
3. **Expected**: Redirect to `/delivery/dashboard`
4. **Features**:
   - Delivery assignments
   - Route optimization
   - Order tracking

## 🔍 Verification Checklist

### ✅ Database Setup
- [ ] Central migrations completed (17 migrations)
- [ ] Tenant migrations completed (52 migrations)
- [ ] All seeders ran successfully
- [ ] Demo tenant created with domain

### ✅ Authentication & Authorization
- [ ] All user roles created with proper permissions
- [ ] Role-based dashboard redirects work
- [ ] Middleware protects routes correctly
- [ ] Multi-language support (EN/BN) works

### ✅ Multi-Tenancy
- [ ] Subdomain routing works
- [ ] Tenant isolation is maintained
- [ ] Central admin can access all tenants
- [ ] Tenant users can only access their restaurant

### ✅ Frontend Components
- [ ] All dashboard pages load without 404 errors
- [ ] Vue.js components render correctly
- [ ] Inertia.js navigation works
- [ ] Responsive design on mobile/desktop

## 🐛 Troubleshooting

### Common Issues

1. **404 on Dashboard Pages**
   - Check if Vue components exist in `resources/js/Pages/`
   - Verify route definitions in `routes/tenant.php`

2. **Subdomain Not Working**
   - Add domain to `/etc/hosts`
   - Check tenancy configuration in `config/tenancy.php`

3. **Permission Denied**
   - Verify user has correct role assigned
   - Check middleware in route definitions

4. **Database Connection Issues**
   - Verify `.env` database settings
   - Check if tenant database was created

### Debug Commands
```bash
# Check routes
php artisan route:list

# Check tenant status
php artisan tenants:list

# Check user roles
php artisan tinker
>>> User::with('roles')->get()

# Clear caches
php artisan optimize:clear
```

## 📊 Expected Results

After successful setup, you should have:
- ✅ 1 Super admin with full system access
- ✅ 1 Demo restaurant tenant with 11 staff members
- ✅ 5 Role-based dashboards working correctly
- ✅ Subdomain-based tenant isolation
- ✅ Multi-language support (English/Bengali)
- ✅ Complete restaurant management features
